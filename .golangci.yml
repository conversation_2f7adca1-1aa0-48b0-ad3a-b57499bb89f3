version: "2"
run:
  concurrency: 20
linters:
  default: none
  enable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - containedctx
    - contextcheck
    - copyloopvar
    - durationcheck
    - errcheck
    - errchkjson
    - errorlint
    - forbidigo
    - gocritic
    - gomodguard
    - gosec
    - govet
    - ineffassign
    - lll
    - loggercheck
    - makezero
    - misspell
    - nilerr
    - noctx
    - nolintlint
    - reassign
    - revive
    - staticcheck
    - unconvert
    - unparam
    - unused
    - usestdlibvars
  settings:
    errcheck:
      check-blank: true
    forbidigo:
      forbid:
        - pattern: ^print.*$
          msg: use common/log pkg instead
        - pattern: ^fmt\.Print.*$
          msg: use common/log pkg instead
        - pattern: models\.LoadAttributes\{\}
          msg: use TMS.GetDefaultLoadAttributes() instead
        - pattern: .*http\.Client\{\}.*
          msg: use otel.TracingHTTPClient() instead
        - pattern: http\.DefaultClient
          msg: use otel.TracingHTTPClient() instead
        - pattern: .*c\.Context.*
          msg: use c.UserContext() instead to properly propagate the user context
        - pattern: .*errtypes\.NewUserFacingError.*fmt\.Errorf.*
          msg: use errtypes.WrapNewUserFacingError instead to preserve the original error msg & context
        - pattern: .*errtypes\.NewUserFacingError.*errors\.New.*
          msg: use errtypes.WrapNewUserFacingError instead to preserve the original error msg & context
        - pattern: .*defer\s+(?i:metaSpan|span)\.End.*
          msg: "correct usage to properly capture errors: defer func() { metaSpan.End(nil) }()"
        - pattern: .*(?i:type:\bJSON\b).*
          msg: use gorm:"type:JSONB" instead to store in human-readable and indexable JSON format
        - pattern: .*SetupTestDBForDebugging\(t\).*
          msg: use SetupTestDB in finalize tests instead to avoid potential race conditions with other tests
        - pattern: .*ReadOnlySuggestedRequest.*[=:].*
          msg: don't write to ReadOnlySuggestedRequest fields directly, write to SuggestedRequest instead
        - pattern: .*ReadOnlyAppliedRequest.*[=:].*
          msg: don't write to ReadOnlyAppliedRequest fields directly, write to AppliedRequest instead
    gomodguard:
      blocked:
        modules:
          - github.com/aws/aws-sdk-go/aws:
              recommendations:
                - github.com/aws/aws-sdk-go-v2
          - github.com/brianvoe/gofakeit:
              recommendations:
                - github.com/brianvoe/gofakeit/v6
          - github.com/getsentry/sentry-go:
              recommendations:
                - github.com/drumkitai/drumkit/common/sentry
          - github.com/go-ozzo/ozzo-validation:
              recommendations:
                - github.com/go-playground/validator/v10
          - github.com/go-playground/validator:
              recommendations:
                - github.com/go-playground/validator/v10
          - github.com/go-redis/redis/v9:
              recommendations:
                - github.com/redis/go-redis/v9
          - github.com/json-iterator/go:
              reason: use 'encoding/json' from the stdlib
          - github.com/pkg/errors:
              reason: use 'errors' from the stdlib
        versions:
          - gorm.io/gorm:
              version: "> 1.25.5"
              reason: newer versions are backwards-incompatible. Check Gorm Github periodically for a fix
          - gorm.io/driver/postgres:
              version: "> v1.5.4"
              reason: newer versions are backwards-incompatible. Check Gorm Github periodically for a fix
          - gorm.io/driver/mysql:
              version: "> v1.5.2"
              reason: newer versions are backwards-incompatible. Check Gorm Github periodically for a fix
    lll:
      line-length: 120
      tab-width: 4
    revive:
      enable-all-rules: true
      rules:
        - name: confusing-results
          disabled: true
        - name: early-return
          disabled: true
        - name: empty-lines
          disabled: true
        - name: import-alias-naming
          disabled: true
        - name: import-shadowing
          disabled: true
        - name: nested-structs
          disabled: true
        - name: unchecked-type-assertion
          disabled: true
        - name: unexported-naming
          disabled: true
        - name: unused-receiver
          disabled: true
        - name: add-constant
          disabled: true
        - name: argument-limit
          disabled: true
        - name: banned-characters
          disabled: true
        - name: bare-return
          disabled: true
        - name: cognitive-complexity
          disabled: true
        - name: comment-spacings
          disabled: true
        - name: confusing-naming
          disabled: true
        - name: cyclomatic
          disabled: true
        - name: file-header
          disabled: true
        - name: flag-parameter
          disabled: true
        - name: function-length
          disabled: true
        - name: function-result-limit
          disabled: true
        - name: line-length-limit
          disabled: true
        - name: max-public-structs
          disabled: true
        - name: unhandled-error
          disabled: true
        - name: use-errors-new
          disabled: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
