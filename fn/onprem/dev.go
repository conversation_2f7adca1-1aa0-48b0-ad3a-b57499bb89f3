package main

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/fn/onprem/routes/webhook"
)

type (
	LocalEmailBody struct {
		Email *models.IngestedEmail
	}
)

func ProcessLocalInboxWebhook(c *fiber.Ctx) error {
	var body LocalEmailBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	user, err := onpremuser.GetByEmail(ctx, body.Email.Account)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "handleInboxWebhook: user does not exist")

			return c.SendStatus(http.StatusBadRequest)
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	email := models.OnPremEmail{
		Account:      user.EmailAddress,
		ExternalID:   body.Email.ExternalID,
		UserID:       user.ID,
		RFCMessageID: body.Email.RFCMessageID,
		SentAt:       body.Email.SentAt,
		ThreadID:     body.Email.ThreadID,
	}
	err = emailDB.Create(ctx, &email)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to store email in DB",
			zap.String("msgId", body.Email.ExternalID),
		)

		return c.SendStatus(http.StatusInternalServerError)
	}

	payload := models.IngestedEmail{
		Account:          user.EmailAddress,
		CC:               body.Email.CC,
		ExternalID:       email.ExternalID,
		ExternalEmailID:  email.ID,
		ExternalUserID:   user.ID,
		InReplyTo:        body.Email.InReplyTo,
		Body:             body.Email.Body,
		Recipients:       body.Email.Recipients,
		RFCMessageID:     email.RFCMessageID,
		Sender:           body.Email.Sender,
		SentAt:           email.SentAt,
		Subject:          body.Email.Subject,
		ThreadID:         email.ThreadID,
		ThreadReferences: body.Email.ThreadReferences,
	}

	err = webhook.ForwardEmailToDrumkit(ctx, webhook.Payload{Email: &payload})
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusCreated)
}
