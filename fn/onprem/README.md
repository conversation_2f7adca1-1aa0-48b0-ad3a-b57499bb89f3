# On-Prem Lambda

A Lambda function that customers can self-host to only send out emails with certain labels to Drumkit.

This Lambda ingests emails from Outlook or Gmail, categorizes emails with labels, and if applicable forwards them via API requests to the Drumkit Processor Lambda.

## Architecture Decision Record

https://www.notion.so/drumkitai/Drumkit-On-Prem-Solution-cfe8ec5a30b94d4d949ad2dac7274213

## Get Started

The `OnPrem` Lambda is meant to run in our customer's environment including their database. It's best to mimic this locally too. Let's start by creating a new database.

1. Create an `on_prem_dev` DB in Postgres:

```sql
$ ~ psql
postgres=# CREATE DATABASE on_prem_dev;
postgres=# \c on_prem_dev
```

2. Create an `.env` file and place it in `fn/onprem/`:

```shell
APP_ENV=dev
DB_USER=postgres
DB_HOST=localhost
DB_NAME=drumkit_dev
DB_PASSWORD=postgres

GOOGLE_CLIENT_ID=local
GOOGLE_CLIENT_SECRET="---See Google APIs Console OR AWS Secrets Manager---"
MICROSOFT_CLIENT_ID="259634b0-c84c-4c6b-8788-55cd80391928"
MICROSOFT_CLIENT_SECRET="---See Azure Portal or AWS Secrets Manager---"
MICROSOFT_WEBHOOK_URL="{ngrokURL}/webhook/outlook" # Required only if platform = 'outlook' AND running Drumkit Portal locally (see below)
ENCRYPTION_KEY="abcdefghijklmnopqrstuvwxyz123456"

DOMAIN=http://localhost
# this should be either `gmail` or `outlook`
PLATFORM=outlook
DRUMKIT_API_URL=http://localhost:5000
DRUMKIT_PROCESSOR_URL=http://localhost:5005
```

3. Run the `API` Lambda (`fn/api`) and `Processor` Lambda (`fn/processor`) locally. The former is used to forward logins and signups from the `OnPrem` Lambda to the local drumkit database. The latter is used to process emails that are labeled as truck lists. Expanding the labeling logic is a future `TODO`.

4. Run the `OnPrem` Lambda and confirm that migrations ran successfully in your new database. You should only see these tables there:

```sql
\d
             List of relations
 Schema |      Name      | Type  |  Owner
--------+----------------+-------+----------
 public | on_prem_emails | table | postgres
 public | on_prem_users  | table | postgres
(2 rows)
```

### Running Locally with Drumkit Portal and Live Webhooks (recommended)

1. Install ngrok (https://ngrok.com/download) and run `ngrok http 5011` (the port that the on-prem lambda runs on in dev).
1. Run Drumkit Portal locally with the following `.env.development` file:

```shell
VITE_API_URL="http://localhost:5011/" # Drumkit Portal specifically checks this port to determine if on-premise
VITE_RW_AUTH_TOKEN="localtoken" # FIXME: Required by Drumkit Portal code, but not checked by OnPrem endpoint
VITE_GOOGLE_CLIENT_ID="968651685013-i120oufqf06lonr2lj3ahh92il7j67qo.apps.googleusercontent.com"
VITE_MICROSOFT_CLIENT_ID="f83c7168-4bb2-4de1-8f90-a4255453975f"
VITE_MICROSOFT_API_CLIENT_ID="7ef2023f-908c-4cd7-99ba-866a75fa15d0"
```

1. Go to local Drumkit Portal's sign up page (http://localhost:5173/signup), click Google/Microsoft then follow the prompts.
1. Upon succcessful signup, you should see:
   1. A new user in the `on_prem_users` table in `on_prem_dev` DB
   1. A new user in the `users` table in `dev` DB with `is_on_prem` set to `true`
   1. Gmail/Outlook webhooks should be forwarded to ngrok -> http://localhost:5011/webhook/{outlook|gmail} and, if the email is labelled as a Truck List, it will be forwarded to Drumkit Processor @ `http://localhost:5005/onprem/email`.

### Running Without Drumkit Portal or Live Webhooks

5. Signing up a user works here just like it does in the `API` Lambda by specifying a dev email. Make a `POST` request to `http://localhost:5011/user/signup/google` or `http://localhost:5011/user/signup/microsoft` with this payload:

```json
{
  "DevEmail": "<your-email>"
}
```

6. Logins work the same way. The endpoints are `http://localhost:5011/user/login/microsoft` or `http://localhost:5011/user/login/microsoft` with the same payload:

```json
{
  "DevEmail": "<your-email>"
}
```

7. Ingesting and forwarding emails to the `Processor` Lambda work by making a `POST` request to this endpoint `http://localhost:5011/webhook/local/email` with this payload:

```json
{
  "Account": "<your-email-address>",
  "CC": "<cc>",
  "Plaintext": "<email-body>",
  "Recipients": "<comma-delimited-list-of-email-addresses>",
  "Sender": "<senders-email-address>",
  "Subject": "<subject-of-the-email>"
}
```
