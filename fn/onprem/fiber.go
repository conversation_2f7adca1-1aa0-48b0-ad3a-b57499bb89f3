package main

import (
	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/fn/onprem/env"
	"github.com/drumkitai/drumkit/fn/onprem/routes/email"
	"github.com/drumkitai/drumkit/fn/onprem/routes/user"
	"github.com/drumkitai/drumkit/fn/onprem/routes/webhook"
)

func buildApp() *fiber.App {
	app := fiber.New()

	app.Use(middleware.Sentry())
	app.Use(middleware.Tracer())
	app.Use(middleware.Zap(middleware.WithAppEnv(env.Vars.AppEnv)))

	// Required to allow localhost:5173 (Drumkit Portal) and gmail to query the API
	// https://docs.aws.amazon.com/apigateway/latest/developerguide/how-to-cors.html#apigateway-enable-cors-proxy
	app.Use(middleware.CORS(middleware.WithCORSOrigins(
		"http://localhost:5173," + "http://localhost," + env.Vars.Domain,
	)))

	// Open endpoints (no auth header required)
	registerOpenRoutes(app, env.Vars.Platform, env.Vars.AppEnv)

	if env.Vars.AppEnv == "dev" {
		// POST email body directly
		app.Post("/webhook/local/email", ProcessLocalInboxWebhook)
	}

	return app
}

// registerOpenRoutes configures routes that do not require authentication.
func registerOpenRoutes(app *fiber.App, platform, appEnv string) {
	var (
		health, login, signup, loginGoogle, signupGoogle, signupMicrosoft, loginMicrosoft string
		emailIngest, watchInbox, webhookGmail, webhookOutlook                             string
	)

	// NOTE: In production, AWS API Gateway will prefix the stage name ("/v1/") to each of these routes.
	//
	// Reason for camelCase routes: Each is registered in an Azure http trigger folder with that name that includes
	// a function.json configuration file.
	switch {
	case platform == "outlook" && appEnv != "dev":
		health = "/api/health"
		login = "/api/userLogin"
		signup = "/api/userSignup"
		loginGoogle = "/api/userLoginGoogle"
		signupGoogle = "/api/userSignupGoogle"
		signupMicrosoft = "/api/userSignupMicrosoft"
		loginMicrosoft = "/api/userLoginMicrosoft"
		webhookGmail = "/api/webhookGmail"
		webhookOutlook = "/api/webhookOutlook"
		watchInbox = "/api/webhookSubscribe"
		emailIngest = "/api/emailIngest"

	default:
		health = "/health"
		login = "/user/login"
		signup = "/user/signup"
		loginGoogle = "/user/login/google"
		signupGoogle = "/user/signup/google"
		signupMicrosoft = "/user/signup/microsoft"
		loginMicrosoft = "/user/login/microsoft"
		webhookGmail = "/webhook/gmail"
		webhookOutlook = "/webhook/outlook"
		watchInbox = "/webhook/subscribe"
		emailIngest = "/email/ingest"
	}

	// Health check endpoints
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendString("Healthy")
	})

	app.Get(health, func(c *fiber.Ctx) error {
		return c.SendString("Healthy")
	})

	// For backwards compatibility, temporarily keep the original signup route
	// TODO: remove once drumkit-portal switches to /google and /microsoft signup routes
	app.Post(login, user.LoginGoogle)
	app.Post(signup, user.SignupGoogle)

	app.Post(loginGoogle, user.LoginGoogle)
	app.Post(signupGoogle, user.SignupGoogle)

	app.Post(signupMicrosoft, user.SignupMicrosoft)
	app.Post(loginMicrosoft, user.LoginMicrosoft)

	app.Post(webhookGmail, webhook.ProcessGmailInboxWebhook)
	app.Post(webhookOutlook, webhook.ProcessOutlookInboxWebhook)

	// TODO: configure JWT logic and gate this as a protected auth route
	app.Post(watchInbox, user.WatchInbox)
	app.Post(emailIngest, email.Ingest)
}
