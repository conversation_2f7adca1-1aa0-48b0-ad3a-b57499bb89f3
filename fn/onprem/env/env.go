package env

import (
	"context"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars Variables

type Variables struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv              string `envconfig:"APP_ENV" required:"true"`
	Domain              string `envconfig:"DOMAIN" required:"true"`   // https://<customers-domain>
	PortalDomain        string `envconfig:"PORTAL_DOMAIN"`            // the custom drumkit portal domain
	Platform            string `envconfig:"PLATFORM" required:"true"` // one of `gmail` or `outlook`
	EncryptionKey       string `envconfig:"ENCRYPTION_KEY" required:"true"`
	SentryDSN           string `envconfig:"SENTRY_DSN"`
	DrumkitAccessToken  string `envconfig:"DRUMKIT_ACCESS_TOKEN"`
	DrumkitAPIURL       string `envconfig:"DRUMKIT_API_URL" required:"true"`
	DrumkitProcessorURL string `envconfig:"DRUMKIT_PROCESSOR_URL" required:"true"`

	BackfillHours  int  `envconfig:"BACKFILL_HOURS" default:"0"`
	SkipDuplicates bool `envconfig:"SKIP_DUPLICATES" default:"true"`

	// Incoming gmail hooks are only accepted from this subscription ID
	GmailSubscriptionID string `envconfig:"GMAIL_SUBSCRIPTION_ID"`

	// Associated with the Customer's Google/Microsoft apps
	GoogleClientID    string `envconfig:"GOOGLE_CLIENT_ID"`
	MicrosoftClientID string `envconfig:"MICROSOFT_CLIENT_ID"`

	// Register Gmail webhooks to this topic
	GmailWebhookTopic string `envconfig:"GMAIL_WEBHOOK_TOPIC"`
	// Where should outlook send msg webhooks? I.e. the customer's ingestion function URL
	MicrosoftWebhookURL string `envconfig:"MICROSOFT_WEBHOOK_URL"`

	GoogleClientSecret    string `envconfig:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `envconfig:"MICROSOFT_CLIENT_SECRET"`
	JWT                   string `envconfig:"JWT"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.Warn(ctx, "no .env file found", zap.Error(err))
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.Platform == "gmail" {
			if Vars.GoogleClientID == "" {
				log.WarnNoSentry(ctx, "missing GOOGLE_CLIENT_ID")
			}

			if Vars.GoogleClientSecret == "" {
				log.WarnNoSentry(ctx, "missing GOOGLE_CLIENT_SECRET")
			}

			if Vars.GmailSubscriptionID == "" {
				log.WarnNoSentry(ctx, "missing GMAIL_SUBCRIPTION_ID")
			}
		}

		if Vars.Platform == "outlook" {
			if Vars.MicrosoftClientID == "" {
				log.WarnNoSentry(ctx, "missing MICROSOFT_CLIENT_ID")
			}

			if Vars.MicrosoftClientSecret == "" {
				log.WarnNoSentry(ctx, "missing MICROSOFT_CLIENT_SECRET")
			}

			if Vars.MicrosoftWebhookURL == "" {
				log.WarnNoSentry(ctx, "missing MICROSOFT_WEBHOOK_URL")
			}
		}

	case "staging", "prod":
		if Vars.SentryDSN == "" {
			panic("missing SENTRY_DSN")
		}

		if Vars.Domain == "" {
			log.Error(ctx, "missing DOMAIN")
		}

		if Vars.EncryptionKey == "" {
			log.Error(ctx, "missing ENCRYPTION_KEY")
		}

		if Vars.JWT == "" {
			log.Error(ctx, "missing JWT")
		}

		if Vars.DrumkitAccessToken == "" {
			// NOTE: This isn't being checked right now in outgoing API requests.
			log.WarnNoSentry(ctx, "missing DRUMKIT_ACCESS_TOKEN")
		}

		// Gmail-specific checks
		if Vars.Platform == "gmail" {
			if Vars.GoogleClientID == "" {
				log.Error(ctx, "missing GOOGLE_CLIENT_ID")
			}

			if Vars.GoogleClientSecret == "" {
				log.Error(ctx, "missing GOOGLE_CLIENT_SECRET")
			}

			if Vars.GmailSubscriptionID == "" {
				log.Error(ctx, "missing GMAIL_SUBCRIPTION_ID")
			}
		}

		// Outlook-specific checks
		if Vars.Platform == "outlook" {
			if Vars.Domain == "https://redwoodlogistics.com" {
				return readRedwoodSecrets(ctx)
			}

			if Vars.MicrosoftClientID == "" {
				log.Error(ctx, "missing MICROSOFT_CLIENT_ID")
			}

			if Vars.MicrosoftClientSecret == "" {
				log.Error(ctx, "missing MICROSOFT_CLIENT_SECRET")
			}

			if Vars.MicrosoftWebhookURL == "" {
				log.Error(ctx, "missing MICROSOFT_WEBHOOK_URL")
			}
		}
	}

	return nil
}

func readRedwoodSecrets(ctx context.Context) error {
	secretNames := []string{
		Vars.MicrosoftClientID,
		Vars.MicrosoftClientSecret,
		Vars.EncryptionKey,
		Vars.JWT,
	}

	creds, err := rds.ReadAzureSecret(ctx, Vars.DBSecretARN, secretNames)
	if err != nil {
		return err
	}

	varsMap := map[string]*string{
		Vars.MicrosoftClientID:     &Vars.MicrosoftClientID,
		Vars.MicrosoftClientSecret: &Vars.MicrosoftClientSecret,
		Vars.EncryptionKey:         &Vars.EncryptionKey,
		Vars.JWT:                   &Vars.JWT,
	}

	for key, ptr := range varsMap {
		if value, ok := creds[key]; ok {
			if valueStr, ok := value.(string); ok {
				*ptr = valueStr
			} else {
				log.Error(ctx, "value retrieved is not a string", zap.String("key", key))
			}
		} else {
			log.Error(ctx, "key not found in secret", zap.String("key", key))
		}
	}

	return nil
}
