package main

import (
	"context"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

var fiberPort = "5011"

func main() {
	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	// Set up graceful shutdown handling
	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-onprem")

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)
	log.Debug(ctx, "IN DEBUG MODE")

	var secretStore rds.Option

	// Default to reading from Azure Key Vault for customers but change to AWS Secrets Manager depending on the
	// customer.
	secretStore = rds.WithAzureKeyVault(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod")
	if env.Vars.Domain != "https://redwoodlogistics.com" {
		secretStore = rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod")
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		secretStore,
		rds.WithOnPrem(true),
		rds.WithApplicationName("drumkit-onprem"),
	); err != nil {
		panic(err)
	}

	if env.Vars.Platform == "gmail" {
		api.InitializeOAuthGoogle(env.Vars.GoogleClientID, env.Vars.GoogleClientSecret)
	}

	onPremMigrationOrder := []any{
		&models.OnPremUser{},
		&models.OnPremEmail{},
	}

	if err := rds.AutoMigrate(ctx, rds.WithOnPrem(true), rds.WithMigrationOrder(onPremMigrationOrder)); err != nil {
		panic(err)
	}

	// TODO: add Redis

	app := buildApp()

	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "shutting down fiber server")
		return app.Shutdown()
	})

	// local development function
	if env.Vars.AppEnv == "dev" {
		errChan := api.RunServer(ctx, app, api.WithPort(fiberPort))
		for {
			select {
			case err := <-errChan:
				log.Error(ctx, "fiber server failed to start", zap.Error(err))
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	if helpers.IsLambda() {
		go api.RunServer(ctx, app, api.WithPort(fiberPort))
		// aws lambda function
		lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
	}

	// azure function
	errChan := api.RunServer(ctx, app, api.WithPort("7071"))
	for {
		select {
		case err := <-errChan:
			log.Error(ctx, "fiber server failed to start", zap.Error(err))
			return
		case err := <-shutdownCompleteChan:
			if err != nil {
				log.Error(ctx, "graceful shutdown failed", zap.Error(err))
			} else {
				log.Info(ctx, "graceful shutdown successful, exiting application.")
			}
			return
		}
	}
}

func handlerWithLogging(
	ctx context.Context,
	event events.APIGatewayProxyRequest,
) (result *events.APIGatewayProxyResponse, err error) {
	return api.GatewayHandler(ctx, &event, api.WithPort(fiberPort))
}
