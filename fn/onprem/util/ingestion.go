package onpremutil

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func ShouldBackfill(ctx context.Context, user models.OnPremUser) bool {
	duration := time.Since(user.BackfillStartTime.Time)
	// A backfill started but due to an error (i.e. timeout), the lock was never removed so we should try again.
	// Duration is based on Lambda timeout configuration
	isLongTime := !user.BackfillStartTime.Time.IsZero() && duration > 10*time.Minute

	if isLongTime {
		log.WarnNoSentry(ctx, "backfill lock set too long", zap.Duration("duration", duration))
	}

	return user.BackfillStartTime.Time.IsZero() || isLongTime
}
