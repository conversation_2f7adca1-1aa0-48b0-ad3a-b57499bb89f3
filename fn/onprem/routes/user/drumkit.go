package user

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

type (
	LoginPayload struct {
		User            models.OnPremUser `json:"user"`
		AccessToken     string            `json:"access_token"`
		TokenExpiration int64             `json:"token_expiration"` // In Unix time
		Email           string            `json:"email"`
		TokenType       string            `json:"token_type"`
		DevEmail        string            `json:"dev_email"`
	}

	SignupPayload struct {
		User     models.OnPremUser `json:"user"`
		DevEmail string            `json:"dev_email"`
	}

	DrumkitSignupResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

func forwardSignedInUserToDrumkit(ctx context.Context, payload LoginPayload) error {
	// NOTE: We don't want to forward the encrypted access and refresh tokens to Drumkit
	removeTokensFromUser(&payload.User)

	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(
			ctx,
			"failed to json marshal user login payload to send to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return err
	}

	loginURL := env.Vars.DrumkitAPIURL + "/onprem/user/login/" + string(payload.User.EmailProvider)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		loginURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return err
	}

	generatedAccessToken, err := jwt.NewAccessToken(
		payload.User.EmailAddress,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithIsOnPrem(true),
		jwt.WithIssuer(env.Vars.Domain),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt for drumkit requests failed", zap.Error(err))
		return err
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+generatedAccessToken)

	drumkitResp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(
			ctx,
			"failed to send http request to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return err
	}
	drumkitResp.Body.Close()

	return nil
}

func forwardSignedUpUser(ctx context.Context, payload SignupPayload) (DrumkitSignupResponse, error) {
	// NOTE: We don't want to forward the encrypted access and refresh tokens to Drumkit
	removeTokensFromUser(&payload.User)

	reqBody, err := json.Marshal(payload)
	if err != nil {
		log.Error(
			ctx,
			"failed to json marshal user signup payload to send to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return DrumkitSignupResponse{}, err
	}

	signupURL := env.Vars.DrumkitAPIURL + "/onprem/user/signup/" + string(payload.User.EmailProvider)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		signupURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return DrumkitSignupResponse{}, err
	}

	generatedAccessToken, err := jwt.NewAccessToken(
		payload.User.EmailAddress,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithIsOnPrem(true),
		jwt.WithIssuer(env.Vars.Domain),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt for drumkit requests failed", zap.Error(err))
		return DrumkitSignupResponse{}, err
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+generatedAccessToken)

	drumkitResp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(
			ctx,
			"failed to send http request to drumkit",
			zap.Uint("userId", payload.User.ID),
			zap.String("emailProvider", string(payload.User.EmailProvider)),
			zap.Error(err),
		)

		return DrumkitSignupResponse{}, err
	}
	defer drumkitResp.Body.Close()

	drumkitRespBody, err := io.ReadAll(drumkitResp.Body)
	if err != nil {
		return DrumkitSignupResponse{}, err
	}

	var userInfo DrumkitSignupResponse
	if err = json.Unmarshal(drumkitRespBody, &userInfo); err != nil {
		log.Error(ctx, "ReadAll: "+err.Error()+"\n")
		return DrumkitSignupResponse{}, errors.New("reading failed")
	}

	return userInfo, nil
}

func removeTokensFromUser(user *models.OnPremUser) {
	user.EncryptedAccessToken = ""
	user.EncryptedRefreshToken = ""
}
