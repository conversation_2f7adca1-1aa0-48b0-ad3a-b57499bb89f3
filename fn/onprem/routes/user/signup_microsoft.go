package user

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

type (
	SignupMicrosoftBody struct {
		msclient.MicrosoftAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}
)

// FIXME: We're not checking the code parameter to verify it's Redwood with a valid Drumkit access token
func SignupMicrosoft(c *fiber.Ctx) error {
	var body SignupMicrosoftBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("account", body.Account))

	if env.Vars.AppEnv == "dev" && body.DevEmail != "" {
		user := models.OnPremUser{
			EmailAddress:  body.DevEmail,
			EmailProvider: models.OutlookEmailProvider,
		}

		if err := onpremuser.Create(ctx, &user); err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusCreated).SendString("dev user created successfully")
	}

	log.Info(ctx, "received ms signup request")

	user, err := handleMicrosoftAuth(ctx, body.MicrosoftAuthCodeRequest)
	if err != nil {
		log.Error(ctx, "ms auth failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.PortalDomain = env.Vars.PortalDomain

	userFound, err := onpremuser.GetByEmail(ctx, body.Account.Username)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = userFound.ID

	if userFound.ID == 0 {
		err = onpremuser.Create(ctx, user)
	} else {
		err = onpremuser.Update(ctx, *user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	payload := SignupPayload{
		User:     *user,
		DevEmail: body.DevEmail,
	}

	drumkitUserInfo, err := forwardSignedUpUser(ctx, payload)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(DrumkitSignupResponse{
		AccessToken:     drumkitUserInfo.AccessToken,
		Email:           drumkitUserInfo.Email,
		ServiceID:       drumkitUserInfo.ServiceID,
		GroupID:         drumkitUserInfo.GroupID,
		TokenExpiration: drumkitUserInfo.TokenExpiration,
		TokenType:       drumkitUserInfo.TokenType,
	})
}

func handleMicrosoftAuth(ctx context.Context, auth msclient.MicrosoftAuthCodeRequest) (*models.OnPremUser, error) {
	user := &models.OnPremUser{
		EmailAddress:       strings.ToLower(auth.Account.Username),
		EmailProvider:      models.OutlookEmailProvider,
		Name:               auth.Account.Name,
		MailClientID:       auth.Account.LocalAccountID,
		TenantID:           auth.Account.TenantID,
		OutlookClientState: uuid.NewString(),
	}

	tokenResp, err := msclient.GetOboToken(ctx, auth, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret)
	if err != nil {
		return user, fmt.Errorf("error getting OBO token: %w", err)
	}

	user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
		ctx,
		tokenResp.AccessToken,
		tokenResp.RefreshToken,
		[]byte(env.Vars.EncryptionKey),
	)
	if err != nil {
		return user, fmt.Errorf("outlook token encryption error: %w", err)
	}

	user.TokenExpiry = time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return user, watchOutlookInbox(ctx, user, []byte(env.Vars.EncryptionKey))
}

// Creates an outlook subscription for new messages (and updates the user with the subscription details)
func watchOutlookInbox(ctx context.Context, user *models.OnPremUser, encryptionKey []byte) error {
	client, err := msclient.New(
		ctx,
		env.Vars.MicrosoftClientID,
		env.Vars.MicrosoftClientSecret,
		user, oauth.WithEncryptionKey(&encryptionKey),
	)
	if err != nil {
		return fmt.Errorf("error building MSClient: %w", err)
	}

	sub, err := client.WatchInbox(ctx, env.Vars.MicrosoftWebhookURL, user.OutlookClientState)
	if err != nil {
		return err
	}

	user.WebhookExpiration = sub.ExpirationDateTime
	user.OutlookSubscriptionID = sub.ID

	log.Info(ctx, "successfully created outlook subscription", zap.String("subId", user.OutlookSubscriptionID))

	return nil
}
