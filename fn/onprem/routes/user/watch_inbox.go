package user

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

func WatchInbox(c *fiber.Ctx) error {
	userEmail := c.Query("email")
	if userEmail == "" {
		return c.Status(http.StatusBadRequest).SendString("email parameter required")
	}

	ctx := log.With(c.UserContext(), zap.String("userEmail", userEmail))

	user, err := onpremuser.GetByEmail(ctx, userEmail)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString("user not found")
		}
		return c.SendStatus(http.StatusInternalServerError)
	}

	encryptionKey := []byte(env.Vars.EncryptionKey)

	switch user.EmailProvider {
	case models.GmailEmailProvider:
		if err := watchGmailInbox(ctx, &user, encryptionKey); err != nil {
			log.Error(ctx, "failed to watch gmail inbox", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

	case models.OutlookEmailProvider:
		if err := watchOutlookInbox(ctx, &user, encryptionKey); err != nil {
			log.Error(ctx, "failed to watch outlook inbox", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

	default:
		return c.Status(http.StatusBadRequest).SendString("unknown email provider")
	}

	if err := onpremuser.Update(ctx, user); err != nil {
		log.Error(ctx, "failed to update user webhook details", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.EncryptedAccessToken = ""
	user.EncryptedRefreshToken = ""

	return c.Status(http.StatusOK).JSON(user)
}
