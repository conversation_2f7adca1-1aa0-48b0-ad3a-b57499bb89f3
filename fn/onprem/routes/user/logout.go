package user

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
)

func Logout(c *fiber.Ctx) error {
	email := middleware.ClaimsFromContext(c).Email
	ctx := c.UserContext()

	user, err := onpremuser.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "skipping logout: user does not exist")
			return c.SendStatus(http.StatusOK)
		}

		// Some other DB error
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx, zap.String("userEmail", user.EmailAddress))

	// Invalidate the access token: remove access token from list of hashed access tokens
	updatedTokenList := removeHashedToken(user.HashedSessionToken, user.HashedSessionTokens)

	// future API requests will fail for this client until a new token is generated by /login
	user.HashedSessionTokens = updatedTokenList

	if err := onpremuser.Update(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.SendStatus(http.StatusOK)
}

func removeHashedToken(token string, tokenList []string) []string {
	for i, value := range tokenList {
		if value == token {
			return append(tokenList[:i], tokenList[i+1:]...)
		}
	}

	// if the token is for some reason not in the slice, return the original list of tokens
	return tokenList
}
