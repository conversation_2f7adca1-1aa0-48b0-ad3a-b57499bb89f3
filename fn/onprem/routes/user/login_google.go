package user

import (
	"errors"
	"net/http"
	"slices"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/fn/onprem/env"
)

type (
	LoginBody struct {
		api.GoogleAuthCodeRequest

		// For local dev only: the email can be specified directly
		DevEmail string
	}

	LoginResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		TokenType       string `json:"token_type"`
	}
)

func LoginGoogle(c *fiber.Ctx) error {
	var body LoginBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	var email string
	var resp api.GoogleAuthResponse
	var err error

	if env.Vars.AppEnv == "dev" && body.Scope == "" {
		if email = body.DevEmail; email == "" {
			return c.Status(http.StatusBadRequest).SendString("email is required")
		}
	} else {
		// get email from Google via auth code
		resp, err = api.CallBackFromGoogle(ctx, body.GoogleAuthCodeRequest)
		if err != nil {
			log.Error(ctx, "callBackFromGoogle failed", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		email = resp.UserInfo.Email
	}

	ctx = log.With(ctx, zap.String("userEmail", email))

	user, err := onpremuser.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "login failed: user does not exist", zap.String("error", err.Error()))
			return c.SendStatus(http.StatusUnauthorized)
		}

		return c.SendStatus(http.StatusInternalServerError)
	}

	// NOTE: that dev mode can either be a dry-run with no Google auth (see README) or
	// localhost drumkit-portal that *does* use Google auth
	if env.Vars.AppEnv == "dev" || body.DevEmail != "" {
		user.EncryptedAccessToken, user.EncryptedRefreshToken, err = crypto.EncryptTokens(
			ctx,
			resp.AccessToken,
			resp.RefreshToken,
			[]byte(env.Vars.EncryptionKey),
		)
		if err != nil {
			log.Error(ctx, "token encryption failed", zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

		user.TokenExpiry = resp.ExpTime
		user.MailClientID = resp.UserInfo.ID

		// Update user with gmail tokens
		if err := onpremuser.Update(ctx, user); err != nil {
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if err := watchGmailInbox(ctx, &user, []byte(env.Vars.EncryptionKey)); err != nil {
		// Fail-open. If initial watch fails, manually trigger Lambda for the user
		log.Error(ctx, "watchGmailInbox failed", zap.Error(err))
	}

	accessToken, err := jwt.NewAccessToken(
		email,
		jwt.WithAppEnv(env.Vars.AppEnv),
		jwt.WithIsOnPrem(true),
		jwt.WithIssuer(env.Vars.Domain),
		jwt.WithJWT(env.Vars.JWT),
	)
	if err != nil {
		log.Error(ctx, "creating new jwt failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.HashedSessionToken = middleware.HashedToken(accessToken)

	// add hashed session token to list of all session tokens, if it isn't already there
	if !slices.Contains(user.HashedSessionTokens, user.HashedSessionToken) {
		user.HashedSessionTokens = append(user.HashedSessionTokens, user.HashedSessionToken)
	}

	if err := onpremuser.Update(ctx, user); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	payload := LoginPayload{
		User:            user,
		AccessToken:     accessToken,
		Email:           email,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
		DevEmail:        email,
	}

	err = forwardSignedInUserToDrumkit(ctx, payload)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginResponse{
		AccessToken:     accessToken,
		Email:           email,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
