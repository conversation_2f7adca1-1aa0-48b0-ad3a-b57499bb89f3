package webhook

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/googleapi"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	onpremuser "github.com/drumkitai/drumkit/common/rds/user/onprem"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/onprem/env"
	helpers "github.com/drumkitai/drumkit/fn/onprem/util"
)

type (
	GmailWebhookBody struct {
		Message      Message `json:"message"`
		Subscription string  `json:"subscription"`
	}

	Message struct {
		// Base64-URL encoded JSON string of MessageData
		Data string `json:"data"`

		// ID of the message that triggered the webhook. NOT necessarily a new message.
		MessageID string `json:"messageId"`

		PublishTime time.Time `json:"publishTime"`

		// ***** Following section is for dev purposes only and not part of Pub/Sub ****** //

		// If true, use the historyID of the specified message ID. If false, get just that message.
		UseMessageHistoryID bool

		// Max number of most recent messages to get. `MessageID` takes precedence
		NumMessages int `json:"numMessages"`
	}

	MessageData struct {
		EmailAddress string `json:"emailAddress"`
		HistoryID    uint64 `json:"historyId"`
	}

	WatchInboxBody struct {
		EmailAddress string `json:"emailAddress"`
		StopWatching bool   `json:"stopWatching"`
	}
)

var (
	// Unit tests can replace these functions
	getUserByEmail          = onpremuser.GetByEmail
	dbUpdateUserFunc        = onpremuser.Update
	updateGmailBackfillLock = onpremuser.UpdateBackfillLock
	gmailConstructor        = gmailclient.New[models.UserAccessor]
)

// TODO (later): deduplicate with GmailInboxWebhook
func ProcessGmailInboxWebhook(c *fiber.Ctx) error {
	var body GmailWebhookBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	if body.Subscription != env.Vars.GmailSubscriptionID {
		log.Warnf(ctx, "ignoring /inboxWebhook request from unknown subscription '%s' (expected '%s')",
			body.Subscription, env.Vars.GmailSubscriptionID)

		return c.SendStatus(http.StatusUnauthorized)
	}

	decoded, err := base64.URLEncoding.DecodeString(body.Message.Data)
	if err != nil {
		log.Error(ctx, "error base64-decoding webhook data", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	var msgData MessageData
	if err = json.Unmarshal(decoded, &msgData); err != nil {
		log.Error(ctx, "error unmarshaling message data", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(
		ctx,
		zap.String("emailAddress", msgData.EmailAddress),
		zap.Uint64("historyId", msgData.HistoryID),
		zap.String("emailProvider", "gmail"),
	)

	user, err := getUserByEmail(ctx, msgData.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "handleInboxWebhook: user does not exist")

			return c.SendStatus(http.StatusBadRequest)
		}

		return c.SendStatus(http.StatusInternalServerError)
	}
	sentry.SetUser(ctx, &user)

	encryptionKey := []byte(env.Vars.EncryptionKey)

	client, err := gmailConstructor(
		ctx,
		env.Vars.GoogleClientID,
		env.Vars.GoogleClientSecret,
		&user,
		oauth.WithEncryptionKey(&encryptionKey),
	)
	if err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	var newWebhookExpiry time.Time
	// Re-watch inbox before subscription expires
	diff := time.Until(user.WebhookExpiration).Hours()
	if diff < 72 {
		req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}

		if r, err := client.WatchInbox(ctx, req); err == nil {
			log.Info(ctx, "successfully resubscribed to inbox", zap.Any("response", r))
			newWebhookExpiry = time.UnixMilli(r.Expiration)
		} else {
			log.ErrorNoSentry(ctx, "error resubscribing to inbox", zap.Error(err))
			if diff < 24 && shouldSendToSentry() {
				sentry.GetHubFromContext(ctx).CaptureException(err)
			}
		}
	}

	var msgIDs []string
	// Pub/Sub returns the startHistoryId since the latest message, but Gmail.History.List().StartHistoryId()
	// returns histories exclusive of the `startHistoryId` param, so we use the previous history ID to get
	// the latest message(s).
	if user.GmailLastHistoryID == 0 {
		if !helpers.ShouldBackfill(ctx, user) {
			// Backfill already in progress; respond with non-2xx so that webhook tries again
			// and we can get the latest messages that the backfill may have missed
			log.Warnf(ctx, "backfill in progress since %s, try again later", user.BackfillStartTime.Time)

			return c.SendStatus(http.StatusUnprocessableEntity)
		}

		log.Infof(ctx, "backfilling %d hours for new user", env.Vars.BackfillHours)

		backfillStartTime := models.NullTime{Time: time.Now(), Valid: true}
		if err = updateGmailBackfillLock(ctx, &user, backfillStartTime); err != nil {
			log.Error(ctx, "error acquiring DB lock to perform backfill", zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

		defer func() {
			log.Info(ctx, "removing backfill lock triggered by new user")

			if err = updateGmailBackfillLock(ctx, &user, models.NullTime{}); err != nil {
				log.Error(ctx, "error removing DB backfill lock", zap.Error(err))
			}
		}()

		startDate := time.Now().Add(-time.Duration(env.Vars.BackfillHours) * time.Hour)
		msgIDs, err = client.ListMessagesAfterDate(ctx, startDate)
		if err != nil {
			msg := fmt.Sprintf("error backfilling new user %s", user.EmailAddress)
			log.Error(ctx, msg, zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

	}

	// Complete dev request
	if msgData.HistoryID == 0 {
		msg, err := client.GetMessage(ctx, body.Message.MessageID)
		if err != nil {
			msg := fmt.Sprintf("[Dev Request] error getting message %s", body.Message.MessageID)
			log.Error(ctx, msg, zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

		if body.Message.UseMessageHistoryID {
			user.GmailLastHistoryID = msg.HistoryId
		} else {
			msgIDs = append(msgIDs, msg.Id)
		}
	}

	// Complete Pub/Sub request
	if len(msgIDs) == 0 {
		msgIDs, err = getMsgIDs(ctx, client, &user)
		var httpErr *googleapi.Error
		if err != nil {
			//nolint:staticcheck
			if !(errors.As(err, &httpErr) && httpErr.Code == http.StatusNotFound) {
				// unexpected error: return
				log.Error(ctx, "error getting history list", zap.Error(err))

				return c.SendStatus(http.StatusInternalServerError)
			}

			log.Info(ctx, "gmailclient.ListHistory returned 404",
				zap.Uint64("lastHistoryId", user.GmailLastHistoryID))

			if !helpers.ShouldBackfill(ctx, user) {
				log.Warnf(
					ctx,
					"invalid historyID(%d) but backfill in progress since %s, try again later",
					user.GmailLastHistoryID, user.BackfillStartTime.Time,
				)

				return c.SendStatus(http.StatusServiceUnavailable)
			}

			// Per docs, history IDs are valid for about a week and may have gaps/become invalid, thus
			// returning a 404. In those cases, backfill emails.
			log.Infof(ctx, "backfill not in progress, getting emails from last %d hours",
				env.Vars.BackfillHours)

			backfillStartTime := models.NullTime{Time: time.Now(), Valid: true}
			if err = updateGmailBackfillLock(ctx, &user, backfillStartTime); err != nil {
				log.Error(ctx, "error acquiring DB lock to perform backfill", zap.Error(err))

				return c.SendStatus(http.StatusInternalServerError)
			}

			defer func() {
				log.Info(ctx, "removing backfill lock triggered by invalid historyID")

				if err = updateGmailBackfillLock(ctx, &user, models.NullTime{}); err != nil {
					log.Error(ctx, "error removing DB backfill lock", zap.Error(err))
				}
			}()

			startDate := time.Now().Add(-time.Duration(env.Vars.BackfillHours) * time.Hour)
			msgIDs, err = client.ListMessagesAfterDate(ctx, startDate)
			if err != nil {
				msg := fmt.Sprintf("error listing messages from last %d hours", env.Vars.BackfillHours)
				log.Error(ctx, msg, zap.Error(err))

				return c.SendStatus(http.StatusInternalServerError)
			}
		}

	}

	log.Info(ctx, "processing messages", zap.Int("count", len(msgIDs)), zap.Strings("msgIds", msgIDs))

	if len(msgIDs) == 0 {
		if err := updateUserHistory(ctx, user, msgData.HistoryID, newWebhookExpiry); err != nil {
			// Fail-open. If there's a transient error updating the DB, next webhook will try same history ID
			log.Error(ctx, "updateUserHistory failed", zap.Error(err))
		}

		return c.SendStatus(http.StatusOK)
	}

	const batchSize = 100
	var groups [][]string

	for i := 0; i < len(msgIDs); i += batchSize {
		end := i + batchSize
		if end > len(msgIDs) {
			end = len(msgIDs)
		}
		groups = append(groups, msgIDs[i:end])
	}

	var msgs []*gmail.Message
	for _, group := range groups {
		groupMsgs, err := client.BatchGetMessages(ctx, group)
		if err != nil {
			log.Error(ctx, "failed to get msgs", zap.Error(err))

			return c.SendStatus(http.StatusInternalServerError)
		}

		msgs = append(msgs, groupMsgs...)
	}

	for _, msg := range msgs {
		if msg.Id == "" {
			log.WarnNoSentry(ctx, "skipping gmail msg with no id", zap.Any("msg", msg))
			continue
		}

		if env.Vars.SkipDuplicates {
			_, err = dbGetEmailFunc(ctx, msg.Id)
			if err == nil {
				log.Info(ctx, "skipping duplicate message", zap.String("duplicateMsg", msg.Id))
				continue
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.ErrorNoSentry(ctx, "error querying email", zap.Error(err))
			}
		}
		// If record not found, send to drumkit api. If other error, occasional duplicate processing is fine

		// NOTE: It's critical that we reset the `shouldSkipDraftMsg` flag here to false so we don't carry-over
		// the previous flag state.
		shouldSkipDraftMsg := false
		// Most messages usually only have 1 Label ID.
		for _, labelID := range msg.LabelIds {
			if labelID == "DRAFT" {
				shouldSkipDraftMsg = true
				break
			}
		}

		if shouldSkipDraftMsg {
			log.Info(ctx, "skipping gmail draft msg", zap.Any("fullMsg", msg))
			continue
		}

		gmailMsg := emails.GmailMessage{Message: msg}

		email := models.OnPremEmail{
			Account:          user.EmailAddress,
			UserID:           user.ID,
			ExternalID:       gmailMsg.GetID(),
			RFCMessageID:     gmailMsg.GetRFCMessageID(),
			SentAt:           gmailMsg.GetInternalDate(),
			ThreadID:         gmailMsg.GetThreadID(),
			ThreadReferences: gmailMsg.GetThreadReferences(),
		}
		err := emailDB.Create(ctx, &email)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to store email in DB",
				zap.String("msgId", gmailMsg.GetID()),
			)
		}

		payload, err := emails.PrepareEmailPayload(
			ctx,
			&gmailMsg,
			emails.WithEmailAddress(user.EmailAddress),
			emails.WithExternalUserID(user.ID),
			emails.WithExternalEmailID(email.ID),
			emails.WithOnPrem(true),
		)
		if err != nil {
			log.Error(
				ctx,
				"failed to prepare email payload to send to drumkit",
				zap.Uint("externalEmailId", email.ID),
				zap.Error(err),
			)
		}

		// Use the full email body (including inline replies) when classifying
		labels, approach, _, _ := emails.Classify(ctx, *payload)
		log.Info(ctx, "email labels", zap.Strings("labels", labels))
		// TODO: the customer should be able to specify the email labels via an environment variable
		// that are okay to forward to drumkit
		if slices.Contains(labels, string(emails.TruckListLabel)) {
			payload.Label = string(emails.TruckListLabel)
			payload.ClassificationMethod = approach

			err := ForwardEmailToDrumkit(ctx, Payload{Email: payload})
			if err != nil {
				return c.SendStatus(http.StatusInternalServerError)
			}
		}
	}

	if err := updateUserHistory(ctx, user, msgData.HistoryID, newWebhookExpiry); err != nil {
		log.Error(ctx, "updateUserHistory failed", zap.Error(err))
	}

	return c.SendStatus(http.StatusOK)
}

func updateUserHistory(
	ctx context.Context,
	user models.OnPremUser,
	msgHistoryID uint64,
	newWebhookExpiry time.Time,
) error {

	if msgHistoryID != 0 && user.GmailLastHistoryID != msgHistoryID ||
		newWebhookExpiry.Sub(user.WebhookExpiration) > 0 {
		user.GmailLastHistoryID = msgHistoryID
		// If time = 0, Gorm will not update by default
		user.WebhookExpiration = newWebhookExpiry

		log.Info(
			ctx,
			"updating user in DB",
			zap.String("emailAddress", user.EmailAddress),
			zap.Uint64("historyId", user.GmailLastHistoryID),
		)

		if err := dbUpdateUserFunc(ctx, user); err != nil {
			return fmt.Errorf("rds.UpdateUser failed: %w", err)
		}

		payload := UpdatedUserPayload{
			ID:                user.ID,
			EmailAddress:      user.EmailAddress,
			WebhookExpiration: newWebhookExpiry,
		}

		if err := forwardUpdatedUserToDrumkit(ctx, payload); err != nil {
			return fmt.Errorf("drumkit update user failed: %w", err)
		}
	}

	return nil
}

// Simple time-based function to filter out Sentry reports due to auth issues and prevent spikes
// Specific to oauth issues for now until more cases are known
func shouldSendToSentry() bool {
	now := time.Now()
	return now.Minute()%10 == 0 && now.Second()%25 == 0
}

func getMsgIDs(ctx context.Context, client gmailclient.Client, user *models.OnPremUser) (msgIDs []string, err error) {
	resp, err := client.ListHistory(ctx, user.GmailLastHistoryID)
	if err != nil {
		return msgIDs, err
	}

	for _, history := range resp.History {
		for _, msgAdded := range history.MessagesAdded {
			msgIDs = append(msgIDs, msgAdded.Message.Id)
		}
	}

	return msgIDs, nil
}
