package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/gofiber/fiber/v2"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/services"
	"github.com/drumkitai/drumkit/fn/watchinbox/env"
)

const (
	devPort        = ":5010"                  // Port of the local fiber service in dev mode.
	serviceName    = "watch-inbox-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"                  // Version of the service.
)

func main() {
	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-watchinbox")

	log.Debug(ctx, "IN DEBUG MODE")

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)
	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-watchinbox"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.AppEnv == "dev" {
		app := fiber.New()
		shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
			log.Info(ctx, "shutting down fiber server")
			return app.Shutdown()
		})

		errChan := runLocalServer(ctx, app)
		for {
			select {
			case err := <-errChan:
				log.Error(ctx, "fiber server failed to start", zap.Error(err))
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

func handlerWithLogging(ctx context.Context) (result *events.LambdaFunctionURLResponse, err error) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		log.Info(ctx, "received AWS EventBridge Request")

		if result, err = handler(ctx); err != nil {
			return
		}

		log.Info(ctx, "returning AWS EventBridge Response", zap.Any("response", result))
	})

	return
}

func handler(ctx context.Context) (*events.LambdaFunctionURLResponse, error) {
	return refreshAllUserTokens(ctx)
}

func refreshAllUserTokens(ctx context.Context) (_ *events.LambdaFunctionURLResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "refreshAllUserTokens", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "failed to refresh all user tokens", zap.Error(err))
		}
		metaSpan.End(err)
	}()

	users, err := userDB.GetAllGmailAndOutlook(ctx)
	if err != nil {
		return nil, fmt.Errorf("users lookup failed: %w", err)
	}

	var wg sync.WaitGroup
	for _, user := range users {
		if hoursLeft := time.Until(user.WebhookExpiration).Hours(); hoursLeft < 24 {
			wg.Add(1)
			go func(user models.User) {
				defer wg.Done()
				err := rewatchInbox(ctx, &user)
				log.Warn(
					ctx,
					"error watching inbox",
					zap.Uint("userID", user.ID),
					zap.String("emailAddress", user.EmailAddress),
					zap.Error(err),
				)
			}(user)
		}
	}
	wg.Wait()

	return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}, nil
}

func rewatchInbox(ctx context.Context, user *models.User) error {
	if user.IsOnPrem {
		return rewatchOnPremUserInbox(ctx, user)
	}

	switch user.EmailProvider {
	case models.GmailEmailProvider:
		return rewatchGmailInbox(ctx, user)
	case models.OutlookEmailProvider:
		return rewatchOutlookInbox(ctx, user)
	default:
		return errors.New("user's email provider doesn't exist")
	}
}

func rewatchGmailInbox(ctx context.Context, user *models.User) error {
	client, err := gmailclient.New(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, user)
	if err != nil {
		return err
	}

	ctx = log.With(
		ctx,
		zap.String("emailAddress", user.EmailAddress),
		zap.String("emailProvider", string(user.EmailProvider)),
	)

	req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}

	updatedSub, err := client.WatchInbox(ctx, req)
	if err != nil {
		log.Warn(ctx, "error watching inbox", zap.Error(err))

		return fmt.Errorf("error watching %s Gmail inbox: %w", user.EmailAddress, err)
	}

	log.Info(
		ctx,
		"successfully re-subscribed to inbox",
		zap.Time("newExpiration", time.UnixMilli(updatedSub.Expiration)),
	)

	user.WebhookExpiration = time.UnixMilli(updatedSub.Expiration)
	if err = userDB.Update(ctx, *user); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(ctx, "error updating subscription expiration", zap.Error(err))

		return err
	}

	return nil
}

func rewatchOutlookInbox(ctx context.Context, user *models.User) error {
	client, err := msclient.New(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, user)
	if err != nil {
		return err
	}

	ctx = log.With(
		ctx,
		zap.String("emailAddress", user.EmailAddress),
		zap.String("emailProvider", string(user.EmailProvider)),
	)

	updatedSub, err := client.RewatchInbox(ctx, env.Vars.MicrosoftWebhookURL, user)
	if err != nil {
		log.Warn(ctx, "error watching inbox", zap.Error(err))

		return fmt.Errorf("error watching %s Outlook inbox: %w", user.EmailAddress, err)
	}

	log.Info(
		ctx,
		"successfully re-subscribed to inbox",
		zap.Time("newExpiration", updatedSub.ExpirationDateTime),
		zap.String("subId", updatedSub.ID),
	)

	user.WebhookExpiration = updatedSub.ExpirationDateTime
	user.OutlookSubscriptionID = updatedSub.ID

	if err = userDB.Update(ctx, *user); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(
			ctx,
			"error updating subscription expiration",
			zap.String("subId", updatedSub.ID),
			zap.Error(err),
		)

		return err
	}

	return nil
}

func rewatchOnPremUserInbox(ctx context.Context, onPremUser *models.User) error {
	ctx = log.With(
		ctx,
		zap.Uint("userID", onPremUser.ID),
		zap.String("emailAddress", onPremUser.EmailAddress),
		zap.String("emailProvider", string(onPremUser.EmailProvider)),
	)

	service, err := rds.GetServiceByID(ctx, onPremUser.ServiceID)
	if err != nil {
		log.Error(
			ctx,
			"failed to find service corresponding to the onprem user",
			zap.Error(err),
		)

		return err
	}

	serviceName := strings.ToLower(service.Name)
	watchInboxURL, err := services.GetOnPremWatchInboxURL(serviceName)
	if err != nil {
		return err
	}

	queryParams := url.Values{}
	queryParams.Set("email", onPremUser.EmailAddress)

	if serviceName == "redwood" {
		queryParams.Set("code", service.OnPremAuthToken)
	}

	fullURL := watchInboxURL + "?" + queryParams.Encode()
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, fullURL, nil)
	if err != nil {
		log.Error(
			ctx,
			"failed to construct http request to send",
			zap.String("customer", serviceName),
			zap.Error(err),
		)

		return err
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		log.Error(
			ctx,
			"failed to send http request",
			zap.String("customer", serviceName),
			zap.Error(err),
		)

		return err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var updatedUser models.OnPremUser
	if err = json.Unmarshal(respBody, &updatedUser); err != nil {
		log.Error(ctx, "failed to unmarshal response body to rewatch onprem inbox", zap.Error(err))

		return err
	}

	log.Info(
		ctx,
		"successfully re-subscribed to inbox",
		zap.Time("newTokenExpiration", updatedUser.TokenExpiry),
		zap.Time("newWebhookExpiration", updatedUser.WebhookExpiration),
	)

	if err = userDB.Update(ctx, *onPremUser); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(
			ctx,
			"error updating subscription expiration",
			zap.Error(err),
		)

		return err
	}

	return nil
}
