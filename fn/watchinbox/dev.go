package main

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
)

func runLocalServer(ctx context.Context, app *fiber.App) <-chan error {
	errChan := make(chan error, 1)

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/watchinbox", fiberHandler)

	go func(ctx context.Context) {
		if err := app.Listen(devPort); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Error(ctx, "fiber server failed", zap.Error(err))
			errChan <- err
		}
	}(ctx)
	return errChan
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	log.Info(ctx, "refreshing tokens in drumkit-watch-inbox handler", zap.Any("time", time.Now()))

	result, err := handler(ctx)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(result.StatusCode).SendString(result.Body)
}
