package main

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/cloudwatch"
	"github.com/aws/aws-sdk-go-v2/service/cloudwatch/types"
	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// mockCloudWatchClient implements the cloudwatchAPI for tests.
type mockCloudWatchClient struct {
	GetMetricDataFunc func(
		ctx context.Context,
		params *cloudwatch.GetMetricDataInput,
		optFns ...func(*cloudwatch.Options),
	) (*cloudwatch.GetMetricDataOutput, error)
}

func (m *mockCloudWatchClient) GetMetricData(
	ctx context.Context,
	params *cloudwatch.GetMetricDataInput,
	optFns ...func(*cloudwatch.Options),
) (*cloudwatch.GetMetricDataOutput, error) {
	if m.GetMetricDataFunc != nil {
		return m.GetMetricDataFunc(ctx, params, optFns...)
	}
	return nil, errors.New("GetMetricDataFunc not implemented")
}

func TestThrottler_IsThrottled(t *testing.T) {
	ctx := context.Background()
	config := SqsThrottlerConfig{
		RDSClusterIdentifier:            "test-cluster",
		ProcessorSQSQueueURL:            "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
		ThrottleRDSMaxConnections:       100,
		ThrottleProcessorMaxConcurrency: 50,
	}

	testCases := []struct {
		name                string
		mockCloudWatch      func(*mockCloudWatchClient)
		setupRedisMock      func(redismock.ClientMock)
		expectedIsThrottled bool
		expectedErrContains string
	}{
		{
			name: "Not Throttled",
			setupRedisMock: func(mock redismock.ClientMock) {
				mock.ExpectGet(throttlerCacheKey).RedisNil()
				mock.ExpectSet(throttlerCacheKey, "false", notThrottledCacheTTL).SetVal("OK")
			},
			mockCloudWatch: func(m *mockCloudWatchClient) {
				m.GetMetricDataFunc = func(
					_ context.Context,
					params *cloudwatch.GetMetricDataInput,
					_ ...func(*cloudwatch.Options),
				) (*cloudwatch.GetMetricDataOutput, error) {
					metricName := *params.MetricDataQueries[0].MetricStat.Metric.MetricName
					if metricName == "DatabaseConnections" {
						return &cloudwatch.GetMetricDataOutput{
							MetricDataResults: []types.MetricDataResult{{
								Values:     []float64{50},
								StatusCode: types.StatusCodeComplete,
							}},
						}, nil
					}
					if metricName == "ConcurrentExecutions" {
						return &cloudwatch.GetMetricDataOutput{
							MetricDataResults: []types.MetricDataResult{{
								Values:     []float64{25},
								StatusCode: types.StatusCodeComplete,
							}},
						}, nil
					}
					return nil, fmt.Errorf("unexpected metric: %s", metricName)
				}
			},
			expectedIsThrottled: false,
		},
		{
			name: "Throttled on Cache Hit",
			setupRedisMock: func(mock redismock.ClientMock) {
				mock.ExpectGet(throttlerCacheKey).SetVal("true")
			},
			mockCloudWatch: func(m *mockCloudWatchClient) {
				m.GetMetricDataFunc = func(
					_ context.Context,
					_ *cloudwatch.GetMetricDataInput,
					_ ...func(*cloudwatch.Options),
				) (*cloudwatch.GetMetricDataOutput, error) {
					t.Fatal("CloudWatch should not be called when Redis cache is hit")
					return nil, nil
				}
			},
			expectedIsThrottled: true,
		},
		{
			name: "RDS Throttled",
			mockCloudWatch: func(m *mockCloudWatchClient) {
				m.GetMetricDataFunc = func(
					_ context.Context,
					_ *cloudwatch.GetMetricDataInput,
					_ ...func(*cloudwatch.Options),
				) (*cloudwatch.GetMetricDataOutput, error) {
					return &cloudwatch.GetMetricDataOutput{
						MetricDataResults: []types.MetricDataResult{{
							Values:     []float64{150},
							StatusCode: types.StatusCodeComplete,
						}},
					}, nil
				}
			},
			setupRedisMock: func(mock redismock.ClientMock) {
				mock.ExpectGet(throttlerCacheKey).RedisNil()
				mock.ExpectSet(throttlerCacheKey, "true", throttledCacheTTL).SetVal("OK")
			},
			expectedIsThrottled: true,
		},
		{
			name: "Lambda Throttled",
			mockCloudWatch: func(m *mockCloudWatchClient) {
				m.GetMetricDataFunc = func(
					_ context.Context,
					params *cloudwatch.GetMetricDataInput,
					_ ...func(*cloudwatch.Options),
				) (*cloudwatch.GetMetricDataOutput, error) {
					metricName := *params.MetricDataQueries[0].MetricStat.Metric.MetricName
					if metricName == "DatabaseConnections" {
						return &cloudwatch.GetMetricDataOutput{
							MetricDataResults: []types.MetricDataResult{{
								Values:     []float64{50},
								StatusCode: types.StatusCodeComplete,
							}},
						}, nil
					}
					if metricName == "ConcurrentExecutions" {
						return &cloudwatch.GetMetricDataOutput{
							MetricDataResults: []types.MetricDataResult{{
								Values:     []float64{75},
								StatusCode: types.StatusCodeComplete,
							}},
						}, nil
					}
					return nil, fmt.Errorf("unexpected metric: %s", metricName)
				}
			},
			setupRedisMock: func(mock redismock.ClientMock) {
				mock.ExpectGet(throttlerCacheKey).RedisNil()
				mock.ExpectSet(throttlerCacheKey, "true", throttledCacheTTL).SetVal("OK")
			},
			expectedIsThrottled: true,
		},
		{
			name: "CloudWatch GetMetricData Error",
			setupRedisMock: func(mock redismock.ClientMock) {
				mock.ExpectGet(throttlerCacheKey).RedisNil()
			},
			mockCloudWatch: func(m *mockCloudWatchClient) {
				m.GetMetricDataFunc = func(
					_ context.Context,
					_ *cloudwatch.GetMetricDataInput,
					_ ...func(*cloudwatch.Options),
				) (*cloudwatch.GetMetricDataOutput, error) {
					return nil, errors.New("internal cloudwatch error")
				}
			},
			expectedIsThrottled: false,
			expectedErrContains: "internal cloudwatch error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// -- Arrange --
			mockCwClient := &mockCloudWatchClient{}
			if tc.mockCloudWatch != nil {
				tc.mockCloudWatch(mockCwClient)
			}

			db, mock := redismock.NewClientMock()
			if tc.setupRedisMock != nil {
				tc.setupRedisMock(mock)
			}

			throttler := NewSqsThrottler(mockCwClient, db, config)

			// -- Act --
			isThrottled, err := throttler.IsThrottled(ctx)

			// -- Assert --
			assert.Equal(t, tc.expectedIsThrottled, isThrottled)
			if tc.expectedErrContains != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErrContains)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
