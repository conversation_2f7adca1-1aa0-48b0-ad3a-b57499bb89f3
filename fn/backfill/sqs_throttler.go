package main

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/cloudwatch"
	"github.com/aws/aws-sdk-go-v2/service/cloudwatch/types"
	redisclient "github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

var errNoCloudWatchDataPoints = errors.New("no CloudWatch data points found")

// SqsThrottlerConfig holds the configuration values for the throttler.
type SqsThrottlerConfig struct {
	RDSClusterIdentifier            string
	ProcessorSQSQueueURL            string
	ProcessorFunctionName           string
	ThrottleRDSMaxConnections       int
	ThrottleProcessorMaxConcurrency int
}

// SqsThrottler is responsible for checking if the system is currently throttled.
type SqsThrottler struct {
	client cloudwatchAPI
	redis  *redisclient.Client
	config SqsThrottlerConfig
}

// NewSqsThrottler creates a new SqsThrottler with the given dependencies.
func NewSqsThrottler(client cloudwatchAPI, redis *redisclient.Client, config SqsThrottlerConfig) *SqsThrottler {
	return &SqsThrottler{
		client: client,
		redis:  redis,
		config: config,
	}
}

const (
	// throttlerCacheKey is the Redis key used to cache the throttled state.
	throttlerCacheKey = "email:backfill:throttled"
	// throttledCacheTTL is the time-to-live for the throttled state in Redis.
	throttledCacheTTL = 60 * time.Second
	// notThrottledCacheTTL is the time-to-live for the not-throttled state in Redis.
	notThrottledCacheTTL = 15 * time.Second
)

// IsThrottled checks if the system is currently above its operational thresholds.
// It uses a Redis cache to avoid repeatedly checking CloudWatch when the system is throttled.
func (t *SqsThrottler) IsThrottled(ctx context.Context) (isThrottled bool, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "IsThrottled", nil)
	defer func() { metaSpan.End(err) }()

	// 1. Check Redis cache first.
	cachedVal, redisErr := t.redis.Get(ctx, throttlerCacheKey).Result()
	if redisErr != nil && !errors.Is(redisErr, redis.NilEntry) {
		// Log the error but proceed to check CloudWatch as a fallback.
		log.Warn(ctx, "failed to get throttler cache from Redis", zap.Error(redisErr))
	}
	if cachedVal == "true" {
		log.Info(ctx, "system is currently throttled (cached result)")
		return true, nil
	}
	if cachedVal == "false" {
		log.Info(ctx, "system is not currently throttled (cached result)")
		return false, nil
	}

	// 2. Check RDS Connections
	rdsConnections, err := t.getLatestCloudWatchMetric(
		ctx,
		"AWS/RDS",
		"DatabaseConnections",
		[]types.Dimension{{Name: aws.String("DBClusterIdentifier"), Value: &t.config.RDSClusterIdentifier}},
	)
	if err != nil {
		return false, fmt.Errorf("failed to get RDS connections metric: %w", err)
	}
	if rdsConnections > float64(t.config.ThrottleRDSMaxConnections) {
		log.WarnNoSentry(ctx, "RDS connection limit exceeded, throttling", zap.Float64("connections", rdsConnections))
		// Cache the throttled state
		if cacheErr := t.redis.Set(ctx, throttlerCacheKey, "true", throttledCacheTTL).Err(); cacheErr != nil {
			log.Warn(ctx, "failed to set throttler cache in Redis", zap.Error(cacheErr))
		}
		return true, nil
	}

	// 3. Check Processor Queue Length
	concurrentExecutions, err := t.getLatestCloudWatchMetric(
		ctx,
		"AWS/Lambda",
		"ConcurrentExecutions",
		[]types.Dimension{{Name: aws.String("FunctionName"), Value: &t.config.ProcessorFunctionName}},
	)
	if err != nil {
		if errors.Is(err, errNoCloudWatchDataPoints) {
			// It's possible for ConcurrentExecutions to have no data points if there are no executions.
			// In this case, we can safely assume the value is 0.
			log.Info(
				ctx,
				"no data points for ConcurrentExecutions, assuming 0",
				zap.String("functionName", t.config.ProcessorFunctionName),
				zap.Error(err),
			)
			concurrentExecutions = 0
		} else {
			return false, fmt.Errorf("failed to get SQS queue length metric: %w", err)
		}
	}
	if concurrentExecutions > float64(t.config.ThrottleProcessorMaxConcurrency) {
		log.Warn(
			ctx,
			"Processor concurrent executions threshold exceeded, throttling",
			zap.Float64("concurrentExecutions", concurrentExecutions),
		)
		// Cache the throttled state
		if cacheErr := t.redis.Set(ctx, throttlerCacheKey, "true", throttledCacheTTL).Err(); cacheErr != nil {
			log.Warn(ctx, "failed to set throttler cache in Redis", zap.Error(cacheErr))
		}
		return true, nil
	}

	// 4. Cache the "not throttled" state
	if cacheErr := t.redis.Set(ctx, throttlerCacheKey, "false", notThrottledCacheTTL).Err(); cacheErr != nil {
		log.Warn(ctx, "failed to set not-throttled cache in Redis", zap.Error(cacheErr))
	}

	return false, nil
}

// getLatestCloudWatchMetric is a helper that fetches a single metric value from CloudWatch.
func (t *SqsThrottler) getLatestCloudWatchMetric(
	ctx context.Context,
	namespace,
	metricName string,
	dimensions []types.Dimension,
) (metricValue float64, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getLatestCloudWatchMetric", nil)
	defer func() { metaSpan.End(err) }()

	result, err := t.client.GetMetricData(ctx, &cloudwatch.GetMetricDataInput{
		MetricDataQueries: []types.MetricDataQuery{
			{
				Id: aws.String("m1"),
				MetricStat: &types.MetricStat{
					Metric: &types.Metric{
						Namespace:  aws.String(namespace),
						MetricName: aws.String(metricName),
						Dimensions: dimensions,
					},
					Period: aws.Int32(60),
					Stat:   aws.String("Maximum"),
				},
				ReturnData: aws.Bool(true),
			},
		},
		StartTime: aws.Time(time.Now().Add(-5 * time.Minute)),
		EndTime:   aws.Time(time.Now()),
	})
	if err != nil {
		return 0, err
	}

	if len(result.MetricDataResults) == 0 {
		return 0, fmt.Errorf("metric %s: %w", metricName, errNoCloudWatchDataPoints)
	}

	metricResult := result.MetricDataResults[0]
	if metricResult.StatusCode != types.StatusCodeComplete {
		// StatusCode can be PartialData, InternalError, etc. We'll treat anything other than Complete as an error.
		return 0, fmt.Errorf("metric %s: unexpected CloudWatch status code: %s", metricName, metricResult.StatusCode)
	}

	if len(metricResult.Values) == 0 {
		return 0, fmt.Errorf("metric %s: %w", metricName, errNoCloudWatchDataPoints)
	}

	return metricResult.Values[0], nil
}
