package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars envVars

type envVars struct {
	rds.EnvConfig

	AppEnv               string `envconfig:"APP_ENV" required:"true"`
	LiveRun              bool   `envconfig:"LIVE_RUN" default:"false"`
	SkipDuplicates       bool   `envconfig:"SKIP_DUPLICATES" default:"true"`
	DefaultBackfillHours int    `envconfig:"DEFAULT_BACKFILL_HOURS" default:"24"`

	// Infrastructure URLs
	RedisURL             string `envconfig:"REDIS_URL"`
	ProcessorSQSQueueURL string `envconfig:"PROCESSOR_SQS_QUEUE_URL" required:"true"`
	BackfillSQSQueueURL  string `envconfig:"BACKFILL_SQS_QUEUE_URL" required:"true"`
	S3BucketName         string `envconfig:"S3_BUCKET_NAME"`

	// Throttling configuration
	RDSClusterIdentifier            string `envconfig:"RDS_CLUSTER_IDENTIFIER" required:"true"`
	ThrottleRDSMaxConnections       int    `envconfig:"THROTTLE_RDS_MAX_CONNECTIONS" default:"500"`
	ThrottleProcessorMaxConcurrency int    `envconfig:"THROTTLE_PROCESSOR_MAX_CONCURRENCY" default:"500"`

	// Observability
	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`
	SentryDSN         string `envconfig:"SENTRY_DSN"`

	// Provider secrets
	GoogleClientID        string `envconfig:"GOOGLE_CLIENT_ID"`
	MicrosoftClientID     string `envconfig:"MICROSOFT_CLIENT_ID"`
	IngestionSecretARN    string `envconfig:"INGESTION_SECRET_ARN" required:"true"`
	GoogleClientSecret    string `envconfig:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `envconfig:"MICROSOFT_CLIENT_SECRET"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey                string `json:"AES_KEY"`
	GoogleClientSecret    string `json:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `json:"MICROSOFT_CLIENT_SECRET"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file found")
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.GoogleClientSecret == "" {
			log.Warn(ctx, "missing GOOGLE_CLIENT_SECRET env var")
		}
		if Vars.MicrosoftClientSecret == "" {
			log.Warn(ctx, "missing MICROSOFT_CLIENT_SECRET env var")
		}

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.IngestionSecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.GoogleClientSecret == "" || secret.MicrosoftClientSecret == "" {
			return fmt.Errorf("%s is missing some fields", Vars.IngestionSecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.GoogleClientSecret = secret.GoogleClientSecret
		Vars.MicrosoftClientSecret = secret.MicrosoftClientSecret
	}

	return nil
}
