// co-author: <PERSON><PERSON><PERSON> <<EMAIL>>
// co-authored-by: G<PERSON><PERSON> <<EMAIL>>
package main

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

func TestOutlookBackfill_Success(t *testing.T) {
	// -- Arrange --
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobEndTime := time.Date(2023, 1, 4, 0, 0, 0, 0, time.UTC)
	jobStartTime := jobEndTime.Add(-72 * time.Hour)
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "outlook",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}

	mockMsClient := &mock.Client{}
	mockBackfillSqs := &mockSqsClient{}
	var syncWindowCalls int

	backfiller := &OutlookBackfiller{
		processorSqs: &mockSqsClient{},
		backfillSqs:  mockBackfillSqs,
		throttler:    &mockThrottler{},
		newMsClient: func(
			_ context.Context,
			_, _ string,
			_ *models.User,
			_ ...oauth.Option,
		) (msclient.Client, error) {
			return mockMsClient, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{Model: gorm.Model{ID: 1}}, nil
		},
		syncWindow: func(
			_ context.Context,
			_ msclient.Client,
			_ *models.User,
			_ *models.Service,
			_ time.Time,
			_ time.Time,
		) error {
			syncWindowCalls++
			return nil
		},
	}

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.True(t, isComplete, "Expected backfill to be marked as complete")
	assert.Equal(t, 24, syncWindowCalls, "Expected syncWindow to be called 24 times for 3 days")
}

func TestOutlookBackfill_FullRun(t *testing.T) {
	// -- Arrange --
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobEndTime := time.Date(2023, 1, 3, 0, 0, 0, 0, time.UTC)
	jobStartTime := jobEndTime.Add(-48 * time.Hour)
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "outlook",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}

	var syncCalled bool
	backfiller := &OutlookBackfiller{
		throttler: &mockThrottler{},
		newMsClient: func(
			_ context.Context,
			_, _ string,
			_ *models.User,
			_ ...oauth.Option,
		) (msclient.Client, error) {
			return &mock.Client{}, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{}, nil
		},
		syncWindow: func(
			_ context.Context,
			_ msclient.Client,
			_ *models.User,
			_ *models.Service,
			_ time.Time,
			_ time.Time,
		) error {
			syncCalled = true
			return nil
		},
	}

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.True(t, isComplete)
	assert.True(t, syncCalled, "Expected sync function to be called")
}

func TestOutlookBackfill_ClientCreationError(t *testing.T) {
	// -- Arrange --
	testUser := models.User{Model: gorm.Model{ID: 1}}
	job := models.BackfillJobPayload{UserID: testUser.ID}
	expectedErr := errors.New("client creation failed")

	backfiller := &OutlookBackfiller{
		newMsClient: func(
			_ context.Context,
			_, _ string,
			_ *models.User,
			_ ...oauth.Option,
		) (msclient.Client, error) {
			return nil, expectedErr
		},
	}

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	assert.False(t, isComplete)
	require.Error(t, err)
	assert.Contains(t, err.Error(), expectedErr.Error())
}

func TestOutlookBackfill_ContinuationOnTimeout(t *testing.T) {
	// -- Arrange --
	var sentMessages []*sqs.SendMessageInput
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobStartTime := time.Now().Add(-48 * time.Hour)
	jobEndTime := time.Now()
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "outlook",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}
	mockBackfillSqs := &mockSqsClient{
		SendMessageFunc: func(
			_ context.Context,
			params *sqs.SendMessageInput,
			_ ...func(*sqs.Options),
		) (*sqs.SendMessageOutput, error) {
			sentMessages = append(sentMessages, params)
			return nil, nil
		},
	}

	backfiller := &OutlookBackfiller{
		backfillSqs:            mockBackfillSqs,
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
		newMsClient: func(
			_ context.Context,
			_, _ string,
			_ *models.User,
			_ ...oauth.Option,
		) (msclient.Client, error) {
			return &mock.Client{}, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{}, nil
		},
	}

	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(10*time.Millisecond))
	defer cancel()

	// -- Act --
	isComplete, err := backfiller.backfill(ctx, testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.False(t, isComplete, "Expected backfill to be marked as incomplete due to timeout")
	require.Len(t, sentMessages, 1, "Expected one message to be sent to SQS for continuation")

	var continuationJob models.BackfillJobPayload
	err = json.Unmarshal([]byte(*sentMessages[0].MessageBody), &continuationJob)
	require.NoError(t, err)
	assert.Equal(t, job.UserID, continuationJob.UserID)
	assert.WithinDuration(t, jobStartTime, *continuationJob.StartTime, time.Second)
	assert.WithinDuration(t, jobEndTime, *continuationJob.EndTime, time.Second)
}

func TestOutlookBackfill_ContinuationOnThrottle(t *testing.T) {
	// -- Arrange --
	var sentMessages []*sqs.SendMessageInput
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobStartTime := time.Now().Add(-48 * time.Hour)
	jobEndTime := time.Now()
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "outlook",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}
	mockBackfillSqs := &mockSqsClient{
		SendMessageFunc: func(
			_ context.Context,
			params *sqs.SendMessageInput,
			_ ...func(*sqs.Options),
		) (*sqs.SendMessageOutput, error) {
			sentMessages = append(sentMessages, params)
			return nil, nil
		},
	}
	mockMsClient := &mock.Client{}
	mockThrottler := &mockThrottler{isThrottled: true}

	backfiller := &OutlookBackfiller{
		processorSqs: &mockSqsClient{},
		backfillSqs:  mockBackfillSqs,
		throttler:    mockThrottler,
		newMsClient: func(
			_ context.Context,
			_, _ string,
			_ *models.User,
			_ ...oauth.Option,
		) (msclient.Client, error) {
			return mockMsClient, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{Model: gorm.Model{ID: 1}}, nil
		},
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
		syncWindow: func(
			ctx context.Context,
			_ msclient.Client,
			_ *models.User,
			_ *models.Service,
			_ time.Time,
			_ time.Time,
		) error {
			// This is the key part for the throttle test.
			// The real syncWindow would check the throttler.
			// We simulate this by returning the throttled error directly.
			isThrottled, err := mockThrottler.IsThrottled(ctx)
			if err != nil {
				return err
			}
			if isThrottled {
				return errThrottled
			}
			return nil
		},
	}

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.False(t, isComplete, "Expected backfill to be marked as incomplete due to throttling")
	require.Len(t, sentMessages, 1, "Expected one message to be sent to SQS for continuation")

	var continuationJob models.BackfillJobPayload
	err = json.Unmarshal([]byte(*sentMessages[0].MessageBody), &continuationJob)
	require.NoError(t, err)
	assert.Equal(t, job.UserID, continuationJob.UserID)
	assert.WithinDuration(t, jobStartTime, *continuationJob.StartTime, time.Second)
	assert.WithinDuration(t, jobEndTime, *continuationJob.EndTime, time.Second)
}
