version: "3.8"

# This is a minimal docker-compose file for running the backfill service's
# local dependencies. It ONLY starts LocalStack.
#
# To run, navigate to this directory (drumkit/fn/backfill) and run:
#   docker-compose up -d
#
# DO NOT run docker-compose from the root of the repository, as that may
# start a much larger, resource-intensive stack.

services:
  localstack:
    image: localstack/localstack:3
    container_name: localstack_backfill
    ports:
      - "4566:4566"
    environment:
      # Starting only the required services saves a lot of memory.
      - SERVICES=sqs,s3,cloudwatch
      - QUEUES=backfill-jobs-local,processor-local

  redis:
    image: redis:alpine
    container_name: redis_backfill
    ports:
      - "6379:6379" 