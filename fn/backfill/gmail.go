package main

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	gmail_helpers "github.com/drumkitai/drumkit/common/gmail"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/backfill/env"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

// GmailBackfiller holds the dependencies for the Gmail backfill process.
type GmailBackfiller struct {
	processorSqs   sqsclient.API
	backfillSqs    sqsclient.API
	s3             s3backup.Archiver
	throttler      throttler
	newGmailClient func(
		ctx context.Context,
		clientID, clientSecret string,
		user *models.User,
		opts ...oauth.Option,
	) (gmailclient.Client, error)
	getService             func(context.Context, uint) (models.Service, error)
	getEmail               func(context.Context, string) (*models.Email, error)
	enqueueContinuationJob func(
		ctx context.Context,
		sqs sqsclient.API,
		queueURL string,
		provider string,
		user *models.User,
		opts ...helpers.EnqueueBackfillOption,
	) error
	syncWindow func(
		ctx context.Context,
		client gmailclient.Client,
		user *models.User,
		service *models.Service,
		startTime time.Time,
		endTime time.Time,
	) error
}

// newGmailBackfiller creates a new GmailBackfiller with production dependencies.
func newGmailBackfiller(
	processorSqs, backfillSqs sqsclient.API,
	s3 s3backup.Archiver,
	throttler throttler,
	getSvc func(context.Context, uint) (models.Service, error),
	getEmail func(context.Context, string) (*models.Email, error),
) *GmailBackfiller {
	b := &GmailBackfiller{
		processorSqs:           processorSqs,
		backfillSqs:            backfillSqs,
		s3:                     s3,
		throttler:              throttler,
		newGmailClient:         gmailclient.New[*models.User],
		getService:             getSvc,
		getEmail:               getEmail,
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
	}
	b.syncWindow = b.syncMessagesInWindow
	return b
}

// Backfill performs the backfill operation for Gmail. It satisfies the backfiller interface.
func (b *GmailBackfiller) backfill(ctx context.Context, user models.User, job models.BackfillJobPayload) (bool, error) {
	log.Info(ctx, "starting gmail backfill")

	client, err := b.newGmailClient(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, &user)
	if err != nil {
		return false, fmt.Errorf("failed to create gmail client: %w", err)
	}

	service, err := b.getService(ctx, user.ServiceID)
	if err != nil {
		return false, fmt.Errorf("failed to get service for user %d: %w", user.ID, err)
	}

	endTime := time.Now()
	if job.EndTime != nil {
		endTime = *job.EndTime
	}
	startTime := endTime.Add(-time.Duration(env.Vars.DefaultBackfillHours) * time.Hour)
	if job.StartTime != nil {
		startTime = *job.StartTime
	}

	log.Info(ctx, "processing window", zap.String("start", startTime.String()), zap.String("end", endTime.String()))

	const chunkSize = 3 * time.Hour    // Reduced from 24 hours to get more frequent timeout checks
	const safeExitTimeInMillis = 60000 // Increased to 60 seconds for a more conservative safety window

	// The backfill process works backwards from the end time in discrete chunks.
	// This allows the process to be resumable. If the job fails, is throttled,
	// or times out, it can be re-enqueued with a new time range that picks up
	// where the previous job left off.
	for t := endTime; t.After(startTime); {
		// Before processing a chunk, check if the function is about to time out.
		// If so, enqueue a continuation job for the remaining window and exit.
		deadline, ok := ctx.Deadline()
		if ok && time.Until(deadline).Milliseconds() < safeExitTimeInMillis {
			log.Info(
				ctx,
				"approaching timeout, creating continuation job",
				zap.Duration("remaining", time.Until(deadline)),
			)

			if err := b.enqueueContinuationJob(
				ctx,
				b.backfillSqs,
				env.Vars.BackfillSQSQueueURL,
				job.Provider,
				&user,
				helpers.WithTimeWindow(startTime, t),
				helpers.WithContinuation(),
			); err != nil {
				return false, fmt.Errorf("failed to enqueue continuation job: %w", err)
			}

			log.Info(ctx, "successfully enqueued continuation job")
			return false, nil
		}

		chunkStartTime := t.Add(-chunkSize)
		if chunkStartTime.Before(startTime) {
			chunkStartTime = startTime
		}

		log.Info(ctx, "processing chunk", zap.String("start", chunkStartTime.String()), zap.String("end", t.String()))

		err := b.syncWindow(
			ctx,
			client,
			&user,
			&service,
			chunkStartTime,
			t,
		)
		if err != nil {
			if errors.Is(err, errThrottled) {
				// If we are throttled, re-enqueue a job for the entire remaining window
				// to be processed later and exit.
				log.Info(ctx, "throttled during chunk sync, creating continuation job for remaining window")
				if enqueueErr := b.enqueueContinuationJob(
					ctx,
					b.backfillSqs,
					env.Vars.BackfillSQSQueueURL,
					job.Provider,
					&user,
					helpers.WithTimeWindow(startTime, t),
					helpers.WithContinuation(),
				); enqueueErr != nil {
					return false, fmt.Errorf("failed to enqueue continuation job after being throttled: %w", enqueueErr)
				}
				log.Info(ctx, "successfully enqueued continuation job after throttling")
				// Return false (job not complete) and no error, letting the new job handle the rest.
				return false, nil
			}

			// For any other error, log it and continue to the next chunk.
			log.Error(ctx, "failed to sync window chunk", zap.Error(err))
		}

		t = chunkStartTime
	}

	log.Info(ctx, "finished processing entire window")
	return true, nil
}

// syncMessagesInWindow retrieves message IDs for a given time window, fetches the full
// message details in batches, and enqueues them for processing. It will return
// a special error, errThrottled, if the system is currently throttled.
func (b *GmailBackfiller) syncMessagesInWindow(
	ctx context.Context,
	client gmailclient.Client,
	user *models.User,
	service *models.Service,
	startTime time.Time,
	endTime time.Time,
) error {
	msgIDs, err := client.ListMessagesBetweenDates(ctx, startTime, endTime)
	if err != nil {
		return fmt.Errorf("error listing messages between %v and %v: %w", startTime, endTime, err)
	}

	log.Info(ctx, "processing messages", zap.Int("count", len(msgIDs)), zap.Strings("msgIds", msgIDs))

	if len(msgIDs) == 0 {
		return nil
	}

	const batchSize = 100
	var groups [][]string

	for i := 0; i < len(msgIDs); i += batchSize {
		end := min(i+batchSize, len(msgIDs))
		groups = append(groups, msgIDs[i:end])
	}

	for _, group := range groups {
		throttled, err := b.throttler.IsThrottled(ctx)
		if err != nil {
			log.Error(ctx, "failed to check for throttling, stopping message sync", zap.Error(err))
			return fmt.Errorf("failed to check for throttling: %w", err)
		}
		if throttled {
			log.WarnNoSentry(ctx, "system is throttled, stopping message sync for this chunk")
			return errThrottled
		}

		groupMsgs, err := client.BatchGetMessages(ctx, group)
		if err != nil {
			return fmt.Errorf("failed to get msgs: %w", err)
		}

		for _, msg := range groupMsgs {
			if msg.Id == "" {
				log.WarnNoSentry(ctx, "skipping gmail msg with no id", zap.Any("fullMsg", msg))
				continue
			}

			if env.Vars.SkipDuplicates {
				existingEmail, err := b.getEmail(ctx, msg.Id)
				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					log.Error(ctx, "getEmailFunc failed, skipping message", zap.Error(err))
					continue // Skip this message when DB errors occur
				}
				if existingEmail != nil && existingEmail.ID != 0 {
					log.Info(ctx, "skipping duplicate email", zap.String("gmailMsgId", msg.Id))
					continue
				}
			}

			if err := gmail_helpers.ProcessAndEnqueueGmailMessage(
				ctx,
				client,
				b.processorSqs,
				b.s3,
				user,
				service,
				msg,
				env.Vars.ProcessorSQSQueueURL,
			); err != nil {
				// The helper function returns fatal errors, non-fatal ones are just logged.
				log.Error(ctx, "failed to process and enqueue message", zap.Error(err), zap.String("msgId", msg.Id))
			}
		}
	}

	return nil
}
