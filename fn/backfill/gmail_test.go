package main

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/gmail/v1"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	mem "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

// mockSqsClient implements the sqsAPI interface for tests
type mockSqsClient struct {
	SendMessageFunc func(
		ctx context.Context,
		params *sqs.SendMessageInput,
		optFns ...func(*sqs.Options),
	) (*sqs.SendMessageOutput, error)
}

func (m *mockSqsClient) SendMessage(
	ctx context.Context,
	params *sqs.SendMessageInput,
	optFns ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {
	if m.SendMessageFunc != nil {
		return m.SendMessageFunc(ctx, params, optFns...)
	}
	return nil, nil
}

// mockThrottler implements the throttler interface for tests
type mockThrottler struct {
	isThrottled bool
}

func (m *mockThrottler) IsThrottled(_ context.Context) (bool, error) {
	return m.isThrottled, nil
}

func (m *mockThrottler) GetLatestCloudWatchMetric(_ context.Context, _ string) (float64, error) {
	return 0, nil
}

func TestBackfill_Success(t *testing.T) {
	// -- Arrange --
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	// Use a fixed time window to avoid boundary condition errors with time.Now()
	jobEndTime := time.Date(2023, 1, 4, 0, 0, 0, 0, time.UTC)
	jobStartTime := jobEndTime.Add(-72 * time.Hour)
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "gmail",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}

	mockGmailSvc := &mem.Service{Messages: map[string]*gmail.Message{
		"msg1": {Id: "msg1", Payload: &gmail.MessagePart{Headers: []*gmail.MessagePartHeader{}}},
		"msg2": {Id: "msg2", Payload: &gmail.MessagePart{Headers: []*gmail.MessagePartHeader{}}},
	}}
	mockBackfillSqs := &mockSqsClient{}

	backfiller := &GmailBackfiller{
		processorSqs: &mockSqsClient{},
		backfillSqs:  mockBackfillSqs,
		throttler:    &mockThrottler{},
		newGmailClient: func(
			_ context.Context, _, _ string, _ *models.User,
			_ ...oauth.Option,
		) (gmailclient.Client, error) {
			return mockGmailSvc, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{Model: gorm.Model{ID: 1}}, nil
		},
		getEmail: func(_ context.Context, _ string) (*models.Email, error) {
			return &models.Email{}, nil
		},
	}
	backfiller.syncWindow = backfiller.syncMessagesInWindow

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.True(t, isComplete, "Expected backfill to be marked as complete")

	// For a 72-hour window with 3-hour chunks, we expect 24 chunks.
	// Each chunk makes 1 list call and 1 batch get call. 24 * 2 = 48 calls.
	assert.Len(t, mockGmailSvc.Calls, 48)
}

func TestBackfill_SkipsExisting(t *testing.T) {
	// -- Arrange --
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobEndTime := time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC)
	jobStartTime := jobEndTime.Add(-24 * time.Hour)
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "gmail",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}

	mockGmailSvc := &mem.Service{Messages: map[string]*gmail.Message{
		"msg1": {Id: "msg1", Payload: &gmail.MessagePart{Headers: []*gmail.MessagePartHeader{}}},
	}}
	// This SQS client should not be called
	mockProcessorSqs := &mockSqsClient{
		SendMessageFunc: func(
			_ context.Context, _ *sqs.SendMessageInput, _ ...func(*sqs.Options),
		) (*sqs.SendMessageOutput, error) {
			t.Fatal("processor SQS should not be called when emails are skipped")
			return nil, nil
		},
	}

	backfiller := &GmailBackfiller{
		processorSqs: mockProcessorSqs,
		backfillSqs:  &mockSqsClient{},
		throttler:    &mockThrottler{},
		newGmailClient: func(
			_ context.Context, _, _ string, _ *models.User,
			_ ...oauth.Option,
		) (gmailclient.Client, error) {
			return mockGmailSvc, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{Model: gorm.Model{ID: 1}}, nil
		},
		getEmail: func(_ context.Context, _ string) (*models.Email, error) {
			// Return an existing email to test the skipping logic
			return &models.Email{Model: gorm.Model{ID: 123}}, nil
		},
	}
	backfiller.syncWindow = backfiller.syncMessagesInWindow

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.True(t, isComplete, "Expected backfill to be marked as complete")
	// The Gmail service should be called 16 times for a 24-hour window with 3-hour chunks.
	// 8 for ListMessagesBetweenDates and 8 for BatchGetMessages
	assert.Len(t, mockGmailSvc.Calls, 16)
}

func TestBackfill_ContinuationOnTimeout(t *testing.T) {
	// -- Arrange --
	var sentMessages []*sqs.SendMessageInput
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobStartTime := time.Now().Add(-48 * time.Hour) // 2 days ago
	jobEndTime := time.Now()
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "gmail",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}
	mockBackfillSqs := &mockSqsClient{
		SendMessageFunc: func(
			_ context.Context,
			params *sqs.SendMessageInput,
			_ ...func(*sqs.Options),
		) (*sqs.SendMessageOutput, error) {
			sentMessages = append(sentMessages, params)
			return nil, nil
		},
	}

	backfiller := &GmailBackfiller{
		backfillSqs: mockBackfillSqs,
		// use the real continuation job enqueuer
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
		// Mocks are required for dependencies called before the timeout check.
		newGmailClient: func(
			_ context.Context, _, _ string, _ *models.User,
			_ ...oauth.Option,
		) (gmailclient.Client, error) {
			return &mem.Service{}, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{}, nil
		},
	}
	// We don't need to set syncWindow or other dependencies because the timeout should happen before they are called.

	// create a context that will timeout almost immediately
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(10*time.Millisecond))
	defer cancel()

	// -- Act --
	isComplete, err := backfiller.backfill(ctx, testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.False(t, isComplete, "Expected backfill to be marked as incomplete due to timeout")
	require.Len(t, sentMessages, 1, "Expected one message to be sent to SQS for continuation")

	var continuationJob models.BackfillJobPayload
	err = json.Unmarshal([]byte(*sentMessages[0].MessageBody), &continuationJob)
	require.NoError(t, err)
	assert.Equal(t, job.UserID, continuationJob.UserID)
	// The new job should cover the original window since the timeout happens before any chunk is processed.
	assert.WithinDuration(t, jobStartTime, *continuationJob.StartTime, time.Second)
	assert.WithinDuration(t, jobEndTime, *continuationJob.EndTime, time.Second)
}

func TestBackfill_ContinuationOnThrottle(t *testing.T) {
	// -- Arrange --
	var sentMessages []*sqs.SendMessageInput
	testUser := models.User{Model: gorm.Model{ID: 1}, ServiceID: 1}
	jobStartTime := time.Now().Add(-48 * time.Hour) // 2 days ago
	jobEndTime := time.Now()
	job := models.BackfillJobPayload{
		UserID:    testUser.ID,
		Provider:  "gmail",
		StartTime: &jobStartTime,
		EndTime:   &jobEndTime,
	}
	mockBackfillSqs := &mockSqsClient{
		SendMessageFunc: func(
			_ context.Context,
			params *sqs.SendMessageInput,
			_ ...func(*sqs.Options),
		) (*sqs.SendMessageOutput, error) {
			sentMessages = append(sentMessages, params)
			return nil, nil
		},
	}
	mockGmailSvc := &mem.Service{Messages: map[string]*gmail.Message{
		"msg1": {Id: "msg1"},
	}}
	// Throttler will be engaged immediately
	mockThrottler := &mockThrottler{isThrottled: true}

	backfiller := &GmailBackfiller{
		processorSqs: &mockSqsClient{},
		backfillSqs:  mockBackfillSqs,
		throttler:    mockThrottler,
		newGmailClient: func(
			_ context.Context, _, _ string, _ *models.User,
			_ ...oauth.Option,
		) (gmailclient.Client, error) {
			return mockGmailSvc, nil
		},
		getService: func(_ context.Context, _ uint) (models.Service, error) {
			return models.Service{Model: gorm.Model{ID: 1}}, nil
		},
		getEmail: func(_ context.Context, _ string) (*models.Email, error) {
			return &models.Email{}, nil
		},
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
	}
	backfiller.syncWindow = backfiller.syncMessagesInWindow

	// -- Act --
	isComplete, err := backfiller.backfill(context.Background(), testUser, job)

	// -- Assert --
	require.NoError(t, err)
	assert.False(t, isComplete, "Expected backfill to be marked as incomplete due to throttling")
	require.Len(t, sentMessages, 1, "Expected one message to be sent to SQS for continuation")

	// The throttler should be checked, and only ListMessages should be called.
	require.Len(t, mockGmailSvc.Calls, 1)
	assert.Contains(t, mockGmailSvc.Calls[0], "ListMessagesBetweenDates")

	var continuationJob models.BackfillJobPayload
	err = json.Unmarshal([]byte(*sentMessages[0].MessageBody), &continuationJob)
	require.NoError(t, err)
	assert.Equal(t, job.UserID, continuationJob.UserID)
	// The new job should cover the window of the chunk that was throttled.
	assert.WithinDuration(t, jobStartTime, *continuationJob.StartTime, time.Second)
	assert.WithinDuration(t, jobEndTime, *continuationJob.EndTime, time.Second)
}
