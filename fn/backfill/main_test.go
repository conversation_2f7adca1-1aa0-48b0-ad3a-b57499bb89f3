package main

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

// mockBackfiller implements the backfiller interface for tests.
type mockBackfiller struct {
	backfillFunc func(ctx context.Context, user models.User, job models.BackfillJobPayload) (bool, error)
}

func (m *mockBackfiller) backfill(ctx context.Context, user models.User, job models.BackfillJobPayload) (bool, error) {
	if m.backfillFunc != nil {
		return m.backfillFunc(ctx, user, job)
	}
	return false, nil
}

func TestProcessMessage_Success(t *testing.T) {
	// -- Arrange --
	var backfillCalled bool
	var updateLockCalls int

	testUser := models.User{Model: gorm.Model{ID: 1}}

	// Create a mock backfiller for the gmail provider.
	mockBf := &mockBackfiller{
		backfillFunc: func(_ context.Context, user models.User, _ models.BackfillJobPayload) (bool, error) {
			backfillCalled = true
			require.Equal(t, testUser.ID, user.ID)
			return true, nil // Indicate backfill is complete
		},
	}

	// Instantiate the handler with all its dependencies mocked.
	throttler := &mockThrottler{isThrottled: false}
	handler := &LambdaHandler{
		throttler: throttler,
		getUserByID: func(_ context.Context, id uint) (models.User, error) {
			require.Equal(t, testUser.ID, id)
			return testUser, nil
		},
		updateBackfillLock: func(_ context.Context, user *models.User, nt models.NullTime) error {
			updateLockCalls++
			require.Equal(t, testUser.ID, user.ID)
			if updateLockCalls == 1 {
				// First call should be to acquire the lock
				assert.True(t, nt.Valid, "Expected backfill lock time to be set")
			} else {
				// Second call should be to clear the lock
				assert.False(t, nt.Valid, "Expected backfill lock time to be cleared")
			}
			return nil
		},
		backfillers: map[string]backfiller{
			string(models.GmailEmailProvider): mockBf,
		},
	}

	// Create the SQS event payload
	job := models.BackfillJobPayload{
		UserID:   testUser.ID,
		Provider: string(models.GmailEmailProvider),
	}
	jobBytes, err := json.Marshal(job)
	require.NoError(t, err)
	message := events.SQSMessage{Body: string(jobBytes)}

	// -- Act --
	err = handler.processMessage(context.Background(), message)

	// -- Assert --
	require.NoError(t, err)
	assert.True(t, backfillCalled, "backfill should have been called")
	assert.Equal(t, 2, updateLockCalls, "updateBackfillLock should have been called twice (acquire and release)")
}

func TestProcessMessage_Throttled(t *testing.T) {
	// -- Arrange --
	var backfillCalled, clearLockCalled bool

	handler := &LambdaHandler{
		throttler: &mockThrottler{isThrottled: true},
		// These dependencies should not be called when throttled.
		getUserByID: func(_ context.Context, _ uint) (models.User, error) {
			t.Fatal("getUserByID should not be called when throttled")
			return models.User{}, nil
		},
		backfillers: map[string]backfiller{
			string(models.GmailEmailProvider): &mockBackfiller{
				backfillFunc: func(_ context.Context, _ models.User, _ models.BackfillJobPayload) (bool, error) {
					backfillCalled = true
					return false, nil
				},
			},
		},
		updateBackfillLock: func(_ context.Context, _ *models.User, _ models.NullTime) error {
			clearLockCalled = true
			return nil
		},
	}

	// Create a dummy SQS message
	job := models.BackfillJobPayload{UserID: 1, Provider: string(models.GmailEmailProvider)}
	jobBytes, err := json.Marshal(job)
	require.NoError(t, err)
	message := events.SQSMessage{Body: string(jobBytes)}

	// -- Act --
	err = handler.processMessage(context.Background(), message)

	// -- Assert --
	require.Error(t, err, "processMessage should return an error when throttled")
	assert.Contains(t, err.Error(), "system is throttled")
	assert.False(t, backfillCalled, "backfill should not have been called")
	assert.False(t, clearLockCalled, "updateBackfillLock should not have been called")
}
