# Backfill Lambda Worker

## Overview

This Lambda function is responsible for performing historical email backfills for users. When a new user connects their email account (Gmail or Outlook), a job is placed onto an SQS queue to trigger this worker. The worker then fetches the user's historical emails for a specified time period, processes them, and enqueues them for further processing by downstream services.

The primary goal is to populate a user's account with their recent email history, ensuring a seamless experience from the moment they sign up.

## Architecture

The service is designed with a modern, testable architecture centered around explicit dependency injection.

-   **Trigger**: The Lambda is triggered by messages from the `BackfillSQSQueue`. Each message contains a JSON payload (`BackfillJobPayload`) specifying the `UserID` and the `Provider` (e.g., "gmail", "outlook") to backfill.

-   **Composition Root**: The `main.go` file acts as the application's **composition root**. It is responsible for instantiating all concrete dependencies (AWS clients for SQS, CloudWatch, S3; database connections; Redis client) and injecting them into the core application logic. This pattern decouples the business logic from concrete implementations, making the service highly testable and modular.

-   **Components**: The logic is broken down into several key components:
    -   `LambdaHandler`: The main entry point for the Lambda, which holds all top-level dependencies.
    -   `GmailBackfiller` & `OutlookBackfiller`: Provider-specific implementations of the `backfiller` interface. Each component encapsulates the logic required to interact with its respective email provider's API.
    -   `SqsThrottler`: A dedicated component responsible for system-wide throttling to prevent overloading downstream resources.

### Throttling Mechanism

To ensure the backfill process does not overwhelm the database or other parts of the system, a throttling mechanism is in place.

-   The `SqsThrottler` checks two key CloudWatch metrics before processing a job:
    1.  `AWS/RDS` `DatabaseConnections`: To ensure the database is not approaching its connection limit.
    2.  `AWS/Lambda` `ConcurrentExecutions`: To monitor the load on the downstream processor Lambda.
-   **Redis Caching**: If the throttler detects that the system is exceeding its thresholds, it sets a key (`email:backfill:throttled`) in Redis with a 60-second TTL. On subsequent invocations, the throttler checks for this key first, avoiding repeated, unnecessary calls to the CloudWatch API while the system is under load.
-   **Retry**: If a job arrives while the system is throttled, it is not processed, and an error is returned to the Lambda runtime. This causes the SQS message to be returned to the queue, where it will be automatically retried after its visibility timeout expires.

## Provider-Specific Implementations

### GmailBackfiller

The Gmail backfiller is designed to handle potentially very long time windows (e.g., a full year) without running into the Lambda execution timeout limit.

-   **Chunking**: It breaks the total backfill window into smaller, 24-hour chunks.
-   **Continuation Jobs**: It iteratively processes each chunk. Before starting a new chunk, it checks the remaining time on the Lambda's context deadline. If the time is low, it enqueues a new "continuation job" onto the backfill SQS queue for the remainder of the time window and exits gracefully. This allows the backfill to seamlessly pick up where it left off in a new Lambda invocation.

### OutlookBackfiller

The Outlook backfiller is simpler as it's designed to handle a smaller, fixed time window in a single operation. It uses the efficient `ListMessagesBetweenDates` method of the Microsoft Graph API to fetch only the messages it needs, minimizing data transfer and processing time.

## Dependencies

-   **SQS**: Used as the trigger and for enqueuing continuation jobs.
-   **S3**: Used to archive the full, raw email payloads and any attachments.
-   **CloudWatch**: The source for throttling metrics.
-   **Redis**: Used for caching the throttled state.
-   **RDS (PostgreSQL)**: The primary application database for user and service data.

## Configuration

Configuration is managed via environment variables, sourced from a `.env` file for local development and from AWS Secrets Manager in deployed environments (`staging`, `prod`). Key variables include AWS service endpoints, database credentials, Redis URL, and provider API secrets. See `env/env.go` for a complete list.

## Local Development Setup

This service can be run locally for development and testing without connecting to live AWS services. This is achieved by running a local AWS stack using Docker and LocalStack.

### Prerequisites

-   [Docker](https://www.docker.com/get-started)
-   [AWS CLI](https://aws.amazon.com/cli/)

### 1. Start the Local AWS Stack

A `docker-compose.yml` file is provided in the root of the backfill folder to easily start a LocalStack container. This container will simulate SQS, S3, and CloudWatch services on your local machine.

From the root of the backfill folder, run:

```bash
docker-compose up -d
```

This will start the LocalStack and Redis container in the background.

### 2. Create the SQS Queues

The backfill worker listens for jobs on one SQS queue and sends its results to another. You need to create both of these queues in your local stack.

**Backfill Queue (Input)**

Run the following command to create the queue for incoming backfill jobs:

```bash
aws --endpoint-url=http://localhost:4566 sqs create-queue --queue-name backfill-jobs-local
```

This will return a `QueueUrl`. Copy it for the next step.

**Processor Queue (Output)**

Run the following command to create the queue where the backfill worker will send processed emails:

```bash
aws --endpoint-url=http://localhost:4566 sqs create-queue --queue-name processor-local
```

Copy the `QueueUrl` for this queue as well.

### 3. Configure and Run the Service

The service needs to know the URLs of both queues. Set these using environment variables. Make sure your `.env` file is also correctly configured with database and other necessary credentials.

```bash
# Replace with the QueueUrl for backfill-jobs-local
export BACKFILL_SQS_QUEUE_URL="http://localhost:4566/000000000000/backfill-jobs-local"

# Replace with the QueueUrl for processor-local
export PROCESSOR_SQS_QUEUE_URL="http://localhost:4566/000000000000/processor-local"
```

# Run the backfill service

```bash
go run ./fn/backfill/...
```

OR

```bash
dev -- air
```

The service will now start and begin long-polling the local `backfill-jobs-local` queue for messages.

### 4. Sending a Test Message and Verifying Output

To trigger the worker, you can send a message to the local backfill queue using the AWS CLI.

First, set the queue URL environment variables for convenience:

```bash
# Replace with your backfill queue URL
export BACKFILL_QUEUE_URL="http://localhost:4566/000000000000/backfill-jobs-local"

# Replace with your processor queue URL
export PROCESSOR_QUEUE_URL="http://localhost:4566/000000000000/processor-local"
```

Now you can run a complete test.

**A Note on Backfill Window**: You can control the time window of the backfill by adding an optional `startTime` field to the message body. The format should be a string in RFC3339 format (e.g., `2023-01-01T00:00:00Z`). If `startTime` is omitted, the backfill will default to the duration set by the `DEFAULT_BACKFILL_HOURS` environment variable (24 hours by default).

#### Testing Gmail

1.  **Send a message** to backfill for a user with `provider: "gmail"`. This example starts the backfill from a specific time (3 days ago).

    First, generate a timestamp string. The command differs slightly between macOS/BSD and Linux:

    ```bash
    START_TIME=$(date -v-3d -u +"%Y-%m-%dT%H:%M:%SZ")
    ```

    Then, send the message using this timestamp:
    ```bash
    aws --endpoint-url=http://localhost:4566 sqs send-message \
      --queue-url "$BACKFILL_QUEUE_URL" \
      --message-body "{\"userId\": 1, \"provider\": \"gmail\", \"startTime\": \"$START_TIME\"}"
    ```

2.  **Verify the output** by checking for messages in the processor queue. You should see `IngestedEmail` messages appear.

#### Testing Outlook

1.  **Send a message** to backfill for a user with `provider: "outlook"`. This example uses the default time window.

    ```bash
    aws --endpoint-url=http://localhost:4566 sqs send-message \
      --queue-url "$BACKFILL_QUEUE_URL" \
      --message-body "{\"userId\": 1, \"provider\": \"outlook\", \"startTime\": \"$START_TIME\"}'
    ```

You should see log output from the running service indicating that it has received and is processing the message, and you should see the resulting messages in the processor queue.
