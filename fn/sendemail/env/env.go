package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars Variables

type Variables struct {
	rds.EnvConfig

	// Deployment stage: "dev", "staging" or "prod"
	AppEnv string `envconfig:"APP_ENV" required:"true"`

	RedisURL string `envconfig:"REDIS_URL"`

	// Associated with the Drumkit Google/Microsoft apps
	GoogleClientID    string `envconfig:"GOOGLE_CLIENT_ID" required:"true"`
	MicrosoftClientID string `envconfig:"MICROSOFT_CLIENT_ID" required:"true"`

	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	// For prod only
	SendEmailSecretARN string `envconfig:"SEND_EMAIL_SECRET_ARN"`

	// In dev, these are set directly.
	// In prod, these are loaded from EmailSecretARN at startup.
	GoogleClientSecret    string `envconfig:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `envconfig:"MICROSOFT_CLIENT_SECRET"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey                string `json:"AES_KEY"`
	GoogleClientSecret    string `json:"GOOGLE_CLIENT_SECRET"`
	MicrosoftClientSecret string `json:"MICROSOFT_CLIENT_SECRET"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.Warn(ctx, "no .env file found", zap.Error(err))
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.RedisURL == "" {
			log.WarnNoSentry(ctx, "missing Redis URL")
		}

		if Vars.GoogleClientSecret == "" {
			log.WarnNoSentry(ctx, "missing GOOGLE_CLIENT_SECRET")
		}

		if Vars.MicrosoftClientSecret == "" {
			log.WarnNoSentry(ctx, "missing MICROSOFT_CLIENT_SECRET")
		}

	case "prod", "staging":
		if Vars.AxiomToken == "" {
			log.Warn(ctx, "missing Axiom Token")
		}

		if Vars.AxiomLogDataset == "" {
			log.Warn(ctx, "missing Axiom Log Dataset")
		}

		if Vars.AxiomTraceDataset == "" {
			log.Warn(ctx, "missing Axiom Trace Dataset")
		}

		if Vars.AxiomOrgID == "" {
			log.Warn(ctx, "missing Axiom Org ID")
		}

		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.SendEmailSecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.GoogleClientSecret == "" || secret.MicrosoftClientSecret == "" {

			return fmt.Errorf("%s is missing some fields", Vars.SendEmailSecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.GoogleClientSecret = secret.GoogleClientSecret
		Vars.MicrosoftClientSecret = secret.MicrosoftClientSecret
	}

	return nil
}
