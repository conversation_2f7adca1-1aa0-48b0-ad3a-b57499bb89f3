package main

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/log"
)

type (
	// Excludes the SQSEvent abstraction for easier local testing
	LocalPayload struct {
		*sqsclient.SendEmailSQSEventBody
		*LocalStepFunctionEvent
	}

	LocalStepFunctionEvent struct {
		*StepFunctionEvent
		TriggerTime string `json:"triggerTime"`
	}
)

func runLocalServer(shutdownHandler *graceful.ShutdownHandler) <-chan error {
	errChan := make(chan error, 1)
	app := fiber.New()
	shutdownHandler.AddCleanupFunc(func(_ context.Context) error {
		return app.Shutdown()
	})

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/email", fiberHandler)

	go func() {
		if err := app.Listen(devPort); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Error(context.Background(), "fiber server failed", zap.Error(err))
			errChan <- err
		}
	}()
	return errChan
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	var body LocalPayload
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	log.Info(ctx, "sending event to beacon-send-email handler", zap.Any("event", body))

	if body.SendEmailSQSEventBody != nil {
		return handleSQSEvent(ctx, body.SendEmailSQSEventBody)
	}

	if body.LocalStepFunctionEvent != nil {
		return handleStepFunctionEvent(ctx, body.LocalStepFunctionEvent, c)
	}

	return c.Status(http.StatusBadRequest).SendString("invalid request")
}

// Helper to handle StepFunction event logic
func handleStepFunctionEvent(ctx context.Context, stepFn *LocalStepFunctionEvent, c *fiber.Ctx) error {
	var path string
	if stepFn.EmailProvider == "gmail" {
		path = "/email/gmail"
	}
	if stepFn.EmailProvider == "outlook" {
		path = "/email/outlook"
	}

	payload, err := json.Marshal(stepFn)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	event := events.LambdaFunctionURLRequest{
		Body:    string(payload),
		RawPath: path,
	}

	if stepFn.TriggerTime != "" {
		triggerTime, err := time.Parse(time.RFC3339, stepFn.TriggerTime)
		if err != nil {
			return c.Status(http.StatusBadRequest).SendString("invalid trigger time format")
		}

		now := time.Now()
		if triggerTime.After(now) {
			duration := triggerTime.Sub(now)
			log.Info(ctx,
				"waiting until trigger time",
				zap.Time("triggerTime", triggerTime),
				zap.Duration("duration", duration),
			)
			time.Sleep(duration)
		} else {
			log.Info(ctx, "trigger time is in the past, executing immediately")
		}
	} else {
		log.Info(ctx, "trigger time not provided, executing immediately")
	}

	result, err := stepFunctionHandler(ctx, event)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(result.StatusCode).SendString(result.Body)
}
