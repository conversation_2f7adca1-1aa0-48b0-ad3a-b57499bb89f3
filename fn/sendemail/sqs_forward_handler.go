package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"go.uber.org/zap"
	"google.golang.org/api/googleapi"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/sendemail/env"
)

// Dependency injection for testability
var (
	dbFuncGetUserByID          = rds.GetUserByID
	gmailClientConstructor     = gmailclient.New[*models.User]
	batchCreateGeneratedEmails = genEmailDB.BatchCreateGeneratedEmails
	skipDuplicateForward       = emails.SkipDuplicateForward
	skipSelfForward            = emails.SkipSelfForward
)

func sqsHandler(ctx context.Context, event *events.SQSEvent) error {
	sanitizedRecords := sqsclient.SanitizeSQSRecords(event)
	log.Info(ctx, "received event", zap.Any("event", map[string]any{"Records": sanitizedRecords}))

	start := time.Now()

	for _, record := range event.Records {
		var payload sqsclient.SendEmailSQSEventBody
		if err := json.Unmarshal([]byte(record.Body), &payload); err != nil {
			log.Error(ctx, "error unmarshaling json body", zap.Error(err))
			continue
		}

		ctx = sqsclient.CaptureSQSMetadata(ctx, start, record, nil)

		return handleSQSEvent(ctx, &payload)
	}

	return nil
}

func handleSQSEvent(ctx context.Context, payload *sqsclient.SendEmailSQSEventBody) (err error) {
	email := payload.Email
	rule := payload.Rule
	wasThreadForwardedBefore := payload.WasThreadForwardedBefore

	ctx, metaSpan := otel.StartSpan(ctx, "handleSQSEvent", nil)
	defer func() { metaSpan.End(err) }()

	skip, err := skipDuplicateForward(ctx, email.ExternalID, rule.ID)
	if err != nil {
		log.Error(ctx, "error checking if message was previously forwarded, could lead to duplicates", zap.Error(err))
	} else if skip {
		log.Info(ctx, "message was previously forwarded, skipping")
		return nil
	}

	skip, err = skipSelfForward(ctx, email.UserID, email.RFCMessageID, rule.ID)
	if err != nil {
		log.Error(ctx, "error checking if message was self-forwarded, could lead to duplicates", zap.Error(err))
	} else if skip {
		log.Info(ctx, "message was self-forwarded, skipping")
		return nil
	}

	log.Info(ctx, "forwarding email based on rule")

	// Prevent duplicate forwarding by locking message in Redis
	redisErr := redis.SetKeyWithRetries(
		ctx,
		emails.GetRedisKeyForwardedMessage(rule.ID, email.ExternalID),
		"true",
		emails.ForwardedMessageTTL,
	)
	if redisErr != nil {
		log.Error(ctx, "error setting redis for forwarded message, could lead to duplicates", zap.Error(redisErr))
	}

	userVal, err := dbFuncGetUserByID(ctx, email.UserID)
	if err != nil {
		return fmt.Errorf("error getting user: %w", err)
	}
	user := &userVal

	if user.EmailProvider != models.GmailEmailProvider {
		return errors.New("forwarding not yet supported for non-Gmail email providers")
	}

	client, err := gmailClientConstructor(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, user)
	if err != nil {
		return fmt.Errorf("error creating Gmail client: %w", err)
	}

	genEmail := &models.GeneratedEmail{
		ServiceID:                  user.ServiceID,
		UserID:                     user.ID,
		Recipients:                 rule.Recipients,
		CC:                         rule.CCRecipients,
		Sender:                     user.EmailAddress,
		TriggeredByRuleID:          &rule.ID,
		TriggeredByUserID:          user.ID,
		IsForward:                  true,
		ForwardedMessageID:         &email.ID,
		ForwardedMessageExternalID: email.ExternalID,
		ForwardedMessageThreadID:   email.ThreadID,
	}

	var forwardErr error
	var allErrs error
	backoffs := []time.Duration{time.Second, 3 * time.Second, 10 * time.Second}
	for i := range 3 {
		_, forwardErr = client.ForwardMessage(
			ctx,
			email.ExternalID,
			genEmail,
			rule.UseSenderSignature,
			!wasThreadForwardedBefore,
		)
		if forwardErr == nil {
			break
		}

		var httpErr *googleapi.Error
		if errors.As(forwardErr, &httpErr) && httpErr.Code >= 500 {
			allErrs = errors.Join(allErrs, forwardErr)
			if i < 2 {
				time.Sleep(backoffs[i])
			}
			continue
		}
		// Not a retryable error, break
		allErrs = errors.Join(allErrs, forwardErr)
		break
	}

	if forwardErr != nil {
		// Remove lock so SQS can retry
		redisErr := redis.DeleteKey(ctx, emails.GetRedisKeyForwardedMessage(rule.ID, email.ExternalID))
		if redisErr != nil {
			log.Warn(ctx, "error deleting redis for failed forward, will prevent future retries", zap.Error(redisErr))
		}

		return fmt.Errorf("error forwarding message after retries: %w", allErrs)
	}

	log.Info(
		ctx,
		"email successfully forwarded based on rule",
		zap.Strings("recipients", rule.Recipients),
		zap.Strings("ccRecipients", rule.CCRecipients),
	)

	err = batchCreateGeneratedEmails(ctx, []*models.GeneratedEmail{genEmail})
	if err != nil {
		log.Error(ctx, "error inserting forward email into DB", zap.Error(err))
	}

	// Cache the forwarded thread ID in Redis for rule.ForwardSubsequentEmailsInThread
	err = redis.SetKey(
		ctx,
		emails.GetRedisKeyForwardedThread(rule.ID, email.ThreadID),
		email.ExternalID,
		emails.ForwardedMessageTTL,
	)
	if err != nil {
		log.Warn(ctx, "error caching forwarded thread ID in redis", zap.Error(err))
	}

	// Cache generated RFC for `skipSelfForward` logic. Prevents infinite forwards in case user
	// is a recipient of own forward
	err = redis.SetKey(
		ctx,
		emails.GetRedisKeyForwardedRFC(rule.ID, genEmail.RFCMessageID),
		genEmail.ExternalID,
		emails.ForwardedMessageTTL,
	)
	if err != nil {
		log.Warn(ctx, "error caching forwarded RFC in redis", zap.Error(err))
	}

	// Archive the original message
	if (rule.ArchiveOriginalForwardedEmail && !wasThreadForwardedBefore) ||
		(wasThreadForwardedBefore && rule.ArchiveSubsequentEmailsInThread) {
		err = client.ArchiveMessage(ctx, email.ExternalID)
		if err != nil {
			log.Error(ctx, "error archiving original message", zap.Error(err))
		} else {
			log.Info(ctx, "original message archived")
		}
	}

	if len(rule.AddLabels) > 0 {
		err = client.AddLabels(ctx, genEmail.ExternalID, rule.AddLabels)
		if err != nil {
			log.Error(ctx, "error adding labels to forwarded message", zap.Error(err))
		} else {
			log.Info(ctx, "labels added to forwarded message", zap.Strings("labels", rule.AddLabels))
		}
	}

	return nil
}
