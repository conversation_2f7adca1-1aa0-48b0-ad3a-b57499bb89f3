package main

import (
	"context"
	"encoding/json"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/sendemail/env"
)

const (
	devPort        = ":5002"      // Port of the local fiber service in dev mode.
	serviceName    = "send-email" // Name of the service for tracing.
	serviceVersion = "0.0.1"      // Version of the service.
)

type (
	Trigger struct {
		// Supports synchronous email forwarding via Processor -> SQS (can be extended for other use cases)
		*events.SQSEvent
		// Supports scheduled email sending via Step Function
		*StepFunctionEvent
	}

	StepFunctionEvent struct {
		GenEmailID    uint   `json:"generatedEmailID" validate:"required"`
		EmailAddress  string `json:"senderEmailAddress" validate:"required"`
		EmailProvider string `json:"emailProvider" validate:"oneof=gmail outlook"`
	}
)

var (
	getUserByEmail = userDB.GetByEmail
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-sendemail")

	log.Debug(ctx, "IN DEBUG MODE")

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)
	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-sendemail"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.AppEnv == "dev" {
		errChan := runLocalServer(shutdownHandler)
		for {
			select {
			case err := <-errChan:
				log.Error(ctx, "fiber server failed to start", zap.Error(err))
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

func handlerWithLogging(ctx context.Context, event Trigger) (result any, err error) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		ctx = log.NewFromEnv(ctx)

		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		// Log Lambda result and report errors to Sentry
		defer func() {
			if err != nil {
				log.Error(ctx, "sendemail lambda returning error", zap.Error(err))
			}
		}()

		switch {
		case event.SQSEvent != nil:
			log.Info(ctx, "received AWS SQS Event", zap.Any("request", event.SQSEvent))
			err = sqsHandler(ctx, event.SQSEvent)

		case event.StepFunctionEvent != nil:
			request := event.StepFunctionEvent
			log.Info(ctx, "received AWS Step Function request", zap.Any("request", request))

			var path string

			if request.EmailProvider == "gmail" {
				path = "/email/gmail"
			}

			if request.EmailProvider == "outlook" {
				path = "/email/outlook"
			}

			payload, err := json.Marshal(request)
			if err != nil {
				log.Error(ctx, "error marshaling email payload to JSON", zap.Error(err))
				return
			}

			event := events.LambdaFunctionURLRequest{
				Body:    string(payload),
				RawPath: path,
			}

			if result, err = stepFunctionHandler(ctx, event); err != nil {
				return
			}

			log.Info(ctx, "returning AWS Step Function Response", zap.Any("response", result))
		}
	})

	return result, err
}
