package main

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/processor/routes/email"
)

const (
	devPort                 = ":5005"
	devPathForIngestion     = "/"
	pathForOnPrem           = "/onprem/email"
	pathForReprocessContent = "/reprocess-content/:id"
)

type Request struct {
	Email models.IngestedEmail
}

func runLocalServer(ctx context.Context, shutdownHandler *graceful.ShutdownHandler) <-chan error {
	errChan := make(chan error, 1)
	app := fiber.New()

	// Register the server's shutdown with the graceful handler
	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "shutting down local dev server")
		return app.Shutdown()
	})

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	// In prod, this is an actual API Gateway endpoint.
	app.Post(pathForOnPrem, email.ProcessOnPremEmail)
	app.Post(pathForReprocessContent, email.ReprocessContent)

	// In prod, this is triggered by SQS. In dev, behavior is mimicked by the local server.
	app.Post(devPathForIngestion, func(c *fiber.Ctx) error {
		ctx := c.UserContext()
		var payload Request
		if err := json.Unmarshal(c.Body(), &payload.Email); err != nil {
			panic(err)
		}

		if err := handleLocalEvent(ctx, payload.Email); err != nil {
			log.ErrorNoSentry(ctx, "error handling local event", zap.Error(err))
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}

		return c.Status(http.StatusOK).SendString("Processed email successfully!")
	})

	// Start the server in a goroutine so it doesn't block the main thread.
	// The main thread needs to stay alive to wait for the shutdown signal.
	go func(ctx context.Context) {
		log.Info(ctx, "starting local dev server", zap.String("port", devPort))
		if err := app.Listen(devPort); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Error(ctx, "local dev server failed", zap.Error(err))
			errChan <- err
		}
	}(ctx)
	return errChan
}

func handleLocalEvent(ctx context.Context, msg models.IngestedEmail) error {
	log.Info(ctx, "dev: received email", zap.Any("msg", msg))

	return startProcessing(ctx, msg)
}
