package email

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	utilsentry "github.com/drumkitai/drumkit/common/sentry"
)

type ProcessOnPremEmailBody struct {
	Email *models.IngestedEmail
}

// ProcessOnPremEmail handles incoming requests to save on-premise emails into the Drumkit database and initiates
// relevant LLM workflows.
//
// This function assumes that the email is already labeled, so it proceeds with the analysis by calling the `Analyze`
// function. This is different from scenarios where the Processor is triggered by internal
// SQS messages, which perform both classification and analysis of emails end-to-end.
func ProcessOnPremEmail(c *fiber.Ctx) error {
	var body ProcessOnPremEmailBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),

		zap.String("emailAddress", body.Email.Account),
		zap.Uint("externalUserID", body.Email.ExternalUserID),
		zap.Uint("externalEmailID", body.Email.ExternalEmailID),
		zap.String("msgID", body.Email.ExternalID),
		zap.String("threadID", body.Email.ThreadID),
		zap.String("rfcId", body.Email.RFCMessageID),
		zap.String("subject", body.Email.Subject),
		zap.String("sender", body.Email.Sender),
	)

	log.Info(ctx, "received onprem email from customer", zap.Any("label", body.Email.Label))

	user, err := userDB.GetByOnPremID(ctx, body.Email.ExternalUserID, body.Email.Account)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error(ctx, "no user associated with this email", zap.Error(err))
			return c.SendStatus(http.StatusNotFound)
		}

		log.Error(ctx, "error getting user", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}
	utilsentry.SetUser(ctx, &user)

	body.Email.UserID = user.ID
	body.Email.ServiceID = user.ServiceID

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		err = fmt.Errorf("rds.GetServiceByID error: %w", err)
		log.ErrorNoSentry(ctx, "db error", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	log.With(ctx, zap.Uint("serviceID", service.ID))

	if err := emails.AnalyzeOnPrem(ctx, body.Email, service); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	return nil
}
