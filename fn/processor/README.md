# Processor Service

The Processor service handles email processing for both Drumkit-hosted and on-premise hosted clients. For the former, it receives emails from SQS added by Gmail, Outlook, and Front ingestion lambdas. For the latter, an HTTP server is exposed to which on-premise processors send webhooks.

## E2E Onboarding Guide

https://www.notion.so/drumkitai/Drumkit-Getting-Started-ffbf8461e7ba4ef390da994390adac5e#ca9a07eb7d2f437f9c8ea79a7a2090c0

## Dependencies

- OpenAI
- [AWS Vault](https://www.notion.so/drumkitai/aws-vault-c144881ae88f4563982d43a857eb5b0e) -- enables application access to AWS resources like S3 locally
- [Cyclops](https://github.com/drumkitai/cyclops) -- enables attachment processing

## Setup

### Local Mode

When running locally (`APP_ENV=dev`):

- The service runs as an HTTP server on port 5005
- Queue configuration checks are skipped
- SQS events are simulated via HTTP endpoints

### Lambda Mode

When running in AWS Lambda:

- The service is triggered by SQS events
- On-premise webhooks are handled via API Gateway
- Uses Redis for email deduplication
- Queue configuration is checked periodically

## Get Started

The Processor service can run in two modes: Lambda (production/staging) or local server (development). Let's set up the local development environment.

1. Create a `.env` file in `fn/processor/`:

```shell
DB_USER=postgres
DB_HOST=localhost
DB_NAME=beacon_dev
DB_PASSWORD=postgres

DISABLE_RATE_LIMIT=true
OPENAI_API_KEY="---Generate a custom key on https://platform.openai.com/; sign in with Drumkit Google SSO---"
DEBUG=false
COPY_DUPLICATE_EMAILS=false # Set to false so you can test the same email multiple times
SKIP_DUPLICATE_SUGGESTIONS=false # Set to false so you can test the same email multiple times
CYCLOPS_URL=http://127.0.0.1:8000 # Required for attachment processing; code fallsback to GoFitz
REDIS_URL=redis://localhost:6379
DISABLE_RATE_LIMIT=true # For Aljex

# Optional - Observability (only needed if you're testing tracing/Sentry/Braintrust changes)
AXIOM_ORG_ID=axle-xqz9
AXIOM_TOKEN="---See 1Password---"
AXIOM_TRACE_DATASET=beacon-otel-dev
PYROSCOPE_PASSWORD="---See 1Password---"
PROFILING_ENABLED=false
SENTRY_DSN=https://<EMAIL>/4505756382658560
# AXIOM_LOG_DATASET="none"

```

2. Run the service locally:

```shell
AWS_SESSION_TOKEN_TTL=12h aws-vault exec {your-username} -- go run main.go
# or for hot reloads
AWS_SESSION_TOKEN_TTL=12h aws-vault exec {your-username} -- dev
```

The service will start on port 5005 and handle both:

- SQS events (simulated via HTTP endpoints in dev)
- On-premise webhooks

Notes:

- You may have aliased the AWS vault command with `dev` per our [Notion guide](https://www.notion.so/drumkitai/aws-vault-c144881ae88f4563982d43a857eb5b0e)
- Air hot-reloading can only watch files within this directory, but most of the logic is contained in `common/emails`

### Testing Email Processing

#### E2E Testing

Follow the instructions in [Notion](https://www.notion.so/drumkitai/Drumkit-Getting-Started-ffbf8461e7ba4ef390da994390adac5e#ca9a07eb7d2f437f9c8ea79a7a2090c0), [Gmail](../ingestion/gmail/README.md), [Outlook](../ingestion/outlook/README.md), or [Front](../ingestion/front/README.md) READMEs to test locally E2E.

#### Manual Testing

1. To simulate Drumkit-hosted clients, send a POST request to `http://localhost:5005/email` using a tool like Postman (you can get a sample email body from the `dev: received email` log):

```json
{
  "external_user_id": 0, // On-prem fields
  "external_email_id": 0, // On-prem fields
  "body": "Release 96100 PO# 18787",
  "body_type": "markdown",
  "label": "",
  "account": "<EMAIL>",
  "user_id": 38,
  "service_id": 34,
  "rfc_message_id": "<<EMAIL>>",
  "external_id": "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QABPwMx1wAA",
  "thread_id": "AAQkADRmNTg1NzRmLWJhOTAtNDcxZi05NDQ0LWIzYWVlZWZhMTRlNgAQAPqKHcHnZ0DdvCvP-zOmWIM=",
  "sent_at": "2025-04-29T03:27:24Z",
  "sender": "<EMAIL>",
  "recipients": "<EMAIL>,<EMAIL>",
  "subject": "FW: Release 96100 (53' dry van with swing doors) (Please ship next available truck) Thank you",
  "s3_url": "https://s3.console.aws.amazon.com/s3/object/dev-axle-beacon-ingestion?region=us-east-1&prefix=outlook/<EMAIL>/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QABPwMx1wAA.json"
  // Optional
  // "has_pdfs": true,
  // "attachment_s3_urls": [
  //     {
  //         "messageExternalId": "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QABPwMx1wAA",
  //         "externalId": "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QABPwMx1wAAARIAEADzNL4E8VPBQ7xQDtDmRL5h",
  //         "mimeType": "application/pdf",
  //         "isInline": false,
  //         "isInSenderSignature": false,
  //         "originalFileName": "Release.pdf",
  //         "transformedFileName": "Release.pdf",
  //         "s3URL": "https://s3.console.aws.amazon.com/s3/object/dev-axle-beacon-ingestion?region=us-east-1&prefix=outlook/<EMAIL>/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QABPwMx1wAA/Release.pdf"
  //     }
  // ]
}
```

2. For on-premise clients, send a POST request to `http://localhost:5005/onprem/email` with the same payload.

## Email Deduplication

The service uses Redis to manage email deduplication, ensuring that when an email is sent to multiple recipients (creating "sibling" emails), it is only processed once.

1.  **Check Redis**: When an email arrives, a key is generated from its RFC Message ID to check for a lock in Redis.
2.  **Process Primary**: If no lock exists, a lock is set with an `inFlight` status, and the email is processed. This is considered the "primary" email.
3.  **Defer Siblings**: If an `inFlight` lock is found, the current "sibling" email's data is pushed to a Redis list (keyed by `<rfc-id>:siblings`) for deferred processing. The processor then exits for this email.
4.  **Handle Completed**: If the lock indicates the email is already processed (e.g., a `done:<email_id>` value), the service copies the data from the original.
5.  **Process Deferred Siblings**: Once the primary email is successfully processed, the processor retrieves the list of deferred siblings from Redis. It then copies the results of the primary processing to each sibling and removes them from the list.

This LucidChart shows the flow of de-duplication logic
https://lucid.app/lucidchart/ad9cc2e0-7186-44d9-839f-d8205038bf6a/edit?invitationId=inv_3e14ee3d-845b-48c1-9841-15643e4adcdc&page=QhjlMVmiukNi#

### Queue Configuration

The service will warn you if your SQS queue configuration doesn't match the deduplication setting:

- When deduplication is enabled:
  - Visibility timeout should be ≤ 10 seconds
  - Max receive count should be ≥ 10
- When deduplication is disabled:
  - Visibility timeout should be ≥ 2 minutes

### Enabling Deduplication

This is the default configuration set by Parthenon. If you need to temporarily change them, then you can do so via Parthenon and/or AWS console. If permanently changing config, then set in Parthenon.

To ensure deduplication is enabled:

1. Set `COPY_DUPLICATE_EMAILS=true`
2. Ensure your SQS queue is configured with:
   - Visibility timeout ≤ 10 seconds
   - Max receive count ≥ 10 before sending to dead-letter queue (DLQ)
   - This ensures timely retries for proper deduplication

### Disabling Deduplication

In some cases, you may need to disable email deduplication (e.g., during incidents or when Redis is unavailable). To do this:

1. Set the environment variable `COPY_DUPLICATE_EMAILS=false`
2. This will:
   - Skip Redis lock checks
   - Process every email as if it's new
   - Allow duplicate processing

⚠️ **Important**: When disabling deduplication, ensure your SQS queue is configured with:

- Visibility timeout ≥ 2 minutes
- This prevents overwhelming the Processor with duplicate retries

### Unit Testing

Run the test suite:

```shell
go test ./...
```

Key test cases:

- Email deduplication logic
- Queue configuration validation
- On-premise webhook handling
- Error handling and retries
