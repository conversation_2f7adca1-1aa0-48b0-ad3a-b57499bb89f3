package main

import (
	"net/http"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/fn/processor/env"
	"github.com/drumkitai/drumkit/fn/processor/routes/email"
)

// NOTE: in prod, API Gateway will prefix the stage name ("/v1/") to each of these routes
func buildApp() *fiber.App {
	app := fiber.New()

	app.Use(middleware.Sentry())
	app.Use(middleware.Tracer())
	app.Use(middleware.Zap(middleware.WithAppEnv(env.Vars.AppEnv)))

	// Required to allow localhost and gmail to query the API
	// https://docs.aws.amazon.com/apigateway/latest/developerguide/how-to-cors.html#apigateway-enable-cors-proxy
	app.Use(middleware.CORS(middleware.WithCORSOrigins("*")))

	// FIXME: add middleware and validate request via drumkit access token

	app.Get("/health", func(c *fiber.Ctx) error {
		return c.SendStatus(http.StatusOK)
	})

	// Each route must use the token claims to authorize the request
	app.Post(pathForOnPrem, email.ProcessOnPremEmail)
	app.Post(pathForReprocessContent, email.ReprocessContent)

	return app
}
