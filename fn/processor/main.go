package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"runtime/pprof"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/grafana/pyroscope-go"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/memory"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	suggestionsDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
	truckListDB "github.com/drumkitai/drumkit/common/rds/truck"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

var (
	fiberPort             = "5005"
	serverReady           = make(chan struct{})
	errProcessingInFlight = errors.New("email already being processed, try again later")

	dbGetVectorRepositoryFunc  = rds.GetVectorRepository
	dbGetServiceFunc           = rds.GetServiceByID
	dbGetEmailByIDFunc         = emailDB.GetByIDPreloadWithLoadsAndVectors
	dbUpsertEmailFunc          = emailDB.UpsertEmailAndLoads
	dbGetLoadSuggestionsFunc   = suggestionsDB.GetSuggestedLoadChangesByEmailIDPreloadWithVectors
	dbCreateLoadSuggestionFunc = suggestionsDB.Create
	dbGetQuoteRequestsFunc     = quoteRequestDB.GetRequestByEmailIDWithVectors
	dbCreateQuoteRequestFunc   = quoteRequestDB.CreateQuoteRequest
	dbGetTruckListFunc         = truckListDB.GetTruckListByEmail
	dbCreateTruckListFunc      = truckListDB.CreateTruckList
	analyzeFunc                = emails.Analyze
	processForwardingRulesFunc = emails.ProcessForwardingRules
	sqsClient                  sqsclient.API
)

const (
	serviceName        = "processor-axiom-otel" // Name of the service for tracing.
	serviceVersion     = "0.0.1"                // Version of the service.
	redisLockExtension = 5 * time.Second
	copyAction         = "copy"
	processAction      = "process"
	deferAndExitAction = "defer"
	appTmpDir          = "/tmp/drumkit-processor"
)

type (
	// DeferredSiblingMessage represents the minimal data needed to process a deferred sibling email.
	// It's stored in Redis to avoid holding the entire (potentially large) IngestedEmail object in memory.
	DeferredSiblingMessage struct {
		UserID             uint                      `json:"user_id"`
		Account            string                    `json:"account"`
		ServiceID          uint                      `json:"service_id"`
		ExternalID         string                    `json:"external_id"`
		ThreadID           string                    `json:"thread_id"`
		RFCMessageID       string                    `json:"rfc_message_id"`
		ProcessingMetadata models.ProcessingMetadata `json:"processing_metadata"`
	}

	Event struct {
		*events.SQSEvent
		*events.APIGatewayProxyRequest
	}

	Response struct {
		events.SQSEventResponse        `json:",omitempty"`
		events.APIGatewayProxyResponse `json:",omitempty"`
	}
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	ctx := context.Background()

	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-processor")

	if envErr := env.Load(ctx); envErr != nil {
		panic(envErr)
	}

	memory.SetGoMemoryLimit(ctx)

	if env.Vars.ProfilingEnabled {
		_, err := pyroscope.Start(pyroscope.Config{
			ApplicationName: "processor-" + os.Getenv("APP_ENV"),
			// replace this with the address of pyroscope server
			ServerAddress:     "https://profiles-prod-001.grafana.net",
			BasicAuthUser:     "1212715",
			BasicAuthPassword: os.Getenv("PYROSCOPE_PASSWORD"),
			// you can disable logging by setting this to nil
			Logger: nil,

			// you can provide static tags via a map:
			Tags: map[string]string{"hostname": os.Getenv("HOSTNAME")},

			ProfileTypes: []pyroscope.ProfileType{
				// these profile types are enabled by default:
				pyroscope.ProfileCPU,
				pyroscope.ProfileAllocObjects,
				pyroscope.ProfileAllocSpace,
				pyroscope.ProfileInuseObjects,
				pyroscope.ProfileInuseSpace,

				// these profile types are optional:
				pyroscope.ProfileGoroutines,
				pyroscope.ProfileMutexCount,
				pyroscope.ProfileMutexDuration,
				pyroscope.ProfileBlockCount,
				pyroscope.ProfileBlockDuration,
			},
		})

		if err != nil {
			log.Error(ctx, "Error starting pyroscope", zap.Error(err))
		} else {
			log.Info(ctx, "Pyroscope profiler started")
		}
	}

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	// Setup temporary directory for Lambda environment during cold start
	if helpers.IsLambda() {
		if err := os.MkdirAll(appTmpDir, 0755); err != nil {
			log.Warn(ctx, "Failed to create app temp directory", zap.String("path", appTmpDir), zap.Error(err))
		} else {
			os.Setenv("TMPDIR", appTmpDir)
			log.Info(ctx, "Set TMPDIR for Lambda environment", zap.String("TMPDIR", appTmpDir))
		}
	}

	ctx = log.NewFromEnv(ctx)
	log.Debug(ctx, "IN DEBUG MODE")

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	ip, err := httputil.GetIPAddress()
	if err != nil {
		log.Warn(ctx, "error retrieving public IP address", zap.Error(err))
	} else {
		log.Info(ctx, "retrieved public IP address", zap.String("IPAddress", ip))
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-processor"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	sqsClient, err = sqsclient.New(env.Vars.AppEnv)
	if err != nil {
		panic(err)
	}

	// If dev, run local server which handles both on-premise webhooks and mimicks SQS trigger with HTTP endpoint
	if env.Vars.AppEnv == "dev" {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.WarnNoSentry(ctx, "Error running migrations", zap.Error(err))
		}

		errChan := runLocalServer(ctx, shutdownHandler)

		for {
			select {
			case err := <-errChan:
				log.Error(ctx, "fiber server failed to start", zap.Error(err))
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	app := buildApp()
	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "shutting down fiber server")
		return app.Shutdown()
	})
	go func() {
		close(serverReady)
		api.RunServer(ctx, app, api.WithPort(fiberPort))
	}()

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

// NOTE: Only APIGatewayProxyRequest events return results; SQS triggers fail-open
func handlerWithLogging(ctx context.Context, event Event) (result any, err error) {
	lambdafield := ""
	if lc, ok := lambdacontext.FromContext(ctx); ok {
		lambdafield = lc.AwsRequestID
	}

	sentry.WithHub(ctx, func(ctx context.Context) {
		ctx = log.NewFromEnv(ctx)

		// Clean up temporary files from previous Lambda invocations
		cleanupTmpDirectory(ctx)
		logMemoryStats(ctx, "lambda_start")

		hub := sentry.GetHubFromContext(ctx)

		// If there is an error, flush it to Sentry before the Lambda invocation is finished.
		defer hub.Flush(sentry.FlushTimeout)

		// Log Lambda result and report errors to Sentry
		defer func() {
			if err != nil {
				log.Error(ctx, "returning error", zap.Error(err))
			}
		}()
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		// Clean up temporary files and log final memory stats after processing
		defer func() {
			logMemoryStats(ctx, "lambda_end")
			cleanupTmpDirectory(ctx)
		}()

		switch {
		case event.SQSEvent != nil:
			log.Info(
				ctx,
				"Triggered Processor Lambda's SQS trigger",
				zap.Int("numRecords", len(event.Records)),
			)

			pprof.Do(ctx, pprof.Labels(lambdafield, "sqs"), func(c context.Context) {
				batchItemFailures, err := handleSQSEvent(c, *event.SQSEvent)
				if err != nil {
					log.Error(ctx, "failed to handle SQS event", zap.Error(err))
					return
				}

				if len(batchItemFailures) > 0 {
					result = events.SQSEventResponse{
						BatchItemFailures: batchItemFailures,
					}
				}
			})

		case event.APIGatewayProxyRequest != nil:
			log.Info(ctx, "Triggered Processor Lambda's API trigger")

			pprof.Do(ctx, pprof.Labels(lambdafield, "api"), func(c context.Context) {
				result, err = handleAPIEvent(c, event.APIGatewayProxyRequest)
			})

		default:
			log.Error(ctx, "Trigger type does not exist", zap.Any("event", event))
		}
	})

	return result, err
}

func handleSQSEvent(ctx context.Context, event events.SQSEvent) ([]events.SQSBatchItemFailure, error) {
	// Use BatchItemFailures to return duplicate messages to the queue without counting as a Lambda error
	// https://docs.aws.amazon.com/lambda/latest/dg/example_serverless_SQS_Lambda_batch_item_failures_section.html
	var batchItemFailures []events.SQSBatchItemFailure
	start := time.Now()

	// This is always just one record, but in case of a batch, it's a slice
	sanitizedRecords := sqsclient.SanitizeSQSRecords(&event)
	log.Info(ctx, "received event", zap.Any("event", map[string]any{"Records": sanitizedRecords}))

	for _, record := range event.Records {
		var msg models.IngestedEmail
		if err := json.Unmarshal([]byte(record.Body), &msg); err != nil {
			log.Error(ctx, "failed to json unmarshal SQS record", zap.Error(err), zap.Any("record", record))
			continue
		}

		ctx = captureSQSMetadata(ctx, start, record, &msg)

		if err := startProcessing(ctx, msg); err != nil {
			// If the error is not due to the email already being processed, return the error
			// This counts as a Lambda error and will be retried by SQS
			if !errors.Is(err, errProcessingInFlight) {
				return nil, err
			}

			log.Info(ctx, "message will be retried",
				zap.String("account", msg.Account),
				zap.String("externalId", msg.ExternalID),
				zap.String("threadId", msg.ThreadID),
				zap.String("rfcId", msg.RFCMessageID),
				zap.String("subject", msg.Subject),
			)

			// Add to batch failures for retry without counting as Lambda error (again, should just be 1 SQS record)
			batchItemFailures = append(batchItemFailures, events.SQSBatchItemFailure{ItemIdentifier: record.MessageId})
		}
	}

	if len(batchItemFailures) > 0 {
		log.Debug(ctx, "returning batch item failures", zap.Any("batchItemFailures", batchItemFailures))
	}

	return batchItemFailures, nil
}

func handleAPIEvent(
	ctx context.Context,
	event *events.APIGatewayProxyRequest,
) (*events.APIGatewayProxyResponse, error) {

	select {
	case <-serverReady:
		log.Info(ctx, "server ready to handle processor lambda api requests")
	case <-time.After(5 * time.Second):
		log.Warn(ctx, "server failed to start within 5s")
	case <-ctx.Done():
		log.Warn(ctx, "context canceled before server started", zap.Error(ctx.Err()))
	}

	client := &http.Client{Timeout: 500 * time.Millisecond}

	for i := 0; i < 10; i++ {
		req, err := http.NewRequestWithContext(
			ctx,
			http.MethodGet,
			fmt.Sprintf("http://localhost:%s/health", fiberPort),
			nil,
		)
		if err != nil {
			log.Warn(ctx, "failed to create health check request", zap.Error(err))
			break
		}

		resp, err := client.Do(req)
		if resp != nil {
			resp.Body.Close()
		}

		if err == nil && resp.StatusCode == http.StatusOK {
			break
		}

		if i == 9 {
			log.Warn(ctx, "server not responding after 5s")
		}

		time.Sleep(500 * time.Millisecond)
	}

	return api.GatewayHandler(ctx, event, api.WithPort(fiberPort))
}

func startProcessing(ctx context.Context, msg models.IngestedEmail) (err error) {
	ctx, span := otel.StartSpan(ctx, "startProcessing", nil)

	rfcIDRedisKey := emails.GetRFCIDRedisKey(msg.ServiceID, msg.RFCMessageID)

	// Create a done channel to signal completion to async redis locker
	done := make(chan struct{})
	newEmailID := new(uint)

	defer func() {
		close(done)
		defer span.End(err)

		if err != nil {
			log.Error(ctx, "processing failed", zap.Error(err))
		}
	}()

	ctx = log.With(ctx,
		zap.String("account", msg.Account),
		zap.String("externalId", msg.ExternalID),
		zap.String("threadId", msg.ThreadID),
		zap.String("rfcId", msg.RFCMessageID),
		zap.String("subject", msg.Subject),
		zap.String("sender", msg.Sender),
	)

	log.Info(ctx, "received email")

	serviceID, err := resolveServiceID(ctx, msg)
	if err != nil {
		return err
	}
	msg.ServiceID = serviceID
	log.With(ctx, zap.Uint("serviceID", serviceID))

	// Filter spam emails early to prevent any downstream processing
	spamDetector := emails.NewSpamDetector()
	if isSpam, reason := spamDetector.IsSpam(msg); isSpam {
		log.Info(
			ctx,
			"email flagged as spam and skipped from processing",
			zap.String("spam_reason", string(reason)),
		)
		return nil
	}

	service, err := dbGetServiceFunc(ctx, serviceID)
	if err != nil {
		log.ErrorNoSentry(ctx, "db error", zap.Error(err))
		return fmt.Errorf("dbGetServiceFunc: %w", err)
	}

	existingEmail, action := determineAction(ctx, msg, rfcIDRedisKey, &service)
	if action == deferAndExitAction {
		log.Info(ctx, "deferring processing for sibling email")
		return nil
	}

	switch action {
	case processAction:
		// Start goroutine to extend TTL periodically while processing
		go extendRedisLock(ctx, msg, rfcIDRedisKey, done)

		*newEmailID, err = analyzeFunc(ctx, &msg, service, sqsClient, emails.WithAppTmpDir(appTmpDir))
		if err != nil {
			// If processing fails, remove the in-flight key to allow another message to be processed.
			if delErr := redis.DeleteKey(ctx, rfcIDRedisKey); delErr != nil && !errors.Is(delErr, redis.NilEntry) {
				log.Warn(
					ctx,
					"failed to delete in-flight redis key after processing failure",
					zap.Error(delErr),
					zap.String("key", rfcIDRedisKey),
				)
			}
			return err
		}

		// After successful processing of the primary email, process any deferred siblings.
		if err := processSiblingEmails(ctx, *newEmailID, rfcIDRedisKey); err != nil {
			// Log the error but don't fail the whole operation, as the primary email was successful.
			log.Warn(ctx, "failed to process sibling emails", zap.Error(err))
		}

		// Now, set the final lock value with the 'done' status prefix.
		log.Info(ctx, "processing succeeded, setting final redis key with email ID")
		finalValue := fmt.Sprintf("done:%d", *newEmailID)
		emails.SetRedisWithWarn(
			ctx,
			rfcIDRedisKey,
			finalValue,
			24*time.Hour, // Long TTL for final state
		)
		// If locally you want to repeatedly test the same email(s) + deduplication,
		// comment out the above and uncomment the below
		// if redisErr := redis.DeleteKey(ctx, rfcIDRedisKey); redisErr != nil {
		// 	log.Warn(ctx, "failed to delete in-flight redis key after processing success", zap.Error(redisErr))
		// }

	case copyAction:
		// When copying, we need to create the DeferredSiblingMessage to pass to copyIfAlreadyProcessed
		deferredMsg := DeferredSiblingMessage{
			UserID:             msg.UserID,
			Account:            msg.Account,
			ServiceID:          msg.ServiceID,
			ExternalID:         msg.ExternalID,
			ThreadID:           msg.ThreadID,
			RFCMessageID:       msg.RFCMessageID,
			ProcessingMetadata: msg.ProcessingMetadata,
		}

		err = copyIfAlreadyProcessed(ctx, existingEmail, deferredMsg, &service, sqsClient)
		if err != nil {
			if strings.Contains(err.Error(), "shared carrier quoting thread") {
				log.Info(ctx, err.Error())
			} else {
				// Fail-open; occasional duplicate processing is fine
				log.Warn(ctx, "failed to copy processed email info, continuing processing", zap.Error(err))
			}

			*newEmailID, err = analyzeFunc(ctx, &msg, service, sqsClient, emails.WithAppTmpDir(appTmpDir))
			return err
		}
		return nil

	default:
		log.Warn(ctx, "unknown action, processing email", zap.String("action", action))
		*newEmailID, err = analyzeFunc(ctx, &msg, service, sqsClient, emails.WithAppTmpDir(appTmpDir))
	}

	// Delete ingestion status for frontend loading state
	ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", msg.ThreadID)
	if err := redis.DeleteKey(ctx, ingestionStatusKey); err != nil && !errors.Is(err, redis.NilEntry) {
		log.Error(ctx, "error deleting email ingestion status from redis", zap.Error(err))
	}

	return err
}

// extendRedisLock extends the Redis lock for the email every 5 seconds until processing is complete (error or success).
// Processing duration varies widely due to dependencies, transient network lags, email labels & pipelines,
// (e.g. PDF load building takes longer than check call pipeline), etc. We want the lock to account for this in order
// to meaningfully reduce the chances of duplicate processing. However, we don't want to use a high TTL for all emails
// in order to minimize user's wait time. Hence, we continuously extend the lock until processing finishes or times out.
// as a compromise b/w reducing duplicate processing and minimizing user wait time.
//
// (Optional reading)
// Upper bound on artificially inflated user wait time:
// X = processing duration of Copy A
// C = ~50-300ms = time to process copy B after copy A completes
// R = 10s = SQS Retry configuration (see Parthenon)
// E = 5s = Redis lock extension interval
//
// Suppose Lambda extends the Redis lock a split second before Copy A processing completes,
// and SQS *just* started the 10s retry countdown for Copy B. Then, User B has to wait a maximum of
//
//	R + E + C = 5 + 10 + ~100ms = 15.1s
//
// *in addition to X* for their email available to them, compared to User A who waits just X.
// But on average, the delay will be much lower than this; furthermore, deduplicating reduces strain on network and LLM,
// reducing X itself which lowers overall user wait time for both A & B.
func extendRedisLock(ctx context.Context, msg models.IngestedEmail, rfcIDRedisKey string, done <-chan struct{}) {
	ticker := time.NewTicker(4 * time.Second)
	timeout := time.After(2 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			// Timeout if Zerox or OpenAI is slow; retrying on the copy may be faster
			log.WarnNoSentry(ctx, "redis lock extension timed out after 2 minutes",
				zap.String("rfcId", msg.RFCMessageID))
			return
		case <-done:
			return
		case <-ticker.C:
			// Extend TTL by 5 seconds
			log.Debug(ctx, "extending redis lock")
			emails.SetRedisWithWarn(ctx, rfcIDRedisKey, string(models.InFlight), 5*time.Second)
		case <-ctx.Done():
			return
		}
	}
}

// determineAction determines the action to take based on the email's status in Redis--process, copy, or retry later
func determineAction(
	ctx context.Context,
	msg models.IngestedEmail,
	rfcIDRedisKey string,
	service *models.Service,
) (existingEmail *models.Email, action string) {

	redisVal, found, err := redis.GetKey[string](ctx, rfcIDRedisKey)

	// TODO: remove this after SDS and Trifecta are fixed to support carrier quote
	if service.IsCarrierNetworkQuotingEnabled {
		log.Info(ctx, "turn off email de-dupe due to carrier quoting feature")
		return nil, processAction
	}

	switch {
	case !env.Vars.CopyDuplicateEmails:
		log.Info(ctx, "email deduplication is disabled, processing email")
		return nil, processAction

	case err != nil && !errors.Is(err, redis.NilEntry):
		// Fail-open; occasional duplicate processing is fine
		log.WarnNoSentry(ctx, "failed to get message RFC lock, processing email", zap.Error(err))
		return nil, processAction

	case found && redisVal == string(models.InFlight):
		log.Info(ctx, "email already being processed by another instance, deferring this message")
		siblingKey := rfcIDRedisKey + ":siblings"

		// Create a smaller representation of the sibling message to save on Redis memory.
		deferredMsg := DeferredSiblingMessage{
			UserID:             msg.UserID,
			Account:            msg.Account,
			ServiceID:          msg.ServiceID,
			ExternalID:         msg.ExternalID,
			ThreadID:           msg.ThreadID,
			RFCMessageID:       msg.RFCMessageID,
			ProcessingMetadata: msg.ProcessingMetadata,
		}

		msgJSON, err := json.Marshal(deferredMsg)
		if err != nil {
			// If we can't marshal, we can't defer. Process it to avoid losing the email.
			log.Error(ctx, "failed to marshal sibling message for deferral, processing instead", zap.Error(err))
			return nil, processAction
		}

		// Use a pipeline to atomically add the sibling and set a TTL on the list
		pipe := redis.RDB.Pipeline()
		pipe.RPush(ctx, siblingKey, msgJSON)
		pipe.Expire(ctx, siblingKey, 24*time.Hour)
		if _, err := pipe.Exec(ctx); err != nil {
			// If we can't write to redis, we can't defer. Process it to avoid losing the email.
			log.Error(ctx, "failed to push sibling message to redis, processing instead", zap.Error(err))
			return nil, processAction
		}

		log.Info(ctx, "successfully deferred sibling message", zap.String("siblingKey", siblingKey))
		return nil, deferAndExitAction

	case !found:
		// Lock the RFC message ID to prevent duplicate processing
		// Cannot check the DB because email is added to DB before all processing (i.e. LLM pipelines) finishes
		// According to AWS CW logs, avg. processing time is ~10s, so we initially lock for 5s and extendRedisLock()
		// will extend it every 5s until processing finishes or times out
		log.Info(
			ctx,
			"email not found in Redis, processing email",
			zap.String("key", rfcIDRedisKey),
			zap.String("redisVal", redisVal),
		)
		emails.SetRedisWithWarn(ctx, rfcIDRedisKey, string(models.InFlight), redisLockExtension)

		return nil, processAction

	default:
		var emailIDStr string
		if strings.HasPrefix(redisVal, "done:") {
			emailIDStr = strings.TrimPrefix(redisVal, "done:")
		} else {
			emailIDStr = redisVal
		}

		id, convErr := strconv.Atoi(emailIDStr)
		if convErr != nil || id <= 0 {
			log.WarnNoSentry(
				ctx,
				"failed to convert redis value to int, processing email",
				zap.Error(convErr),
				zap.String("redisVal", redisVal),
			)

			return nil, processAction
		}

		existingEmail, err := dbGetEmailByIDFunc(ctx, uint(id))
		if err != nil {
			// Fail-open; occasional duplicate processing is fine
			log.WarnNoSentry(ctx, "failed to get email by ID, processing email", zap.Error(err))

			return nil, processAction
		}

		if strings.Contains(existingEmail.Labels, string(emails.CarrierQuoteResponseLabel)) &&
			service.IsDelegatedInboxEnabled &&
			service.IsCarrierNetworkQuotingEnabled {

			log.Info(ctx, "email may belong to shared carrier quoting thread, processing email")

			return nil, processAction
		}

		log.Info(ctx, "email already processed", zap.String("redisVal", redisVal))
		return &existingEmail, copyAction

	}

}

// TODO: Only add the service ID at the moment of ingestion instead of here
// This is more complicated to do because it will break replay of older
// emails from S3 which means it requires some transition period
func resolveServiceID(ctx context.Context, msg models.IngestedEmail) (uint, error) {
	if msg.ServiceID != 0 {
		return msg.ServiceID, nil
	}

	user, err := userDB.GetByID(ctx, msg.UserID)
	if err != nil {
		log.ErrorNoSentry(ctx, "db error", zap.Error(err))
		return 0, fmt.Errorf("userDB.GetByID: %w", err)
	}

	sentry.SetUser(ctx, &user)
	return user.ServiceID, nil
}

// copyProcessedEmailInfo copies processed associations from the existingEmail to the msg, namely:
//   - Loads
//   - QuoteRequest suggestions
//   - LoadBuilding suggestions
//   - Check call & appointment suggestions
//   - Truck list suggestions
//   - Vector associations for emails and suggestions
//
// NOTES:
//   - Applied fields are *not* copied bc those are specific to each user and must be separate for accurate metrics.
//   - As pipelines are added, this should be updated to copy the appropriate fields.
//   - CarrierQuotes are not copied because even if carrier CC's multiple users, the carrier quote DB record
//     is associated only with the user and thread who sent the carrier email.
//   - Attachments are still stored by user email in ingestion step
func copyIfAlreadyProcessed(
	ctx context.Context,
	existingEmail *models.Email,
	deferredMsg DeferredSiblingMessage,
	service *models.Service,
	sqsClient sqsclient.API,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "copyIfAlreadyProcessed", nil)
	defer func() { metaSpan.End(err) }()

	vectorRepo := dbGetVectorRepositoryFunc(ctx)
	if vectorRepo == nil {
		return errors.New("failed to get vector repository")
	}

	// Small optimization to skip if this email has already been processed; this also preserves prevents false positive
	// on "copied_from_email_id" metadata
	if existingEmail.ExternalID == deferredMsg.ExternalID {
		log.Info(
			ctx,
			"skipping copy of already processed email",
			zap.Uint("existingEmailID", existingEmail.ID),
			zap.String("existingEmailAccount", existingEmail.Account),
			zap.String("ingestedExternalID", deferredMsg.ExternalID),
		)
		return nil
	}

	log.Info(
		ctx,
		"copying already processed email",
		zap.String("rfcId", deferredMsg.RFCMessageID),
	)

	// Attachments are copied from the existing email, not from the deferred message.
	newMsg := &models.Email{
		UserID:             deferredMsg.UserID,
		Account:            deferredMsg.Account,
		ServiceID:          deferredMsg.ServiceID,
		ExternalID:         deferredMsg.ExternalID,
		ThreadID:           deferredMsg.ThreadID,
		RFCMessageID:       deferredMsg.RFCMessageID,
		Attachments:        existingEmail.Attachments,
		ProcessingMetadata: deferredMsg.ProcessingMetadata,
	}

	newMsg = existingEmail.CopyProcessedDuplicate(newMsg)

	if err := dbUpsertEmailFunc(ctx, newMsg); err != nil {
		return fmt.Errorf("failed to upsert email and loads: %w", err)
	}
	log.Info(ctx, "successfully upserted copied email and loads", zap.Uint("emailID", newMsg.ID))

	// Process forwarding rules synchronously; if error, fail-close and process email from scratch
	if service.IsEmailForwardingEnabled {
		if err := processForwardingRulesFunc(ctx, *newMsg, service, sqsClient); err != nil {
			return fmt.Errorf("failed to process forwarding rules:%w", err)
		}
	}

	// Copy email vector associations
	if len(existingEmail.Vectors) > 0 {
		log.Debug(ctx, "copying email vector associations",
			zap.Uint("emailID", existingEmail.ID),
			zap.Int("vectorCount", len(existingEmail.Vectors)))

		if err := vectorRepo.BatchAssociateWithEmail(ctx, existingEmail.Vectors, newMsg); err != nil {
			log.WarnNoSentry(ctx, "failed to batch associate vectors with copied email",
				zap.Error(err),
				zap.Uint("emailID", newMsg.ID),
				zap.Int("vectorCount", len(existingEmail.Vectors)))
		}
	}

	// Copy load suggestions; fail-open on failures as we do for pipelines
	loadSuggestions, err := dbGetLoadSuggestionsFunc(ctx, existingEmail.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.WarnNoSentry(ctx, "failed to get load suggestions by email ID", zap.Error(err))
	}

	shouldCopyQuoteRequestSeparately := true

	for _, existingSug := range loadSuggestions {
		if existingSug.QuoteRequestSuggestionID != nil && existingSug.QuoteRequestSuggestion == nil {
			log.Warn(
				ctx,
				"Quote Request Suggestion should be preloaded for copying",
				zap.Uint("suggestionID", existingSug.ID))
		}

		newSug := existingSug.CopyProcessedDuplicate(newMsg)

		if existingSug.QuoteRequestSuggestionID != nil {
			log.Debug(ctx, "setting shouldCopyQuoteRequestSeparately to false")
			shouldCopyQuoteRequestSeparately = false
		}

		if err := dbCreateLoadSuggestionFunc(ctx, newSug); err != nil {
			log.WarnNoSentry(ctx, "failed to store copied load suggestion",
				zap.Error(err), zap.Uint("suggestionID", existingSug.ID))
		} else if len(existingSug.Vectors) > 0 {
			// Copy vector associations for the load suggestion
			log.Debug(ctx, "copying load suggestion vector associations",
				zap.Uint("suggestionID", existingSug.ID),
				zap.Int("vectorCount", len(existingSug.Vectors)))

			if err := vectorRepo.BatchAssociateWithLoadSuggestion(ctx, existingSug.Vectors, newSug); err != nil {
				log.WarnNoSentry(ctx, "failed to batch associate vectors with copied load suggestion",
					zap.Error(err),
					zap.Uint("suggestionID", newSug.ID),
					zap.Int("vectorCount", len(existingSug.Vectors)))
			}

		}
	}

	// Copy quote request suggestions generated
	if shouldCopyQuoteRequestSeparately {
		existingQRs, err := dbGetQuoteRequestsFunc(ctx, existingEmail.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to get quote request by email ID: %w", err)
		}

		for _, existingQR := range existingQRs {
			newQR := existingQR.CopyProcessedDuplicate(newMsg)

			err = dbCreateQuoteRequestFunc(ctx, newQR)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"failed to store copied quote request",
					zap.Error(err),
					zap.Uint("quoteRequestID", existingQR.ID))
			} else if len(existingQR.Vectors) > 0 {
				// Copy vector associations for the quote request
				log.Debug(ctx, "copying quote request vector associations",
					zap.Uint("quoteRequestID", existingQR.ID),
					zap.Int("vectorCount", len(existingQR.Vectors)))

				if err := vectorRepo.BatchAssociateWithQuoteRequest(ctx, existingQR.Vectors, newQR); err != nil {
					log.WarnNoSentry(ctx, "failed to batch associate vectors with copied quote request",
						zap.Error(err),
						zap.Uint("quoteRequestID", newQR.ID),
						zap.Int("vectorCount", len(existingQR.Vectors)))
				}

			}
		}
	}

	// Copy truck list suggestions
	existingTruckList, err := dbGetTruckListFunc(ctx, fmt.Sprint(existingEmail.ID))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to get truck list by email ID: %w", err)
	}

	if existingTruckList != nil {
		newTruckList := existingTruckList.CopyProcessedDuplicate(newMsg)

		err = dbCreateTruckListFunc(ctx, newTruckList)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to store copied truck list",
				zap.Error(err),
				zap.Uint("truckListID", existingTruckList.ID),
			)
		}
	}

	log.Info(ctx, "successfully copied processed email info")
	return nil
}

// processSiblingEmails retrieves deferred sibling emails from Redis and processes them by copying
// the results from the already-processed primary email.
func processSiblingEmails(ctx context.Context, processedEmailID uint, rfcIDRedisKey string) (err error) {
	ctx, span := otel.StartSpan(ctx, "processSiblingEmails", nil)
	defer span.End(err)

	siblingsKey := rfcIDRedisKey + ":siblings"
	log.Info(ctx, "checking for and processing sibling emails", zap.String("siblingsKey", siblingsKey))

	// Fetch the original, fully processed email to use as a template for copies.
	processedEmail, err := dbGetEmailByIDFunc(ctx, processedEmailID)
	if err != nil {
		return fmt.Errorf("failed to get processed email by ID %d to copy for siblings: %w", processedEmailID, err)
	}

	// Use a slice to hold all siblings to avoid modifying the list while iterating if we used LRem
	siblingsJSON, err := redis.RDB.LRange(ctx, siblingsKey, 0, -1).Result()
	if err != nil && !errors.Is(err, redis.NilEntry) {
		return fmt.Errorf("failed to get sibling emails from redis list %s: %w", siblingsKey, err)
	}

	if len(siblingsJSON) == 0 {
		log.Info(ctx, "no sibling emails to process")
		return nil
	}

	log.Info(ctx, "found deferred sibling emails to process", zap.Int("count", len(siblingsJSON)))

	service, err := dbGetServiceFunc(ctx, processedEmail.ServiceID)
	if err != nil {
		return fmt.Errorf("failed to get service by ID %d: %w", processedEmail.ServiceID, err)
	}

	for _, siblingJSON := range siblingsJSON {
		var siblingMsg DeferredSiblingMessage
		if err := json.Unmarshal([]byte(siblingJSON), &siblingMsg); err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to unmarshal sibling email from redis, skipping",
				zap.Error(err),
				zap.String("json", siblingJSON),
			)
			// Remove the malformed entry to prevent repeated processing attempts.
			if remErr := redis.RDB.LRem(ctx, siblingsKey, 1, siblingJSON).Err(); remErr != nil {
				log.Warn(ctx, "failed to remove malformed sibling email from redis list", zap.Error(remErr))
			}
			continue
		}

		// Use a new context for each sibling to avoid log pollution, while inheriting
		// the parent trace for observability.
		siblingCtx := log.NewFromEnv(ctx)
		siblingCtx = log.With(siblingCtx,
			zap.String("account", siblingMsg.Account),
			zap.String("externalId", siblingMsg.ExternalID),
			zap.String("threadId", siblingMsg.ThreadID),
			zap.String("rfcId", siblingMsg.RFCMessageID),
			zap.String("subject", processedEmail.Subject), // Subject from processed email
			zap.String("sender", processedEmail.Sender),   // Sender from processed email
			zap.Uint("copied_from_email_id", processedEmail.ID),
		)

		log.Info(siblingCtx, "processing deferred sibling email")
		if err := copyIfAlreadyProcessed(siblingCtx, &processedEmail, siblingMsg, &service, sqsClient); err != nil {
			// Don't let one failed copy stop the others. Sibling remains in the list for future retry.
			log.Warn(siblingCtx, "failed to copy processed info to sibling email, leaving in list", zap.Error(err))
		} else {
			// Successfully processed, so remove it from the list.
			if remErr := redis.RDB.LRem(ctx, siblingsKey, 1, siblingJSON).Err(); remErr != nil {
				log.Warn(ctx, "failed to remove processed sibling email from redis list", zap.Error(remErr))
			}
			ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", siblingMsg.ThreadID)
			if delErr := redis.DeleteKey(ctx, ingestionStatusKey); delErr != nil && !errors.Is(delErr, redis.NilEntry) {
				log.Error(ctx, "error deleting email ingestion status from redis", zap.Error(delErr))
			}
		}
	}

	log.Info(ctx, "finished processing sibling emails", zap.String("siblingsKey", siblingsKey))
	return nil
}

// captureSQSMetadata captures metadata from an SQS record for analytics and logging
func captureSQSMetadata(
	ctx context.Context,
	start time.Time,
	record events.SQSMessage,
	msg *models.IngestedEmail,
) context.Context {
	// Add metadata to context for logging
	ctx = log.With(ctx,
		zap.String("sqsEventId", record.MessageId),
		zap.String("sqsApproximateReceiveCount", record.Attributes["ApproximateReceiveCount"]),
	)

	msg.SQSEventID = record.MessageId
	msg.ProcessingStartTime = start

	if lc, ok := lambdacontext.FromContext(ctx); ok {
		msg.LambdaRequestID = lc.AwsRequestID
	}

	if ts, ok := record.Attributes["ApproximateFirstReceiveTimestamp"]; ok {
		if unixMs, err := strconv.ParseInt(ts, 10, 64); err == nil {
			timestamp := time.Unix(0, unixMs*int64(time.Millisecond))
			msg.SQSApproximateFirstReceiveTimestamp = timestamp
			ctx = log.With(ctx, zap.Time("sqsApproximateFirstReceiveTimestamp", timestamp))
		}
	}

	if ts, ok := record.Attributes["SentTimestamp"]; ok {
		if unixMs, err := strconv.ParseInt(ts, 10, 64); err == nil {
			timestamp := time.Unix(0, unixMs*int64(time.Millisecond))
			msg.SQSSentTimestamp = timestamp
		}
	}

	if count, ok := record.Attributes["ApproximateReceiveCount"]; ok {
		if receiveCount, err := strconv.Atoi(count); err == nil {
			msg.SQSApproximateReceiveCount = receiveCount
		}
	}

	return ctx
}

// cleanupTmpDirectory removes all files and directories from /tmp/drumkit-processor to prevent memory accumulation
// across Lambda invocations. This is particularly important for MuPDF temporary files created
// during PDF processing that may persist when Lambda execution environments are reused.
// Only runs cleanup when in a lambda environment.
func cleanupTmpDirectory(ctx context.Context) {
	// Only cleanup temporary directory when running in Lambda environment
	if !helpers.IsLambda() {
		return
	}

	// Ensure directory exists before attempting cleanup
	if _, err := os.Stat(appTmpDir); os.IsNotExist(err) {
		if err := os.MkdirAll(appTmpDir, 0755); err != nil {
			log.Warn(
				ctx,
				"Failed to create app temp directory for cleanup",
				zap.String("path", appTmpDir),
				zap.Error(err),
			)
			return
		}
	}

	items, err := filepath.Glob(appTmpDir + "/*")
	if err != nil {
		log.Warn(
			ctx,
			"Error reading app temp directory",
			zap.String("path", appTmpDir),
			zap.Error(err),
		)
		return
	}

	if len(items) == 0 {
		log.Debug(ctx, "No temporary files to clean up")
		return
	}

	cleanedCount := 0
	errorCount := 0

	for _, item := range items {
		if err := os.RemoveAll(item); err != nil {
			log.Warn(ctx, "Error deleting temp file", zap.String("path", item), zap.Error(err))
			errorCount++
		} else {
			log.Debug(ctx, "Successfully deleted temp file", zap.String("path", item))
			cleanedCount++
		}
	}

	if cleanedCount > 0 || errorCount > 0 {
		log.Info(
			ctx,
			"Completed app temp directory cleanup",
			zap.String("path", appTmpDir),
			zap.Int("cleaned", cleanedCount),
			zap.Int("errors", errorCount),
		)
	}
}

// logMemoryStats logs current memory usage statistics for monitoring.
// Includes both Go heap memory and total process RSS to track C allocations.
func logMemoryStats(ctx context.Context, stage string) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// TODO: Change this to .Debug after initial monitoring period
	log.Info(
		ctx,
		"Memory statistics",
		zap.String("stage", stage),
		zap.Uint64("alloc_mb", bToMb(m.Alloc)),
		zap.Uint64("total_alloc_mb", bToMb(m.TotalAlloc)),
		zap.Uint64("sys_mb", bToMb(m.Sys)),
		zap.Uint32("num_gc", m.NumGC),
	)
}

// bToMb converts bytes to megabytes
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}
