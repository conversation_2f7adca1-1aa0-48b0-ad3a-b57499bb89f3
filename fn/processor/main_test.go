package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/vector"
	commonRedis "github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

var originalEmail = models.Email{
	Model:   gorm.Model{ID: 1},
	Account: "<EMAIL>",
}

var dupeEmail = &models.IngestedEmail{
	Account:      "<EMAIL>",
	UserID:       1,
	ServiceID:    1,
	RFCMessageID: "msg123",
	ExternalID:   "ext123",
	ThreadID:     "thread123",
	Body:         "test body",
	Recipients:   "<EMAIL>",
	CC:           "<EMAIL>",
	Subject:      "Test Subject",
	SentAt:       time.Now(),
	Sender:       "<EMAIL>",
	BodyType:     models.PlaintextEmailBodyType,
}

func TestDeduplicateProcessing(t *testing.T) {
	setupMocks()
	env.Vars.CopyDuplicateEmails = true

	tests := []struct {
		name           string
		email          *models.IngestedEmail
		setupMockRedis func(mock redismock.ClientMock)
		setupMockDB    func(ctx context.Context, emailID uint) (email models.Email, err error)
		expectedError  error
	}{
		{
			name:  "New email - should process",
			email: dupeEmail,
			setupMockRedis: func(mock redismock.ClientMock) {
				key := "message-service-1-rfc-msg123"

				// 1. Check if email already exists
				mock.ExpectGet(key).RedisNil()

				// 2. Set in-flight status - use the exact byte array format
				inFlightBytes := []byte("\"inFlight\"")
				mock.ExpectSet(key, inFlightBytes, redisLockExtension).SetVal("OK")

				// 3. After processing, check for siblings
				siblingKey := key + ":siblings"
				mock.ExpectLRange(siblingKey, 0, -1).RedisNil()

				// 4. Set the final 'done' status - use the exact byte array format
				doneBytes := []byte("\"done:1\"")
				mock.ExpectSet(key, doneBytes, 24*time.Hour).SetVal("OK")

				// 5. Delete ingestion status for frontend loading state
				ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", dupeEmail.ThreadID)
				mock.ExpectDel(ingestionStatusKey).SetVal(1)
			},
			expectedError: nil,
		},
		{
			name:  "Email in-flight - should defer",
			email: dupeEmail,
			setupMockRedis: func(mock redismock.ClientMock) {
				key := "message-service-1-rfc-msg123"
				mock.ExpectGet(key).SetVal("\"inFlight\"")

				// Expect the sibling to be pushed to the list
				siblingKey := key + ":siblings"
				deferredMsg := DeferredSiblingMessage{
					UserID:             dupeEmail.UserID,
					Account:            dupeEmail.Account,
					ServiceID:          dupeEmail.ServiceID,
					ExternalID:         dupeEmail.ExternalID,
					ThreadID:           dupeEmail.ThreadID,
					RFCMessageID:       dupeEmail.RFCMessageID,
					ProcessingMetadata: dupeEmail.ProcessingMetadata,
				}
				msgJSON, err := json.Marshal(deferredMsg)
				assert.NoError(t, err)
				mock.ExpectRPush(siblingKey, msgJSON).SetVal(1)
				mock.ExpectExpire(siblingKey, 24*time.Hour).SetVal(true)
			},
			expectedError: nil,
		},
		{
			name:  "Email already processed - should copy",
			email: dupeEmail,
			setupMockRedis: func(mock redismock.ClientMock) {
				mock.ExpectGet("message-service-1-rfc-msg123").SetVal("\"done:1\"")
			},

			expectedError: nil,
		},
		{
			name:  "Carrier quote thread - should process",
			email: dupeEmail,
			setupMockRedis: func(mock redismock.ClientMock) {
				mock.ExpectGet("message-service-1-rfc-msg123").SetVal("\"accepted\"")

				// Delete ingestion status for frontend loading state
				ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", dupeEmail.ThreadID)
				mock.ExpectDel(ingestionStatusKey).SetVal(1)
			},
			setupMockDB: func(_ context.Context, emailID uint) (email models.Email, err error) {
				email = originalEmail
				email.ID = emailID
				email.Labels = string(emails.CarrierQuoteResponseLabel)

				return email, nil
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set up mocks
			mockRDB, mock := redismock.NewClientMock()
			commonRedis.RDB = mockRDB
			tt.setupMockRedis(mock)
			if tt.setupMockDB != nil {
				dbGetEmailByIDFunc = tt.setupMockDB
			}

			defer func() {
				// Reset to default mock after each test run
				dbGetEmailByIDFunc = func(_ context.Context, id uint) (models.Email, error) {
					return models.Email{Model: gorm.Model{ID: id}, Account: "<EMAIL>"}, nil
				}
			}()

			err := startProcessing(context.Background(), *tt.email)

			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestDeduplicateProcessingWithForwardingRules(t *testing.T) {
	setupMocks()
	env.Vars.CopyDuplicateEmails = true

	// Track forwarding rule calls
	var forwardingRuleCalls []string
	processForwardingRulesFunc = func(_ context.Context, email models.Email, _ *models.Service, _ sqsclient.API) error {
		forwardingRuleCalls = append(
			forwardingRuleCalls,
			fmt.Sprintf("ProcessForwardingRules called for email %s (user %d)", email.ExternalID, email.UserID),
		)
		return nil
	}

	// Reset the mock after test
	defer func() {
		processForwardingRulesFunc = emails.ProcessForwardingRules
	}()

	// Create test emails - original and duplicate
	originalEmail := models.Email{
		Model:        gorm.Model{ID: 1},
		Account:      "<EMAIL>",
		UserID:       1,
		ServiceID:    1,
		ExternalID:   "original-ext-123",
		ThreadID:     "thread123",
		RFCMessageID: "msg123",
		Subject:      "Test Subject",
		Labels:       "load_building", // Has forwarding rule
	}

	duplicateEmail := &models.IngestedEmail{
		Account:      "<EMAIL>",
		UserID:       2, // Different user
		ServiceID:    1,
		RFCMessageID: "msg123", // Same RFC ID
		ExternalID:   "duplicate-ext-456",
		ThreadID:     "thread123", // Same thread as original
		Body:         "test body",
		Subject:      "Test Subject",
		Sender:       "<EMAIL>",
	}

	// Common setup function
	setupTest := func(t *testing.T, forwardingEnabled bool) {
		// Reset call tracker
		forwardingRuleCalls = []string{}

		// Set up Redis mock for copy action
		mockRDB, mock := redismock.NewClientMock()
		commonRedis.RDB = mockRDB

		rfcKey := "message-service-1-rfc-msg123"
		mock.ExpectGet(rfcKey).SetVal("\"done:1\"") // Email already processed

		// Mock the database function to return the original email
		dbGetEmailByIDFunc = func(_ context.Context, id uint) (models.Email, error) {
			if id == 1 {
				return originalEmail, nil
			}
			return models.Email{}, gorm.ErrRecordNotFound
		}

		// Mock service with forwarding enabled/disabled
		dbGetServiceFunc = func(_ context.Context, id uint) (models.Service, error) {
			return models.Service{
				Model: gorm.Model{ID: id},
				Name:  "Test Service",
				FeatureFlags: models.FeatureFlags{
					IsEmailForwardingEnabled: forwardingEnabled,
				},
			}, nil
		}

		// Clean up after test
		t.Cleanup(func() {
			// Reset mocks
			dbGetEmailByIDFunc = func(_ context.Context, id uint) (models.Email, error) {
				return models.Email{Model: gorm.Model{ID: id}, Account: "<EMAIL>"}, nil
			}
			dbGetServiceFunc = func(_ context.Context, id uint) (models.Service, error) {
				return models.Service{Model: gorm.Model{ID: id}, Name: "Test Service"}, nil
			}
		})

		// Process the duplicate email
		err := startProcessing(context.Background(), *duplicateEmail)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	}

	t.Run("Forwarding enabled - duplicate gets forwarding rule processed", func(t *testing.T) {
		setupTest(t, true)

		// Verify forwarding rules were called for the copied email
		assert.Len(t, forwardingRuleCalls, 1)
		assert.Contains(t, forwardingRuleCalls[0], "duplicate-ext-456")
		assert.Contains(t, forwardingRuleCalls[0], "user 2")
	})

	t.Run("Forwarding disabled - duplicate does not get forwarding rule processed", func(t *testing.T) {
		setupTest(t, false)

		// Verify forwarding rules were not called since forwarding is disabled
		assert.Len(t, forwardingRuleCalls, 0)
	})
}

func TestCopyIfAlreadyProcessed(t *testing.T) {
	// Mock implementations
	setupMocks()
	service := models.Service{
		Model: gorm.Model{ID: 1},
		Name:  "Test Service",
	}

	deferredDupeEmail := DeferredSiblingMessage{
		UserID:             dupeEmail.UserID,
		Account:            dupeEmail.Account,
		ServiceID:          dupeEmail.ServiceID,
		ExternalID:         dupeEmail.ExternalID,
		ThreadID:           dupeEmail.ThreadID,
		RFCMessageID:       dupeEmail.RFCMessageID,
		ProcessingMetadata: dupeEmail.ProcessingMetadata,
	}

	t.Run("Successfully copy email", func(t *testing.T) {
		err := copyIfAlreadyProcessed(
			context.Background(),
			&originalEmail,
			deferredDupeEmail,
			&service,
			&sqsclient.MockSQSClient{},
		)
		assert.NoError(t, err)
	})

	t.Run("Separately copy QRs", func(t *testing.T) {
		dbGetLoadSuggestionsFunc = func(_ context.Context, emailID uint) ([]models.SuggestedLoadChange, error) {
			id1 := uint(1)
			id2 := uint(2)

			return []models.SuggestedLoadChange{
				{
					Model:                    gorm.Model{ID: 1},
					EmailID:                  emailID,
					QuoteRequestSuggestionID: &id1,
					QuoteRequestSuggestion: &models.QuoteRequest{
						ID:      id1,
						EmailID: emailID,
					},
				},
				{
					Model:                    gorm.Model{ID: 2},
					EmailID:                  emailID,
					QuoteRequestSuggestionID: &id2,
					QuoteRequestSuggestion: &models.QuoteRequest{
						ID:      id2,
						EmailID: emailID,
					},
				},
			}, nil
		}

		dbGetQuoteRequestsFunc = func(context.Context, uint) ([]models.QuoteRequest, error) {
			t.Error("dbGetQuoteRequestsFunc should not be called")
			return nil, nil
		}

		dbCreateQuoteRequestFunc = func(context.Context, *models.QuoteRequest) error {
			t.Error("dbCreateQuoteRequestFunc should not be called")
			return nil
		}

		err := copyIfAlreadyProcessed(
			context.Background(),
			&originalEmail,
			deferredDupeEmail,
			&service,
			&sqsclient.MockSQSClient{},
		)
		assert.NoError(t, err)
	})
}

type MockVectorRepository struct {
	Repository vector.Repository
}

func setupMocks() {
	analyzeFunc = func(
		context.Context,
		*models.IngestedEmail,
		models.Service,
		sqsclient.API,
		...emails.Option,
	) (uint, error) {
		time.Sleep(3 * time.Second)
		return 1, nil
	}

	processForwardingRulesFunc = func(_ context.Context, _ models.Email, _ *models.Service, _ sqsclient.API) error {
		return nil
	}

	dbGetVectorRepositoryFunc = func(_ context.Context) *vector.Repository {
		mock := &MockVectorRepository{}
		return &mock.Repository
	}

	dbGetServiceFunc = func(_ context.Context, id uint) (models.Service, error) {
		return models.Service{
			Model: gorm.Model{ID: id},
			Name:  "Test Service",
		}, nil
	}

	dbUpsertEmailFunc = func(_ context.Context, email *models.Email) error {
		email.ID = 2 // Simulate DB auto-increment
		return nil
	}

	dbGetEmailByIDFunc = func(_ context.Context, id uint) (models.Email, error) {
		return models.Email{
			Model:   gorm.Model{ID: id},
			Account: "<EMAIL>",
		}, nil
	}

	dbGetLoadSuggestionsFunc = func(_ context.Context, emailID uint) ([]models.SuggestedLoadChange, error) {
		return []models.SuggestedLoadChange{
			{
				Model:                    gorm.Model{ID: 1},
				EmailID:                  emailID,
				QuoteRequestSuggestionID: nil,
			},
			{
				Model:                    gorm.Model{ID: 2},
				EmailID:                  emailID,
				QuoteRequestSuggestionID: nil, // Test both with and without QR suggestion
			},
		}, nil
	}

	dbCreateLoadSuggestionFunc = func(_ context.Context, suggestion *models.SuggestedLoadChange) error {
		suggestion.ID = 3 // Simulate DB auto-increment
		return nil
	}

	dbGetQuoteRequestsFunc = func(_ context.Context, emailID uint) ([]models.QuoteRequest, error) {
		return []models.QuoteRequest{
			{
				ID:      1,
				EmailID: emailID,
			},
		}, nil
	}

	dbCreateQuoteRequestFunc = func(_ context.Context, qr *models.QuoteRequest) error {
		qr.ID = 2 // Simulate DB auto-increment
		return nil
	}

	dbGetTruckListFunc = func(_ context.Context, emailID string) (*models.TruckList, error) {
		//nolint:errcheck
		id, _ := strconv.ParseUint(emailID, 10, 64)
		return &models.TruckList{
			Model:   gorm.Model{ID: 1},
			EmailID: uint(id),
		}, nil
	}

	dbCreateTruckListFunc = func(_ context.Context, tl *models.TruckList) error {
		tl.ID = 2 // Simulate DB auto-increment
		return nil
	}
}
