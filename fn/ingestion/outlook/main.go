package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/gofiber/fiber/v2"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	outlookhelpers "github.com/drumkitai/drumkit/common/outlook"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/outlook/env"
)

var (
	// Unit tests can replace these functions
	getUserByOutlookIDs = userDB.GetByOutlookIDs
	updateUser          = userDB.Update
	getEmailFunc        = emailDB.GetEmailByExternalID
	outlookConstructor  = msclient.New[models.UserAccessor]
	dbGetServiceFunc    = rds.GetServiceByID

	s3Uploader s3backup.Archiver
	sqsClient  sqsclient.API
)

const (
	serviceName    = "ingestion-outlook-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"                        // Version of the service.
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	// Set up graceful shutdown handling
	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-ingestion-outlook")

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	app := fiber.New()
	shutdownHandler.AddCleanupFunc(func(ctx context.Context) error {
		log.Info(ctx, "shutting down fiber server")
		return app.Shutdown()
	})

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-ingestion-outlook"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.S3BucketName != "" {
		var err error
		if s3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName); err != nil {
			panic(err)
		}
	}

	if !helpers.IsLambda() {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.WarnNoSentry(ctx, "Error running migrations", zap.Error(err))
		}

		errChan := runLocalServer(ctx, app)
		for {
			select {
			case err := <-errChan:
				log.Error(ctx, "fiber server failed to start", zap.Error(err))
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	var err error
	sqsClient, err = sqsclient.New(env.Vars.AppEnv)
	if err != nil {
		panic(err)
	}

	lambda.Start(otellambda.InstrumentHandler(handlerWithSentry))
}

func handlerWithSentry(
	ctx context.Context,
	request events.LambdaFunctionURLRequest,
) (result events.LambdaFunctionURLResponse, _ error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		ctx = log.NewFromEnv(ctx, zap.String("path", request.RawPath))
		result = handler(ctx, request)
	})

	return
}

func handler(ctx context.Context, request events.LambdaFunctionURLRequest) events.LambdaFunctionURLResponse {
	// Payloads don't include sensitive tokens or msg contents - safe to log
	log.Info(ctx, "received request", zap.Any("request", request))

	// Confirm outlook subscription
	if token := request.QueryStringParameters["validationToken"]; token != "" {
		log.Info(ctx, "received validation request - confirming subscription")

		return events.LambdaFunctionURLResponse{
			StatusCode: http.StatusOK,
			Headers:    map[string]string{"Content-Type": "text/plain"},
			Body:       token,
		}
	}

	var data msclient.OutlookWebhook
	if err := json.Unmarshal([]byte(request.Body), &data); err != nil {
		log.Error(ctx, "request body parsing failed", zap.Error(err))

		return events.LambdaFunctionURLResponse{StatusCode: http.StatusBadRequest}
	}

	log.Info(ctx, "received outlook webhook", zap.Any("wh", data))
	if count := len(data.Value); count > 1 {
		log.Info(ctx, "webhook contains more than 1 item", zap.Int("count", count))
	}

	for _, v := range data.Value {
		if err := processMsg(ctx, v); err != nil {
			return events.LambdaFunctionURLResponse{StatusCode: http.StatusInternalServerError}
		}
	}

	return events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}
}

func processMsg(ctx context.Context, v msclient.Value) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "processMsgOutlook", nil)
	defer func() {
		// NOTE: Error logged in startProcessing so it contains zap metadata (ENGB-2300)
		if err != nil {
			// Ignore logging temporary service errors
			if !strings.Contains(err.Error(), "429") &&
				!strings.Contains(err.Error(), "503") &&
				!strings.Contains(err.Error(), "504") {

				log.Error(ctx, "failed to process webhook", zap.Error(err))
			}

		}
		metaSpan.End(err)
	}()

	ctx = log.With(ctx, zap.String("externalID", v.ResourceData.ID), zap.String("resource", v.Resource))

	userExternalID, err := extractUserID(v.Resource)
	if err != nil {
		return err
	}

	user, err := getUserByOutlookIDs(ctx, userExternalID, v.ClientState, v.SubscriptionID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// NotFound can occur if the user exists but not that subscriptionID, meaning there are duplicate subs
			if stopErr := stopWatchingInbox(ctx, v.SubscriptionID, userExternalID); stopErr == nil {
				log.Info(ctx, "deleted unknown subscription", zap.String("subID", v.SubscriptionID))

				return nil
			} else if !errors.Is(stopErr, gorm.ErrRecordNotFound) {
				// NOTE: Subscriptions are short-lived, so if this fails, it will expire by itself in <3 days
				log.WarnNoSentry(ctx, "error canceling subscription", zap.Error(stopErr))
			}
		}

		return fmt.Errorf("unable to find user with externalID %s and clientState %s: %w",
			userExternalID, v.ClientState, err)
	}

	ctx = log.With(ctx, zap.String("account", user.EmailAddress), zap.Uint("serviceID", user.ServiceID))

	service, err := dbGetServiceFunc(ctx, user.ServiceID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting service feature flags, continuing", zap.Error(err))
	}

	client, err := outlookConstructor(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		return err
	}

	// Subscriptions are short-lived; we must re-subscribe
	if hoursLeft := time.Until(user.WebhookExpiration).Hours(); hoursLeft < 24 {
		rewatchInbox(ctx, &user, client)
	}

	if env.Vars.SkipDuplicates {
		if ingestion.ShouldSkipDuplicate(ctx, v.ResourceData.ID, getEmailFunc) {
			return nil
		}
	}

	// Construct the message summary from the webhook data to pass to the helper.
	msgSummary := msclient.Message{ID: v.ResourceData.ID}

	return outlookhelpers.ProcessAndEnqueueOutlookMessage(
		ctx,
		client,
		sqsClient,
		s3Uploader,
		&user,
		&service,
		msgSummary,
		v,
		env.Vars.SQSQueueURL,
	)
}

func extractUserID(input string) (string, error) {
	parts := strings.Split(input, "/")

	var userID string
	found := false

	for i, part := range parts {
		if strings.EqualFold(part, "Users") && i+1 < len(parts) {
			userID = parts[i+1]
			found = true
			break
		}
	}

	if !found {
		return "", errors.New("userID not found in the input string")
	}

	return userID, nil
}

func rewatchInbox(ctx context.Context, user *models.User, client msclient.Client) {
	// We can only rewatch an inbox here, so we pass in an empty string for the webhook url as we can't
	// create a new subscription.
	// The `WatchInbox` lambda handles the aforementioned edge case, if we run into a 404 when rewatching
	// the inbox.
	updatedSub, err := client.RewatchInbox(ctx, "", user)
	if err != nil {
		log.Warn(ctx, "error re-subscribing to Outlook inbox", zap.Error(err))
		return
	}
	log.Info(ctx, fmt.Sprint("successfully re-subscribed to inbox until ", updatedSub.ExpirationDateTime))

	user.WebhookExpiration = updatedSub.ExpirationDateTime
	if err = updateUser(ctx, *user); err != nil {
		// Fail-open; next Lambda will try again because the expiration time won't be updated
		log.WarnNoSentry(ctx, "error updating subscription expiration", zap.Error(err))
	}

}

func stopWatchingInbox(ctx context.Context, subID, userExternalID string) error {
	user, err := userDB.GetByExternalID(ctx, userExternalID)
	if err != nil {
		return err
	}

	client, err := outlookConstructor(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		return err
	}

	return client.StopWatchingInbox(ctx, subID)
}
