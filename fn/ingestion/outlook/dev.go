package main

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/ingestion"
)

const devPort = ":5006"

// POST format when invoking beacon-ingestion-outlook locally via Fiber
//
// Request body can be 1) an actual webhook from Outlook, or 2) defined explicitly.
// TODO: Support replaying from the S3 archive
type localWebhookBody struct {
	// Explicitly define the entire message yourself or receive an actual webhook from Outlook.
	// Postman example: https://shorturl.at/bqCJ7
	// NOTE: A user with the corresponding Outlook ID and client state must exist in your local DB.
	Value []msclient.Value `json:"value" validate:"required_without=S3Key"`

	EmailAddress string `json:"emailAddress"`

	// Alternatively, specify an email from the axle-beacon-ingestion S3 bucket to replay.
	// E.g. "outlook/<EMAIL>/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AsXKT2wft0kGXoohZ23c11QAAC-epXQAA.json"
	S3Key string `json:"s3Key" validate:"required_without=Value"`
}

func runLocalServer(ctx context.Context, app *fiber.App) <-chan error {
	errChan := make(chan error, 1)

	var localSQS sqsclient.LocalSQSClient
	sqsClient = &localSQS

	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/inboxWebhook", fiberHandler)

	go func(ctx context.Context) {
		if err := app.Listen(devPort); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Error(ctx, "fiber server failed", zap.Error(err))
			errChan <- err
		}
	}(ctx)
	return errChan
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	// Confirm outlook subscription
	if token := c.Queries()["validationToken"]; token != "" {
		log.Info(ctx, "received validation request - confirming subscription")

		return c.Status(http.StatusOK).SendString(token)
	}

	var body localWebhookBody
	if err := c.BodyParser(&body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	var msg msclient.Message
	if body.S3Key != "" {
		var err error
		if err = ingestion.DownloadFromS3(ctx, body.S3Key, &msg); err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}
		body.Value = append(body.Value, msclient.Value{}) // TODO
	} else if body.Value == nil {
		return c.Status(http.StatusBadRequest).SendString("s3Key or payload must be defined")
	}

	// Convert Fiber POST request into the Outlook payload expected by the app
	dataJSON, err := json.Marshal(msclient.OutlookWebhook{Value: body.Value})
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("invalid json body: " + err.Error())
	}

	event := events.LambdaFunctionURLRequest{
		Body:    string(dataJSON),
		RawPath: c.Path(),
	}

	log.Info(ctx, "sending event to beacon-ingestion-outlook handler", zap.Any("event", event))

	result := handler(ctx, event)

	return c.Status(result.StatusCode).SendString(result.Body)
}
