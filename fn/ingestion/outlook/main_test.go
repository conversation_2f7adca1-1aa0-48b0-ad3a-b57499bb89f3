package main

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/ingestion/outlook/env"
)

const (
	clientState = "TestClientState"
	testTenant  = "TestTenant"
	externalID  = "OutlookUser1"
)

//nolint:lll
const webhookBody = ` {"value":[{"subscriptionId":"test-subscription","subscriptionExpirationDateTime":"","changeType":"created","resource":"Users/OutlookUser1/Messages/msg2","resourceData":{"@odata.type":"","@odata.id":"","@odata.etag":"","id":"msg2"},"clientState":"TestClientState","tenantId":"TestTenant"}]}`

var (
	defaultMsgs = map[string]msclient.Message{
		"msg1": {
			ID: "msg1",
			Body: &msclient.Body{
				ContentType: "text",
				Content:     "This is the first message",
			},
		},
		"msg2": {
			ID: "msg2",
			Body: &msclient.Body{
				ContentType: "text",
				Content:     "This is the second message",
			},
		},
	}

	user = models.User{
		Model:                 gorm.Model{ID: 1},
		MailClientID:          externalID,
		EmailAddress:          "<EMAIL>",
		OutlookClientState:    clientState,
		WebhookExpiration:     time.Now().Add(48 * time.Hour),
		TokenExpiry:           time.Now().Add(48 * time.Hour),
		OutlookSubscriptionID: "test-subscription",
	}

	defaultUpdateUser = func(context.Context, models.User) error {
		return nil
	}

	defaultGetEmailFunc = func(_ context.Context, _ string) (*models.Email, error) {
		return nil, gorm.ErrRecordNotFound
	}

	defaultGetServiceByID = func(context.Context, uint) (res models.Service, _ error) {
		res.ID = 1
		return res, nil
	}
)

type mockSQSClient struct {
	sentMessagesByQueue map[string][]*sqs.SendMessageInput
}

func (c *mockSQSClient) SendMessage(
	_ context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {
	if c.sentMessagesByQueue == nil {
		c.sentMessagesByQueue = make(map[string][]*sqs.SendMessageInput)
	}
	queueURL := *input.QueueUrl
	c.sentMessagesByQueue[queueURL] = append(c.sentMessagesByQueue[queueURL], input)

	return &sqs.SendMessageOutput{}, nil
}

func TestIngestionHandler(t *testing.T) {
	env.Vars.SQSQueueURL = "processor-queue"
	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		// Make UpdatedAt != CreatedAt to force non-backfill logic
		res.UpdatedAt = time.Now()

		return res, nil
	}

	getEmailFunc = defaultGetEmailFunc
	updateUser = defaultUpdateUser
	dbGetServiceFunc = defaultGetServiceByID

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	mockSQS := &mockSQSClient{}
	sqsClient = mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()

	// Run the handler
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"GetMessageByID(msg2)",
		// Based on outlookhelpers.blackListedFolders
		"GetFolderByID(sentitems)",
		"GetFolderByID(outbox)",
		"GetFolderByID(deleteditems)",
		"GetFolderByID(drafts)",
		"GetFolderByID(scheduled)",
		"GetFolderByID(recoverableitemsdeletions)",
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify only 1 message sent to S3/SQS
	assert.Equal(t, map[string]bool{"msg2": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.sentMessagesByQueue[env.Vars.SQSQueueURL], 1)
}

func TestIngestionHandlerRewatchInbox(t *testing.T) {
	env.Vars.SQSQueueURL = "processor-queue"
	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	updateUser = defaultUpdateUser

	outlookConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}

	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		res := user
		res.UpdatedAt = time.Now()
		res.WebhookExpiration = time.Now()

		return res, nil
	}

	getEmailFunc = defaultGetEmailFunc

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	mockSQS := &mockSQSClient{}
	sqsClient = mockSQS

	event := events.LambdaFunctionURLRequest{
		Body: webhookBody,
	}

	ctx := context.Background()
	result, err := handlerWithSentry(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Outlook SDK
	expected := []string{
		"RewatchInbox(1)",
		"GetMessageByID(msg2)",
		// Based on outlookhelpers.blackListedFolders
		"GetFolderByID(sentitems)",
		"GetFolderByID(outbox)",
		"GetFolderByID(deleteditems)",
		"GetFolderByID(drafts)",
		"GetFolderByID(scheduled)",
		"GetFolderByID(recoverableitemsdeletions)",
	}
	assert.Equal(t, expected, mockService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"msg2": true}, mockS3.OutlookMessages)
	assert.Len(t, mockSQS.sentMessagesByQueue[env.Vars.SQSQueueURL], 1)
}

func TestIngestionHandlerDuplicateEmails(t *testing.T) {
	env.Vars.SkipDuplicates = true
	env.Vars.SQSQueueURL = "processor-queue"

	mockService := &mock.Client{
		Messages: defaultMsgs,
	}

	outlookConstructor = func(
		context.Context,
		string, string,
		models.UserAccessor,
		...oauth.Option,
	) (msclient.Client, error) {
		return mockService, nil
	}
	getUserByOutlookIDs = func(context.Context, string, string, string) (models.User, error) {
		// Return a user that is not new and not stale to prevent backfill
		res := user
		res.UpdatedAt = time.Now()
		return res, nil
	}

	// Mock the DB to return an existing email for "msg2", which is the ID of the message in the webhook
	// Only "msg1" email should be processed
	getEmailFunc = func(_ context.Context, externalID string) (*models.Email, error) {
		switch externalID {
		case "msg1":
			return nil, gorm.ErrRecordNotFound
		case "msg2":
			return &models.Email{ExternalID: externalID}, nil
		}

		return nil, fmt.Errorf("unexpected ID: %s", externalID)
	}

	updateUser = defaultUpdateUser
	dbGetServiceFunc = defaultGetServiceByID

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	// Mock DB calls
	originalGetEmailFunc := getEmailFunc
	defer func() { getEmailFunc = originalGetEmailFunc }()
	getEmailFunc = func(_ context.Context, externalID string) (*models.Email, error) {
		if externalID == "msg2" {
			// This one is a duplicate
			return &models.Email{Model: gorm.Model{ID: 123}}, nil
		}
		// This one is not
		return nil, gorm.ErrRecordNotFound
	}

	// Mock Redis for deduplication check
	redisClient, redisMock := redismock.NewClientMock()
	redisMock.ExpectGet("email-external-id-msg1").SetErr(redis.NilEntry)
	redisMock.ExpectGet("email-external-id-msg2").SetVal("msg2")
	redis.RDB = redisClient
	defer func() { redis.RDB = nil }()

	// First event for a new message
	event1Body := `{"value":[{"subscriptionId":"test-subscription","changeType":"created",` +
		`"resource":"Users/OutlookUser1/Messages/msg1","resourceData":{"id":"msg1"}}]}`
	event1 := events.LambdaFunctionURLRequest{
		Body: event1Body,
	}
	// Second event for a duplicate message
	event2Body := `{"value":[{"subscriptionId":"test-subscription",` +
		`"resource":"Users/OutlookUser1/Messages/msg2","resourceData":{"id":"msg2"}}]}`
	event2 := events.LambdaFunctionURLRequest{
		Body: event2Body,
	}

	ctx := context.Background()
	// Process the first message
	result := handler(ctx, event1)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Process the second (duplicate) message
	result = handler(ctx, event2)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// -- Assert --
	// 1. Verify only the first message was sent to S3 and SQS
	assert.Len(t, mockS3.OutlookMessages, 1)
	assert.Contains(t, mockS3.OutlookMessages, "msg1")
	processedMessages := mockSQS.sentMessagesByQueue[env.Vars.SQSQueueURL]
	assert.Len(t, processedMessages, 1)

	// 2. Verify Redis was called
	assert.Nil(t, redisMock.ExpectationsWereMet())
}

// Test helpers
func TestExtractUserID(t *testing.T) {
	t.Run("OK", func(t *testing.T) {
		//nolint:lll
		res, err := extractUserID("Users/b5346c76-d251-4e32-b72e-b4bd003e9f88/Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA")
		require.NoError(t, err)
		assert.Equal(t, "b5346c76-d251-4e32-b72e-b4bd003e9f88", res)
	})

	t.Run("Error expected", func(t *testing.T) {
		res, err := extractUserID("Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA")
		require.Error(t, err)
		assert.Empty(t, res)
	})

	t.Run("Switched order", func(t *testing.T) {
		//nolint:lll
		res, err := extractUserID("Messages/AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0BJ6gAA/Users/<USER>")
		require.NoError(t, err)
		assert.Equal(t, "b5346c76-d251-4e32-b72e-b4bd003e9f88", res)
	})
}

func TestHandlerValidation(t *testing.T) {
	// -- Arrange --
	validationToken := "a-validation-token"
	event := events.LambdaFunctionURLRequest{
		QueryStringParameters: map[string]string{"validationToken": validationToken},
	}
	ctx := context.Background()

	// -- Act --
	result := handler(ctx, event)

	// -- Assert --
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.Equal(t, "text/plain", result.Headers["Content-Type"])
	assert.Equal(t, validationToken, result.Body)
}
