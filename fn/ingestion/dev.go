package ingestion

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// For dev; replay emails from S3
// `out` should be either *gmail.Message or *msclient.Message
func DownloadFromS3(ctx context.Context, key string, out any) error {
	if out == nil {
		return errors.New("you want me to get a message from S3, but you didn't provide an `out` param :(")
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return fmt.Errorf("failed to create S3 client: %w", err)
	}
	s3Client := s3.NewFromConfig(cfg)

	const bucket = "axle-beacon-ingestion"
	log.Info(ctx, "downloading from s3", zap.String("bucket", bucket), zap.String("key", key))
	resp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("failed to GetObject from s3: %w", err)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read S3 response body: %w", err)
	}

	if err := json.Unmarshal(data, out); err != nil {
		return fmt.Errorf("json.Unmarshal into out failed: %w", err)
	}

	return nil
}
