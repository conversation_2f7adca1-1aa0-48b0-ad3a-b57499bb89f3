package main

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/gmail/v1"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	mem "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

// The msg data is base64-encoded: `{"emailAddress":"<EMAIL>","historyId":1045507}`
const whTestBody = `{
    "message": {
        "data": "****************************************************************************",
        "messageId": "9431319009453080",
        "publishTime": "2023-10-03T19:34:46.792Z"
    },
	"subscription": "projects/drumkit-397017/subscriptions/LambdaSub"
}`

var (
	defaultGetEmailFunc = func(context.Context, string) (*models.Email, error) {
		return nil, gorm.ErrRecordNotFound
	}
)

type mockSQSClient struct {
	sentMessagesByQueue map[string][]*sqs.SendMessageInput
}

func (c *mockSQSClient) SendMessage(
	_ context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	if c.sentMessagesByQueue == nil {
		c.sentMessagesByQueue = make(map[string][]*sqs.SendMessageInput)
	}
	queueURL := *input.QueueUrl
	c.sentMessagesByQueue[queueURL] = append(c.sentMessagesByQueue[queueURL], input)

	return &sqs.SendMessageOutput{
		MessageId: aws.String("mock-message-id"),
	}, nil
}

func TestHandleInboxWebhookNoMessages(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"

	memService := &mem.Service{
		Messages: make(map[string]*gmail.Message),
	}

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 123456, ServiceID: 555}, nil
	}

	dbGetServiceFunc = func(context.Context, uint) (models.Service, error) {
		return models.Service{}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.Equal(t, []string{"mem.WatchInbox()", "mem.ListHistory(123456)"}, memService.Calls)
}

func TestHandleInboxWebhookInvalidHistoryID(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {
				Id: "alpha",
				Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
			"beta": {
				Id: "beta",
				Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
		},
	}

	getEmailFunc = defaultGetEmailFunc
	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 9999999}, nil
	}
	dbGetServiceFunc = func(_ context.Context, _ uint) (models.Service, error) {
		return models.Service{}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		"mem.ListHistory(9999999)",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify NO messages sent to S3/SQS
	assert.Empty(t, mockS3.GmailMessages)
	assert.Empty(t, mockSQS.sentMessagesByQueue)
}

func TestHandleInboxWebhookNewUser(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.SQSQueueURL = "processor-queue"

	// For a new user with GmailLastHistoryID == 0, we expect early return
	// without processing any messages, just storing the historyId for future webhooks
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"9431319009453080": {
				Id: "9431319009453080",
				Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
		},
	}

	// This user has no last history ID, which will trigger the early return path
	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{Model: gorm.Model{ID: 123}, EmailAddress: emailAddr, GmailLastHistoryID: 0}, nil
	}

	dbGetServiceFunc = func(context.Context, uint) (models.Service, error) {
		return models.Service{}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	mockSQS := &mockSQSClient{}
	sqsClient = mockSQS

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)
	assert.Equal(t, "no lastHistoryID stored in DB yet, waiting for next webhook", result.Body)

	// Should only call WatchInbox for resubscription, no ListHistory or BatchGetMessages
	expected := []string{"mem.WatchInbox()"}
	assert.Equal(t, expected, memService.Calls)

	// Verify NO messages sent to S3/SQS since we returned early
	assert.Empty(t, mockS3.GmailMessages)
	assert.Empty(t, mockSQS.sentMessagesByQueue)
}

func TestHandleInboxWebhookDuplicateEmails(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.SkipDuplicates = true
	const historyID = 123456789

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {
				Id: "alpha", Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
			"beta": {
				Id: "beta", Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
		},
	}

	getEmailFunc = func(_ context.Context, externalID string) (*models.Email, error) {
		switch externalID {
		case "alpha":
			return nil, gorm.ErrRecordNotFound
		case "beta":
			return &models.Email{ExternalID: externalID}, nil
		}

		return nil, fmt.Errorf("unexpected ID: %s", externalID)
	}

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: historyID}, nil
	}

	dbGetServiceFunc = func(context.Context, uint) (models.Service, error) {
		return models.Service{}, nil
	}

	gmailConstructor = func(
		_ context.Context,
		_,
		_ string,
		_ models.UserAccessor,
		_ ...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	// Mock Redis for deduplication check
	redisClient, redisMock := redismock.NewClientMock()
	redisMock.ExpectGet("email-external-id-msg1").SetErr(redis.NilEntry)
	redisMock.ExpectGet("email-external-id-msg2").SetVal("msg2")
	redis.RDB = redisClient
	defer func() { redis.RDB = nil }()

	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handleInboxWebhook(ctx, event)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		fmt.Sprintf("mem.ListHistory(%d)", historyID),
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessagesByQueue[env.Vars.SQSQueueURL], 1)
}

func TestHandleInboxWebhook(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"
	env.Vars.SkipDuplicates = true

	// Replace RDS functions
	getEmailFunc = defaultGetEmailFunc

	getUserByEmail = func(_ context.Context, emailAddr string) (user models.User, err error) {
		assert.Equal(t, "<EMAIL>", emailAddr)
		return models.User{EmailAddress: emailAddr, GmailLastHistoryID: 123456}, nil
	}
	dbUpdateUserFunc = func(_ context.Context, user models.User) error {
		// We should be updating the history ID
		assert.Equal(t, uint64(1045507), user.GmailLastHistoryID)
		return nil
	}
	dbGetServiceFunc = func(context.Context, uint) (models.Service, error) {
		return models.Service{}, nil
	}

	// Replace Gmail client with in-memory mock implementation
	memService := &mem.Service{
		Messages: map[string]*gmail.Message{
			"alpha": {
				Id: "alpha", Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
			"beta": {
				Id: "beta", Payload: &gmail.MessagePart{
					Headers: []*gmail.MessagePartHeader{{Name: "Subject", Value: "test"}},
				},
			},
		},
	}

	gmailConstructor = func(
		context.Context,
		string, string,
		models.UserAccessor,
		...oauth.Option,
	) (gmailclient.Client, error) {
		return memService, nil
	}

	// Replace AWS clients with in-memory mock implementations
	mockS3 := s3backup.NewMock()
	s3Uploader = mockS3
	var mockSQS mockSQSClient
	sqsClient = &mockSQS

	// Mock Redis for deduplication check
	redisClient, redisMock := redismock.NewClientMock()
	redisMock.ExpectGet("email-external-id-alpha").SetErr(redis.NilEntry)
	redisMock.ExpectGet("email-external-id-beta").SetErr(redis.NilEntry)
	redis.RDB = redisClient
	defer func() { redis.RDB = nil }()

	// invoke the main handler
	event := events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		RawPath: "/inboxWebhook",
		Headers: map[string]string{"from": "<EMAIL>"},
	}

	ctx := context.Background()
	result, err := handler(ctx, event)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, result.StatusCode)

	// Verify calls to the Gmail SDK
	expected := []string{
		"mem.WatchInbox()",
		"mem.ListHistory(123456)",
		"mem.BatchGetMessages([alpha, beta])",
	}
	assert.Equal(t, expected, memService.Calls)

	// Verify messages sent to S3/SQS
	assert.Equal(t, map[string]bool{"alpha": true, "beta": true}, mockS3.GmailMessages)
	assert.Len(t, mockSQS.sentMessagesByQueue[env.Vars.SQSQueueURL], 2)
	assert.Nil(t, redisMock.ExpectationsWereMet())
}

func TestParseWebhook(t *testing.T) {
	env.Vars.GmailSubscriptionID = "projects/drumkit-397017/subscriptions/LambdaSub"

	whBody, msgData, err := parseWebhook(context.Background(), events.LambdaFunctionURLRequest{
		Body:    whTestBody,
		Headers: map[string]string{"from": "<EMAIL>"},
	})
	require.NoError(t, err)

	assert.Equal(t, "9431319009453080", whBody.Message.MessageID)
	assert.Equal(t, "projects/drumkit-397017/subscriptions/LambdaSub", whBody.Subscription)
	assert.Equal(t, "<EMAIL>", msgData.EmailAddress)
	assert.Equal(t, uint64(1045507), msgData.HistoryID)
}
