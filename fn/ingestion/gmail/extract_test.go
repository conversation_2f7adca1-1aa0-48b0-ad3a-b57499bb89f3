package main

import (
	"context"
	"encoding/base64"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/models"
)

func TestSQSPayload(t *testing.T) {
	t.<PERSON>()

	now := time.Now()

	gmailMsg := &gmail.Message{
		Id:           "msg-f:1775132995676831833",
		InternalDate: now.UnixMilli(),
		Payload: &gmail.MessagePart{
			Headers: []*gmail.MessagePartHeader{
				{Name: "Message-ID", Value: "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>"},
				{Name: "From", Value: "Austin Byers <<EMAIL>>"},
				{Name: "To", Value: "<EMAIL>, aks<PERSON><EMAIL>, <EMAIL>"},
				{Name: "Cc", Value: "Sophie1142 <<EMAIL>>, Review <<EMAIL>>"},
				{Name: "In-Reply-To", Value: "<drumkitai/parthenon/pull/<EMAIL>>"},
				{Name: "References", Value: "<drumkitai/drumkit/pull/<EMAIL>>"},
				{Name: "Subject", Value: "Re: [drumkitai/parthenon] Add drumkit-specific SENTRY_DSN env vars (PR #91)"},
			},
			MimeType: "multipart/alternative",
			Parts: []*gmail.MessagePart{
				{
					Body: &gmail.MessagePartBody{
						Data: base64.URLEncoding.EncodeToString([]byte("<html>Hello world</html>")),
					},
					MimeType: "text/html",
				},
				{
					Body: &gmail.MessagePartBody{
						Data: base64.URLEncoding.EncodeToString([]byte("This should be ignored")),
					},
					MimeType: "text/plain",
				},
			},
		},
		ThreadId: "thread-f:1775120948217100423",
	}

	user := models.User{
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	user.ID = 1

	result, err := emails.PrepareEmailPayload(
		context.Background(),
		&emails.GmailMessage{Message: gmailMsg},
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithUser(&user),
		emails.WithS3URL("s3://bucket/key.json"),
	)

	require.NoError(t, err)

	expected := &models.IngestedEmail{
		Account:   "<EMAIL>",
		UserID:    1,
		ServiceID: 1,

		RFCMessageID: "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>",
		ExternalID:   "msg-f:1775132995676831833",
		ThreadID:     "thread-f:1775120948217100423",
		SentAt:       time.UnixMilli(now.UnixMilli()),

		ThreadReferences: "<drumkitai/drumkit/pull/<EMAIL>>",
		InReplyTo:        "<drumkitai/parthenon/pull/<EMAIL>>",

		Sender:     "<EMAIL>",
		Recipients: "<EMAIL>, <EMAIL>, <EMAIL>",
		CC:         "<EMAIL>, <EMAIL>",

		Subject:  "Re: [drumkitai/parthenon] Add drumkit-specific SENTRY_DSN env vars (PR #91)",
		Body:     "Hello world",
		BodyType: models.MarkdownEmailBodyType,

		S3URL: "s3://bucket/key.json",
	}

	assert.Equal(t, expected, result)
}

func TestSQSPayloadSpecialChars(t *testing.T) {
	t.Parallel()

	gmailMsg := &gmail.Message{
		Payload: &gmail.MessagePart{
			Headers: []*gmail.MessagePartHeader{
				{Name: "From", Value: "First Last \<EMAIL>\u003e"},
				{Name: "To", Value: "Undisclosed recipients:"},
			},
		},
	}

	user := models.User{
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	user.ID = 1

	result, err := emails.PrepareEmailPayload(
		context.Background(),
		&emails.GmailMessage{Message: gmailMsg},
		emails.WithUser(&user),
	)

	require.NoError(t, err)

	assert.Equal(t, "<EMAIL>", result.Sender)
	assert.Equal(t, "Undisclosed recipients:", result.Recipients)
}

func TestSQSPayloadHTMLOnly(t *testing.T) {
	t.Parallel()

	gmailMsg := &gmail.Message{
		Payload: &gmail.MessagePart{
			MimeType: "multipart/alternative",
			Parts: []*gmail.MessagePart{
				{
					Body: &gmail.MessagePartBody{
						Data: base64.URLEncoding.EncodeToString([]byte("<html><b>Hello</b> world</html>")),
					},
					MimeType: "text/html",
				},
			},
		},
	}

	user := models.User{
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	user.ID = 1

	result, err := emails.PrepareEmailPayload(
		context.Background(),
		&emails.GmailMessage{Message: gmailMsg},
		emails.WithUser(&user),
	)

	assert.Nil(t, err)
	assert.Equal(t, "**Hello** world", result.Body)
	assert.Equal(t, models.MarkdownEmailBodyType, result.BodyType)
}

func TestSQSPayloadNoTextParts(t *testing.T) {
	t.Parallel()

	gmailMsg := &gmail.Message{Payload: &gmail.MessagePart{MimeType: "multipart/alternative"}}

	user := models.User{
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	user.ID = 1

	result, err := emails.PrepareEmailPayload(
		context.Background(),
		&emails.GmailMessage{Message: gmailMsg},
		emails.WithUser(&user),
	)

	assert.Nil(t, err)
	assert.Empty(t, result.Body)
}

func TestSQSPayloadTruncatedBody(t *testing.T) {
	t.Parallel()

	// 500KB msg
	msg := make([]byte, 500000)
	for i := range msg {
		msg[i] = byte('a' + (i % 26))
	}

	gmailMsg := &gmail.Message{
		Payload: &gmail.MessagePart{
			Headers: []*gmail.MessagePartHeader{
				{Name: "To", Value: "<EMAIL>"},
			},
			MimeType: "text/plain",
			Body: &gmail.MessagePartBody{
				Data: base64.URLEncoding.EncodeToString(msg),
			},
		},
	}

	user := models.User{
		EmailAddress: "<EMAIL>",
		ServiceID:    1,
	}
	user.ID = 1

	result, err := emails.PrepareEmailPayload(
		context.Background(),
		&emails.GmailMessage{Message: gmailMsg},
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithUser(&user),
		emails.WithS3URL("some-test-s3-url"),
	)

	assert.Nil(t, err)

	assert.True(t, result.Truncated)
	assert.Len(t, result.Body, 200000)
}
