# Gmail Ingestion Lambda

A lambda function for getting emails via Google Cloud Pub/Sub push notifications and the Gmail API's Go SDK.

# Gmail Webhook Logic

## History ID

Gmail’s webhook system does not send us new emails directly. Instead, each webhook includes a `historyId` representing the current state of the user’s inbox. To fetch new emails, we:

1. **Store the last processed `historyId`** in our database for each user.
2. **On webhook receipt:**
   - Look up the user’s `lastHistoryId` in the DB.
   - Use the Gmail API to fetch all new emails since that `historyId`.
   - For each new email, call `ProcessAndEnqueueGmailMessage` to process and enqueue it for downstream handling.
   - Update the user’s `lastHistoryId` in the DB with the new value from the webhook.

If the `historyId` is `0`, it means the user just onboarded. Backfilling emails is handled by [`backfill`](../../backfill/main.go)
so upon receiving the first webhook, this lambda just saves it to the DB and waits for the next webhook.

### Resources

- https://developers.google.com/workspace/gmail/api/guides/push

## Onboarding Backfill

When a user first onboards to Drumkit, the API sends a backfill job to the [backfill worker](../../backfill/main.go)

### Flowchart: Gmail Onboarding & Webhook Flow

<details>
<summary> Mermaid diagram code </summary>

```mermaid
flowchart TD
    A[User Onboarding] --> B[Store historyId in DB]
    B --> C[API sends backfill job to backfill worker]
    C --> D[Backfill worker processes 14 days of emails]
    B --> E[Wait for Gmail webhook]
    E --> F[Webhook received with new historyId]
    F --> G[Look up lastHistoryId in DB]
    G --> H[Fetch new emails since lastHistoryId]
    H --> I[Process each email with ProcessAndEnqueueGmailMessage]
    I --> J[Update lastHistoryId in DB]
    J --> E
    G -- If lastHistoryId is 0 --> K[User just onboarded, save new historyId, wait for next webhook]
    K --> E
```

</details>

![](./assets/Onboarding%20&%20Webhook%20Flowchart.png)

## Deduplication

Because Gmail requires that we fetch all emails since the last known history ID, it's important to avoid unnecessary duplicate processing.

We deduplicate emails both at the email ingestion and processor layer if `env.Vars.SkipDuplicates = true` (the default setting). When an email is successfully enqueued for Processor, [it's stored in Redis](./main.go:494). During future webhooks, the lambda checks Redis (and RDS as a fallback) and skips the message if it's already in the Drumkit processing pipeline.

## Retries

If the lambda does not respond to the webhook with a OK 200 status, Google will retry based on the parameters specified in our [Pub/Sub configuration on Google Cloud Console](https://console.cloud.google.com/cloudpubsub/subscription/edit/LambdaSub?project=beacon-397017&inv=1&invt=Ab3iZA). This is where deduplication via Redis/RDS is particularly important because before, if there was an issue that caused a backlog of Gmail webhooks, then upon fixing the original issue our system was slammed with all the old historyId webhooks and duplicate emails ([Notion](https://www.notion.so/drumkitai/RDS-overwhelmed-due-to-multiple-email-upserts-1ea2b16b087a80be8f89d6b2aa593395?source=copy_link#1eb2b16b087a805aa094ec540bea35fa)). Now with [deduplication strategy](https://github.com/drumkitai/drumkit/pull/1793), even if there is a backlog of Gmail webhooks, only new unique emails will be enqueued to Processor.

![PubSub Retry Policy](./assets/GCP%20PubSub%20Retry%20Policy.png)

![GCP PubSub Message Retention](./assets/GCP%20PubSub%20Message%20Retention.png)

### Resources

- https://cloud.google.com/pubsub/docs/handling-failures
- [Lambda Subscription GCP Configuration](https://console.cloud.google.com/cloudpubsub/subscription/edit/LambdaSub?project=beacon-397017&inv=1&invt=Ab3iZA)

# Development

### Testing Pub/Sub Subscription

Google [Pub/Sub](https://developers.google.com/gmail/api/guides/push) is Google's webhook notification service. We use this service to notify Drumkit of when there have been changes to the user's inbox that Drumkit needs to process.

1. Ensure that the user's inbox you'd like to watch is in the Drumkit RDS (<EMAIL> is already registered).
2. Send a [`/watchInbox`](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion?tab=testing) request at least once every 7 days to subscribe the user's inbox to the Pub/Sub topic (see `ExampleWatchInbox` test event in Lambda console).
3. Observe logs for when Pub/Sub sends push notifications to the Lambda URL every time the user's inbox changes (additions and/or deletions).

### Testing Without Pub/Sub Subscription

Additional fields have been provided in the Lambda request body so that developers can directly invoke the Ingestion Lambda to call the Gmail API without waiting for a Pub/Sub notification.

1. Ensure that the user is in the Drumkit RDS (<EMAIL> is already registered).
2. Invoke ['/inboxWebhook'] endpoint. See `ExampleWebhookNotif` test event in Lambda console for an example. Note how similar to the actual Pub/Sub payload body, the `message.data` field must be a base64 URL-encoded JSON object containing the desired user's `emailAddress`. History ID can be left empty. See additional fields you can provide for direct developer invocations in [main.go](./main.go)

```
{
  "rawPath": "/inboxWebhook",
  "headers": {
    "from": "<EMAIL>"
  },
  "body": "{\"message\":{\"data\":\"****************************************************\",\"messageId\":\"msg-f:1774971923102135034\",\"useMessageHistoryID\":false},\"subscription\":\"projects/beacon-397017/subscriptions/LambdaSub\"}"
}
```

### Backfills

When a new user signs-up, their first email webhook will trigger a 14-day backfill of their emails. If you need to manually trigger a backfill (for example, due to the Great Aljex IP Address Incident of 2024), do the following:

1. (Optional) If you need to trigger re-processing of messages already in the DB (such as, if there was a bug associating loads with emails or generating AI suggestions), set the `SKIP_DUPLICATES_SUGGESTIONS=false` in the [Gmail ingestion](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion-outlook?tab=code) AND [Processor](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-processor?tab=code) lambdas (Lambda console -> beacon-ingestion-outlook/beacon-processor -> Configuration tab -> Environment variables).
   - **Context:** As an optimization, for some AI suggestions (like load building) we don't create multiple suggestions for a thread if one already exists as users may continue to use the same thread for all communications related to that shipment. But there are cases where we need to backfill/correct an existing suggestion, hence the `SKIP_DUPLICATES_SUGGESTIONS=false` flag.
1. In the DB, identify the target user and record their `gmail_history_id` somewhere.
1. Set their `gmail_history_id` to 0 (use a SQL transaction! Postico (and Tableplus?) automatically performs updates in a transactions). This is the condition Gmail ingestion Lambda checks to know it should backfill.
1. Trigger backfill by either:
   1. Waiting for the Gmail ingestion Lambda to receive a webhook for that user. When complete, the `gmail_history_id` will no longer be 0.
   1. Another option is to manually trigger ingestion by replaying the last webhook associated with that historyID. Search the ingestion logs for that historyID and you'll find the webhook body, then use Lambda console's Test feature or Postman to re-POST that webhook. As the first option, the backfill is complete when the history ID no longer equals 0.
1. If you did step 1, be sure to reset `SKIP_DUPLICATES_SUGGESTIONS=true` when the backfill is complete.

# Google Cloud Infrastructure

Any app using Google resources must create a Google Cloud Project and configure the Google APIs and scopes the app needs to access.

## Setup

1. Follow the instructions [here](https://cloud.google.com/resource-manager/docs/creating-managing-projects?ref_topic=6158848&visit_id=637463496589315265-3245443433&rd=1#creating_a_project) to create a Google Cloud Project.

   a. Our Existing GCP: https://console.cloud.google.com/home/<USER>

2. To enable the Gmail API for the app, follow the instructions under [Activate an API in a standard Cloud project](https://developers.google.com/apps-script/guides/cloud-platform-projects#enabling_an_api_in_a_standard_gcp_project).
3. To enable inbox webhook notifications for the add-on project, follow the instructions [here](https://developers.google.com/gmail/api/guides/push) for creating a Pub/Sub topic.

   a. Existing Topic & Subscriptions: https://console.cloud.google.com/cloudpubsub/topic/list?project=beacon-demo-395521

4. To [publish and verify the app](https://support.google.com/cloud/answer/10311615#publishing-status&zippy=), follow the steps in the [Oauth Consent Screen](https://console.cloud.google.com/apis/credentials/consent?project=beacon-397017) of the GCP.

## Common GCP Operations

### 1. Purging Pub/Sub Messages

**⚠️ This should rarely be done and only as a last, last resort. ⚠️**
Google continually retries messages if it receives a non-2xx responses (despite the fact that we set a 15-second maximum backoff ¯\_(ツ)\_/¯). If we no longer need to process these old messages, to go the [Drumkit GCP -> Pub/Sub -> select `GmailInboxPush` -> select the `LambdaSub` subscription -> click "Purge Messages"](https://console.cloud.google.com/cloudpubsub/subscription/detail/LambdaSub?project=beacon-397017).

### 2. Configuring Authorized Domains

We use this to configure authorized customer domains, frontend/backend services, etc. To change these settings, go to the [Drumkit GCP -> Oauth consent screen -> Edit app -> Scroll down to 'Authorized Domains'](https://console.cloud.google.com/apis/credentials/consent/edit?project=beacon-397017)

### 3. Viewing Client Credentials

Our app needs these credentials in order to access Google resources. As such, **admin access to the Google Cloud Project should be limited to authorized engineers as they contain our app's credentials**. [Here](https://console.cloud.google.com/apis/credentials?orgonly=true&project=beacon-397017&supportedpurview=organizationId) is the page for viewing and downloading the Gmail API credentials Drumkit needs for those authorized.

## Observability

In addition to our Cloudwatch Dashboards, Google Cloud Console also provides metrics & dashboards on our app. For example, you can view number and age of unacknowledged PubSub (aka webhook) notifications.

![](./assets/GCP%20PubSub%20Metrics.png)
