# Backtest Beacon with a list of emails in S3 and their expected classification / extracted metadata

emails:
  - s3_key: gmail/<EMAIL>/18ab4b5b09490564.json
    expected_db_vals:
      rfc_message_id: "<CAKLDJyAAG8HWvtfHAApDjFb3f=m29ius=6qfBQ=<EMAIL>>"
      subject: "Bradshaw Delivery Apt Request PRO 2852657"
      labels: "appointment scheduling"
      freight_tracking_id: "2852657"

  - s3_key: gmail/<EMAIL>/18b1a8e5230560ba.json
    expected_db_vals:
      rfc_message_id: "<CAP4mXj_8jPQCrg+W=<EMAIL>>"
      subject: "P/U REQUEST / PO 3128385 (NFI 2083949)"
      labels: "appointment scheduling"
      freight_tracking_id: "2083949" # available with NFI prefix in subject

  - s3_key: gmail/<EMAIL>/18b1b9046aed69fc.json
    expected_db_vals:
      rfc_message_id: "<CAFH3-kos0+QXpLJgq1rFogvXLB4ajrQn706A=<EMAIL>>"
      subject: "Pick up 3124853"
      labels: "appointment scheduling"
      freight_tracking_id: "2084831"

  - s3_key: gmail/<EMAIL>/18af79c3de43501f.json
    expected_db_vals:
      rfc_message_id: "<CAPARPZYdDmWwwhAq=<EMAIL>>"
      subject: "Pickup Appt Request"
      labels: "appointment scheduling"
      freight_tracking_id: "2082184" # lives in a single-row table

  - s3_key: gmail/<EMAIL>/18b05b087b1127c3.json
    expected_db_vals:
      rfc_message_id: "<<EMAIL>>"
      subject: "[EXTERNAL] TENDER REQUEST    - Load #: 7110009916 - Organization Name:   7-11 Virginia"
      labels: "other"
      freight_tracking_id: ""

  - s3_key: gmail/<EMAIL>/18b1b25887cce003.json
    expected_db_vals:
      rfc_message_id: "<<EMAIL>>"
      subject: "[EXTERNAL] Wrong shipping location"
      labels: "other"
      freight_tracking_id: ""

  - s3_key: gmail/<EMAIL>/18b05dea69236b99.json
    expected_db_vals:
      rfc_message_id: "<<EMAIL>>"
      subject: "Re: [EXTERNAL] Re: PRO 2082975"
      freight_tracking_id: "2082975"

  - s3_key: gmail/<EMAIL>/18b070e4d28805c0.json
    expected_db_vals:
      rfc_message_id: "<<EMAIL>>"
      subject: "Re: [EXTERNAL] TENDER REQUEST - Load #: 7110009927 - Organization Name: 7-11 Virginia"
      labels: "other"
      freight_tracking_id: "2083972" # not to be confused with PO# 3130324 in the same table row

  - s3_key: gmail/<EMAIL>/18d17c6ee5749fb0.json
    expected_db_vals:
      rfc_message_id: "<CAEzfNZfG5AJm=<EMAIL>>"
      subject: "PRO 7928328, 7928266, 7928304"
      freight_tracking_ids: ["7928328", "7928266", "7928304"]
