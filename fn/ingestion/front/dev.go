package main

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/log"
)

const frontDevPort = ":5007"

func runLocalServer(ctx context.Context, app *fiber.App) <-chan error {
	errChan := make(chan error, 1)

	var localSQS sqsclient.LocalSQSClient
	sqsClient = &localSQS

	app.Use(middleware.Zap())

	app.Post("/inboxWebhook", fiberHandler)

	go func(ctx context.Context) {
		if err := app.Listen(frontDevPort); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Error(ctx, "fiber server failed", zap.Error(err))
			err<PERSON>han <- err
		}
	}(ctx)
	return err<PERSON>han
}

func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	var event frontclient.FrontEvent
	if err := c.BodyParser(&event); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	// Convert Fiber POST request into the Front payload expected by the app
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("invalid json body: " + err.Error())
	}

	eventParsed := events.LambdaFunctionURLRequest{
		Body:    string(eventJSON),
		RawPath: c.Path(),
	}

	log.Info(ctx, "sending event to beacon-ingestion-front handler", zap.Any("event", eventParsed))

	result := handler(ctx, eventParsed)

	return c.Status(result.StatusCode).SendString(result.Body)
}
