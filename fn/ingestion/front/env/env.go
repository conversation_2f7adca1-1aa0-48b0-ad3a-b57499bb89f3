package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars envVars

type envVars struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv string `envconfig:"APP_ENV" required:"true"`

	RedisURL       string `envconfig:"REDIS_URL"`
	TraceOn        bool   `envconfig:"TRACE_ON"`
	BackfillHours  int    `envconfig:"BACKFILL_HOURS" required:"true"`
	SkipDuplicates bool   `envconfig:"SKIP_DUPLICATES" default:"true"`

	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	S3BucketName string `envconfig:"S3_BUCKET_NAME" required:"true"`
	SQSQueueURL  string `envconfig:"SQS_QUEUE_URL" required:"true"`

	// For prod only
	SecretARN string `envconfig:"SECRET_ARN"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey string `json:"AES_KEY"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file found")
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.RedisURL == "" {
			log.WarnNoSentry(ctx, "missing Redis URL")
		}

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.SecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" {
			return fmt.Errorf("%s is missing some fields", Vars.SecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)

	}

	return nil
}
