# Front Ingestion Lambda

A lambda function for getting emails via Front webhooks and our native [Front SDK](../../../common/integrations/email/frontclient/front.go).

# Testing Webhooks

## Receive Actual Webhooks from Front Locally

Helpful for testing end-to-end processing flows, particularly categorization and AI parsing.

1. Open a terminal in this directory and run `go run .`.
1. In a separate terminal, run `ngrok http 5007` (the port that Front ingestion runs on in dev).
   1. For more information on ngrok, see [here](https://ngrok.com/docs/getting-started/)
1. ngrok returns a public URL assigned to this port. In the [API's env file](../../api/.env)
1. Run the API, Processor, Portal locally.
1. Create a new service in your local Drumkit DB and fill in:
   1. front_auth_token with the API Token obtained from Front (Settings -> Developers -> API Tokens)
   1. front_tenant_subdomain with the subdomain from the Front account (e.g. `drumkit-ai`.api.frontapp...)
1. Create a rule within Front to send messages to the ngrok endpoint with `/inboxWebhook` appended

## Working with Shared Inboxes
Due to Front's nature of shared inboxes, we can have emails that don't include any individual human users (the ones that Login through Drumkit) in the recipient list - since they were forward to Front by inboxes created for different pipeline (operations, dispatch, customer success etc).

What we now do is validate emails addresses from the recipient list and create a separate email record of each of them. Valid email addresses include:
- Individual email addresses from users e.g. <EMAIL>
- Emails from the same domain as service (@drumkit.ai) that are use as an inbound channel in Front
