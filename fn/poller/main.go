package main

import (
	"context"
	"encoding/json"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/grafana/pyroscope-go"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/graceful"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/poller/env"
	customEvents "github.com/drumkitai/drumkit/fn/poller/events"
)

const (
	serviceName    = "poller" // Name of the service for tracing.
	serviceVersion = "0.0.1"  // Version of the service.

	pollerLockKey = "poller:running"
	// Lock either expires after 10 minutes or gets cleared when poller finishes execution
	pollerLockTTL = 10 * time.Minute
)

// Poller is triggered every 10 minutes so we search for loads that have been added in the last 30 minutes as buffer
var defaultFromDuration = -30 * time.Minute

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	ctx := context.Background()
	// Add isPolling flag to context to indicate this is a polling operation
	ctx = context.WithValue(ctx, models.IsPollingKey, true)

	if envErr := env.Load(ctx); envErr != nil {
		panic(envErr)
	}

	shutdownHandler, shutdownCompleteChan := graceful.SetupGracefulShutdown(ctx, "drumkit-poller")

	_, err := pyroscope.Start(pyroscope.Config{
		ApplicationName:   "poller-" + os.Getenv("APP_ENV"),
		ServerAddress:     "https://profiles-prod-001.grafana.net",
		BasicAuthUser:     "1212715",
		BasicAuthPassword: os.Getenv("PYROSCOPE_PASSWORD"),
		Logger:            nil,

		Tags: map[string]string{"hostname": os.Getenv("HOSTNAME")},

		ProfileTypes: []pyroscope.ProfileType{
			// Default profile types:
			pyroscope.ProfileCPU,
			pyroscope.ProfileAllocObjects,
			pyroscope.ProfileAllocSpace,
			pyroscope.ProfileInuseObjects,
			pyroscope.ProfileInuseSpace,

			// Optional profile types:
			pyroscope.ProfileGoroutines,
			pyroscope.ProfileMutexCount,
			pyroscope.ProfileMutexDuration,
			pyroscope.ProfileBlockCount,
			pyroscope.ProfileBlockDuration,
		},
	})
	if err != nil {
		log.Error(ctx, "Error starting pyroscope", zap.Error(err))
	}

	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	ctx = log.NewFromEnv(ctx)
	log.Debug(ctx, "IN DEBUG MODE")

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-poller"),
	); err != nil {
		panic(err)
	}

	// Require Redis for poller so we can guarantee self-imposed rate limits
	if env.Vars.RedisURL == "" {
		log.Error(ctx, "redis url is not set, exiting")
		return
	}

	if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
		panic(err)
	}

	acquired, err := redis.SetIfNotExists(ctx, pollerLockKey, "running", pollerLockTTL)
	if err != nil {
		log.Error(ctx, "failed to check poller lock", zap.Error(err))
		panic(err)
	}

	if !acquired {
		log.Info(ctx, "another poller instance is already running, exiting")
		return
	}

	// Release the lock after execution
	defer func() {
		err := redis.DeleteKey(ctx, pollerLockKey)
		if err != nil {
			log.Warn(ctx, "error releasing poller lock", zap.Error(err))
		}
	}()

	if env.Vars.AppEnv == "dev" {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.WarnNoSentry(ctx, "Error running migrations", zap.Error(err))
		}
		ctx, cancel := context.WithCancel(ctx)
		shutdownHandler.AddCleanupFunc(func(_ context.Context) error {
			cancel()
			return nil
		})

		go localHandler(ctx)
		// Block, waiting for the shutdown to complete.
		for {
			select {
			case <-ctx.Done():
				log.Info(ctx, "context cancelled, exiting.")
				return
			case err := <-shutdownCompleteChan:
				if err != nil {
					log.Error(ctx, "graceful shutdown failed", zap.Error(err))
				} else {
					log.Info(ctx, "graceful shutdown successful, exiting application.")
				}
				return
			}
		}
	}

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

func handlerWithLogging(ctx context.Context, event events.SQSEvent) (err error) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		ctx = log.NewFromEnv(ctx)

		hub := sentry.GetHubFromContext(ctx)

		// If there is an error, flush it to Sentry before the Lambda invocation is finished.
		defer hub.Flush(sentry.FlushTimeout)

		// Log Lambda result and report errors to Sentry
		defer func() {
			if err != nil {
				log.Error(ctx, "returning error", zap.Error(err))
			}
		}()

		// Check and handle custom poller event
		// If there is exactly one record, poller is likely triggered from a custom test event
		if len(event.Records) == 1 {
			isCustomPollerEvent, handleErr := handleCustomPollerEvent(ctx, event.Records[0].Body)
			if isCustomPollerEvent {
				// If the custom poller event was handled successfully, return early without running regular poller
				if handleErr == nil {
					return
				}
				// Continue to normal polling
				log.Error(ctx, "custom poller event error", zap.Error(handleErr))
				err = handleErr

				return
			}
		}

		if len(event.Records) > 0 {
			sanitizedRecords := sqsclient.SanitizeSQSRecords(&event)
			log.Info(ctx, "received event", zap.Any("event", map[string]any{"Records": sanitizedRecords}))
		} else {
			log.Info(ctx, "received scheduled event from EventBridge")
		}

		// Always run the regular poller regardless of event type
		err = handler(ctx)
	})

	return
}

// handleCustomPollerEvent attempts to unmarshal and validate a custom backfill event from the given body string.
// Returns (true, error) if it was a custom backfill event (error is non-nil if handling failed),
// or (false, nil) if not a custom backfill event.
func handleCustomPollerEvent(ctx context.Context, body string) (isCustomPollerEvent bool, err error) {
	var backfillEvent customEvents.BackfillTMSLoadsEvent
	if unmarshalErr := json.Unmarshal([]byte(body), &backfillEvent); unmarshalErr != nil {
		log.Info(
			ctx,
			"couldn't unmarshal as backfill event, continuing with normal polling",
			zap.Error(unmarshalErr),
		)
		return false, nil
	}

	if validateErr := backfillEvent.Validate(); validateErr != nil {
		log.Info(
			ctx,
			"not a valid backfill event, continuing with normal polling",
			zap.Error(validateErr),
		)
		return false, nil
	}

	backfillErr := handleBackfillEvent(ctx, backfillEvent)

	return true, backfillErr
}
