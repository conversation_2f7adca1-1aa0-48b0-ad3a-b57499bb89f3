# Poller Lambda

A lambda function for polling TMS integrations for loads every 10 minutes. Having our own poller enables:

1. Faster lookups in RDS vs. potentially slow TMS APIs (e.g. Aljex, Relay)
2. Enables advanced search for all TMS's, even those that don't have native search API (e.g. Ascend)

## Backfilling Loads

- In [pollByTime](poll_by_time.go), if there are no loads in the DB, the function will backfill `os.Getenv("BACKFILL_HOURS")` hours worth of loads from the TMS (default = 6 hours).
- [pollByIncrementingExternalTMSID](poll_by_incrementing_id.go) and [pollByList](poll_by_list.go) currently do not support backfilling because they don't have native search APIs and rely on the latest load ID to poll. If you'd like to backfill for TMSes such as Aljex, Relay, or Ascend:
  - Add a record in the `loads` table from which you'd like to backfill, either by manually inserting a row or ingesting an email with the desired load ID.

### Using Custom Backfill Events

The poller Lambda also supports triggering a backfill via a lambda test event with a JSON payload. This is useful for one-time backfills or testing. To use this feature:

1. Navigate to the Lambda function in the AWS Console
2. Click "Test" and create a new test event with the following JSON format (modeled to mock SQS Event):

```json
{
  "Records": [
    {
      "body": "{\"type\":\"backfill-tms-loads\",\"integration_id\":5,\"backfill_hours\":24,\"start_time\":\"2025-04-10T00:00:00Z\"}"
    }
  ]
}
```

Note: json body must be kept as 1 line string.

Where:

- `type` must be "backfill-tms-loads"
- `integration_id` is the ID of the TMS integration to backfill (see integrations table in rds)
- `backfill_hours` is the number of hours to backfill (must be greater than 0)
- `start_time` (optional) is an ISO-8601 formatted timestamp to start the backfill from. If not provided, backfill will start from current time.

When this payload is read we will trigger a forced backfill for the specified number of hours.
This force backfill method only works for TMS integrations that support time-based polling
(currently McLeod Enterprise and Turvo).

Note: After successfully handling a backfill event, the poller will return early and skip its normal 10-minute polling cycle.

> ⚠️ Be mindful of rate limits when backfilling! Polling loads from TMSes can be resource-intensive, especially for TMSes that don't have native search APIs.

### Testing/Running Custom Backfill Locally

To run the custom backfill functionality locally, you **must** set all three environment variables:

1. `TEST_BACKFILL=true` - Enables the custom backfill test
2. `BACKFILL_INTEGRATION_ID=X` - The specific integration ID to backfill (required)
3. `BACKFILL_HOURS_OVERRIDE=Y` - The number of hours to backfill (required)
4. `BACKFILL_START_TIME=2025-04-10T00:00:00Z` - (optional) ISO-8601 formatted timestamp to start backfill from

Example in `.env`:

```
# Custom backfill test settings
# NOTE: This test will actually backfill/update your local db
TEST_BACKFILL=true
BACKFILL_INTEGRATION_ID=5
BACKFILL_HOURS_OVERRIDE=1440
BACKFILL_START_TIME=2025-04-10T00:00:00Z  # Optional: specific start time to backfill from
```

If **any** of the required environment variables are missing or invalid:

- The custom backfill test will be skipped
- The poller will run its normal 10-minute polling cycle

Notes:

- The backfill test will only work for TMS integrations that support time-based polling
  (currently McLeod Enterprise and Turvo)
- Be mindful of rate limits when backfilling, as it can be resource-intensive
