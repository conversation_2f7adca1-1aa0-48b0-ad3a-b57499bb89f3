package main

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestGetFreightTrackingIDSet(t *testing.T) {
	ctx := context.Background()
	mockIntegrationID := uint(1)

	t.Run("OK", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID3"}
		dbFuncListFreightTrackingIDs = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetFreightTrackingIDSet(ctx, mockIntegrationID)
		assert.NoError(t, err)
		assert.Len(t, result, len(mockData))
		for _, id := range mockData {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("Duplicate IDs", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID1", "ID3", "ID2"} // Duplicate IDs
		dbFuncListFreightTrackingIDs = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetFreightTrackingIDSet(ctx, mockIntegrationID)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // Unique IDs only
		for _, id := range []string{"ID1", "ID2", "ID3"} {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("DB error", func(t *testing.T) {
		dbFuncListFreightTrackingIDs = func(context.Context, uint) ([]string, error) {
			return nil, errors.New("database error")
		}

		result, err := GetFreightTrackingIDSet(ctx, mockIntegrationID)
		assert.Error(t, err)
		assert.Nil(t, result)
	})

}

func TestGetExternalTMSIDSet(t *testing.T) {
	ctx := context.Background()
	mockIntegrationID := uint(1)

	t.Run("OK", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID3"}
		dbFuncListExternalTMSIDs = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetExternalTMSIDSet(ctx, mockIntegrationID)
		assert.NoError(t, err)
		assert.Len(t, result, len(mockData))
		for _, id := range mockData {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("Duplicate IDs", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID1", "ID3", "ID2"} // Duplicate IDs
		dbFuncListExternalTMSIDs = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetExternalTMSIDSet(ctx, mockIntegrationID)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // Unique IDs only
		for _, id := range []string{"ID1", "ID2", "ID3"} {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("DB error", func(t *testing.T) {
		dbFuncListExternalTMSIDs = func(context.Context, uint) ([]string, error) {
			return nil, errors.New("database error")
		}

		result, err := GetExternalTMSIDSet(ctx, mockIntegrationID)
		assert.Error(t, err)
		assert.Nil(t, result)
	})

}

func TestGetLoadsWithEmptyCarrierCost(t *testing.T) {
	ctx := context.Background()
	mockIntegrationID := uint(1)
	mockIntegrationName := models.IntegrationName("Turvo")

	t.Run("OK", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID3"}
		dbFuncFindLoadIDsEmptyCarrierCost = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetLoadsWithEmptyCarrierCost(ctx, mockIntegrationID, mockIntegrationName)
		assert.NoError(t, err)
		assert.Len(t, result, len(mockData))
		for _, id := range mockData {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("Empty IDs filtered", func(t *testing.T) {
		mockData := []string{"ID1", "", "ID2", "", "ID3"} // Empty IDs
		dbFuncFindLoadIDsEmptyCarrierCost = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetLoadsWithEmptyCarrierCost(ctx, mockIntegrationID, mockIntegrationName)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // Only non-empty IDs
		for _, id := range []string{"ID1", "ID2", "ID3"} {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("Duplicate IDs", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID1", "ID3", "ID2"} // Duplicate IDs
		dbFuncFindLoadIDsEmptyCarrierCost = func(_ context.Context, integrationID uint) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			return mockData, nil
		}

		result, err := GetLoadsWithEmptyCarrierCost(ctx, mockIntegrationID, mockIntegrationName)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // Unique IDs only
		for _, id := range []string{"ID1", "ID2", "ID3"} {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("DB error", func(t *testing.T) {
		dbFuncFindLoadIDsEmptyCarrierCost = func(context.Context, uint) ([]string, error) {
			return nil, errors.New("database error")
		}

		result, err := GetLoadsWithEmptyCarrierCost(ctx, mockIntegrationID, mockIntegrationName)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}

func TestGetRecentlyUpdatedLoadIDSet(t *testing.T) {
	ctx := context.Background()
	mockIntegrationID := uint(1)
	mockMinutes := 20

	t.Run("OK", func(t *testing.T) {
		mockData := []string{"ID1", "ID2", "ID3"}
		dbFuncFindRecentlyUpdatedLoadIDs = func(_ context.Context, integrationID uint, minutes int) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			assert.Equal(t, mockMinutes, minutes)
			return mockData, nil
		}

		result, err := GetRecentlyUpdatedLoadIDSet(ctx, mockIntegrationID, mockMinutes)
		assert.NoError(t, err)
		assert.Len(t, result, len(mockData))
		for _, id := range mockData {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("Empty IDs filtered", func(t *testing.T) {
		mockData := []string{"ID1", "", "ID2", "", "ID3"} // Empty IDs
		dbFuncFindRecentlyUpdatedLoadIDs = func(_ context.Context, integrationID uint, minutes int) ([]string, error) {
			assert.Equal(t, mockIntegrationID, integrationID)
			assert.Equal(t, mockMinutes, minutes)
			return mockData, nil
		}

		result, err := GetRecentlyUpdatedLoadIDSet(ctx, mockIntegrationID, mockMinutes)
		assert.NoError(t, err)
		assert.Len(t, result, 3) // Only non-empty IDs
		for _, id := range []string{"ID1", "ID2", "ID3"} {
			_, exists := result[id]
			assert.True(t, exists)
		}
	})

	t.Run("DB error", func(t *testing.T) {
		dbFuncFindRecentlyUpdatedLoadIDs = func(context.Context, uint, int) ([]string, error) {
			return nil, errors.New("database error")
		}

		result, err := GetRecentlyUpdatedLoadIDSet(ctx, mockIntegrationID, mockMinutes)
		assert.Error(t, err)
		assert.Nil(t, result)
	})
}
