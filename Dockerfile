# Start from golang base image
FROM golang:1.24-alpine AS builder

# Set the current working directory inside the container
WORKDIR /build

# Copy go.mod, go.sum files and download deps
COPY go.mod go.sum ./
RUN go mod download

# Copy sources to the working directory
COPY . .

# Build the Go app
ARG project
RUN GOOS=linux CGO_ENABLED=0 GOARCH=amd64 go build -a -v -o main $project

RUN apk add --no-cache ca-certificates openssl

# Start a new stage from busybox
FROM busybox:latest

WORKDIR /dist

# Copy the build artifacts and certs from the previous stage
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /build/main .

# Run the executable
CMD ["./main"]