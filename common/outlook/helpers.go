package outlookhelpers

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers"
	email_helpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/jsoncfg"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

// ProcessAndEnqueueOutlookMessage encapsulates the common logic for processing a single Outlook message
// and enqueueing it for downstream processing. It fetches full message details, processes attachments,
// archives the message to S3, and sends a payload to an SQS queue.
func ProcessAndEnqueueOutlookMessage(
	ctx context.Context,
	client msclient.Client,
	sqsClient sqsclient.API,
	s3Uploader s3backup.Archiver,
	user *models.User,
	service *models.Service,
	msgSummary msclient.Message, // Contains only the message ID from the webhook
	webhookBody msclient.Value,
	processorQueueURL string,
) (err error) {
	ctx, metaSpan := otel.StartSpan(
		ctx, "outlook.ProcessAndEnqueueOutlookMessage",
		emails.MessageAttrs(user, &emails.OutlookMessage{Message: &msgSummary}),
	)
	defer func() { metaSpan.End(err) }()

	// Get the full message details.
	msg, err := client.GetMessageByID(
		ctx,
		msgSummary.ID,
		msclient.WithContentType(msclient.HTMLContentType),
	)
	if err != nil {
		if strings.Contains(err.Error(), "resource not found") {
			log.Info(
				ctx,
				"message not found, skipping",
				zap.String("messageId", msgSummary.ID),
			)
			return nil // Not an error, just skip.
		}
		// Return a fatal error to be handled by the caller.
		return fmt.Errorf("failed to get full message details: %w", err)
	}

	// Now that we have the full message, log it with the full message attrs
	ctx, _ = emails.LogWithMessageAttrs(ctx, user, &emails.OutlookMessage{Message: &msg})

	if !ShouldProcessMessage(ctx, user.EmailAddress, msg, webhookBody, client) {
		return nil
	}

	// Process attachments.
	var s3Attachments []models.Attachment
	var hasPDFs bool
	if email_helpers.ShouldProcessAttachments(service) {
		s3Attachments, hasPDFs, err = ProcessAttachments(ctx, client, user, msg.ID, s3Uploader)
		if err != nil {
			// Log the error but continue processing the email itself.
			if !strings.Contains(err.Error(), "resource not found") {
				log.Error(ctx, "failed to process attachments", zap.Error(err))
			}
		}
	}

	outlookMsg := emails.OutlookMessage{Message: &msg}

	// Prepare the payload for SQS.
	payload, err := emails.PrepareEmailPayload(
		ctx,
		&outlookMsg,
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithUser(user),
		emails.WithS3URL("temp-s3-url"), // This will be replaced after S3 upload.
		emails.WithAttachments(s3Attachments),
		emails.WithHasPDFs(hasPDFs),
	)
	if err != nil {
		log.Error(ctx, "error creating email payload", zap.Error(err))
		return nil // Non-fatal, log and continue.
	}

	// Archive the full message payload to S3.
	var s3URL string
	if s3Uploader != nil {
		if s3URL, err = s3Uploader.Outlook(ctx, payload, user.EmailAddress); err != nil {
			// Non-fatal, S3 archiving failure should not block ingestion.
			log.Error(ctx, "s3 archive failed", zap.String("msgId", msg.ID), zap.Error(err))
		}
	}
	payload.S3URL = s3URL

	payloadBytes, err := jsoncfg.SpaceEfficientConfig.Marshal(payload)
	if err != nil {
		log.Error(ctx, "error marshalling email payload", zap.Error(err))
		return nil // Non-fatal, log and continue.
	}

	_, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:    aws.String(processorQueueURL),
		MessageBody: aws.String(string(payloadBytes)),
	})
	if err != nil {
		// This is potentially a more significant error (e.g., SQS permissions).
		// We log it and continue, but it might indicate a larger problem.
		log.Error(ctx, "error enqueueing email", zap.Error(err))
		return nil
	}

	// Save ingestion status to redis
	if msg.ConversationID != "" {
		ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", msg.ConversationID)
		// Set a comfortable buffer to account for email processing time.
		// Processor queue metrics show an average of ~60s, but processing time can spike higher under load.
		if err := redis.SetKey(ctx, ingestionStatusKey, string(models.InFlight), 10*time.Minute); err != nil {
			log.Error(ctx, "error saving outlook email ingestion status to redis", zap.Error(err))
		}
	} else {
		log.WarnNoSentry(
			ctx,
			"no conversation ID found for message, could not save ingestion status",
			zap.Any("msg", msg),
		)
	}

	log.Info(ctx, "successfully enqueued email for processing")
	return nil
}

func ProcessAttachments(
	ctx context.Context,
	client msclient.Client,
	user *models.User,
	msgID string,
	s3Uploader s3backup.Archiver,
) (res []models.Attachment, hasPDFs bool, err error) {
	ctx, metaSpan := otel.StartSpan(
		ctx,
		"outlook.ProcessAttachments",
		emails.MessageAttrs(user, &emails.OutlookMessage{Message: &msclient.Message{ID: msgID}}),
	)
	defer func() { metaSpan.End(nil) }()

	attachments, err := client.GetMessageAttachmentsByID(ctx, msgID)
	if err != nil {
		return nil, hasPDFs, fmt.Errorf("failed to get attachments: %w", err)
	}

	if len(attachments) == 0 {
		log.Info(ctx, "No attachments found for message", zap.String("msgID", msgID))
		return nil, hasPDFs, nil
	}

	for _, attachment := range attachments {
		if attachment.ODataType != "#microsoft.graph.fileAttachment" {
			continue
		}

		ctx = log.With(
			ctx,
			zap.String("attachmentName", attachment.Name),
			zap.String("attachmentType", attachment.ContentType),
		)

		// LLM Load Building and Quote Request pipelines currently only support PDFs for now
		isPDF := email_helpers.IsPDF(attachment.ContentType, attachment.Name)

		data, err := base64.StdEncoding.DecodeString(attachment.ContentBytes)
		if err != nil {
			log.Error(ctx, "Failed to decode attachment content", zap.Error(err))
			continue
		}

		// S3 Cost Optimization: LLM Load Building and Quote Request pipelines currently only support PDFs for now
		// Store other attachments' metadata for displaying on FE
		var s3URL string
		if isPDF {
			hasPDFs = true

			s3URL, err = s3Uploader.Attachment(ctx, models.Outlook, user.EmailAddress, msgID, attachment.Name, data)
			if err != nil {
				log.Error(ctx, "Failed to upload attachment to S3", zap.Error(err))
			} else {
				log.Info(ctx, "Uploaded attachment to S3")
			}
		}

		res = append(res, models.Attachment{
			MessageExternalID:   msgID,
			ExternalID:          attachment.ID,
			MimeType:            helpers.Ternary(isPDF, "application/pdf", attachment.ContentType),
			IsInline:            attachment.IsInline,
			OriginalFileName:    attachment.Name,
			TransformedFileName: s3backup.SanitizeFileName(attachment.Name),
			S3URL:               s3URL,
		})

	}

	return res, hasPDFs, nil
}

// List of system folders excluded from ingestion (https://learn.microsoft.com/en-us/graph/api/resources/mailfolder).
//
// CONTEXT: MSGraph API doesn't support omitting certain folders from webhook subscription.
// This causes duplicate webhooks in cases when an email is drafted, moved to outbox, then sent,
// or when a user moves a message from one folder to another folder.
// MSGraph API does support subscribing to a subset of folders. However, some users have custom rules automatically
// move certain emails to certain folders, which we previously missed when we subscribed to just the "inbox" folder.
// SOLUTION: Drumkit subscribes to all messages and filters out webhooks that are:
//  1. not of changeType "created" and
//  2. are in blacklisted folders.
//
// In the future, we  may need to add support for sent items. This refactor will need to be done carefully and
// thoughtfully to avoid duplicate AI processing.
var blackListedFolders = []string{
	"sentitems", "outbox", "deleteditems", "drafts", "scheduled", "recoverableitemsdeletions",
}

// ShouldProcessMessage is a function that determines if a message should be processed.
// It is used to filter out draft messages and messages sent by the user.
// For clarity, we only want to process incoming messages.
func ShouldProcessMessage(
	ctx context.Context,
	userEmail string,
	msg msclient.Message,
	webhookBody msclient.Value,
	client msclient.Client,
) (res bool) {
	ctx, metaSpan := otel.StartSpan(ctx, "msclient.ShouldProcessMessage", nil)
	defer func() { metaSpan.End(nil) }()

	if webhookBody.ChangeType != "created" {
		log.Info(ctx, "skipping message without created change type", zap.String("changeType", webhookBody.ChangeType))
		return false
	}

	// Check if message is in a blacklisted folder
	blacklistedFolderMap, err := getBlacklistedFolderIDs(ctx, userEmail, client)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to get blacklisted folder IDs, defaulting to process message",
			zap.Error(err),
		)
	} else {
		// Check if the message's parent folder is in the blacklist
		if folderName, isBlacklisted := blacklistedFolderMap[msg.ParentFolderID]; isBlacklisted {
			log.Info(
				ctx,
				"skipping message in blacklisted folder",
				zap.String("msgID", msg.ID),
				zap.String("parentFolderID", msg.ParentFolderID),
				zap.String("blacklistedFolderName", folderName),
			)
			return false
		}
	}

	if msg.IsDraft {
		log.Info(ctx, "skipping draft message", zap.String("msgID", msg.ID))
		return false
	}

	// We DO want to process messages that a user sends to themself.
	// Example: a user forwards an email to themself.
	if msg.Sender.EmailAddress.Address != "" && len(msg.ToRecipients) > 0 {
		senderEmail := strings.ToLower(msg.Sender.EmailAddress.Address)
		userEmail = strings.ToLower(userEmail)

		// if sender is the user, check if they are one of the recipients
		isUserRecipient := func(recipient []msclient.RecipientCollection) bool {
			for _, r := range recipient {
				if strings.Contains(strings.ToLower(r.EmailAddress.Address), strings.ToLower(userEmail)) {
					return true
				}
			}
			return false
		}

		if senderEmail == userEmail &&
			(isUserRecipient(msg.ToRecipients) || isUserRecipient(msg.CcRecipients)) {

			log.Info(ctx, "processing message user sent to themself", zap.String("msgID", msg.ID))
			return true
		}
	}

	// we DON'T want to process messages sent by the user
	if msg.Sender.EmailAddress.Address != "" {
		senderEmail := strings.ToLower(msg.Sender.EmailAddress.Address)
		userEmail = strings.ToLower(userEmail)

		if senderEmail == userEmail {
			log.Info(ctx, "skipping outgoing message",
				zap.String("msgID", msg.ID),
				zap.String("sender", senderEmail))
			return false
		}
	}

	return true
}

var folderCacheTTL = 7 * 24 * time.Hour

// getBlacklistedFolderIDs retrieves folder IDs for blacklisted folders, using Redis cache
// with a 1-week TTL. Falls back to API calls if cache misses.
// Returns a map of folder ID to human-readable name for logging purposes.
func getBlacklistedFolderIDs(
	ctx context.Context,
	userEmail string,
	client msclient.Client,
) (folderMap map[string]string, err error) {
	if redis.RDB == nil {
		log.Warn(ctx, "Redis client not available, falling back to API calls only")
		return getBlacklistedFolderIDsFromAPI(ctx, client)
	}

	folderMap = make(map[string]string)
	var uncachedFolders []string

	// Check Redis cache for each blacklisted folder
	for _, folderName := range blackListedFolders {
		cacheKey := fmt.Sprintf("outlook-folder-%s-%s", userEmail, folderName)

		folderID, found, err := redis.GetKey[string](ctx, cacheKey)
		if err != nil || !found {
			// Cache miss - need to fetch from API
			uncachedFolders = append(uncachedFolders, folderName)
			log.Debug(
				ctx,
				"folder ID not found in cache",
				zap.String("folderName", folderName),
				zap.String("userEmail", userEmail),
			)
		} else {
			// Cache hit
			folderMap[folderID] = folderName
			log.Debug(
				ctx,
				"folder ID found in cache",
				zap.String("folderName", folderName),
				zap.String("folderID", folderID),
			)
		}
	}

	// Fetch uncached folder IDs from API and cache them
	for _, folderName := range uncachedFolders {
		folder, err := client.GetFolderByID(ctx, folderName)
		if err != nil {
			log.Warn(
				ctx,
				"failed to get folder by well-known name",
				zap.String("folderName", folderName),
				zap.Error(err),
			)
			continue
		}

		// Cache the folder ID for 1 week
		cacheKey := fmt.Sprintf("outlook-folder-%s-%s", userEmail, folderName)
		err = redis.SetKey(ctx, cacheKey, folder.ID, folderCacheTTL)
		if err != nil {
			log.Warn(
				ctx,
				"failed to cache folder ID",
				zap.String("cacheKey", cacheKey),
				zap.String("folderID", folder.ID),
				zap.Error(err),
			)
		} else {
			log.Debug(
				ctx,
				"cached folder ID",
				zap.String("folderName", folderName),
				zap.String("folderID", folder.ID),
			)
		}

		folderMap[folder.ID] = folderName
	}

	return folderMap, nil
}

// getBlacklistedFolderIDsFromAPI fetches all blacklisted folder IDs directly from API
// Used as fallback when Redis is unavailable
func getBlacklistedFolderIDsFromAPI(ctx context.Context, client msclient.Client) (map[string]string, error) {
	folderMap := make(map[string]string)

	for _, folderName := range blackListedFolders {
		folder, err := client.GetFolderByID(ctx, folderName)
		if err != nil {
			log.Warn(
				ctx,
				"failed to get folder by well-known name",
				zap.String("folderName", folderName),
				zap.Error(err),
			)
			continue
		}
		folderMap[folder.ID] = folderName
	}

	return folderMap, nil
}
