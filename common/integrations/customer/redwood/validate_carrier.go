package redwood

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	ValidateCarrierRequest struct {
		Metadata MetadataInfo `json:"metadata"`
		Payload  PayloadInfo  `json:"payload"`
	}
)

func (r *Redwood) ValidateCarrier(
	ctx context.Context,
	user models.User,
	carrier models.CarrierInformation,
) (*models.TruckListErrors, error) {
	payload := TruckListRequest{
		Metadata: MetadataInfo{
			MethodParamsInfo{
				ProcessID: "3d6e0575-4f7a-4307-be39-e0b576d2c754",
			},
		},
		Payload: PayloadInfo{
			TruckListID: 1,
			UserName:    fmt.Sprintf("%s - Validating Carrier", user.Name),
			UserEmail:   fmt.Sprintf("%s - Validating Carrier", user.EmailAddress),
			Carrier: CarrierInfo{
				CarrierName:  carrier.Name,
				CarrierMC:    carrier.MC,
				CarrierDOT:   carrier.DOT,
				ContactEmail: carrier.ContactEmail,
				ContactName:  carrier.ContactName,
			},
			Trucks: []TruckInfo{
				{
					TruckID:      1,
					PickupCity:   "Boston",
					PickupState:  "MA",
					DropoffCity:  "New York",
					DropoffState: "NY",
					TruckType:    "VAN",
					TruckLength:  53,
				},
			},
			IsDraft: true,
		},
	}

	postedByUserDomain := strings.ToLower(strings.Split(user.EmailAddress, "@")[1])
	if postedByUserDomain == "78n517.onmicrosoft.com" {
		payload.Payload.UserEmail = "<EMAIL>"
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	validateCarrierURL := r.host + TruckListEndpoint

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, validateCarrierURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("x-api-key", r.customer.APIKey)

	log.Info(
		ctx,
		"redwood.ValidateCarrier - Payload",
		zap.String("payload", string(reqBody)),
		zap.String("url", validateCarrierURL),
	)

	res, err := httpRequest(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	log.Info(
		ctx,
		"redwood.ValidateCarrier - Response",
		zap.String("response", string(resBody)),
		zap.Int("HTTP Status Code", res.StatusCode),
		zap.String("url", validateCarrierURL),
	)

	var dst TruckListResponse

	if err = json.Unmarshal(resBody, &dst); err != nil {
		return nil, err
	}

	validationTruckList := models.TruckList{
		Errors: models.TruckListErrors{},
	}

	if dst.Carrier.Outcome == "Failure" {
		if validationTruckList.Errors.Carrier == nil {
			validationTruckList.Errors.Carrier = make(map[string][]models.CarrierInformation)
		}

		for _, reason := range dst.Carrier.Reasons {
			switch PostTruckListError(reason) {
			case NoCarriersFound:
				validationTruckList.Errors.Carrier[reason] = []models.CarrierInformation{}
			case MultipleCarriersFound:
				var carriers []models.CarrierInformation

				for _, c := range dst.Carrier.Result {
					carrier := models.CarrierInformation{
						Name:         c.CarrierName,
						MC:           c.CarrierMC,
						DOT:          c.CarrierDOT,
						ContactEmail: c.ContactEmail,
						ContactName:  c.ContactName,
					}

					carriers = append(carriers, carrier)
				}

				validationTruckList.Errors.Carrier[reason] = carriers
			default:
				validationTruckList.Errors.Carrier[string(UnexpectedError)] = []models.CarrierInformation{}
			}
		}
	}

	if dst.CarrierContact.Outcome == "Failure" {
		if validationTruckList.Errors.CarrierContact == nil {
			validationTruckList.Errors.CarrierContact = []string{}
		}

		for _, reason := range dst.CarrierContact.Reasons {
			if PostTruckListError(reason) == NoCarrierContactFound {
				validationTruckList.Errors.CarrierContact = append(validationTruckList.Errors.CarrierContact, reason)
			} else {
				validationTruckList.Errors.CarrierContact = append(validationTruckList.Errors.CarrierContact,
					string(UnexpectedError))
			}
		}
	}

	if validationTruckList.Errors.HasErrors() {
		return &validationTruckList.Errors, nil
	}

	return nil, nil
}

// BuildCarrierValidationErrorMessage constructs a human-readable error message.
func BuildCarrierValidationErrorMessage(errors models.TruckListErrors) string {
	var msgs []string

	// Carrier Errors
	if len(errors.Carrier) > 0 {
		for reason := range errors.Carrier {
			if message, exists := errorMessages[PostTruckListError(reason)]; exists {
				msgs = append(msgs, fmt.Sprintf("Carrier Error: %s", message))
			} else {
				msgs = append(msgs, fmt.Sprintf("Carrier Error: %s", reason))
			}
		}
	}

	// Carrier Contact Errors
	if len(errors.CarrierContact) > 0 {
		for _, reason := range errors.CarrierContact {
			if message, exists := errorMessages[PostTruckListError(reason)]; exists {
				msgs = append(msgs, fmt.Sprintf("Carrier Contact Error: %s", message))
			} else {
				msgs = append(msgs, fmt.Sprintf("Carrier Contact Error: %s", reason))
			}
		}
	}

	return strings.Join(msgs, "\n\n")
}
