# Redwood Connect

This is an integration to [Redwood's Connect Platform](https://www.redwoodlogistics.com/technology/redwood-connect).

We use this [endpoint](https://drumkit-redwood.apidog.io/api-10049787) to submit truck lists that we detect in their emails.

## Flow
We usually submit truck lists twice, once as a draft to detect errors early (in the processor lambda) and second via user submission as non-draft (in the drumkit sidebar). We do the former to show the users actions they need to take before submitting the truck list. Actions can be external ones that they need the users to take on the Connect Platform e.g., create a new equipment type for the carrier or ones within Drumkit e.g., select the right carrier.

## Submission Requirements

### Required Fields
- Process ID: A constant value needed for all requests.
- Available/Pickup Date: Must be current date or within 30 days in the future.
- Carrier Contact Email: Required for carrier identification.
- Location Information:
  - Origin: Either valid pickup city and state combination OR valid zip code.
  - Destination: Must be provided with either city/state combination OR valid zip code.
- Truck Length: Must be specified for each truck.

### Draft Mode
The `isDraft` flag is used to:
- When `true`: Pre-validate submissions and fetch potential errors to display to users.
- When `false`: Indicate an actual submission by users.

For detailed request structure, refer to our [API documentation](https://drumkit-redwood.apidog.io/api-10049787). The password is stored in 1Password.

## Error Handling

### Truck List Level Errors
These errors prevent the entire truck list from being processed:
- Carrier Errors: Invalid or missing carrier information.
- Carrier Contact Errors: Issues with carrier contact details.
- Truck Level Errors: Validation failures for individual trucks.

A truck list is considered to have errors if any of the above categories contain validation failures. Posted By validation errors are not considered blocking errors for submission.

### Carrier Validation
Verifies carrier existence and uniqueness in the system.
- Success: `one-carrier-found` - Carrier successfully identified
- Errors:
  - `no-carriers-found` - No carriers were found with that information. It's either incorrect or the user has to create this new carrier in their portal. The former case is more likely.
  - `multiple-carriers-found` - Multiple carriers with that information were found. The user has to select the one that's correct. If the carriers all have the same name, then the only distinction is the carrier id that they could select by, however, we don't support that flow yet.
  - `unexpected-error-occurred` - System error during validation.

### Carrier Contact Validation
Ensures the provided contact email is associated with the carrier.
- Success: `carrier-contact-found` - Contact email is valid for carrier.
- Errors:
  - `no-carrier-contact-found`: The carrier contact email isn't associated with this carrier in their portal (yet). They should select another one or they can create a new contact under that carrier.
  - `unexpected-error-occurred` - System error during validation.

### Equipment Validation
Validates truck type and equipment specifications.
- Success:
  - `exact-equipment-match-found` - Perfect match for equipment type and length.
  - `equipment-match-found-without-length` - Equipment type matches but length not found.
- Errors:
  - `no-carriers-found` - Carrier validation must pass first
  - `invalid-or-unknown-truck-type` - This truck type doesn't exist under this carrier.
  - `no-matching-equipment-found` - Equipment specifications don't match carrier's available types.
  - `no-matching-trailer-type-found-in-carrier-file` - Trailer type not configured for carrier.
  - `unexpected-error-occurred` - System error during validation.

### Origin Location Validation
Verifies pickup location details.
- Success:
  - `pickup-found-by-zip` - Valid ZIP code provided.
  - `pickup-found-by-city-state` - Valid city/state combination.
- Errors:
  - `pickup-could-not-be-found` - Location information invalid or incomplete.
  - `unexpected-error-occurred` - System error during validation.

### Destination Validation
Verifies drop-off location details.
- Success:
  - `nulldestination-replacedwith-carrierdomicile` - Using carrier's registered location.
  - `destination-processed` - General success.
  - `destination-city-state-valid` - Valid city/state combination.
  - `destination-state-valid` - State validated.
  - `destination-zip-valid` - Valid ZIP code provided.
  - `destination-city-zip-valid` - Valid city/ZIP combination.
  - `destination-state-zip-valid` - Valid state/ZIP combination.
- Errors:
  - `no-carriers-found` - Carrier validation must pass first.
  - `carrier-domicile-not-found` - Carrier's registered location not found.
  - `destination-needs-more-than-city` - City alone is insufficient.
  - `destination-city-state-not-found` - Invalid city/state combination.
  - `destination-state-not-found` - Invalid state.
  - `destination-zip-not-found` - Invalid ZIP code.
  - `destination-city-zip-not-found` - Invalid city/ZIP combination.
  - `destination-state-zip-not-found` - Invalid state/ZIP combination.
  - `unexpected-error-occurred` - System error during validation.

### Available Date Validation
Validates the pickup date timing.
- Success: `availabledate-is-valid` - Date meets all requirements.
- Errors:
  - `availabledate-is-required` - Pickup date not provided.
  - `availabledate-mustbe-noworfuture` - Date cannot be in the past.
  - `availabledate-mustbe-withinthirtydays` - Date must be within 30 days.
  - `unexpected-error-occurred` - System error during validation.

### Posted By Validation
Verifies the user posting the truck list.
- Success: `posted-by-found` - Valid posting user.
- Errors:
  - `posted-by-not-found` - User not found in system.
  - `unexpected-error-occurred` - System error during validation.

### Posting Status (individual truck)
For each truck in a valid truck list:
- Success:
  - `truck-posted` - Individual truck successfully posted
- Errors:
  - `truck-not-posted` - Individual truck posting failed.
  - `unexpected-error-occurred` - System error during truck list submission.
