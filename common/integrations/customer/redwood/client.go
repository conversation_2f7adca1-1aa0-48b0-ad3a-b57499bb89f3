package redwood

import (
	"context"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type Redwood struct {
	customer models.Integration
	host     string
}

type API interface {
	SubmitTruckList(
		ctx context.Context, isDraft bool, trucklist models.TruckList,
	) (*models.TruckListErrors, error)
	ValidateCarrier(
		ctx context.Context, user models.User, carrier models.CarrierInformation,
	) (*models.TruckListErrors, error)
}

func New(ctx context.Context, customer models.Integration) (API, error) {
	log.With(ctx, zap.Uint("drumkitCustomerID", customer.ID), zap.String("customerName", "redwood"))

	if customer.Disabled {
		return nil, errtypes.DisabledIntegrationError(customer)
	}

	var client Redwood

	client.customer = customer
	client.host = customer.Tenant

	return &client, nil
}
