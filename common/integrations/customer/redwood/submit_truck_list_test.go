package redwood

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestSubmitTruckList_Success(t *testing.T) {
	originalHTTPRequest := httpRequest
	defer func() { httpRequest = originalHTTPRequest }()

	httpRequest = func(_ *http.Request) (*http.Response, error) {
		response := TruckListResponse{
			IsSuccess:   true,
			TruckListID: 111,
			Carrier: CarrierResult{
				Outcome: "Success",
				Result: []Carrier{
					{
						CarrierID:    155555,
						CarrierName:  "ACME TRUCKING LLC",
						CarrierMC:    "888105",
						CarrierDOT:   "1058888",
						ContactEmail: "<EMAIL>",
						ContactName:  "",
					},
				},
				Reasons: []string{"one-carrier-found"},
			},
			PostedBy: PostedByResult{
				Outcome: "Failure",
				Result: PostedBy{
					PostedByID: 0,
				},
				Reasons: []string{string(PostedByNotFound)},
			},
			CarrierContact: CarrierContactResult{
				Outcome: "Success",
				Result: CarrierContact{
					CarrierContactID: 471033,
				},
				Reasons: []string{"carrier-contact-found"},
			},
			Trucks: []TruckResult{
				{
					IsSuccess: true,
					TruckID:   1,
					PickupDate: DateResult{
						Outcome: "Success",
						Result: DateInfo{
							AvailableDate: "2024-10-15",
						},
						Reasons: []string{"availabledate-is-valid"},
					},
					Pickup: LocationResult{
						Outcome: "Success",
						Result: LocationInfo{
							City:    "Chantilly",
							State:   "VA",
							ZipCode: "20152",
						},
						Reasons: []string{"pickup-found-by-city-state"},
					},
					Dropoff: LocationResult{
						Outcome: "Success",
						Result: LocationInfo{
							City:    "Boston",
							State:   "MA",
							ZipCode: "22222",
						},
						Reasons: []string{"destination-city-state-valid"},
					},
					Equipment: EquipmentResult{
						Outcome: "Success",
						Result: EquipmentInfo{
							EquipmentID: 1,
							TruckType:   "VAN",
							TruckLength: 53,
						},
						Reasons: []string{"exact-equipment-match-found"},
					},
				},
			},
		}

		responseBody, err := json.Marshal(response)
		if err != nil {
			return nil, err
		}

		respBody := io.NopCloser(bytes.NewReader(responseBody))

		mockResp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       respBody,
			Header:     make(http.Header),
		}
		mockResp.Header.Set("Content-Type", "application/json")

		return mockResp, nil
	}

	originalGetUserByID := getUserByID
	defer func() { getUserByID = originalGetUserByID }()

	getUserByID = func(_ context.Context, _ uint) (models.User, error) {
		return models.User{
			Name:         "Test User",
			EmailAddress: "<EMAIL>",
		}, nil
	}

	originalUpdateTruckList := updateTruckList
	defer func() { updateTruckList = originalUpdateTruckList }()

	updateTruckList = func(_ context.Context, _ *models.TruckList) error {
		return nil
	}

	trucklist := models.TruckList{
		UserID:  1,
		IsDraft: false,
		Trucks: []models.Truck{
			{
				PickupLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "Boston",
						State: "MA",
						Zip:   "12345",
					},
				},
				PickupDate: models.SuggestionAppliedPair[models.NullTime]{
					Applied: models.NullTime{
						Time:  time.Now().Add(24 * time.Hour),
						Valid: true,
					},
				},
				DropoffLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "New York",
						State: "NY",
						Zip:   "67890",
					},
				},
				DropoffDate: models.SuggestionAppliedPair[models.NullTime]{
					Applied: models.NullTime{
						Time:  time.Now().Add(48 * time.Hour),
						Valid: true,
					},
				},
				Type: models.SuggestionAppliedPair[models.TruckType]{
					Applied: models.VanTruckType,
				},
				Length: models.SuggestionAppliedPair[float32]{
					Applied: 53,
				},
			},
		},
		Carrier: models.CarrierInformation{
			Name:         "ACME INC",
			MC:           "123456",
			DOT:          "654321",
			ContactEmail: "<EMAIL>",
			ContactName:  "Jane Doe",
		},
	}

	client := Redwood{
		host: "https://fakeurl.com",
		customer: models.Integration{
			APIKey: "test-api-key",
		},
	}

	errors, err := client.SubmitTruckList(context.Background(), false, trucklist)

	assert.NoError(t, err)
	assert.Nil(t, errors)
}

func TestSubmitTruckList_TruckFailure(t *testing.T) {
	originalHTTPRequest := httpRequest
	defer func() { httpRequest = originalHTTPRequest }()

	httpRequest = func(_ *http.Request) (*http.Response, error) {
		response := TruckListResponse{
			IsSuccess:   false,
			TruckListID: 333,
			Carrier: CarrierResult{
				Outcome: "Success",
				Result: []Carrier{
					{
						CarrierID:    155555,
						CarrierName:  "ACME TRUCKING LLC",
						CarrierMC:    "888105",
						CarrierDOT:   "1058888",
						ContactEmail: "<EMAIL>",
						ContactName:  "",
					},
				},
				Reasons: []string{"one-carrier-found"},
			},
			PostedBy: PostedByResult{
				Outcome: "Success",
				Result: PostedBy{
					PostedByID: 123456,
				},
				Reasons: []string{},
			},
			CarrierContact: CarrierContactResult{
				Outcome: "Success",
				Result: CarrierContact{
					CarrierContactID: 471033,
				},
				Reasons: []string{"carrier-contact-found"},
			},
			Trucks: []TruckResult{
				{
					IsSuccess: false,
					TruckID:   1,
					PickupDate: DateResult{
						Outcome: "Failure",
						Result:  DateInfo{},
						Reasons: []string{string(PickupDateIsPast)},
					},
					Pickup: LocationResult{
						Outcome: "Success",
						Result: LocationInfo{
							City:    "Boston",
							State:   "MA",
							ZipCode: "12345",
						},
						Reasons: []string{"pickup-found-by-city-state"},
					},
					Dropoff: LocationResult{
						Outcome: "Success",
						Result: LocationInfo{
							City:    "New York",
							State:   "NY",
							ZipCode: "67890",
						},
						Reasons: []string{"destination-city-state-valid"},
					},
					Equipment: EquipmentResult{
						Outcome: "Success",
						Result: EquipmentInfo{
							EquipmentID: 1,
							TruckType:   "VAN",
							TruckLength: 53,
						},
						Reasons: []string{"exact-equipment-match-found"},
					},
				},
			},
		}

		responseBody, err := json.Marshal(response)
		if err != nil {
			return nil, err
		}

		respBody := io.NopCloser(bytes.NewReader(responseBody))

		mockResp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       respBody,
			Header:     make(http.Header),
		}
		mockResp.Header.Set("Content-Type", "application/json")

		return mockResp, nil
	}

	originalGetUserByID := getUserByID
	defer func() { getUserByID = originalGetUserByID }()
	getUserByID = func(_ context.Context, _ uint) (models.User, error) {
		return models.User{
			Name:         "Test User",
			EmailAddress: "<EMAIL>",
		}, nil
	}

	originalUpdateTruckList := updateTruckList
	defer func() { updateTruckList = originalUpdateTruckList }()
	updateTruckList = func(_ context.Context, _ *models.TruckList) error {
		return nil
	}

	trucklist := models.TruckList{
		UserID:  1,
		IsDraft: false,
		Trucks: []models.Truck{
			{
				PickupLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "Boston",
						State: "MA",
						Zip:   "12345",
					},
				},
				PickupDate: models.SuggestionAppliedPair[models.NullTime]{
					Applied: models.NullTime{
						Time:  time.Now().Add(-24 * time.Hour),
						Valid: true,
					},
				},
				DropoffLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "New York",
						State: "NY",
						Zip:   "67890",
					},
				},
				DropoffDate: models.SuggestionAppliedPair[models.NullTime]{
					Applied: models.NullTime{
						Time:  time.Now().Add(48 * time.Hour),
						Valid: true,
					},
				},
				Type: models.SuggestionAppliedPair[models.TruckType]{
					Applied: models.VanTruckType,
				},
				Length: models.SuggestionAppliedPair[float32]{
					Applied: 53,
				},
			},
		},
		Carrier: models.CarrierInformation{
			Name:         "ACME TRUCKING LLC",
			MC:           "888105",
			DOT:          "1058888",
			ContactEmail: "<EMAIL>",
			ContactName:  "",
		},
	}

	client := Redwood{
		host: "https://fakeurl.com",
		customer: models.Integration{
			APIKey: "test-api-key",
		},
	}

	errors, err := client.SubmitTruckList(context.Background(), false, trucklist)

	assert.NoError(t, err)
	assert.NotNil(t, errors)

	assert.NotNil(t, errors.Truck)
	truckError, exists := errors.Truck[1]
	assert.True(t, exists)
	assert.Contains(t, truckError.PickupDate, string(PickupDateIsPast))

	errorMessage := BuildTruckListErrorMessage(*errors)
	expectedErrorMessage := "Truck ID 1 Errors:\nPickup Date Error: The available date must be now or a future date."
	assert.Equal(t, expectedErrorMessage, errorMessage)
}

func TestSubmitTruckList_CarrierNotFound(t *testing.T) {
	originalHTTPRequest := httpRequest
	defer func() { httpRequest = originalHTTPRequest }()

	httpRequest = func(_ *http.Request) (*http.Response, error) {
		response := TruckListResponse{
			IsSuccess:   false,
			TruckListID: 444,
			Carrier: CarrierResult{
				Outcome: "Failure",
				Result:  []Carrier{},
				Reasons: []string{string(NoCarriersFound)},
			},
			PostedBy: PostedByResult{
				Outcome: "Success",
				Result: PostedBy{
					PostedByID: 123456,
				},
				Reasons: []string{},
			},
			CarrierContact: CarrierContactResult{
				Outcome: "Success",
				Result: CarrierContact{
					CarrierContactID: 471033,
				},
				Reasons: []string{"carrier-contact-found"},
			},
			Trucks: []TruckResult{
				{
					IsSuccess: true,
					TruckID:   1,
					// Since carrier is not found, trucks won't be processed
				},
			},
		}

		responseBody, err := json.Marshal(response)
		if err != nil {
			return nil, err
		}

		respBody := io.NopCloser(bytes.NewReader(responseBody))

		mockResp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       respBody,
			Header:     make(http.Header),
		}
		mockResp.Header.Set("Content-Type", "application/json")

		return mockResp, nil
	}

	originalGetUserByID := getUserByID
	defer func() { getUserByID = originalGetUserByID }()
	getUserByID = func(_ context.Context, _ uint) (models.User, error) {
		return models.User{
			Name:         "Test User",
			EmailAddress: "<EMAIL>",
		}, nil
	}

	originalUpdateTruckList := updateTruckList
	defer func() { updateTruckList = originalUpdateTruckList }()
	updateTruckList = func(_ context.Context, _ *models.TruckList) error {
		return nil
	}

	// Create a truck list with an unknown carrier
	trucklist := models.TruckList{
		UserID: 1,
		Trucks: []models.Truck{
			{
				// Populating minimal truck details
			},
		},
		Carrier: models.CarrierInformation{
			Name:         "Unknown Carrier Inc.",
			MC:           "000000",
			DOT:          "0000000",
			ContactEmail: "<EMAIL>",
			ContactName:  "Unknown",
		},
	}

	client := Redwood{
		host: "https://fakeurl.com",
		customer: models.Integration{
			APIKey: "test-api-key",
		},
	}

	errors, err := client.SubmitTruckList(context.Background(), false, trucklist)

	assert.NoError(t, err)
	assert.NotNil(t, errors)

	assert.NotNil(t, errors.Carrier)
	assert.Contains(t, errors.Carrier, string(NoCarriersFound))

	errorMessage := BuildTruckListErrorMessage(*errors)
	expectedErrorMessage := "Carrier Error: No carriers were found matching the provided information."
	assert.Equal(t, expectedErrorMessage, errorMessage)
}

func TestSubmitTruckList_MultipleCarriersFound(t *testing.T) {
	originalHTTPRequest := httpRequest
	defer func() { httpRequest = originalHTTPRequest }()

	httpRequest = func(_ *http.Request) (*http.Response, error) {
		response := TruckListResponse{
			IsSuccess:   false,
			TruckListID: 555,
			Carrier: CarrierResult{
				Outcome: "Failure",
				Result: []Carrier{
					{
						CarrierID:    1001,
						CarrierName:  "ACME Logistics",
						CarrierMC:    "MC1001",
						CarrierDOT:   "DOT1001",
						ContactEmail: "<EMAIL>",
						ContactName:  "John Doe",
					},
					{
						CarrierID:    1002,
						CarrierName:  "ACME Transport",
						CarrierMC:    "MC1002",
						CarrierDOT:   "DOT1002",
						ContactEmail: "<EMAIL>",
						ContactName:  "Jane Smith",
					},
				},
				Reasons: []string{string(MultipleCarriersFound)},
			},
			PostedBy: PostedByResult{
				Outcome: "Success",
				Result: PostedBy{
					PostedByID: 123456,
				},
				Reasons: []string{},
			},
			CarrierContact: CarrierContactResult{
				Outcome: "Success",
				Result: CarrierContact{
					CarrierContactID: 7890,
				},
				Reasons: []string{"carrier-contact-found"},
			},
			Trucks: []TruckResult{
				{
					IsSuccess: true,
					TruckID:   1,
					// Since multiple carriers were found, trucks won't be processed
				},
			},
		}

		responseBody, err := json.Marshal(response)
		if err != nil {
			return nil, err
		}

		respBody := io.NopCloser(bytes.NewReader(responseBody))
		mockResp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       respBody,
			Header:     make(http.Header),
		}
		mockResp.Header.Set("Content-Type", "application/json")

		return mockResp, nil
	}

	originalGetUserByID := getUserByID
	defer func() { getUserByID = originalGetUserByID }()
	getUserByID = func(_ context.Context, _ uint) (models.User, error) {
		return models.User{
			Name:         "Test User",
			EmailAddress: "<EMAIL>",
		}, nil
	}

	originalUpdateTruckList := updateTruckList
	defer func() { updateTruckList = originalUpdateTruckList }()
	updateTruckList = func(_ context.Context, _ *models.TruckList) error {
		return nil
	}

	// Create a trucklist where multiple carriers could match the provided details.
	trucklist := models.TruckList{
		UserID: 1,
		Trucks: []models.Truck{
			{
				PickupLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "Los Angeles",
						State: "CA",
						Zip:   "90001",
					},
				},
				DropoffLocation: models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  "San Francisco",
						State: "CA",
						Zip:   "94105",
					},
				},
			},
		},
		Carrier: models.CarrierInformation{
			Name:         "ACME",
			MC:           "",
			DOT:          "",
			ContactEmail: "",
			ContactName:  "",
		},
	}

	client := Redwood{
		host: "https://fakeurl.com",
		customer: models.Integration{
			APIKey: "test-api-key",
		},
	}

	errors, err := client.SubmitTruckList(context.Background(), false, trucklist)

	assert.NoError(t, err)
	assert.NotNil(t, errors)

	assert.NotNil(t, errors.Carrier)
	assert.Contains(t, errors.Carrier, string(MultipleCarriersFound))

	multipleCarriers := errors.Carrier[string(MultipleCarriersFound)]
	assert.Len(t, multipleCarriers, 2)
	assert.Equal(t, "ACME Logistics", multipleCarriers[0].Name)
	assert.Equal(t, "ACME Transport", multipleCarriers[1].Name)

	errorMessage := BuildTruckListErrorMessage(*errors)
	expectedErrorMessage := "Carrier Error: Multiple carriers were found. Please specify the correct one."
	assert.Equal(t, expectedErrorMessage, errorMessage)
}
