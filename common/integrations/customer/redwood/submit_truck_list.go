package redwood

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type (
	PostTruckListError   string
	PostTruckListSuccess string
)

const (
	TruckListEndpoint = "/run/execute_process"

	// Generic errors
	UnexpectedError PostTruckListError = "unexpected-error-occurred"

	// Carrier errors
	NoCarriersFound       PostTruckListError = "no-carriers-found"
	MultipleCarriersFound PostTruckListError = "multiple-carriers-found"

	// Carrier contact errors
	NoCarrierContactFound PostTruckListError = "no-carrier-contact-found"

	// Equipment errors
	InvalidTruckType         PostTruckListError = "invalid-or-unknown-truck-type"
	NoMatchingEquipment      PostTruckListError = "no-matching-equipment-found"
	NoMatchingTrailerType    PostTruckListError = "no-matching-trailer-type-found-in-carrier-file"
	EquipmentNoCarriersFound PostTruckListError = "no-carriers-found"

	// Pickup location errors
	PickupNotFound PostTruckListError = "pickup-could-not-be-found"

	// Destination location successes
	NullDestinationReplacedWithCarrierDomicile PostTruckListSuccess = "nulldestination-replacedwith-carrierdomicile"

	// Destination location errors
	CarrierDomicileNotFound      PostTruckListError = "carrier-domicile-not-found"
	DestinationNeedsMoreThanCity PostTruckListError = "destination-needs-more-than-city"
	DestinationCityStateNotFound PostTruckListError = "destination-city-state-not-found"
	DestinationStateNotFound     PostTruckListError = "destination-state-not-found"
	DestinationZipNotFound       PostTruckListError = "destination-zip-not-found"
	DestinationCityZipNotFound   PostTruckListError = "destination-city-zip-not-found"
	DestinationStateZipNotFound  PostTruckListError = "destination-state-zip-not-found"
	DestinationNoCarriersFound   PostTruckListError = "no-carriers-found"

	// Available (pickup) date errors
	PickupDateIsRequired   PostTruckListError = "availabledate-is-required"
	PickupDateIsPast       PostTruckListError = "availabledate-mustbe-noworfuture"
	PickupDateIsOutOfRange PostTruckListError = "availabledate-mustbe-withinthirtydays"

	// Posted by errors
	PostedByNotFound PostTruckListError = "posted-by-not-found" // Redwood agent's internal ID

	// Truck errors
	TruckNotPosted PostTruckListError = "truck-not-posted"
)

var (
	getUserByID     = userDB.GetByID
	updateTruckList = truckDB.UpdateTruckList
	httpRequest     = otel.TracingHTTPClient().Do

	errorMessages = map[PostTruckListError]string{
		UnexpectedError: "An unexpected error occurred.",
		// Carrier Errors
		NoCarriersFound:       "No carriers were found matching the provided information.",
		MultipleCarriersFound: "Multiple carriers were found. Please specify the correct one.",
		// Carrier Contact Errors
		NoCarrierContactFound: "No carrier contact was found.",
		// Equipment Errors
		InvalidTruckType:      "Invalid or unknown truck type specified.",
		NoMatchingEquipment:   "No matching equipment was found for the carrier.",
		NoMatchingTrailerType: "No matching trailer type was found in the carrier file.",
		// Origin Errors
		PickupNotFound: "The pickup location could not be found.",
		// Destination Errors
		CarrierDomicileNotFound:      "Carrier domicile not found.",
		DestinationNeedsMoreThanCity: "Destination needs more than the city provided.",
		DestinationCityStateNotFound: "The destination city and state could not be found.",
		DestinationStateNotFound:     "The destination state could not be found.",
		DestinationZipNotFound:       "The destination ZIP code could not be found.",
		DestinationCityZipNotFound:   "The destination city and ZIP code could not be found.",
		DestinationStateZipNotFound:  "The destination state and ZIP code could not be found.",
		// Available Date Errors
		PickupDateIsRequired:   "The available date is required.",
		PickupDateIsPast:       "The available date must be now or a future date.",
		PickupDateIsOutOfRange: "The available date must be within thirty days.",
		// Posted By Errors
		PostedByNotFound: "The poster could not be identified.",
		// Create Posting Errors
		TruckNotPosted: "The truck could not be posted.",
	}
)

type (
	TruckListRequest struct {
		Metadata MetadataInfo `json:"metadata"`
		Payload  PayloadInfo  `json:"payload"`
	}

	MetadataInfo struct {
		MethodParams MethodParamsInfo `json:"method_params"`
	}

	MethodParamsInfo struct {
		ProcessID string `json:"process_id"`
		StepID    string `json:"step_id"`
	}

	PayloadInfo struct {
		TruckListID uint        `json:"truckListId"`
		UserName    string      `json:"userName"`
		UserEmail   string      `json:"userEmail"`
		IsDraft     bool        `json:"isDraft"`
		Carrier     CarrierInfo `json:"carrier"`
		Trucks      []TruckInfo `json:"trucks"`
	}

	CarrierInfo struct {
		CarrierName  string `json:"carrierName"`
		CarrierMC    string `json:"carrierMc"`
		CarrierDOT   string `json:"carrierDot"`
		ContactEmail string `json:"contactEmail"`
		ContactName  string `json:"contactName"`
	}

	TruckInfo struct {
		TruckID      uint             `json:"truckId"`
		PickupCity   string           `json:"pickupCity"`
		PickupState  string           `json:"pickupState"`
		PickupZip    string           `json:"pickupZip,omitempty"`
		PickupDate   time.Time        `json:"pickupDate"`
		DropoffCity  string           `json:"dropoffCity"`
		DropoffState string           `json:"dropoffState"`
		DropoffZip   string           `json:"dropoffZip,omitempty"`
		DropoffDate  time.Time        `json:"dropoffDate,omitempty"`
		TruckType    models.TruckType `json:"truckType"`
		TruckLength  float32          `json:"truckLength,omitempty"`
	}

	TruckListResponse struct {
		IsSuccess      bool                 `json:"isSuccess"`
		TruckListID    uint                 `json:"truckListId"`
		Carrier        CarrierResult        `json:"carrier"`
		PostedBy       PostedByResult       `json:"postedBy"`
		CarrierContact CarrierContactResult `json:"carrierContact"`
		Trucks         []TruckResult        `json:"trucks"`
	}

	CarrierResult struct {
		Outcome string    `json:"outcome"`
		Result  []Carrier `json:"result"`
		Reasons []string  `json:"reasons"`
	}

	Carrier struct {
		CarrierID    uint   `json:"carrierId"`
		CarrierName  string `json:"carrierName"`
		CarrierMC    string `json:"carrierMc"`
		CarrierDOT   string `json:"carrierDot"`
		ContactEmail string `json:"contactEmail"`
		ContactName  string `json:"contactName"`
	}

	PostedByResult struct {
		Outcome string   `json:"outcome"`
		Result  PostedBy `json:"result"`
		Reasons []string `json:"reasons"`
	}

	PostedBy struct {
		PostedByID uint `json:"postedById"`
	}

	CarrierContactResult struct {
		Outcome string         `json:"outcome"`
		Result  CarrierContact `json:"result"`
		Reasons []string       `json:"reasons"`
	}

	CarrierContact struct {
		CarrierContactID uint `json:"carrierContactId"`
	}

	TruckResult struct {
		IsSuccess  bool            `json:"isSuccess"`
		TruckID    uint            `json:"truckId"`
		PickupDate DateResult      `json:"pickupDate"`
		Pickup     LocationResult  `json:"pickup"`
		Dropoff    LocationResult  `json:"dropoff"`
		Equipment  EquipmentResult `json:"equipment"`
		Posting    PostingResult   `json:"posting"`
	}

	DateResult struct {
		Outcome string   `json:"outcome"`
		Result  DateInfo `json:"result"`
		Reasons []string `json:"reasons"`
	}

	DateInfo struct {
		AvailableDate string `json:"availableDate"`
	}

	LocationResult struct {
		Outcome string       `json:"outcome"`
		Result  LocationInfo `json:"result"`
		Reasons []string     `json:"reasons"`
	}

	LocationInfo struct {
		City    string `json:"city"`
		State   string `json:"state"`
		ZipCode string `json:"zipCode"`
	}

	EquipmentResult struct {
		Outcome string        `json:"outcome"`
		Result  EquipmentInfo `json:"result"`
		Reasons []string      `json:"reasons"`
	}

	PostingResult struct {
		Outcome string             `json:"outcome"`
		Result  TruckPostingResult `json:"result"`
		Reasons []string           `json:"reasons"`
	}

	TruckPostingResult struct {
		PostingID uint `json:"postingId"`
	}

	EquipmentInfo struct {
		EquipmentID uint    `json:"equipmentId"`
		TruckType   string  `json:"truckType"`
		TruckLength float32 `json:"truckLength,omitempty"`
	}
)

func (r *Redwood) SubmitTruckList(
	ctx context.Context,
	isDraft bool,
	trucklist models.TruckList,
) (*models.TruckListErrors, error) {

	if len(trucklist.Trucks) == 0 {
		log.Error(
			ctx,
			"redwood.SubmitTruckList - unable to submit empty truck list",
			zap.Bool("isDraft", isDraft),
			zap.Any("truck list", trucklist),
		)

		return nil, errors.New("unable to submit empty truck list")
	}

	trucks := make([]TruckInfo, len(trucklist.Trucks))

	for i, t := range trucklist.Trucks {
		pickupLocation := t.PickupLocation.Applied
		pickupDate := t.PickupDate.Applied
		dropoffLocation := t.DropoffLocation.Applied
		dropoffDate := t.DropoffDate.Applied
		truckType := t.Type.Applied
		truckLength := t.Length.Applied

		// Only use suggestion values if the truck hasn't been submitted yet (e.g. pickupDate.Applied is empty)
		if (t.PickupDate.Applied == models.NullTime{}) {
			pickupLocation = t.PickupLocation.Suggestion
			pickupDate = t.PickupDate.Suggestion
			dropoffLocation = t.DropoffLocation.Suggestion
			dropoffDate = t.DropoffDate.Suggestion
			truckType = t.Type.Suggestion
			truckLength = t.Length.Suggestion
		}

		trucks[i] = TruckInfo{
			TruckID:      t.ID,
			PickupCity:   pickupLocation.City,
			PickupState:  pickupLocation.State,
			PickupZip:    pickupLocation.Zip,
			PickupDate:   pickupDate.Time,
			DropoffCity:  dropoffLocation.City,
			DropoffState: dropoffLocation.State,
			DropoffZip:   dropoffLocation.Zip,
			DropoffDate:  dropoffDate.Time,
			TruckType:    truckType,
			TruckLength:  truckLength,
		}
	}

	user, err := getUserByID(ctx, trucklist.UserID)
	if err != nil {
		return nil, errors.New("unable to find user for truck list")
	}

	payload := TruckListRequest{
		Metadata: MetadataInfo{
			MethodParamsInfo{
				ProcessID: "3d6e0575-4f7a-4307-be39-e0b576d2c754",
			},
		},
		Payload: PayloadInfo{
			TruckListID: trucklist.ID,
			UserName:    user.Name,
			UserEmail:   user.EmailAddress,
			Carrier: CarrierInfo{
				CarrierName:  trucklist.Carrier.Name,
				CarrierMC:    trucklist.Carrier.MC,
				CarrierDOT:   trucklist.Carrier.DOT,
				ContactEmail: trucklist.Carrier.ContactEmail,
				ContactName:  trucklist.Carrier.ContactName,
			},
			Trucks:  trucks,
			IsDraft: isDraft,
		},
	}

	postedByUserDomain := strings.ToLower(strings.Split(user.EmailAddress, "@")[1])
	if postedByUserDomain == "78n517.onmicrosoft.com" {
		payload.Payload.UserEmail = "<EMAIL>"
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	postTruckListURL := r.host + TruckListEndpoint

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, postTruckListURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("x-api-key", r.customer.APIKey)

	log.Info(
		ctx,
		"redwood.SubmitTruckList - Payload",
		zap.String("payload", string(reqBody)),
		zap.String("url", postTruckListURL),
	)

	res, err := httpRequest(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	log.Info(
		ctx,
		"redwood.SubmitTruckList - Response",
		zap.String("response", string(resBody)),
		zap.Int("HTTP Status Code", res.StatusCode),
		zap.String("url", postTruckListURL),
	)

	var dst TruckListResponse

	if err = json.Unmarshal(resBody, &dst); err != nil {
		return nil, err
	}

	if len(dst.Trucks) > 0 {
		for _, truck := range dst.Trucks {
			if truck.Dropoff.Outcome != "Success" {
				continue
			}

			trucklistIndex := slices.IndexFunc(trucklist.Trucks, func(tlTruck models.Truck) bool {
				return tlTruck.ID == truck.TruckID
			})

			if trucklistIndex == -1 {
				continue
			}

			for _, reason := range truck.Dropoff.Reasons {
				if PostTruckListSuccess(reason) != NullDestinationReplacedWithCarrierDomicile {
					continue
				}

				trucklist.Trucks[trucklistIndex].DropoffLocation = models.SuggestionAppliedPair[models.Address]{
					Applied: models.Address{
						City:  truck.Dropoff.Result.City,
						State: truck.Dropoff.Result.State,
					},
				}
				trucklist.Trucks[trucklistIndex].DropoffIsCarrierDomicile = true
			}
		}
	}

	trucklist.IsDraft = isDraft
	trucklist.Errors = models.TruckListErrors{}

	if dst.IsSuccess {
		err = updateTruckList(ctx, &trucklist)
		if err != nil {
			return nil, fmt.Errorf("unable to update successful truck list: %w", err)
		}

		return nil, nil
	}

	if dst.Carrier.Outcome == "Failure" {
		if trucklist.Errors.Carrier == nil {
			trucklist.Errors.Carrier = make(map[string][]models.CarrierInformation)
		}

		for _, reason := range dst.Carrier.Reasons {
			switch PostTruckListError(reason) {
			case NoCarriersFound:
				trucklist.Errors.Carrier[reason] = []models.CarrierInformation{}
			case MultipleCarriersFound:
				var carriers []models.CarrierInformation

				for _, c := range dst.Carrier.Result {
					carrier := models.CarrierInformation{
						Name:         c.CarrierName,
						MC:           c.CarrierMC,
						DOT:          c.CarrierDOT,
						ContactEmail: c.ContactEmail,
						ContactName:  c.ContactName,
					}

					carriers = append(carriers, carrier)
				}

				trucklist.Errors.Carrier[reason] = carriers
			case UnexpectedError:
				trucklist.Errors.Carrier[reason] = []models.CarrierInformation{}
			default:
				log.Warn(ctx, "parsing redwood carrier errors - unknown reason", zap.String("reason", reason))
			}
		}
	}

	if dst.CarrierContact.Outcome == "Failure" {
		if trucklist.Errors.CarrierContact == nil {
			trucklist.Errors.CarrierContact = []string{}
		}

		for _, reason := range dst.CarrierContact.Reasons {
			switch PostTruckListError(reason) {
			case NoCarrierContactFound:
				trucklist.Errors.CarrierContact = append(trucklist.Errors.CarrierContact, reason)
			case UnexpectedError:
				trucklist.Errors.CarrierContact = append(trucklist.Errors.CarrierContact,
					string(UnexpectedError))
			default:
				log.Warn(ctx, "parsing redwood carrier contact errors - unknown reason",
					zap.String("reason", reason))
			}
		}
	}

	if dst.PostedBy.Outcome == "Failure" {
		if trucklist.Errors.PostedBy == nil {
			trucklist.Errors.PostedBy = []string{}
		}

		for _, reason := range dst.PostedBy.Reasons {
			switch PostTruckListError(reason) {
			case PostedByNotFound:
				trucklist.Errors.PostedBy = append(trucklist.Errors.PostedBy, reason)
			case UnexpectedError:
				trucklist.Errors.PostedBy = append(trucklist.Errors.PostedBy,
					string(UnexpectedError))
			default:
				log.Warn(ctx, "parsing redwood posted-by errors - unknown reason",
					zap.String("reason", reason))
			}
		}
	}

	if len(dst.Trucks) > 0 {
		if trucklist.Errors.Truck == nil {
			trucklist.Errors.Truck = make(map[uint]models.TruckError)
		}

		for _, truckResult := range dst.Trucks {
			if truckResult.IsSuccess {
				continue
			}

			truckError := models.TruckError{
				PickupDate:      []string{},
				PickupLocation:  []string{},
				DropoffLocation: []string{},
				Equipment:       []string{},
				Posting:         []string{},
			}

			if truckResult.PickupDate.Outcome == "Failure" {
				for _, reason := range truckResult.PickupDate.Reasons {
					switch PostTruckListError(reason) {
					case PickupDateIsRequired, PickupDateIsPast, PickupDateIsOutOfRange:
						truckError.PickupDate = append(truckError.PickupDate, reason)
					case UnexpectedError:
						truckError.PickupDate = append(truckError.PickupDate,
							string(UnexpectedError))
					default:
						log.Warn(ctx, "parsing redwood pickup date errors - unknown reason",
							zap.String("reason", reason))
					}
				}
			}

			if truckResult.Pickup.Outcome == "Failure" {
				for _, reason := range truckResult.Pickup.Reasons {
					switch PostTruckListError(reason) {
					case PickupNotFound:
						truckError.PickupLocation = append(truckError.PickupLocation, reason)
					case UnexpectedError:
						truckError.PickupLocation = append(truckError.PickupLocation,
							string(UnexpectedError))
					default:
						log.Warn(ctx, "parsing redwood pickup location errors - unknown reason",
							zap.String("reason", reason))
					}
				}
			}

			if truckResult.Dropoff.Outcome == "Failure" {
				for _, reason := range truckResult.Dropoff.Reasons {
					switch PostTruckListError(reason) {
					case CarrierDomicileNotFound, DestinationNeedsMoreThanCity,
						DestinationCityStateNotFound, DestinationStateNotFound,
						DestinationZipNotFound, DestinationCityZipNotFound,
						DestinationStateZipNotFound:

						truckError.DropoffLocation = append(truckError.DropoffLocation, reason)
					case UnexpectedError:
						truckError.DropoffLocation = append(truckError.DropoffLocation,
							string(UnexpectedError))
					default:
						log.Warn(ctx, "parsing redwood dropoff location errors - unknown reason",
							zap.String("reason", reason))
					}
				}
			}

			if truckResult.Equipment.Outcome == "Failure" {
				for _, reason := range truckResult.Equipment.Reasons {
					switch PostTruckListError(reason) {
					case InvalidTruckType, NoMatchingEquipment, NoMatchingTrailerType, EquipmentNoCarriersFound:
						truckError.Equipment = append(truckError.Equipment, reason)
					case UnexpectedError:
						truckError.Equipment = append(truckError.Equipment, string(UnexpectedError))
					default:
						log.Warn(ctx, "parsing redwood equipment errors - unknown reason", zap.String("reason", reason))
					}
				}
			}

			if truckResult.Posting.Outcome == "Failure" {
				for _, reason := range truckResult.Equipment.Reasons {
					switch PostTruckListError(reason) {
					case TruckNotPosted:
						truckError.Posting = append(truckError.Posting, reason)
					case UnexpectedError:
						truckError.Posting = append(truckError.Posting, string(UnexpectedError))
					default:
						log.Warn(ctx, "parsing redwood posting errors - unknown reason", zap.String("reason", reason))
					}
				}
			}

			trucklist.Errors.Truck[truckResult.TruckID] = truckError
		}
	}

	err = updateTruckList(ctx, &trucklist)
	if err != nil {
		return nil, fmt.Errorf("unable to update truck list with errors: %w", err)
	}

	if trucklist.Errors.HasErrors() {
		return &trucklist.Errors, nil
	}

	return nil, nil
}

// BuildTruckListErrorMessage constructs a human-readable error message.
func BuildTruckListErrorMessage(errors models.TruckListErrors) string {
	var msgs []string

	// Carrier Errors
	if len(errors.Carrier) > 0 {
		for reason := range errors.Carrier {
			if message, exists := errorMessages[PostTruckListError(reason)]; exists {
				msgs = append(msgs, fmt.Sprintf("Carrier Error: %s", message))
			} else {
				msgs = append(msgs, fmt.Sprintf("Carrier Error: %s", reason))
			}
		}
	}

	// Carrier Contact Errors
	if len(errors.CarrierContact) > 0 {
		for _, reason := range errors.CarrierContact {
			if message, exists := errorMessages[PostTruckListError(reason)]; exists {
				msgs = append(msgs, fmt.Sprintf("Carrier Contact Error: %s", message))
			} else {
				msgs = append(msgs, fmt.Sprintf("Carrier Contact Error: %s", reason))
			}
		}
	}

	// Truck Errors
	if len(errors.Truck) > 0 {
		for truckID, truckError := range errors.Truck {
			var truckMsgs []string

			// Pickup Date Errors
			for _, reason := range truckError.PickupDate {
				if message, exists := errorMessages[PostTruckListError(reason)]; exists {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Pickup Date Error: %s", message))
				} else {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Pickup Date Error: %s", reason))
				}
			}

			// Pickup Location Errors
			for _, reason := range truckError.PickupLocation {
				if message, exists := errorMessages[PostTruckListError(reason)]; exists {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Pickup Location Error: %s", message))
				} else {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Pickup Location Error: %s", reason))
				}
			}

			// Dropoff Location Errors
			for _, reason := range truckError.DropoffLocation {
				if message, exists := errorMessages[PostTruckListError(reason)]; exists {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Dropoff Location Error: %s", message))
				} else {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Dropoff Location Error: %s", reason))
				}
			}

			// Posting Errors (Equipment Errors)
			for _, reason := range truckError.Posting {
				if message, exists := errorMessages[PostTruckListError(reason)]; exists {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Equipment Error: %s", message))
				} else {
					truckMsgs = append(truckMsgs, fmt.Sprintf("Equipment Error: %s", reason))
				}
			}

			if len(truckMsgs) > 0 {
				msgs = append(
					msgs,
					fmt.Sprintf("Truck ID %d Errors:\n%s", truckID, strings.Join(truckMsgs, "\n")),
				)
			}
		}
	}

	return strings.Join(msgs, "\n\n")
}
