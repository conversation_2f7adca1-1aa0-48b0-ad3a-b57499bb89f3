package trident

import (
	"context"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	Stop struct {
		QuickQuoteID string `json:"quickQuoteId,omitempty"`
		Order        int    `json:"order"`
		City         string `json:"city"`
		State        string `json:"state"`
		Zip          string `json:"zip"`
		Country      string `json:"country"`
	}
	GreenscreensSubmission struct {
		ID                             string  `json:"id"`
		UserEmail                      string  `json:"userEmail"`
		CustomerEmail                  string  `json:"customerEmail"`
		Stops                          []Stop  `json:"stops"`
		SelectedRateName               string  `json:"selectedRateName"`
		NetworkLaneRateDistance        float64 `json:"networkLaneRateDistance"`
		NetworkLaneRateTargetBuy       float64 `json:"networkLaneRateTargetBuy"`
		NetworkLaneRateConfidenceLevel int     `json:"networkLaneRateConfidenceLevel"`
		LaneRateDistance               float64 `json:"laneRateDistance"`
		LaneRateTargetBuy              float64 `json:"laneRateTargetBuy"`
		LaneRateConfidenceLevel        int     `json:"laneRateConfidenceLevel"`
	}

	GreenscreensSubmissionResponse struct {
		ID                             string  `json:"id"`
		UserEmail                      string  `json:"userEmail"`
		CustomerEmail                  string  `json:"customerEmail"`
		Stops                          []Stop  `json:"stops"`
		SelectedRateName               string  `json:"selectedRateName"`
		NetworkLaneRateDistance        float64 `json:"networkLaneRateDistance"`
		NetworkLaneRateTargetBuy       float64 `json:"networkLaneRateTargetBuy"`
		NetworkLaneRateConfidenceLevel int     `json:"networkLaneRateConfidenceLevel"`
		LaneRateDistance               float64 `json:"laneRateDistance"`
		LaneRateTargetBuy              float64 `json:"laneRateTargetBuy"`
		LaneRateConfidenceLevel        int     `json:"laneRateConfidenceLevel"`
	}
)

func (t *Trident) PostGSQuote(
	ctx context.Context,
	quote GreenscreensSubmission,
) (resp *GreenscreensSubmissionResponse, err error) {

	log.Info(ctx, "Posting Greenscreens quote to service", zap.String("service name:", string(t.customer.Name)))

	err = post(ctx, t.host+"/quick-quote/greenscreens?apikey="+t.customer.APIKey, nil, quote, &resp)
	return resp, err
}
