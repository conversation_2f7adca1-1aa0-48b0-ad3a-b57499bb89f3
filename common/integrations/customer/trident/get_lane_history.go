package trident

import (
	"context"
	"encoding/json"
	"errors"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	LaneHistoryRequest struct {
		OriginZip      string `json:"originZip"`
		DestinationZip string `json:"destinationZip"`
		Equipment      string `json:"equipment"`
	}

	LaneHistoryResponse struct {
		Origin      string `json:"origin"`      // will be zip code
		Destination string `json:"destination"` // will be zip code
		Equipment   string `json:"equipment"`
		// Month 1 data
		ReportsMonth1 json.Number `json:"reportsMonth1"`
		HighMonth1    json.Number `json:"HighMonth1"`
		P75Month1     json.Number `json:"p75Month1"`
		LowMonth1     json.Number `json:"lowMonth1"`
		P25Month1     json.Number `json:"p25Month1"`
		RateMonth1    json.Number `json:"rateMonth1"`
		// Month 2 data
		ReportsMonth2 json.Number `json:"reportsMonth2"`
		HighMonth2    json.Number `json:"HighMonth2"`
		P75Month2     json.Number `json:"p75Month2"`
		LowMonth2     json.Number `json:"lowMonth2"`
		P25Month2     json.Number `json:"p25Month2"`
		RateMonth2    json.Number `json:"rateMonth2"`
		// Month 3 data
		ReportsMonth3 json.Number `json:"reportsMonth3"`
		HighMonth3    json.Number `json:"HighMonth3"`
		P75Month3     json.Number `json:"p75Month3"`
		LowMonth3     json.Number `json:"lowMonth3"`
		P25Month3     json.Number `json:"p25Month3"`
		RateMonth3    json.Number `json:"rateMonth3"`
		// Month 4 data
		ReportsMonth4 json.Number `json:"reportsMonth4"`
		HighMonth4    json.Number `json:"HighMonth4"`
		P75Month4     json.Number `json:"p75Month4"`
		LowMonth4     json.Number `json:"lowMonth4"`
		P25Month4     json.Number `json:"p25Month4"`
		RateMonth4    json.Number `json:"rateMonth4"`
		// Month 5 data
		ReportsMonth5 json.Number `json:"reportsMonth5"`
		HighMonth5    json.Number `json:"HighMonth5"`
		P75Month5     json.Number `json:"p75Month5"`
		LowMonth5     json.Number `json:"lowMonth5"`
		P25Month5     json.Number `json:"p25Month5"`
		RateMonth5    json.Number `json:"rateMonth5"`
		// Month 6 data
		ReportsMonth6 json.Number `json:"reportsMonth6"`
		HighMonth6    json.Number `json:"HighMonth6"`
		P75Month6     json.Number `json:"p75Month6"`
		LowMonth6     json.Number `json:"lowMonth6"`
		P25Month6     json.Number `json:"p25Month6"`
		RateMonth6    json.Number `json:"rateMonth6"`
		// Week data
		ReportsWeek json.Number `json:"reportsWeek"`
		AvgHighWeek json.Number `json:"avgHighWeek"`
		AvgLowWeek  json.Number `json:"avgLowWeek"`
		AvgRateWeek json.Number `json:"avgRateWeek"`
	}

	LaneHistoryNumerical struct {
		// Month 1 data
		ReportsMonth1 int64   `json:"reportsMonth1"`
		HighMonth1    float64 `json:"highMonth1"`
		P75Month1     float64 `json:"p75Month1"`
		LowMonth1     float64 `json:"lowMonth1"`
		P25Month1     float64 `json:"p25Month1"`
		RateMonth1    float64 `json:"rateMonth1"`
		// Month 2 data
		ReportsMonth2 int64   `json:"reportsMonth2"`
		HighMonth2    float64 `json:"highMonth2"`
		P75Month2     float64 `json:"p75Month2"`
		LowMonth2     float64 `json:"lowMonth2"`
		P25Month2     float64 `json:"p25Month2"`
		RateMonth2    float64 `json:"rateMonth2"`
		// Month 3 data
		ReportsMonth3 int64   `json:"reportsMonth3"`
		HighMonth3    float64 `json:"highMonth3"`
		P75Month3     float64 `json:"p75Month3"`
		LowMonth3     float64 `json:"lowMonth3"`
		P25Month3     float64 `json:"p25Month3"`
		RateMonth3    float64 `json:"rateMonth3"`
		// Month 4 data
		ReportsMonth4 int64   `json:"reportsMonth4"`
		HighMonth4    float64 `json:"highMonth4"`
		P75Month4     float64 `json:"p75Month4"`
		LowMonth4     float64 `json:"lowMonth4"`
		P25Month4     float64 `json:"p25Month4"`
		RateMonth4    float64 `json:"rateMonth4"`
		// Month 5 data
		ReportsMonth5 int64   `json:"reportsMonth5"`
		HighMonth5    float64 `json:"highMonth5"`
		P75Month5     float64 `json:"p75Month5"`
		LowMonth5     float64 `json:"lowMonth5"`
		P25Month5     float64 `json:"p25Month5"`
		RateMonth5    float64 `json:"rateMonth5"`
		// Month 6 data
		ReportsMonth6 int64   `json:"reportsMonth6"`
		HighMonth6    float64 `json:"highMonth6"`
		P75Month6     float64 `json:"p75Month6"`
		LowMonth6     float64 `json:"lowMonth6"`
		P25Month6     float64 `json:"p25Month6"`
		RateMonth6    float64 `json:"rateMonth6"`
		// Week data
		ReportsWeek int64   `json:"reportsWeek"`
		AvgHighWeek float64 `json:"avgHighWeek"`
		AvgLowWeek  float64 `json:"avgLowWeek"`
		AvgRateWeek float64 `json:"avgRateWeek"`
	}
)

const tripPath = "/stats/trip"

func (c *Trident) GetLaneHistory(ctx context.Context, req LaneHistoryRequest) (*LaneHistoryResponse, error) {
	var result LaneHistoryResponse

	query := url.Values{}
	query.Add("origin", req.OriginZip)
	query.Add("dest", req.DestinationZip)
	query.Add("equip", req.Equipment)
	query.Add("apikey", c.customer.APIKey)

	err := get(ctx, c.host+tripPath+"?"+query.Encode(), nil, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// ParseCalculateLaneHistory parses a subset of LaneHistoryResponse into a LaneHistoryNumerical struct,
// converting the respective fields to float64 and int64 values.
// If any conversion fails, it logs an error and returns an error message.
func ParseCalculateLaneHistory(
	ctx context.Context,
	resp *LaneHistoryResponse,
) (laneHistoryNumerical LaneHistoryNumerical, err error) {
	// Month 1 data
	laneHistoryNumerical.ReportsMonth1, err = resp.ReportsMonth1.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth1 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth1 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth1, err = resp.HighMonth1.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth1 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth1 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month1, err = resp.P75Month1.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month1 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month1 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth1, err = resp.LowMonth1.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth1 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth1 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month1, err = resp.P25Month1.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month1 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month1 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth1, err = resp.RateMonth1.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth1 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth1 as float from Trident DAT")
	}

	// Month 2 data
	laneHistoryNumerical.ReportsMonth2, err = resp.ReportsMonth2.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth2 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth2 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth2, err = resp.HighMonth2.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth2 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth2 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month2, err = resp.P75Month2.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month2 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month2 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth2, err = resp.LowMonth2.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth2 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth2 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month2, err = resp.P25Month2.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month2 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month2 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth2, err = resp.RateMonth2.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth2 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth2 as float from Trident DAT")
	}

	// Month 3 data
	laneHistoryNumerical.ReportsMonth3, err = resp.ReportsMonth3.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth3 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth3 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth3, err = resp.HighMonth3.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth3 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth3 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month3, err = resp.P75Month3.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month3 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month3 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth3, err = resp.LowMonth3.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth3 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth3 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month3, err = resp.P25Month3.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month3 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month3 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth3, err = resp.RateMonth3.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth3 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth3 as float from Trident DAT")
	}

	// Month 4 data
	laneHistoryNumerical.ReportsMonth4, err = resp.ReportsMonth4.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth4 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth4 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth4, err = resp.HighMonth4.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth4 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth4 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month4, err = resp.P75Month4.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month4 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month4 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth4, err = resp.LowMonth4.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth4 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth4 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month4, err = resp.P25Month4.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month4 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month4 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth4, err = resp.RateMonth4.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth4 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth4 as float from Trident DAT")
	}

	// Month 5 data
	laneHistoryNumerical.ReportsMonth5, err = resp.ReportsMonth5.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth5 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth5 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth5, err = resp.HighMonth5.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth5 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth5 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month5, err = resp.P75Month5.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month5 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month5 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth5, err = resp.LowMonth5.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth5 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth5 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month5, err = resp.P25Month5.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month5 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month5 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth5, err = resp.RateMonth5.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth5 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth5 as float from Trident DAT")
	}

	// Month 6 data
	laneHistoryNumerical.ReportsMonth6, err = resp.ReportsMonth6.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsMonth6 as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsMonth6 as int64 from Trident DAT")
	}

	laneHistoryNumerical.HighMonth6, err = resp.HighMonth6.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highMonth6 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse highMonth6 as float from Trident DAT")
	}

	laneHistoryNumerical.P75Month6, err = resp.P75Month6.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p75Month6 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p75Month6 as float from Trident DAT")
	}

	laneHistoryNumerical.LowMonth6, err = resp.LowMonth6.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowMonth6 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse lowMonth6 as float from Trident DAT")
	}

	laneHistoryNumerical.P25Month6, err = resp.P25Month6.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse p25Month6 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse p25Month6 as float from Trident DAT")
	}

	laneHistoryNumerical.RateMonth6, err = resp.RateMonth6.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse rateMonth6 as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse rateMonth6 as float from Trident DAT")
	}

	// Week data
	laneHistoryNumerical.ReportsWeek, err = resp.ReportsWeek.Int64()
	if err != nil {
		log.Error(ctx, "failed to parse reportsWeek as int64 from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse reportsWeek as int64 from Trident DAT")
	}

	laneHistoryNumerical.AvgHighWeek, err = resp.AvgHighWeek.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse avgHighWeek as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse avgHighWeek as float from Trident DAT")
	}

	laneHistoryNumerical.AvgLowWeek, err = resp.AvgLowWeek.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse avgLowWeek as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse avgLowWeek as float from Trident DAT")
	}

	laneHistoryNumerical.AvgRateWeek, err = resp.AvgRateWeek.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse avgRateWeek as float from Trident DAT", zap.Error(err))
		return LaneHistoryNumerical{}, errors.New("failed to parse avgRateWeek as float from Trident DAT")
	}

	return laneHistoryNumerical, nil
}
