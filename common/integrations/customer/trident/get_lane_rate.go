package trident

import (
	"context"
	"encoding/json"
	"errors"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	LaneRateRequest struct {
		OriginZip      string `json:"originZip"`
		DestinationZip string `json:"destinationZip"`
		Equipment      string `json:"equipment"`
	}

	LaneRateResponse struct {
		Origin      string      `json:"origin"`      // will be zip code
		Destination string      `json:"destination"` // will be zip code
		Equipment   string      `json:"equipment"`
		RateType    string      `json:"rateType"`
		Year        string      `json:"year"`
		Month       string      `json:"month"`
		Reports     json.Number `json:"reports"`
		Mileage     json.Number `json:"mileage"`
		Companies   json.Number `json:"companies"`
		Confidence  json.Number `json:"confidence"` // Trident DAT confidence is a Standard Deviation
		// Per Mile data
		RatePerMile    json.Number `json:"ratePerMile"`
		HighPerMile    json.Number `json:"highPerMile"`
		LowPerMile     json.Number `json:"lowPerMile"`
		AvgFuelPerMile json.Number `json:"avgFuelPerMile"`
		// Per Trip data
		RatePerTrip    json.Number `json:"ratePerTrip"`
		HighPerTrip    json.Number `json:"highPerTrip"`
		LowPerTrip     json.Number `json:"lowPerTrip"`
		AvgFuelPerTrip json.Number `json:"avgFuelPerTrip"`
		// Timeframe and geographical data
		Timeframe       string `json:"esc_timeframe"`
		OriginName      string `json:"esc_origin_name"`
		OriginType      string `json:"esc_origin_type"`
		DestinationName string `json:"esc_destination_name"`
		DestinationType string `json:"esc_destination_type"`
	}

	LaneRateNumerical struct {
		Mileage    float64 `json:"mileage"`
		Confidence float64 `json:"confidence"`
		// Per Trip data
		RatePerTrip    float64 `json:"ratePerTrip"`
		HighPerTrip    float64 `json:"highPerTrip"`
		LowPerTrip     float64 `json:"lowPerTrip"`
		AvgFuelPerTrip float64 `json:"avgFuelPerTrip"`
		// Per Mile data
		RatePerMile    float64 `json:"ratePerMile"`
		HighPerMile    float64 `json:"highPerMile"`
		LowPerMile     float64 `json:"lowPerMile"`
		AvgFuelPerMile float64 `json:"avgFuelPerMile"`
	}
)

func (c *Trident) GetLaneRate(ctx context.Context, req LaneRateRequest) (*LaneRateResponse, error) {
	var result LaneRateResponse

	err := get(ctx, c.host+"/lane-rate?apikey="+c.customer.APIKey, nil, req, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// ParseCalculateLaneRate parses a subset of LaneRateResponse into a LaneRateNumerical struct,
// converting the respective fields to float64 and calculating fuel-adjusted rate values.
// If any conversion fails, it logs an error and returns an error message.
func ParseCalculateLaneRate(
	ctx context.Context,
	resp *LaneRateResponse,
) (laneRateNumerical LaneRateNumerical, err error) {
	laneRateNumerical.RatePerTrip, err = resp.RatePerTrip.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse ratePerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse ratePerTrip as float from Trident DAT")
	}

	laneRateNumerical.LowPerTrip, err = resp.LowPerTrip.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse lowPerTrip as float from Trident DAT")
	}

	laneRateNumerical.HighPerTrip, err = resp.HighPerTrip.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse highPerTrip as float from Trident DAT")
	}

	laneRateNumerical.AvgFuelPerTrip, err = resp.AvgFuelPerTrip.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse avgFuelPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse avgFuelPerTrip as float from Trident DAT")
	}

	laneRateNumerical.RatePerMile, err = resp.RatePerMile.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse ratePerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse ratePerTrip as float from Trident DAT")
	}

	laneRateNumerical.LowPerMile, err = resp.LowPerMile.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse lowPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse lowPerTrip as float from Trident DAT")
	}

	laneRateNumerical.HighPerMile, err = resp.HighPerMile.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse highPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse highPerTrip as float from Trident DAT")
	}

	laneRateNumerical.AvgFuelPerMile, err = resp.AvgFuelPerMile.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse avgFuelPerTrip as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse avgFuelPerTrip as float from Trident DAT")
	}

	laneRateNumerical.Mileage, err = resp.Mileage.Float64()
	if err != nil {
		log.Error(ctx, "failed to parse mileage as float from Trident DAT", zap.Error(err))
		return LaneRateNumerical{}, errors.New("failed to parse mileage as float from Trident DAT")
	}

	laneRateNumerical.Confidence, err = resp.Confidence.Float64()
	if err != nil {
		log.Warn(ctx, "failed to parse confidence as float from Trident DAT", zap.Error(err))
	}

	laneRateNumerical.RatePerTrip += laneRateNumerical.AvgFuelPerTrip
	laneRateNumerical.LowPerTrip += laneRateNumerical.AvgFuelPerTrip
	laneRateNumerical.HighPerTrip += laneRateNumerical.AvgFuelPerTrip

	laneRateNumerical.RatePerMile += laneRateNumerical.AvgFuelPerMile
	laneRateNumerical.LowPerMile += laneRateNumerical.AvgFuelPerMile
	laneRateNumerical.HighPerMile += laneRateNumerical.AvgFuelPerMile

	return laneRateNumerical, nil
}
