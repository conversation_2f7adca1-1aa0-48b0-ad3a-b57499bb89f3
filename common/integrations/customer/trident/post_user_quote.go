package trident

import (
	"context"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type UserSubmission struct {
	ID            string  `json:"id"`
	UserEmail     string  `json:"userEmail"`
	CustomerEmail string  `json:"customerEmail"`
	GSQuoteID     string  `json:"gsQuoteID"`
	Stops         []Stop  `json:"stops"`
	CarrierCost   float64 `json:"carrierCost"`
	Margin        float64 `json:"margin"`
	MarginType    string  `json:"marginType"`
	TargetSell    float64 `json:"targetSell"`
	DraftResponse string  `json:"draftResponse"`
}

type UserSubmissionResponse struct {
	ID            string  `json:"id"`
	GSQuoteID     string  `json:"gsQuoteId"`
	UserEmail     string  `json:"userEmail"`
	CustomerEmail string  `json:"customerEmail"`
	CarrierCost   float64 `json:"carrierCost"`
	Margin        float64 `json:"margin"`
	TargetSell    float64 `json:"targetSell"`
	DraftResponse string  `json:"draftResponse"`
	Stops         []Stop  `json:"stops"`
}

func (t *Trident) PostUserQuote(ctx context.Context, quote UserSubmission) (resp *UserSubmissionResponse, err error) {
	log.Info(ctx, "Posting user's quote to service", zap.String("service name:", string(t.customer.Name)))

	err = post(ctx, t.host+"/quick-quote/trident?apikey="+t.customer.APIKey, nil, quote, &resp)
	return resp, err
}
