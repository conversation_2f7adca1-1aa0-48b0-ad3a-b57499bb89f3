package globaltranz

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
)

type (
	Location struct {
		Latitude     float32 `json:"latitude"`
		Longitude    float32 `json:"longitude"`
		RadiusMiles  int     `json:"radiusMiles"`
		IncludeState bool    `json:"includeState"`
		ExcludeState bool    `json:"excludeState"`
		City         string  `json:"city"`
		StateCode    string  `json:"stateCode"`
		ZipCode      string  `json:"zipCode"`
		CountryID    int     `json:"countryId"`
	}

	ServiceType struct {
		ID          int    `json:"id"`
		DisplayText string `json:"displayText"`
	}

	Services struct {
		AdditionalServices []string `json:"additionalServices"`
		ServiceType        int      `json:"serviceType"`
	}

	GetLaneCarrierListRequest struct {
		AdditionalFleetOptions []string    `json:"additionalFleetOptions"`
		ServiceOptions         []string    `json:"serviceOptions"`
		InsuranceCost          []string    `json:"insuranceCost"`
		Compliance             []string    `json:"compliance"`
		Accessorials           []string    `json:"accessorials"`
		SafetyRatings          []string    `json:"safetyRatings"`
		Origin                 Location    `json:"origin"`
		Destination            Location    `json:"destination"`
		EquipmentType          int         `json:"equipmentType"`
		Services               Services    `json:"services"`
		ServiceType            ServiceType `json:"serviceType"`
		BookableCarriersOnly   bool        `json:"bookableCarriersOnly"`
		IncludeLoads           bool        `json:"includeLoads"`
		IncludeQuotes          bool        `json:"includeQuotes"`
	}

	ResponseDetails struct {
		Message  string `json:"message"`
		DidError bool   `json:"didError"`
		// TODO: Confirm this, couldn't find responses where this was populated when implementing
		ErrorMessages []string `json:"errorMessages"`
	}

	CarrierCostDetails struct {
		AverageCost        float32 `json:"averageCost"`
		MinimumCost        float32 `json:"minimumCost"`
		AverageRatePerMile float32 `json:"averageRatePerMile"`
	}

	CarrierCountDetails struct {
		Shipment              int `json:"shipment"`
		InspectionAlongRoute  int `json:"inspectionAlongRoute"`
		OriginInspection      int `json:"originInspection"`
		DestinationInspection int `json:"destinationInspection"`
	}

	CarrierItem struct {
		MCNumber         string              `json:"mcNumber"`
		USDotNumber      string              `json:"usDotNumber"`
		Bookable         bool                `json:"bookable"`
		CostDetails      CarrierCostDetails  `json:"costDetails"`
		CountDetails     CarrierCountDetails `json:"countDetails"`
		CarrierBk        int                 `json:"carrierBk"`
		CarrierName      string              `json:"carrierName"`
		HeadquarterState string              `json:"headquarterState"`
		PhoneNumber      string              `json:"phoneNumber"`
	}

	GetLaneCarrierListResponse struct {
		ResponseDetails ResponseDetails `json:"responseDetails"`
		Carriers        []CarrierItem   `json:"model"`
	}
)

// GetLaneCarrierList retrieves carriers that have operated on a given lane.
func (c Client) GetLaneCarrierList(
	ctx context.Context,
	req *GetLaneCarrierListRequest,
) (*GetLaneCarrierListResponse, error) {
	var result GetLaneCarrierListResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)
	if _, err = c.post(ctx, c.mainSystemHost, "CarrierSource/SourceInfo", body, &result); err != nil {
		return nil, err
	}

	return &result, nil
}
