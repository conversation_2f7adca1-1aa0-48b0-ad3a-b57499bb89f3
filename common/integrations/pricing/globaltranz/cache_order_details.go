package globaltranz

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

func redisOrderDetailsLookupKey(orderBK string) string {
	return fmt.Sprintf("order-details-%s", orderBK)
}

func getRedisOrderDetails(ctx context.Context, orderBK string) (orderDetails OrderDetails, err error) {
	redisKey := redisOrderDetailsLookupKey(orderBK)

	orderDetails, _, err = redis.GetKey[OrderDetails](ctx, redisKey)
	if err != nil {
		return OrderDetails{}, err
	}

	log.Info(ctx, "found cached GlobalTranz order details in Redis", zap.String("orderBK", orderBK))
	return orderDetails, nil
}

func setRedisOrderDetails(ctx context.Context, orderBK string, orderDetails OrderDetails) error {
	redisKey := redisOrderDetailsLookupKey(orderBK)
	if err := redis.SetKey(ctx, redisKey, orderDetails, 30*24*time.Hour); err != nil {

		log.WarnNoSentry(
			ctx,
			"error setting cached GlobalTranz order details in redis",
			zap.Error(err),
			zap.String("orderBK", orderBK),
		)
		return err
	}

	log.Info(
		ctx,
		"cached GlobalTranz order details stored successfully on redis",
		zap.String("redisKey", redisKey),
		zap.String("orderBK", orderBK),
	)
	return nil
}
