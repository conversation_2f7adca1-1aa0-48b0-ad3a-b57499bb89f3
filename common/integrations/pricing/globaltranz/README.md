# GlobalTranz Go SDK

A simplified Go SDK for the [GlobalTranz API](https://tms.globaltranz.com/).

## Authentication
Authentication for GlobalTranz in order to get the access token happens on the is.globaltranz.com subdomain, which is an [IdentityServer4](https://identityserver4.readthedocs.io/en/latest/) client. IS4 uses the OpenID Connect (OIDC) and OAuth 2.0 authentication protocols.

The image below provides an overall idea of how authentication through IS4 works:
![Authorization flow with OIDC and Oauth2](./docs/oidc-oauth2.png)

Notes:
- The "User" entity doesn't manually login or authenticate, but rather uses Drumkit as a proxy by providing login credentials;
  - This also means Step 1 is yet another API call to the IS4 host - namely to the `/Account/Login` route;
- The "App" entity in this case refer to the Drumkit app, responsible for handling all authentication steps and storing the final access token.
- The "Auth0 Tenant" entity refers to GlobalTranz's IS4 subdomain, and "Your API" refers to the actual GlobalTranz API we fetch data from.

### Implementation details

- The initial login step returns `set-cookie` headers that are required for subsequent requests - hence why a Cookie Jar is essential.
- The following authorization step resolves to a `302 Found` redirect to the required `ReturnUrl` parameter, however we need to intercept the response headers and extract `code` from the `Location` header. This is why we use a `http.Client` with a custom `CheckRedirect` function that prevents redirects.
- The final authorization step simply exchanges that `code` parameter, along with a `code_verifier` from the initially created `code_challenge`, for the final access token.

### References
For more details on both the DAT and GlobalTranz integrations, please check the Lunch & Learn we had going over their implementation:
- Slide Deck at https://gamma.app/docs/Quoting-with-DAT-and-GlobalTranz-l88plil8ja5k9as