package globaltranz

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

// Lane History redis keys follow the format: lane-history-MA-NY-VAN
func redisLaneHistoryLookupKey(req *GetLaneHistoryRequest) string {
	return fmt.Sprintf(
		"globaltranz-lane-history-%s-%s-%s",
		strings.ToUpper(req.OriginState),
		strings.ToUpper(req.DestinationState),
		strings.ToUpper(req.TransportType),
	)
}

func GetRedisLaneHistory(ctx context.Context, req *GetLaneHistoryRequest) (*GetLaneHistoryResponse, error) {
	redisKey := redisLaneHistoryLookupKey(req)

	laneHistory, _, err := redis.GetKey[GetLaneHistoryResponse](ctx, redisKey)
	if err != nil {
		return nil, err
	}

	log.Info(ctx, "found cached GlobalTranz lane history in Redis", zap.String("redisKey", redisKey))
	return &laneHistory, nil
}

func SetRedisLaneHistory(ctx context.Context, req *GetLaneHistoryRequest, laneHistory *GetLaneHistoryResponse) error {
	redisKey := redisLaneHistoryLookupKey(req)
	if err := redis.SetKey(ctx, redisKey, laneHistory, 24*time.Hour); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting cached GlobalTranz lane history in redis",
			zap.Error(err),
			zap.String("redisKey", redisKey),
		)
		return err
	}

	log.Info(
		ctx,
		"cached GlobalTranz lane history stored successfully on redis",
		zap.String("redisKey", redisKey),
	)
	return nil
}
