package globaltranz

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

func redisCarrierHistoryLookupKey(carrierName string) string {
	return fmt.Sprintf("carrier-history-%s", carrierName)
}

func getRedisCarrierHistory(ctx context.Context, carrierName string) (carrierHistory []HistoryItem, err error) {
	redisKey := redisCarrierHistoryLookupKey(carrierName)

	carrierHistory, _, err = redis.GetKey[[]HistoryItem](ctx, redisKey)
	if err != nil {
		return nil, err
	}

	log.Info(ctx, "found cached GlobalTranz carrier history in Redis", zap.String("carrier name", carrierName))
	return carrierHistory, nil
}

func setRedisCarrierHistory(ctx context.Context, carrierName string, carrierHistory []HistoryItem) error {
	redisKey := redisCarrierHistoryLookupKey(carrierName)
	if err := redis.SetKey(ctx, redisKey, carrierHistory, 24*time.Hour); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting cached GlobalTranz carrier history in redis",
			zap.Error(err),
			zap.Any("carrier name", carrierName),
		)
		return err
	}

	log.Info(
		ctx,
		"cached GlobalTranz carrier history stored successfully on redis",
		zap.Any("redisKey", redisKey),
	)
	return nil
}
