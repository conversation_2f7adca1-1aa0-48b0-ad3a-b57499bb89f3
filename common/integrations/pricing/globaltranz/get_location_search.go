package globaltranz

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	LocationItem struct {
		Street             string  `json:"street"`
		City               string  `json:"city"`
		StateCode          string  `json:"stateCode"`
		State              string  `json:"state"`
		Zip                string  `json:"zip"`
		CountryCode        int     `json:"countryCode"`
		Country            string  `json:"country"`
		Zone               string  `json:"zone"`
		CountyAbbreviation string  `json:"countyAbbreviation"`
		Latitude           float32 `json:"latitude"`
		Longitude          float32 `json:"longitude"`
		IsCanada           bool    `json:"isCanada"`
		IsMexico           bool    `json:"isMexico"`
		IsHawaii           bool    `json:"isHawaii"`
		IsAlaska           bool    `json:"isAlaska"`
	}

	GetLocationSearchResponse struct {
		ResponseDetails ResponseDetails `json:"responseDetails"`
		Locations       []LocationItem  `json:"model"`
	}
)

// GetLocationSearch retrieves location options for a given keyword.
func (c Client) GetLocationSearch(
	ctx context.Context,
	city, state, zipcode string,
) (*GetLocationSearchResponse, error) {
	var err error
	var result GetLocationSearchResponse

	if zipcode != "" {
		err = c.searchGlobalTranzLocation(ctx, zipcode, &result)

		if err != nil {
			log.Warn(ctx, "error searching GlobalTranz zipcode location", zap.Error(err))
		} else if len(result.Locations) > 0 {
			return &result, nil
		}
	}

	if city != "" && state != "" {
		err = c.searchGlobalTranzLocation(ctx, fmt.Sprintf("%s, %s", city, state), &result)

		if err != nil {
			log.Warn(ctx, "error searching GlobalTranz city/state location", zap.Error(err))
		} else if len(result.Locations) > 0 {
			return &result, nil
		}
	}

	return nil, nil
}

func (c Client) searchGlobalTranzLocation(
	ctx context.Context,
	search string,
	result *GetLocationSearchResponse,
) error {
	query := make(url.Values)
	query.Add("countryCode", "1") // We only support US lookups for now (countryCode = 1)
	query.Add("keyword", search)

	_, err := c.doWithRetry(ctx, http.MethodGet, c.addressBookHost, GetLocationSearchPath, query, nil, &result)
	if err != nil {
		return err
	}

	return nil
}
