package globaltranz

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"time"
)

type (
	GetCarrierHistoryRequest struct {
		VendorBk    int `json:"vendorBk"`
		RecordCount int `json:"recordCount"`
	}

	HistoryItem struct {
		BolNumber     string            `json:"bolNumber"`
		StatusID      int               `json:"statusId"`
		EquipmentType string            `json:"equipmentType"`
		PickupDate    string            `json:"pickupDate"`
		DeliveryDate  string            `json:"deliveryDate"`
		TotalCost     float64           `json:"totalCost"`
		Revenue       float64           `json:"revenue"`
		Origin        CityStateLocation `json:"origin"`
		Destination   CityStateLocation `json:"destination"`
		BookedBy      string            `json:"bookedBy"`
	}

	GetCarrierHistoryResponse struct {
		ResponseDetails ResponseDetails `json:"responseDetails"`
		HistoryItems    []HistoryItem   `json:"model"`
	}
)

// Implement LaneHistoryRawDataAccessor
func (i Item) GetPickupDate() (time.Time, error) {
	return time.Parse("2006-01-02", i.PickupDateTime)
}

func (i Item) GetTotalCarrierCost() float32 {
	return float32(i.TotalCarrierCost)
}

func (i Item) GetTotalDistance() float32 {
	return float32(i.Distance)
}

func (i Item) GetCarrierName() string {
	return i.CarrierName
}

// GetCarrierHistory retrieves the history of shipments for a given carrier (vendor).
func (c Client) GetCarrierHistory(
	ctx context.Context,
	req *GetCarrierHistoryRequest,
) (*GetCarrierHistoryResponse, error) {
	var result GetCarrierHistoryResponse

	query := make(url.Values)
	query.Add("vendorBk", fmt.Sprintf("%d", req.VendorBk))
	query.Add("recordCount", fmt.Sprintf("%d", req.RecordCount))

	_, err := c.doWithRetry(ctx, http.MethodGet, c.mainSystemHost, GetCarrierHistoryPath, query, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
