package greenscreens

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

// GetNetworkLaneRatePrediction retrieves a lane rate prediction based on the full network of load data available to us.
// https://connect.greenscreens.ai/#tag/Prediction/operation/predictionNetworkRates
func (c Client) GetNetworkLaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	userEmail string,
	req *GetRatePredictionRequest,
) (*RatePredictionDetail, error) {

	var result RatePredictionDetail
	var apiResp helpers.APIResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if apiResp, err = c.post(ctx, "prediction/network-rates", body, &result); err != nil {
		return nil, err
	}

	if aws.S3Uploader != nil {
		if _, err = aws.S3Uploader.NetworkLaneRatePrediction(ctx, serviceID, userEmail, apiResp); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Error(ctx, "s3 archive lane rate prediction failed", zap.Error(err))
		}
	}

	return &result, nil
}
