package greenscreens

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	ExtraStop struct {
		Stop
		Type                string `json:"type"`
		AppointmentDateTime string `json:"appointmentDateTime"`
	}

	GetLaneHistoryRequest struct {
		Currency           string      `json:"currency"`
		DateFrom           string      `json:"dateFrom"`
		DateTo             string      `json:"dateTo"`
		DestinationCity    string      `json:"destinationCity"`
		DestinationCountry string      `json:"destinationCountry"`
		DestinationState   string      `json:"destinationState"`
		DestinationZip     string      `json:"destinationZip"`
		ExtraStops         []ExtraStop `json:"extraStops"`
		OriginCity         string      `json:"originCity"`
		OriginCountry      string      `json:"originCountry"`
		OriginState        string      `json:"originState"`
		OriginZip          string      `json:"originZip"`
		Region             string      `json:"region"`
		TransportType      string      `json:"transportType"`
	}

	Item struct {
		TotalCarrierCost    float64     `json:"totalCarrierCost"`
		TotalCarrierRate    float64     `json:"totalCarrierRate"`
		LoadPickupDate      string      `json:"loadPickupDate"`
		PickupDateTime      string      `json:"pickupDateTime"`
		DeliveredDateTime   string      `json:"deliveredDateTime"`
		TransportTypeTms    string      `json:"transportTypeTms"`
		Distance            float64     `json:"distance"`
		Weight              float64     `json:"weight"`
		Commodity           string      `json:"commodity"`
		OriginCountry       string      `json:"originCountry"`
		OriginState         string      `json:"originState"`
		OriginCity          string      `json:"originCity"`
		OriginZip           string      `json:"originZip"`
		DestinationCountry  string      `json:"destinationCountry"`
		DestinationState    string      `json:"destinationState"`
		DestinationCity     string      `json:"destinationCity"`
		DestinationZip      string      `json:"destinationZip"`
		ExtraStops          []ExtraStop `json:"extraStops"`
		CarrierName         string      `json:"carrierName"`
		CarrierPhoneNumber  string      `json:"carrierPhoneNumber"`
		CarrierEmail        string      `json:"carrierEmail"`
		CustomerName        string      `json:"customerName"`
		CustomerPhoneNumber string      `json:"customerPhoneNumber"`
		CustomerEmail       string      `json:"customerEmail"`
		SellRate            float64     `json:"sellRate"`
		BuySideRep          string      `json:"buySideRep"`
		SellSideRep         string      `json:"sellSideRep"`
		LoadProNumber       string      `json:"loadProNumber"`
		Currency            string      `json:"currency"`
	}

	GetLaneHistoryResponse struct {
		Items        []Item `json:"items"`
		OutlierItems []Item `json:"outlierItems"`
	}
)

// Implement LaneHistoryRawDataAccessor
func (i Item) GetPickupDate() (time.Time, error) {
	return time.Parse("2006-01-02", i.LoadPickupDate)
}

func (i Item) GetTotalCarrierCost() float32 {
	return float32(i.TotalCarrierCost)
}

func (i Item) GetTotalDistance() float32 {
	return float32(i.Distance)
}

func (i Item) GetCarrierName() string {
	return i.CarrierName
}

var _ models.LaneHistoryRawDataAccessor = Item{}

// GetLaneHistory retrieves the pricing history for a given lane.
// https://connect.greenscreens.ai/#tag/Datalake/operation/datalakeHistory
func (c Client) GetLaneHistory(
	ctx context.Context,
	req *GetLaneHistoryRequest,
) (*GetLaneHistoryResponse, error) {

	var result GetLaneHistoryResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if _, err = c.post(ctx, "datalake/history", body, &result); err != nil {
		return nil, err
	}

	return &result, nil
}
