package main

import (
	"context"
	"fmt"
	"time"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type envVars struct {
	Username string `envconfig:"GREENSCREENS_WPL_USERNAME" required:"true"`
	Password string `envconfig:"GREENSCREENS_WPL_PASSWORD" required:"true"`
}

var (
	env envVars
)

func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	if err := loadEnv(ctx); err != nil {
		log.Fatal(ctx, "loadEnv failed", zap.Error(err))
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, env.Password, nil)
	if err != nil {
		log.Error(ctx, "failed to encrypt greenscreens env password", zap.Error(err))
	}

	client, _, err := greenscreens.New(
		ctx,
		models.Integration{
			Name:              models.Greenscreens,
			Type:              models.Pricing,
			EncryptedPassword: []byte(encryptedPassword),
			Username:          env.Username,
			Tenant:            "staging",
		},
	)
	if err != nil {
		log.Fatal(ctx, "failed to build greenscreens client", zap.Error(err))
	}

	serviceID := uint(1)
	email := "<EMAIL>"

	laneRatePrediction, err := getLaneRatePrediction(ctx, serviceID, email, client)
	if err != nil {
		log.Fatal(ctx, "getLaneRatePrediction failed", zap.Error(err))
	}

	networkLaneRatePrediction, err := getNetworkLaneRatePrediction(ctx, serviceID, email, client)
	if err != nil {
		log.Fatal(ctx, "getNetworkLaneRatePrediction failed", zap.Error(err))
	}

	quote, err := getQuickQuote(ctx, email, laneRatePrediction.ID, client)
	if err != nil {
		log.Fatal(ctx, "getQuickQuote failed", zap.Error(err))
	}

	laneHistory, err := getLaneHistory(ctx, client)
	if err != nil {
		log.Fatal(ctx, "getLaneHistory failed", zap.Error(err))
	}

	log.Info(ctx, "Received lane rate prediction", zap.Any("lane rate prediction", laneRatePrediction))
	log.Info(ctx, "Received network lane rate prediction",
		zap.Any("network lane rate prediction", networkLaneRatePrediction))
	log.Info(ctx, "Received quick quote", zap.Any("quote", quote))
	log.Info(ctx, "Received lane history", zap.Any("lane history", laneHistory))
	log.Info(ctx, "All tests passed!")
}

func getQuickQuote(
	ctx context.Context,
	email,
	laneRatePredictionID string,
	client greenscreens.API,
) (greenscreens.QuoteDetail, error) {

	quote, err := client.GetQuote(
		ctx,
		email,
		laneRatePredictionID,
		&greenscreens.GetQuoteRequest{
			TransportType: "VAN",
			Stops: []greenscreens.Stop{
				{
					Order: 0,
					Address: greenscreens.Address{
						Country: "US",
						City:    "Chicago",
						State:   "IL",
						Zip:     "60611",
					}},
				{
					Order: 1,
					Address: greenscreens.Address{
						Country: "US",
						City:    "Dallas",
						State:   "TX",
						Zip:     "75204",
					}},
			},
			Region:        "ZIP",
			CustomerName:  "Test Customer",
			PickupDate:    "2023-12-18T16:00:00Z",
			DeliveryDate:  "2023-12-19T16:00:00Z",
			Weight:        1000,
			Commodity:     "Apples",
			TargetBuyCost: 5000,
			Currency:      "USD",
		})
	if err != nil {
		return greenscreens.QuoteDetail{}, err
	}

	log.Info(ctx, "GetQuote finished")
	return *quote, err
}

func getLaneHistory(ctx context.Context, client greenscreens.API) (greenscreens.GetLaneHistoryResponse, error) {
	laneHistory, err := client.GetLaneHistory(ctx, &greenscreens.GetLaneHistoryRequest{
		Currency:           "USD",
		DateFrom:           "2024-10-01",
		DateTo:             "2024-11-01",
		DestinationCity:    "Anniston",
		DestinationState:   "AL",
		DestinationZip:     "36201",
		DestinationCountry: "US",
		OriginCountry:      "US",
		OriginZip:          "36260",
		OriginCity:         "Eastaboga",
		OriginState:        "AL",
		Region:             "KMA",
		ExtraStops:         []greenscreens.ExtraStop{},
		TransportType:      "VAN",
	})
	if err != nil {
		return greenscreens.GetLaneHistoryResponse{}, err
	}

	log.Info(ctx, "GetLaneHistory finished")
	return *laneHistory, err
}

func getLaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	email string,
	client greenscreens.API,
) (greenscreens.RatePredictionDetail, error) {

	laneRatePrediction, err := client.GetLaneRatePrediction(
		ctx,
		serviceID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: time.Date(2023, 12, 18, 16, 0, 0, 0, time.UTC),
			TransportType:  "VAN",
			Stops: []greenscreens.RatePredictionStop{
				{
					Order:   0,
					Country: "US",
					City:    "Chicago",
					State:   "IL",
					Zip:     "60611",
				},
				{
					Order:   1,
					Country: "US",
					City:    "Dallas",
					State:   "TX",
					Zip:     "75204",
				}},
			Commodity: "Apples",
			Currency:  "USD",
		})
	if err != nil {
		return greenscreens.RatePredictionDetail{}, err
	}

	log.Info(ctx, "GetLaneRatePrediction finished")
	return *laneRatePrediction, err
}

func getNetworkLaneRatePrediction(
	ctx context.Context,
	serviceID uint,
	email string,
	client greenscreens.API,
) (greenscreens.RatePredictionDetail, error) {

	networkLaneRatePrediction, err := client.GetNetworkLaneRatePrediction(
		ctx,
		serviceID,
		email,
		&greenscreens.GetRatePredictionRequest{
			PickupDateTime: time.Date(2023, 12, 18, 16, 0, 0, 0, time.UTC),
			TransportType:  "VAN",
			Stops: []greenscreens.RatePredictionStop{
				{
					Order:   0,
					Country: "US",
					City:    "Chicago",
					State:   "IL",
					Zip:     "60611",
				},
				{
					Order:   1,
					Country: "US",
					City:    "Dallas",
					State:   "TX",
					Zip:     "75204",
				}},
			Commodity: "Apples",
			Currency:  "USD",
		})
	if err != nil {
		return greenscreens.RatePredictionDetail{}, err
	}

	log.Info(ctx, "GetNetworkLaneRatePrediction finished")
	return *networkLaneRatePrediction, err
}

func loadEnv(ctx context.Context) error {
	if err := godotenv.Load(); err != nil {
		log.Warn(ctx, "unable to load .env file", zap.Error(err))
	}

	if err := envconfig.Process("", &env); err != nil {
		return fmt.Errorf("failed to parse env vars: %w", err)
	}

	return nil
}
