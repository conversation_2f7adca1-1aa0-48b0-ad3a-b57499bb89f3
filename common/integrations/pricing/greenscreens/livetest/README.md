# Greenscreens SDK Live Test
This script is a helpful way to quickly test the Greenscreens SDK directly without having to go through beacon-api.

Create an `.env` file in the same directory:

```shell
GREENSCREENS_WPL_USERNAME=...
GREENSCREENS_WPL_PASSWORD=...
```

The credentials can be found in our 1Password Axle vault.

then run the script:

```shell
go run .
```

1. Fetch the Wickerpark Logistics Greenscreens username/password from 1password.
2. `GetQuickQuote` will get a quick quote.
3. `GetLaneRatePrediction` will get a lane rate prediction.
4. `GetNetworkLaneRatePrediction` will get a network lane rate prediction.
