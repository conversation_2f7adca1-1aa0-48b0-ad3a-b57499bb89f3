package greenscreens

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

const (
	Van     TransportType = "VAN"
	Reefer  TransportType = "REEFER"
	Flatbed TransportType = "FLATBED"

	CAD Currency = "CAD"
	USD Currency = "USD"

	Default                  PricingOption = "default"
	LaneHistorical           PricingOption = "laneHistorical"
	CustomerHistorical       PricingOption = "customerHistorical"
	CustomerOnLaneHistorical PricingOption = "customerOnLaneHistorical"
	PriorityRule             PricingOption = "priorityRule"
	CombinationRules         PricingOption = "combinationRules"
)

type (
	Currency      string
	TransportType string
	PricingOption string

	Address struct {
		Country string `json:"country" validate:"iso3166_1_alpha2"` // 2-letter country code in ISO 3166-1 alpha-2
		State   string `json:"state"`                               // 2 letter code
		City    string `json:"city"`
		Zip     string `json:"zip"`
	}

	Stop struct {
		Order   int     `json:"order"`
		Address Address `json:"address"`
	}

	GetQuoteRequest struct {
		TransportType   TransportType `json:"transportType"`
		Stops           []Stop        `json:"stops"`
		Region          string        `json:"region"`
		CustomerName    string        `json:"customerName"`
		PickupDate      string        `json:"pickupDate"`
		DeliveryDate    string        `json:"deliveryDate"`
		Weight          float64       `json:"weight"`
		Commodity       string        `json:"commodity"`
		TargetBuyCost   float64       `json:"targetBuyCost"`
		ConfidenceLevel int           `json:"confidenceLevel"`
		Currency        Currency      `json:"currency"`
	}

	CostEffect struct {
		Amount   float64
		Currency string
	}

	PricingEffect struct {
		PercentageAdjustment struct{ Percentage float64 }
		FlatAdjustment       CostEffect
		SetSellCost          CostEffect
	}

	DefaultPricing struct {
		Effects        PricingEffect
		TargetSellCost float64
	}

	PricingStatistics struct {
		Average float64
		Minimum float64
		Maximum float64
	}

	HistoricalPricingStatistics struct {
		Pricing        PricingStatistics
		TargetSellCost float64
	}

	ConfidenceLevelRange struct {
		From int
		To   int
	}

	RuleLocations struct {
		Type       string // ANYWHERE, STATE, CITY, MARKET
		Country    string // Two letter country code in ISO 3166-1 alpha-2 format
		State      string
		City       string
		MarketCode string
		MarketName string
	}

	RuleLane struct {
		Origins      []RuleLocations
		Destinations []RuleLocations
	}

	LengthOfHaulRange struct {
		From int
		To   int
	}

	MatchingTimeRange struct {
		From float64
		To   float64
	}

	TransitTimeRange struct {
		From float64
		To   float64
	}

	WeightRange struct {
		From int
		To   int
	}

	RuleCondition struct {
		Commodity       []string
		ConfidenceLevel ConfidenceLevelRange
		Customers       []string
		Lanes           []RuleLane
		LengthOfHaul    LengthOfHaulRange
		MatchingTime    MatchingTimeRange
		TransitTime     TransitTimeRange
		TransportTypes  []TransportType
		Users           []string
		Weight          WeightRange
	}

	PriorityPricingRule struct {
		ID         int64
		Name       string
		Priority   int
		Active     bool
		Conditions RuleCondition
		Effects    PricingEffect
	}

	PriorityRuleCalculation struct {
		Rule           PriorityPricingRule
		TargetSellCost float64
	}

	CombinationPricingEffect struct {
		PercentageAdjustment struct{ Percentage float64 }
		FlatAdjustment       CostEffect
	}

	CombinationPricingRule struct {
		ID         int64
		Name       string
		Active     bool
		Conditions RuleCondition
		Effects    CombinationPricingEffect
	}

	CombinationRulesCalculation struct {
		Rules          []CombinationPricingRule
		TargetSellCost float64
	}

	SuggestedPricing struct {
		Name           PricingOption
		TargetSellCost float64
	}

	QuoteDetail struct {
		Default                  DefaultPricing
		LaneHistorical           HistoricalPricingStatistics
		CustomerHistorical       HistoricalPricingStatistics
		CustomerOnLaneHistorical HistoricalPricingStatistics
		PriorityRule             PriorityRuleCalculation
		CombinationRules         CombinationRulesCalculation
		Suggested                SuggestedPricing
		Currency                 Currency
	}
)

// GetQuote retrieves predictive pricing details.
// https://connect.greenscreens.ai/#tag/Pricing/operation/pricingTplPricing
func (c Client) GetQuote(
	ctx context.Context,
	account,
	laneRatePredictionID string,
	req *GetQuoteRequest,
) (*QuoteDetail, error) {

	var result QuoteDetail
	var apiResp helpers.APIResponse

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)

	if apiResp, err = c.post(ctx, "pricing/tpl-pricing", body, &result); err != nil {
		return nil, err
	}

	if aws.S3Uploader != nil {
		if _, err = aws.S3Uploader.QuickQuote(ctx, account, laneRatePredictionID, apiResp); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Error(ctx, "s3 archive quick quote failed", zap.Error(err))
		}
	}

	return &result, nil
}
