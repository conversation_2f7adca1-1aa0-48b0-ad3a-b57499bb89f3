package dat

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type UserRequestBody struct {
	Username string `json:"username"`
}

const (
	orgTokenEndpoint  = "/access/v1/token/organization"
	userTokenEndpoint = "/access/v1/token/user"
)

func (d *Client) authenticate(ctx context.Context) (resp models.PricingOnBoardResp, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "Auth DAT Organization", otel.IntegrationAttrs(d.integration))
	defer func() { metaSpan.End(err) }()

	// Credentials
	if d.integration.Username == "" || d.integration.EncryptedPassword == nil {
		return models.PricingOnBoardResp{}, errors.New("missing DAT organization credentials")
	}

	if d.userEmail == "" {
		return models.PricingOnBoardResp{}, errors.New("missing DAT user credentials")
	}

	// Organization token
	orgToken, orgTokenExpiration, err := d.getOrgToken(ctx)
	if err != nil {
		return models.PricingOnBoardResp{}, fmt.Errorf("failed to get org access token: %w", err)
	}

	if orgToken == "" || orgTokenExpiration.IsZero() {
		return models.PricingOnBoardResp{}, errors.New("failed to get org access token")
	}

	d.orgToken = ClientToken{
		AccessToken: orgToken,
		ExpiresWhen: models.NullTime{Time: orgTokenExpiration, Valid: true},
	}

	// User token
	userToken, userTokenExpiration, err := d.getUserToken(ctx)
	if err != nil {
		return models.PricingOnBoardResp{}, fmt.Errorf("failed to get user access token: %w", err)
	}

	if userToken == "" || userTokenExpiration.IsZero() {
		return models.PricingOnBoardResp{}, errors.New("failed to get DAT user access token")
	}

	d.userToken = ClientToken{
		AccessToken: userToken,
		ExpiresWhen: models.NullTime{Time: userTokenExpiration, Valid: true},
	}

	return models.PricingOnBoardResp{
		Username:                  d.integration.Username,
		EncryptedPassword:         d.integration.EncryptedPassword,
		AccessToken:               d.orgToken.AccessToken,
		AccessTokenExpirationDate: d.orgToken.ExpiresWhen,
	}, nil
}

func (d *Client) authenticateOnboard(ctx context.Context) (resp models.PricingOnBoardResp, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "Auth DAT Organization", otel.IntegrationAttrs(d.integration))
	defer func() { metaSpan.End(err) }()

	// Credentials
	if d.integration.Username == "" || d.integration.EncryptedPassword == nil {
		return models.PricingOnBoardResp{}, errors.New("missing DAT organization credentials")
	}

	// Organization token
	orgToken, orgTokenExpiration, err := d.getOrgToken(ctx)
	if err != nil {
		return models.PricingOnBoardResp{}, fmt.Errorf("failed to get org access token: %w", err)
	}

	if orgToken == "" || orgTokenExpiration.IsZero() {
		return models.PricingOnBoardResp{}, errors.New("failed to get org access token")
	}

	d.orgToken = ClientToken{
		AccessToken: orgToken,
		ExpiresWhen: models.NullTime{Time: orgTokenExpiration, Valid: true},
	}

	return models.PricingOnBoardResp{
		Username:                  d.integration.Username,
		EncryptedPassword:         d.integration.EncryptedPassword,
		AccessToken:               d.orgToken.AccessToken,
		AccessTokenExpirationDate: d.orgToken.ExpiresWhen,
	}, nil
}

func (d *Client) getOrgToken(ctx context.Context) (string, time.Time, error) {
	decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(d.integration.EncryptedPassword), nil)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to decrypt organization password: %w", err)
	}

	var orgResp TokenResponse

	jsonData, err := json.Marshal(OrgAuthRequestBody{
		Username: d.integration.Username,
		Password: decryptedPassword,
	})
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	if _, err := d.post(ctx, orgTokenEndpoint, bytes.NewBuffer(jsonData), &orgResp, nil); err != nil {
		return "", time.Time{}, fmt.Errorf("failed to get organization access token: %w", err)
	}

	return orgResp.AccessToken, orgResp.ExpiresWhen, nil
}

func (d *Client) getUserToken(ctx context.Context) (string, time.Time, error) {
	var userResp TokenResponse

	jsonData, err := json.Marshal(UserRequestBody{Username: d.userEmail})
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	reqAuth := fmt.Sprintf("Bearer %s", d.orgToken.AccessToken)
	if _, err := d.post(ctx, userTokenEndpoint, bytes.NewBuffer(jsonData), &userResp, &reqAuth); err != nil {
		return "", time.Time{}, fmt.Errorf("failed to get user access token: %w", err)
	}

	return userResp.AccessToken, userResp.ExpiresWhen, nil
}
