## Authentication
1. First we authenticate using the organization (aka company/service) credentials, those being the ones we store in the Integrations table record

2. Then using the access token result from above, we simply fetch the user's individual access token by submitting their email to another endpoint - no further auth needed.

- Both tokens expire within 30 minutes, but that logic is handled by the API Client
- We serialize and store API Client on Redis by the user's email it refers to
    - This allows us to cache individual access Clients for multiple users, preventing repetitive authorization

## Individual Access
The user-level individual access mentioned ends up pretty important, as DAT does in fact require us to make sure all users are using separate API Clients and no credentials (except the organization ones) are being shared.

To comply with DAT rules while also being transparent to our users, we created:

- User columns for `datEmailAddress` and `hasGrantedDATPermissions`
    - Though most of the time the email address for their DAT access will be the same as their Drumkit email, this allows us to handle edge cases
    - Users have to grant access to create their DAT Client for the first time, recognizing that Drumkit is doing so with their datEmailAddress
    - Those fields will simply be null for users of other services that don't use DAT

### References
For more details on both the DAT and GlobalTranz integrations, please check the Lunch & Learn we had going over their implementation:
- <PERSON>lide Deck at https://gamma.app/docs/Quoting-with-DAT-and-GlobalTranz-l88plil8ja5k9as