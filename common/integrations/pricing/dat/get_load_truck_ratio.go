package dat

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
)

type (
	MarketInformationTimeframe    string
	MarketInformationLocationType string
	MarketInformationDirection    string
)

const (
	MCTimeframePastDay        MarketInformationTimeframe = "PREVIOUS_BUSINESS_DAY"
	MCTimeframePastSevenDays  MarketInformationTimeframe = "PREVIOUS_SEVEN_DAYS"
	MCTimeframePastEightDays  MarketInformationTimeframe = "PREVIOUS_EIGHT_DAYS"
	MCTimeframePastThirtyDays MarketInformationTimeframe = "PREVIOUS_THIRTY_DAYS"

	MCITimeframeSevenDayForecast MarketInformationTimeframe = "SEVEN_DAY_FORECAST"
	MCITimeframeEightDayForecast MarketInformationTimeframe = "EIGHT_DAY_FORECAST"

	MCLocationTypeThreeDigitZip      MarketInformationLocationType = "THREE_DIGIT_ZIP"
	MCLocationTypeMarketArea         MarketInformationLocationType = "MARKET_AREA"
	MCLocationTypeExtendedMarketArea MarketInformationLocationType = "EXTENDED_MARKET_AREA"
	MCLocationTypeStateOrProvince    MarketInformationLocationType = "STATE_OR_PROVINCE"
	MCLocationTypeRegion             MarketInformationLocationType = "REGION"
	MCLocationTypeCountry            MarketInformationLocationType = "COUNTRY"

	MCDirectionInbound  MarketInformationDirection = "INBOUND"
	MCDirectionOutbound MarketInformationDirection = "OUTBOUND"

	LoadToTruckRatioEndpoint = "/marketconditions/v1/loadTruckRatios"
)

func (d *Client) GetLoadToTruckRatio(
	ctx context.Context,
	req GetLoadToTruckRatioRequest,
) (*GetLoadToTruckRatioResponse, error) {
	var result []GetLoadToTruckRatioResponse

	query := url.Values{}
	query.Add("areaType", string(req.AreaType))
	query.Add("direction", string(req.Direction))
	query.Add("equipmentCategory", string(req.TransportType))
	query.Add("startDate", req.StartDate)
	query.Add("endDate", req.EndDate)

	if req.PostalCode != "" {
		query.Add("postalCode", req.PostalCode)
	}

	if req.City != "" && req.StateOrProvince != "" {
		query.Add("city", req.City)
		query.Add("stateOrProvince", req.StateOrProvince)
	}

	if !query.Has("city") && !query.Has("postalCode") {
		return nil, errors.New("city/state pair or postal code is required")
	}

	reqAuth := fmt.Sprintf("Bearer %s", d.userToken.AccessToken)
	if _, err := d.do(ctx, http.MethodGet, LoadToTruckRatioEndpoint, query, nil, &result, &reqAuth); err != nil {
		lowercaseErr := strings.ToLower(err.Error())
		// Check for forbidden message/status code that may indicate a connexion seat issue
		if strings.Contains(lowercaseErr, "403") || strings.Contains(lowercaseErr, "forbidden") {
			return nil, AddConnexionSeatMessageToError(err)
		}

		return nil, err
	}

	if len(result) == 0 {
		return nil, errors.New("no load to truck ratio found")
	}

	return &result[0], nil
}
