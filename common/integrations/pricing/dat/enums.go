package dat

type RateTimeframe string

const (
	ThreeDays         RateTimeframe = "3_DAYS"
	SevenDays         RateTimeframe = "7_DAYS"
	FifteenDays       RateTimeframe = "15_DAYS"
	ThirtyDays        RateTimeframe = "30_DAYS"
	SixtyDays         RateTimeframe = "60_DAYS"
	NinetyDays        RateTimeframe = "90_DAYS"
	HundredEightyDays RateTimeframe = "180_DAYS"
	OneYear           RateTimeframe = "365_DAYS"
	OneMonth          RateTimeframe = "1_MONTH"
)

type LocationType string

const (
	ZipCode            LocationType = "3_DIGIT_ZIP"
	MarketArea         LocationType = "MARKET_AREA"
	ExtendedMarketArea LocationType = "EXTENDED_MARKET_AREA"
	State              LocationType = "STATE"
	Region             LocationType = "REGION"
	Country            LocationType = "COUNTRY"
)

type RateType string

const (
	SpotRateType                     RateType = "SPOT"
	ContractRateType                 RateType = "CONTRACT"
	ShipperToBrokerSpotRateType      RateType = "SHIPPER_TO_BROKER_SPOT"
	BrokerToCarrierSpotRateType      RateType = "BROKER_TO_CARRIER_SPOT"
	ShipperToCarrierContractRateType RateType = "SHIPPER_TO_CARRIER_CONTRACT"
)

type RateTense string

const (
	CurrentRateTense  RateTense = "CURRENT"
	HistoricRateTense RateTense = "HISTORIC"
)

type EscalationType string

const (
	BestFitEscalationType                              EscalationType = "BEST_FIT"
	SpecificAreaTypeAndSpecificTimeFrameEscalationType EscalationType = "SPECIFIC_AREA_TYPE_AND_SPECIFIC_TIME_FRAME"
	MinimumAreaTypeAndMinimumTimeFrameEscalationType   EscalationType = "MINIMUM_AREA_TYPE_AND_MINIMUM_TIME_FRAME"
	SpecificAreaTypeAndShortestTimeFrameEscalationType EscalationType = "SPECIFIC_AREA_TYPE_AND_SHORTEST_TIME_FRAME"
	SpecificTimeFrameAndSmallerAreaTypeEscalationType  EscalationType = "SPECIFIC_TIME_FRAME_AND_SMALLER_AREA_TYPE"
)
