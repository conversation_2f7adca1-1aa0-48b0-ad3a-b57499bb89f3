package dat

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
)

const (
	MarketConditionsForecastEndpoint = "/marketconditions/v1/marketConditionsForecasts"
)

func (d *Client) GetMarketConditionsForecast(
	ctx context.Context,
	req GetMarketInformationRequest,
) (*GetMarketConditionsForecastResponse, error) {
	var result []GetMarketConditionsForecastResponse

	query := url.Values{}
	query.Add("areaType", string(req.AreaType))
	query.Add("direction", string(req.Direction))
	query.Add("equipmentCategory", string(req.TransportType))
	query.Add("timeframe", string(MCITimeframeSevenDayForecast))

	if req.PostalCode != "" {
		query.Add("postalCode", req.PostalCode)
	}

	if req.City != "" && req.StateOrProvince != "" {
		query.Add("city", req.City)
		query.Add("stateOrProvince", req.StateOrProvince)
	}

	if !query.Has("city") && !query.Has("postalCode") {
		return nil, errors.New("city/state pair or postal code is required")
	}

	reqAuth := fmt.Sprintf("Bearer %s", d.userToken.AccessToken)
	if _, err := d.do(
		ctx,
		http.MethodGet,
		MarketConditionsForecastEndpoint,
		query,
		nil,
		&result,
		&reqAuth,
	); err != nil {
		lowercaseErr := strings.ToLower(err.Error())
		// Check for forbidden message/status code that may indicate a connexion seat issue
		if strings.Contains(lowercaseErr, "403") || strings.Contains(lowercaseErr, "forbidden") {
			return nil, AddConnexionSeatMessageToError(err)
		}

		return nil, err
	}

	if len(result) == 0 {
		return nil, errors.New("no market conditions forecast found")
	}

	return &result[0], nil
}
