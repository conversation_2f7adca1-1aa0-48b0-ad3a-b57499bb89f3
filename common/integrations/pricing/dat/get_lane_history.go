package dat

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

const laneHistoryEndpoint = "/linehaulrates/v1/history"

func (d *Client) GetLaneHistory(ctx context.Context, reqBody *GetLaneHistoryRequest) (*GetLaneHistoryResponse, error) {
	var result GetLaneHistoryResponse

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)
	reqAuth := fmt.Sprintf("Bearer %s", d.userToken.AccessToken)
	if _, err = d.post(ctx, laneHistoryEndpoint, body, &result, &reqAuth); err != nil {
		lowercaseErr := strings.ToLower(err.Error())
		// Check for forbidden message/status code that may indicate a connexion seat issue
		if strings.Contains(lowercaseErr, "403") || strings.Contains(lowercaseErr, "forbidden") {
			return nil, AddConnexionSeatMessageToError(err)
		}

		return nil, err
	}

	return &result, nil
}
