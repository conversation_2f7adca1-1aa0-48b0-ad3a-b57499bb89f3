package dat

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

const laneRateEndpoint = "/linehaulrates/v1/lookups"

func (d *Client) GetLaneRate(ctx context.Context, reqBody []RateRequest) (*GetLaneRateResponse, error) {
	var result GetLaneRateResponse

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	body := bytes.NewBuffer(jsonData)
	reqAuth := fmt.Sprintf("Bearer %s", d.userToken.AccessToken)
	if _, err = d.post(ctx, laneRateEndpoint, body, &result, &reqAuth); err != nil {
		lowercaseErr := strings.ToLower(err.Error())
		// Check for forbidden message/status code that may indicate a connexion seat issue
		if strings.Contains(lowercaseErr, "403") || strings.Contains(lowercaseErr, "forbidden") {
			return nil, AddConnexionSeatMessageToError(err)
		}

		return nil, err
	}

	return &result, nil
}
