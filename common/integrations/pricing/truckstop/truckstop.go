package truckstop

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type Truckstop struct {
	integration models.Integration
}

const (
	devRedirectURI  = "https://eoqbpfs6yi5m4x9.m.pipedream.net"
	prodRedirectURI = "https://app.drumkit.ai/integrations/truckstop"
)

func New(ctx context.Context, integration models.Integration) (Truckstop, error) {
	log.With(ctx, zap.Uint("drumkitIntegrationID", integration.ID), zap.String("integration", "truckstop"))

	if integration.AccessToken != "" && integration.NeedsRefresh() {
		client := NewClient(integration)
		tokenResp, err := client.RefreshToken(ctx)
		if err != nil {
			return Truckstop{}, fmt.Errorf("failed to refresh token: %w", err)
		}

		integration.AccessToken = tokenResp.AccessToken
		integration.AccessTokenExpirationDate = models.NullTime{
			Time:  time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second),
			Valid: true,
		}
		integration.RefreshToken = tokenResp.RefreshToken

		if integration.ID != 0 {
			if err = integrationDB.Update(ctx, &integration); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update truckstop info on integration db",
					zap.Any("integration", integration),
				)
				return Truckstop{}, fmt.Errorf("integration db update failed: %w", err)
			}
		}
	}

	return Truckstop{integration: integration}, nil
}

// InitialOnboard handles the initial authentication with Truckstop
func (t Truckstop) InitialOnboard(
	ctx context.Context,
	service models.Service,
	request models.OnboardPricingRequest,
) (models.PricingOnBoardResp, error) {
	t.integration.Service = service
	t.integration.ClientID = service.TruckstopClientID
	t.integration.ClientSecret = service.TruckstopClientSecret

	var redirectURI string
	switch env.Vars.AppEnv {
	case "dev", "staging":
		redirectURI = devRedirectURI
	default:
		redirectURI = prodRedirectURI
	}

	tokenResp, err := NewClient(t.integration).Authenticate(
		ctx,
		request.AuthorizationCode,
		redirectURI,
	)
	if err != nil {
		return models.PricingOnBoardResp{}, err
	}

	tokenExpTime := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return models.PricingOnBoardResp{
		AccessToken: tokenResp.AccessToken,
		AccessTokenExpirationDate: models.NullTime{
			Time:  tokenExpTime,
			Valid: true,
		},
		RefreshToken: tokenResp.RefreshToken,
		ClientID:     t.integration.Service.TruckstopClientID,
		ClientSecret: t.integration.Service.TruckstopClientSecret,
	}, nil
}

func (t Truckstop) GetBookedHistory(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *BookedRateTrendline,
) (*BookedHistoryResp, error) {
	client := NewClient(t.integration)
	return client.GetBookedHistory(ctx, serviceID, userEmail, req)
}

func (t Truckstop) GetBookedTrendline(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *BookedRateTrendline,
) (*BookedTrendlineResp, error) {
	client := NewClient(t.integration)
	return client.GetBookedTrendline(ctx, serviceID, userEmail, req)
}

func (t Truckstop) GetBookedRate(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *BookedRateTrendline,
) (*BookedRateEstimateResp, error) {
	client := NewClient(t.integration)
	return client.GetBookedRate(ctx, serviceID, userEmail, req)
}

func (t Truckstop) GetPostedTrendline(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *PostedRateTrendline,
) ([]PostedTrendlineResp, error) {
	client := NewClient(t.integration)
	return client.GetPostedTrendline(ctx, serviceID, userEmail, req)
}

func (t Truckstop) GetPostedRate(
	ctx context.Context,
	serviceID uint,
	userEmail string, // email of shipper
	req *PostedRateTrendline,
) (PostedRateResp, error) {
	client := NewClient(t.integration)
	return client.GetPostedRate(ctx, serviceID, userEmail, req)
}
