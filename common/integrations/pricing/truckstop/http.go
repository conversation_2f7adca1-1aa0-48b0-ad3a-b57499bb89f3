package truckstop

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

func (c *Client) Post(
	ctx context.Context,
	path string,
	query url.Values,
	body io.Reader,
	out any,
) (helpers.APIResponse, error) {
	return c.do(ctx, http.MethodPost, path, query, body, out)
}

func (c *Client) Get(
	ctx context.Context,
	path string,
	query url.Values,
	body io.Reader,
	out any,
) (helpers.APIResponse, error) {
	return c.do(ctx, http.MethodGet, path, query, body, out)
}

func (c *Client) do(
	ctx context.Context,
	method, path string,
	query url.Values,
	reqBody io.Reader,
	out any,
) (helpers.APIResponse, error) {
	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     c.host,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, reqBody)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}

	// Set headers
	if path == "/auth/token" {
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Add("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(
			c.integration.ClientID+":"+c.integration.ClientSecret)))
	} else {
		// For non-auth endpoints, add Bearer token
		req.Header.Add("Authorization", "Bearer "+c.integration.AccessToken)

		if method == http.MethodPost || method == http.MethodPut {
			req.Header.Set("Content-Type", "application/json")
		}
	}

	req.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, c.integration, err)
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to send %s %s request: %w", method, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, c.integration, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return helpers.APIResponse{Status: 599, Body: ""},
			fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code != http.StatusOK && code != http.StatusCreated {
		return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
			errtypes.NewHTTPResponseError(c.integration, req, resp, body)
	}

	if out != nil {
		if err := json.Unmarshal(body, out); err != nil {
			log.Error(ctx, "json unmarshal failed for truckstop response body",
				zap.ByteString("body", body))

			return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)},
				fmt.Errorf("%s %s json unmarshal failed: %w", method, reqURL, err)
		}

		if path != "auth/token" {
			log.Debug(ctx, "received truckstop response",
				zap.String("method", method), zap.String("url", reqURL), zap.Any("body", out))
		}
	}

	return helpers.APIResponse{Status: resp.StatusCode, Body: string(body)}, nil
}
