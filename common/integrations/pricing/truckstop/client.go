package truckstop

import (
	"context"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const BaseURL = "https://api.truckstop.com/"

type (
	Client struct {
		integration models.Integration
		host        string
	}

	API interface {
		Authenticate(ctx context.Context, code, redirectURI string) (TokenResp, error)
		RefreshToken(ctx context.Context) (TokenResp, error)
		GetPostedRate(
			ctx context.Context,
			serviceID uint,
			userEmail string,
			req *PostedRateTrendline,
		) (PostedRateResp, error)
		GetPostedTrendline(
			ctx context.Context,
			serviceID uint,
			userEmail string,
			req *PostedRateTrendline,
		) ([]PostedTrendlineResp, error)
		GetBookedRate(
			ctx context.Context,
			serviceID uint,
			userEmail string,
			req *BookedRateTrendline,
		) (*BookedRateEstimateResp, error)
		GetBookedTrendline(
			ctx context.Context,
			serviceID uint,
			userEmail string,
			req *BookedRateTrendline,
		) (*BookedTrendlineResp, error)
		GetBookedHistory(
			ctx context.Context,
			serviceID uint,
			userEmail string,
			req *BookedRateTrendline,
		) (*BookedHistoryResp, error)
	}
)

func NewClient(integration models.Integration) API {
	host := "api.truckstop.com"
	return &Client{
		integration: integration,
		host:        host,
	}
}

func (c Client) Authenticate(ctx context.Context, code, redirectURI string) (TokenResp, error) {
	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", redirectURI)

	var token TokenResp
	resp, err := c.do(ctx, http.MethodPost, "/auth/token", nil, strings.NewReader(data.Encode()), &token)

	if err != nil {
		log.Error(ctx, "truckstop authentication failed",
			zap.Error(err),
			zap.Int("status", resp.Status),
			zap.String("body", resp.Body))
	} else {
		log.Info(ctx, "truckstop authentication successful")
	}

	return token, err
}

func (c Client) RefreshToken(ctx context.Context) (TokenResp, error) {
	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", c.integration.RefreshToken)

	query := url.Values{}
	query.Set("scope", "truckstop")

	var token TokenResp
	_, err := c.do(ctx, http.MethodPost, "/auth/token", query, strings.NewReader(data.Encode()), &token)
	return token, err
}
