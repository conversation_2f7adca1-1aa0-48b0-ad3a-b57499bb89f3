package truckstop

type TokenResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Claims       string `json:"claims"`
}

type BookedHistoryResp struct {
	Results []struct {
		RatePerMile float64 `json:"ratePerMile"`
		Prediction  float64 `json:"prediction"`
		Date        string  `json:"date"`
	} `json:"results"`
}

type BookedRateEstimateResp struct {
	Result           string  `json:"result"`
	LoadID           string  `json:"loadId"`
	LowerRate        float64 `json:"lowerRate"`
	PredictedRate    float64 `json:"predictedRate"`
	UpperRate        float64 `json:"upperRate"`
	Message          string  `json:"message"`
	DataRecordsScore float64 `json:"dataRecordsScore"`
	LoadInputScore   float64 `json:"loadInputScore"`
	Average          float64 `json:"average"`
	RatePerMile      float64 `json:"ratePerMile"`
}

type PostedTrendlineResp struct {
	Result               string `json:"result"`
	LoadID               string `json:"loadId"`
	UpperBound           int    `json:"upperBound"`
	LowerBound           int    `json:"lowerBound"`
	TrendlineCollections []struct {
		TrendlineName   string `json:"trendlineName"`
		TrendlineSeries []struct {
			TrendlineStartDate string `json:"trendlineStartDate"`
			TrendlineEndDate   string `json:"trendlineEndDate"`
			TotalRate          int    `json:"totalRate"`
			RatePerMile        int    `json:"ratePerMile"`
		} `json:"trendlineSeries"`
	} `json:"trendlineCollections"`
	Message string `json:"message"`
}

type BookedRateTrendline struct {
	LoadID                  string          `json:"LoadId"`
	RateDateTime            string          `json:"RateDateTime"`
	BookedDateTime          string          `json:"BookedDateTime"`
	ShipDateTime            string          `json:"ShipDateTime"`
	DeliveryDateTime        string          `json:"DeliveryDateTime"`
	Origin                  LocationDetails `json:"Origin"`
	Destination             LocationDetails `json:"Destination"`
	Mileage                 int             `json:"Mileage,omitempty"`
	TransportationMode      string          `json:"TransportationMode"`
	EquipmentCode           string          `json:"EquipmentCode"`
	EquipmentOptions        []string        `json:"EquipmentOptions,omitempty"`
	Weight                  int             `json:"Weight,omitempty"`
	NumberOfLoads           int             `json:"NumberOfLoads,omitempty"`
	MultiPickDrop           string          `json:"MultiPickDrop"`
	CommodityID             int             `json:"CommodityId,omitempty"`
	ServicesAndRequirements string          `json:"ServicesAndRequirements,omitempty"`
	Accessorial             string          `json:"Accessorial,omitempty"`
	FuelIncluded            bool            `json:"FuelIncluded,omitempty"`
	FuelPricePerTrip        float64         `json:"FuelPricePerTrip,omitempty"`
	PalletCount             int             `json:"PalletCount,omitempty"`
}

type LocationDetails struct {
	Address   string  `json:"Address"`
	City      string  `json:"City"`
	StateCode string  `json:"StateCode"`
	ZipCode   string  `json:"ZipCode"`
	Latitude  float64 `json:"Latitude"`
	Longitude float64 `json:"Longitude"`
}

type BookedTrendlineResp struct {
	Result               string `json:"result"`
	LoadID               string `json:"loadId"`
	UpperBound           int    `json:"upperBound"`
	LowerBound           int    `json:"lowerBound"`
	TrendlineCollections []struct {
		TrendlineName   string `json:"trendlineName"`
		TrendlineSeries []struct {
			TrendlineStartDate string  `json:"trendlineStartDate"`
			TrendlineEndDate   string  `json:"trendlineEndDate"`
			TotalRate          float64 `json:"totalRate"`
			RatePerMile        float64 `json:"ratePerMile"`
		} `json:"trendlineSeries"`
	} `json:"trendlineCollections"`
	Message string `json:"message"`
}

type PostedRateTrendline struct {
	LoadID              string          `json:"LoadId"`
	Origin              LocationDetails `json:"Origin"`
	Destination         LocationDetails `json:"Destination"`
	TransportationMode  string          `json:"TransportationMode"`
	Stops               int             `json:"Stops"`
	Mileage             int             `json:"Mileage,omitempty"`
	PostedDateTime      string          `json:"PostedDateTime"`
	EntryDateTime       string          `json:"EntryDateTime"`
	PickUpDateTime      string          `json:"PickUpDateTime"`
	DeliveryDateTime    string          `json:"DeliveryDateTime"`
	EquipmentCode       string          `json:"EquipmentCode"`
	EquipmentOptions    []string        `json:"EquipmentOptions"`
	Weight              int             `json:"Weight,omitempty"`
	Length              int             `json:"Length,omitempty"`
	PalletCount         int             `json:"PalletCount,omitempty"`
	PieceCount          int             `json:"PieceCount,omitempty"`
	Cube                int             `json:"Cube,omitempty"`
	CommodityID         int             `json:"CommodityId,omitempty"`
	SpecInfo            string          `json:"SpecInfo,omitempty"`
	OtherEquipmentNeeds string          `json:"OtherEquipmentNeeds,omitempty"`
	FuelPricePerGallon  float64         `json:"FuelPricePerGallon,omitempty"`
}

type PostedRateResp struct {
	Result           string  `json:"result"`
	LoadID           string  `json:"loadId"`
	LowerRate        float64 `json:"lowerRate"`
	PredictedRate    float64 `json:"predictedRate"`
	UpperRate        float64 `json:"upperRate"`
	Message          string  `json:"message"`
	DataRecordsScore float64 `json:"dataRecordsScore"`
	LoadInputScore   float64 `json:"loadInputScore"`
	Average          float64 `json:"average"`
	RatePerMile      float64 `json:"ratePerMile"`
}
