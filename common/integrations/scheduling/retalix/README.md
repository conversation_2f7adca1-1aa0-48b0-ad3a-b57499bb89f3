# Retalix Go SDK

A simplified Go SDK for the [Retalix](https://www.retalixtraffic.com/help/InstructionsForUsingRetalixWebScheduling.pdf) scheduling portal.
See [livetest](livetest/main.go) for example usage.

## Scheduling Flow
1. **Login**: Authenticate with the Retalix portal using customer credentials (e.g., email and password).
2. **Validate PO Numbers**: Validate a list of purchase order (PO) numbers before requesting an appointment to ensure they’re correct and associated with the customer.
3. **Set Appointment Type**: Choose between `live` (immediate scheduling) or `drop` (alternative handling).
4. **Request Appointment**: Either schedule a `live` appointment by selecting an available time slot or submit a request for review with a preferred date and time (e.g., "03/19 before noon") if no slots are open.
5. **Check Status**: After submission, retrieve the confirmation number provided by Retalix to verify the appointment.

## Edge Cases
- **Customer Review Requirement**: In some cases, the Retalix portal requires a "customer review," disabling the ability to toggle between `live` and `drop` and defaulting to `live`. The SDK now adapts to this by proceeding with `live` automatically, whereas earlier versions would fail the submission. Ensure your code accounts for this forced `live` type when applicable.

## Considerations
Booking or canceling an appointment requires approval, which may take several hours. To avoid delays in development, it is recommended that engineers **record network calls as HAR files and save HTML pages**. This allows us to code against these records without being stuck waiting for customer communication or approval processes.
