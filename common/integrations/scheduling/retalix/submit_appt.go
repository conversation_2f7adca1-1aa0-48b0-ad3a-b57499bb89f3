package retalix

import (
	"context"
	"fmt"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

type LumperStatus int

const (
	LumperUnassigned LumperStatus = -1
	LumperNo         LumperStatus = 0
	LumperYes        LumperStatus = 1
)

// Convert bool to LumperStatus
func boolToLumperStatus(requested bool) LumperStatus {
	if requested {
		return LumperYes
	}

	return LumperNo
}

// Convert LumperStatus to string for form values
func (ls LumperStatus) toString() string {
	switch ls {
	case LumperYes:
		return "LumperYes"
	case LumperNo:
		return "LumperNo"
	default:
		return "LumperUnassigned"
	}
}

// Convert LumperStatus to API parameter
func (ls LumperStatus) toParam() string {
	return strconv.Itoa(int(ls))
}

func (r *Retalix) SubmitAppointment(
	ctx context.Context,
	poNumbers []string,
	warehouse models.Warehouse,
	lumperRequested bool,
	note string,
	opts ...models.SchedulingOption,
) error {

	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	// Step 1: Initial setup and form state
	err := r.getApptFormViewState(ctx)
	if err != nil {
		return err
	}

	// Step 2: Validate PO numbers
	err = r.postApptFormForValidation(ctx, poNumbers, warehouse)
	if err != nil {
		return err
	}

	// Step 3: Force PO validation
	err = r.beginForceApptFormValidation(ctx, warehouse)
	if err != nil {
		return err
	}

	// Step 4: Get validated appointment form
	validatedPOs, err := r.getValidatedApptForm(ctx, warehouse)
	if err != nil {
		return err
	}

	// Verify all POs were validated successfully
	for _, po := range validatedPOs {
		if !po.IsValid {
			return &Error{
				Type:    ValidationError,
				Message: fmt.Sprintf("PO number %s failed validation: %s", po.PONumber, po.Error),
			}
		}
	}

	// Step 5: Set appointment details
	lp := boolToLumperStatus(lumperRequested)

	doc, err := r.changeApptDetails(ctx, warehouse, lp.toString(), *options)
	if err != nil {
		return err
	}

	err = r.submitAppt(ctx, lp, note, doc, *options)
	if err != nil {
		return err
	}

	return nil
}
