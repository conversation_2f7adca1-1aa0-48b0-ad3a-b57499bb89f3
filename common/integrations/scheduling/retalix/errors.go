package retalix

import (
	"strings"

	"github.com/drumkitai/drumkit/common/errtypes"
)

// cleanErrorMessage extracts a user-facing error string from Retalix error responses.
// It removes the integration prefixes and returns just the core error message.
func cleanErrorMessage(origErr error) string {
	errStr := origErr.Error()

	// Remove the outer wrapper: "retalix.getopenslots failed: "
	errStr = strings.TrimPrefix(errStr, "retalix.getopenslots failed: ")

	errStr = strings.TrimPrefix(errStr, "retalix validation error: ")

	// Remove other retalix error prefixes
	prefixes := []string{
		"retalix session error: ",
		"retalix form error: ",
		"retalix response error: ",
	}

	for _, prefix := range prefixes {
		if strings.HasPrefix(errStr, prefix) {
			errStr = strings.TrimPrefix(errStr, prefix)
			break
		}
	}

	return strings.TrimSpace(errStr)
}

// CleanRetalixError wraps a Retalix error with user-facing error handling
func CleanRetalixError(err error) error {
	return errtypes.NewUserFacingError(err, cleanErrorMessage)
}
