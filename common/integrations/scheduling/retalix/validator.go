package retalix

import (
	"fmt"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

type (
	ErrorType int

	Error struct {
		Type    ErrorType
		Message string
	}

	OrderError struct {
		PONumber string
		Error    string
	}

	ValidationOptions struct {
		IgnoreReviewRequired bool
	}
)

type ValidationOption func(*ValidationOptions)

// Option to ignore review required message
func WithIgnoreReviewRequired() ValidationOption {
	return func(o *ValidationOptions) {
		o.IgnoreReviewRequired = true
	}
}

const (
	SessionError ErrorType = iota
	FormError
	ResponseError
	ValidationError
)

func (e *Error) Error() string {
	switch e.Type {
	case SessionError:
		return fmt.Sprintf("retalix session error: %s", e.Message)
	case FormError:
		return fmt.Sprintf("retalix form error: %s", e.Message)
	case ResponseError:
		return fmt.Sprintf("retalix response error: %s", e.Message)
	case ValidationError:
		return fmt.Sprintf("retalix validation error: %s", e.Message)
	default:
		return e.Message
	}
}

func validateRetalixResponse(doc *goquery.Document, opts ...ValidationOption) error {
	options := &ValidationOptions{}
	for _, opt := range opts {
		opt(options)
	}

	// Check for session validation error in script
	if script := doc.Find("script").First().Text(); strings.Contains(script, "Your session could not be validated") {
		return &Error{
			Type:    SessionError,
			Message: "session validation failed - user needs to login",
		}
	}

	// Check for order validation errors
	if err := validateOrderErrors(doc, *options); err != nil {
		return err
	}

	// Check for error message in lblInfo span
	if errorMsg := doc.Find("span#lblInfo font[color='#B50010']").Text(); errorMsg != "" {
		return &Error{
			Type:    FormError,
			Message: errorMsg,
		}
	}

	// Check for error message in Response div
	responseMsg := doc.Find("div#Response").Text()
	if responseMsg != "" && !strings.Contains(responseMsg, "success") {
		return &Error{
			Type:    ResponseError,
			Message: responseMsg,
		}
	}

	// Check for form validation errors
	// Only check for review required if not ignored
	if !options.IgnoreReviewRequired {
		errMsg := doc.Find("span[style*='color:Red']").Text()
		if errMsg != "" && !strings.Contains(errMsg, "Please select an appointment") {
			return &Error{
				Type:    ValidationError,
				Message: errMsg,
			}
		}
	}

	return nil
}

func validateOrderErrors(doc *goquery.Document, opts ValidationOptions) error {
	var errors []OrderError

	// Check the OrderList table for errors
	doc.Find("table#OrderList tr").Each(func(i int, row *goquery.Selection) {
		// Skip header row aka the first row
		if i == 0 {
			return
		}

		// Find PO number from span with ID ending in "_OrderNum"
		// In HTML: <span id="OrderList__ctl2_OrderNum">200</span>
		poNumber := row.Find("span[id$='_OrderNum']").Text()

		// Find error text from span with ID ending in "_ErrorText"
		// In HTML: <span id="OrderList__ctl2_ErrorText">Order Not Found</span>
		errorText := row.Find("span[id$='_ErrorText']").Text()

		if poNumber != "" && strings.Contains(errorText, "Order Not Found") {
			errors = append(errors, OrderError{
				PONumber: poNumber,
				Error:    errorText,
			})
		}
	})

	// Check for the general error message
	// In HTML: <span id="lbError">Orders with errors will not be processed...</span>
	errorMsg := doc.Find("span#lbError").Text()
	if strings.Contains(errorMsg, "Orders with errors will not be processed") {
		return &Error{
			Type:    ValidationError,
			Message: fmt.Sprintf("Order validation failed: %s", formatOrderErrors(errors)),
		}
	}

	// Only check for review required if not ignored
	if !opts.IgnoreReviewRequired {
		// Check for customer review requirement
		// In HTML: <span id="Messages">This appointment requires customer review...</span>
		reviewMsg := doc.Find("span#Messages").Text()
		if strings.Contains(reviewMsg, "This appointment requires customer review") {
			return &Error{
				Type:    ValidationError,
				Message: "Appointment requires customer review",
			}
		}
	}

	// If we found any order errors, return them even if the general messages weren't present
	if len(errors) > 0 {
		return &Error{
			Type:    ValidationError,
			Message: formatOrderErrors(errors),
		}
	}

	return nil
}

func formatOrderErrors(errors []OrderError) string {
	if len(errors) == 0 {
		return ""
	}

	var msgs []string
	for _, err := range errors {
		msgs = append(msgs, fmt.Sprintf("PO#%s: %s", err.PONumber, err.Error))
	}

	return strings.Join(msgs, "; ")
}
