# Retalix SDK Live Test
This script helps test the Retalix SDK directly without going through the Drumkit API.

## Usage
```shell
go run . \
  -username "<your-retalix-username>" \
  -password "<your-retalix-password>" \
  -warehouse-id "<warehouse-id>" \
  -warehouse-name "<warehouse-name>" \
  -po "<po-number>"
```

## Example
```shell
go run . \
  -username "<EMAIL>" \
  -password "password123" \
  -warehouse-id "814" \
  -warehouse-name "Hy-Vee Food Stores, Inc. - Cumming Distribution Center (Cumming, IA )" \
  -po "215628"
```
