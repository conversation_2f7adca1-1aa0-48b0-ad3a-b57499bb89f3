package retalix

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"

	"github.com/drumkitai/drumkit/common/models"
)

func (r *Retalix) GetAllWarehouses(
	ctx context.Context,
	_ ...models.SchedulingOption,
) (warehouse []models.Warehouse, err error) {

	resp, _, err := r.get(ctx, buildURL(paths.CreateAppt), nil)
	if err != nil {
		return nil, fmt.Errorf("error getting retalix warehouse list HTML: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return nil, fmt.Errorf("could not parse HTML from Retalix when getting view state: %w", err)
	}

	if err := validateRetalixResponse(doc); err != nil {
		return nil, err
	}

	var allWarehouses []models.Warehouse
	doc.Find("select[name='Customers'] option").Each(func(_ int, element *goquery.Selection) {
		value, _ := element.Attr("value")
		text := element.Text()

		warehouseInfo := strings.Split(text, " - ")
		warehouseName := warehouseInfo[0]
		warehouseAddressLine1 := strings.Split(warehouseInfo[1], " (")[0]

		parenthesisRegex := regexp.MustCompile(`\((.*?)\)`)
		warehouseAddessLine2 := parenthesisRegex.FindStringSubmatch(text)[1]

		warehouseFullAddress := strings.Join([]string{warehouseAddressLine1, warehouseAddessLine2}, " ")
		allWarehouses = append(allWarehouses, models.Warehouse{
			WarehouseID:             value,
			WarehouseName:           warehouseName,
			WarehouseAddressLine1:   warehouseAddressLine1,
			WarehouseAddressLine2:   warehouseAddessLine2,
			WarehouseFullAddress:    warehouseFullAddress,
			WarehouseFullIdentifier: strings.Join([]string{warehouseName, warehouseFullAddress}, " "),
			Source:                  models.RetalixSource,
		})
	})

	return allWarehouses, nil
}
