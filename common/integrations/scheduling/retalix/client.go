package retalix

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	Retalix struct {
		scheduler  models.Integration
		httpClient httpClient
		creds      *Credentials
	}

	httpClient interface {
		Do(req *http.Request) (*http.Response, error)
	}

	Credentials struct {
		Username      string
		Password      string
		Loginx        string
		Loginy        string
		ViewState     ViewState // for login
		AppointmentVS ViewState // for appointment form
	}

	// ASP.NET form handling types
	ViewState struct {
		State      string
		Generator  string
		Validation string
	}

	PageError struct {
		Operation string
		Message   string
		Err       string
	}

	FormOptions struct {
		Target      string
		Argument    string
		LastFocus   string
		ViewState   ViewState
		ExtraFields map[string]string
	}
)

const (
	baseHost = "www.ncrpowertraffic.com"
	baseURL  = "https://" + baseHost
)

var paths = struct {
	Login        string
	CreateAppt   string
	AddOrders    string
	ScheduleAppt string
	ScheduleApp  string
	ConfirmAppt  string
}{
	Login:        "/default.aspx",
	CreateAppt:   "/createAppointment.aspx",
	AddOrders:    "/addOrders.aspx",
	ScheduleAppt: "/scheduleAppointment.aspx",
	ScheduleApp:  "/scheduleApp.aspx",
	ConfirmAppt:  "/confirmAppointment.aspx",
}

func (e *PageError) Error() string {
	return fmt.Sprintf("retalix %s failed: %s: %v", e.Operation, e.Message, e.Err)
}

func buildURL(path string) string {
	return baseURL + path
}

func buildURLWithQuery(path string, query url.Values) string {
	if len(query) == 0 {
		return buildURL(path)
	}

	return buildURL(path) + "?" + query.Encode()
}

func parseHTML(resp []byte) (*goquery.Document, error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))

	if err != nil {
		return nil, &PageError{
			Operation: "html parsing",
			Message:   "failed to parse response",
			Err:       err.Error(),
		}
	}

	return doc, nil
}

func extractViewState(doc *goquery.Document) ViewState {
	var vs ViewState

	doc.Find("input[type='hidden']").Each(func(_ int, element *goquery.Selection) {
		name, _ := element.Attr("name")
		value, _ := element.Attr("value")
		switch name {
		case "__EVENTVALIDATION":
			vs.Validation = value
		case "__VIEWSTATE":
			vs.State = value
		case "__VIEWSTATEGENERATOR":
			vs.Generator = value
		}
	})

	return vs
}

func buildForm(opts FormOptions) url.Values {
	form := url.Values{}

	// Add ViewState fields
	if opts.ViewState.State != "" {
		form.Set("__VIEWSTATE", opts.ViewState.State)
	}
	if opts.ViewState.Generator != "" {
		form.Set("__VIEWSTATEGENERATOR", opts.ViewState.Generator)
	}
	if opts.ViewState.Validation != "" {
		form.Set("__EVENTVALIDATION", opts.ViewState.Validation)
	}

	// Add ASP.NET fields
	if opts.Target != "" {
		form.Set("__EVENTTARGET", opts.Target)
	}
	if opts.Argument != "" {
		form.Set("__EVENTARGUMENT", opts.Argument)
	}
	if opts.LastFocus != "" {
		form.Set("__LASTFOCUS", opts.LastFocus)
	}

	// Add extra fields
	for k, v := range opts.ExtraFields {
		form.Set(k, v)
	}

	return form
}

func (r *Retalix) getViewState(ctx context.Context) (err error) {
	resp, _, err := r.get(ctx, baseURL, nil)
	if err != nil {
		return fmt.Errorf("error getting retalix login HTML: %w", err)
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return err
	}

	vs := extractViewState(doc)
	if vs.State == "" || vs.Validation == "" {
		return &Error{
			Type:    ValidationError,
			Message: "missing required viewstate fields",
		}
	}

	r.creds.ViewState = vs

	return nil
}

func (r *Retalix) getApptFormViewState(ctx context.Context) error {
	resp, _, err := r.get(ctx, buildURL(paths.CreateAppt), nil)
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to get appointment form page",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return err
	}

	vs := extractViewState(doc)
	if vs.State == "" || vs.Validation == "" || vs.Generator == "" {
		return &Error{
			Type:    ValidationError,
			Message: "missing required viewstate fields in appointment form",
		}
	}

	r.creds.AppointmentVS = vs

	return nil
}

func (r *Retalix) postApptFormForValidation(ctx context.Context, poNumbers []string, warehouse models.Warehouse) error {
	form := buildForm(FormOptions{
		Target: "Customers",
		ViewState: ViewState{
			State:      r.creds.AppointmentVS.State,
			Generator:  r.creds.AppointmentVS.Generator,
			Validation: r.creds.AppointmentVS.Validation,
		},
		ExtraFields: map[string]string{
			"PONums":    strings.Join(poNumbers, ", "),
			"Customers": warehouse.WarehouseID,
			"AddPOs":    "Add",
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.CreateAppt), strings.NewReader(form.Encode()))
	if err != nil {
		return &Error{
			Type:    FormError,
			Message: "failed to submit PO validation form",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return err
	}

	return validateRetalixResponse(doc)
}

func (r *Retalix) beginForceApptFormValidation(ctx context.Context, warehouse models.Warehouse) error {
	// First step: Begin validation
	beginQuery := url.Values{
		"req": []string{"ins"},
		"c":   []string{warehouse.WarehouseID},
	}

	resp, _, err := r.get(ctx, buildURLWithQuery(paths.AddOrders, beginQuery), nil)
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to initiate PO validation",
		}
	}

	var doc *goquery.Document

	doc, err = parseHTML(resp)
	if err != nil {
		return err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return err
	}

	// Second step: Force validation
	forceQuery := url.Values{
		"req": []string{"val"},
		"c":   []string{warehouse.WarehouseID},
	}

	resp, _, err = r.get(ctx, buildURLWithQuery(paths.AddOrders, forceQuery), nil)
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to complete PO validation",
		}
	}

	doc, err = parseHTML(resp)
	if err != nil {
		return err
	}

	return validateRetalixResponse(doc)
}

func (r *Retalix) getValidatedApptForm(
	ctx context.Context,
	warehouse models.Warehouse,
) (result []models.ValidatedPONumber, err error) {

	form := buildForm(FormOptions{
		ExtraFields: map[string]string{
			"AppDestID":    warehouse.WarehouseID,
			"AppCustName":  warehouse.WarehouseName,
			"ForceManual":  "TRUE",
			"ConfirmRedir": "TRUE",
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.CreateAppt), strings.NewReader(form.Encode()))
	if err != nil {
		return nil, &Error{
			Type:    ResponseError,
			Message: "failed to get validated appointment form",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return nil, err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return nil, err
	}

	// Parse PO table
	tableRows := doc.Find("table#OrderList tr")
	if tableRows.Length() <= 2 {
		return nil, &Error{
			Type:    ValidationError,
			Message: "no PO numbers found in validation response",
		}
	}

	// Extract PO information
	var validatedPOs []models.ValidatedPONumber
	tableRows.Each(func(i int, element *goquery.Selection) {
		// Skip header row
		if i == 0 {
			return
		}

		// Extract and clean PO data
		entryPO := strings.TrimSpace(element.Find("td:nth-of-type(2)").Text())
		entryDate := strings.TrimSpace(element.Find("td:nth-of-type(3)").Text())

		// Skip empty rows
		if entryPO == "" || entryPO == "PO #" {
			return
		}

		// Check for errors
		entryHasError := strings.Contains(entryDate, "ERROR")
		entryError := ""
		if entryHasError {
			entryError = entryDate
		}

		validatedPOs = append(validatedPOs, models.ValidatedPONumber{
			PONumber: entryPO,
			IsValid:  !entryHasError,
			Error:    entryError,
		})
	})

	return validatedPOs, nil
}

func (r *Retalix) getScheduleView(ctx context.Context, warehouse models.Warehouse) (*goquery.Document, error) {
	form := buildForm(FormOptions{
		ExtraFields: map[string]string{
			"AppDestID":    warehouse.WarehouseID,
			"AppCustName":  warehouse.WarehouseName,
			"AppDate":      "",
			"AppComments":  "",
			"ForceManual":  "FALSE",
			"ConfirmRedir": "TRUE",
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.ScheduleAppt), strings.NewReader(form.Encode()))
	if err != nil {
		return nil, &Error{
			Type:    ResponseError,
			Message: "failed to get schedule view",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return nil, err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return nil, err
	}

	return doc, nil
}

func (r *Retalix) getAvailableDates(doc *goquery.Document) []string {
	var dates []string

	doc.Find("select#AppointmentDate option").Each(func(_ int, s *goquery.Selection) {
		if value, exists := s.Attr("value"); exists {
			dates = append(dates, value)
		}
	})

	return dates
}

func (r *Retalix) getTimesForDate(
	ctx context.Context,
	date string,
	_ models.Warehouse,
	viewState map[string]string,
) ([]time.Time, error) {

	form := buildForm(FormOptions{
		Target: "AppointmentDate",
		ViewState: ViewState{
			State:      viewState["viewState"],
			Generator:  viewState["viewStateGenerator"],
			Validation: viewState["eventValidation"],
		},
		ExtraFields: map[string]string{
			"AppointmentDate": date,
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.ScheduleAppt), strings.NewReader(form.Encode()))
	if err != nil {
		return nil, &Error{
			Type:    ResponseError,
			Message: fmt.Sprintf("failed to get appointment times for date %s", date),
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return nil, err
	}

	if err := validateRetalixResponse(doc); err != nil {
		return nil, err
	}

	// Parse available times
	var times []time.Time
	timeFormat := "1/2/2006 3:04:05 PM"

	doc.Find("select#Appointments option").Each(func(_ int, s *goquery.Selection) {
		if value, exists := s.Attr("value"); exists {
			if t, err := time.Parse(timeFormat, value); err == nil {
				times = append(times, t)
			}
		}
	})

	// Validate we got some times
	if len(times) == 0 {
		return nil, &Error{
			Type:    ValidationError,
			Message: fmt.Sprintf("no appointment times available for date %s", date),
		}
	}

	return times, nil
}

// formatAppointmentTime handles the specific time format needed to select a live appointment time
func formatAppointmentTime(t time.Time) string {
	// NOTE: Expected Format: "2/13/2025 6:00:00 AM"
	return t.Format("1/2/2006 3:04:05 PM")
}

// submitReservation clicks the Reserve button and returns the confirmation page
func (r *Retalix) submitReservation(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ViewState,
) (*goquery.Document, error) {

	// Build query params matching the actual request
	query := url.Values{
		"req": []string{"ins"},
		"t":   []string{formatAppointmentTime(req.StartTime)},
		"d":   []string{req.StartTime.Format("1/2/2006")},
		"uc":  []string{""},
		"lp":  []string{"-1"},
		"del": []string{"0"},
		"ed":  []string{"undefined"},
		"et":  []string{"undefined"},
		"tl":  []string{"undefined"},
	}

	resp, _, err := r.get(ctx, buildURLWithQuery(paths.ScheduleApp, query), nil)
	if err != nil {
		return nil, &Error{
			Type:    FormError,
			Message: "failed to submit reservation",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return nil, err
	}

	return doc, validateRetalixResponse(doc)
}

// ConfirmationDetails holds the parsed confirmation information
type ConfirmationDetails struct {
	ID       string    // Confirmation number
	Number   string    // Same as ID, for consistency with other integrations
	Time     time.Time // Confirmed appointment time
	Duration string    // Appointment duration
	Location string    // Facility location
}

// extractConfirmationFromValidation extracts confirmation details from the validation response
func (r *Retalix) extractConfirmationFromValidation(doc *goquery.Document) (*ConfirmationDetails, error) {
	confirmNum := doc.Find("#lblConfirmnum").Text()
	if confirmNum == "" {
		return nil, &Error{
			Type:    ValidationError,
			Message: "confirmation number not found in response",
		}
	}

	return &ConfirmationDetails{
		Number: confirmNum,
		ID:     confirmNum,
	}, nil
}

// TODO: Test this with an open appointment so we can reduce the repeated calls in step 6 of making an appointment.
// extractConfirmationDetails parses the confirmation page to get appointment details
// func (r *Retalix) extractConfirmationDetails(doc *goquery.Document) (ConfirmationDetails, error) {
// 	var details ConfirmationDetails
// 	var err error

// 	// Find the confirmation table
// 	table := doc.Find("table#AppInfo")
// 	if table.Length() == 0 {
// 		return details, &Error{
// 			Type:    ValidationError,
// 			Message: "confirmation table not found",
// 		}
// 	}

// 	// Extract confirmation number
// 	confNum := table.Find("tr#AppInfo__ctl2_Confirmation td:nth-child(2) p").Text()
// 	if confNum == "" {
// 		return details, &Error{
// 			Type:    ValidationError,
// 			Message: "confirmation number not found",
// 		}
// 	}
// 	details.ID = confNum
// 	details.Number = confNum

// 	// Extract appointment time
// 	timeStr := table.Find("span#AppInfo__ctl2_TimeVal").Text()
// 	if timeStr != "" {
// 		// Parse "Thursday, February 13, 2025 8:00 AM"
// 		details.Time, err = time.Parse("Monday, January 2, 2006 3:04 PM", timeStr)
// 		if err != nil {
// 			return details, &Error{
// 				Type:    ValidationError,
// 				Message: fmt.Sprintf("failed to parse appointment time: %v", err),
// 			}
// 		}
// 	}

// 	// Extract duration
// 	details.Duration = strings.TrimSpace(table.Find("tr#AppInfo__ctl2_DurationRow td:nth-child(2)").Text())

// 	// Extract location
// 	details.Location = strings.TrimSpace(table.Find("tr#AppInfo__ctl2_LocRow td:nth-child(2) p").Text())

// 	// Validate we got minimum required info
// 	if details.ID == "" {
// 		return details, &Error{
// 			Type:    ValidationError,
// 			Message: "failed to extract confirmation number",
// 		}
// 	}

// 	return details, nil
// }

// changeApptDetails handles appointment configuration
func (r *Retalix) changeApptDetails(
	ctx context.Context,
	warehouse models.Warehouse,
	lumperStatus string,
	opts models.SchedulingOptions,
) (*goquery.Document, error) {
	// First get schedule view and its ViewState
	initialForm := buildForm(FormOptions{
		ExtraFields: map[string]string{
			"AppDestID":    warehouse.WarehouseID,
			"AppCustName":  warehouse.WarehouseName,
			"AppDate":      "",
			"AppComments":  "",
			"ForceManual":  "FALSE",
			"ConfirmRedir": "TRUE",
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.ScheduleAppt), strings.NewReader(initialForm.Encode()))
	if err != nil {
		return nil, &Error{
			Type:    ResponseError,
			Message: "failed to access appointment details page",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return nil, err
	}

	if err := validateRetalixResponse(doc, WithIgnoreReviewRequired()); err != nil {
		return nil, err
	}

	// Extract ViewState from schedule view
	vs := extractViewState(doc)
	if vs.State == "" || vs.Validation == "" || vs.Generator == "" {
		return nil, &Error{
			Type:    ValidationError,
			Message: "missing viewstate in schedule response",
		}
	}

	// Parse default values from the HTML
	defaultAppointmentDate := ""
	doc.Find("select#AppointmentDate option[selected]").Each(func(_ int, s *goquery.Selection) {
		if val, exists := s.Attr("value"); exists {
			defaultAppointmentDate = val // e.g., "3/20/2025 12:00:00 AM"
		}
	})

	defaultDeliveryDate := ""
	if val, exists := doc.Find("input#DeliveryDate").Attr("value"); exists {
		defaultDeliveryDate = val // e.g., "3/19/2025"
	} else {
		return nil, &Error{
			Type:    ValidationError,
			Message: "failed to parse default DeliveryDate",
		}
	}

	// Determine time preference: use provided or default to "Anytime"
	tp := models.TimePreferenceAnytime
	if opts.TimePreference != "" {
		tp = opts.TimePreference
	}

	// Determine AMPM based on time preference
	ampm := "AM" // Default for "Anytime" and unspecified
	switch tp {
	case models.TimePreferenceBeforeNoon:
		ampm = "AM"
	case models.TimePreferenceNoonToSix, models.TimePreferenceAfterSix:
		ampm = "PM"
	}

	formFields := map[string]string{
		"Lumper":                    lumperStatus,        // e.g, "LumperYes"
		"OrderList:_ctl2:ErrorCode": "1",                 // Hardcoded as per HAR file
		"DeliveryType":              "LiveUnload",        // Fixed for this appointment type
		"ArrivalDate":               "",                  // Empty as per HAR file
		"ArrivalHour":               "00",                // Default value
		"ArrivalMin":                "00",                // Default value
		"AMPM":                      ampm,                // Determined from TimePreference
		"Trailer":                   "",                  // Empty as per HAR file
		"Comments":                  "",                  // Empty unless specified
		"DeliveryDate":              defaultDeliveryDate, // Parsed from HTML, overridden below if provided
		"RequestedDeliveryTime":     string(tp),          // Default "Anytime", overridden below if provided
	}

	// Add appointment date if not empty
	if defaultAppointmentDate != "" {
		formFields["AppointmentDate"] = defaultAppointmentDate
	}

	// Add date if provided
	if !opts.RequestedDate.IsZero() {
		formFields["DeliveryDate"] = opts.RequestedDate.Format("1/2/2006") // MM/DD/YYYY format
	}

	// Submit appointment details with extracted ViewState
	detailsForm := buildForm(FormOptions{
		Target:      "RequestedDeliveryTime",
		ViewState:   vs,
		ExtraFields: formFields,
	})

	resp, _, err = r.postForm(ctx, buildURL(paths.ScheduleAppt), strings.NewReader(detailsForm.Encode()))
	if err != nil {
		return nil, &Error{
			Type:    ResponseError,
			Message: "failed to submit appointment details",
		}
	}

	doc, err = parseHTML(resp)
	if err != nil {
		return nil, err
	}

	// Update stored ViewState
	vs = extractViewState(doc)
	r.creds.AppointmentVS = vs

	return doc, validateRetalixResponse(doc, WithIgnoreReviewRequired())
}

// submitAppt handles final submission
func (r *Retalix) submitAppt(
	ctx context.Context,
	lumperStatus LumperStatus,
	note string,
	initialDoc *goquery.Document,
	_ models.SchedulingOptions,
) error {

	deliveryDate := ""
	if val, exists := initialDoc.Find("input#DeliveryDate").Attr("value"); exists {
		deliveryDate = val // e.g., "3/19/2025"
	} else {
		return &Error{
			Type:    ValidationError,
			Message: "failed to parse default DeliveryDate",
		}
	}

	// First request: initialization ('ins')
	insQuery := url.Values{
		"req": {"ins"},
		"t":   {"manual"},
		"d":   {deliveryDate},
		"lp":  {lumperStatus.toParam()},
		"uc":  {note},
	}

	// TODO: check for 200 status code
	_, _, err := r.get(ctx, buildURLWithQuery(paths.ScheduleApp, insQuery), nil)
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to submit initial appointment request",
		}
	}

	// Second request: validation ('val')
	valQuery := url.Values{
		"req": {"val"},
		"t":   {"manual"},
		"d":   {deliveryDate},
		"lp":  {lumperStatus.toParam()},
		"uc":  {note},
	}

	// TODO: check for 200 status code
	_, _, err = r.get(ctx, buildURLWithQuery(paths.ScheduleApp, valQuery), nil)
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to validate appointment request",
		}
	}

	form := buildForm(FormOptions{
		ExtraFields: map[string]string{
			"AppTime":        "manual",
			"AppComments":    "",
			"ConfirmRedir":   "TRUE",
			"UpdateComments": note,
			"ccApp":          "",
		},
	})

	resp, _, err := r.postForm(ctx, buildURL(paths.ConfirmAppt), strings.NewReader(form.Encode()))
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to submit appointment confirmation request",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return err
	}

	return validateRetalixResponse(doc, WithIgnoreReviewRequired())
}
