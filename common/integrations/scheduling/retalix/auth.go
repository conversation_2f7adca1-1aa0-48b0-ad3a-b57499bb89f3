package retalix

import (
	"context"
	"fmt"
	"net/http/cookiejar"
	"strings"
	"time"

	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func New(ctx context.Context, scheduler models.Integration) (*Retalix, error) {
	retalix, err := initialize(ctx, scheduler)
	if err != nil {
		return nil, err
	}

	err = retalix.Auth(ctx)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	return retalix, nil
}

func initialize(ctx context.Context, scheduler models.Integration) (*Retalix, error) {
	if scheduler.Username == "" {
		return nil, &Error{
			Type:    ValidationError,
			Message: "missing required username",
		}
	}

	password, err := crypto.DecryptAESGCM(ctx, string(scheduler.EncryptedPassword), nil)
	if err != nil {
		return nil, &Error{
			Type:    ValidationError,
			Message: "failed to decrypt password",
		}
	}

	creds := &Credentials{
		Username: scheduler.Username,
		Password: password,
		Loginx:   "1",
		Loginy:   "2",
	}

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create Retalix cookie jar: %w", err)
	}

	httpClient := otel.TracingHTTPClient()
	httpClient.Timeout = 20 * time.Second
	httpClient.Jar = cookieJar

	retalix := Retalix{
		scheduler:  scheduler,
		httpClient: httpClient,
		creds:      creds,
	}

	err = retalix.getViewState(ctx)
	if err != nil {
		return nil, err
	}

	return &retalix, nil
}

func (r *Retalix) Auth(ctx context.Context) error {
	log.Info(ctx, "attempting retalix authentication")

	// Build login form
	form := buildForm(FormOptions{
		ViewState: ViewState{
			State:      r.creds.ViewState.State,
			Validation: r.creds.ViewState.Validation,
		},
		ExtraFields: map[string]string{
			"Username": r.creds.Username,
			"Pass":     r.creds.Password,
			"Login.x":  r.creds.Loginx,
			"Login.y":  r.creds.Loginy,
		},
	})

	// Submit login request
	resp, _, err := r.postForm(ctx, buildURL(paths.Login), strings.NewReader(form.Encode()))
	if err != nil {
		return &Error{
			Type:    ResponseError,
			Message: "failed to submit login request",
		}
	}

	doc, err := parseHTML(resp)
	if err != nil {
		return err
	}

	// Check for common error messages
	if strings.Contains(string(resp), "An error occurred while processing a request") {
		return &Error{
			Type:    ValidationError,
			Message: "invalid credentials or session expired",
		}
	}

	// Check for successful login by looking for home page element
	if doc.Find("div.home_description").Text() != "Please choose an option from the above main menu." {
		return &Error{
			Type:    ValidationError,
			Message: "login failed - invalid credentials or session expired",
		}
	}

	log.Info(ctx, "successfully authenticated retalix client")

	return nil
}

func (r *Retalix) OnboardScheduler(_ context.Context) (resp models.OnboardSchedulerResponse, err error) {
	return models.OnboardSchedulerResponse{}, errtypes.NotImplemented(models.Retalix, "OnboardScheduler")
}
