package retalix

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func (r *Retalix) ValidateAppointment(
	ctx context.Context,
	poNumbers []string,
	warehouse models.Warehouse,
	_ ...models.SchedulingOption,
) (result []models.ValidatedPONumber, err error) {

	err = r.getApptFormViewState(ctx)
	if err != nil {
		return nil, err
	}

	err = r.postApptFormForValidation(ctx, poNumbers, warehouse)
	if err != nil {
		return nil, err
	}

	err = r.beginForceApptFormValidation(ctx, warehouse)
	if err != nil {
		return nil, err
	}

	validatedPOs, err := r.getValidatedApptForm(ctx, warehouse)
	if err != nil {
		return nil, err
	}

	return validatedPOs, nil
}
