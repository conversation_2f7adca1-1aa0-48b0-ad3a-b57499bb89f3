package yardview

import (
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

const (
	YardViewPlatform = "YardView"

	ScheduleTypeInbound              = "Inbound"
	ScheduleTypeTargetAirFreightOnly = "TargetAirFreightOnly"
	ScheduleTypeTaxAirFreight        = "TaxAirFreight"
	ScheduleTypeElwood               = "Elwood"
)

type (
	ScheduleFilterType string

	CyclopsYardViewBaseRequest struct {
		models.CyclopsBaseRequest
		// Determines which source e.g., Elwood or Allentown to use if mode is API
		Source string `json:"source,omitempty"`
	}

	AppointmentData struct {
		AppointmentID string                       `json:"appointmentId"`
		Duration      int                          `json:"duration"`
		Notes         string                       `json:"notes"`
		ScheduledTime string                       `json:"scheduledTime"`
		Status        string                       `json:"status"`
		Warehouse     *models.CyclopsWarehouseInfo `json:"warehouse,omitempty"`
		Extended      any                          `json:"extended,omitempty"`
		Capacity      int                          `json:"capacity"`
	}
)

func (s ScheduleFilterType) IsValid() bool {
	switch s {
	case ScheduleTypeInbound, ScheduleTypeTargetAirFreightOnly, ScheduleTypeTaxAirFreight, ScheduleTypeElwood:
		return true
	default:
		return false
	}
}

func convertToAppointment(appt models.CyclopsAppointment, warehouseTimezone string) (models.Appointment, error) {
	// Early return for empty or placeholder scheduled times
	if appt.ScheduledTime == "" || appt.ScheduledTime == "-" {
		return models.Appointment{
			ExternalID: appt.AppointmentID,
			StartTime:  time.Time{},
			Status:     appt.Status,
		}, nil
	}

	var scheduledTime time.Time
	var err error

	// Try different time parsing layouts in order of preference
	layouts := []struct {
		layout   string
		useLocal bool
	}{
		{time.RFC3339, false},
		{time.RFC3339Nano, false},
		{"2006-01-02T15:04:05", true},
	}

	for _, l := range layouts {
		if l.useLocal && warehouseTimezone != "" {
			if loc, locErr := time.LoadLocation(warehouseTimezone); locErr == nil {
				scheduledTime, err = time.ParseInLocation(l.layout, appt.ScheduledTime, loc)
			} else {
				scheduledTime, err = time.Parse(l.layout, appt.ScheduledTime)
			}
		} else {
			scheduledTime, err = time.Parse(l.layout, appt.ScheduledTime)
		}

		if err == nil {
			break
		}
	}

	if err != nil {
		return models.Appointment{}, fmt.Errorf("invalid scheduled time format: %w", err)
	}

	// Normalize to warehouse timezone if provided
	if warehouseTimezone != "" {
		if loc, locErr := time.LoadLocation(warehouseTimezone); locErr == nil {
			scheduledTime = scheduledTime.In(loc)
		}
	}

	return models.Appointment{
		ExternalID: appt.AppointmentID,
		StartTime:  scheduledTime,
		Status:     appt.Status,
	}, nil
}
