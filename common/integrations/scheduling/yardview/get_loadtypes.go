package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetLoadTypesRequest struct {
		CyclopsYardViewBaseRequest
	}
)

func (y *YardView) GetLoadTypes(ctx context.Context, req models.GetLoadTypesRequest) ([]models.LoadType, error) {
	return y.GetLoadTypesWithCyclops(ctx, req)
}

func (y *YardView) GetLoadTypesWithCyclops(
	ctx context.Context,
	req models.GetLoadTypesRequest,
) ([]models.LoadType, error) {

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadTypesWithCyclops", nil)
	defer func() { metaSpan.End(nil) }()

	cyclopsReq := GetLoadTypesRequest{
		CyclopsYardViewBaseRequest: CyclopsYardViewBaseRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionGetLoadTypes,
				UserID:      y.scheduler.Username,
				Mode:        models.CyclopsModeAPI,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
			Source: req.WarehouseID,
		},
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetLoadTypesResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return convertToLoadTypes(res.LoadTypes), nil
}

func convertToLoadTypes(loadTypes []models.CyclopsLoadTypeData) []models.LoadType {
	lts := make([]models.LoadType, 0)

	for _, lt := range loadTypes {
		loadType := models.LoadType{
			ID:                     strconv.Itoa(lt.PID),
			Name:                   lt.Name,
			AllowCarrierScheduling: true,
		}

		lts = append(lts, loadType)
	}

	return lts
}
