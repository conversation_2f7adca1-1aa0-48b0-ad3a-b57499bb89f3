package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetAppointmentsRequest struct {
		CyclopsYardViewBaseRequest

		StartDate  time.Time `json:"startDate"`
		EndDate    time.Time `json:"endDate"`
		CustomerID string    `json:"customerId"`
		// Status integer values for Yardview API (handled in Cyclops)
		// AppointmentStatusPending = 0
		// AppointmentStatusCancelled = 1
		// AppointmentStatusCompleted = 2
		Status models.AppointmentStatus `json:"status"`
	}
)

func (y *YardView) GetAppointments(
	ctx context.Context,
	req models.GetAppointmentsRequest,
) ([]models.Appointment, error) {

	return y.GetAppointmentsWithCyclops(ctx, req)
}

func (y *YardView) GetAppointmentsWithCyclops(
	ctx context.Context,
	req models.GetAppointmentsRequest,
) ([]models.Appointment, error) {

	warehouseID := req.WarehouseID
	status := req.Status
	startDate := req.StartDate
	endDate := req.EndDate
	customerID := req.CustomerID
	errorOnEmptyAppointments := req.ErrorOnEmptyAppointments

	// Set mode based on Yardview warehouse, currently only support Yardview API for Target/Allentown warehouses.
	var cyclopsMode models.CyclopsMode
	if req.Warehouse != nil &&
		req.Warehouse.Source == models.YardViewSource &&
		(req.Warehouse.WarehouseName == "Allentown" ||
			req.Warehouse.WarehouseName == "Target") {
		cyclopsMode = models.CyclopsModeAPI
	} else {
		cyclopsMode = models.CyclopsModeSelenium
	}

	cyclopsReq := GetAppointmentsRequest{
		CyclopsYardViewBaseRequest: CyclopsYardViewBaseRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionGetAppointment,
				UserID:      y.scheduler.Username,
				Mode:        cyclopsMode,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
			Source: warehouseID,
		},
		Status:     status,
		StartDate:  startDate,
		EndDate:    endDate,
		CustomerID: customerID,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return []models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	if len(res.Appointments) < 1 && errorOnEmptyAppointments {
		return []models.Appointment{}, &models.CyclopsError{
			Message: "no appointments found",
			Errors:  []string{"no appointments found"},
		}
	}

	// Extract timezone from request warehouse if available
	warehouseTimezone := ""
	if req.Warehouse != nil && req.Warehouse.WarehouseTimezone != "" {
		warehouseTimezone = req.Warehouse.WarehouseTimezone
	} else {
		log.Warn(
			ctx,
			"No warehouse timezone available for YardView appointments, falling back to UTC",
			zap.String("warehouseID", req.WarehouseID),
		)
	}

	var appts []models.Appointment
	for _, appt := range res.Appointments {
		item, err := convertToAppointment(appt, warehouseTimezone)
		if err != nil {
			log.Error(ctx, "failed to convert cyclops appointment", zap.Any("appt", appt), zap.Error(err))
			continue
		}

		// Special handling: Allentown YardView returns RFC3339 timestamps with a Central offset.
		// For capacity-based filtering to work, reinterpret the wall-clock time into America/New_York
		// so that appointments align with slots (which are parsed in the warehouse timezone).
		// This is only applicable for Allentown YardView API mode.
		if req.Warehouse != nil &&
			cyclopsMode == models.CyclopsModeAPI &&
			req.Warehouse.Source == models.YardViewSource &&
			req.Warehouse.WarehouseName == "Allentown" &&
			appt.ScheduledTime != "" &&
			!item.StartTime.IsZero() {
			if loc, locErr := time.LoadLocation("America/New_York"); locErr == nil {
				// Parse the original RFC3339 string to preserve the wall-clock components (HH:MM)
				// then rebuild a time.Time in the desired timezone without shifting the wall time.
				if parsed, pErr := time.Parse(time.RFC3339, appt.ScheduledTime); pErr == nil {
					item.StartTime = time.Date(
						parsed.Year(), parsed.Month(), parsed.Day(),
						parsed.Hour(), parsed.Minute(), parsed.Second(), parsed.Nanosecond(),
						loc,
					)
				}
			}
		}

		appts = append(appts, item)
	}

	return appts, nil
}
