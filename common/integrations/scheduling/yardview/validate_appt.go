package yardview

import (
	"context"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

func (y *YardView) ValidateAppointment(
	_ context.Context,
	_ []string,
	_ models.Warehouse,
	_ ...models.SchedulingOption,
) ([]models.ValidatedPONumber, error) {

	return []models.ValidatedPONumber{}, errtypes.NotImplemented(models.YardView, "ValidateAppointment")
}
