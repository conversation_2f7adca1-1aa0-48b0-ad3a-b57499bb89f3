package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetSlotsRequest struct {
		CyclopsYardViewBaseRequest
		StartDate   string             `json:"startDate"`
		EndDate     string             `json:"endDate"`
		LocationID  string             `json:"locationId,omitempty"`
		SchedulePID ScheduleFilterType `json:"schedulePID,omitempty"`
		WeekDate    string             `json:"weekDate"`
	}

	GetSlotsResponse struct {
		models.CyclopsBaseResponse
		Appointments []AppointmentData          `json:"appointments"`
		PlatformData models.CyclopsPlatformData `json:"platformData"`
	}
)

func (y *YardView) GetOpenSlots(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	return y.GetOpenSlotsWithCyclops(ctx, loadTypeID, req)
}

func (y *YardView) GetOpenSlotsWithCyclops(
	ctx context.Context,
	_ string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	if req.RequestType == "" {
		req.RequestType = models.RequestTypePickup
	}

	if !req.RequestType.IsValid() {
		return nil, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	filter := ScheduleFilterType(req.FilterType)
	if req.FilterType != "" && !filter.IsValid() {
		return nil, fmt.Errorf("invalid schedule filter: %s", filter)
	}

	cyclopsReq := GetSlotsRequest{
		CyclopsYardViewBaseRequest: CyclopsYardViewBaseRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionGetOpenSlots,
				UserID:      y.scheduler.Username,
				Mode:        models.CyclopsModeAPI,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
			Source: req.WarehouseID,
		},
		SchedulePID: filter,
		WeekDate:    req.Start.Format("2006-01-02"),
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var res GetSlotsResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return nil, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	// TODO: Update get open slots response type to be named slots instead of appointments
	if len(res.Appointments) == 0 {
		return nil, &models.CyclopsError{
			Message: "no slots found",
			Errors:  []string{"no slots found"},
		}
	}

	// Extract warehouse timezone from request if available
	warehouseTimezone := ""
	if req.Warehouse != nil && req.Warehouse.WarehouseTimezone != "" {
		warehouseTimezone = req.Warehouse.WarehouseTimezone
	} else {
		log.Warn(
			ctx,
			"No warehouse timezone available for YardView, falling back to UTC",
			zap.String("warehouseID", req.WarehouseID),
		)
	}

	return convertToSlots(ctx, res.Appointments, warehouseTimezone), nil
}

func convertToSlots(ctx context.Context, appointments []AppointmentData, warehouseTimezone string) []models.Slot {
	ctx = log.With(ctx, zap.String("source", string(models.YardViewSource)))

	slots := make([]models.Slot, 0)
	for _, appt := range appointments {
		if appt.Status != "AVAILABLE" {
			continue
		}

		var times []time.Time

		var t time.Time
		var err error

		if warehouseTimezone != "" {
			// Parse time in warehouse timezone
			loc, locErr := time.LoadLocation(warehouseTimezone)
			if locErr != nil {
				log.Warn(
					ctx,
					"Invalid warehouse timezone, falling back to UTC",
					zap.String("timezone", warehouseTimezone),
					zap.Error(locErr),
				)
				t, err = time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
			} else {
				t, err = time.ParseInLocation("2006-01-02T15:04:05", appt.ScheduledTime, loc)
				log.Debug(
					ctx,
					"Parsed YardView appointment time in warehouse timezone",
					zap.String("scheduledTime", appt.ScheduledTime),
					zap.String("timezone", warehouseTimezone),
					zap.String("parsedTime", t.Format("2006-01-02T15:04:05Z07:00")),
				)
			}
		} else {
			// Fallback to UTC parsing if no timezone provided
			t, err = time.Parse("2006-01-02T15:04:05", appt.ScheduledTime)
		}

		if err != nil {
			log.Infof(
				ctx,
				"Invalid time format for YardView warehouse, ScheduledTime %s: %v",
				appt.ScheduledTime,
				zap.Error(err),
			)

			continue
		}
		times = append(times, t)

		if len(times) > 0 {
			slot := models.Slot{
				Dock: models.Dock{
					ID: appt.AppointmentID,
				},
				StartTimes: times,
				Capacity:   appt.Capacity,
			}
			slots = append(slots, slot)
		}
	}

	return slots
}
