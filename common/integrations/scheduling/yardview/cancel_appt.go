package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

// TODO: We need to pass in `source` aka the warehouse id if we want to ping the correct API (Elwood or Allentown).
// This would require us to create a custom `YardViewGetAppointmentRequest`, which includes
// `YardViewCyclopsBaseRequest`. The reason we're not doing that right now is because the endpoint isn't being used.
// However, this method works for Selenium and targeting the Test API.
func (y *YardView) CancelAppointment(ctx context.Context, id, _ string) (models.Appointment, error) {
	return y.CancelAppointmentWithCyclops(ctx, id)
}

func (y *YardView) CancelAppointmentWithCyclops(ctx context.Context, id string) (models.Appointment, error) {
	req := models.CyclopsCancelAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    YardViewPlatform,
			Action:      models.ActionCancelAppointment,
			UserID:      y.scheduler.Username,
			Mode:        models.CyclopsModeAPI,
			Credentials: models.CyclopsCredentials{
				Username: y.creds.Username,
				Password: y.creds.Password,
			},
		},
		Appointment: models.CyclopsAppointmentData{
			AppointmentID: id,
		},
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsCancelAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	// No warehouse context available here; keep UTC parsing fallback
	return convertToAppointment(res.Appointment, "")
}
