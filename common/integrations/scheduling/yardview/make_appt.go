package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	MakeAppointmentRequestData struct {
		AppointmentID   int                      `json:"appointmentID"`
		AppointmentKey  string                   `json:"appointmentKey,omitempty"`
		ProID           string                   `json:"proID"`
		SchedulePID     ScheduleFilterType       `json:"schedulePID"`
		LocationPID     int                      `json:"locationPID,omitempty"`
		CarrierSCAC     string                   `json:"carrierSCAC,omitempty"`
		LoadTypePID     int                      `json:"loadTypePID"`
		StartTime       string                   `json:"startTime" validate:"required"`
		EndTime         string                   `json:"endTime" validate:"required"`
		Status          models.AppointmentStatus `json:"status" validate:"required"`
		AppointmentType models.RequestType       `json:"appointmentType" validate:"required"`
		Notes           string                   `json:"notes,omitempty"`
		UpdatedAt       string                   `json:"updatedAt" validate:"required"`
		UpdatedBy       string                   `json:"updatedBy,omitempty"`
		TrailerID       string                   `json:"trailerID,omitempty"`
		Weight          int                      `json:"weight,omitempty"`
	}

	MakeAppointmentRequest struct {
		CyclopsYardViewBaseRequest
		Appointment MakeAppointmentRequestData `json:"appointment"`
	}

	MakeAppointmentResponse struct {
		models.CyclopsBaseResponse
		Appointment Appointment `json:"appointment"`
	}

	Appointment struct {
		AppointmentPID int    `json:"appointmentPid"`
		AppointmentID  string `json:"appointmentId"`
		Duration       int    `json:"duration"`
		Location       string `json:"location"`
		ScheduledTime  string `json:"scheduledTime"`
		Status         string `json:"status"`
	}
)

func (y *YardView) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.YardView, "MakeAppointmentWithLoad")
}

func (y *YardView) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	opts ...models.SchedulingOption,
) (models.Appointment, error) {

	options := &models.SchedulingOptions{}
	options.Apply(opts...)

	return y.MakeAppointmentWithCyclops(ctx, req, options)
}

func (y *YardView) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	options *models.SchedulingOptions,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	var dockID int
	if req.DockID != "" {
		var err error
		dockID, err = strconv.Atoi(req.DockID)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("invalid dock ID format: %w", err)
		}
	}

	// CarrierSCAC is now optional - can be empty

	filter := ScheduleFilterType(options.SchedulePID)
	if options.SchedulePID != "" && !filter.IsValid() {
		return models.Appointment{}, errors.New("valid schedule filter type required for make appointment")
	}

	if options.LoadTypePID == 0 {
		return models.Appointment{}, errors.New("valid load type pid required for make appointment")
	}

	// Format time in warehouse timezone to match how GetOpenSlots parses times
	var startTimeStr, endTimeStr string
	if req.WarehouseTimezone != "" {
		loc, err := time.LoadLocation(req.WarehouseTimezone)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("invalid warehouse timezone %s: %w", req.WarehouseTimezone, err)
		}
		// Convert to warehouse timezone and format without timezone suffix (same as GetOpenSlots expects)
		localTime := req.StartTime.In(loc)
		startTimeStr = localTime.Format("2006-01-02T15:04:05")
		endTimeStr = localTime.Format("2006-01-02T15:04:05")
	} else {
		// Fallback to UTC format if no timezone provided
		startTimeStr = req.StartTime.Format("2006-01-02T15:04:05.000Z")
		endTimeStr = req.StartTime.Format("2006-01-02T15:04:05.000Z")
	}

	appointmentData := MakeAppointmentRequestData{
		ProID:           req.LoadTypeID,
		SchedulePID:     filter,
		LocationPID:     dockID,
		LoadTypePID:     options.LoadTypePID,
		AppointmentType: reqType,
		StartTime:       startTimeStr,
		EndTime:         endTimeStr,
		Status:          "",
		UpdatedAt:       time.Now().String(),
		AppointmentKey:  req.ApptKey,
		CarrierSCAC:     options.CarrierSCAC,
		Notes:           req.Notes,
		TrailerID:       req.TrailerID,
		Weight:          req.Weight,
	}

	cyclopsReq := MakeAppointmentRequest{
		CyclopsYardViewBaseRequest: CyclopsYardViewBaseRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    YardViewPlatform,
				Action:      models.ActionMakeAppointment,
				UserID:      y.scheduler.Username,
				Mode:        models.CyclopsModeAPI,
				Credentials: models.CyclopsCredentials{
					Username: y.creds.Username,
					Password: y.creds.Password,
				},
			},
			Source: req.WarehouseID,
		},
		Appointment: appointmentData,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res MakeAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	return models.Appointment{
		ExternalPrivateID: res.Appointment.AppointmentPID,
		ExternalID:        res.Appointment.AppointmentID,
		ConfirmationNo:    res.Appointment.AppointmentID,
		PONums:            req.PONums,
		StartTime:         req.StartTime,
		Status:            res.Appointment.Status,
	}, nil
}
