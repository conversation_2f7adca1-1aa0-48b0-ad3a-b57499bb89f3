package velostics

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

func (v *Velostics) GetAllWarehouses(ctx context.Context, _ ...models.SchedulingOption) ([]models.Warehouse, error) {
	var result GetWarehousesResp
	if err := v.get(ctx, "/facilities", nil, &result); err != nil {
		return nil, fmt.Errorf("error getting all warehouses: %w", err)
	}

	warehouses := make([]models.Warehouse, len(result.Items))
	for i, wh := range result.Items {
		warehouses[i] = models.Warehouse{
			WarehouseID:   wh.ID,
			WarehouseName: wh.Name,
			Source:        models.VelosticsSource,
		}
	}

	return warehouses, nil
}
