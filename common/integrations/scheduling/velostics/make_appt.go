package velostics

import (
	"context"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

func (v *Velostics) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	layout := "2006-01-02T15:04:05.000Z07:00"

	apptReq := MakeAppointmentReq{
		Appointment: AppointmentRequest{
			ArrivalTime: req.StartTime.Format(layout),
		},
	}

	var resp GetAppointmentResp
	if err := v.post(ctx, "/appointments", nil, apptReq, &resp); err != nil {
		return models.Appointment{}, fmt.Errorf("error creating appointment: %w", err)
	}

	startTime, err := time.Parse(layout, resp.ArrivalTime)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("parsing arrival time: %w", err)
	}

	return models.Appointment{
		Date:       startTime.Format(time.DateOnly),
		DockID:     resp.DockID,
		ExternalID: resp.ID,
		StartTime:  startTime,
	}, nil
}

func (v *Velostics) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.Velostics, "MakeAppointmentWithLoad")
}
