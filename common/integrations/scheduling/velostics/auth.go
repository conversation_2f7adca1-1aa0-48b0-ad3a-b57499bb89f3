package velostics

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func New(_ context.Context, source models.Integration) (*Velostics, error) {
	return &Velostics{Source: source}, nil
}

func (v *Velostics) OnboardScheduler(ctx context.Context) (models.OnboardSchedulerResponse, error) {
	var result models.OnboardSchedulerResponse

	accessToken, err := v.getToken(ctx)
	if err != nil {
		return result, err
	}

	result.Username = v.Source.Username
	result.AccessToken = accessToken

	return result, nil
}
