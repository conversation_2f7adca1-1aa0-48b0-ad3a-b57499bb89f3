package velostics

type (
	TokenResp struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		ExpiresIn   int    `json:"expires_in"`
	}

	Answer struct {
		QuestionID string `json:"question_id"`
		Question   string `json:"question"`
		Answer     string `json:"answer"`
		AnswerID   string `json:"answer_id"`
		Position   int    `json:"position"`
	}

	GetAppointmentResp struct {
		ID                       string   `json:"id"`
		FacilityID               string   `json:"facility_id"`
		CreatedAt                string   `json:"created_at"`
		UpdatedAt                string   `json:"updated_at"`
		PurchaseOrderIdentifiers string   `json:"purchase_order_identifiers"`
		ArrivalTime              string   `json:"arrival_time"`
		DockID                   string   `json:"dock_id"`
		Quantity                 string   `json:"quantity"`
		AppointmentTypeID        string   `json:"appointment_type_id"`
		CustomerName             string   `json:"customer_name"`
		CommodityType            string   `json:"commodity_type"`
		CreatedByAuditUserName   string   `json:"created_by_audit_user_name"`
		SchedulerID              string   `json:"scheduler_id"`
		ConfirmationID           string   `json:"confirmation_id"`
		EquipmentTypeID          string   `json:"equipment_type_id"`
		CarrierID                string   `json:"carrier_id"`
		Answers                  []Answer `json:"answers"`
	}

	AppointmentRequest struct {
		AnswersAttributes []struct {
			QuestionID string `json:"question_id"`
			Response   string `json:"response"`
		} `json:"answers_attributes"`
		AppointmentTypeID        string `json:"appointment_type_id"`
		ArrivalTime              string `json:"arrival_time"`
		CarrierID                string `json:"carrier_id"`
		CustomerName             string `json:"customer_name"`
		EquipmentTypeID          string `json:"equipment_type_id"`
		FacilityID               string `json:"facility_id"`
		PurchaseOrdersAttributes []struct {
			Identifier string `json:"identifier"`
		} `json:"purchase_orders_attributes"`
		SchedulerID string `json:"scheduler_id"`
	}

	MakeAppointmentReq struct {
		Appointment AppointmentRequest `json:"appointment"`
	}

	GetWarehousesResp struct {
		Items []struct {
			ID   string `json:"id"`
			Name string `json:"name"`
		} `json:"items"`
		Total int `json:"total"`
	}
)
