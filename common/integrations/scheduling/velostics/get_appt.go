package velostics

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (v *Velostics) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	path := fmt.Sprintf("/appointments/%s", id)

	var result GetAppointmentResp
	if err := v.get(ctx, path, nil, &result); err != nil {
		return models.Appointment{}, err
	}

	layout := "2006-01-02T15:04:05.000-07:00"
	startTime, err := time.Parse(layout, result.ArrivalTime)
	if err != nil {
		log.Error(ctx, "could not parse start time", zap.Error(err))
	}

	appt := models.Appointment{
		Date:       startTime.Format(time.DateOnly),
		DockID:     result.DockID,
		ExternalID: result.ID,
		StartTime:  startTime,
	}

	return appt, nil
}
