# Scheduling Integrations

This package contains the scheduling integrations for Drumkit.

## Overview

The scheduling integrations are responsible for creating appointments, validating appointments, and submitting appointments to a warehouse. They are called by Drumkit users via the `beacon-api` and return the results to the user.

Each integration is responsible for its own scheduling logic, and the `beacon-api` will call the appropriate integration based on the 1) source of the requested warehouse (e.g. OpenDock, Retalix) and 2) the user's service configuration.

## Resources

- [Architecture Design Record](https://www.notion.so/drumkitai/Architecture-Design-Records-ADRs-d7864aa357704f87a3fa08094977ded0?p=1ad2b16b087a80eeba2cd8f6f4afda74&pm=s)
- [Creating a Scheduling Integration Playbook](https://www.notion.so/drumkitai/Scheduling-Portal-Integration-Playbook-1a62b16b087a80f19c25c6f1b51eb8b4)
