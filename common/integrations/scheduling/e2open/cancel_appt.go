package e2open

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func (e *E2open) CancelAppointment(ctx context.Context, id, reason string) (models.Appointment, error) {
	req := AppointmentCancelationReq{}
	req.Identifiers.AppointmentID = id
	req.Identifiers.ReasonCode = reason

	var res AppointmentCancelationResp
	return models.Appointment{}, e.post(ctx, "appointments/v1/cancel-appointment", nil, &req, &res)
}
