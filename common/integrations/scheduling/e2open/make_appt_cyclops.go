package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments    []AppointmentData `json:"appointments"`
		RequestType     string            `json:"requestType"`
		Operation       string            `json:"operation"`
		CompanyName     string            `json:"companyName"`
		AppointmentDate string            `json:"apptDate"`
		ProIDFieldName  string            `json:"proIdFieldName,omitempty"`
	}
)

func (e *E2open) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) ([]models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return nil, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	// Handle multiple appointments if provided
	var appointments []AppointmentData
	if len(req.Appointments) > 0 {
		// Process multiple appointments
		for _, apptReq := range req.Appointments {
			appointmentData, err := e.buildAppointmentData(apptReq, req)
			if err != nil {
				return nil, fmt.Errorf("failed to build appointment data for %s: %w", apptReq.FreightTrackingID, err)
			}
			appointments = append(appointments, appointmentData)
		}
	} else {
		return nil, errors.New("appointments not received")
	}

	// Format appointment date to YYYY/MM/DD if provided
	formattedApptDate := ""
	if req.AppointmentDate != "" {
		if formatted, err := formatAppointmentDate(req.AppointmentDate); err == nil {
			formattedApptDate = formatted
		}
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    E2openPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      e.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: e.creds.Username,
				Password: e.creds.Password,
			},
		},
		Appointments:    appointments,
		RequestType:     req.AppointmentType,
		Operation:       req.Operation,
		CompanyName:     req.Company,
		AppointmentDate: formattedApptDate,
		ProIDFieldName:  req.ProIDFieldName,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if len(req.Appointments) > 0 {
		// Multiple appointments response
		var multiApptResp CyclopsMultipleAppointmentResponse
		if err = json.Unmarshal(body, &multiApptResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal multiple appointments response: %w", err)
		}

		if !multiApptResp.Success {
			return nil, &models.CyclopsError{
				Message: multiApptResp.Message,
				Errors:  multiApptResp.Errors,
			}
		}

		// Return all appointments
		appointments, err := convertToAppointments(multiApptResp)
		if err != nil {
			return nil, fmt.Errorf("failed to convert appointments: %w", err)
		}

		if len(appointments) == 0 {
			return nil, errors.New("no appointments returned from cyclops")
		}

		return appointments, nil
	}

	return nil, errors.New("appointments not received")
}

// buildAppointmentData builds appointment data for multiple appointments
func (e *E2open) buildAppointmentData(
	apptReq models.AppointmentData,
	globalReq models.MakeAppointmentRequest,
) (AppointmentData, error) {
	// Parse the start time from the appointment request
	startTime, err := time.Parse("2006-01-02T15:04:05", apptReq.Start)
	if err != nil {
		// Try alternative time formats
		if startTime, err = time.Parse(time.RFC3339, apptReq.Start); err != nil {
			return AppointmentData{}, fmt.Errorf("invalid start time format: %s", apptReq.Start)
		}
	}

	appointmentData := AppointmentData{
		ProID:           apptReq.FreightTrackingID,
		AppointmentTime: startTime.Format("2006-01-02T15:04:05"),
		Scac:            apptReq.Scac,
	}

	warehouse := e.getWarehouseDetails(apptReq, globalReq)

	appointmentData.Warehouse = warehouse

	return appointmentData, nil
}

// getWarehouseDetails retrieves warehouse details from database
func (e *E2open) getWarehouseDetails(
	apptReq models.AppointmentData,
	globalReq models.MakeAppointmentRequest,
) *models.CyclopsWarehouseInfo {
	return &models.CyclopsWarehouseInfo{
		City:     apptReq.City,
		State:    apptReq.State,
		ZipCode:  apptReq.ZipCode,
		Country:  apptReq.Country,
		StopType: globalReq.StopType,
	}
}
