package e2open

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (e *E2open) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	path := "/appointments/v1/fetch-appointment-details"
	req := GetAppointmentReq{}
	req.Identifiers.AppointmentID = id

	var result GetAppointmentResp
	if err := e.post(ctx, path, nil, req, &result); err != nil {
		return models.Appointment{}, err
	}

	var status string
	if len(result.Status) > 0 {
		if statusBytes, err := json.Marshal(result.Status); err == nil {
			status = string(statusBytes)
		}
	}

	layout := "2006-01-02T15:04:05.000-07:00"
	startTime, err := time.Parse(layout, result.Appointment.ArrivalWindow.StartDateTime)
	if err != nil {
		log.Error(ctx, "could not parse start time", zap.Error(err))
	}

	appt := models.Appointment{
		ExternalID:     result.Appointment.ID,
		ConfirmationNo: result.AppointmentConfirmationNumber,
		Date:           startTime.Format(time.DateOnly),
		StartTime:      startTime,
		Status:         status,
	}

	return appt, nil
}
