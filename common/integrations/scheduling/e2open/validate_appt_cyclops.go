package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsValidateAppointmentRequest struct {
		models.CyclopsBaseRequest
		ProID           string    `json:"proId"`
		FilterType      string    `json:"filterType,omitempty"`
		RequestType     string    `json:"requestType"`
		Operation       string    `json:"operation"`
		CompanyName     string    `json:"companyName"`
		AppointmentDate string    `json:"apptDate"`
		Warehouse       Warehouse `json:"warehouse"`
		FetchCompanies  bool      `json:"fetchCompanies,omitempty"`
		FetchOperations bool      `json:"fetchOperations,omitempty"`
		FetchFormFields bool      `json:"fetchFormFields,omitempty"`
		ProIDFieldName  string    `json:"proIdFieldName,omitempty"`
	}

	ValidatedAppointment struct {
		AppointmentID string            `json:"appointmentId"`
		Reference     string            `json:"reference"`
		ScheduledTime string            `json:"scheduledTime"`
		Duration      int               `json:"duration"`
		Location      string            `json:"location"`
		Status        string            `json:"status"`
		Notes         string            `json:"notes"`
		Warehouse     Warehouse         `json:"warehouse"`
		Extended      ValidatedExtended `json:"extended"`
	}

	Warehouse struct {
		Name     string `json:"name"`
		City     string `json:"city"`
		State    string `json:"state"`
		ZipCode  string `json:"zipCode"`
		Country  string `json:"country"`
		Website  string `json:"website"`
		StopType string `json:"stopType"`
	}

	ValidatedExtended struct {
		IsFCFS bool `json:"isFcfs"`
	}

	CompanyInfo struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	}

	OperationInfo struct {
		Name string `json:"name"`
		ID   string `json:"id"`
	}

	ProIDField struct {
		Label     string `json:"label"`
		FieldName string `json:"fieldName"`
	}

	DateField struct {
		Label     string `json:"label"`
		FieldName string `json:"fieldName"`
		Exists    bool   `json:"exists"`
	}

	ValidateAppointmentResponse struct {
		Success                    bool                   `json:"success"`
		Message                    string                 `json:"message"`
		Errors                     []string               `json:"errors"`
		Appointments               []ValidatedAppointment `json:"appointments"`
		Companies                  []CompanyInfo          `json:"companies,omitempty"`
		Operations                 []OperationInfo        `json:"operations,omitempty"`
		RequiresCompanySelection   bool                   `json:"requiresCompanySelection,omitempty"`
		RequiresOperationSelection bool                   `json:"requiresOperationSelection,omitempty"`
		ProIDFields                []ProIDField           `json:"proIdFields,omitempty"`
		DateFields                 []DateField            `json:"dateFields,omitempty"`
	}
)

func (e *E2open) ValidateAppointmentWithCyclops(
	ctx context.Context,
	poNumbers []string,
	_ models.Warehouse,
	opts *models.SchedulingOptions,
) ([]models.ValidatedPONumber, error) {

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	validatedPOs := make([]models.ValidatedPONumber, 0, len(poNumbers))

	// Format appointment date to YYYY/MM/DD if provided
	formattedApptDate := ""
	if opts.AppointmentDate != "" {
		if formatted, err := formatAppointmentDate(opts.AppointmentDate); err == nil {
			formattedApptDate = formatted
		}
	}

	// Handle special case: fetching companies, operations, or form fields (3rd party flow)
	// In this case, we make a single call and return the metadata
	if opts.FetchCompanies || opts.FetchOperations || opts.FetchFormFields {
		proID := ""
		if len(poNumbers) > 0 {
			proID = poNumbers[0]
		}

		cyclopsReq := CyclopsValidateAppointmentRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    E2openPlatform,
				Action:      models.ActionValidateAppointment,
				UserID:      e.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: e.creds.Username,
					Password: e.creds.Password,
				},
			},
			ProID:           proID,
			RequestType:     opts.AppointmentType,
			Operation:       opts.Operation,
			CompanyName:     opts.Company,
			AppointmentDate: formattedApptDate,
			FilterType:      "inbound",
			FetchCompanies:  opts.FetchCompanies,
			FetchOperations: opts.FetchOperations,
			FetchFormFields: opts.FetchFormFields,
			ProIDFieldName:  opts.ProIDFieldName,
			Warehouse: Warehouse{
				City:     opts.City,
				State:    opts.State,
				ZipCode:  opts.ZipCode,
				Country:  opts.Country,
				StopType: opts.StopType,
			},
		}

		reqBody, err := json.Marshal(cyclopsReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}

		httpReq, err := http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			cyclopsURL,
			bytes.NewBuffer(reqBody),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("Accept", "application/json")

		resp, err := otel.TracingHTTPClient().Do(httpReq)
		if err != nil {
			return nil, fmt.Errorf("failed to execute request: %w", err)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response: %w", err)
		}
		resp.Body.Close()

		var validationResp ValidateAppointmentResponse
		if err = json.Unmarshal(body, &validationResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		if !validationResp.Success {
			return nil, fmt.Errorf("failed to fetch data: %s", validationResp.Message)
		}

		// Return the response data encoded in a special ValidatedPONumber
		// The API layer will decode this and return it properly
		responseData := map[string]any{
			"companies":                  validationResp.Companies,
			"operations":                 validationResp.Operations,
			"requiresCompanySelection":   validationResp.RequiresCompanySelection,
			"requiresOperationSelection": validationResp.RequiresOperationSelection,
			"proIdFields":                validationResp.ProIDFields,
			"dateFields":                 validationResp.DateFields,
		}

		responseJSON, err := json.Marshal(responseData)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal response data: %w", err)
		}

		// Return a special marker ValidatedPONumber with the JSON data
		return []models.ValidatedPONumber{{
			PONumber: "__METADATA__",
			IsValid:  true,
			Error:    string(responseJSON),
		}}, nil
	}

	for _, poNumber := range poNumbers {
		cyclopsReq := CyclopsValidateAppointmentRequest{
			CyclopsBaseRequest: models.CyclopsBaseRequest{
				Integration: models.CyclopsSchedulingIntegration,
				Platform:    E2openPlatform,
				Action:      models.ActionValidateAppointment,
				UserID:      e.scheduler.Username,
				Credentials: models.CyclopsCredentials{
					Username: e.creds.Username,
					Password: e.creds.Password,
				},
			},
			ProID:       poNumber,
			RequestType: opts.AppointmentType,
			Warehouse: Warehouse{
				City:     opts.City,
				State:    opts.State,
				ZipCode:  opts.ZipCode,
				Country:  opts.Country,
				StopType: opts.StopType,
			},
			Operation:       opts.Operation,
			CompanyName:     opts.Company,
			AppointmentDate: formattedApptDate,
			FilterType:      "inbound",
			FetchCompanies:  opts.FetchCompanies,
			FetchOperations: opts.FetchOperations,
			FetchFormFields: opts.FetchFormFields,
			ProIDFieldName:  opts.ProIDFieldName,
		}

		reqBody, err := json.Marshal(cyclopsReq)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}

		httpReq, err := http.NewRequestWithContext(
			ctx,
			http.MethodPost,
			cyclopsURL,
			bytes.NewBuffer(reqBody),
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}

		httpReq.Header.Set("Content-Type", "application/json")
		httpReq.Header.Set("Accept", "application/json")

		resp, err := otel.TracingHTTPClient().Do(httpReq)
		if err != nil {
			return nil, fmt.Errorf("failed to execute request: %w", err)
		}

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read response: %w", err)
		}
		resp.Body.Close()

		var validationResp ValidateAppointmentResponse
		if err = json.Unmarshal(body, &validationResp); err != nil {
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		if !validationResp.Success {
			validatedPOs = append(validatedPOs, models.ValidatedPONumber{
				PONumber: poNumber,
				IsValid:  false,
				Error:    fmt.Sprintf("Validation failed: %s", validationResp.Message),
			})
			continue
		}

		validatedPO := models.ValidatedPONumber{
			PONumber: poNumber,
			IsValid:  true,
		}

		if len(validationResp.Appointments) == 0 {
			validatedPO.IsValid = false
			validatedPO.Error = "No appointments found for validation"
			validatedPOs = append(validatedPOs, validatedPO)
			continue
		}

		var relevantAppt *ValidatedAppointment
		switch opts.RequestType {
		case models.RequestTypePickup:
			for _, appt := range validationResp.Appointments {
				if appt.Warehouse.StopType == "pickup" {
					relevantAppt = &appt
					break
				}
			}
		case models.RequestTypeDropoff:
			for i := len(validationResp.Appointments) - 1; i >= 0; i-- {
				if validationResp.Appointments[i].Warehouse.StopType == "dropoff" {
					relevantAppt = &validationResp.Appointments[i]
					break
				}
			}
		}

		if relevantAppt == nil {
			validatedPO.IsValid = false
			validatedPO.Error = fmt.Sprintf("No %s appointment found", opts.RequestType)
			validatedPOs = append(validatedPOs, validatedPO)
			continue
		}

		if relevantAppt.Extended.IsFCFS {
			validatedPO.IsValid = false
			validatedPO.Error = fmt.Sprintf(
				"%s appointment requires FCFS scheduling",
				opts.RequestType,
			)
		}

		validatedPOs = append(validatedPOs, validatedPO)
	}

	return validatedPOs, nil
}
