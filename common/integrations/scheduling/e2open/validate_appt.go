package e2open

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func (e *E2open) ValidateAppointment(
	ctx context.Context,
	poNumbers []string,
	warehouse models.Warehouse,
	opts ...models.SchedulingOption,
) ([]models.ValidatedPONumber, error) {

	options := &models.SchedulingOptions{
		RequestType: models.RequestTypePickup,
	}

	options.Apply(opts...)

	return e.ValidateAppointmentWithCyclops(ctx, poNumbers, warehouse, options)
}
