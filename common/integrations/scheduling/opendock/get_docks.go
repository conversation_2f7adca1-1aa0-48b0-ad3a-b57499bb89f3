package opendock

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	DockResponse struct {
		Data      []models.Dock `json:"data"`
		Entity    string        `json:"entity"`
		Action    string        `json:"action"`
		Count     int           `json:"count"`
		Total     int           `json:"total"`
		Page      int           `json:"page"`
		PageCount int           `json:"pageCount"`
	}
)

// GetDocks supported by a given warehouse and load type.
//
// https://neutron.opendock.com/docs/#/Dock/getManyBaseDockControllerDock
func (o *Opendock) GetDocks(
	ctx context.Context,
	req models.GetDocksRequest,
) (docks []models.Dock, err error) {

	if req.Search == "" {
		return nil, fmt.Errorf("opendock docks query requires a search parameter: %w", err)
	}

	query := make(url.Values)

	// Opendock explicitly expects an unescaped string in JSON format
	unescapedJSONSearch, err := url.PathUnescape(req.Search)
	if err != nil {
		return nil, fmt.Errorf("error unescaping query params for opendock json query: %w", err)
	}

	query.Add("s", unescapedJSONSearch)

	var page int
	for {
		page++

		var result DockResponse
		if err = o.get(ctx, "dock", query, &result); err != nil {
			return docks, fmt.Errorf("error getting page %d of docks: %w", page, err)
		}

		docks = append(docks, result.Data...)

		if result.Page == result.PageCount {
			break
		}

		query.Set("page", strconv.Itoa(result.Page+1))
	}

	return docks, nil
}
