package opendock

import (
	"context"
	"net/url"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	// GetWarehouseResponse wraps the response for warehouse retrieval.
	GetWarehouseResponse struct {
		Data models.WarehouseDetails `json:"data"`
	}
)

// GetWarehouse is used for querying a specific warehouse on opendock.
func (o *Opendock) GetWarehouse(ctx context.Context, warehouseID string) (warehouse models.Warehouse, err error) {
	var result GetWarehouseResponse

	query := make(url.Values)
	query.Add(
		"fields",
		"id,timezone,name,street,city,state,zip,email,phone,timezone,settings,customApptFieldsTemplate",
	)

	path := "warehouse/" + warehouseID
	if err = o.get(ctx, path, query, &result); err != nil {
		return warehouse, err
	}

	whMainAddressLine := strings.Split(result.Data.Street, ",")[0]
	whSecondaryAddressLine := strings.Join(
		[]string{
			result.Data.City,
			", ",
			result.Data.State,
			" ",
			result.Data.Zip,
		},
		"",
	)

	warehouse = models.Warehouse{
		CustomApptFieldsTemplate: result.Data.CustomApptFieldsTemplate,
		DefaultSubscribedEmail:   result.Data.DefaultSubscribedEmail,
		Settings:                 result.Data.Settings,
		Source:                   models.OpendockSource,
		WarehouseAddressLine1:    whMainAddressLine,
		WarehouseAddressLine2:    whSecondaryAddressLine,
		WarehouseFullAddress:     strings.Join([]string{whMainAddressLine, whSecondaryAddressLine}, " "),
		WarehouseFullIdentifier: strings.Join(
			[]string{
				result.Data.Name,
				whMainAddressLine,
				whSecondaryAddressLine,
			},
			" ",
		),
		WarehouseID:       result.Data.ID,
		WarehouseName:     result.Data.Name,
		WarehouseTimezone: result.Data.Timezone,
	}

	return warehouse, nil
}
