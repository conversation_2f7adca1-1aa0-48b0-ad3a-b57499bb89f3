package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/integrations/scheduling/opendock"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	//nolint:gosec // location of the secret, not the secret itself
	secretArn = "arn:aws:secretsmanager:us-east-1:558920728231:secret:beacon-api-iW6tIA"

	warehouseID  = opendock.SevenElevenNeutronWarehouseID // Seven Eleven, Bradburn St, Stafford VA
	loadTypeName = "Cooler / Freezer (Palletized)"
	loadTypeID   = "8f031f85-3d25-4cef-9998-6f5f7fc2603b"
)

func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	ctx = log.With(ctx, zap.String("warehouseID", warehouseID), zap.String("loadTypeID", loadTypeID))

	client, err := buildClient(ctx)
	if err != nil {
		log.Fatal(ctx, "buildClient failed", zap.Error(err))
	}

	if err := getWarehouse(ctx, client); err != nil {
		log.Fatal(ctx, "getWarehouse", zap.Error(err))
	}

	if err := getLoadTypes(ctx, client); err != nil {
		log.Fatal(ctx, "getLoadTypes failed", zap.Error(err))
	}

	if err := getDocks(ctx, client); err != nil {
		log.Fatal(ctx, "getDocks failed", zap.Error(err))
	}

	slots, err := getOpenSlots(ctx, client)
	if err != nil {
		log.Fatal(ctx, "getOpenSlots failed", zap.Error(err))
	}

	if len(slots) == 0 {
		log.Fatal(ctx, "no available appointment slots")
	}

	if os.Getenv("MAKE_APPT") != "1" {
		log.Info(ctx, "Done (ReadOnly mode): to temporarily schedule a real appointment, run again with MAKE_APPT=1")
		return
	}

	appt, err := makeAppt(ctx, client, slots[0])
	if err != nil {
		log.Fatal(ctx, "makeAppt failed", zap.Error(err))
	}

	if err := getAppt(ctx, client, appt.ExternalID); err != nil {
		log.Fatal(ctx, "getAppt failed", zap.Error(err))
	}

	// Now immediately delete the appointment to free up the slot for real use
	if err := cancelAppt(ctx, client, appt.ExternalID); err != nil {
		log.Fatal(ctx, "cancelAppt failed", zap.Error(err))
	}

	log.Info(ctx, "All tests passed!")
}

func buildClient(ctx context.Context) (*opendock.Opendock, error) {
	if os.Getenv("AWS_ACCESS_KEY_ID") == "" {
		return nil, errors.New("no AWS env vars found")
	}

	// Load NFI username/password from secrets manager
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return nil, fmt.Errorf("failed to build AWS config: %w", err)
	}

	secret, err := secretsmanager.NewFromConfig(cfg).GetSecretValue(ctx, &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretArn),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to fetch secret %s: %w", secretArn, err)
	}

	type fields struct {
		Username string `json:"OPENDOCK_NFI_USERNAME"`
		Password string `json:"OPENDOCK_NFI_PASSWORD"`
	}

	var creds fields
	if err := json.Unmarshal([]byte(aws.ToString(secret.SecretString)), &creds); err != nil {
		return nil, fmt.Errorf("json.Unmarshal of secret value failed: %w", err)
	}

	opendockIntegration := models.Integration{
		Name:              models.Opendock,
		Type:              models.Scheduling,
		Username:          creds.Username,
		EncryptedPassword: []byte(creds.Password),
		ServiceID:         1,
	}

	return opendock.New(ctx, opendockIntegration)
}

func getWarehouse(ctx context.Context, client *opendock.Opendock) error {
	start := time.Now()

	_, err := client.GetWarehouse(ctx, opendock.SevenElevenNeutronWarehouseID)
	log.Info(ctx, "GetWarehouse finished", zap.Duration("duration", time.Since(start)))
	if err != nil {
		return err
	}

	return nil
}

func getLoadTypes(ctx context.Context, client *opendock.Opendock) error {
	start := time.Now()

	// Test paging logic
	result, err := client.GetLoadTypes(ctx, models.GetLoadTypesRequest{WarehouseID: warehouseID, Limit: 3})
	if err != nil {
		return err
	}

	log.Info(
		ctx,
		"GetLoadTypes finished",
		zap.Int("results", len(result)),
		zap.Duration("duration", time.Since(start)),
	)

	// Verify the Cooler / Freezer (Palletized) load type
	for _, detail := range result {
		if detail.Name != loadTypeName {
			continue
		}

		if loadTypeID != detail.ID {
			return fmt.Errorf("expected '%s' to be load type ID %s, found %s", loadTypeName, loadTypeID, detail.ID)
		}

		// load type name/ID match
		return nil
	}

	return errtypes.HTTPResponseError{
		IntegrationName: models.Opendock,
		IntegrationType: models.Scheduling,
		URL:             "LoadTypes",
	}
}

func getDocks(ctx context.Context, client *opendock.Opendock) error {
	start := time.Now()

	// Test paging logic
	result, err := client.GetDocks(
		ctx,
		models.GetDocksRequest{
			Search: `{"warehouseId": "9b1beeac-c174-4282-860e-36eac63779c1"}`,
			Limit:  3,
		},
	)
	if err != nil {
		return err
	}

	log.Info(
		ctx,
		"GetDocksRequest finished",
		zap.Int("results", len(result)),
		zap.Any("docks", result),
		zap.Duration("duration", time.Since(start)),
	)

	return nil
}

func getOpenSlots(ctx context.Context, client *opendock.Opendock) ([]models.Slot, error) {
	start := time.Now()

	slots, err := client.GetOpenSlots(ctx, loadTypeID, models.GetOpenSlotsRequest{
		// Pick a date 4 & 5 weeks from now
		// (far enough in the future to easily schedule + cancel without affecting real operations)
		Start:             time.Now().Add(24 * 28 * time.Hour),
		End:               time.Now().Add(24 * 35 * time.Hour),
		IncludeStartTimes: true,
		WarehouseID:       warehouseID,
	})

	log.Info(
		ctx,
		"GetOpenSlots finished",
		zap.Int("slots", len(slots)),
		zap.Duration("duration", time.Since(start)),
	)

	return slots, err
}

func makeAppt(ctx context.Context, client *opendock.Opendock, slot models.Slot) (models.Appointment, error) {
	// Submit the maximum number of PO numbers to test every field
	poNums := make([]string, 0, 49)
	for i := 1; i < 50; i++ {
		poNums = append(poNums, "TEST-PO-"+strconv.Itoa(i))
	}

	load := models.Load{
		FreightTrackingID: "testing12345",
		LoadCoreInfo: models.LoadCoreInfo{
			PONums: strings.Join(poNums, ","),
			Consignee: models.Consignee{
				RefNumber: "TEST REF",
				ApptNote:  "TEST APPOINTMENT. DO NOT SCHEDULE.",
			},
			Carrier: models.Carrier{
				ExternalTMSTrailerID: "TRAILER ABC",
			},
			Specifications: models.Specifications{TotalOutPalletCount: 22},
		},
	}

	if len(slot.StartTimes) == 0 {
		return models.Appointment{}, fmt.Errorf("no start times available for dock %s", slot.Dock.ID)
	}

	slotStart := slot.StartTimes[0]
	req := models.MakeAppointmentRequest{
		CustomFields: models.CustomApptFieldsTemplate{
			{
				Name:        "strValidation Status",
				Type:        "str",
				Label:       "Validation Status",
				Value:       nil,
				Description: "VldtStatus",
				//nolint:lll
				Placeholder: "This will be populated automatically when the appointment is validated by DHL. Do not edit this field.",
			},
			{
				Name:        "strValidation Message",
				Type:        "str",
				Label:       "Validation Message",
				Value:       nil,
				Description: "VldtReason",
				//nolint:lll
				Placeholder: "This will be populated automatically when the appointment is validated by DHL. Do not edit this field.",
			},
			{
				Name:             "strTrailer",
				Type:             "str",
				Label:            "Trailer #",
				Value:            "0353TRL",
				Description:      "Trailer",
				MaxLengthOrValue: 12,
			},
			{
				Name:        "dropdownVehicleType",
				Type:        "dropdown",
				Label:       "Trailer Type",
				Value:       "Straight Truck",
				Description: "VehicleType",
				DropDownValues: []string{
					"Cargo Van",
					"LTL/FTL Trailer",
					"Passenger Vehicle",
					"Straight Truck",
				},
			},
			{
				Name:             "strMisc2",
				Type:             "str",
				Label:            "Product Category",
				Value:            nil,
				Description:      "Misc2",
				MaxLengthOrValue: 20,
			},
			{
				Name:               "intMisc1",
				Type:               "int",
				Label:              "Pallet Count",
				Value:              15,
				Description:        "Misc1",
				MaxLengthOrValue:   100,
				RequiredForCarrier: true,
			},
			{
				Name:               "dropdownOrder Type",
				Type:               "dropdown",
				Label:              "PO Order Type",
				Value:              "PO",
				Description:        "TypeOrder",
				DropDownValues:     []string{"PO"},
				RequiredForCarrier: true,
			},
			{
				Name:               "strOrder 1",
				Type:               "str",
				Label:              "PO 1",
				Value:              "3100000",
				Description:        "Order1",
				MaxLengthOrValue:   20,
				RequiredForCarrier: true,
			},
			{
				Name:             "strOrder 2",
				Type:             "str",
				Label:            "PO 2",
				Value:            "3100001",
				Description:      "Order2",
				MaxLengthOrValue: 20,
			},
			{
				Name:             "strOrder3",
				Type:             "str",
				Label:            "PO 3",
				Value:            "3100002",
				Description:      "Order3",
				MaxLengthOrValue: 20,
			},
			{
				Name:             "strOrder4",
				Type:             "str",
				Label:            "PO 4",
				Value:            "3100003",
				Description:      "Order4",
				MaxLengthOrValue: 20,
			},
			{
				Name:             "strOrder5",
				Type:             "str",
				Label:            "PO 5",
				Value:            "3100004",
				Description:      "Order5",
				MaxLengthOrValue: 20,
			},
			{
				Name:             "strOrder6",
				Type:             "str",
				Label:            "PO 6",
				Value:            "3100005",
				Description:      "Order6",
				MaxLengthOrValue: 20,
			},
		},
		DockID:     slot.Dock.ID,
		LoadTypeID: loadTypeID,
		StartTime:  slotStart,
	}

	ctx = log.With(
		ctx,
		zap.String("dockName", slot.Dock.Name),
		zap.Time("startTime", slotStart),
		zap.Any("endTime", req.EndTime), // this needs to be null
	)

	log.Info(ctx, "submitting MakeAppointment request")

	start := time.Now()
	appt, err := client.MakeAppointmentWithLoad(ctx, req, load)
	log.Info(ctx, "MakeAppointment finished", zap.Duration("duration", time.Since(start)))

	return appt, err
}

func getAppt(ctx context.Context, client *opendock.Opendock, id string) error {
	start := time.Now()

	result, err := client.GetAppointment(ctx, id)
	if err != nil {
		return err
	}

	log.Info(
		ctx,
		"GetAppointment finished",
		zap.String("apptId", id),
		zap.Duration("duration", time.Since(start)),
	)

	if id != result.ExternalID {
		return fmt.Errorf("expected appointment ID %s in response, found %d", id, result.ID)
	}

	return nil
}

func cancelAppt(ctx context.Context, client *opendock.Opendock, id string) error {
	start := time.Now()
	appt, err := client.CancelAppointment(ctx, id, "Cancelling test appointment")
	if err != nil {
		return err
	}

	log.Info(
		ctx,
		"CancelAppointment succeeded",
		zap.String("apptId", id),
		zap.Duration("duration", time.Since(start)),
		zap.Any("status", appt.Status),
	)

	// Remove quotes if they exist
	status := strings.Trim(appt.Status, "\"")

	if status != "Cancelled" && status != "Requested" {
		return fmt.Errorf("expected 'Cancelled' or 'Requested' status, found '%s'", status)
	}

	return nil
}
