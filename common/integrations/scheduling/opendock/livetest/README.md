# Opendock SDK Live Test
This script is a helpful way to quickly test the Opendock SDK directly without having to go through beacon-api.

```shell
aws-vault exec {your-profile-name} -- go run .
```

By default, the test runs in read-only mode:

1. Fetch the NFI Opendock username/password from beacon-api secret
2. `SearchWarehouses` for "7 eleven"
3. `GetWarehouse` details
4. `GetLoadTypes` details
5. `GetOpenSlots` for the Cooler / Freezer (Palletized) docks starting 4 weeks from today

To schedule, get, and cancel a real appointment, run again with `MAKE_APPT=1` (use sparingly)
