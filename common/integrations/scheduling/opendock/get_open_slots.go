package opendock

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetSlotsResponse struct {
		Data   []Slot `json:"data"`
		Entity string `json:"entity"`
		Action string `json:"action"`
	}

	Slot struct {
		Dock struct {
			ID string `json:"id"`
		} `json:"dock"`
		Request struct {
			Start         string `json:"start"`
			End           string `json:"end"`
			ExcludeApptID string `json:"excludeApptId"`
		} `json:"request"`
		Availability []struct {
			Start string `json:"start"`
			End   string `json:"end"`
		} `json:"availability"`
		StartTimes []string `json:"startTimes"`
	}
)

// GetSlots returns appointment slots that are available for scheduling.
//
// If no slots are available, an empty array is returned.
// https://neutron.opendock.com/docs#/LoadType/LoadTypeController_availability
func (o *Opendock) GetOpenSlots(
	ctx context.Context,
	loadTypeID string,
	req models.GetOpenSlotsRequest,
) ([]models.Slot, error) {

	var resp GetSlotsResponse
	path := fmt.Sprintf("loadtype/%s/get-availability", url.PathEscape(loadTypeID))
	if err := o.post(ctx, path, nil, req, &resp); err != nil {
		return nil, err
	}

	slots := make([]models.Slot, len(resp.Data))
	for i, slot := range resp.Data {
		startTimes := make([]time.Time, len(slot.StartTimes))
		for j, st := range slot.StartTimes {
			t, err := time.Parse(time.RFC3339, st)
			if err != nil {
				return nil, fmt.Errorf("invalid time format: %w", err)
			}

			startTimes[j] = t
		}

		slots[i] = models.Slot{
			Dock: models.Dock{
				ID: slot.Dock.ID,
			},
			StartTimes: startTimes,
		}

	}

	return slots, nil
}
