package opendock

import (
	"context"
	"fmt"
	"net/url"

	"github.com/drumkitai/drumkit/common/models"
)

type CancelAppointmentRequest struct {
	Notes  string `json:"notes"`  // Existing notes + "Cancellation Reason: TEST APPOINTMENT"
	Status string `json:"status"` // "Cancelled"
}

// NOTE: Appointments can't be modified/cancelled within 48 hours of start time. Be careful when testing; select a date
// far out into the future.
//
// Furthermore, this cancels an appointment but doesn't hard-delete it. Creds don't have the permissions to delete.
func (o *Opendock) CancelAppointment(ctx context.Context, id, reason string) (models.Appointment, error) {
	appt, err := o.GetAppointment(ctx, id)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("error getting appt: %w", err)
	}

	req := CancelAppointmentRequest{
		Notes:  appt.Notes + "<br>Cancellation Reason: " + reason,
		Status: "Cancelled",
	}

	path := fmt.Sprint("appointment/", url.PathEscape(id))

	var res AppointmentResponse
	return appt, o.patch(ctx, path, req, &res)
}
