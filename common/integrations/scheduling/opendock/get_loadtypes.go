package opendock

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

type (
	LoadTypeResponse struct {
		Data      []models.LoadType `json:"data"`
		Entity    string            `json:"entity"`
		Action    string            `json:"action"`
		Count     int               `json:"count"`
		Total     int               `json:"total"`
		Page      int               `json:"page"`
		PageCount int               `json:"pageCount"`
	}
)

// GetLoadTypes offered by the given warehouse. You must select a service before attempting to schedule an appointment.
//
// https://neutron.opendock.com/docs#/LoadType
func (o *Opendock) GetLoadTypes(
	ctx context.Context,
	req models.GetLoadTypesRequest,
) (types []models.LoadType, err error) {

	query := make(url.Values)

	if req.Search != "" {
		query.Add("s", req.Search)
	}

	if req.Limit > 0 {
		query.Set("limit", strconv.Itoa(req.Limit))
	}

	if req.WarehouseID != "" {
		query.Set("warehouseId", req.WarehouseID)
	}

	// Mimicking Opendock's internal request — prevents returning empty slots for unassigned load types
	query.Add("showOnlyAssignedLoadTypes", "true")

	var page int
	for {
		page++

		var result LoadTypeResponse

		if err = o.get(ctx, "loadtype", query, &result); err != nil {
			return types, fmt.Errorf("error getting page %d of load types: %w", page, err)
		}

		types = append(types, result.Data...)

		if result.Page == result.PageCount {
			break
		}

		query.Set("page", strconv.Itoa(result.Page+1))
	}

	return types, nil
}
