package opendock

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	neutronHost                   = "neutron.opendock.com"
	SevenElevenNeutronWarehouseID = "9b1beeac-c174-4282-860e-36eac63779c1" // Warehouse ID under Neutron API

	// Generic error messages to filter out
	badRequestError        = "Bad Request"
	internalServerError    = "Internal Server Error"
	genericErrorMessage    = "An error occurred while processing your request"
	checkInputMessage      = "Please check your input and try again"
	validationIssuesPrefix = "Please fix the following issues:\n"
)

// General usage, copied from Opendock API documentation:
// Due to the high variability between warehouses, the flow for scheduling an appointment involves a few "discovery"
// steps before the final call to create the appointment:
// 1. Choose a warehouse ID by either supplying it directly or looking it up via "Warehouse Query".
// 2. Query that warehouse for its "Services" offered, and select one of those services (by ID).
// 3. Query that warehouse for its "Booking Rules", which will return a list of rules dictating how appointments have to
// be booked/scheduled.
// 4. Query for "Open Slots" with the selected service ID.
// 5. Combine information from the returned data above to make the final "Appointments" call to schedule the
// appointment.

type (
	authenticateResponse struct {
		AccessToken string `json:"access_token"`
	}

	AccessTokenIntegrationData struct {
		AccessToken               string
		AccessTokenExpirationDate models.NullTime
	}

	// ErrorResponse represents the error response format from Opendock API
	ErrorResponse struct {
		ErrorMessage string   `json:"errorMessage"`
		Message      []string `json:"message"` // Can be array of validation messages
		Error        string   `json:"error"`
		StatusCode   int      `json:"statusCode"`
	}
)

// parseOpendockError attempts to parse Opendock error response and extract user-friendly message
func (o *Opendock) parseOpendockError(respBody []byte) string {
	// Try to parse as generic JSON to handle different response formats
	var errorResponse map[string]any
	if err := json.Unmarshal(respBody, &errorResponse); err != nil {
		// If we can't parse as JSON, return the raw body as string
		bodyStr := strings.TrimSpace(string(respBody))
		if bodyStr != "" {
			return bodyStr
		}
		return genericErrorMessage
	}

	// Check for errorMessage field first (highest priority)
	if errorMessage, ok := errorResponse["errorMessage"].(string); ok && errorMessage != "" {
		return errorMessage
	}

	// Check for message field - can be string or array
	if message, exists := errorResponse["message"]; exists {
		switch msg := message.(type) {
		case string:
			// Single string message
			if msg != "" && msg != badRequestError {
				return msg
			}
		case []any:
			// Array of validation messages
			var messages []string
			for _, item := range msg {
				if str, ok := item.(string); ok && str != "" {
					messages = append(messages, "• "+str)
				}
			}
			if len(messages) > 0 {
				return validationIssuesPrefix + strings.Join(messages, "\n")
			}
		}
	}

	// Check for error field (but skip generic ones)
	if errorStr, ok := errorResponse["error"].(string); ok && errorStr != "" {
		if errorStr != badRequestError && errorStr != internalServerError {
			return errorStr
		}
	}

	// If we only have generic error messages, return a helpful fallback
	return checkInputMessage
}

//nolint:unparam
func (o *Opendock) post(ctx context.Context, path string, query url.Values, body, out any) error {
	return o.do(ctx, http.MethodPost, path, query, body, out)
}

func (o *Opendock) postMultipart(
	ctx context.Context, path string, body *bytes.Buffer, contentType string, out any,
) error {
	reqURL := (&url.URL{
		Scheme: "https",
		Host:   neutronHost,
		Path:   path,
	}).String()

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, body)
	if err != nil {
		return fmt.Errorf("failed to build %s %s request: %w", http.MethodPost, reqURL, err)
	}

	req.Header.Set("Content-Type", contentType)

	if path != "login" {
		req.Header.Add("Authorization", "Bearer "+o.neutronAPIAccessToken)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, o.Source, err)
		return fmt.Errorf("failed to send %s %s request: %w", http.MethodPost, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, o.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(o.Source, req, resp, respBody)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"json unmarshal failed for Opendock response body",
				zap.ByteString("body", respBody),
			)
			return fmt.Errorf("%s %s json unmarshal failed: %w", http.MethodPost, reqURL, err)
		}
	}

	return nil
}

func (o *Opendock) get(ctx context.Context, path string, query url.Values, out any) error {
	return o.do(ctx, http.MethodGet, path, query, nil, out)
}

func (o *Opendock) patch(ctx context.Context, path string, body any, out any) error {
	return o.do(ctx, http.MethodPatch, path, nil, body, out)
}

func (o *Opendock) do(ctx context.Context, method, path string, query url.Values, body any, out any) (err error) {
	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     neutronHost,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	var bodyBytes []byte
	if body != nil {
		if bodyBytes, err = json.Marshal(body); err != nil {
			return fmt.Errorf("error marshaling body: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, reqURL, bytes.NewReader(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Bearer token required for all endpoints except POST /login (which creates the neutron token)
	if path != "login" {
		req.Header.Add("Authorization", "Bearer "+o.neutronAPIAccessToken)
	}

	reqTimeout := 30 * time.Second // Default timeout

	// Allow request to hang for over 30 seconds when fetching all warehouses, since payload isn't paginated
	if path == "warehouse/" {
		reqTimeout = 60 * time.Second
	}

	resp, err := otel.TracingHTTPClient(reqTimeout).Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, o.Source, err)
		return fmt.Errorf("failed to send %s %s request: %w", method, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, o.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		// For client errors (4xx), these are likely validation errors that should be shown to the user
		if code >= 400 && code < 500 {
			userFriendlyMsg := o.parseOpendockError(respBody)
			httpErr := errtypes.NewHTTPResponseError(o.Source, req, resp, []byte(userFriendlyMsg))
			return errtypes.NewUserFacingError(httpErr)
		}

		// For server errors (5xx), return the original error without exposing internal details
		return errtypes.NewHTTPResponseError(o.Source, req, resp, respBody)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"json unmarshal failed for Opendock response body",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("%s %s json unmarshal failed: %w", method, reqURL, err)
		}
	}

	return nil
}
