package opendock

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/jwt"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	Opendock struct {
		neutronAPIAccessToken   string // Lasts 30 days
		neutronAPIWarehouseUUID uuid.UUID
		userID                  string
		Source                  models.Integration
	}

	AuthReqBody struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
)

func New(ctx context.Context, source models.Integration) (*Opendock, error) {
	client := &Opendock{Source: source}

	// If we already have a valid token, use it
	if source.AccessToken != "" && !source.NeedsRefresh() {
		client.neutronAPIAccessToken = source.AccessToken

		if strings.Contains(source.Username, "7-Eleven") {
			uuid, err := uuid.Parse(SevenElevenNeutronWarehouseID)
			if err != nil {
				return nil, fmt.Errorf("parsing 7-Eleven warehouse UUID: %w", err)
			}

			client.neutronAPIWarehouseUUID = uuid
		}

		return client, nil
	}

	// Otherwise, only use OnboardScheduler to authenticate if this is NOT an onboard.
	//
	// This happens because the caller during onboarding (onboard_scheduler.go:56) already calls
	// OnboardScheduler and we don't want to call it twice.
	if source.ID != 0 {
		if _, err := client.OnboardScheduler(ctx); err != nil {
			return nil, err
		}
	}

	return client, nil
}

func NewWithToken(ctx context.Context, source models.Integration) (*Opendock, error) {
	client := &Opendock{
		Source:                source,
		neutronAPIAccessToken: source.AccessToken,
	}

	if strings.Contains(source.Username, "7-Eleven") {
		uuid, err := uuid.Parse(SevenElevenNeutronWarehouseID)
		if err != nil {
			return nil, fmt.Errorf("parsing 7-Eleven warehouse UUID: %w", err)
		}

		client.neutronAPIWarehouseUUID = uuid
	}

	log.Info(
		ctx,
		"created Opendock client with existing token",
		zap.String("username", source.Username),
	)

	return client, nil
}

// TODO: Refactor this to split Onboarding and Auth into separate functions
func (o *Opendock) OnboardScheduler(ctx context.Context) (resp models.OnboardSchedulerResponse, err error) {
	username := o.Source.Username
	password, err := crypto.DecryptAESGCM(ctx, string(o.Source.EncryptedPassword), nil)
	if err != nil {
		return resp, fmt.Errorf("error decrypting password: %w", err)
	}

	if o.Source.Disabled {
		return resp, errtypes.DisabledIntegrationError(o.Source)
	}

	if username == "" || password == "" {
		return resp, errors.New("missing Opendock username or password")
	}

	o.Source.Name = models.Opendock
	o.Source.Type = models.Scheduling

	requestBody := AuthReqBody{
		Email:    username,
		Password: password,
	}

	var auth authenticateResponse
	if err := o.post(ctx, "auth/login", nil, requestBody, &auth); err != nil {
		log.Error(ctx, "opendock authentication failed", zap.String("email", username), zap.Error(err))

		// if authentication creds are wrong and integration exists, disable it
		if strings.Contains(err.Error(), "Email and Password combination is incorrect") && o.Source.ID != 0 {
			o.Source.Disabled = true

			log.Warn(ctx, "incorrect credentials for integration", zap.Any("integrationID", o.Source.ID))

			if err = integrationDB.Update(ctx, &o.Source); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update opendock info on integration table after failed creds",
					zap.Any("integrationID", o.Source.ID),
				)
			}
		}

		return resp, fmt.Errorf("opendock authentication failed: %w", err)
	}

	// access token is valid for 1 year
	o.neutronAPIAccessToken = auth.AccessToken

	opendockClaims := jwt.OpendockTokenClaims{}
	err = jwt.ParseOpendock(auth.AccessToken, &opendockClaims)
	if err != nil {
		return resp, fmt.Errorf("base64 decoding opendock auth token failed: %w", err)
	}

	// when creating a new client, store the latest access token on integrations DB
	o.Source.AccessToken = auth.AccessToken
	o.Source.AccessTokenExpirationDate = models.NullTime{
		Time:  opendockClaims.ExpiresAt.Time,
		Valid: true,
	}

	resp.AccessToken = auth.AccessToken
	resp.AccessTokenExpirationDate = opendockClaims.ExpiresAt.Time

	// When onboarding, handler will create the new record with the correct token info
	if o.Source.ID != 0 {
		if err = integrationDB.Update(ctx, &o.Source); err != nil {
			log.ErrorNoSentry(
				ctx,
				"failed to update opendock info on integration db",
				zap.Any("integration", o.Source),
			)
			return resp, fmt.Errorf("integration db update failed: %w", err)
		}
	}

	if strings.Contains(username, "7-Eleven") {
		SevenElevenNeutronWarehouseUUID, err := uuid.Parse(SevenElevenNeutronWarehouseID)
		if err != nil {
			return resp, err
		}

		o.neutronAPIWarehouseUUID = SevenElevenNeutronWarehouseUUID
	}

	user, err := o.GetUser(ctx)
	if err != nil {
		return resp, fmt.Errorf("failed to get user details after authentication: %w", err)
	}

	o.userID = user.ID

	log.Info(ctx, "successfully created Opendock client", zap.String("username", username))
	return resp, nil
}
