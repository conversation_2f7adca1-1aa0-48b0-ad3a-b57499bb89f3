package datadocks

type (
	AppointmentResp struct {
		ID                int           `json:"id"`
		AppointmentNumber int           `json:"appointment_number"`
		State             string        `json:"state"`
		Duration          int           `json:"duration"`
		CreatedBy         string        `json:"created_by"`
		PackingLists      []PackingList `json:"packing_lists"`
		Notes             []Note        `json:"notes"`
		ShippingInfo
		Status
		LocationInfo
		TimeInfo
		CustomValues
	}

	ShippingInfo struct {
		ShippingNumber string `json:"shipping_number"`
		TrailerNumber  string `json:"trailer_number"`
		BolNumber      string `json:"bol_number"`
		CarrierName    string `json:"carrier_name"`
		DriverName     string `json:"driver_name"`
	}

	Status struct {
		Outbound    bool `json:"outbound"`
		DropTrailer bool `json:"drop_trailer"`
		Queued      bool `json:"queued"`
	}

	LocationInfo struct {
		DockName *string `json:"dock_name"`
		YardName *string `json:"yard_name"`
	}

	TimeInfo struct {
		ScheduledAt string  `json:"scheduled_at"`
		ApprovedAt  string  `json:"approved_at"`
		ArrivedAt   *string `json:"arrived_at"`
		StartedAt   string  `json:"started_at"`
		CompletedAt string  `json:"completed_at"`
		LeftAt      string  `json:"left_at"`
		CancelledAt *string `json:"cancelled_at"`
	}

	CustomValues struct {
		ExpectedAt       string `json:"expected_at"`
		TravelType       string `json:"travel_type"`
		ForkliftOperator string `json:"forklift_operator"`
		InspectionPassed string `json:"inspection_passed,omitempty"`
	}

	PackingList struct {
		ID           int    `json:"id"`
		PoNumber     string `json:"po_number"`
		CustomerName string `json:"customer_name"`
		ProductName  string `json:"product_name"`
		UnitName     string `json:"unit_name"`
		QuantityInfo
		PackingListCustom
	}

	QuantityInfo struct {
		BookedQuantity int `json:"booked_quantity"`
		BookedWeight   int `json:"booked_weight"`
		ActualQuantity int `json:"actual_quantity"`
		ActualWeight   int `json:"actual_weight"`
	}

	PackingListCustom struct {
		Barcode    string `json:"barcode"`
		Dimensions string `json:"dimensions"`
	}

	Note struct {
		ID   int    `json:"id"`
		Body string `json:"body"`
	}
)
