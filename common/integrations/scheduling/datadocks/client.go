package datadocks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	baseHost = "toronto-acme.datadocks.com"
	baseURL  = "https://" + baseHost
)

type (
	DataDocks struct {
		Source models.Integration
	}
)

func (d *DataDocks) convertToAppointment(
	ctx context.Context,
	resp AppointmentResp,
	appointmentID string,
) (models.Appointment, error) {

	startTime, err := parseAppointmentTime(resp.StartedAt)
	if err != nil {
		log.Warn(
			ctx,
			"failed to parse appointment start time",
			zap.String("appointmentID", appointmentID),
			zap.Error(err),
		)
	}

	return models.Appointment{
		ExternalID:     appointmentID,
		ConfirmationNo: strconv.Itoa(resp.AppointmentNumber),
		Date:           startTime.Format(time.DateOnly),
		StartTime:      startTime,
		Notes:          combineNotes(resp.Notes),
		Status:         parseStatus(resp.State),
	}, nil
}

func parseAppointmentTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, errors.New("empty start time")
	}

	parsedTime, err := helpers.ParseDatetime(timeStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid time format: %w", err)
	}

	return parsedTime, nil
}

func combineNotes(notes []Note) string {
	if len(notes) == 0 {
		return ""
	}

	noteBodies := make([]string, len(notes))
	for i, note := range notes {
		noteBodies[i] = note.Body
	}

	return strings.Join(noteBodies, noteSeparator)
}

func parseStatus(state string) string {
	if state == "" {
		return ""
	}

	statusBytes, err := json.Marshal(state)
	if err != nil {
		return state
	}

	status := string(statusBytes)
	status = strings.Trim(status, `"`)

	return status
}
