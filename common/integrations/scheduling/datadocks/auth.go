package datadocks

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
)

func New(_ context.Context, source models.Integration) (*DataDocks, error) {
	return &DataDocks{Source: source}, nil
}

func (d *DataDocks) OnboardScheduler(_ context.Context) (models.OnboardSchedulerResponse, error) {
	var result models.OnboardSchedulerResponse
	result.APIKey = d.Source.APIKey
	result.Username = d.Source.Username

	return result, nil
}
