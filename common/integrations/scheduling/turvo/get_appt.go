package turvo

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	getApptPathFormat = "/appointments/%s"
)

// TODO: pass in confirmation number instead of appointment id
func (t *Turvo) GetAppointment(ctx context.Context, id string) (models.Appointment, error) {
	if id == "" {
		return models.Appointment{}, errors.New("appointment ID is required")
	}

	path := fmt.Sprintf(getApptPathFormat, id)

	var result GetAppointmentResp
	if err := t.get(ctx, path, nil, &result); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to fetch appointment: %w", err)
	}

	if err := validateAppointmentResponse(result); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid appointment response: %w", err)
	}

	startTime, err := parseAppointmentTime(ctx, result.Details.StartDate.Date)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to parse appointment time: %w", err)
	}

	appt := models.Appointment{
		ConfirmationNo: result.Details.ConfirmationNumber,
		Date:           startTime.Format(time.DateOnly),
		StartTime:      startTime,
		Notes:          result.Details.Attributes.Notes,
	}

	return appt, nil
}

func validateAppointmentResponse(resp GetAppointmentResp) error {
	if resp.Details.ConfirmationNumber == "" {
		return errors.New("missing confirmation number")
	}

	if resp.Details.StartDate.Date == "" {
		return errors.New("missing start date")
	}

	return nil
}

func parseAppointmentTime(ctx context.Context, timeStr string) (time.Time, error) {
	parsedTime, err := helpers.ParseDatetime(timeStr)
	if err != nil {
		log.Error(
			ctx,
			"failed to parse appointment time",
			zap.String("timeString", timeStr),
			zap.Error(err),
		)

		return time.Time{}, fmt.Errorf("invalid time format: %w", err)
	}

	return parsedTime, nil
}
