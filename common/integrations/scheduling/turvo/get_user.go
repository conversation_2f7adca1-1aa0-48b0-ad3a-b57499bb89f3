package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	usersEndpoint = "/v1/users/list"
)

func (t *Turvo) GetUser(ctx context.Context) (models.SchedulingUser, error) {
	if t.Source.Username == "" {
		return models.SchedulingUser{}, errors.New("username is required")
	}

	var (
		offset    = 0
		nextPage  = true
		userFound models.SchedulingUser
	)

	for nextPage {
		result, err := t.fetchUsersPage(ctx, offset)
		if err != nil {
			return models.SchedulingUser{}, fmt.<PERSON>rrorf("failed to fetch users: %w", err)
		}

		user, found, err := t.findUserInResponse(result)
		if err != nil {
			return models.SchedulingUser{}, fmt.<PERSON>rrorf("failed to process user data: %w", err)
		}

		if found {
			userFound = user
			break
		}

		nextPage = result.Details.Pagination.MoreAvailable
		offset += limit
	}

	if userFound.ID == "" {
		return models.SchedulingUser{}, fmt.<PERSON><PERSON><PERSON>("user not found: %s", t.Source.Username)
	}

	return userFound, nil
}

func (t *Turvo) fetchUsersPage(ctx context.Context, offset int) (GetUsersResp, error) {
	queryParams := url.Values{
		"start":    {strconv.Itoa(offset)},
		"pageSize": {strconv.Itoa(limit)},
	}

	var result GetUsersResp
	if err := t.get(ctx, usersEndpoint, queryParams, &result); err != nil {
		return GetUsersResp{}, fmt.Errorf("failed to fetch users: %w", err)
	}

	return result, nil
}

func (t *Turvo) findUserInResponse(result GetUsersResp) (models.SchedulingUser, bool, error) {
	for _, data := range result.Details.Users {
		if t.Source.Username == data.Name {
			user, err := t.createUserModel(data)
			if err != nil {
				return models.SchedulingUser{}, false, err
			}

			return user, true, nil
		}
	}

	return models.SchedulingUser{}, false, nil
}

func (t *Turvo) createUserModel(data User) (models.SchedulingUser, error) {
	createdTime, err := helpers.ParseDatetime(data.Created)
	if err != nil {
		return models.SchedulingUser{}, fmt.Errorf("failed to parse creation time: %w", err)
	}

	email := ""
	if len(data.Email) > 0 {
		email = data.Email[0].Email
	}

	firstName, lastName := parseUserName(data.Name)

	return models.SchedulingUser{
		ID:             strconv.Itoa(data.ID),
		CreateDateTime: createdTime,
		IsActive:       true,
		Email:          email,
		FirstName:      firstName,
		LastName:       lastName,
	}, nil
}

func parseUserName(fullName string) (firstName, lastName string) {
	if fullName == "" {
		return "", ""
	}

	parts := strings.Fields(strings.TrimSpace(fullName))

	switch len(parts) {
	case 0:
		return "", ""
	case 1:
		return parts[0], ""
	case 2:
		return parts[0], parts[1]
	default:
		return parts[0], strings.Join(parts[1:], " ")
	}
}
