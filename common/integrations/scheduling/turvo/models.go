package turvo

import (
	"encoding/json"
	"time"
)

type (
	KeyValue struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}

	TimeSlot struct {
		Date     string `json:"date"`
		Timezone string `json:"timezone"`
	}

	TimeSlotWithTime struct {
		Date     time.Time `json:"date"`
		Timezone string    `json:"timezone"`
	}

	Order struct {
		ID string `json:"id"`
	}

	Carrier struct {
		Scac      string      `json:"scac"`
		McNumber  string      `json:"mcNumber"`
		DotNumber json.Number `json:"dotNumber"`
		VatNumber string      `json:"vatNumber"`
	}

	Person struct {
		ID                string   `json:"id,omitempty"`
		Email             string   `json:"email"`
		ReceiveTurvoLinks bool     `json:"receiveTurvoLinks,omitempty"`
		IsPrimary         bool     `json:"isPrimary,omitempty"`
		Type              KeyValue `json:"type,omitempty"`
		Deleted           bool     `json:"deleted,omitempty"`
	}

	Address struct {
		ID        string   `json:"id,omitempty"`
		Line1     string   `json:"line1"`
		Line2     string   `json:"line2"`
		City      string   `json:"city"`
		State     string   `json:"state"`
		Zip       string   `json:"zip"`
		Type      KeyValue `json:"type,omitempty"`
		IsPrimary bool     `json:"isPrimary,omitempty"`
		Country   string   `json:"country,omitempty"`
		Lon       float64  `json:"lon,omitempty"`
		Lat       float64  `json:"lat,omitempty"`
		Deleted   bool     `json:"deleted,omitempty"`
	}

	Pagination struct {
		Start              int  `json:"start"`
		PageSize           int  `json:"pageSize"`
		TotalRecordsInPage int  `json:"totalRecordsInPage"`
		MoreAvailable      bool `json:"moreAvailable"`
	}

	Notification struct {
		Email bool `json:"email"`
		Sms   bool `json:"sms"`
	}

	AppointmentAttributes struct {
		Orders       []Order       `json:"orders"`
		OrderIDType  string        `json:"orderIdType,omitempty"`
		Carrier      Carrier       `json:"carrier"`
		EquipmentID  string        `json:"equipmentId,omitempty"`
		Direction    KeyValue      `json:"direction"`
		Type         KeyValue      `json:"type"`
		ServiceType  KeyValue      `json:"serviceType"`
		TrailerType  KeyValue      `json:"trailerType"`
		Notes        string        `json:"notes"`
		Services     []KeyValue    `json:"services,omitempty"`
		People       []Person      `json:"people,omitempty"`
		Notification *Notification `json:"notification,omitempty"`
	}

	TokenResp struct {
		AccessToken string `json:"access_token"`
		Country     string `json:"country"`
		ExpiresIn   int    `json:"expires_in"`
		Scope       string `json:"scope"`
		TokenType   string `json:"token_type"`
	}

	GetAppointmentResp struct {
		Details struct {
			StartDate          TimeSlot              `json:"startDate"`
			EndDate            TimeSlot              `json:"endDate"`
			ConfirmationNumber string                `json:"confirmationNumber"`
			Attributes         AppointmentAttributes `json:"attributes"`
		} `json:"details"`
	}

	Resource struct {
		IDType     string `json:"idType"`
		ResourceID string `json:"resourceId"`
		Type       string `json:"type"`
	}

	MakeAppointmentReq struct {
		Resource           Resource              `json:"resource"`
		StartDate          TimeSlot              `json:"startDate"`
		EndDate            TimeSlot              `json:"endDate"`
		Attributes         AppointmentAttributes `json:"attributes"`
		ConfirmationNumber string                `json:"confirmationNumber"`
	}

	MakeAppointmentResp struct {
		RespMsg  string `json:"respMsg"`
		RespCode int    `json:"respCode"`
		Details  struct {
			StartDate          TimeSlot              `json:"startDate"`
			EndDate            TimeSlot              `json:"endDate"`
			ConfirmationNumber string                `json:"confirmationNumber"`
			Attributes         AppointmentAttributes `json:"attributes"`
		} `json:"details"`
	}

	GetOpenSlotsReq struct {
		Resource  Resource `json:"resource"`
		StartDate TimeSlot `json:"startDate"`
		EndDate   TimeSlot `json:"endDate"`
	}

	Customer struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	Reservation struct {
		SlotAccountType KeyValue `json:"slotAccountType"`
		Customer        Customer `json:"customer,omitempty"`
		Direction       KeyValue `json:"direction"`
		ServiceType     KeyValue `json:"serviceType"`
		TrailerType     KeyValue `json:"trailerType"`
		ReserveUploaded bool     `json:"reserveUploaded"`
		Used            int      `json:"used"`
	}

	SlotDefinition struct {
		StartDate    string        `json:"startDate"`
		EndDate      string        `json:"endDate"`
		OpenSlots    int           `json:"openSlots"`
		Reservations []Reservation `json:"reservations,omitempty"`
	}

	GetOpenSlotsResp struct {
		SlotsDefinition []SlotDefinition `json:"slotsDefinition"`
	}

	Phone struct {
		CountryCode string `json:"countryCode"`
		Number      string `json:"number"`
		Extension   string `json:"extension"`
	}

	WarehouseLocation struct {
		ID        int       `json:"id"`
		Name      string    `json:"name"`
		Created   string    `json:"created"`
		Updated   string    `json:"updated"`
		Addresses []Address `json:"addresses"`
		Phones    []Phone   `json:"phones"`
	}

	GetAllWarehousesResp struct {
		Status  string `json:"Status"`
		Details struct {
			Pagination Pagination          `json:"pagination"`
			Locations  []WarehouseLocation `json:"locations"`
		} `json:"details"`
	}

	OperationHours struct {
		Start string   `json:"start"`
		End   string   `json:"end"`
		Days  []string `json:"days"`
	}

	Account struct {
		ID int `json:"id"`
	}

	RadiusInfo struct {
		Units KeyValue `json:"units"`
	}

	Geofence struct {
		Type    string     `json:"type"`
		Polygon []string   `json:"polygon"`
		Radius  RadiusInfo `json:"radius"`
	}

	DeliveryStatus struct {
		Enabled bool     `json:"enabled"`
		Code    KeyValue `json:"code"`
	}

	LateTypeInfo struct {
		Enabled bool     `json:"enabled"`
		Type    KeyValue `json:"type"`
	}

	DurationConfig struct {
		Value int      `json:"value"`
		Units KeyValue `json:"units"`
	}

	TimeInfo struct {
		Enabled  bool           `json:"enabled"`
		Duration DurationConfig `json:"duration"`
	}

	DeliveryLink struct {
		Status   DeliveryStatus `json:"status"`
		LateType LateTypeInfo   `json:"lateType"`
		Time     TimeInfo       `json:"time"`
	}

	ExternalID struct {
		Deleted bool     `json:"deleted"`
		Type    KeyValue `json:"type"`
		ID      int      `json:"id"`
		Value   string   `json:"value"`
		Account Account  `json:"account"`
	}

	DwellTime struct {
		Value int      `json:"value"`
		Units KeyValue `json:"units"`
	}

	AppointmentInfoCutoffConfig struct {
		Field1 bool `json:"40200"`
		Field2 bool `json:"40201"`
		Field3 bool `json:"40202"`
		Field4 bool `json:"40203"`
		Field5 bool `json:"40204"`
	}

	CutoffTimeDetails struct {
		ScheduleDaysCount int    `json:"scheduleDaysCount"`
		InboundTime       string `json:"inboundTime"`
		OutboundTime      string `json:"outboundTime"`
		IsCutoffTimeOn    bool   `json:"isCutoffTimeOn"`
		SkipDayCount      int    `json:"skipDayCount"`
	}

	AppointmentDateConfig struct {
		CutoffTime CutoffTimeDetails `json:"cutoffTime"`
		Range      struct {
			Value int `json:"value"`
		} `json:"range"`
	}

	CutoffTimeConfig struct {
		IsCutoffTimeForDeleteOn               bool                        `json:"isCutoffTimeForDeleteOn"`
		UpdateAppointmentInfoCutoffTimeConfig AppointmentInfoCutoffConfig `json:"updateAppointmentInfoCutoffTimeConfig"`
	}

	SchedulingInfo struct {
		AppointmentCutoffTimeConfig CutoffTimeConfig      `json:"appointmentCutoffTimeConfig"`
		AppointmentDateConfig       AppointmentDateConfig `json:"appointmentDateConfig"`
		Enabled                     bool                  `json:"enabled"`
		Warehouse                   bool                  `json:"warehouse"`
	}

	EmailContact struct {
		ID                string   `json:"id"`
		Email             string   `json:"email"`
		ReceiveTurvoLinks bool     `json:"receiveTurvoLinks"`
		IsPrimary         bool     `json:"isPrimary"`
		Type              KeyValue `json:"type"`
		Deleted           bool     `json:"deleted"`
	}

	PhoneContact struct {
		ID                string   `json:"id"`
		Number            int      `json:"number"`
		Extension         int      `json:"extension"`
		IsPrimary         bool     `json:"isPrimary"`
		ReceiveTurvoLinks bool     `json:"receiveTurvoLinks"`
		Type              KeyValue `json:"type"`
		Country           KeyValue `json:"country"`
		Deleted           bool     `json:"deleted"`
	}

	ServiceInfo struct {
		ID    int    `json:"id"`
		Key   int    `json:"key"`
		Value string `json:"value"`
	}

	Contact struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}

	GetWarehouseResp struct {
		Status  string `json:"Status"`
		Details struct {
			ID                  int            `json:"id"`
			Name                string         `json:"name"`
			Timezone            string         `json:"timezone"`
			Account             Account        `json:"account"`
			Geofence            Geofence       `json:"geofence"`
			DeliveryLink        DeliveryLink   `json:"deliveryLink"`
			ExternalIDs         []ExternalID   `json:"externalIds"`
			HoursOfOperation    OperationHours `json:"hoursOfOperation"`
			ShippingHours       OperationHours `json:"shippingHours"`
			ReceivingHours      OperationHours `json:"receivingHours"`
			CrossDock           bool           `json:"crossDock"`
			ColdStorage         bool           `json:"coldStorage"`
			Warehouse           bool           `json:"warehouse"`
			UseApptAsDwellTime  bool           `json:"useApptAsDwellTime"`
			Locode              string         `json:"locode"`
			DwellTime           DwellTime      `json:"dwellTime"`
			SpecialInstructions string         `json:"specialInstructions"`
			Directions          string         `json:"directions"`
			SchedulingType      KeyValue       `json:"schedulingType"`
			SchedulingInfo      SchedulingInfo `json:"schedulingInfo"`
			Address             []Address      `json:"address"`
			Email               []EmailContact `json:"email"`
			Phone               []PhoneContact `json:"phone"`
			LtlShipmentService  []ServiceInfo  `json:"ltlShipmentService"`
			Contact             Contact        `json:"contact"`
		} `json:"details"`
	}

	User struct {
		ID          int            `json:"id"`
		Name        string         `json:"name"`
		Created     string         `json:"created"`
		Updated     string         `json:"updated"`
		CreatedDate string         `json:"createdDate"`
		UpdatedDate string         `json:"updatedDate"`
		Email       []EmailContact `json:"email"`
	}

	GetUsersResp struct {
		Status  string `json:"Status"`
		Details struct {
			Pagination Pagination `json:"pagination"`
			Users      []User     `json:"users"`
		} `json:"details"`
	}

	CancelApptResp struct {
		RespMsg  string `json:"respMsg"`
		RespCode int    `json:"respCode"`
		Details  struct {
			StartDate          TimeSlotWithTime      `json:"startDate"`
			EndDate            TimeSlotWithTime      `json:"endDate"`
			ConfirmationNumber string                `json:"confirmationNumber"`
			Attributes         AppointmentAttributes `json:"attributes"`
		} `json:"details"`
	}
)
