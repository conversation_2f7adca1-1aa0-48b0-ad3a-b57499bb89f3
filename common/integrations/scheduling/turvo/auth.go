package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func New(ctx context.Context, source models.Integration) (*Turvo, error) {
	client := &Turvo{Source: source}

	if source.AccessToken == "" || source.NeedsRefresh() {
		if _, err := client.OnboardScheduler(ctx); err != nil {
			return nil, fmt.Errorf("failed to authenticate: %w", err)
		}
	}

	return client, nil
}

func (t *Turvo) OnboardScheduler(ctx context.Context) (models.OnboardSchedulerResponse, error) {
	var result models.OnboardSchedulerResponse

	tokenResp, err := t.getToken(ctx)
	if err != nil {
		return result, err
	}

	expirationTime := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	t.Source.AccessToken = tokenResp.AccessToken
	t.Source.AccessTokenExpirationDate = models.NullTime{
		Time:  expirationTime,
		Valid: true,
	}

	if t.Source.ID != 0 {
		if err = integrationDB.Update(ctx, &t.Source); err != nil {
			log.ErrorNoSentry(
				ctx,
				"failed to update Turvo info in integration db",
				zap.Any("integration", t.Source),
			)

			return result, fmt.Errorf("integration db update failed: %w", err)
		}
	}

	result.AccessToken = tokenResp.AccessToken
	result.AccessTokenExpirationDate = expirationTime
	result.APIKey = t.Source.APIKey
	result.Username = t.Source.Username

	return result, nil
}

const (
	authEndpoint = "/v1/oauth/token"
	grantType    = "password"
	scopeValue   = "read+trust+write"
	userType     = "business"
)

func (t *Turvo) getToken(ctx context.Context) (TokenResp, error) {
	// TODO: need to confirm entered values
	if t.Source.Service.TurvoSchedulingClientID == "" ||
		t.Source.Service.TurvoSchedulingClientSecret == "" ||
		t.Source.Username == "" ||
		len(t.Source.EncryptedPassword) == 0 {

		return TokenResp{}, errors.New("missing required token configuration")
	}

	password, err := crypto.DecryptAESGCM(ctx, string(t.Source.EncryptedPassword), nil)
	if err != nil {
		return TokenResp{}, fmt.Errorf("failed to decrypt password: %w", err)
	}

	queryParams := url.Values{
		"client_id":     {t.Source.Service.TurvoSchedulingClientID},
		"client_secret": {t.Source.Service.TurvoSchedulingClientSecret},
	}

	postBody := url.Values{
		"grant_type": {grantType},
		"username":   {t.Source.Username},
		"password":   {password},
		"scope":      {scopeValue},
		"type":       {userType},
	}

	var tokenResp TokenResp
	if err := t.post(ctx, authEndpoint, queryParams, postBody, &tokenResp); err != nil {
		return TokenResp{}, fmt.Errorf("token request failed: %w", err)
	}

	if tokenResp.AccessToken == "" {
		return TokenResp{}, errors.New("received empty access token")
	}

	return tokenResp, nil
}
