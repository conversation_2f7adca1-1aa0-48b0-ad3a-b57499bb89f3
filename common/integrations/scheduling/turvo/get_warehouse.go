package turvo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/drumkitai/drumkit/common/models"
)

func (t *Turvo) GetWarehouse(ctx context.Context, id string) (models.Warehouse, error) {
	var result GetWarehouseResp
	query := make(url.Values)

	path := fmt.Sprintf("/v1/locations/%s", id)
	if err := t.get(ctx, path, query, &result); err != nil {
		return models.Warehouse{}, err
	}

	if len(result.Details.Address) == 0 {
		return models.Warehouse{}, fmt.Errorf("no address details found for warehouse ID: %s", id)
	}

	address := result.Details.Address[0]

	warehouse := models.Warehouse{
		Source:                models.TurvoSource,
		WarehouseID:           strconv.Itoa(result.Details.ID),
		WarehouseName:         result.Details.Name,
		WarehouseAddressLine1: address.Line1,
		WarehouseAddressLine2: address.Line2,
		WarehouseFullAddress: formatFullAddress(
			Address{
				Line1: address.Line1,
				Line2: address.Line2,
				City:  address.City,
				State: address.State,
				Zip:   address.Zip,
			},
		),
		WarehouseTimezone: result.Details.Timezone,
	}

	return warehouse, nil
}
