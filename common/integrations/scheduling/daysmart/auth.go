package daysmart

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/models"
)

func New(_ context.Context, source models.Integration) (*DaySmart, error) {
	return &DaySmart{Source: source}, nil
}

func (d *DaySmart) OnboardScheduler(ctx context.Context) (models.OnboardSchedulerResponse, error) {
	var result models.OnboardSchedulerResponse

	accessToken, err := d.getToken(ctx)
	if err != nil {
		return result, err
	}

	result.AccessToken = accessToken
	result.Username = d.Source.Username

	return result, nil
}

func (d *DaySmart) getToken(ctx context.Context) (string, error) {
	username := d.Source.Username
	password, err := crypto.DecryptAESGCM(ctx, string(d.Source.EncryptedPassword), nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt password: %w", err)
	}

	token := base64.StdEncoding.EncodeToString([]byte(username + ":" + password))
	return token, nil
}
