package daysmart

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

type requestOption func(*http.Request)

func withJSON(body any) requestOption {
	return func(req *http.Request) {
		if body == nil {
			return
		}

		//nolint:errcheck
		jsonBytes, _ := json.Marshal(body)
		req.Body = io.NopCloser(bytes.NewReader(jsonBytes))
		req.Header.Set("Content-Type", "application/json")
	}
}

//nolint:unparam
func (d *DaySmart) post(ctx context.Context, path string, query url.Values, body, out any) error {
	return d.do(ctx, http.MethodPost, path, query, out, withJSO<PERSON>(body))
}

func (d *DaySmart) do(
	ctx context.Context,
	method,
	path string,
	query url.Values,
	out any,
	opts ...requestOption,
) error {

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     baseHost,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, method, reqURL, nil)
	if err != nil {
		return fmt.Errorf("building request %s %s: %w", method, reqURL, err)
	}

	if path != "login" {
		req.Header.Set("Authorization", "Basic "+d.Source.AccessToken)
	}

	for _, opt := range opts {
		opt(req)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, d.Source, err)
		return fmt.Errorf("sending request %s %s: %w", method, reqURL, err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, d.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("reading response %s: %w", reqURL, err)
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(d.Source, req, resp, respBody)
	}

	if !strings.Contains(path, "login") {
		log.Debug(
			ctx,
			"daysmart response",
			zap.String("method", method),
			zap.String("url", reqURL),
			zap.ByteString("body", respBody),
		)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"unmarshal failed",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("unmarshal %s %s: %w", method, reqURL, err)
		}
	}

	return nil
}
