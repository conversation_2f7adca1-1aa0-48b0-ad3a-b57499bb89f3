package daysmart

import (
	"context"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

func (d *DaySmart) ValidateAppointment(
	_ context.Context,
	_ []string,
	_ models.Warehouse,
	_ ...models.SchedulingOption,
) ([]models.ValidatedPONumber, error) {

	return []models.ValidatedPONumber{}, errtypes.NotImplemented(models.DaySmart, "ValidateAppointment")
}
