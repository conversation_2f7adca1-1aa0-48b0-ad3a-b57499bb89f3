package daysmart

type (
	CommonResponse struct {
		Resource string   `json:"resource"`
		Action   string   `json:"action"`
		Request  string   `json:"request"`
		Result   string   `json:"result"`
		Count    int      `json:"count"`
		Errors   []string `json:"errors"`
	}

	AppointmentResp struct {
		CommonResponse
		Data AppointmentData `json:"data"`
	}

	AppointmentData struct {
		Account       string   `json:"account"`
		ApptID        string   `json:"appt_id"`
		CustomFields  []string `json:"custom_fields"`
		CustomerID    string   `json:"customer_id"`
		EmployeeID    string   `json:"employee_id"`
		LocationIDCID string   `json:"location_id (c_id)"`
		Reason        string   `json:"reason"`
		RoomID        string   `json:"room_id"`
		ServiceID     string   `json:"service_id"`
		Spots         string   `json:"spots"`
		Notes
		Payment
		Status
		TimeSlot
		Vehicle
	}

	TimeSlot struct {
		Date      string `json:"date"`
		StartTime int    `json:"start_time"`
		EndTime   int    `json:"end_time"`
	}

	Notes struct {
		CustomerNotes string `json:"customer_notes"`
		EmployeeNotes string `json:"employee_notes"`
	}

	Status struct {
		StatusID      string `json:"status_id"`
		CreationEmpID string `json:"creation_emp_id"`
		LastEmpID     int    `json:"last_emp_id"`
		RepID         string `json:"rep_id"`
	}

	Payment struct {
		Cost          string `json:"cost"`
		Tip           string `json:"tip"`
		PaymentTypeID string `json:"payment_type_id"`
		CouponCode    string `json:"coupon_code"`
	}

	Vehicle struct {
		CertNumber   string `json:"cert_number"`
		MakeID       string `json:"make_id"`
		ModelID      string `json:"model_id"`
		ModelYear    string `json:"model_year"`
		Odometer     string `json:"odometer"`
		OtherVehicle string `json:"other_vehicle"`
		Vin          string `json:"vin"`
		Aces
		Dealer
	}

	Dealer struct {
		DealerAddress string `json:"dealer_address"`
		DealerName    string `json:"dealer_name"`
		SalvageReason string `json:"salvage_reason"`
	}

	Aces struct {
		Year     string `json:"aces_year"`
		Make     string `json:"aces_make"`
		Model    string `json:"aces_model"`
		Submodel string `json:"aces_submodel"`
	}

	GetAllWarehousesResp struct {
		CommonResponse
		Data []LocationData `json:"data"`
	}

	LocationData struct {
		AssignedAttributeValues []AttributeValue `json:"assignedAttributeValues"`
		Headquarters            string           `json:"headquarters"`
		LocationID              int              `json:"location_id"`
		Address
		ContactInfo
		GeoLocation
		LocationInfo
	}

	LocationInfo struct {
		ClientSecret     string `json:"client_secret"`
		ClientType       int    `json:"client_type"`
		CurrentSite      string `json:"current_site"`
		Directions       string `json:"directions"`
		HeadquartersID   int    `json:"headquarters_id"`
		HideFromCustomer string `json:"hide_from_customer"`
		LocationName     string `json:"location_name"`
		Name             string `json:"name"`
		NameLink         string `json:"name_link"`
		SecureServer     string `json:"secure_server"`
		SortOrder        int    `json:"sort_order"`
		Status           int    `json:"status"`
		StoreNumber      string `json:"store_number"`
		TimezoneID       int    `json:"timezone_id"`
	}

	ContactInfo struct {
		AltPhone         string `json:"alt_phone"`
		ContactFirstName string `json:"contact_first_name"`
		ContactLastName  string `json:"contact_last_name"`
		Email            string `json:"email"`
		Fax              string `json:"fax"`
		Phone            string `json:"phone"`
	}

	Address struct {
		Address1  string `json:"address1"`
		Address2  string `json:"address2"`
		City      string `json:"city"`
		CountryID int    `json:"country_id"`
		State     string `json:"state"`
		Zip       string `json:"zip"`
	}

	AttributeValue struct {
		ClientAttributeID      int `json:"clientAttributeId"`
		ClientAttributeValueID int `json:"clientAttributeValueId"`
	}

	GeoLocation struct {
		Latitude  string `json:"latitude"`
		Longitude string `json:"longitude"`
	}

	GetUserResp struct {
		CommonResponse
		Data UserData `json:"data"`
	}

	UserData struct {
		Account        string `json:"account"`
		Address1       string `json:"address1"`
		Address2       string `json:"address2"`
		Alert          string `json:"alert"`
		AllowLogin     string `json:"allow_login"`
		BirthDate      string `json:"birth_date"`
		CID            int    `json:"c_id"`
		CallOkay       string `json:"call_okay"`
		CellPhone      string `json:"cell_phone"`
		City           string `json:"city"`
		ContactOkay    string `json:"contact_okay"`
		CustomerID     int    `json:"customer_id"`
		CustomerTypeID string `json:"customer_type_id"`
		Email          string `json:"email"`
		EmailOkay      string `json:"email_okay"`
		Employer       string `json:"employer"`
		FirstName      string `json:"first_name"`
		Gender         string `json:"gender"`
		LastName       string `json:"last_name"`
		LastUpdate     string `json:"last_update"`
		LastUpdateID   string `json:"last_update_id"`
		LeadID         string `json:"lead_id"`
		MailOkay       string `json:"mail_okay"`
		MiddleName     int    `json:"middle_name"`
		Needs          string `json:"needs"`
		NightPhone     string `json:"night_phone"`
		Occupation     string `json:"occupation"`
		PaymentTypeID  string `json:"payment_type_id"`
		ReferredBy     string `json:"referred_by"`
		RepID          string `json:"rep_id"`
		SignupDate     string `json:"signup_date"`
		State          string `json:"state"`
		StatusID       string `json:"status_id"`
		TimezoneID     string `json:"timezone_id"`
		Zip            string `json:"zip"`
	}

	GetOpenSlotsResp struct {
		CommonResponse
		Data OpenSlotData `json:"data"`
	}

	OpenSlotData struct {
		CID          string `json:"c_id"`
		Date         string `json:"date"`
		EmployeeID   string `json:"employee_id"`
		GmtTimestamp string `json:"gmt_timestamp"`
		NumAppts     string `json:"num_appts"`
		NumSlots     string `json:"num_slots"`
		RoomID       string `json:"room_id"`
		StartTime    string `json:"start_time"`
		TimeZone     string `json:"time_zone"`
	}
)
