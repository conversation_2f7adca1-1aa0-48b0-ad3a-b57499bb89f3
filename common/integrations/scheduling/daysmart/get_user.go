package daysmart

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	userEndpoint = "/Customers/GetCustomerStatuses"
)

func (d *DaySmart) GetUser(ctx context.Context) (models.SchedulingUser, error) {
	userData, err := d.fetchUserData(ctx)
	if err != nil {
		return models.SchedulingUser{}, fmt.Errorf("failed to fetch user data: %w", err)
	}

	user, err := d.createUserModel(ctx, userData.Data)
	if err != nil {
		return models.SchedulingUser{}, fmt.Errorf("failed to create user model: %w", err)
	}

	return user, nil
}

func (d *DaySmart) fetchUserData(ctx context.Context) (GetUserResp, error) {
	var result GetUserResp
	if err := d.post(ctx, userEndpoint, nil, nil, &result); err != nil {
		return GetUserResp{}, fmt.Errorf("API request failed: %w", err)
	}

	if result.Data.CustomerID == 0 {
		return GetUserResp{}, errors.New("received empty user data")
	}

	return result, nil
}

func (d *DaySmart) createUserModel(ctx context.Context, userData UserData) (models.SchedulingUser, error) {
	lastUpdate, err := parseLastUpdate(userData.LastUpdate)
	if err != nil {
		log.Warn(
			ctx,
			"failed to parse last update time",
			zap.String("lastUpdate", userData.LastUpdate),
			zap.Error(err),
		)

		return models.SchedulingUser{}, err
	}

	return models.SchedulingUser{
		ID:                  strconv.Itoa(userData.CustomerID),
		LastChangedDateTime: lastUpdate,
		Email:               userData.Email,
		FirstName:           userData.FirstName,
		LastName:            userData.LastName,
		Phone:               userData.CellPhone,
	}, nil
}

func parseLastUpdate(lastUpdate string) (time.Time, error) {
	if lastUpdate == "" {
		return time.Time{}, errors.New("empty last update time")
	}

	parsed, err := helpers.ParseDatetime(lastUpdate)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid timestamp format: %w", err)
	}

	return parsed, nil
}
