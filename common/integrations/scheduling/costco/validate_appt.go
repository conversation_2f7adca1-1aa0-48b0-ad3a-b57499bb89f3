package costco

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsValidateAppointmentRequest struct {
		models.CyclopsBaseRequest
		PoID       string `json:"poId"`
		ZipCode    string `json:"zipCode"`
		DepotValue string `json:"depotValue"`
		Uom        string `json:"uom"`
		QtyCount   int    `json:"qtyCount"`
	}

	ValidatedAppointment struct {
		AppointmentID string            `json:"appointmentId"`
		Reference     string            `json:"reference"`
		ScheduledTime string            `json:"scheduledTime"`
		Duration      int               `json:"duration"`
		Location      string            `json:"location"`
		Status        string            `json:"status"`
		Notes         string            `json:"notes"`
		Warehouse     Warehouse         `json:"warehouse"`
		Extended      ValidatedExtended `json:"extended"`
	}

	Warehouse struct {
		Name     string `json:"name"`
		City     string `json:"city"`
		State    string `json:"state"`
		ZipCode  string `json:"zipCode"`
		Country  string `json:"country"`
		Website  string `json:"website"`
		StopType string `json:"stopType"`
	}

	ValidatedExtended struct {
		DoorType map[string]string `json:"door_type"`
	}

	ValidateAppointmentResponse struct {
		Success      bool                   `json:"success"`
		Message      string                 `json:"message"`
		Errors       []string               `json:"errors"`
		Appointments []ValidatedAppointment `json:"appointments"`
	}
)

func (m *Costco) ValidateAppointment(
	ctx context.Context,
	poNumbers []string,
	_ models.Warehouse,
	opts ...models.SchedulingOption,
) ([]models.ValidatedPONumber, error) {

	if len(poNumbers) == 0 {
		return nil, errors.New("at least one PO number is required")
	}

	options := &models.SchedulingOptions{
		RequestType: models.RequestTypePickup,
	}
	options.Apply(opts...)

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get url: %w", err)
	}

	// Since PO numbers are not used in the Cyclops request, we only need to make one API call
	// and then create validation results for all PO numbers based on the single response
	cyclopsReq := CyclopsValidateAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    CostcoPlatform,
			Action:      models.ActionValidateAppointment,
			UserID:      m.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: m.creds.Username,
				Password: m.creds.Password,
			},
		},
		ZipCode:    options.ZipCode,
		PoID:       poNumbers[0],
		DepotValue: options.DepotValue,
		Uom:        options.Uom,
		QtyCount:   options.QtyCount,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var validationResp ValidateAppointmentResponse
	if err = json.Unmarshal(body, &validationResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	validatedPOs := make([]models.ValidatedPONumber, 0, len(poNumbers))

	if !validationResp.Success {
		// If the API call failed, mark all PO numbers as invalid
		for _, poNumber := range poNumbers {
			validatedPOs = append(validatedPOs, models.ValidatedPONumber{
				PONumber: poNumber,
				IsValid:  false,
				Error:    fmt.Sprintf("Validation failed: %s", validationResp.Message),
			})
		}
		return validatedPOs, nil
	}

	// Determine if validation was successful based on appointments availability
	isValid := len(validationResp.Appointments) > 0
	var doorType map[string]string
	if isValid && len(validationResp.Appointments) > 0 {
		doorType = validationResp.Appointments[0].Extended.DoorType
	}

	for _, poNumber := range poNumbers {
		validatedPO := models.ValidatedPONumber{
			PONumber: poNumber,
			IsValid:  isValid,
			DoorType: doorType,
		}

		if !isValid {
			validatedPO.Error = "No appointments found for validation"
		}

		validatedPOs = append(validatedPOs, validatedPO)
	}

	return validatedPOs, nil
}
