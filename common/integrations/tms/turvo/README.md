# Turvo TMS

When working with Tur<PERSON>, you will need to create a new public API profile in the Turvo admin console in order for the API to work.

## Testing

Test account creds are in 1Password. If you do not have access, ask an administrator.
WARNING: Do NOT write to production loads. Only write against the production load provided to us by ATSA's Turvo account, `ID=********`, `customId=31408-01721`.

#### Sandbox Environment

If you are using a sandbox environment, you will need to update the following constants in `turvo.go`:

```go
const (
	stagingTMSHost = "my-sandbox-publicapi.turvo.com" // if using a sandbox environment
	prodTMSHost    = "publicapi.turvo.com"
	tokenHeader    = "x-api-key"
)
```

NOTE: Also, the hyperlink in the sidebar to create a new customer needs to point to the sandbox environment! It will send you to the production environment so all you need to do is change the url from app to my-sandbox.

## Callouts

- HTTP requests may return status 200, but the response body may include a `status` field that is either `SUCCESS` or `ERROR` with an `errorMessage` field in `details` object. This is an example of a check call POST that returned `200 - OK` but the date time was incorrectly in the future.

```json
{
  "Status": "ERROR",
  "details": {
    "errorMessage": "Cannot update status with future date",
    "errorCode": "15031"
  }
}
```

- 5XXs may actually be due to request/code error. For example, POST check call where `result.Status.StatusDate.Date.LocalDatetime` was `time.Time` instead of a `string` led to 5XX's. Be sure to double-check the code.

- The `shipments/list` endpoint returns 404 for shipments older than 120 days; this means we'll have to periodically create new test loads. You can still use the Get by ID endpoint for older shipments.

## Load Building

- Not much has changed from the base implementation. The only thing to note is that we've added a new function `BuildTurvoSpecifications` that takes the LLM output and builds the specifications struct specifically for Turvo Loads. In the near future, Sophie will push code to introduce a solution to handle TMS specific fields. Once this happens, a greater explanation of the Turvo load building process will be added.
