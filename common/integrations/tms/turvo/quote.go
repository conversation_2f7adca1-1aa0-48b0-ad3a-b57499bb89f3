package turvo

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/location/types"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

func (t *Turvo) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateQuoteTurvo", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	// Geocode using AWS for consistent city, state format in final submission
	pickupLocation, err := helpers.AwsLocationLookup(ctx,
		quoteBody.PickupLocationCity,
		quoteBody.PickupLocationState,
		quoteBody.PickupLocationZip,
	)
	if err != nil || pickupLocation == nil {
		log.Error(ctx, "error looking up pickup zipcode with AWS Location", zap.Error(err))
		return nil, err
	}

	pickupPlace := *pickupLocation.Results[0].Place

	deliveryLocation, err := helpers.AwsLocationLookup(ctx,
		quoteBody.DeliveryLocationCity,
		quoteBody.DeliveryLocationState,
		quoteBody.DeliveryLocationZip,
	)
	if err != nil || deliveryLocation == nil {
		log.Error(ctx, "error looking up delivery zipcode with AWS Location", zap.Error(err))
		return nil, err
	}
	deliveryPlace := *deliveryLocation.Results[0].Place

	var userTMSGroups models.Groups
	if quoteBody.UserID != 0 {
		user, err := userDB.GetByID(ctx, quoteBody.UserID)
		if err != nil {
			log.Error(ctx, "error getting user by id", zap.Error(err))
			return nil, err
		}
		userTMSGroups = user.GetTMSGroups()
	}

	createQuoteReqBody := buildCreateQuoteBody(
		ctx,
		pickupPlace,
		deliveryPlace,
		quoteBody,
		userTMSGroups,
	)

	createQuoteQueryParams := url.Values{}
	createQuoteQueryParams.Add("fullResponse", "true")

	var createQuoteResponse CreateQuoteResponse

	err = t.postWithAuth(ctx,
		"/v1/shipments",
		createQuoteQueryParams,
		createQuoteReqBody,
		&createQuoteResponse,
		s3backup.TypeQuotes,
	)
	if err != nil {
		return nil, err
	}

	if strings.EqualFold(createQuoteResponse.Status, "error") {
		return nil, fmt.Errorf("failed to create quote: %s - %s",
			createQuoteResponse.Details.ErrorCode, createQuoteResponse.Details.ErrorMessage)
	}

	log.Info(ctx, "created quote", zap.Any("quote resp", createQuoteResponse))

	return &models.CreateQuoteResponse{
		QuoteID:         createQuoteResponse.Details.ID,
		QuoteExternalID: strconv.Itoa(createQuoteResponse.Details.ID),
	}, nil
}

func buildCreateQuoteBody(
	ctx context.Context,
	pickupPlace types.Place,
	deliveryPlace types.Place,
	quoteBody models.CreateQuoteBody,
	userTMSGroups models.Groups,
) CreateQuoteRequest {

	// get customer ids
	customerTempID := int(uuid.New().ID())
	customerID, err := strconv.Atoi(quoteBody.CustomerID)
	if err != nil {
		log.WarnNoSentry(ctx, "error converting customerID to int", zap.Error(err))
		return CreateQuoteRequest{}
	}

	// get equipment
	var equipmentInfo ShipmentEquipment
	switch strings.ToUpper(string(quoteBody.TransportType)) {
	case string(models.VanTransportType):
		equipmentInfo.Type = KeyValuePair{
			Key:   VanKey,
			Value: VanValue,
		}

	case string(models.ReeferTransportType):
		equipmentInfo.Type = KeyValuePair{
			Key:   ReeferKey,
			Value: ReeferValue,
		}
		equipmentInfo.Temp = ReeferTemp
		equipmentInfo.TempUnits = KeyValuePair{
			Key:   FahrenheitKey,
			Value: FahrenheitValue,
		}

	case string(models.FlatbedTransportType):
		equipmentInfo.Type = KeyValuePair{
			Key:   FlatbedKey,
			Value: FlatbedValue,
		}
	}
	// only support 53 ft for now
	equipmentInfo.Size = KeyValuePair{
		Key:   FiftyThreeFTKey,
		Value: FiftyThreeFTValue,
	}

	request := CreateQuoteRequest{
		LTLShipment: false,
		StartDate: struct {
			Date     string `json:"date"`
			TimeZone string `json:"timeZone"`
		}{
			Date:     quoteBody.PickupDate.Time.Format(time.RFC3339),
			TimeZone: *pickupPlace.TimeZone.Name,
		},
		EndDate: struct {
			Date     string `json:"date"`
			TimeZone string `json:"timeZone"`
		}{
			Date:     quoteBody.DeliveryDate.Time.Format(time.RFC3339),
			TimeZone: *deliveryPlace.TimeZone.Name,
		},
		Status: QuoteStatus{
			Code: KeyValuePair{
				Key:   QuoteStatusKey,
				Value: QuoteStatusValue,
			},
		},
		Lane: struct {
			Start string `json:"start"`
			End   string `json:"end"`
		}{
			Start: fmt.Sprintf(
				"%s, %s",
				*pickupPlace.Municipality,
				*pickupPlace.Region,
			),
			End: fmt.Sprintf(
				"%s, %s",
				*deliveryPlace.Municipality,
				*deliveryPlace.Region,
			),
		},
		ModeInfo: []ModeInfo{
			{
				Operation:             0,
				SourceSegmentSequence: "0",
				Mode: KeyValuePair{
					Key:   TLModeKey,
					Value: TLModeValue,
				},
				ServiceType: KeyValuePair{
					Key:   AnyServiceKey,
					Value: AnyServiceValue,
				},
				TotalSegmentValue: TotalSegmentValue{
					Sync:  true,
					Value: json.Number("0"),
					Currency: KeyValuePair{
						Key:   USDCurrencyKey,
						Value: USDCurrencyValue,
					},
				},
			},
		},
		Equipment: []ShipmentEquipment{equipmentInfo},
		CustomerOrder: []CustomerOrder{
			{
				CustomerOrderSourceID: customerTempID,
				Customer: Customer{
					ID: customerID,
				},
			},
		},
		Margin: Margin{
			MinPay: float32(quoteBody.QuotePrice),
			MaxPay: float32(quoteBody.QuotePrice),
		},
	}

	if len(userTMSGroups) > 0 {
		request.Groups = []Group{}
		for _, group := range userTMSGroups {
			request.Groups = append(request.Groups, Group{
				ID:        group.ID,
				Name:      group.Name,
				Operation: 0,
			})
		}
	}

	if quoteBody.QuoteNumber != "" {
		request.CustomerOrder[0].ExternalIDs = []ExternalIDs{
			{
				Type: KeyValuePair{
					Key:   ReferenceExternalIDTypeKey,
					Value: ReferenceExternalIDTypeValue,
				},
				Value: quoteBody.QuoteNumber,
			},
		}
	}

	return request
}
