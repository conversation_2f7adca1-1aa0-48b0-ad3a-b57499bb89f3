package turvo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func (t *Turvo) getTimezoneFromRDS(ctx context.Context, externalID string) (string, error) {
	var loc models.TMSLocation
	err := rds.WithContextReader(ctx).
		Select("timezone").
		Where("tms_integration_id = ? AND external_tms_id = ?", t.tms.ID, strings.TrimSpace(externalID)).
		First(&loc).Error
	if err != nil {
		return "", err
	}
	timezone := strings.TrimSpace(loc.Timezone)
	if timezone == "" {
		return "", fmt.Errorf("empty timezone in DB for external_tms_id=%s", externalID)
	}
	return timezone, nil
}

func (t *Turvo) upsertTimezoneInRDS(ctx context.Context, externalID, timezone string) error {
	loc := models.TMSLocation{
		TMSIntegrationID: t.tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strings.TrimSpace(externalID),
		},
		Timezone: strings.TrimSpace(timezone),
	}

	return rds.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "tms_integration_id"}, {Name: "external_tms_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"timezone", "updated_at"}),
		}).
		Create(&loc).Error
}

func (t *Turvo) lookupTimezone(ctx context.Context, externalTMSID string) (string, error) {
	externalTMSID = strings.TrimSpace(externalTMSID)

	timezoneRds, rdsErr := t.getTimezoneFromRDS(ctx, externalTMSID)
	if strings.TrimSpace(timezoneRds) != "" && rdsErr == nil {
		return strings.TrimSpace(timezoneRds), nil
	}

	timezoneTurvo, turvoErr := t.getTimezoneByTurvoLocationID(ctx, externalTMSID)
	if turvoErr == nil && strings.TrimSpace(timezoneTurvo) != "" {
		if err := t.upsertTimezoneInRDS(ctx, externalTMSID, timezoneTurvo); err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to upsert timezone",
				zap.Error(err),
				zap.String("external_id", externalTMSID),
			)
		}
		return strings.TrimSpace(timezoneTurvo), nil
	}

	return "", fmt.Errorf(
		"failed to resolve timezone from rds and turvo external_tms_id=%s (rdsErr=%w, turvoErr=%w)",
		externalTMSID,
		rdsErr,
		turvoErr,
	)
}

func (t *Turvo) lookupTimezonePickupAndConsignee(
	ctx context.Context,
	pickup models.Pickup,
	consignee models.Consignee,
) (string, string, error) {
	pickupTimezone, puErr := t.lookupTimezone(ctx, pickup.ExternalTMSID)
	deliveryTimezone, dlErr := t.lookupTimezone(ctx, consignee.ExternalTMSID)

	// third fallback to aws (can remove this if we want to only rely on rds and turvo)
	if pickupTimezone == "" {
		pickupTimezone, puErr = timezone.GetTimezoneByZipOrCity(
			ctx,
			pickup.Zipcode,
			pickup.City,
			pickup.State,
			pickup.Country,
		)
	}

	if deliveryTimezone == "" {
		deliveryTimezone, dlErr = timezone.GetTimezoneByZipOrCity(
			ctx,
			consignee.Zipcode,
			consignee.City,
			consignee.State,
			consignee.Country,
		)
	}

	if strings.TrimSpace(pickupTimezone) == "" || strings.TrimSpace(deliveryTimezone) == "" {
		return pickupTimezone, deliveryTimezone, fmt.Errorf(
			"failed to resolve timezones after aws fallback (pickupErr=%w, deliveryErr=%w)", puErr, dlErr,
		)
	}
	return pickupTimezone, deliveryTimezone, nil
}

func (t *Turvo) getTimezoneByTurvoLocationID(ctx context.Context, idStr string) (string, error) {
	id, err := strconv.Atoi(strings.TrimSpace(idStr))
	if err != nil {
		return "", fmt.Errorf("invalid location id %q: %w", idStr, err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")

	var response LocationResponse
	if err := t.getWithAuth(
		ctx,
		"/v1/locations/"+strconv.Itoa(id),
		queryParams,
		&response,
		s3backup.TypeLocations,
	); err != nil {
		return "", fmt.Errorf("get location failed: %w", err)
	}
	timezone := strings.TrimSpace(response.Details.Timezone)
	if timezone == "" {
		return "", fmt.Errorf("empty timezone in response for location %d", id)
	}
	return timezone, nil
}

// mapToValue converts a human-readable value to a Turvo-specific KeyValuePair using a reverse mapping.
// This function is used to translate standard values (like "USD", "kg", "Flatbed") into Turvo's
// internal representation, which consists of numeric codes paired with their human-readable values.
//
// For example, if we have a reverse mapping of {"USD": "1", "EUR": "2"}, then:
//   - mapToValue("USD", reverseMap) returns KeyValuePair{Key: "1", Value: "USD"}
//   - mapToValue("GBP", reverseMap) returns an empty KeyValuePair{}
//
// The reverse mappings are defined in enums.go and are used throughout the Turvo integration
// to convert our standard values into Turvo's expected format for API requests.
func mapToValue(value string, rm map[string]string) KeyValuePair {
	if rm[value] != "" {
		return KeyValuePair{
			Key:   rm[value],
			Value: value,
		}
	}

	return KeyValuePair{}
}

// findWeightUnitWithFallback attempts to find a matching weight unit in the Turvo mapping,
// with intelligent fallbacks. Returns empty string if no weight or if unit can't be determined.
func findWeightUnitWithFallback(unit string, weight float32) string {
	// If no weight value, return empty unit
	if weight <= 0 {
		return ""
	}

	// Try direct mapping first
	if matchedUnit := helpers.FindMatchingKey(unit, WeightUnitReverseMap); matchedUnit != "" {
		return matchedUnit
	}

	// Handle common variations that might not be in the mapping
	unitLower := strings.ToLower(strings.TrimSpace(unit))
	switch unitLower {
	case "lbs", "pounds", "pound":
		return "lb"
	case "kilograms", "kilogram", "kgs":
		return "kg"
	case "grams", "gram":
		return "g"
	case "tons":
		return "ton"
	case "ounces", "ounce":
		return "oz"
	case "tonnes", "metric tons", "metric ton":
		return "t"
	}

	// If we can't determine a valid unit, return empty string
	// This will cause the weight fields to be omitted from the Turvo API request
	// rather than sending invalid empty KeyValuePairs
	return ""
}

// BuildTurvoSpecifications takes LLM load building output and builds the specifications struct specifically
// for Turvo Loads.
func BuildTurvoSpecifications(rawSpecs models.SuggestedSpecifications) (specs models.SuggestedSpecifications) {

	specs = rawSpecs

	if helpers.IsStringInArray([]string{"Any", "Standard", "Expedite"}, rawSpecs.ServiceType) {
		specs.ServiceType = rawSpecs.ServiceType
	} else {
		specs.ServiceType = "Any"
	}

	if helpers.IsStringInArray(
		models.TransportTypesToStrings(ListTurvoTransportTypes()),
		rawSpecs.TransportType,
	) {
		specs.TransportType = rawSpecs.TransportType
	} else {
		specs.TransportType = "VAN"
	}

	// Match weight units with fallbacks to ensure they're never blank
	specs.TotalWeight.Unit = findWeightUnitWithFallback(rawSpecs.TotalWeight.Unit, rawSpecs.TotalWeight.Val)
	specs.NetWeight.Unit = findWeightUnitWithFallback(rawSpecs.NetWeight.Unit, rawSpecs.NetWeight.Val)

	// If either weight is zero, copy both value and unit from the non-zero weight
	if rawSpecs.NetWeight.Val == 0 && rawSpecs.TotalWeight.Val != 0 {
		specs.NetWeight = specs.TotalWeight
	} else if rawSpecs.TotalWeight.Val == 0 && rawSpecs.NetWeight.Val != 0 {
		specs.TotalWeight = specs.NetWeight
	}

	specs.TransportSize = helpers.FindMatchingKey(rawSpecs.TransportSize, EquipmentSizeReverseMap)

	specs.TotalPieces.Unit = helpers.FindMatchingKey(rawSpecs.TotalPieces.Unit, ItemUnitReverseMap)

	return specs
}
