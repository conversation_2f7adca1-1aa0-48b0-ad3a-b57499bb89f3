package turvo

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDateTimeField_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    time.Time
		expectError bool
	}{
		{
			name:        "Valid RFC3339 date",
			input:       `{"date": "2024-03-20T15:04:05Z", "timeZone": "UTC"}`,
			expected:    time.Date(2024, 03, 20, 15, 04, 05, 0, time.UTC),
			expectError: false,
		},
		{
			name:        "Empty date field",
			input:       `{"date": "", "timeZone": "UTC"}`,
			expected:    time.Time{},
			expectError: false,
		},
		{
			name:        "Invalid date format",
			input:       `{"date": "2024-03-20", "timeZone": "UTC"}`,
			expectError: true,
		},
		{
			name:        "Missing date field",
			input:       `{"timeZone": "UTC"}`,
			expected:    time.Time{},
			expectError: false,
		},
		{
			name:        "Invalid JSON",
			input:       `{"date": "2024-03-20T15:04:05Z", "timeZone": "UTC"`, // Missing closing brace
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var dtf DateTimeField
			err := json.Unmarshal([]byte(tt.input), &dtf)

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, dtf.Date)
			}
		})
	}
}
