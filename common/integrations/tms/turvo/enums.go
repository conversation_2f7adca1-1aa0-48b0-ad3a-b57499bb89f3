package turvo

import "github.com/drumkitai/drumkit/common/models"

const CustomIDType = "customId[eq]"
const PONumIDType = "poNumber[eq]"

var ServiceTypeReverseMap = map[string]string{
	"Any":      "24304",
	"Expedite": "24349",
	"Standard": "24338",
}

var CurrencyReverseMap = map[string]string{
	"USD": "1550",
	"CAD": "1551",
}

var WeightUnitReverseMap = map[string]string{
	"t":   "1522",
	"oz":  "1523",
	"ton": "1524",
	"g":   "1525",
	"lb":  "1520",
	"kg":  "1521",
}

var EquipmentSizeReverseMap = map[string]string{
	"8 ft":     "1308",
	"10 ft":    "1309",
	"12 ft":    "1310",
	"14 ft":    "1311",
	"16 ft":    "1312",
	"42 ft":    "1313",
	"45 ft":    "1314",
	"20 ft":    "1300",
	"24 ft":    "1301",
	"26 ft":    "1317",
	"28 ft":    "1302",
	"40 ft":    "1303",
	"48 ft":    "1304",
	"50 ft":    "1305",
	"53 ft":    "1306",
	"Other":    "1307",
	"75 bbl":   "1336",
	"90 bbl":   "1337",
	"115 bbl":  "1339",
	"110 bbl":  "1330",
	"130 bbl":  "1331",
	"150 bbl":  "1332",
	"170 bbl":  "1333",
	"190 bbl":  "1334",
	"210 bbl":  "1335",
	"5000 psi": "1344",
	"6000 psi": "1345",
	"7000 psi": "1346",
	"8000 psi": "1347",
	"1000 psi": "1340",
	"2000 psi": "1341",
	"3000 psi": "1342",
	"4000 psi": "1343",
	"1 car":    "1318",
	"2 car":    "1319",
	"3 car":    "1320",
	"4 car":    "1321",
	"70 bbl":   "1322",
	"82 bbl":   "1323",
	"92 bbl":   "1324",
	"100 bbl":  "1325",
	"125 bbl":  "1326",
	"160 bbl":  "1327",
	"180 bbl":  "1328",
	"200 bbl":  "1329",
}

var ItemUnitReverseMap = map[string]string{
	"Bags":                 "6003",
	"Bales":                "6004",
	"Barrels":              "6035",
	"Base units":           "6039",
	"Bin":                  "6047",
	"Blocks":               "6033",
	"Bottles":              "6040",
	"Boxes":                "6001",
	"Buckets":              "6056",
	"Bulk":                 "6034",
	"Bundles":              "6005",
	"Bushels":              "6026",
	"Cans":                 "6006",
	"Carboys":              "6007",
	"Carpets":              "6008",
	"Cartons":              "6009",
	"Cases":                "6022",
	"Coils":                "6010",
	"Combinations":         "6057",
	"Containers":           "6027",
	"Crates":               "6011",
	"Cylinders":            "6012",
	"Drums":                "6013",
	"Each":                 "6037",
	"Feet":                 "6051",
	"Gallons":              "6028",
	"Grams":                "6053",
	"Hundredweight on Net": "6058",
	"Inner Pack":           "6052",
	"Items":                "6055",
	"Kegs":                 "6024",
	"Kgs":                  "6031",
	"Kit":                  "6048",
	"Layer":                "6054",
	"Lbs":                  "6030",
	"Liters":               "6032",
	"Loose":                "6002",
	"Metric tons":          "6025",
	"Other":                "6021",
	"Pack":                 "6049",
	"Packages":             "6041",
	"Pails":                "6014",
	"Pair":                 "6050",
	"Pallets":              "6038",
	"Piece":                "6043",
	"Pouches":              "6059",
	"Racks":                "6042",
	"Ream":                 "6044",
	"Reels":                "6015",
	"Rolls":                "6016",
	"Skids":                "6017",
	"Spack":                "6045",
	"Spool":                "6046",
	"Tons":                 "6029",
	"Totes":                "6018",
	"Trays":                "6060",
	"Truckload":            "6023",
	"Tubes/pipes":          "6019",
	"Tubs":                 "6061",
	"Units":                "6036",
	"Vehicles":             "6020",
}

// CostTypeReverseMap maps lowercase charge labels to their corresponding keys
// Source: https://app.turvo.com/lobby/documentation#tag/Shipment
var CostTypeReverseMap = map[string]string{
	"accessorial - cross dock":                           "1611",
	"accessorial - detention":                            "1612",
	"accessorial - drayage":                              "1613",
	"accessorial - extra miles":                          "1614",
	"accessorial - layover":                              "1615",
	"accessorial - loading":                              "1616",
	"accessorial - lumper":                               "1617",
	"accessorial - pallets":                              "1618",
	"accessorial - stops":                                "1619",
	"accessorial - storage":                              "1620",
	"accessorial - tarp":                                 "1621",
	"accessorial - team services":                        "1622",
	"accessorial - tolls":                                "1623",
	"accessorial - trailer cleaning":                     "1624",
	"accessorial - twic services":                        "1625",
	"accessorial - unloading":                            "1626",
	"accessorial - restack":                              "1636",
	"accessorial - origin terminal handling charge":      "23014",
	"accessorial - destination terminal handling charge": "23015",
	"accessorial - demurrage and detention":              "23016",
	"accessorial - origin reefer plug-in":                "23017",
	"accessorial - destination reefer plug-in":           "23018",
	"accessorial - accessorial charge":                   "160200",
	"accessorial - additional charge":                    "160201",
	"accessorial - bulkhead":                             "160202",
	"accessorial - carb compliance fee":                  "160203",
	"accessorial - charged as weight":                    "160204",
	"accessorial - deficit charge":                       "160205",
	"accessorial - excessive length":                     "160206",
	"accessorial - forklift":                             "160207",
	"accessorial - high cost delivery":                   "160208",
	"accessorial - redelivery fee":                       "160209",
	"accessorial - refrigerate":                          "160210",
	"accessorial - requires signature":                   "160211",
	"accessorial - unknown charge":                       "160212",
	"accessorial - weekend delivery":                     "160213",
	"accessorial - weekend pickup":                       "160214",
	"accessorial - weight fee - reweigh":                 "160215",
	"accessorial - white glove service":                  "160216",
	"location - airport delivery":                        "160221",
	"location - airport pickup":                          "160222",
	"location - camp delivery":                           "160223",
	"location - camp pickup":                             "160224",
	"location - church delivery":                         "160225",
	"location - church pickup":                           "160226",
	"location - commercial delivery":                     "160227",
	"location - commercial pickup":                       "160228",
	"location - construction site delivery":              "160229",
	"location - construction site pickup":                "160230",
	"location - convention/tradeshow delivery":           "160231",
	"location - convention/tradeshow pickup":             "160232",
	"location - country club delivery":                   "160233",
	"location - country club pickup":                     "160234",
	"location - fair/amusement/park delivery":            "160235",
	"location - fair/amusement/park pickup":              "160236",
	"location - farm delivery":                           "160237",
	"location - farm pickup":                             "160238",
	"location - government site delivery":                "160239",
	"location - government site pickup":                  "160240",
	"location - grocery warehouse delivery":              "160241",
	"location - grocery warehouse pickup":                "160242",
	"location - hospital delivery":                       "160243",
	"location - hospital pickup":                         "160244",
	"location - hotel delivery":                          "160245",
	"location - hotel pickup":                            "160246",
	"location - mall delivery":                           "160247",
	"location - mall pickup":                             "160248",
	"location - military installation delivery":          "160249",
	"location - military installation pickup":            "160250",
	"harmonized sales tax":                               "160251",
	"mine site delivery":                                 "160252",
	"mine site pickup":                                   "160253",
	"native american reservation delivery":               "160254",
	"native american reservation pickup":                 "160255",
	"nursing home delivery":                              "160256",
	"nursing home pickup":                                "160257",
	"prison delivery":                                    "160258",
	"prison pickup":                                      "160259",
	"resort delivery":                                    "160260",
	"resort pickup":                                      "160261",
	"school delivery":                                    "160262",
	"school pickup":                                      "160263",
	"accessorial - other":                                "accessorial.other",
	"accessorial - white glove":                          "1640",
	"accessorial - bonded shipment":                      "1641",
	"accessorial - border crossing":                      "1642",
	"accessorial - chassis":                              "1643",
	"accessorial - crane":                                "1644",
	"accessorial - delivery charge":                      "1645",
	"accessorial - demurrage":                            "1646",
	"accessorial - driver assistance":                    "1647",
	"accessorial - drop trailer":                         "1648",
	"accessorial - escort":                               "1649",
	"accessorial - expedited":                            "1650",
	"accessorial - farm":                                 "1651",
	"accessorial - freeze protect":                       "1652",
	"accessorial - hook up":                              "1653",
	"accessorial - insurance":                            "1654",
	"accessorial - liftgate":                             "1655",
	"accessorial - limited access":                       "1656",
	"accessorial - oversized":                            "1657",
	"accessorial - overweight":                           "1658",
	"accessorial - permit":                               "1659",
	"accessorial - chassis split":                        "25100",
	"accessorial - rebill":                               "1660",
	"accessorial - detention (loading)":                  "25101",
	"accessorial - reclass":                              "1661",
	"accessorial - detention (unloading)":                "25102",
	"accessorial - reconsignment":                        "1662",
	"accessorial - per diem":                             "25103",
	"accessorial - redelivery":                           "1663",
	"accessorial - tri-axle":                             "25104",
	"accessorial - repossession":                         "1664",
	"accessorial - residential":                          "1665",
	"accessorial - reweigh":                              "1666",
	"accessorial - scale":                                "1667",
	"accessorial - school":                               "1668",
	"accessorial - service area surcharge":               "1669",
	"accessorial - shunting":                             "1670",
	"accessorial - single shipment":                      "1671",
	"accessorial - tow":                                  "1672",
	"accessorial - tracking":                             "1673",
	"accessorial - transload":                            "1674",
	"accessorial - weekend":                              "1675",
	"accessorial - pre-pull":                             "25105",
	"accessorial - capacity surcharge":                   "23006",
	"accessorial - shipment restraints":                  "23007",
	"accessorial - delivery appointment":                 "1683",
	"accessorial - hazmat":                               "1687",
	"accessorial - notify before arrival":                "1691",
	"accessorial - temperature controlled":               "1696",
	"accessorial - after hours":                          "1680",
	"accessorial - driver count":                         "1684",
	"accessorial - holiday":                              "1688",
	"accessorial - pickup appointment":                   "1693",
	"accessorial - tradeshow":                            "1697",
	"accessorial - bobtail":                              "1681",
	"accessorial - empty return":                         "1685",
	"accessorial - inside delivery":                      "1689",
	"accessorial - small truck":                          "1694",
	"accessorial - deadhead":                             "1682",
	"accessorial - guaranteed":                           "1686",
	"accessorial - inside pickup":                        "1690",
	"accessorial - sort and segregate":                   "1695",
	"accessorial - customs or in-bond freight charge":    "25268",
	"accessorial - port customs paperwork processing":    "25339",
	"accessorial - truck ordered not used":               "1793",
	"accessorial - goods and services tax":               "25295",
	"accessorial - heat":                                 "25298",
	"freight - per ton":                                  "1606",
	"freight - per tote":                                 "1607",
	"freight - per unit":                                 "1608",
	"freight - per hour":                                 "23008",
	"freight - per kg":                                   "23022",
	"freight - per kilometer":                            "23023",
	"freight - line haul surcharge":                      "160217",
	"freight - linehaul":                                 "160218",
	"freight - minimum charge":                           "160219",
	"freight - item":                                     "160220",
	"freight - per pallet":                               "23003",
	"freight - per case":                                 "23004",
	"freight - per mile":                                 "1601",
	"freight - per bag":                                  "1602",
	"freight - flat":                                     "1600",
	"freight - per box":                                  "1603",
	"freight - per 50 lbs":                               "1604",
	"freight - per 100 lbs":                              "1605",
	"fuel - flat":                                        "1609",
	"fuel - per mile":                                    "1610",
	"fuel - bunker adjustment":                           "23019",
	"fuel - per kilometer":                               "23024",
	"fuel - percent of freight":                          "23002",
	"fuel - percentage":                                  "23005",
	"other - advance fee":                                "1627",
	"other - canceled shipment":                          "1628",
	"other - claim":                                      "1629",
	"other - credit card fee":                            "1630",
	"other - documentation":                              "1631",
	"other - gate fee":                                   "1632",
	"other - late truck":                                 "1633",
	"other - payment adjustment":                         "1634",
	"other - product issue":                              "1635",
	"other":                                              "1637",
	"other - food grade":                                 "23009",
	"other - documentation fees":                         "23010",
	"other - pre-carriage":                               "23011",
	"other - on-carriage":                                "23012",
	"other - imo surcharge":                              "23013",
	"other - discount":                                   "1638",
	"total planned cost":                                 "23020",
	"other - payment fee":                                "1676",
	"other - processing fee":                             "1677",
	"other - tax":                                        "1678",
	"other - claim expense":                              "25106",
	"service - per kg":                                   "23025",
	"service - per kilometer":                            "23026",
	"service - flat":                                     "1698",
	"service - per mile":                                 "1699",
	"service - per 100 lbs":                              "23000",
	"service - per stop":                                 "23001",
	"tax - vat":                                          "23021",
	"accessorial - freight under management":             "25500",
	"accessorial - freight audit pay":                    "25501",
}

var EquipmentReverseMap = map[string]string{
	"Ag hopper":                          "24520",
	"Auger feed truck":                   "24519",
	"Auto transport":                     "1209",
	"Automotive racks":                   "24401",
	"Barrack barge":                      "24436",
	"Bobtail":                            "1238",
	"Box Truck":                          "24527",
	"Boxcars":                            "24402",
	"Bulk carriers":                      "24418",
	"Bulk commodity":                     "24446",
	"Car float barge":                    "24434",
	"Cargo ship":                         "24420",
	"Cargo van":                          "24455",
	"Centerbeams":                        "24403",
	"Chassis":                            "1237",
	"Coil cars":                          "24405",
	"Conestoga":                          "1210",
	"Container - Bulk":                   "24462",
	"Container - Power pack":             "24463",
	"Container - Upgraded":               "24464",
	"Container - flat rack":              "1229",
	"Container - high cube":              "1227",
	"Container - open top":               "1228",
	"Container - pallet wide":            "1230",
	"Container - refrigerated":           "1231",
	"Container - standard":               "1211",
	"Container - tanker":                 "1232",
	"Container - temperature controlled": "1245",
	"Container ships":                    "24419",
	"Containers":                         "24480",
	"Covered hoppers":                    "24404",
	"Crane":                              "24459",
	"Deck barge":                         "24432",
	"Double drop (low boy)":              "1212",
	"Double-axle":                        "24450",
	"Driveaway":                          "24461",
	"Dry - HC":                           "24504",
	"Dry - Standard":                     "24503",
	"Dump - end":                         "1218",
	"Dump - side":                        "1219",
	"Electric heat":                      "24452",
	"Enclosed":                           "1220",
	"Flatbed":                            "1204",
	"Flatbed - 4' tarp":                  "1205",
	"Flatbed - 6' tarp":                  "1206",
	"Flatbed - 8' tarp":                  "1207",
	"Flatbed - Quad-axle":                "24515",
	"Flatbed - Quad-axle rolltite":       "24516",
	"Flatbed - Super B":                  "24517",
	"Flatbed - auto":                     "1217",
	"Flatbed - covered wagon":            "24444",
	"Flatbed - curtain side":             "24445",
	"Flatbed - legal":                    "24442",
	"Flatbed - standard":                 "24441",
	"Flatbed - stretch/extendable":       "24443",
	"Flatcars":                           "24406",
	"Food trailer - frozen":              "1243",
	"Gondolas":                           "24407",
	"Hopper":                             "1221",
	"Hopper barge":                       "24433",
	"Hot oil truck":                      "1239",
	"Hotshot":                            "24460",
	"Hotshot Flatbed":                    "24511",
	"Hydro excavation truck":             "1240",
	"Insulated Van or Reefer":            "24468",
	"Isotank":                            "24514",
	"Liquid cargo barge":                 "24435",
	"Logging":                            "24447",
	"Mini float":                         "24456",
	"Moffett":                            "24457",
	"Open top hoppers":                   "24409",
	"Other":                              "1216",
	"Pickup truck":                       "24454",
	"Pneumatic":                          "1226",
	"Power barge":                        "24438",
	"Power unit - tractor":               "1233",
	"Pump truck":                         "1241",
	"Reefer":                             "24487",
	"Reefer - HC":                        "24506",
	"Reefer or Vented Van":               "24469",
	"Reefer- Standard":                   "24505",
	"Reefer – Quadaxle":                  "24525",
	"Reefer – Triaxle":                   "24524",
	"Refrigerated":                       "1208",
	"Refrigerated boxcar":                "24408",
	"Refrigerated ships":                 "24421",
	"Removable goose neck":               "1213",
	"Roll Tite":                          "24523",
	"Roll on/Roll off Ships":             "24422",
	"Royal Barge":                        "24439",
	"Single-axle":                        "24449",
	"Specialized rail equipment":         "24410",
	"Split Hopper barge":                 "24437",
	"Step deck":                          "1214",
	"Stepdeck Conestoga":                 "24526",
	"Straight truck":                     "24453",
	"Tank barge":                         "24431",
	"Tanker":                             "1215",
	"Tanker - chemical":                  "1222",
	"Tanker - food":                      "1223",
	"Tanker - fuel":                      "1224",
	"Tow":                                "1225",
	"Tow - heavy duty":                   "1235",
	"Tow - landoll":                      "1236",
	"Tow - light duty":                   "24448",
	"Tow - medium duty":                  "1234",
	"Tri-axle":                           "24451",
	"Truck, van":                         "1244",
	"Van":                                "1200",
	"Van - curtain side":                 "1203",
	"Van - dry":                          "24440",
	"Van - open top":                     "24528",
	"Van - sprinter":                     "24513",
	"Van - tri-axle dry":                 "24518",
	"Van - vented":                       "1201",
	"Walking floor":                      "24521",
	"Winch truck":                        "24458",
}

// For mapping Turvo equipment codes to Drumkit QuickQuote transport type enums; powers win rate metrics
var EquipmentToTransportTypeEnumMap = map[string]models.TransportType{
	"ag hopper":                          models.SpecialTransportType,
	"auger feed truck":                   models.SpecialTransportType,
	"auto transport":                     models.VanTransportType,
	"automotive racks":                   models.SpecialTransportType,
	"barrack barge":                      models.SpecialTransportType,
	"bobtail":                            models.VanTransportType,
	"box truck":                          models.BoxTruckTransportType,
	"boxcars":                            models.SpecialTransportType,
	"bulk carriers":                      models.SpecialTransportType,
	"bulk commodity":                     models.SpecialTransportType,
	"car float barge":                    models.SpecialTransportType,
	"cargo ship":                         models.SpecialTransportType,
	"cargo van":                          models.VanTransportType,
	"centerbeams":                        models.SpecialTransportType,
	"chassis":                            models.VanTransportType,
	"coil cars":                          models.SpecialTransportType,
	"conestoga":                          models.FlatbedTransportType,
	"container - bulk":                   models.SpecialTransportType,
	"container - power pack":             models.SpecialTransportType,
	"container - upgraded":               models.SpecialTransportType,
	"container - flat rack":              models.SpecialTransportType,
	"container - high cube":              models.SpecialTransportType,
	"container - open top":               models.SpecialTransportType,
	"container - pallet wide":            models.SpecialTransportType,
	"container - refrigerated":           models.SpecialTransportType,
	"container - standard":               models.SpecialTransportType,
	"container - tanker":                 models.SpecialTransportType,
	"container - temperature controlled": models.SpecialTransportType,
	"container ships":                    models.SpecialTransportType,
	"containers":                         models.SpecialTransportType,
	"covered hoppers":                    models.SpecialTransportType,
	"crane":                              models.SpecialTransportType,
	"deck barge":                         models.FlatbedTransportType,
	"double drop (low boy)":              models.FlatbedTransportType,
	"double-axle":                        models.FlatbedTransportType,
	"driveaway":                          models.FlatbedTransportType,
	"dry - hc":                           models.SpecialTransportType,
	"dry - standard":                     models.SpecialTransportType,
	"dump - end":                         models.SpecialTransportType,
	"dump - side":                        models.SpecialTransportType,
	"electric heat":                      models.SpecialTransportType,
	"enclosed":                           models.SpecialTransportType,
	"flatbed":                            models.FlatbedTransportType,
	"flatbed - 4' tarp":                  models.FlatbedTransportType,
	"flatbed - 6' tarp":                  models.FlatbedTransportType,
	"flatbed - 8' tarp":                  models.FlatbedTransportType,
	"flatbed - quad-axle":                models.FlatbedTransportType,
	"flatbed - quad-axle rolltite":       models.FlatbedTransportType,
	"flatbed - super b":                  models.FlatbedTransportType,
	"flatbed - auto":                     models.FlatbedTransportType,
	"flatbed - covered wagon":            models.FlatbedTransportType,
	"flatbed - curtain side":             models.FlatbedTransportType,
	"flatbed - legal":                    models.FlatbedTransportType,
	"flatbed - standard":                 models.FlatbedTransportType,
	"flatbed - stretch/extendable":       models.FlatbedTransportType,
	"flatcars":                           models.SpecialTransportType,
	"food trailer - frozen":              models.SpecialTransportType,
	"gondolas":                           models.SpecialTransportType,
	"hopper":                             models.SpecialTransportType,
	"hopper barge":                       models.SpecialTransportType,
	"hot oil truck":                      models.SpecialTransportType,
	"hotshot":                            models.HotShotTransportType,
	"hotshot flatbed":                    models.HotShotTransportType,
	"hydro excavation truck":             models.SpecialTransportType,
	"insulated van or reefer":            models.VanTransportType,
	"isotank":                            models.SpecialTransportType,
	"liquid cargo barge":                 models.SpecialTransportType,
	"logging":                            models.SpecialTransportType,
	"mini float":                         models.SpecialTransportType,
	"moffett":                            models.SpecialTransportType,
	"open top hoppers":                   models.SpecialTransportType,
	"other":                              models.SpecialTransportType,
	"pickup truck":                       models.VanTransportType,
	"pneumatic":                          models.SpecialTransportType,
	"power barge":                        models.SpecialTransportType,
	"power unit - tractor":               models.SpecialTransportType,
	"pump truck":                         models.SpecialTransportType,
	"reefer":                             models.ReeferTransportType,
	"reefer - hc":                        models.ReeferTransportType,
	"reefer or vented van":               models.ReeferTransportType,
	"reefer- standard":                   models.ReeferTransportType,
	"reefer - quadaxle":                  models.ReeferTransportType,
	"reefer - triaxle":                   models.ReeferTransportType,
	"refrigerated":                       models.ReeferTransportType,
	"refrigerated boxcar":                models.ReeferTransportType,
	"refrigerated ships":                 models.ReeferTransportType,
	"removable goose neck":               models.FlatbedTransportType,
	"roll tite":                          models.SpecialTransportType,
	"roll on/roll off ships":             models.SpecialTransportType,
	"royal barge":                        models.SpecialTransportType,
	"single-axle":                        models.SpecialTransportType,
	"specialized rail equipment":         models.SpecialTransportType,
	"split hopper barge":                 models.SpecialTransportType,
	"step deck":                          models.FlatbedTransportType,
	"stepdeck conestoga":                 models.FlatbedTransportType,
	"straight truck":                     models.VanTransportType,
	"tank barge":                         models.SpecialTransportType,
	"tanker":                             models.SpecialTransportType,
	"tanker - chemical":                  models.SpecialTransportType,
	"tanker - food":                      models.SpecialTransportType,
	"tanker - fuel":                      models.SpecialTransportType,
	"tow":                                models.SpecialTransportType,
	"tow - heavy duty":                   models.SpecialTransportType,
	"tow - landoll":                      models.SpecialTransportType,
	"tow - light duty":                   models.SpecialTransportType,
	"tow - medium duty":                  models.SpecialTransportType,
	"tri-axle":                           models.SpecialTransportType,
	"truck, van":                         models.VanTransportType,
	"van":                                models.VanTransportType,
	"van - curtain side":                 models.VanTransportType,
	"van - dry":                          models.VanTransportType,
	"van - open top":                     models.VanTransportType,
	"van - sprinter":                     models.SprinterTransportType,
	"van - tri-axle dry":                 models.VanTransportType,
	"van - vented":                       models.VanTransportType,
	"walking floor":                      "",
	"winch truck":                        "",
	"dump trailer":                       "",
	"flatbed or step deck":               models.FlatbedTransportType,
	"1 ton dry":                          "",
	"lowboy or rem gooseneck (rgn)":      models.FlatbedTransportType,
	"rgn extendable":                     "",
}

var AppointmentTypeReverseMap = map[string]string{
	"FCFS":           "9400",
	"By appointment": "9401",
}

var ModeReverseMap = map[string]string{
	string(models.RailMode):       RailModeKey,
	string(models.OceanMode):      OceanModeKey,
	string(models.AirMode):        AirModeKey,
	string(models.TLMode):         TLModeKey,
	string(models.DrayageMode):    DrayageModeKey,
	string(models.LTLMode):        LTLModeKey,
	string(models.ParcelMode):     ParcelModeKey,
	string(models.IntermodalMode): IntermodalModeKey,
}
