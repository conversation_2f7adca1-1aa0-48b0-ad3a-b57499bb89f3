package turvo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmslocation "github.com/drumkitai/drumkit/common/rds/tms_location"
)

const createRoutingGuidePath = "/v1/routing-guides"

// CreateRoutingGuide creates a new routing guide in Turvo from our truck list model
func (t *Turvo) CreateRoutingGuide(ctx context.Context, truckList *models.TruckList) (*RoutingGuideResponse, error) {
	ctx, span := otel.StartSpan(ctx, "turvo.CreateRoutingGuide", []attribute.KeyValue{
		attribute.String("tms", string(t.tms.Name)),
	})
	defer span.End(nil)

	// Create a routing guide for each truck
	var responses []*RoutingGuideResponse
	for _, truck := range truckList.Trucks {
		// Map our truck list to Turvo's routing guide format
		request := t.mapTruckListToRoutingGuide(ctx, truckList, truck)

		log.Info(ctx, "request", zap.Any("request", request))

		jsonBytes, err := json.Marshal(request)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}
		log.Info(ctx, "request", zap.String("request", string(jsonBytes)))

		// Construct query parameters
		queryParams := url.Values{}
		queryParams.Add("fullResponse", "true")

		// Make the API call
		var response RoutingGuideResponse
		if err := t.postWithAuth(
			ctx,
			createRoutingGuidePath,
			queryParams,
			request,
			&response,
			s3backup.TypeTruckLists,
		); err != nil {
			return nil, fmt.Errorf("failed to create routing guide: %w", err)
		}

		log.Info(ctx, "response", zap.Any("response", response))

		// Check for errors in the response
		if response.Status == "Error" {
			return nil, fmt.Errorf("failed to create routing guide: %s", response.Details.ErrorMessage)
		}

		responses = append(responses, &response)
	}

	// Return the last response for backward compatibility
	if len(responses) > 0 {
		return responses[len(responses)-1], nil
	}
	return nil, errors.New("no routing guides were created")
}

// mapTruckListToRoutingGuide maps our truck list model to Turvo's routing guide format
func (t *Turvo) mapTruckListToRoutingGuide(
	ctx context.Context,
	truckList *models.TruckList,
	truck models.Truck,
) *RoutingGuideRequest {
	request := &RoutingGuideRequest{
		RoutingSequences: make([]RoutingSequence, 0),
	}

	// Only set customer if we have a name
	if truckList.Carrier.Name != "" {
		request.Customer = &struct {
			ID   int    `json:"id,omitempty"`
			Name string `json:"name,omitempty"`
		}{
			Name: truckList.Carrier.Name,
		}
	}

	// Map each truck to a routing sequence
	sequence := RoutingSequence{
		Operation:           1, // Add operation
		Sequence:            1,
		Duration:            1440,            // 24 hours in minutes
		RoutingSequenceType: "CARRIER_OFFER", // Changed from "carrier" to "CARRIER_OFFER"
		Rate:                0.0,             // Default rate
		Currency: struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		}{
			Key:   "USD",
			Value: "USD",
		},
		Emails: []struct {
			Email string `json:"email"`
		}{},
	}

	// Add pickup and dropoff dates
	if truck.PickupDate.Applied.Valid {
		request.StartDate = truck.PickupDate.Applied.Time.Format("2006-01-02")
	}
	if truck.DropoffDate.Applied.Valid {
		request.EndDate = truck.DropoffDate.Applied.Time.Format("2006-01-02")
	}

	// Add carrier information
	carrier := struct {
		ID int64 `json:"id"`
	}{
		ID: 0, // Default to 0 if no ID is set
	}

	if truckList.Carrier.ExternalTMSID != "" {
		if id, err := strconv.ParseInt(truckList.Carrier.ExternalTMSID, 10, 64); err == nil {
			carrier.ID = id
		}
	}

	// Always set the carrier in the sequence, even if we don't have an ID
	sequence.Carriers = []struct {
		ID int64 `json:"id"`
	}{carrier}

	// Add carrier contact information
	if truckList.Carrier.ContactEmail != "" {
		sequence.Emails = []struct {
			Email string `json:"email"`
		}{
			{
				Email: truckList.Carrier.ContactEmail,
			},
		}
	}

	// Add equipment information
	if truck.Type.Applied != "" {
		request.Equipment = &struct {
			Key   string `json:"key,omitempty"`
			Value string `json:"value,omitempty"`
		}{
			Key:   mapTruckTypeToTurvo(truck.Type.Applied).Key,
			Value: mapTruckTypeToTurvo(truck.Type.Applied).Value,
		}
	}

	// Add size information
	if truck.Length.Applied > 0 {
		request.Size = &struct {
			Key   string `json:"key,omitempty"`
			Value string `json:"value,omitempty"`
		}{
			Key:   FiftyThreeFTKey,
			Value: FiftyThreeFTValue,
		}
	}

	// Add mode information
	request.Mode = struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	}{
		Key:   TLModeKey,
		Value: TLModeValue,
	}

	// Add service type
	if AnyServiceKey != "" && AnyServiceValue != "" {
		request.ServiceType = &struct {
			Key   string `json:"key,omitempty"`
			Value string `json:"value,omitempty"`
		}{
			Key:   AnyServiceKey,
			Value: AnyServiceValue,
		}
	}

	// Add status
	if QuoteStatusKey != "" && QuoteStatusValue != "" {
		request.Status = &struct {
			Key   string `json:"key,omitempty"`
			Value string `json:"value,omitempty"`
		}{
			Key:   QuoteStatusKey,
			Value: QuoteStatusValue,
		}
	}

	// Add origin and destination locations by looking up TMS location by address
	tmsID := t.tms.ID

	// Get origin location
	pickupInfo := truck.PickupLocation.Applied
	if pickupInfo.City != "" && pickupInfo.State != "" {
		// First try exact match
		locations, err := tmslocation.GetTMSLocationsByTMSID(ctx, rds.GenericGetQuery{
			TMSID: tmsID,
		})
		if err == nil {
			// Filter locations by city and state
			var matches []models.TMSLocation
			for _, loc := range locations {
				if loc.City == pickupInfo.City && loc.State == pickupInfo.State {
					if loc.ExternalTMSID != "" {
						matches = append(matches, loc)
					}
				}
			}
			if len(matches) == 1 {
				if originID, err := strconv.Atoi(matches[0].ExternalTMSID); err == nil {
					request.OriginID = originID
				}
			} else if len(matches) > 1 {
				// If multiple matches, pick the first one and log a warning
				if originID, err := strconv.Atoi(matches[0].ExternalTMSID); err == nil {
					request.OriginID = originID
					log.Warn(ctx, "multiple origin locations found",
						zap.String("city", pickupInfo.City),
						zap.String("state", pickupInfo.State),
						zap.Int("originID", originID))
				}
			}
		}
		if request.OriginID == 0 {
			// Fallback to fuzzy match with a lower threshold
			pickupCoreInfo := models.CompanyCoreInfo{
				City:  pickupInfo.City,
				State: pickupInfo.State,
			}
			// Set a lower similarity threshold for fuzzy matching
			if err := rds.SetSimilarityThreshold(ctx, nil, 0.3); err != nil {
				log.Error(ctx, "failed to set similarity threshold", zap.Error(err))
			}
			if loc, err := tmslocation.GetLocationByAddress(
				ctx,
				tmsID,
				pickupCoreInfo,
			); err == nil && loc.ExternalTMSID != "" {
				if originID, err := strconv.Atoi(loc.ExternalTMSID); err == nil {
					request.OriginID = originID
					log.Info(ctx,
						"found origin location with ID",
						zap.Int("originID", originID),
						zap.String("city", pickupInfo.City),
						zap.String("state", pickupInfo.State))
				}
			} else {
				log.Error(ctx,
					"failed to find origin location",
					zap.Error(err),
					zap.String("city", pickupInfo.City),
					zap.String("state", pickupInfo.State))
			}
		}
	}

	// Get destination location
	dropoffInfo := truck.DropoffLocation.Applied
	if dropoffInfo.City != "" && dropoffInfo.State != "" {
		// First try exact match
		locations, err := tmslocation.GetTMSLocationsByTMSID(ctx, rds.GenericGetQuery{
			TMSID: tmsID,
		})
		if err == nil {
			// Filter locations by city and state
			var matches []models.TMSLocation
			for _, loc := range locations {
				if loc.City == dropoffInfo.City && loc.State == dropoffInfo.State {
					if loc.ExternalTMSID != "" {
						matches = append(matches, loc)
					}
				}
			}
			if len(matches) == 1 {
				if destID, err := strconv.Atoi(matches[0].ExternalTMSID); err == nil {
					request.DestinationID = destID
					log.Info(ctx, "found unique destination location with ID",
						zap.Int("destinationID", destID),
						zap.String("city", dropoffInfo.City),
						zap.String("state", dropoffInfo.State))
				}
			} else if len(matches) > 1 {
				// If multiple matches, pick the first one and log a warning
				if destID, err := strconv.Atoi(matches[0].ExternalTMSID); err == nil {
					request.DestinationID = destID
					log.Warn(ctx, "multiple destination locations found",
						zap.String("city", dropoffInfo.City),
						zap.String("state", dropoffInfo.State),
						zap.Int("destinationID", destID))
				}
			}
		}
		if request.DestinationID == 0 {
			// Fallback to fuzzy match with a lower threshold
			dropoffCoreInfo := models.CompanyCoreInfo{
				City:  dropoffInfo.City,
				State: dropoffInfo.State,
			}
			// Set a lower similarity threshold for fuzzy matching
			if err := rds.SetSimilarityThreshold(ctx, nil, 0.3); err != nil {
				log.Error(ctx, "failed to set similarity threshold", zap.Error(err))
			}
			if loc, err := tmslocation.GetLocationByAddress(
				ctx,
				tmsID,
				dropoffCoreInfo,
			); err == nil && loc.ExternalTMSID != "" {
				if destID, err := strconv.Atoi(loc.ExternalTMSID); err == nil {
					request.DestinationID = destID
					log.Info(ctx, "found destination location with ID",
						zap.Int("destinationID", destID),
						zap.String("city", dropoffInfo.City),
						zap.String("state", dropoffInfo.State))
				}
			} else {
				log.Error(ctx, "failed to find destination location",
					zap.Error(err),
					zap.String("city", dropoffInfo.City),
					zap.String("state", dropoffInfo.State))
			}
		}
	}

	request.RoutingSequences = append(request.RoutingSequences, sequence)

	return request
}

// mapTruckTypeToTurvo maps our truck types to Turvo's equipment types
func mapTruckTypeToTurvo(truckType models.TruckType) KeyValuePair {
	switch truckType {
	case models.VanTruckType:
		return KeyValuePair{
			Key:   VanKey,
			Value: VanValue,
		}
	case models.ReeferTruckType:
		return KeyValuePair{
			Key:   ReeferKey,
			Value: ReeferValue,
		}
	case models.FlatbedTruckType:
		return KeyValuePair{
			Key:   FlatbedKey,
			Value: FlatbedValue,
		}
	default:
		// Default to van if type is unknown
		return KeyValuePair{
			Key:   VanKey,
			Value: VanValue,
		}
	}
}
