package tmsutil

import "github.com/drumkitai/drumkit/common/models"

// ApplyTMSFeatureFlags applies service or group-level restrictions to load attributes
// e.g. TMS may support this writing to this field, but service wants to restrict it from users
func ApplyTMSFeatureFlags(tmsObj *models.Integration, attrs *models.LoadAttributes) {
	if tmsObj.FeatureFlags.IsCarrierAssignmentDisabled {
		// NOTE: Don't re-assign entire models.FieldAttributes{} because we want to preserve
		// the original values for other fields
		attrs.Carrier.ExternalTMSID.IsReadOnly = true
		attrs.Carrier.MovementID.IsReadOnly = true
		attrs.Carrier.Name.IsReadOnly = true
		attrs.Carrier.DOTNumber.IsReadOnly = true
		attrs.Carrier.SCAC.IsReadOnly = true
		attrs.Carrier.MCNumber.IsReadOnly = true

	}
}
