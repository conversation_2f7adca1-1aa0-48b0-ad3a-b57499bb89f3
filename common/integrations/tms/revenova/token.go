package revenova

import (
	"context"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

// RefreshToken refreshes the Revenova session token
func (r *<PERSON>eno<PERSON>) RefreshToken(ctx context.Context) error {
	ctx, metaSpan := otel.StartSpan(ctx, "RefreshTokenRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "refreshing Revenova session token")

	// Decrypt the password
	password, err := crypto.DecryptAESGCM(ctx, string(r.tms.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt password: %w", err)
	}

	// Get a new session token with actual expiration time from Salesforce
	newToken, expiresIn, err := r.GetToken(ctx, r.tms.Username, password, r.tms.AppID)
	if err != nil {
		return fmt.Errorf("failed to get new session token: %w", err)
	}

	// Update the token in the database using actual expiration from Salesforce API
	r.tms.AccessToken = newToken
	r.tms.AccessTokenExpirationDate = models.NullTime{
		Time:  time.Now().Add(time.Duration(expiresIn) * time.Second), // Use actual Salesforce expiration
		Valid: true,
	}

	err = integrationDB.Update(ctx, &r.tms)
	if err != nil {
		return fmt.Errorf("failed to update token in database: %w", err)
	}

	log.Info(ctx, "successfully refreshed Revenova session token")
	return nil
}

// ensureValidToken checks if the token is valid and refreshes it if needed
func (r *Revenova) ensureValidToken(ctx context.Context) error {
	// Check if token needs refresh
	if r.tms.NeedsRefresh() {
		log.Info(ctx, "token needs refresh, refreshing now")
		if err := r.RefreshToken(ctx); err != nil {
			return fmt.Errorf("failed to refresh token: %w", err)
		}
	}
	return nil
}
