package revenova

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/models"
)

func (r Revenova) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	request models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	token, expiresIn, err := r.GetToken(
		ctx,
		request.Username,
		request.Password, // Include security token
		request.AppID,    // 15-digit Organization ID
	)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	tenant := strings.Replace(request.Tenant, "https://", "", 1)

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, request.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	return models.OnboardTMSResponse{
		Username:          request.Username,
		EncryptedPassword: encryptedPassword,
		AccessToken:       token,
		// Use actual Salesforce expiration
		AccessTokenExpirationDate: time.Now().Add(time.Duration(expiresIn) * time.Second),
		Tenant:                    tenant, // Domain URL
		AppID:                     request.AppID,
	}, nil
}

func (r Revenova) GetToken(
	ctx context.Context,
	username,
	password,
	orgID string,
) (sessionToken string, expiresInSeconds int, err error) {
	// Extract login host from tenant URL
	// For sandbox: use the sandbox domain (e.g., "coolerlogisticsllc--full.sandbox.my.salesforce.com")
	// For production: use "login.salesforce.com"
	loginHost := ""
	tenantURL := r.tms.Tenant

	// Remove protocol if present
	tenantURL = strings.TrimPrefix(tenantURL, "https://")
	tenantURL = strings.TrimPrefix(tenantURL, "http://")

	// Check if it's a sandbox URL (contains "sandbox")
	switch {
	case strings.Contains(tenantURL, "sandbox"):
		// Use the sandbox domain for login
		loginHost = tenantURL
	case env.Vars.AppEnv == "prod":
		loginHost = "login.salesforce.com"
	default:
		// Default to test.salesforce.com for non-sandbox dev environments
		loginHost = "test.salesforce.com"
	}

	//nolint:lll
	postBody := fmt.Sprintf(`<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:urn="urn:enterprise.soap.sforce.com">
		<soapenv:Header>
			<urn:LoginScopeHeader>
				<urn:organizationId>%s</urn:organizationId>
			</urn:LoginScopeHeader>
		</soapenv:Header>
		<soapenv:Body>
			<urn:login>
				<urn:username>%s</urn:username>
				<urn:password>%s</urn:password>
			</urn:login>
		</soapenv:Body>
	</soapenv:Envelope>`, orgID, username, password)

	var token TokenResp
	err = r.LoginWithSoap(ctx, loginHost, "services/Soap/c/42.0", postBody, &token)
	if err != nil {
		return "", 0, fmt.Errorf("failed to login with SOAP: %w", err)
	}

	// Return both the session token and the actual expiration time from Salesforce
	sessionToken = token.Body.LoginResponse.Result.SessionID
	expiresInSeconds = token.Body.LoginResponse.Result.UserInfo.SessionSecondsValid

	return sessionToken, expiresInSeconds, nil
}
