package revenova

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type Revenova struct {
	tms models.Integration
}

var tmsHost string

func New(ctx context.Context, tms models.Integration) *Revenova {
	tmsHost = tms.Tenant
	log.With(ctx, zap.Uint("axleTMSID", tms.ID),
		zap.String("tmsName", "revenova"), zap.String("host", tmsHost))
	return &Revenova{tms: tms}
}

func (r <PERSON>enova) GetLoadsByIDType(
	ctx context.Context,
	id string,
	_ string,
) (loads []models.Load, attr models.LoadAttributes, _ error) {
	// For now, just call GetLoad for any ID type
	load, attrs, err := r.GetLoad(ctx, id)
	if err != nil {
		return nil, attrs, err
	}
	return []models.Load{load}, attrs, nil
}

func (r Revenova) GetCheckCallsHistory(context.Context, uint, string) (checkCalls []models.CheckCall, _ error) {
	return checkCalls, helpers.NotImplemented(models.Revenova, "GetCheckCallsHistory")
}

// GetTestLoads returns a map of test load IDs for Revenova
// IMPORTANT: Only use these loads for testing to avoid modifying real customer data
func (r Revenova) GetTestLoads() map[string]bool {
	return map[string]bool{
		// Add actual test load IDs from Revenova sandbox
		// Example: "a0i0x0000014APkAAM": true,
	}
}

func (r Revenova) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return helpers.NotImplemented(models.Revenova, "PostCheckCall")
}

func (r Revenova) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.Revenova, "GetCarriers")
}

// MapTransportTypeEnum maps Revenova transport type to Drumkit transport type
func (r Revenova) MapTransportTypeEnum(tmsTransportType string) (models.TransportType, error) {
	// Map Revenova transport types to Drumkit types
	switch tmsTransportType {
	case "Van", "Dry Van", "DryVan":
		return models.VanTransportType, nil
	case "Flatbed", "Flat Bed":
		return models.FlatbedTransportType, nil
	case "Reefer", "Refrigerated":
		return models.ReeferTransportType, nil
	case "Conestoga":
		return models.FlatbedTransportType, nil
	case "Step Deck", "StepDeck":
		return models.FlatbedTransportType, nil
	case "Hotshot", "Hot Shot":
		return models.HotShotTransportType, nil
	case "Box Truck":
		return models.BoxTruckTransportType, nil
	case "Sprinter":
		return models.SprinterTransportType, nil
	case "Power Only":
		return models.SpecialTransportType, nil
	default:
		return models.SpecialTransportType, nil
	}
}

func (r Revenova) CreateQuote(
	context.Context,
	models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, helpers.NotImplemented(models.Revenova, "CreateQuote")
}

func (r Revenova) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.Revenova, "GetUsers")
}

func (r Revenova) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.Revenova, "PostException")
}

func (r Revenova) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, helpers.NotImplemented(models.Revenova, "GetExceptionHistory")
}

func (r Revenova) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.Revenova, "PostNote")
}

func (r Revenova) LoginWithSoap(ctx context.Context, host, path string, postBody, dst any) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "LoginWithSoapRevenova", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	reqURL := (&url.URL{
		Scheme: "https",
		Host:   host,
		Path:   path,
	}).String()

	// Convert postBody to XML string if it's a string, otherwise marshal it
	var bodyBytes []byte
	switch v := postBody.(type) {
	case string:
		bodyBytes = []byte(v)
	default:
		bodyBytes, err = xml.Marshal(v)
		if err != nil {
			return fmt.Errorf("failed to marshal POST %s request body: %w", reqURL, err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build POST %s request: %w", reqURL, err)
	}

	req.Header.Set("Content-Type", "text/xml")
	req.Header.Set("SOAPAction", "login")

	httpClient := otel.TracingHTTPClient()
	res, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return fmt.Errorf("failed to execute POST %s request: %w", reqURL, err)
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read POST %s response body: %w", reqURL, err)
	}

	if res.StatusCode != http.StatusOK {
		httplog.LogHTTPResponseCode(ctx, r.tms, res.StatusCode)
		return fmt.Errorf("POST %s request failed with status %d: %s", reqURL, res.StatusCode, string(resBody))
	}

	// Try to unmarshal as XML first, then fall back to JSON
	if err = xml.Unmarshal(resBody, dst); err != nil {
		return fmt.Errorf("failed to unmarshal POST %s response: %w", reqURL, err)
	}

	return nil
}

func (r *Revenova) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	dataType s3backup.DataType) error {

	// Ensure token is valid before making request
	if err := r.ensureValidToken(ctx); err != nil {
		return fmt.Errorf("failed to ensure valid token: %w", err)
	}

	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	authorization := fmt.Sprintf("Bearer %s", r.tms.AccessToken)
	body, resp, err := httputil.GetBytesWithToken(ctx, r.tms, addr, nil, &authorization, dataType)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (r *Revenova) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	dataType s3backup.DataType) (err error) {

	// Ensure token is valid before making request
	if err := r.ensureValidToken(ctx); err != nil {
		return fmt.Errorf("failed to ensure valid token: %w", err)
	}

	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}
	headerMap := make(map[string]string)
	authorization := fmt.Sprintf("Bearer %s", r.tms.AccessToken)
	body, resp, err := httputil.PostBytesWithToken(ctx, r.tms, addr, reqBody, headerMap, &authorization, dataType)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (r *Revenova) patch(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	_ s3backup.DataType) (err error) {

	// Ensure token is valid before making request
	if err := r.ensureValidToken(ctx); err != nil {
		return fmt.Errorf("failed to ensure valid token: %w", err)
	}

	// Use the refreshed token
	if authorization == nil {
		auth := fmt.Sprintf("Bearer %s", r.tms.AccessToken)
		authorization = &auth
	}

	// Marshal request body to JSON
	var reqBodyBytes []byte
	if reqBody != nil {
		reqBodyBytes, err = json.Marshal(reqBody)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	// Build URL
	addr := url.URL{Scheme: "https", Host: tmsHost, Path: path, RawQuery: queryParams.Encode()}

	// Create HTTP request with PATCH method
	req, err := http.NewRequestWithContext(ctx, http.MethodPatch, addr.String(), bytes.NewReader(reqBodyBytes))
	if err != nil {
		return fmt.Errorf("failed to create PATCH request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", *authorization)

	// Execute request
	httpClient := otel.TracingHTTPClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, r.tms, err)
		return fmt.Errorf("failed to execute PATCH request: %w", err)
	}
	defer resp.Body.Close()

	// Log response
	httplog.LogHTTPResponseCode(ctx, r.tms, resp.StatusCode)

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for HTTP errors
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return errtypes.NewHTTPResponseError(r.tms, req, resp, respBody)
	}

	// Unmarshal response
	if dst != nil {
		if err := json.Unmarshal(respBody, dst); err != nil {
			return fmt.Errorf("json unmarshal failed: %w", err)
		}
	}

	return nil
}
