package ascend

import (
	"context"
	"fmt"
	"net/url"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (a Ascend) GetLoadIDs(context.Context, models.SearchLoadsQuery) (
	[]string,
	error,
) {
	return nil, helpers.NotImplemented(models.Ascend, "GetLoads")
}

func (a Ascend) GetLoad(ctx context.Context, externalTMSLoadID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(a.tms),
		attribute.String("external_tms_load_id", externalTMSLoadID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadAscend", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs := a.GetDefaultLoadAttributes()

	endPoint := fmt.Sprintf("/api/loads/%s", externalTMSLoadID)
	queryParams := url.Values{}
	var loadData LoadResp
	err = a.get(ctx, endPoint, queryParams, &loadData, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, attrs, err
	}
	load := a.AscendLoadToLoad(ctx, loadData)
	if load.ExternalTMSID != externalTMSLoadID {
		return models.Load{}, attrs, fmt.Errorf("Ascend load %s not found", externalTMSLoadID)
	}
	return load, a.GetDefaultLoadAttributes(), nil

}

func (a Ascend) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	load *models.Load,
) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(a.tms), otel.LoadAttrs(*load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadAscend", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs := a.GetDefaultLoadAttributes()
	reqBody := a.LoadToAscendShipmentReq(ctx, *load)
	if err != nil {
		return models.Load{}, attrs, err
	}
	var response LoadResp

	path := fmt.Sprintf("/api/loads/%s", curLoad.FreightTrackingID)
	err = a.put(ctx, path, nil, reqBody, &response, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, attrs, fmt.Errorf("updating Load failed: %w", err)
	}
	result := a.AscendLoadToLoad(ctx, response)

	return result, attrs, nil
}
