package ascend

import (
	"context"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (a Ascend) authenticate(ctx context.Context, username, password string) (models.OnboardTMSResponse, error) {
	accessToken, err := a.getToken(ctx, username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("getToken failed: %w", err)
	}
	log.Info(ctx, "Successfully authenticated Ascend client")

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		AccessToken:       accessToken,
		EncryptedPassword: encryptedPassword,
		Username:          username,
	}, err
}

func (a Ascend) getToken(ctx context.Context, username, password string) (string, error) {
	reqBody := TokenRequestBody{
		Username: username,
		Password: password,
	}

	var result TokenResponse
	err := a.postNoAuth(ctx, "/api/user/login", nil, reqBody, &result, s3backup.TypeTokens)
	if err != nil {
		return "", fmt.Errorf("POST token failed: %w", err)
	}

	return result.Data.AuthToken, nil
}

func (a Ascend) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardAscend", otel.IntegrationAttrs(a.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing Ascend API credentials")
	}
	return a.authenticate(ctx, onboardRequest.Username, onboardRequest.Password)
}
