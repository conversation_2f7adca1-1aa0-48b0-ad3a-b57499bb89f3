package ascend

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	parser "github.com/ecarter202/godress"
	"go.uber.org/zap"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type TokenResponse struct {
	Status string `json:"status"`
	Data   struct {
		Username  string `json:"username"`
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
		AuthToken string `json:"authtoken"`
	} `json:"data"`
}

type TokenRequestBody struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoadResp struct {
	Status string `json:"status"`
	Data   struct {
		ID          int             `json:"id"`
		Type        string          `json:"type"`
		Status      string          `json:"status"`
		Created     json.RawMessage `json:"created"`
		Reference   string          `json:"reference"`
		TruckType   string          `json:"truckType"`
		TruckLength string          `json:"truckLength"`
		Temperature json.RawMessage `json:"temperature"`
		TempUnit    string          `json:"tempUnit"`
		Weight      json.Number     `json:"weight"`
		Commodity   string          `json:"commodity"`
		Notes       string          `json:"notes"`
		Scratch     string          `json:"scratch"`
		Pickups     []struct {
			Cargo         string   `json:"cargo"`
			Timestamp     int64    `json:"timestamp"`
			Date          string   `json:"date"`
			Time          string   `json:"time"`
			PrevDate      string   `json:"prevDate"`
			Note          string   `json:"note"`
			Reference     string   `json:"reference"`
			Order         int      `json:"order"`
			Directions    []string `json:"directions"`
			Name          string   `json:"name"`
			Address1      string   `json:"address1"`
			BlockAddress  string   `json:"block_address"`
			Address2      string   `json:"address2"`
			City          string   `json:"city"`
			State         string   `json:"state"`
			Zip           string   `json:"zip"`
			PhoneExt      string   `json:"phoneExt"`
			Phone         string   `json:"phone"`
			Scratch       string   `json:"scratch"`
			ContactName   string   `json:"contactName"`
			ContactPhone  string   `json:"contactPhone"`
			ContactExt    string   `json:"contactExt"`
			ContactFax    string   `json:"contactFax"`
			ContactEmail  string   `json:"contactEmail"`
			Type          string   `json:"type"`
			Owner         string   `json:"owner"`
			Created       int64    `json:"created"`
			ID            string   `json:"id"`
			Sample        bool     `json:"sample"`
			Tags          []string `json:"tags"`
			Updated       int64    `json:"updated"`
			PrevTimestamp int      `json:"prev_timestamp,omitempty"`
		} `json:"pickups"`
		Deliveries []struct {
			Cargo             string   `json:"cargo"`
			Timestamp         int64    `json:"timestamp"`
			Date              string   `json:"date"`
			Time              string   `json:"time"`
			PrevTimestamp     int64    `json:"prev_timestamp"`
			PrevDate          string   `json:"prevDate"`
			Note              string   `json:"note"`
			Reference         string   `json:"reference"`
			Order             int      `json:"order"`
			MilesFromLastTask string   `json:"milesFromLastTask"`
			Directions        []string `json:"directions"`
			Type              string   `json:"type"`
			Owner             string   `json:"owner"`
			ID                string   `json:"id"`
			Name              string   `json:"name"`
			Address1          string   `json:"address1"`
			BlockAddress      string   `json:"block_address"`
			Address2          string   `json:"address2"`
			City              string   `json:"city"`
			State             string   `json:"state"`
			Zip               string   `json:"zip"`
			Phone             string   `json:"phone"`
			PhoneExt          string   `json:"phoneExt"`
			Scratch           string   `json:"scratch"`
			ContactName       string   `json:"contactName"`
			ContactPhone      string   `json:"contactPhone"`
			ContactExt        string   `json:"contactExt"`
			ContactFax        string   `json:"contactFax"`
			ContactEmail      string   `json:"contactEmail"`
			Sample            bool     `json:"sample"`
			Created           int64    `json:"created"`
			Tags              []string `json:"tags"`
			Updated           int64    `json:"updated"`
		} `json:"deliveries"`
		Miles   float64 `json:"miles"`
		Charges []struct {
			Type        string  `json:"type"`
			Quantity    int     `json:"quantity"`
			Rate        float64 `json:"rate"`
			Total       float64 `json:"total"`
			Created     int     `json:"created"`
			ID          string  `json:"id"`
			ChargeType  string  `json:"charge_type"`
			CompanyName string  `json:"company_name"`
			Company     struct {
				Name         string   `json:"name"`
				Address1     string   `json:"address1"`
				BlockAddress string   `json:"block_address"`
				Address2     string   `json:"address2"`
				City         string   `json:"city"`
				State        string   `json:"state"`
				Zip          string   `json:"zip"`
				Phone        string   `json:"phone"`
				PhoneExt     string   `json:"phoneExt"`
				McNum        string   `json:"mcNum,omitempty"`
				UsdotNum     int      `json:"usdotNum,omitempty"`
				Scratch      string   `json:"scratch"`
				ContactName  string   `json:"contactName"`
				ContactPhone string   `json:"contactPhone"`
				ContactExt   string   `json:"contactExt"`
				ContactFax   string   `json:"contactFax"`
				ContactEmail string   `json:"contactEmail"`
				Type         string   `json:"type"`
				Owner        string   `json:"owner"`
				Created      int64    `json:"created"`
				Sample       bool     `json:"sample"`
				Updated      int64    `json:"updated"`
				ID           string   `json:"_id"`
				ID1          string   `json:"id"`
				Tags         []string `json:"tags,omitempty"`
			} `json:"company"`
			Updated int64 `json:"updated,omitempty"`
		} `json:"charges"`
		Sample       bool   `json:"sample"`
		TruckStatus  string `json:"truckStatus"`
		TemplateName string `json:"template_name"`
		Settings     struct {
			Currency        string `json:"currency"`
			CurrencyDisplay string `json:"currency_display"`
			Decimal         string `json:"decimal"`
			Thousand        string `json:"thousand"`
			Precision       string `json:"precision"`
			Symbol          string `json:"symbol"`
			CurrSymbol      string `json:"curr_symbol"`
			Format          string `json:"format"`
			Weight          string `json:"weight"`
			Distance        string `json:"distance"`
			Temperature     string `json:"temperature"`
		} `json:"settings"`
		WeightUnit string `json:"weightUnit"`
		Customer   struct {
			Name         string   `json:"name"`
			Address1     string   `json:"address1"`
			BlockAddress string   `json:"block_address"`
			Address2     string   `json:"address2"`
			City         string   `json:"city"`
			State        string   `json:"state"`
			Zip          string   `json:"zip"`
			PhoneExt     string   `json:"phoneExt"`
			Phone        string   `json:"phone"`
			Scratch      string   `json:"scratch"`
			ContactName  string   `json:"contactName"`
			ContactPhone string   `json:"contactPhone"`
			ContactExt   string   `json:"contactExt"`
			ContactFax   string   `json:"contactFax"`
			ContactEmail string   `json:"contactEmail"`
			Tags         []string `json:"tags"`
			Type         string   `json:"type"`
			Owner        string   `json:"owner"`
			Created      int64    `json:"created"`
			ID           string   `json:"id"`
			Sample       bool     `json:"sample"`
			Updated      int64    `json:"updated"`
			ID1          string   `json:"_id"`
		} `json:"customer"`
		Carrier struct {
			Name         string      `json:"name"`
			Address1     string      `json:"address1"`
			BlockAddress string      `json:"block_address"`
			Address2     string      `json:"address2"`
			City         string      `json:"city"`
			State        string      `json:"state"`
			Zip          string      `json:"zip"`
			Phone        string      `json:"phone"`
			PhoneExt     string      `json:"phoneExt"`
			McNum        string      `json:"mcNum"`
			UsdotNum     json.Number `json:"usdotNum"`
			Scratch      string      `json:"scratch"`
			ContactName  string      `json:"contactName"`
			ContactPhone string      `json:"contactPhone"`
			ContactExt   string      `json:"contactExt"`
			ContactFax   string      `json:"contactFax"`
			ContactEmail string      `json:"contactEmail"`
			Type         string      `json:"type"`
			Owner        string      `json:"owner"`
			Created      int64       `json:"created"`
			Sample       bool        `json:"sample"`
			Updated      int64       `json:"updated"`
			ID           string      `json:"_id"`
			ID1          string      `json:"id"`
		} `json:"carrier"`
	} `json:"data"`
}

func (a Ascend) AscendLoadToLoad(ctx context.Context, loadData LoadResp) (load models.Load) {
	toTitlecase := cases.Title(language.English)

	load.ServiceID = a.tms.ServiceID
	load.TMSID = a.tms.ID
	load.FreightTrackingID = strconv.Itoa(loadData.Data.ID)
	load.ExternalTMSID = strconv.Itoa(loadData.Data.ID)
	load.Status = loadData.Data.Status

	// parsing json.Number to float32, since that's how we store it internally
	weightFloat32, err := strconv.ParseFloat(loadData.Data.Weight.String(), 32)
	if err == nil {
		load.Specifications.TotalWeight = models.ValueUnit{Val: float32(weightFloat32), Unit: ""} // TODO
	}

	load.Specifications.TotalDistance = models.ValueUnit{Val: float32(loadData.Data.Miles), Unit: models.MilesUnit}

	// Specifications
	load.Specifications.TransportType = loadData.Data.TruckType
	transportEnum, err := a.MapTransportTypeEnum(loadData.Data.TruckType)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping transport type", zap.Error(err))
	} else {
		load.Specifications.TransportTypeEnum = &transportEnum
	}
	// carrier details
	load.Carrier.ExternalTMSID = loadData.Data.Carrier.ID
	load.Carrier.Name = loadData.Data.Carrier.Name
	load.Carrier.Phone = loadData.Data.Carrier.Phone
	load.Carrier.Email = loadData.Data.Carrier.ContactEmail
	load.Carrier.DispatchCity = loadData.Data.Carrier.City
	load.Carrier.DispatchState = loadData.Data.Carrier.State
	load.Carrier.MCNumber = loadData.Data.Carrier.McNum
	load.Carrier.DOTNumber = loadData.Data.Carrier.UsdotNum.String()

	// pickup details
	if loadData.Data.Pickups != nil {
		load.Pickup.ExternalTMSID = loadData.Data.Pickups[0].ID
		load.Pickup.Name = loadData.Data.Pickups[0].Name
		load.Pickup.Contact = loadData.Data.Pickups[0].ContactName
		load.Pickup.Email = loadData.Data.Pickups[0].ContactEmail
		load.Pickup.Phone = loadData.Data.Pickups[0].ContactPhone
		load.Pickup.AddressLine1 = loadData.Data.Pickups[0].BlockAddress

		newPickup := strings.ReplaceAll(load.Pickup.AddressLine1, "\n", ", ")
		parsedPickupAddress, err := parser.Parse(newPickup)
		if err == nil {
			// title-casing city name
			load.Pickup.City = toTitlecase.String(strings.ToLower(parsedPickupAddress.City))
			load.Pickup.State = parsedPickupAddress.State
			load.Pickup.Zipcode = parsedPickupAddress.PostalCode
		}

		load.Pickup.ApptNote = loadData.Data.Pickups[0].Note
		load.Pickup.RefNumber = loadData.Data.Pickups[0].Reference
		if loadData.Data.Deliveries != nil {
			dateString := fmt.Sprintf("%s %s", loadData.Data.Pickups[0].Date, loadData.Data.Pickups[0].Time)
			dateTimeFormat, err := time.Parse("01/02/2006 15:04", dateString)
			if err == nil {
				load.Pickup.ApptStartTime = models.NullTime{
					Time:  dateTimeFormat,
					Valid: true,
				}
			}
		}
	}

	// consignee/dropoff details
	if loadData.Data.Deliveries != nil {
		load.Consignee.ExternalTMSID = loadData.Data.Deliveries[0].ID
		load.Consignee.Name = loadData.Data.Deliveries[0].Name
		load.Consignee.Contact = loadData.Data.Deliveries[0].ContactName
		load.Consignee.Email = loadData.Data.Deliveries[0].ContactEmail
		load.Consignee.Phone = loadData.Data.Deliveries[0].ContactPhone
		load.Consignee.AddressLine1 = loadData.Data.Deliveries[0].BlockAddress

		newConsignee := strings.ReplaceAll(load.Consignee.AddressLine1, "\n", ", ")
		parsedConsigneeAddress, err := parser.Parse(newConsignee)
		if err == nil {
			// title-casing city name
			load.Consignee.City = toTitlecase.String(strings.ToLower(parsedConsigneeAddress.City))
			load.Consignee.State = parsedConsigneeAddress.State
			load.Consignee.Zipcode = parsedConsigneeAddress.PostalCode
		}

		load.Consignee.ApptNote = loadData.Data.Deliveries[0].Note
		load.Consignee.RefNumber = loadData.Data.Deliveries[0].Reference
		if loadData.Data.Deliveries != nil {
			dateString := fmt.Sprintf("%s %s", loadData.Data.Deliveries[0].Date, loadData.Data.Deliveries[0].Time)
			dateTimeFormat, err := time.Parse("01/02/2006 15:04", dateString)
			if err == nil {
				load.Consignee.ApptStartTime = models.NullTime{
					Time:  dateTimeFormat,
					Valid: true,
				}
			}
		}
	}

	// customer details
	load.Customer.ExternalTMSID = loadData.Data.Customer.ID
	load.Customer.RefNumber = loadData.Data.Reference
	// load.Customer.Name = loadData.Data.Customer.ContactName
	load.Customer.Name = loadData.Data.Customer.Name
	load.Customer.Email = loadData.Data.Customer.ContactEmail
	load.Customer.Phone = loadData.Data.Customer.ContactPhone
	// load.Customer.AddressLine1 = loadData.Data.Customer.Address1
	load.Customer.AddressLine1 = loadData.Data.Customer.BlockAddress
	// load.Customer.AddressLine2 = loadData.Data.Customer.Address2
	// load.Customer.City = loadData.Data.Customer.City
	// load.Customer.State = loadData.Data.Customer.State
	// load.Customer.Zipcode = loadData.Data.Customer.Zip
	load.Customer.Contact = loadData.Data.Customer.ContactName

	return load
}

func (a *Ascend) MapTransportTypeEnum(string) (models.TransportType, error) {
	return "", errtypes.NotImplemented(models.Ascend, "MapTransportTypeEnum")
}

func (a Ascend) LoadToAscendShipmentReq(ctx context.Context, load models.Load) (ascendLoad LoadResp) {
	dataID, err := strconv.Atoi(load.ExternalTMSID)
	if err != nil {
		log.Warn(ctx, "error in parsing externalTMSID")
	}
	ascendLoad.Data.ID = dataID
	ascendLoad.Data.Status = load.Status
	ascendLoad.Data.Weight = json.Number(fmt.Sprintf("%f", load.Specifications.TotalWeight.Val))
	ascendLoad.Data.WeightUnit = strings.ToLower(load.Specifications.TotalWeight.Unit)
	ascendLoad.Data.Miles = float64(load.Specifications.TotalDistance.Val)

	// carrier details
	ascendLoad.Data.Carrier.ID = load.Carrier.ExternalTMSID
	ascendLoad.Data.Carrier.Name = load.Carrier.Name
	ascendLoad.Data.Carrier.Phone = load.Carrier.Phone
	ascendLoad.Data.Carrier.ContactEmail = load.Carrier.Email
	ascendLoad.Data.Carrier.City = load.Carrier.DispatchCity
	ascendLoad.Data.Carrier.State = load.Carrier.DispatchState
	ascendLoad.Data.Carrier.McNum = load.Carrier.MCNumber
	ascendLoad.Data.Carrier.UsdotNum = json.Number(load.Carrier.DOTNumber)

	// pickup details
	// TODO : need to initialise pickups array
	ascendLoad.Data.Pickups[0].ID = load.Pickup.ExternalTMSID
	ascendLoad.Data.Pickups[0].ContactName = load.Pickup.Name
	ascendLoad.Data.Pickups[0].ContactEmail = load.Pickup.Email
	ascendLoad.Data.Pickups[0].Phone = load.Pickup.Phone
	ascendLoad.Data.Pickups[0].Address1 = load.Pickup.AddressLine1
	ascendLoad.Data.Pickups[0].Address2 = load.Pickup.AddressLine2
	ascendLoad.Data.Pickups[0].City = load.Pickup.City
	ascendLoad.Data.Pickups[0].State = load.Pickup.State
	ascendLoad.Data.Pickups[0].Zip = load.Pickup.Zipcode
	ascendLoad.Data.Pickups[0].Note = load.Pickup.ApptNote
	ascendLoad.Data.Pickups[0].Reference = load.Pickup.RefNumber
	if ascendLoad.Data.Deliveries != nil {
		dateString := fmt.Sprintf("%s %s", ascendLoad.Data.Deliveries[0].Date, ascendLoad.Data.Deliveries[0].Time)
		dateTimeFormat, err := time.Parse("01/02/2006 15:04", dateString)
		if err == nil {
			load.Pickup.ApptStartTime = models.NullTime{
				Time:  dateTimeFormat,
				Valid: true,
			}
		}
	}

	// customer details
	ascendLoad.Data.Customer.ID = load.Customer.ExternalTMSID
	ascendLoad.Data.Reference = load.Customer.RefNumber
	ascendLoad.Data.Customer.ContactName = load.Customer.Name
	ascendLoad.Data.Customer.ContactEmail = load.Customer.Email
	ascendLoad.Data.Customer.ContactPhone = load.Customer.Phone
	ascendLoad.Data.Customer.Address1 = load.Customer.AddressLine1
	ascendLoad.Data.Customer.Address2 = load.Customer.AddressLine2
	ascendLoad.Data.Customer.City = load.Customer.City
	ascendLoad.Data.Customer.State = load.Customer.State
	ascendLoad.Data.Customer.Zip = load.Customer.Zipcode

	return
}
