package ascend

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

var (
	DefaultLoadAttributes = models.LoadAttributes{
		// Internals
		Model:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
		ServiceID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		Service:   models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},

		ExternalTMSID:     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
		FreightTrackingID: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
		Commodities:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},

		LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
			Mode:             models.FieldAttributes{IsNotSupported: true},
			MoreThanTwoStops: models.FieldAttributes{IsNotSupported: true},
			Status:           models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
			PONums:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			Operator:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},

			RateData: models.InitUnsupportedRateData,

			Customer: models.CustomerAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				RefNumber:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			},
			BillTo: models.BillToAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				},
			},

			Pickup: models.PickupAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				ExternalTMSStopID:   models.FieldAttributes{IsNotSupported: true},
				ApptType:            models.FieldAttributes{IsNotSupported: true},
				ApptRequired:        models.FieldAttributes{IsNotSupported: true},
				BusinessHours:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RefNumber:           models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
				ReadyTime:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ApptStartTime:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				ApptEndTime:         models.FieldAttributes{IsNotSupported: true},
				ApptNote:            models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
			},
			Consignee: models.ConsigneeAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Name:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					City:          models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					State:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Country:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
					Contact:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Phone:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
					Email:         models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				},
				ExternalTMSStopID:   models.FieldAttributes{IsNotSupported: true},
				ApptType:            models.FieldAttributes{IsNotSupported: true},
				ApptRequired:        models.FieldAttributes{IsNotSupported: true},
				BusinessHours:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RefNumber:           models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
				MustDeliver:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ApptStartTime:       models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				ApptEndTime:         models.FieldAttributes{IsNotSupported: true},
				ApptNote:            models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
			},

			Carrier: models.CarrierAttributes{
				MCNumber:                 models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DOTNumber:                models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Name:                     models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Phone:                    models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				Dispatcher:               models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Notes:                    models.FieldAttributes{IsNotSupported: true},
				SealNumber:               models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				SCAC:                     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				FirstDriverName:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				FirstDriverPhone:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				SecondDriverName:         models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				SecondDriverPhone:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Email:                    models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DispatchCity:             models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				DispatchState:            models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				ExternalTMSID:            models.FieldAttributes{IsNotSupported: false, IsReadOnly: true},
				ExternalTMSTruckID:       models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ExternalTMSTrailerID:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				RateConfirmationSent:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				DispatchedTime:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				DispatchSource:           models.FieldAttributes{IsNotSupported: true},

				ExpectedPickupTime: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				PickupStart:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				PickupEnd:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},

				ExpectedDeliveryTime: models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				DeliveryStart:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				DeliveryEnd:          models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				SignedBy:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			},
			Specifications: models.SpecificationsAttributes{
				OrderType:           models.FieldAttributes{IsNotSupported: true},
				TotalInPalletCount:  models.FieldAttributes{IsNotSupported: true},
				TotalOutPalletCount: models.FieldAttributes{IsNotSupported: true},
				TotalPieces:         models.FieldAttributes{IsNotSupported: true},
				Commodities:         models.FieldAttributes{IsNotSupported: true},
				NumCommodities:      models.FieldAttributes{IsNotSupported: true},
				TotalWeight:         models.FieldAttributes{IsNotSupported: true},
				BillableWeight:      models.FieldAttributes{IsNotSupported: true},
				TotalDistance:       models.FieldAttributes{IsNotSupported: true},
				IsRefrigerated:      models.FieldAttributes{IsNotSupported: true},
				MinTempFahrenheit:   models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				MaxTempFahrenheit:   models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				LiftgatePickup:      models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				LiftgateDelivery:    models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				InsidePickup:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				InsideDelivery:      models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Tarps:               models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Oversized:           models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Hazmat:              models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Straps:              models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Permits:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Escorts:             models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Seal:                models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				CustomBonded:        models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
				Labor:               models.FieldAttributes{IsNotSupported: true, IsReadOnly: true},
			},
		},
	}
)

func (t Ascend) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&t.tms, &attrs)

	return attrs
}
