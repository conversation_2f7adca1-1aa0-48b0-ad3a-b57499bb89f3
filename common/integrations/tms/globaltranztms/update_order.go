package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func (c GlobalTranz) UpdateOrder(ctx context.Context, orderDetails OrderDetails) (OrderDetails, error) {

	var result OrderDetailsResponse

	reqBody, err := json.Marshal(orderDetails)
	if err != nil {
		return OrderDetails{}, err
	}

	err = c.doWithRetry(ctx, http.MethodPut, c.tmsHost, UpdateOrderPath, nil, bytes.NewBuffer(reqBody), &result)
	if err != nil {
		return OrderDetails{}, err
	}

	log.Info(ctx, "updated order details", zap.Any("result", result))

	return result.Model, nil
}
