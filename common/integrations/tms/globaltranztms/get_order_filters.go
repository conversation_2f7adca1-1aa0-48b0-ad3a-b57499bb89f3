package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

type (
	GetOrderFiltersRequest struct {
		FromDate         string         `json:"fromDate"`
		PageNumber       int            `json:"pageNumber"`
		PageSize         int            `json:"pageSize"`
		SearchFilter     []SearchFilter `json:"searchFilter"`
		ShipmentStatusID int            `json:"shipmentStatusFilterTypeId"`
		ToDate           string         `json:"toDate"`
	}

	SearchFilter struct {
		FilterValue string `json:"filterValue"`
		SearchField string `json:"searchField"`
	}

	GetOrderFiltersResponse struct {
		DidError      bool                `json:"didError"`
		ErrorMessages any                 `json:"errorMessages"`
		ItemsCount    int                 `json:"itemsCount"`
		Model         []SearchResultOrder `json:"model"`
		PageCount     int                 `json:"pageCount"`
		PageNumber    int                 `json:"pageNumber"`
		PageSize      int                 `json:"pageSize"`
	}

	SearchResultOrder struct {
		BillOfLading string `json:"billOfLading"`
	}
)

func (c GlobalTranz) GetOrderFilters(
	ctx context.Context,
	filterType,
	filterValue string,
) (GetOrderFiltersResponse, error) {

	payload := GetOrderFiltersRequest{
		FromDate:   time.Now().Format(time.RFC3339),
		PageNumber: 1,
		PageSize:   25,
		SearchFilter: []SearchFilter{
			{
				SearchField: getFilterQueryNameByType(filterType),
				FilterValue: filterValue,
			},
		},
		ShipmentStatusID: 10,
		ToDate:           time.Now().Format(time.RFC3339),
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		return GetOrderFiltersResponse{}, err
	}

	var result GetOrderFiltersResponse
	err = c.doWithRetry(ctx, http.MethodPost, c.tmsHost, GetOrderFilterPath, nil, bytes.NewBuffer(reqBody), &result)

	if err != nil {
		return GetOrderFiltersResponse{}, err
	}

	log.Info(ctx, fmt.Sprintf("found %d orders", result.ItemsCount), zap.Any("result", result))

	return result, nil
}

// getFilterQueryNameByType maps internal filter types to filter names to be used in URL queries
func getFilterQueryNameByType(filterType string) string {
	switch filterType {
	case "poNumber":
		return "poNumber"
	case "pickupNumber":
		return "puNumber"
	default:
		return "poNumber"
	}
}
