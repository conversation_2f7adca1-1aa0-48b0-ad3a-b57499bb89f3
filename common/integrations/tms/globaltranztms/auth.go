package globaltranztms

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/sha256"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type (
	AccessTokenIntegrationData struct {
		AccessToken               string
		AccessTokenExpirationDate models.NullTime
	}

	AuthConfig struct {
		HTTPClient http.Client // Custom HTTP client for GlobalTranz Auth
		ClientID   string      // Trifecta's GlobalTranz ID

		AuthorizeRedirectURI     string // URL required by login endpoint for further authorization
		RequestVerificationToken string // Non-sensitive session token required by login endpoint

		// PKCE Auth data
		CodeVerifier  string // Cryptographically random 32 byte key, generated by Drumkit
		CodeChallenge string // Base64 encoded SHA256 hash of CodeVerifier

		Scope string // Scopes requested by Drumkit: "openid" and "profile"

		IdentityApplication string // Cookie received after login, required for further authorization
		IdentitySession     string // Cookie received after login, required for further authorization

		Code string // Base64 encoded JWT token
	}

	TokenResponse struct {
		AccessToken string `json:"access_token"`
		IDToken     string `json:"id_token"`
		TokenType   string `json:"token_type"`
		ExpiresIn   int    `json:"expires_in"`
		Scope       string `json:"scope"`
	}
)

const tmsRedirectURI = "https://tms.globaltranz.com/callback/login"

func (gt *GlobalTranz) Authenticate(ctx context.Context, tenant string) (*AccessTokenIntegrationData, error) {
	authConfig := AuthConfig{
		Scope:    "openid profile",
		ClientID: tenant,
	}

	verifier, err := generateCodeVerifier()
	if err != nil {
		return nil, err
	}
	authConfig.CodeVerifier = verifier

	challenge := generateCodeChallenge(verifier)
	authConfig.CodeChallenge = challenge

	returnURL := fmt.Sprintf(
		//nolint:lll
		"/connect/authorize/callback?client_id=%s&redirect_uri=%s&response_type=code&scope=%s&code_challenge=%s&code_challenge_method=S256&response_mode=query",
		authConfig.ClientID,
		url.QueryEscape(tmsRedirectURI),
		url.QueryEscape("openid profile"),
		authConfig.CodeChallenge,
	)
	authConfig.AuthorizeRedirectURI = returnURL

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("failed to create cookie jar for GlobalTranz: %w", err)
	}

	httpClient := otel.TracingHTTPClient()
	httpTransport := &http.Transport{
		//nolint:gosec // Workaround for GlobalTranz Identity Server certificate issues
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		Proxy:                 http.ProxyFromEnvironment,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	httpClient.Transport = otelhttp.NewTransport(
		httpTransport,
		otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
	)

	httpClient.Jar = cookieJar
	// Preventing redirects since response headers contain required auth data
	httpClient.CheckRedirect = func(_ *http.Request, _ []*http.Request) error {
		return http.ErrUseLastResponse
	}
	authConfig.HTTPClient = *httpClient

	if err := gt.getIdentityServerLoginInfo(ctx, &authConfig); err != nil {
		return nil, fmt.Errorf("error getting initial login info from GlobalTranz: %w", err)
	}

	if err := gt.identityServerLogin(ctx, authConfig); err != nil {
		return nil, fmt.Errorf("error logging in to GlobalTranz: %w", err)
	}

	code, err := gt.identityServerAuthorization(ctx, authConfig)
	if err != nil || code == "" {
		if code == "" {
			err = errors.New("authorization code from GlobalTranz is empty")
		}

		return nil, fmt.Errorf("error getting authorization code from GlobalTranz: %w", err)
	}

	token, err := gt.identityServerExchangeCodeForToken(ctx, authConfig, code)
	if err != nil || token == nil || token.AccessToken == "" {
		if token == nil || token.AccessToken == "" {
			err = fmt.Errorf("invalid token received from GlobalTranz: %v", token)
		}

		return nil, fmt.Errorf("error exchanging code for token from GlobalTranz: %w", err)
	}

	return &AccessTokenIntegrationData{
		AccessToken: token.AccessToken,
		AccessTokenExpirationDate: models.NullTime{
			Time:  time.Now().Add(time.Duration(token.ExpiresIn) * time.Second),
			Valid: true,
		},
	}, nil
}

func (gt *GlobalTranz) getIdentityServerLoginInfo(ctx context.Context, authConfig *AuthConfig) error {
	urlParams := url.Values{}
	urlParams.Add("ReturnUrl", authConfig.AuthorizeRedirectURI)

	addr := url.URL{Scheme: "https", Host: gt.authHost, Path: "Account/Login", RawQuery: urlParams.Encode()}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		addr.String(),
		nil,
	)
	if err != nil {
		return err
	}

	resp, err := authConfig.HTTPClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading get login initial info response body: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return fmt.Errorf("error parsing get login initial info response document: %w", err)
	}

	verificationToken := doc.Find("input[name='__RequestVerificationToken']").AttrOr("value", "")
	if verificationToken == "" {
		return errors.New("error getting initial verification token")
	}

	authConfig.RequestVerificationToken = verificationToken

	return nil
}

func (gt *GlobalTranz) identityServerLogin(ctx context.Context, authConfig AuthConfig) error {
	urlParams := url.Values{}
	urlParams.Add("ReturnUrl", authConfig.AuthorizeRedirectURI)

	globalTranzIntegration := gt.GetIntegrationModel()
	password, err := crypto.DecryptAESGCM(ctx, string(globalTranzIntegration.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	formData := url.Values{}
	formData.Add("Username", globalTranzIntegration.Username)
	formData.Add("Password", password)
	formData.Add("ReturnUrl", authConfig.AuthorizeRedirectURI)
	formData.Add("__RequestVerificationToken", authConfig.RequestVerificationToken)
	formData.Add("button", "login")
	formData.Add("RememberLogin", "false")

	addr := url.URL{Scheme: "https", Host: gt.authHost, Path: "Account/Login", RawQuery: urlParams.Encode()}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		addr.String(),
		strings.NewReader(formData.Encode()),
	)
	if err != nil {
		return err
	}

	newReq := addLoginHeaders(req)

	resp, err := authConfig.HTTPClient.Do(newReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return nil
}

func (gt *GlobalTranz) identityServerAuthorization(ctx context.Context, authConfig AuthConfig) (string, error) {
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodGet,
		fmt.Sprintf("https://%s%s", gt.authHost, authConfig.AuthorizeRedirectURI),
		nil,
	)
	if err != nil {
		return "", err
	}

	newReq := addAuthorizationHeaders(req)

	resp, err := authConfig.HTTPClient.Do(newReq)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	loc, err := resp.Location()
	if err != nil {
		return "", err
	}

	locParams, err := url.ParseQuery(loc.RawQuery)
	if err != nil {
		return "", err
	}

	return locParams.Get("code"), nil
}

func (gt *GlobalTranz) identityServerExchangeCodeForToken(
	ctx context.Context,
	authConfig AuthConfig,
	code string,
) (*TokenResponse, error) {

	data := url.Values{}
	data.Set("code", code)
	data.Set("client_id", authConfig.ClientID)
	data.Set("redirect_uri", tmsRedirectURI)
	data.Set("code_verifier", authConfig.CodeVerifier)
	data.Set("grant_type", "authorization_code")

	addr := url.URL{Scheme: "https", Host: gt.authHost, Path: "connect/token"}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		addr.String(),
		strings.NewReader(data.Encode()),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	resp, err := authConfig.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var tokenResp *TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, err
	}

	return tokenResp, nil
}

func addLoginHeaders(req *http.Request) *http.Request {
	newReq := req.Clone(req.Context())

	newReq.Header.Add("Accept", "*/*")
	newReq.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	newReq.Header.Add("Accept-Encoding", "gzip, deflate, br, zstd")
	newReq.Header.Add("Accept-Language", "en-US,en;q=0.9")
	newReq.Header.Add("Cache-Control", "max-age=0")
	newReq.Header.Add("Connection", "keep-alive")
	newReq.Header.Add("Origin", "null")
	newReq.Header.Add("Sec-Fetch-Dest", "document")
	newReq.Header.Add("Sec-Fetch-Mode", "navigate")
	newReq.Header.Add("Sec-Fetch-Site", "same-origin")
	newReq.Header.Add("Sec-Fetch-User", "?1")
	newReq.Header.Add("Upgrade-Insecure-Requests", "1")
	//nolint:lll
	newReq.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36:")
	newReq.Header.Add("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"")
	newReq.Header.Add("sec-ch-ua-mobile", "?0")
	newReq.Header.Add("sec-ch-ua-platform", "\"Windows\"")

	return newReq
}

func addAuthorizationHeaders(req *http.Request) *http.Request {
	newReq := req.Clone(req.Context())

	newReq.Header.Add("Accept", "*/*")
	newReq.Header.Add("Accept-Encoding", "gzip, deflate, br, zstd")
	newReq.Header.Add("Accept-Language", "en-US,en;q=0.9")
	newReq.Header.Add("Cache-Control", "max-age=0")
	newReq.Header.Add("Connection", "keep-alive")
	newReq.Header.Add("Sec-Fetch-Dest", "document")
	newReq.Header.Add("Sec-Fetch-Mode", "navigate")
	newReq.Header.Add("Sec-Fetch-Site", "same-origin")
	newReq.Header.Add("Sec-Fetch-User", "?1")
	//nolint:lll
	newReq.Header.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36:")
	newReq.Header.Add("Upgrade-Insecure-Requests", "1")
	newReq.Header.Add("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"")
	newReq.Header.Add("sec-ch-ua-mobile", "?0")
	newReq.Header.Add("sec-ch-ua-platform", "\"Windows\"")

	return newReq
}

func generateCodeVerifier() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	return base64.RawURLEncoding.EncodeToString(bytes), nil
}

func generateCodeChallenge(verifier string) string {
	hash := sha256.Sum256([]byte(verifier))
	return base64.RawURLEncoding.EncodeToString(hash[:])
}

func (gt *GlobalTranz) RefreshToken(ctx context.Context) error {
	log.Info(ctx, "refreshing GlobalTranz client")

	tokenData, err := gt.Authenticate(ctx, gt.tms.Tenant)
	if err != nil {
		log.Error(ctx, "could not authenticate GlobalTranz client for token refresh", zap.Error(err))
		return fmt.Errorf("error authenticating globaltranz for token refresh: %w", err)
	}

	gt.accessToken = tokenData.AccessToken
	gt.tms.AccessToken = tokenData.AccessToken
	gt.tms.AccessTokenExpirationDate = tokenData.AccessTokenExpirationDate

	if errUpdate := integrationDB.Update(ctx, &gt.tms); errUpdate != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to update globaltranz token on integrations table",
			zap.String("username", gt.tms.Username),
		)
		return fmt.Errorf("integrations table update failed: %w", errUpdate)
	}

	log.Info(ctx, "successfully refreshed GlobalTranz token", zap.String("username", gt.tms.Username))

	return nil
}
