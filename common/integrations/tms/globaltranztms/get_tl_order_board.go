package globaltranztms

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	GetTlOrderBoardRequest struct {
		FromDate         string         `json:"fromDate"`
		PageNumber       int            `json:"pageNumber"`
		PageSize         int            `json:"pageSize"`
		SearchFilter     []SearchFilter `json:"searchFilter"`
		ShipmentStatusID int            `json:"shipmentStatusFilterTypeId"`
		ToDate           string         `json:"toDate"`
	}

	GetTlOrderBoardResponse struct {
		DidError      bool               `json:"didError"`
		ErrorMessages any                `json:"errorMessages"`
		ItemsCount    int                `json:"itemsCount"`
		Model         []TlOrderBoardItem `json:"model"`
		PageCount     int                `json:"pageCount"`
		PageNumber    int                `json:"pageNumber"`
		PageSize      int                `json:"pageSize"`
		Message       any                `json:"message"`
	}

	TlOrderBoardItem struct {
		BillOfLading                  string  `json:"billOfLading"`
		Weight                        float64 `json:"weight"`
		PickupTimeframe               string  `json:"pickupTimeframe"`
		OriginCity                    string  `json:"originCity"`
		OriginState                   string  `json:"originState"`
		OriginZip                     string  `json:"originZip"`
		Carrier                       string  `json:"carrier"`
		DeliveryTimeframe             string  `json:"deliveryTimeframe"`
		DestinationCity               string  `json:"destinationCity"`
		DestinationState              string  `json:"destinationState"`
		DestinationZip                string  `json:"destinationZip"`
		Customer                      string  `json:"customer"`
		ShipDate                      string  `json:"shipDate"`
		DeliveryDate                  string  `json:"deliveryDate"`
		EstimatedTimeOfArrival        string  `json:"estimatedTimeOfArrival"`
		BookedBy                      string  `json:"bookedBy"`
		EquipmentLength               string  `json:"equipmentLength"`
		EquipmentType                 int     `json:"equipmentType"`
		Shipper                       string  `json:"shipper"`
		Consignee                     string  `json:"consignee"`
		Miles                         float64 `json:"miles"`
		DeliveryNumber                string  `json:"deliveryNumber"`
		DriverName                    string  `json:"driverName"`
		DriverPhone                   string  `json:"driverPhone"`
		FullPartialOrVolume           string  `json:"fullPartialOrVolume"`
		LastTrack                     *string `json:"lastTrack"`
		LockedBy                      *string `json:"lockedBy"`
		Margin                        float64 `json:"margin"`
		MarginPercentage              float64 `json:"marginPercentage"`
		MaxBuy                        float64 `json:"maxBuy"`
		CarrierMcNumber               string  `json:"carrierMcNumber"`
		CanGet                        bool    `json:"canGet"`
		CostPerMile                   float64 `json:"costPerMile"`
		Cost                          float64 `json:"cost"`
		PONumber                      string  `json:"poNumber"`
		ProNumber                     string  `json:"proNumber"`
		PUNumber                      string  `json:"puNumber"`
		QuoteNumber                   string  `json:"quoteNumber"`
		QuotedBy                      *string `json:"quotedBy"`
		ReferenceNumber               string  `json:"referenceNumber"`
		RevenuePerMile                float64 `json:"revenuePerMile"`
		BrokerAgent                   bool    `json:"brokerAgent"`
		BSUser                        int     `json:"bsUser"`
		Revenue                       float64 `json:"revenue"`
		SalesRep                      string  `json:"salesRep"`
		SpecialHandling               *string `json:"specialHandling"`
		Status                        int     `json:"status"`
		Stops                         int     `json:"stops"`
		AgeInMinutes                  int     `json:"ageInMinutes"`
		CreatedTime                   string  `json:"createdTime"`
		EDITendersStatus              *string `json:"ediTendersStatus"`
		IsUpdateTender                bool    `json:"isUpdateTender"`
		QuoteBK                       string  `json:"quoteBk"`
		IsPickupAppointmentRequired   bool    `json:"isPickupAppointmentRequired"`
		IsPickupAppointmentMade       bool    `json:"isPickupAppointmentMade"`
		IsDeliveryAppointmentRequired bool    `json:"isDeliveryAppointmentRequired"`
		IsDeliveryAppointmentMade     bool    `json:"isDeliveryAppointmentMade"`
		Problems                      []any   `json:"problems"`
		SchedulerAssistant            string  `json:"schedulerAssistant"`
		AccountExecutiveName          *string `json:"accountExecutiveName"`
		CarrierSalesRepName           *string `json:"carrierSalesRepName"`
		IsPodAvailable                bool    `json:"isPodAvailable"`
	}
)

const MaxOrderBoardRequestsPerMinute = 10

// GetTlOrderBoard fetches loads from the GlobalTranz TMS GetTlOrderBoard endpoint
func (gt GlobalTranz) GetTlOrderBoard(
	ctx context.Context,
	fromDate, toDate time.Time,
	pageNumber, pageSize int,
	statusFilter int,
) (GetTlOrderBoardResponse, error) {
	spanAttrs := append(otel.IntegrationAttrs(gt.tms),
		attribute.String("fromDate", fromDate.Format("2006-01-02")),
		attribute.String("toDate", toDate.Format("2006-01-02")))

	ctx, metaSpan := otel.StartSpan(ctx, "GetTlOrderBoardGlobalTranzTMS", spanAttrs)
	defer func() { metaSpan.End(nil) }()

	payload := GetTlOrderBoardRequest{
		FromDate:         fromDate.Format(time.RFC3339),
		ToDate:           toDate.Format(time.RFC3339),
		PageNumber:       pageNumber,
		PageSize:         pageSize,
		SearchFilter:     []SearchFilter{}, // Empty filter for all loads
		ShipmentStatusID: statusFilter,
	}

	reqBody, err := json.Marshal(payload)
	if err != nil {
		return GetTlOrderBoardResponse{}, fmt.Errorf("failed to marshal request body: %w", err)
	}

	var result GetTlOrderBoardResponse
	err = gt.doWithRetry(ctx, http.MethodPost, gt.tmsHost, GetOrderFilterPath, nil, bytes.NewBuffer(reqBody), &result)
	if err != nil {
		return GetTlOrderBoardResponse{}, fmt.Errorf("failed to fetch order board: %w", err)
	}

	log.Info(
		ctx,
		"fetched TL order board",
		zap.Int("itemsCount", result.ItemsCount),
		zap.Int("pageNumber", result.PageNumber),
		zap.Int("pageSize", result.PageSize),
		zap.Int("pageCount", result.PageCount),
	)

	return result, nil
}

func (gt GlobalTranz) GetAllTlOrderLoadIDs(
	ctx context.Context,
	fromDate, toDate time.Time,
	statusFilter int,
) ([]string, error) {
	loadIDs := make(map[string]struct{})

	loads, err := gt.GetAllTlOrderBoardPages(ctx, fromDate, toDate, statusFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get all TL order board pages: %w", err)
	}

	for _, load := range loads {
		loadIDs[load.BillOfLading] = struct{}{}
	}

	return mapKeysToSlice(loadIDs), nil
}

// GetAllTlOrderBoardPages fetches all pages from the GetTlOrderBoard endpoint for a date range
func (gt GlobalTranz) GetAllTlOrderBoardPages(
	ctx context.Context,
	fromDate, toDate time.Time,
	statusFilter int,
) ([]TlOrderBoardItem, error) {
	spanAttrs := append(otel.IntegrationAttrs(gt.tms),
		attribute.String("fromDate", fromDate.Format("2006-01-02")),
		attribute.String("toDate", toDate.Format("2006-01-02")))

	ctx, metaSpan := otel.StartSpan(ctx, "GetAllTlOrderBoardPagesGlobalTranzTMS", spanAttrs)
	defer func() { metaSpan.End(nil) }()

	var allItems []TlOrderBoardItem
	pageSize := 25
	pageNumber := 1

	for {
		response, err := gt.GetTlOrderBoard(ctx, fromDate, toDate, pageNumber, pageSize, statusFilter)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch page %d: %w", pageNumber, err)
		}

		allItems = append(allItems, response.Model...)

		// Break if we've fetched all pages
		if pageNumber >= response.PageCount || len(response.Model) == 0 {
			break
		}

		pageNumber++

		// Add a delay between pages to not slam the API
		if isContextPolling(ctx) {
			time.Sleep(time.Minute / MaxOrderBoardRequestsPerMinute)
		}
	}

	log.Info(
		ctx,
		"fetched all TL order board pages",
		zap.Int("totalItems", len(allItems)),
		zap.Int("totalPages", pageNumber-1),
		zap.Int("pageSize", pageSize),
	)

	return allItems, nil
}

// Helper method to map order board status to load status
func (gt GlobalTranz) mapOrderBoardStatusToLoadStatus(status int) (string, error) {
	// Use the existing orderStatusMap from load.go
	// This maps the status ID to a readable status string
	statusMap := map[int]string{
		0:  "New",
		1:  "Pending Quote",
		3:  "Quoted",
		4:  "Carrier Assigned",
		6:  "Rate Con Sent",
		7:  "In Transit",
		8:  "Delivered",
		11: "Canceled",
		17: "Completed",
		18: "Open",
		19: "At Shipper",
		20: "At Consignee",
		50: "Problem",
		51: "Customer Hold",
		60: "Dispatched",
	}

	if statusStr, exists := statusMap[status]; exists {
		return statusStr, nil
	}
	return "", fmt.Errorf("unknown status %d", status)
}

func (gt GlobalTranz) mapEquipmentTypeToTransportType(equipmentType int) models.TransportType {
	switch equipmentType {
	case 0:
		return models.VanTransportType
	case 26:
		return models.ReeferTransportType
	case 1:
		return models.FlatbedTransportType
	case 21:
		return models.HotShotTransportType
	default:
		return models.VanTransportType
	}
}

func (gt GlobalTranz) MapTransportTypeEnum(transportType string) (models.TransportType, error) {
	if transportType == "" {
		return "", nil
	}

	switch strings.ToLower(transportType) {
	case "van":
		return models.VanTransportType, nil
	case "flatbed":
		return models.FlatbedTransportType, nil
	case "reefer":
		return models.ReeferTransportType, nil
	case "hotshot", "hot-shot", "hs", "hot shot":
		return models.HotShotTransportType, nil
	case "box truck", "boxtruck", "box", "box-truck":
		return models.BoxTruckTransportType, nil
	case "sprinter":
		return models.SprinterTransportType, nil
	}

	// Fallback to equipment code if not found in the map
	equipmentCode, err := strconv.Atoi(transportType)
	if err != nil {
		return "", fmt.Errorf("error parsing transport type into int: %w", err)
	}
	return gt.mapEquipmentTypeToTransportType(equipmentCode), nil
}
