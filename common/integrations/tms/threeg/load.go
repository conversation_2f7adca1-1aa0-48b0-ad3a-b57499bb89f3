package threeg

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loaddb "github.com/drumkitai/drumkit/common/rds/load"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// GetLoad retrieves a load from 3G TMS by its ID
func (t *ThreeG) GetLoad(ctx context.Context, externalTMSID string) (models.Load, models.LoadAttributes, error) {
	url := fmt.Sprintf("%s/loads/?loadNum=%s", t.baseURL, externalTMSID)
	url = t.addAuthParams(url)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error reading response body: %w", err)
	}

	if resp.StatusCode == http.StatusNotFound {
		err := errtypes.EntityNotFoundError(t.integration, externalTMSID, "loadNum")
		return models.Load{}, models.LoadAttributes{}, err
	}

	if resp.StatusCode != http.StatusOK {
		err := errtypes.NewHTTPResponseError(t.integration, req, resp, body)
		return models.Load{}, models.LoadAttributes{}, err
	}

	var loadData LoadData
	if err := xml.Unmarshal(body, &loadData); err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error decoding response: %w", err)
	}

	if len(loadData.Loads.Load) == 0 {
		err := errtypes.EntityNotFoundError(t.integration, externalTMSID, "loadNum")
		return models.Load{}, models.LoadAttributes{}, err
	}

	load := loadData.Loads.Load[0]

	result := parseLoadFromResponse(load, t.integration)

	if err := loaddb.UpsertLoad(ctx, &result, &t.integration.Service); err != nil {
		log.Error(
			ctx,
			"Error upserting load in database",
			zap.String("load_id", result.ExternalTMSID),
			zap.Uint("service_id", t.integration.ServiceID),
			zap.Error(err),
		)
	}

	// Process orders if found
	if len(loadData.Orders.Order) > 0 {
		for _, orderData := range loadData.Orders.Order {
			// Create order model from load response data
			order := models.Order{
				OrderCoreInfo: models.OrderCoreInfo{
					ExternalLoadID:  load.LoadNum,
					ExternalOrderID: orderData.OrdNum,
					OrderTrackingID: orderData.OrdNum,
					Status:          orderData.OrderTMSStatus,
					Mode:            orderData.OrdType,
					FreightTerms:    orderData.FreightTerms,
					IsPrePayment:    orderData.IsPrePayment,
					Currency:        orderData.Currency,
					TotalWeight:     float64(parseFloat(orderData.TotalGrossWeight.WeightValue.Value)),
					TotalVolume:     float64(parseFloat(orderData.TotalGrossVolume.VolumeValue.Value)),
					PieceCount:      orderData.TotalPieceCount,
					IsHazmat:        orderData.IsHazmat,
					RateData: models.RateData{
						CustomerTotalCharge: models.ValueUnit{
							Val:  float32(parseFloat(orderData.TotalNetCharge.CurrencyValue.Value)),
							Unit: orderData.Currency,
						},
					},
					Specifications: models.Specifications{
						TotalWeight: models.ValueUnit{
							Val:  parseFloat(orderData.TotalGrossWeight.WeightValue.Value),
							Unit: orderData.TotalGrossWeight.WeightValue.UOM,
						},
					},
				},
				LoadID:                result.ID,
				ExternalLoadID:        load.LoadNum,
				RequestedPickupDate:   parseTime(orderData.ScheduledEarlyPickup).Time,
				RequestedDeliveryDate: parseTime(orderData.ScheduledLatePickup).Time,
				Reference:             orderData.OrdNum,
				ServiceID:             t.integration.ServiceID,
				Type:                  orderData.OrdType,
			}

			// Add destination contact if available
			if orderData.DestinationContact.ContactName != "" {
				order.Consignee = models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Contact: orderData.DestinationContact.ContactName,
						Phone:   orderData.DestinationContact.Phone1,
						Email:   orderData.DestinationContact.Email,
					},
				}
			}

			// Add comments if available
			if len(orderData.OrderComments.Comment) > 0 {
				notes := make([]models.Note, len(orderData.OrderComments.Comment))
				for i, comment := range orderData.OrderComments.Comment {
					notes[i] = models.Note{
						Note:      comment.CommentValue,
						Source:    comment.Qualifier.QualifierName,
						UpdatedBy: orderData.EntityHeader.LastModifiedBy,
					}
				}
				order.Notes = notes
			}

			// Add reference numbers if available
			if len(orderData.OrderRefNums.RefNum) > 0 {
				refNums := make([]string, len(orderData.OrderRefNums.RefNum))
				for i, refNum := range orderData.OrderRefNums.RefNum {
					refNums[i] = refNum.RefNumValue
				}
				order.Reference = strings.Join(refNums, ", ")
			}

			// Create the order in the database
			if err := orderdb.CreateOrderInDB(ctx, &order); err != nil {
				log.Error(
					ctx,
					"Error creating order in database",
					zap.String("order_id", order.ExternalOrderID),
					zap.Uint("load_id", result.ID),
					zap.Uint("service_id", t.integration.ServiceID),
					zap.Error(err),
				)
				continue
			}
		}
	}

	return result, t.GetDefaultLoadAttributes(), nil
}

// GetLoadsByIDType retrieves loads from 3G TMS by ID type (loadNum, proNum, bolNum)
func (t *ThreeG) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) ([]models.Load, models.LoadAttributes, error) {
	url := fmt.Sprintf("%s/loads/", t.baseURL)

	// Add the appropriate query parameter based on the ID type
	switch strings.ToLower(idType) {
	case "loadnum":
		load, loadAttributes, err := t.GetLoad(ctx, id)
		return []models.Load{load}, loadAttributes, err
	case "pronum", "pronumber":
		url = fmt.Sprintf("%s?proNum=%s", url, id)
	case "bolnum", "bolnumber":
		url = fmt.Sprintf("%s?bolNum=%s", url, id)
	default:
		return nil, models.LoadAttributes{}, fmt.Errorf("unsupported ID type: %s", idType)
	}

	url = t.addAuthParams(url)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, models.LoadAttributes{}, fmt.Errorf("error reading response body: %w", err)
		}
		return nil, models.LoadAttributes{}, fmt.Errorf(
			"error response from 3G TMS: %s - %s",
			resp.Status,
			string(body),
		)
	}

	// For simplicity, we'll assume a single load response
	// In a real implementation, you would handle multiple loads in the response
	var loadResponse LoadResponse
	if err := xml.NewDecoder(resp.Body).Decode(&loadResponse); err != nil {
		return nil, models.LoadAttributes{}, fmt.Errorf("error decoding response: %w", err)
	}

	// Convert the response to a load
	load, _, err := t.GetLoad(ctx, loadResponse.LoadID)
	if err != nil {
		return nil, models.LoadAttributes{}, err
	}

	return []models.Load{load}, t.GetDefaultLoadAttributes(), nil
}

// GetLoadIDs implements the TMS interface
func (t *ThreeG) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) ([]string, error) {
	// Ensure we have a valid session by logging in
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error during login: %w", err)
	}

	// Use the web base URL for the loadList endpoint
	webBaseURL := fmt.Sprintf("https://%s.3gtms.com", t.integration.Tenant)
	endpoint := fmt.Sprintf("%s/web/loadList", webBaseURL)

	// Create the query filter for date range
	dateFilter := map[string]any{
		"fieldName":      "createdDate",
		"fieldType":      "ZonedDateTime",
		"translatedName": "Created Date",
		"fieldNames":     nil,
		"fieldIds": []string{
			"fn1", "comparisonType1", "dateFieldOne1", "dateFieldTwo1",
			"dateFieldThree1", "dateFieldFour1", "dateFieldFive1Label",
			"dateFieldFive1", "dateFieldSix1Label", "dateFieldSix1",
		},
		"userDateFormat": "MM/dd/yyyy HH:mm",
		"values":         []string{"blank", "Relative", "gt", "Minutes", "-300"},
	}

	// If we have a from date, use it instead of the default filter
	if query.FromDate.Valid {

		now := time.Now()
		timeDiffInMinutes := int(-now.Sub(query.FromDate.Time).Minutes())

		dateFilter["values"] = []string{
			"blank",
			"Relative",
			"gt",
			"Minutes",
			fmt.Sprintf("%d", timeDiffInMinutes),
			"",
		}
	}

	// Convert the filter to JSON
	filterJSON, err := json.Marshal([]any{dateFilter})
	if err != nil {
		return nil, fmt.Errorf("error marshaling filter: %w", err)
	}

	// Create form data
	formData := url.Values{}
	formData.Set("filterscount", "0")
	formData.Set("groupscount", "0")
	formData.Set("pagenum", "0")
	formData.Set("pagesize", "500")
	formData.Set("recordstartindex", "0")
	formData.Set("recordendindex", "500")
	formData.Set("query", string(filterJSON))
	formData.Set("quicksearch", "")
	formData.Set("savedQueryId", "-1")

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Origin", webBaseURL)
	req.Header.Set("Referer", fmt.Sprintf("%s/web/clearLoads/", webBaseURL))

	// Make the request
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response from 3G TMS: %s - %s", resp.Status, string(body))
	}

	// Parse the response - it's a direct array of objects with loadNum
	var response []struct {
		LoadNum string `json:"loadNum"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("error decoding response: %w", err)
	}

	// Extract load IDs
	loadIDs := make([]string, 0, len(response))
	for _, record := range response {
		if record.LoadNum != "" {
			loadIDs = append(loadIDs, record.LoadNum)
		}
	}

	return loadIDs, nil
}

// CreateLoad implements the TMS interface
func (t *ThreeG) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (models.Load, error) {
	// Implementation would depend on 3G TMS API capabilities
	return models.Load{}, nil
}

// UpdateLoad implements the TMS interface
func (t *ThreeG) UpdateLoad(
	_ context.Context,
	_ *models.Load,
	_ *models.Load,
) (models.Load, models.LoadAttributes, error) {
	// Implementation would depend on 3G TMS API capabilities
	return models.Load{}, models.LoadAttributes{}, nil
}

// Helper function to parse time strings into models.NullTime
func parseTime(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{}
	}

	// Parse the time string based on the format used by 3G TMS
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return models.NullTime{}
	}

	return models.NullTime{
		Valid: true,
		Time:  t,
	}
}

// Helper function to parse float values
func parseFloat(s string) float32 {
	f, err := strconv.ParseFloat(s, 32)
	if err != nil {
		return 0
	}
	return float32(f)
}

// parseLoadFromResponse converts a 3G TMS load response to a models.Load
func parseLoadFromResponse(load Load, integration models.Integration) models.Load {
	result := models.Load{
		ExternalTMSID:     load.LoadNum,
		FreightTrackingID: load.LoadNum,
		ServiceID:         integration.ServiceID,
		TMSID:             integration.ID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:   load.LoadTMSStatus,
			Mode:     models.LoadMode(load.TransportMode),
			Operator: load.EntityHeader.CreatedBy,
			Carrier: models.Carrier{
				Name:          load.TradingPartnerCarrier.TradingPartnerName,
				MCNumber:      load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.MccNum,
				DOTNumber:     load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.InterstateCcID,
				SCAC:          load.TradingPartnerCarrier.TradingPartnerDetail.TradingPartnerCarrier.Scac,
				EquipmentName: load.AvailableEquipmentRatedName,
			},
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: load.OrganizationName,
				},
			},
			BillTo: models.BillTo{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: load.TradingPartnerClientName,
				},
			},
			Specifications: models.Specifications{
				TotalWeight: models.ValueUnit{
					Val:  parseFloat(load.TotalGrossWeight.WeightValue.Value),
					Unit: load.TotalGrossWeight.WeightValue.UOM,
				},
				TotalDistance: models.ValueUnit{
					Val:  parseFloat(load.TotalDistance.DistanceValue.Value),
					Unit: load.TotalDistance.DistanceValue.UOM,
				},
				TotalPieces: models.ValueUnit{
					Val: float32(load.TotalHandlingUnitCount),
					Unit: func() string {
						// First try to get from LoadHandlingUnits
						if len(load.LoadHandlingUnits.LoadHandlingUnit) > 0 {
							firstHU := load.LoadHandlingUnits.LoadHandlingUnit[0]
							if len(firstHU.HandlingUnit.HandlingUnitLines.HandlingUnitLine) > 0 {
								return firstHU.HandlingUnit.HandlingUnitLines.HandlingUnitLine[0].HandlingUnitTypeName
							}
						}

						// Fallback to LoadLines
						if len(load.LoadLines.LoadLine) > 0 {
							return load.LoadLines.LoadLine[0].HandlingUnitTypeNamePiece
						}

						return "pallets"
					}(),
				},
				TotalVolume: models.ValueUnit{
					Val:  parseFloat(load.TotalGrossVolume.VolumeValue.Value),
					Unit: load.TotalGrossVolume.VolumeValue.UOM,
				},
				TransportType: load.AvailableEquipmentRatedName,
			},
		},
	}

	// Map stops information if available
	if len(load.Stops.Stop) > 2 {
		result.MoreThanTwoStops = true

		stops := make([]models.Stop, len(load.Stops.Stop))
		for i, stop := range load.Stops.Stop {
			stops[i] = getStopFromResponseLocation(load, stop, i)
		}

		result.Stops = stops
		return result
	}

	pickup := load.Stops.Stop[0]
	result.Pickup = models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         pickup.StopLocInfo.AddrName,
			AddressLine1: pickup.StopLocInfo.Addr1,
			AddressLine2: pickup.StopLocInfo.Addr2,
			City:         pickup.StopLocInfo.CityName,
			State:        pickup.StopLocInfo.StateCode,
			Zipcode:      pickup.StopLocInfo.PostalCode,
			Country:      pickup.StopLocInfo.CountryISO2,
		},
	}

	// Pickup appointment fields mapped to carrier
	result.Carrier.ExpectedPickupTime = parseTime(pickup.ExpectedArrival)
	if pickup.ActualArrival != "" {
		result.Carrier.PickupStart = parseTime(pickup.ActualArrival)
	}
	if pickup.ActualDeparture != "" {
		result.Carrier.PickupEnd = parseTime(pickup.ActualDeparture)
	}
	if pickup.Appointment != "" {
		result.Pickup.ApptStartTime = parseTime(pickup.Appointment)
	}

	delivery := load.Stops.Stop[len(load.Stops.Stop)-1]
	result.Consignee = models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         delivery.StopLocInfo.AddrName,
			AddressLine1: delivery.StopLocInfo.Addr1,
			AddressLine2: delivery.StopLocInfo.Addr2,
			City:         delivery.StopLocInfo.CityName,
			State:        delivery.StopLocInfo.StateCode,
			Zipcode:      delivery.StopLocInfo.PostalCode,
			Country:      delivery.StopLocInfo.CountryISO2,
		},
	}

	// Delivery appointment fields mapped to carrier
	result.Carrier.ExpectedDeliveryTime = parseTime(delivery.ExpectedArrival)
	if delivery.ActualArrival != "" {
		result.Carrier.DeliveryStart = parseTime(delivery.ActualArrival)
	}
	if delivery.ActualDeparture != "" {
		result.Carrier.DeliveryEnd = parseTime(delivery.ActualDeparture)
	}
	if delivery.Appointment != "" {
		result.Consignee.ApptStartTime = parseTime(delivery.Appointment)
	}

	// Earliest Pickup is indexed by load rather than stop, so it'll be the same for both here
	if load.EarliestPickup != "" {
		result.Pickup.ReadyTime = parseTime(load.EarliestPickup)
		result.Consignee.MustDeliver = parseTime(load.EarliestPickup)
	}

	return result
}

func getStopFromResponseLocation(load Load, stop Stop, index int) models.Stop {
	address := models.Address{
		Name:         stop.StopLocInfo.AddrName,
		AddressLine1: stop.StopLocInfo.Addr1,
		AddressLine2: stop.StopLocInfo.Addr2,
		City:         stop.StopLocInfo.CityName,
		State:        stop.StopLocInfo.StateCode,
		Zip:          stop.StopLocInfo.PostalCode,
		Country:      stop.StopLocInfo.CountryISO2,
	}

	return models.Stop{
		StopType:          strings.ToLower(stop.StopType), // Pickup or Delivery
		StopNumber:        index,
		Address:           address,
		ExpectedStartTime: parseTime(stop.ExpectedArrival), // Expected Date in 3G TMS
		ReadyTime:         parseTime(load.EarliestPickup),  // Customer Requested Date in 3G TMS
		ActualStartTime:   parseTime(stop.ActualArrival),   // Actual Arrival in 3G TMS
		ActualEndTime:     parseTime(stop.ActualDeparture), // Actual Departure in 3G TMS
		ApptStartTime:     parseTime(stop.Appointment),     // Appointment Date in 3G TMS
	}
}
