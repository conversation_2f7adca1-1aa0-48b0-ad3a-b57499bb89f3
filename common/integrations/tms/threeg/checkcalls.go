package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// PostCheckCall implements the TMS interface
func (t *ThreeG) PostCheckCall(_ context.Context, _ *models.Load, _ models.CheckCall) error {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return a not implemented error
	return helpers.NotImplemented(models.ThreeG, "PostCheckCall")
}

// GetCheckCallsHistory implements the TMS interface
func (t *ThreeG) GetCheckCallsHistory(_ context.Context, _ uint, _ string) ([]models.CheckCall, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.CheckCall{}, helpers.NotImplemented(models.ThreeG, "GetCheckCallsHistory")
}
