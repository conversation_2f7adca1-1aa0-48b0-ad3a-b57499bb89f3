package threeg

import (
	"context"
	"crypto/tls"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// CreateQuote implements the TMS interface
func (t *ThreeG) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateQuoteThreeG", otel.IntegrationAttrs(t.integration))
	defer func() { metaSpan.End(err) }()

	pickupLocation, err := helpers.AwsLocationLookup(
		ctx,
		quoteBody.PickupLocationCity,
		quoteBody.PickupLocationState,
		quoteBody.PickupLocationZip,
	)
	if err != nil || pickupLocation == nil {
		log.Error(ctx, "error looking up pickup zipcode with AWS Location", zap.Error(err))
		return nil, err
	}

	pickupPlace := *pickupLocation.Results[0].Place

	deliveryLocation, err := helpers.AwsLocationLookup(
		ctx,
		quoteBody.DeliveryLocationCity,
		quoteBody.DeliveryLocationState,
		quoteBody.DeliveryLocationZip,
	)
	if err != nil || deliveryLocation == nil {
		log.Error(ctx, "error looking up delivery zipcode with AWS Location", zap.Error(err))
		return nil, err
	}
	deliveryPlace := *deliveryLocation.Results[0].Place

	quoteBody.PickupLocationCity = *pickupPlace.Municipality
	quoteBody.PickupLocationState = helpers.GetStateAbbreviation(ctx, *pickupPlace.Region)
	quoteBody.PickupLocationZip = *pickupPlace.PostalCode
	quoteBody.DeliveryLocationCity = *deliveryPlace.Municipality
	quoteBody.DeliveryLocationState = helpers.GetStateAbbreviation(ctx, *deliveryPlace.Region)
	quoteBody.DeliveryLocationZip = *deliveryPlace.PostalCode

	xmlPayload, err := t.buildQuoteXMLPayload(quoteBody)
	if err != nil {
		log.Error(ctx, "error building quote XML payload", zap.Error(err))
		return nil, fmt.Errorf("error building quote XML payload: %w", err)
	}

	quoteURL := t.addAuthParams(fmt.Sprintf("%s/quotes", t.baseURL))
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, quoteURL, strings.NewReader(xmlPayload))
	if err != nil {
		log.Error(ctx, "error creating quote request", zap.Error(err))
		return nil, fmt.Errorf("error creating quote request: %w", err)
	}

	req.Header.Set("Content-Type", "application/xml")

	httpClient := otel.TracingHTTPClient()
	httpTransport := &http.Transport{
		//nolint:gosec // Workaround for ThreeG Identity Server certificate issues
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		Proxy:                 http.ProxyFromEnvironment,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	httpClient.Transport = otelhttp.NewTransport(
		httpTransport,
		otelhttp.WithSpanNameFormatter(otel.CustomSpanNameFormatter),
	)

	resp, err := httpClient.Do(req)
	if err != nil {
		log.Error(ctx, "error making quote request", zap.Error(err))
		httplog.LogHTTPRequestFailed(ctx, t.integration, err)
		return nil, fmt.Errorf("error making quote request: %w", err)
	}

	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, t.integration, resp.StatusCode)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %w", err)
		}

		log.Error(
			ctx,
			"3G TMS quote creation failed",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)),
			zap.String("request_url", quoteURL),
			zap.String("xml_payload", xmlPayload),
		)

		return nil, fmt.Errorf("3G TMS quote creation failed with status %d: %s", resp.StatusCode, string(body))
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error(ctx, "error reading quote response", zap.Error(err))
		return nil, fmt.Errorf("error reading quote response: %w", err)
	}

	quoteID, err := t.parseQuoteResponse(string(responseBody))
	if err != nil {
		log.Error(ctx, "error parsing quote response", zap.Error(err))
		return nil, fmt.Errorf("error parsing quote response: %w", err)
	}

	return &models.CreateQuoteResponse{
		QuoteExternalID: quoteID,
	}, nil
}

// getOrganization returns the organization name based on the tenant
func (t *ThreeG) getOrganization() string {
	switch t.integration.Tenant {
	case "mohawk-sb":
		return "Mohawk Operations"
	default:
		return "" // Let 3G TMS use default organization
	}
}

// buildQuoteXMLPayload creates the XML payload for 3G TMS quote creation
func (t *ThreeG) buildQuoteXMLPayload(quoteBody models.CreateQuoteBody) (string, error) {
	batchDateTime := time.Now().UTC().Format("2006-01-02T15:04:05.000Z")

	weightStr := "1000"
	if quoteBody.CommodityWeight != "" {
		weightStr = quoteBody.CommodityWeight
	}

	// Assign quote price to variable since it's reused multiple times in XML Payload
	priceValueBase := CurrencyValueWithBase{
		CurrencyValue: ValueWithUOM{
			UOM:   "USD",
			Value: strconv.Itoa(quoteBody.QuotePrice),
		},
		CurrencyBase: ValueWithUOM{
			UOM:   "USD",
			Value: strconv.Itoa(quoteBody.QuotePrice),
		},
	}

	quoteData := QuoteData{
		XMLNS: "http://schemas.3gtms.com/tms/v1/tns",
		BatchInfo: QuoteBatchInfo{
			BatchDateTime: batchDateTime,
			SentBy:        t.username,
			PageNum:       "1",
			PageCnt:       "1",
		},
		Quotes: Quotes{
			Quote: Quote{
				OrganizationName: t.getOrganization(),
				QuoteNum:         "", // Let 3G TMS generate the quote number
				Client: TradingPartner{
					TradingPartnerName: quoteBody.CustomerID,
					TradingPartnerNum:  quoteBody.CustomerID,
				},
				OriginCityName:                   quoteBody.PickupLocationCity,
				OriginCountryISO2:                "US",
				OriginPostalCode:                 quoteBody.PickupLocationZip,
				OriginStateCode:                  quoteBody.PickupLocationState,
				OriginISOCountrySubdivision:      "US-" + quoteBody.PickupLocationState,
				DestinationCityName:              quoteBody.DeliveryLocationCity,
				DestinationCountryISO2:           "US",
				DestinationPostalCode:            quoteBody.DeliveryLocationZip,
				DestinationStateCode:             quoteBody.DeliveryLocationState,
				DestinationISOCountrySubdivision: "US-" + quoteBody.DeliveryLocationState,
				IsHazmat:                         "false",
				QuoteLines: QuoteLines{
					QuoteLine: QuoteLine{
						QuoteLineNum: "1",
						FreightClass: "50",
						WeightNet: WeightNet{
							WeightValue: ValueWithUOM{
								UOM:   "Lb",
								Value: weightStr,
							},
							WeightBase: ValueWithUOM{
								UOM:   "Lb",
								Value: weightStr,
							},
						},
						IsFreightClassLocked: "true",
					},
				},
				QuoteCharges: QuoteCharges{
					QuoteCharge: QuoteCharge{
						CostType:        "Freight",
						Charge:          priceValueBase,
						DiscountPercent: "0",
						Discount: CurrencyValueWithBase{
							CurrencyValue: ValueWithUOM{
								UOM:   "USD",
								Value: "0",
							},
							CurrencyBase: ValueWithUOM{
								UOM:   "USD",
								Value: "0",
							},
						},
						NetCharge:          priceValueBase,
						NetChargeOperating: priceValueBase,
						AccessorialQty:     "0",
					},
				},
			},
		},
	}

	// Filter out Unix epoch times (1970-01-01T00:00:00.000Z)
	unixEpoch := time.Unix(0, 0).UTC()

	isValidPickupDate := quoteBody.PickupDate.Valid &&
		!quoteBody.PickupDate.Time.IsZero() &&
		!quoteBody.PickupDate.Time.Equal(unixEpoch)

	if isValidPickupDate {
		quoteData.Quotes.Quote.PickupDate = quoteBody.PickupDate.Time.UTC().Format("2006-01-02T15:04:05Z")
	}

	isValidDeliveryDate := quoteBody.DeliveryDate.Valid &&
		!quoteBody.DeliveryDate.Time.IsZero() &&
		!quoteBody.DeliveryDate.Time.Equal(unixEpoch)

	if isValidDeliveryDate {
		quoteData.Quotes.Quote.DeliveryDate = quoteBody.DeliveryDate.Time.UTC().Format("2006-01-02T15:04:05Z")
	}

	output, err := xml.MarshalIndent(quoteData, "", "    ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal quote XML: %w", err)
	}

	return xml.Header + string(output), nil
}

// parseQuoteResponse extracts the quote number from 3G TMS response
func (t *ThreeG) parseQuoteResponse(responseBody string) (string, error) {
	var quoteResponse CreateQuoteResponse
	err := xml.Unmarshal([]byte(responseBody), &quoteResponse)
	if err != nil {
		return "", fmt.Errorf("failed to parse XML response: %w", err)
	}

	externalKey := quoteResponse.ImportQuotesResponse.ImportEntities.ImportEntity.ExternalKey
	if externalKey == "" {
		return "", errors.New("ExternalKey not found in 3G TMS response")
	}

	return externalKey, nil
}
