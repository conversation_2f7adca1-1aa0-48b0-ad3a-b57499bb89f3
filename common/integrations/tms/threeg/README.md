# 3G TMS Integration

This package implements the integration with 3G TMS.

## Authentication

3G TMS uses basic authentication with username and password parameters in the URL:

```
GET http://[3gtms.host]/integration/rest/orders?username=user&password=pass
```

## Onboarding

To onboard a new 3G TMS integration, you need to provide:

1. **Username**: Your 3G TMS username
2. **Password**: Your 3G TMS password (will be encrypted using AES-256 encryption)
3. **Tenant**: The hostname of your 3G TMS instance (e.g., "3gtms.host")

The onboarding process will validate your credentials by making a test API call to ensure the connection works. If successful, the password will be securely encrypted before being stored.

## Password Handling

The integration handles password encryption and decryption securely:

- During onboarding, the password is encrypted using AES-256 encryption before being stored
- When creating a new client instance, the encrypted password is automatically decrypted
- All API calls use the decrypted password for authentication

This ensures that passwords are never stored in plaintext while still allowing the integration to authenticate with the 3G TMS API.

## API Endpoints

### Loads

#### Get Load by ID

```
GET [3gtms.host]/integration/rest/loads/{loadId}?{includeDocs=[true/false]}&{detailed=[value]}
```

#### Get Load by Load Number, Pro Number, or BOL Number

```
GET [3gtms.host]/integration/rest/loads/?{loadNum=[value]}&{proNum=[value]}&{bolNum=[value]}&{detailed=[true|false]}&{includeDocs=[true|false]}
```

At least one of the following parameters is required:
- `loadNum`: 3Gtms Load Number
- `proNumber`: Load Pro Number
- `bolNumber`: Load BOL Number

## Implementation Status

- [x] Authentication
- [x] Initial Onboarding with Password Encryption
- [x] Password Decryption
- [x] Get Load by ID
- [x] Get Load by Load Number, Pro Number, or BOL Number
- [ ] Create Load
- [ ] Update Load
- [ ] Post Check Call
- [ ] Get Check Call History
- [ ] Post Exception
- [ ] Get Exception History
- [ ] Post Note
- [ ] Get Customers
- [ ] Get Users
- [ ] Get Locations
- [ ] Get Carriers
- [ ] Create Quote 