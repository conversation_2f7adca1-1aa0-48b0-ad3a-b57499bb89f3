package threeg

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

const pageSize = 500

func (t *ThreeG) GetCustomers(ctx context.Context) ([]models.TMSCustomer, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersThreeG", otel.IntegrationAttrs(t.integration))
	var err error
	defer func() { metaSpan.End(err) }()

	var customers []models.TMSCustomer
	pageNum := 0

	// First, check if we have a saved job state in redis
	_, cursor, err := redis.GetIntegrationState(ctx, t.integration.ID, redis.CustomerJob)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
	} else if cursor != "" {
		// Resume from last page if available
		if resumePage, err := strconv.Atoi(cursor); err == nil {
			pageNum = resumePage
			log.Info(ctx, "resuming 3G TMS customer fetch from page", zap.Int("page", pageNum))
		}
	}

	for {
		log.Info(
			ctx,
			"3G TMS fetching page",
			zap.Int("page_num", pageNum),
			zap.Int("page_size", pageSize),
		)

		partners, fetchErr := t.getTradingPartnerListPaginated(ctx, pageNum, pageSize)
		if fetchErr != nil {
			log.Error(
				ctx,
				"3G TMS page fetch failed",
				zap.Int("page_num", pageNum),
				zap.Error(fetchErr),
			)

			t.SetIntegrationStateWithWarning(ctx, redis.CustomerJob, "", strconv.Itoa(pageNum))
			err = fetchErr
			break
		}

		log.Info(
			ctx,
			"3G TMS page result",
			zap.Int("page_num", pageNum),
			zap.Int("partners_count", len(partners)),
			zap.Int("total_customers_so_far", len(customers)),
		)

		// If we get 0 records, we've reached the end
		if len(partners) == 0 {
			if pageNum == 0 {
				log.Warn(ctx, "Failed to fetch 3G TMS customers - empty response on first page")
			} else {
				log.Info(
					ctx,
					"3G TMS reached end - no more data",
					zap.Int("final_page", pageNum),
					zap.Int("total_customers", len(customers)),
				)
			}

			// Delete the redis key once we've successfully reached the end
			redisErr := redis.DeleteKey(ctx, fmt.Sprintf("integration-id-%d-%s", t.integration.ID, redis.CustomerJob))
			if redisErr != nil && !errors.Is(redisErr, redis.NilEntry) {
				log.Warn(ctx, "failed to delete redis key", zap.Error(redisErr))
			}
			break
		}

		// Map trading partners to TMSCustomer
		customersToRefresh := &[]models.TMSCustomer{}
		*customersToRefresh = make([]models.TMSCustomer, 0, len(partners))

		for _, partner := range partners {
			*customersToRefresh = append(*customersToRefresh, ToCustomerModel(t.integration.ID, partner))
		}

		// Store customers in database
		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, customersToRefresh); err != nil {
			t.SetIntegrationStateWithWarning(ctx, redis.CustomerJob, "", strconv.Itoa(pageNum))
			break
		}

		// Add to return slice
		customers = append(customers, *customersToRefresh...)

		// Save current progress and continue to next page
		if err := redis.SetIntegrationState(
			ctx,
			t.integration.ID,
			redis.CustomerJob,
			"",
			strconv.Itoa(pageNum),
		); err != nil {
			log.Warn(ctx, "failed to set integration state", zap.Error(err))
		}

		pageNum++
	}

	return customers, err
}

// getTradingPartnerListPaginated fetches trading partners from 3G TMS Web API with pagination
func (t *ThreeG) getTradingPartnerListPaginated(
	ctx context.Context,
	pageNum int,
	pageSize int,
) (
	[]WebTradingPartner,
	error,
) {
	if err := t.login(ctx); err != nil {
		return nil, fmt.Errorf("error authenticating with 3G TMS: %w", err)
	}

	endpoint := fmt.Sprintf("%s%s", t.webBaseURL, webTradingPartnerListPath)

	// Create the "Is Active" filter
	activeFilter := WebTradingPartnerRequestFilter{
		FieldName:      "isActive",
		FieldType:      "com.mysema.query.types.path.BooleanPath",
		TranslatedName: "Is Active",
		FieldNames:     nil,
		FieldIDs:       []string{"fn1", "comparisonType1"},
		UserDateFormat: "MM/dd/yyyy HH:mm",
		Values:         []string{"blank", "selected"},
	}

	// Create the "Trading Partner Type" filter to only get Clients
	tradingPartnerTypeFilter := WebTradingPartnerRequestFilter{
		FieldName: "dataValue_TradingPartnerType",
		FieldType: "com.mysema.query.types.path.EnumPath<com._3gtms.javatms.entitytype." +
			"enumeration.TradingPartnerType>",
		TranslatedName: "Trading Partner Type",
		FieldNames:     nil,
		FieldIDs:       []string{"fn4", "comparisonType4", "selections4"},
		DataValueGroup: "TradingPartner",
		UserDateFormat: "MM/dd/yyyy HH:mm",
		Values:         []string{"blank", "Client"},
	}

	// Convert filters to JSON - both active and trading partner type filters
	filters := []WebTradingPartnerRequestFilter{activeFilter, tradingPartnerTypeFilter}
	filterJSON, err := json.Marshal(filters)
	if err != nil {
		return nil, fmt.Errorf("error marshaling customer filters: %w", err)
	}

	// Calculate record indices based on page number and page size
	recordStartIndex := pageNum * pageSize
	recordEndIndex := recordStartIndex + pageSize - 1

	// Build form data with pagination parameters
	formData := url.Values{}
	formData.Set("filterscount", "2") // We have 2 filters (active + trading partner type)
	formData.Set("groupscount", "0")
	formData.Set("sortdatafield", "active")
	formData.Set("sortorder", "desc")
	formData.Set("pagenum", strconv.Itoa(pageNum))
	formData.Set("pagesize", strconv.Itoa(pageSize))
	formData.Set("recordstartindex", strconv.Itoa(recordStartIndex))
	formData.Set("recordendindex", strconv.Itoa(recordEndIndex))
	formData.Set("query", string(filterJSON))
	formData.Set("quicksearch", "")
	formData.Set("savedQueryId", "-1")

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("error creating trading partner list request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Referer", t.webBaseURL)

	log.Debug(
		ctx,
		"3G TMS API request",
		zap.String("endpoint", endpoint),
		zap.Int("page_num", pageNum),
		zap.Int("page_size", pageSize),
		zap.String("record_start", strconv.Itoa(recordStartIndex)),
		zap.String("record_end", strconv.Itoa(recordEndIndex)),
	)

	resp, err := t.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, t.integration, err)
		log.Error(
			ctx,
			"3G TMS API request failed",
			zap.Int("page_num", pageNum),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error making trading partner list request: %w", err)
	}
	defer resp.Body.Close()

	httplog.LogHTTPResponseCode(ctx, t.integration, resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("error reading response body: %w", err)
		}

		log.Error(
			ctx,
			"3G TMS trading partner list request failed",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)),
			zap.String("request_url", endpoint),
		)

		return nil, fmt.Errorf(
			"3G TMS trading partner list request failed with status %d: %s",
			resp.StatusCode,
			string(body),
		)
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading trading partner list response: %w", err)
	}

	log.Debug(
		ctx,
		"3G TMS API raw response",
		zap.Int("page_num", pageNum),
		zap.Int("response_length", len(responseBody)),
	)

	var partners []WebTradingPartner
	var wrappedResponse WebTradingPartnerListResponse

	// Try to unmarshal as a TradingPartnerListResponse first (wrapped in data field)
	if err := json.Unmarshal(responseBody, &wrappedResponse); err == nil && len(wrappedResponse.Data) > 0 {
		partners = wrappedResponse.Data
		log.Info(
			ctx,
			"3G TMS API response - wrapped format",
			zap.Int("page_num", pageNum),
			zap.Int("partners_count", len(partners)),
		)
	} else {
		// Fallback: try to unmarshal as a direct array
		if err := json.Unmarshal(responseBody, &partners); err != nil {
			log.Error(
				ctx,
				"3G TMS API response parsing failed",
				zap.Int("page_num", pageNum),
				zap.Error(err),
			)
			return nil, fmt.Errorf("error parsing trading partner list response: %w", err)
		}

		log.Info(
			ctx,
			"3G TMS API response - direct array format",
			zap.Int("page_num", pageNum),
			zap.Int("partners_count", len(partners)),
		)
	}

	return partners, nil
}

// ToCustomerModel converts WebTradingPartner to TMSCustomer
func ToCustomerModel(tmsID uint, partner WebTradingPartner) models.TMSCustomer {
	customer := models.TMSCustomer{
		TMSIntegrationID: tmsID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: partner.TradingPartnerNum, // Use TradingPartnerNum instead of ID
			Name:          partner.Name,
			AddressLine1:  partner.AddressLine1,
			AddressLine2:  partner.AddressLine2,
			City:          partner.City,
			State:         partner.State,
			Zipcode:       partner.ZipCode,
			Phone:         partner.Phone,
			Email:         partner.Email,
		},
	}

	return customer
}

// SetIntegrationStateWithWarning saves integration state to Redis with warning log on failure
func (t *ThreeG) SetIntegrationStateWithWarning(
	ctx context.Context,
	jobType string,
	updatedAt, cursor string,
) {
	if err := redis.SetIntegrationState(
		ctx,
		t.integration.ID,
		jobType,
		updatedAt,
		cursor,
	); err != nil {
		log.Warn(ctx, "failed to set integration state", zap.Error(err))
	}
}
