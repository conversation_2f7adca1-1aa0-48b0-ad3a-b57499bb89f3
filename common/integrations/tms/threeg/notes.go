package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// PostNote implements the TMS interface
func (t *ThreeG) PostNote(_ context.Context, _ *models.Load, _ models.Note) ([]models.Note, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return a not implemented error
	return nil, helpers.NotImplemented(models.ThreeG, "PostNote")
}
