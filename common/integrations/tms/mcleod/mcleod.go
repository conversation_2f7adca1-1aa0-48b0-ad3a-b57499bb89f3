package mcleod

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

const tspBase = "api.mcleodsoftware.com"

type Mcleod struct {
	tms models.Integration
}

func New(tms models.Integration) *Mcleod {
	return &Mcleod{tms: tms}
}

func (m *Mcleod) GetCustomers(context.Context) (result []models.TMSCustomer, _ error) {
	return result, helpers.NotImplemented(models.Mcle<PERSON>, "GetCustomers")
}
func (m *Mcleod) GetUsers(context.Context) (result []models.TMSUser, _ error) {
	return result, helpers.NotImplemented(models.Mcleod, "GetUsers")
}

func (m *Mcleod) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return helpers.NotImplemented(models.Mcle<PERSON>, "PostCheckCall")
}

func (m *Mcleod) GetLoadByIDType(
	context.Context,
	string,
	string,
) (load models.Load, attr models.LoadAttributes, _ error) {
	return load, attr, helpers.NotImplemented(models.Mcleod, "GetLoadByIDType")
}

func (m *Mcleod) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.Mcleod, "PostException")
}

func (m *Mcleod) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return nil, helpers.NotImplemented(models.Mcleod, "GetExceptionHistory")
}

func (m *Mcleod) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.Mcleod, "PostNote")
}

func (m *Mcleod) GetTestLoads() map[string]bool {
	return nil
}
func (m *Mcleod) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.Mcleod, "GetCarriers")
}

func (m *Mcleod) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, helpers.NotImplemented(models.Mcleod, "GetLocations")
}

func (m *Mcleod) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	s3Type s3backup.DataType) (err error) {
	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Authorization"] = fmt.Sprintf("Bearer %s", m.tms.AccessToken)
	headerMap["Content-Type"] = "application/json"
	headerMap["X-Api-Key"] = m.tms.APIKey
	headerMap["X-Mcld-Tenant"] = m.tms.Tenant

	addr := url.URL{Scheme: "https", Host: tspBase, Path: path, RawQuery: queryParams.Encode()}
	//nolint:bodyclose // False positive
	body, _, err := httputil.GetBytesWithToken(ctx, m.tms, addr, headerMap, nil, s3Type)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return
}

func (m *Mcleod) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	s3Type s3backup.DataType) (err error) {
	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Authorization"] = fmt.Sprintf("Bearer %s", m.tms.AccessToken)
	headerMap["Content-Type"] = "application/json"
	headerMap["X-Api-Key"] = m.tms.APIKey
	headerMap["X-Mcld-Tenant"] = m.tms.Tenant

	addr := url.URL{Scheme: "https", Host: tspBase, Path: path, RawQuery: queryParams.Encode()}
	//nolint:bodyclose // False positive
	body, _, err := httputil.PostBytesWithToken(ctx, m.tms, addr, reqBody, headerMap, authorization, s3Type)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return
}

func (m *Mcleod) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	s3Type s3backup.DataType) (err error) {
	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Authorization"] = fmt.Sprintf("Bearer %s", m.tms.AccessToken)
	headerMap["Content-Type"] = "application/json"
	headerMap["X-Api-Key"] = m.tms.APIKey
	headerMap["X-Mcld-Tenant"] = m.tms.Tenant

	addr := url.URL{Scheme: "https", Host: tspBase, Path: path, RawQuery: queryParams.Encode()}
	//nolint:bodyclose // False positive
	body, _, err := httputil.PutBytesWithToken(ctx, m.tms, addr, reqBody, headerMap, nil, s3Type)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return
}
