package mcleod

import (
	"context"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *Mcleod) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaspan := otel.StartSpan(ctx, "CreateQuoteMcleod", otel.IntegrationAttrs(m.tms))
	defer func() { metaspan.End(err) }()

	quoteReq := m.buildCreateQuoteBody(ctx, quoteBody)
	var quoteResp QuoteResp
	err = m.post(ctx, "/quick-quotes", nil, quoteReq, &quoteResp, nil, s3backup.TypeQuotes)
	if err != nil {
		return nil, err
	}

	// TODO: Remove in favor of QuoteExternalID as string; kept for FE backwards compatibility
	if quoteID, parseErr := strconv.Atoi(quoteResp.ID); parseErr == nil {
		quoteResponse.QuoteID = quoteID
	}

	quoteResponse.QuoteExternalID = quoteResp.ID
	return
}

func (m *Mcleod) buildCreateQuoteBody(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteRequest QuoteRequest) {
	var equipmentType string
	switch quoteBody.TransportType {
	case models.VanTransportType:
		equipmentType = "Van"
	case models.ReeferTransportType:
		equipmentType = "Reefer"
	case models.FlatbedTransportType:
		equipmentType = "Flatbed"
	default:
		log.WarnNoSentry(ctx,
			"couldn't match transport type while creating mcleod quote",
			zap.String("transport_type", string(quoteBody.TransportType)),
		)
	}

	quoteRequest.Consignee.PostalCode = quoteBody.DeliveryLocationZip
	quoteRequest.Shipper.PostalCode = quoteBody.PickupLocationZip

	quoteRequest.RequiredEquipment.EquipmentType = equipmentType
	quoteRequest.ProposedRate.Value = quoteBody.QuotePrice

	return quoteRequest
}
