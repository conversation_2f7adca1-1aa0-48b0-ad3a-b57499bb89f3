package mcleod

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/api/env"
)

func (m *Mcleod) Authenticate(
	ctx context.Context,
	clientID, clientSecret, redirectURI, accessCode string,
) (TokenResp, error) {
	reqBody := map[string]string{
		"grant_type":    "authorization_code",
		"client_id":     clientID,
		"client_secret": clientSecret,
		"redirect_uri":  redirectURI,
		"code":          accessCode,
	}

	var tokenResp TokenResp
	body, err := json.Marshal(reqBody)
	if err != nil {
		return tokenResp, err
	}
	client := otel.TracingHTTPClient()
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		"https://auth.mcleodsoftware.com/OAuth/Token",
		bytes.NewReader(body),
	)
	if err != nil {
		return tokenResp, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return tokenResp, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return tokenResp, err
	}

	if res.StatusCode != http.StatusOK {
		log.Error(ctx, "error with mcleod auth")
		return tokenResp, errors.New("error with mcleod auth")
	}

	if err = json.Unmarshal(resBody, &tokenResp); err != nil {
		return tokenResp, err

	}

	return tokenResp, nil
}

func (m *Mcleod) RefreshToken(
	ctx context.Context,
	clientID, clientSecret, refreshToken string,
) (TokenResp, error) {
	reqBody := map[string]string{
		"grant_type":    "refresh_token",
		"client_id":     clientID,
		"client_secret": clientSecret,
		"refresh_token": refreshToken,
	}

	var tokenResp TokenResp
	body, err := json.Marshal(reqBody)
	if err != nil {
		return tokenResp, err
	}
	client := otel.TracingHTTPClient()
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		"https://auth.mcleodsoftware.com/OAuth/Token",
		bytes.NewReader(body),
	)
	if err != nil {
		return tokenResp, err
	}
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		return tokenResp, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return tokenResp, err
	}

	if res.StatusCode != http.StatusOK {
		log.Error(ctx, "error with mcleod auth")
		return tokenResp, errors.New("error with mcleod auth")
	}

	if err = json.Unmarshal(resBody, &tokenResp); err != nil {
		return tokenResp, err

	}

	return tokenResp, nil
}

func (m *Mcleod) InitialOnboard(
	ctx context.Context,
	service models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardMcLeod", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	var redirectURI string

	switch env.Vars.AppEnv {
	case "dev":
		redirectURI = "https://eobu6bnyji0yr64.m.pipedream.net"
	case "staging":
		redirectURI = "https://app-staging.drumkit.ai/integrations/mcleod"
	case "prod":
		redirectURI = "https://app.drumkit.ai/integrations/mcleod"
	}

	tokenResp, err := New(m.tms).Authenticate(ctx, service.McLeodClientID,
		service.McLeodClientSecret, redirectURI, onboardRequest.AuthorizationCode)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	tokenExpTime := time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return models.OnboardTMSResponse{
		AccessToken:               tokenResp.AccessToken,
		RefreshToken:              tokenResp.RefreshToken,
		AccessTokenExpirationDate: tokenExpTime,
		APIKey:                    service.McLeodAPIKey,
	}, nil
}
