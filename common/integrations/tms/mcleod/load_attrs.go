package mcleod

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

var DefaultLoadAttributes = models.LoadAttributes{
	LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
		Status:           models.FieldAttributes{},
		Mode:             models.FieldAttributes{},
		MoreThanTwoStops: models.FieldAttributes{IsNotSupported: true},
		PONums:           models.FieldAttributes{IsNotSupported: true},
		Operator:         models.FieldAttributes{IsNotSupported: true},
		Customer: models.CustomerAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{},
				AddressLine1:  models.FieldAttributes{IsNotSupported: true},
				AddressLine2:  models.FieldAttributes{IsNotSupported: true},
				City:          models.FieldAttributes{IsNotSupported: true},
				State:         models.FieldAttributes{IsNotSupported: true},
				Zipcode:       models.FieldAttributes{IsNotSupported: true},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{},
				Email:         models.FieldAttributes{},
			},
			RefNumber:           models.FieldAttributes{IsNotSupported: true},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
		},
		BillTo: models.BillToAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{IsNotSupported: true},
				AddressLine1:  models.FieldAttributes{IsNotSupported: true},
				AddressLine2:  models.FieldAttributes{IsNotSupported: true},
				City:          models.FieldAttributes{IsNotSupported: true},
				State:         models.FieldAttributes{IsNotSupported: true},
				Zipcode:       models.FieldAttributes{IsNotSupported: true},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{IsNotSupported: true},
				Email:         models.FieldAttributes{IsNotSupported: true},
			},
		},
		Specifications: models.SpecificationsAttributes{
			OrderType:      models.FieldAttributes{IsNotSupported: true},
			Commodities:    models.FieldAttributes{},
			TotalPieces:    models.FieldAttributes{},
			TotalWeight:    models.FieldAttributes{},
			BillableWeight: models.FieldAttributes{IsNotSupported: true},
			TotalDistance:  models.FieldAttributes{},

			NumCommodities:      models.FieldAttributes{IsNotSupported: true},
			TotalInPalletCount:  models.FieldAttributes{},
			TotalOutPalletCount: models.FieldAttributes{},
			MinTempFahrenheit:   models.FieldAttributes{},
			MaxTempFahrenheit:   models.FieldAttributes{},
			IsRefrigerated:      models.FieldAttributes{IsNotSupported: true},
			LiftgatePickup:      models.FieldAttributes{IsNotSupported: true},
			LiftgateDelivery:    models.FieldAttributes{IsNotSupported: true},
			InsidePickup:        models.FieldAttributes{IsNotSupported: true},
			InsideDelivery:      models.FieldAttributes{IsNotSupported: true},
			Tarps:               models.FieldAttributes{IsNotSupported: true},
			Oversized:           models.FieldAttributes{IsNotSupported: true},
			Hazmat:              models.FieldAttributes{},
			Straps:              models.FieldAttributes{IsNotSupported: true},
			Permits:             models.FieldAttributes{IsNotSupported: true},
			Escorts:             models.FieldAttributes{IsNotSupported: true},
			Seal:                models.FieldAttributes{IsNotSupported: true},
			CustomBonded:        models.FieldAttributes{IsNotSupported: true},
			Labor:               models.FieldAttributes{IsNotSupported: true},
		},
		// If needed, provided in movements
		RateData: models.RateDataAttributes{
			CustomerRateType: models.FieldAttributes{},
			FSCPercent:       models.FieldAttributes{IsNotSupported: true},
			FSCPerMile:       models.FieldAttributes{IsNotSupported: true},
			CarrierRateType:  models.FieldAttributes{IsNotSupported: true},
			CarrierMaxRate:   models.FieldAttributes{IsNotSupported: true},
			NetProfitUSD:     models.FieldAttributes{IsNotSupported: true},
			ProfitPercent:    models.FieldAttributes{IsNotSupported: true},
		},

		Pickup: models.PickupAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{IsNotSupported: true},
				AddressLine1:  models.FieldAttributes{},
				AddressLine2:  models.FieldAttributes{},
				City:          models.FieldAttributes{},
				State:         models.FieldAttributes{},
				Zipcode:       models.FieldAttributes{},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{IsNotSupported: true},
				Email:         models.FieldAttributes{IsNotSupported: true},
			},
			ExternalTMSStopID:   models.FieldAttributes{IsNotSupported: true},
			BusinessHours:       models.FieldAttributes{IsNotSupported: true},
			RefNumber:           models.FieldAttributes{IsNotSupported: true},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			ReadyTime:           models.FieldAttributes{IsNotSupported: true},
			ApptType:            models.FieldAttributes{IsNotSupported: true},
			ApptStartTime:       models.FieldAttributes{},
			ApptEndTime:         models.FieldAttributes{},
			ApptNote:            models.FieldAttributes{IsNotSupported: true},
			Timezone:            models.FieldAttributes{},
		},
		Consignee: models.ConsigneeAttributes{
			CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
				ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
				Name:          models.FieldAttributes{IsNotSupported: true},
				AddressLine1:  models.FieldAttributes{},
				AddressLine2:  models.FieldAttributes{},
				City:          models.FieldAttributes{},
				State:         models.FieldAttributes{},
				Zipcode:       models.FieldAttributes{},
				Country:       models.FieldAttributes{IsNotSupported: true},
				Contact:       models.FieldAttributes{IsNotSupported: true},
				Phone:         models.FieldAttributes{IsNotSupported: true},
				Email:         models.FieldAttributes{IsNotSupported: true},
			},
			ExternalTMSStopID:   models.FieldAttributes{IsNotSupported: true},
			BusinessHours:       models.FieldAttributes{IsNotSupported: true},
			RefNumber:           models.FieldAttributes{},
			RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			MustDeliver:         models.FieldAttributes{},
			ApptType:            models.FieldAttributes{IsNotSupported: true},
			ApptStartTime:       models.FieldAttributes{},
			ApptEndTime:         models.FieldAttributes{},
			ApptNote:            models.FieldAttributes{IsNotSupported: true},
			Timezone:            models.FieldAttributes{},
		},
		Carrier: models.CarrierAttributes{
			ExternalTMSID:            models.FieldAttributes{IsNotSupported: true},
			DOTNumber:                models.FieldAttributes{IsNotSupported: true},
			SealNumber:               models.FieldAttributes{},
			Name:                     models.FieldAttributes{IsNotSupported: true},
			Phone:                    models.FieldAttributes{IsNotSupported: true},
			Email:                    models.FieldAttributes{IsNotSupported: true},
			Dispatcher:               models.FieldAttributes{IsNotSupported: true},
			DispatchSource:           models.FieldAttributes{IsNotSupported: true},
			MCNumber:                 models.FieldAttributes{IsNotSupported: true},
			Notes:                    models.FieldAttributes{IsNotSupported: true},
			SCAC:                     models.FieldAttributes{IsNotSupported: true},
			FirstDriverName:          models.FieldAttributes{IsNotSupported: true},
			FirstDriverPhone:         models.FieldAttributes{IsNotSupported: true},
			SecondDriverName:         models.FieldAttributes{IsNotSupported: true},
			SecondDriverPhone:        models.FieldAttributes{IsNotSupported: true},
			DispatchCity:             models.FieldAttributes{IsNotSupported: true},
			DispatchState:            models.FieldAttributes{IsNotSupported: true},
			ExternalTMSTruckID:       models.FieldAttributes{IsNotSupported: true},
			ExternalTMSTrailerID:     models.FieldAttributes{IsNotSupported: true},
			RateConfirmationSent:     models.FieldAttributes{IsNotSupported: true},
			ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true},
			ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true},
			DispatchedTime:           models.FieldAttributes{IsNotSupported: true},
			ExpectedPickupTime:       models.FieldAttributes{IsNotSupported: true},
			ExpectedDeliveryTime:     models.FieldAttributes{IsNotSupported: true},
			SignedBy:                 models.FieldAttributes{IsNotSupported: true},

			PickupStart:   models.FieldAttributes{IsNotSupported: true},
			PickupEnd:     models.FieldAttributes{IsNotSupported: true},
			DeliveryStart: models.FieldAttributes{IsNotSupported: true},
			DeliveryEnd:   models.FieldAttributes{IsNotSupported: true},
		},
	},
}

func (m *Mcleod) GetDefaultLoadAttributes() models.LoadAttributes {
	attrs := DefaultLoadAttributes
	tmsutil.ApplyTMSFeatureFlags(&m.tms, &attrs)

	return attrs
}
