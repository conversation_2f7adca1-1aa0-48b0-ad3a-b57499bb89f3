package mcleod

type OrderResp struct {
	RequestID string `json:"requestId,omitempty"`
	Status    struct {
		Action          string `json:"action,omitempty"`
		OrderID         string `json:"orderId,omitempty"`
		Reason          string `json:"reason,omitempty"`
		OrderCreateDate string `json:"orderCreateDate,omitempty"`
	} `json:"status,omitempty"`
	PalletCount     int  `json:"palletCount,omitempty"`
	PalletsRequired bool `json:"palletsRequired,omitempty"`
	OrderValue      struct {
		HighValue bool    `json:"highValue,omitempty"`
		Value     float32 `json:"value,omitempty"`
		Currency  string  `json:"currency,omitempty"`
	} `json:"orderValue,omitempty"`
	Commodity struct {
		Name             string `json:"name,omitempty"`
		ID               string `json:"id,omitempty"`
		Hazmat           bool   `json:"hazmat,omitempty"`
		HazmatID         string `json:"hazmatId,omitempty"`
		TemperatureRange struct {
			Low  int `json:"low,omitempty"`
			High int `json:"high,omitempty"`
		} `json:"temperatureRange,omitempty"`
	} `json:"commodity,omitempty"`
	EquipmentDetails struct {
		Preload     bool   `json:"preload,omitempty"`
		TrailerID   string `json:"trailerId,omitempty"`
		TractorType string `json:"tractorType,omitempty"`
		TrailerType string `json:"trailerType,omitempty"`
		Team        bool   `json:"team,omitempty"`
	} `json:"equipmentDetails,omitempty"`
	RoundTrip bool `json:"roundTrip,omitempty"`
	Customer  struct {
		Name    string `json:"name,omitempty"`
		Phone   string `json:"phone,omitempty"`
		Email   string `json:"email,omitempty"`
		Website string `json:"website,omitempty"`
	} `json:"customer,omitempty"`
	Reply struct {
		RespondByDate string `json:"respondByDate,omitempty"`
	} `json:"reply,omitempty"`
	OrderReferenceNumbers struct {
		Bol            string `json:"bol,omitempty"`
		SealNumber     string `json:"sealNumber,omitempty"`
		ConsigneeRefno string `json:"consigneeRefno,omitempty"`
	} `json:"orderReferenceNumbers,omitempty"`
	Stops  []Stop `json:"stops,omitempty"`
	Rating struct {
		CollectionMethod string  `json:"collectionMethod,omitempty"`
		RateType         string  `json:"rateType,omitempty"`
		BillDistance     float32 `json:"billDistance,omitempty"`
		UnitDescription  string  `json:"unitDescription,omitempty"`
		Units            float32 `json:"units,omitempty"`
		Rate             float32 `json:"rate,omitempty"`
		FreightCharge    struct {
			Value    float32 `json:"value,omitempty"`
			Currency string  `json:"currency,omitempty"`
		} `json:"freightCharge,omitempty"`
		Pieces           int `json:"pieces,omitempty"`
		Weight           int `json:"weight,omitempty"`
		OtherChargeTotal struct {
			Value    string `json:"value,omitempty"`
			Currency string `json:"currency,omitempty"`
		} `json:"otherChargeTotal,omitempty"`
		OtherCharges []struct {
			Description string  `json:"description,omitempty"`
			Units       float32 `json:"units,omitempty"`
			Rate        float32 `json:"rate,omitempty"`
			Code        string  `json:"code,omitempty"`
			Charge      struct {
				Value    float32 `json:"value,omitempty"`
				Currency string  `json:"currency,omitempty"`
			} `json:"charge,omitempty"`
		} `json:"otherCharges,omitempty"`
	} `json:"rating,omitempty"`
	AllocationCode string `json:"allocationCode,omitempty"`
	RevenueCode    string `json:"revenueCode,omitempty"`
	OrderType      string `json:"orderType,omitempty"`
	CreatedDate    string `json:"createdDate,omitempty"`
}

type Stop struct {
	LocationID            string `json:"locationId,omitempty"`
	LocationName          string `json:"locationName,omitempty"`
	Address               string `json:"address,omitempty"`
	Address2              string `json:"address2,omitempty"`
	CityName              string `json:"cityName,omitempty"`
	State                 string `json:"state,omitempty"`
	ZipCode               string `json:"zipCode,omitempty"`
	ScheduledArrivalEarly string `json:"scheduledArrivalEarly,omitempty"`
	ScheduledArrivalLate  string `json:"scheduledArrivalLate,omitempty"`
	StopType              string `json:"stopType,omitempty"`
	Cases                 string `json:"cases,omitempty"`
	Weight                string `json:"weight,omitempty"`
	PalletsIn             int    `json:"palletsIn,omitempty"`
	PalletsOut            int    `json:"palletsOut,omitempty"`
	ReferenceNumbers      []struct {
		Value     string `json:"value,omitempty"`
		Qualifier string `json:"qualifier,omitempty"`
		Pieces    int    `json:"pieces,omitempty"`
		Weight    string `json:"weight,omitempty"`
	} `json:"referenceNumbers,omitempty"`
	Contact struct {
		Name  string `json:"name,omitempty"`
		Phone string `json:"phone,omitempty"`
	} `json:"contact,omitempty"`
	Notes []struct {
		Comments string `json:"comments,omitempty"`
		Type     string `json:"type,omitempty"`
		Sequence string `json:"sequence,omitempty"`
	} `json:"notes,omitempty"`
}

type TrackOrderDetails struct {
	ConsigneeReferenceNumber string `json:"consigneeReferenceNumber"`
	Status                   string `json:"status"`
	OrderID                  string `json:"orderId"`
	ShipmentID               string `json:"shipmentId"`
	BillToCustomerCode       string `json:"billToCustomerCode"`
	ShipperStop              struct {
		SequenceNumber        int     `json:"sequenceNumber"`
		City                  string  `json:"city"`
		State                 string  `json:"state"`
		PostalCode            string  `json:"postalCode"`
		Latitude              float64 `json:"latitude"`
		Longitude             float64 `json:"longitude"`
		Eta                   string  `json:"eta"`
		ScheduledArrivalEarly string  `json:"scheduledArrivalEarly"`
		ScheduledArrivalLate  string  `json:"scheduledArrivalLate"`
		ActualArrivalEarly    string  `json:"actualArrivalEarly"`
		ActualArrivalLate     string  `json:"actualArrivalLate"`
		AppointmentRequired   bool    `json:"appointmentRequired"`
		AppointmentConfirmed  bool    `json:"appointmentConfirmed"`
		ReferenceNumbers      []struct {
			QualifierCode   string `json:"qualifierCode,omitempty"`
			ReferenceNumber string `json:"referenceNumber,omitempty"`
		} `json:"referenceNumbers"`
	} `json:"shipperStop"`
	ConsigneeStop struct {
		SequenceNumber        int     `json:"sequenceNumber"`
		City                  string  `json:"city"`
		State                 string  `json:"state"`
		PostalCode            string  `json:"postalCode"`
		Latitude              float64 `json:"latitude"`
		Longitude             float64 `json:"longitude"`
		Eta                   string  `json:"eta"`
		ScheduledArrivalEarly string  `json:"scheduledArrivalEarly"`
		ScheduledArrivalLate  string  `json:"scheduledArrivalLate"`
		ActualArrivalEarly    string  `json:"actualArrivalEarly"`
		ActualArrivalLate     string  `json:"actualArrivalLate"`
		AppointmentRequired   bool    `json:"appointmentRequired"`
		AppointmentConfirmed  bool    `json:"appointmentConfirmed"`
		ReferenceNumbers      []struct {
			QualifierCode   string `json:"qualifierCode,omitempty"`
			ReferenceNumber string `json:"referenceNumber,omitempty"`
		} `json:"referenceNumbers"`
	} `json:"consigneeStop"`
	IntermediateStops []struct {
		SequenceNumber        int     `json:"sequenceNumber"`
		City                  string  `json:"city"`
		State                 string  `json:"state"`
		PostalCode            string  `json:"postalCode"`
		Latitude              float64 `json:"latitude"`
		Longitude             float64 `json:"longitude"`
		Eta                   string  `json:"eta"`
		ScheduledArrivalEarly string  `json:"scheduledArrivalEarly"`
		ScheduledArrivalLate  string  `json:"scheduledArrivalLate"`
		ActualArrivalEarly    string  `json:"actualArrivalEarly"`
		ActualArrivalLate     string  `json:"actualArrivalLate"`
		AppointmentRequired   bool    `json:"appointmentRequired"`
		AppointmentConfirmed  bool    `json:"appointmentConfirmed"`
		ReferenceNumbers      []struct {
			QualifierCode   string `json:"qualifierCode,omitempty"`
			ReferenceNumber string `json:"referenceNumber,omitempty"`
		} `json:"referenceNumbers"`
	} `json:"intermediateStops"`
	Charges struct {
		Freight struct {
			Amount struct {
				Amount       int    `json:"amount"`
				CurrencyCode string `json:"currencyCode"`
			} `json:"amount"`
		} `json:"freight"`
		Other []struct {
			ChargeCode  string `json:"chargeCode"`
			Description string `json:"description"`
			BillToCode  string `json:"billToCode"`
			Amount      struct {
				Amount       int    `json:"amount"`
				CurrencyCode string `json:"currencyCode"`
			} `json:"amount"`
		} `json:"other"`
		Total struct {
			Amount struct {
				Amount       int    `json:"amount"`
				CurrencyCode string `json:"currencyCode"`
			} `json:"amount"`
		} `json:"total"`
	} `json:"charges"`
	ProgressUpdates []struct {
		Event     string `json:"event"`
		Timestamp string `json:"timestamp"`
		Position  struct {
			Latitude   float64 `json:"latitude"`
			Longitude  float64 `json:"longitude"`
			City       string  `json:"city"`
			State      string  `json:"state"`
			PostalCode string  `json:"postalCode"`
		} `json:"position"`
		MilesToConsignee int `json:"milesToConsignee"`
	} `json:"progressUpdates"`
}

type QuoteResp struct {
	ID string `json:"id"`
	QuoteRequest
}

type QuoteRequest struct {
	ContactName  string `json:"contactName,omitempty"`
	CustomerName string `json:"customerName,omitempty"`
	Shipper      struct {
		Name       string `json:"name,omitempty"`
		City       string `json:"city,omitempty"`
		State      string `json:"state,omitempty"`
		PostalCode string `json:"postalCode,omitempty"`
	} `json:"shipper,omitempty"`
	Consignee struct {
		Name       string `json:"name,omitempty"`
		City       string `json:"city,omitempty"`
		State      string `json:"state,omitempty"`
		PostalCode string `json:"postalCode,omitempty"`
	} `json:"consignee,omitempty"`
	RequiredEquipment struct {
		EquipmentType string `json:"equipmentType,omitempty"`
	} `json:"requiredEquipment,omitempty"`
	ProposedRate struct {
		Value int `json:"value,omitempty"`
	} `json:"proposedRate,omitempty"`
	Expiration string `json:"expiration,omitempty"`
	Status     string `json:"status,omitempty"`
}

type TokenResp struct {
	AccessToken  string `json:"access_token"`
	IDToken      string `json:"id_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
}
