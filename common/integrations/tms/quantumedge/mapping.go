package quantumedge

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

func (q *QuantumEdge) mapWeightType(weightTypeID string) string {
	switch weightTypeID {
	case "1":
		return models.LbsUnit
	case "2":
		return models.KgUnit
	default:
		return models.LbsUnit
	}
}

func (q *QuantumEdge) mapMode(modeID string) models.LoadMode {
	switch modeID {
	case "1":
		return models.DrayageMode // IMDL - Intermodal
	case "2":
		return models.TLMode // TL
	case "3":
		return models.LTLMode // LTL
	case "4":
		return models.AirMode // Air
	case "5":
		return models.RailMode // Rail/Ocean
	default:
		return models.DrayageMode
	}
}

func (q *QuantumEdge) mapStopType(stopType string) models.StopType {
	switch strings.TrimSpace(strings.ToLower(stopType)) {
	case "pickup":
		return models.PickupStop
	case "delivery", "dropoff":
		return models.DropoffStop
	default:
		return models.StopType(stopType)
	}
}

func (q *QuantumEdge) parseWeight(weightStr string, unitStr string) models.ValueUnit {
	if weightStr == "" {
		return models.ValueUnit{}
	}

	weight, err := strconv.ParseFloat(weightStr, 32)
	if err != nil {
		return models.ValueUnit{}
	}

	unit := unitStr
	if unit == "" {
		unit = models.LbsUnit
	}

	return models.ValueUnit{
		Val:  float32(weight),
		Unit: unit,
	}
}

func (q *QuantumEdge) parseDateTime(dateStr, timeStr string) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, errors.New("empty date string")
	}

	var fullTimeStr string
	if timeStr != "" {
		fullTimeStr = dateStr + " " + timeStr
	} else {
		fullTimeStr = dateStr
	}

	layouts := []string{
		"01/02/2006 15:04",
		"01/02/2006 3:04 PM",
		"01/02/2006",
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, fullTimeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("unable to parse date/time: %s %s", dateStr, timeStr)
}
