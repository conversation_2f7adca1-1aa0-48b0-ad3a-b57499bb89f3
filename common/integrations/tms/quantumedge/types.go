package quantumedge

// QuantumEdgeLoadData represents the raw load data extracted from QuantumEdge
type LoadData struct {
	// Basic shipment fields
	ShipmentID      string
	CustomerPONum   string
	CustomerRef     string
	BOLNum          string
	SealNumber      string
	ContainerNumber string
	ChassisNumber   string
	ContainerPrefix string
	ContainerSuffix string
	CommodityDesc   string
	ShipmentValue   string

	// Weight and dimensions
	Weight               string
	WeightType           string
	EmptyContainerWeight string
	Length               string
	Width                string
	Height               string
	DimensionUnit        string

	// Equipment and temperature
	EquipmentType   string
	EquipmentLength string
	Temperature     string
	TemperatureUnit string

	// Mode and service
	Mode        string
	ServiceType string

	// Organizational fields
	AccountManager string
	Office         string
	Team           string
	SalesRep       string

	// Status and dates
	Status       string
	CreatedDate  string
	ModifiedDate string

	// Additional references
	TrailerNumber string
	TractorNumber string
	DriverName    string
	CarrierName   string

	// Location information
	CustomerInfo  CustomerInfo
	PickupInfo    LocationInfo
	ConsigneeInfo LocationInfo

	// Parsed stops from stops API
	ParsedStops []LocationInfo

	Notes []Note

	// Rate data
	RateData *RateData
}

// CustomerInfo holds customer details
type CustomerInfo struct {
	Name    string
	Address string
	City    string
	State   string
	Zip     string
	Phone   string
	Contact string
}

// LocationInfo holds pickup/consignee/stop location details
type LocationInfo struct {
	Name            string
	Address         string
	AddressLine2    string
	City            string
	State           string
	Zip             string
	Country         string
	Phone           string
	Contact         string
	Email           string
	Date            string
	Time            string
	ReadyDate       string
	ReadyTime       string
	DeliveryDate    string
	DeliveryTime    string
	AppointmentReq  bool
	AppointmentType string
	Instructions    string
	RefNumber       string
	// Additional fields for stops parsing
	StopType    string // "pickup" or "delivery"
	StopNumber  int    // Sequential stop number
	StopID      string // External TMS stop ID
	ServiceType string // "Live" or "Drop"
	// Schedule appointment fields
	SchedFromDate string
	SchedFromTime string
	SchedToDate   string
	SchedToTime   string
	// Driver arrival/departure fields
	DriverArrivedDate  string
	DriverArrivedTime  string
	DriverDepartedDate string
	DriverDepartedTime string
}

type Note struct {
	ID          string
	Content     string
	User        string
	Type        string
	ShipmentLeg any
	Source      string
	CreatedAt   string
}

// RateData holds AR and AP rate information
type RateData struct {
	ARInvoices []ARInvoiceData `json:"arInvoices"`
	APInvoices []APInvoiceData `json:"apInvoices"`
}

// ARInvoiceData represents customer (AR) invoice data from QuantumEdge
type ARInvoiceData struct {
	TotalRowCount int                  `json:"TOTALROWCOUNT"`
	Query         ARInvoiceQueryResult `json:"QUERY"`
}

// ARInvoiceQueryResult holds the query result structure for AR invoices
type ARInvoiceQueryResult struct {
	Columns []string `json:"COLUMNS"`
	Data    [][]any  `json:"DATA"`
}

// APInvoiceData represents carrier (AP) invoice data from QuantumEdge
type APInvoiceData struct {
	TotalRowCount int                  `json:"TOTALROWCOUNT"`
	Query         APInvoiceQueryResult `json:"QUERY"`
}

// APInvoiceQueryResult holds the query result structure for AP invoices
type APInvoiceQueryResult struct {
	Columns []string `json:"COLUMNS"`
	Data    [][]any  `json:"DATA"`
}

// ARLineItem represents a parsed line item from AR invoice
type ARLineItem struct {
	LineItem            int     `json:"lineItem"`
	LineTypeID          int     `json:"lineTypeID"`
	LineTypeDesc        string  `json:"lineTypeDesc"`
	Amount              float64 `json:"amount"`
	AmountExch          float64 `json:"amountExch"`
	EnterDate           string  `json:"enterDate"`
	EnterUser           int     `json:"enterUser"`
	EnterUserName       string  `json:"enterUserName"`
	ReferenceNumber     string  `json:"referenceNumber"`
	Note                string  `json:"note"`
	UnitQuantity        float64 `json:"unitQuantity"`
	UnitPrice           float64 `json:"unitPrice"`
	UnitTypeID          int     `json:"unitTypeID"`
	UnitTypeName        string  `json:"unitTypeName"`
	RequiresCalculation int     `json:"requiresCalculation"`
}

// APLineItem represents a parsed line item from AP invoice
type APLineItem struct {
	LineItem        int     `json:"lineItem"`
	LineTypeID      int     `json:"lineTypeID"`
	LineTypeDesc    string  `json:"lineTypeDesc"`
	Amount          float64 `json:"amount"`
	AmountExch      float64 `json:"amountExch"`
	UnitQuantity    float64 `json:"unitQuantity"`
	UnitPrice       float64 `json:"unitPrice"`
	UnitTypeID      int     `json:"unitTypeID"`
	UnitTypeName    string  `json:"unitTypeName"`
	EnterDate       string  `json:"enterDate"`
	EnterUser       int     `json:"enterUser"`
	EnterUserName   string  `json:"enterUserName"`
	ReferenceNumber string  `json:"referenceNumber"`
	Note            string  `json:"note"`
	Sequence        int     `json:"sequence"`
	HeaderSite      string  `json:"headerSite"`
}
