package quantumedge

import (
	"encoding/json"
	"testing"
)

func TestParseNotesResponse(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		want     int
		wantErr  bool
	}{
		{
			name: "valid notes response",
			jsonData: `{
				"success": true,
				"data": {
					"notes": [
						{
							"id": 123,
							"content": "Test note 1",
							"user": "John Doe",
							"type": "General",
							"legSeq": 1,
							"source": "TMS",
							"createDate": "2025-09-16 10:00:00.0"
						},
						{
							"id": 124,
							"content": "Test note 2",
							"user": "<PERSON>",
							"type": "Instructions",
							"legSeq": null,
							"source": "Customer",
							"createDate": "2025-09-15 15:30:00.0"
						}
					]
				}
			}`,
			want:    2,
			wantErr: false,
		},
		{
			name: "empty notes",
			jsonData: `{
				"success": true,
				"data": {
					"notes": []
				}
			}`,
			want:    0,
			wantErr: false,
		},
		{
			name: "unsuccessful response",
			jsonData: `{
				"success": false,
				"data": {
					"notes": []
				}
			}`,
			want:    0,
			wantErr: true,
		},
		{
			name:     "invalid JSON",
			jsonData: `{invalid json}`,
			want:     0,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var resp notesResponse
			err := json.Unmarshal([]byte(tt.jsonData), &resp)

			if tt.wantErr && err == nil {
				// For unsuccessful response, we need to check the success field
				if resp.Success {
					t.Error("Expected error for unsuccessful response")
				}
				return
			}

			if !tt.wantErr && err != nil {
				t.Errorf("Unexpected JSON parse error: %v", err)
				return
			}

			if err != nil {
				return
			}

			if !resp.Success {
				return
			}

			if len(resp.Data.Notes) != tt.want {
				t.Errorf("Notes count = %v, want %v", len(resp.Data.Notes), tt.want)
			}

			// Test note conversion
			notes := make([]Note, 0, len(resp.Data.Notes))
			for _, n := range resp.Data.Notes {
				notes = append(notes, Note{
					ID:          string(rune(n.ID)),
					Content:     n.Content,
					User:        n.User,
					Type:        n.Type,
					ShipmentLeg: n.LegSeq,
					Source:      n.Source,
					CreatedAt:   n.CreateDate,
				})
			}

			if len(notes) != tt.want {
				t.Errorf("Converted notes count = %v, want %v", len(notes), tt.want)
			}

			// Verify first note if present
			if len(notes) > 0 && tt.want > 0 {
				firstNote := notes[0]
				if firstNote.Content == "" {
					t.Error("First note content should not be empty")
				}
				if firstNote.User == "" {
					t.Error("First note user should not be empty")
				}
			}
		})
	}
}

func TestNotesJSONStructure(t *testing.T) {
	// Test the actual JSON structure we expect from QuantumEdge
	sampleJSON := `{
		"success": true,
		"data": {
			"notes": [
				{
					"id": 12345,
					"content": "Load picked up on time",
					"user": "Test User",
					"type": "General",
					"legSeq": 1,
					"source": "TMS",
					"createDate": "2025-09-16 10:00:00.0"
				}
			]
		}
	}`

	var resp notesResponse
	err := json.Unmarshal([]byte(sampleJSON), &resp)
	if err != nil {
		t.Fatalf("Failed to parse sample JSON: %v", err)
	}

	if !resp.Success {
		t.Error("Expected success to be true")
	}

	if len(resp.Data.Notes) != 1 {
		t.Errorf("Expected 1 note, got %d", len(resp.Data.Notes))
	}

	note := resp.Data.Notes[0]
	if note.ID != 12345 {
		t.Errorf("Expected note ID 12345, got %d", note.ID)
	}
	if note.Content != "Load picked up on time" {
		t.Errorf("Expected note content 'Load picked up on time', got %s", note.Content)
	}
}
