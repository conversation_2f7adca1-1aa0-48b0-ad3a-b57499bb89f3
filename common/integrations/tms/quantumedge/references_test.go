package quantumedge

import (
	"testing"

	"github.com/drumkitai/drumkit/common/models"
)

func TestBuildAdditionalReferences(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name     string
		loadData *LoadData
		want     models.AdditionalReferences
	}{
		{
			name: "All reference types present",
			loadData: &LoadData{
				BOLNum:          "BOL123456",
				ContainerNumber: "CONTAINER789",
				SealNumber:      "SEAL001",
				ChassisNumber:   "CHASSIS999",
			},
			want: models.AdditionalReferences{
				{Qualifier: "BOL", Number: "BOL123456"},
				{Qualifier: "CONTAINER", Number: "CONTAINER789"},
				{Qualifier: "SEAL", Number: "SEAL001"},
				{Qualifier: "CHASSIS", Number: "CHASSIS999"},
			},
		},
		{
			name: "No references present",
			loadData: &LoadData{
				BOLNum:          "",
				ContainerNumber: "",
				SealNumber:      "",
				ChassisNumber:   "",
			},
			want: models.AdditionalReferences{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.buildAdditionalReferences(tt.loadData)
			if len(got) != len(tt.want) {
				t.Errorf("buildAdditionalReferences() len = %v, want %v", len(got), len(tt.want))
				return
			}
			for i, ref := range got {
				if i >= len(tt.want) || ref.Qualifier != tt.want[i].Qualifier || ref.Number != tt.want[i].Number {
					t.Errorf("buildAdditionalReferences()[%d] = %v, want %v", i, ref, tt.want[i])
				}
			}
		})
	}
}
