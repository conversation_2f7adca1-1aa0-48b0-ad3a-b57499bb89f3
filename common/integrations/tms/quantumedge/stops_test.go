package quantumedge

import (
	"context"
	"strings"
	"testing"

	"github.com/PuerkitoBio/goquery"

	"github.com/drumkitai/drumkit/common/models"
)

func TestConvertStopsToModels(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	tests := []struct {
		name  string
		stops []LocationInfo
		want  int
	}{
		{
			name: "pickup and delivery stops",
			stops: []LocationInfo{
				{
					StopID:     "123",
					StopNumber: 1,
					StopType:   "pickup",
					Name:       "Pickup Location",
					Address:    "123 Main St",
					City:       "Chicago",
					State:      "IL",
					Zip:        "60601",
					Phone:      "(*************",
					RefNumber:  "PU123",
					ReadyDate:  "09/16/2025",
					ReadyTime:  "08:00",
				},
				{
					StopID:     "456",
					StopNumber: 2,
					StopType:   "delivery",
					Name:       "Delivery Location",
					City:       "Boston",
					State:      "MA",
				},
			},
			want: 2,
		},
		{
			name:  "empty stops",
			stops: []LocationInfo{},
			want:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.convertStopsToModels(ctx, tt.stops)
			if len(got) != tt.want {
				t.Errorf("convertStopsToModels() len = %v, want %v", len(got), tt.want)
				return
			}

			for i, stop := range got {
				if i < len(tt.stops) {
					expected := tt.stops[i]
					if stop.StopNumber != expected.StopNumber {
						t.Errorf("stop[%d].StopNumber = %v, want %v", i, stop.StopNumber, expected.StopNumber)
					}
					if stop.ExternalTMSStopID != expected.StopID {
						t.Errorf("stop[%d].ExternalTMSStopID = %v, want %v", i, stop.ExternalTMSStopID, expected.StopID)
					}
					expectedType := "pickup"
					if expected.StopType == "delivery" {
						expectedType = string(models.DropoffStop)
					}
					if stop.StopType != expectedType {
						t.Errorf("stop[%d].StopType = %v, want %v", i, stop.StopType, expectedType)
					}
				}
			}
		})
	}
}

func TestExtractStopDates(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name string
		html string
		want map[string]string
	}{
		{
			name: "all date fields present",
			html: `<div class="stop-wrapper">
				<input id="req_from_date_1" value="09/16/2025" />
				<input id="req_from_time_1" value="08:00" />
				<input id="sched_from_date_1" value="09/17/2025" />
				<input id="sched_from_time_1" value="09:00" />
			</div>`,
			want: map[string]string{
				"ReadyDate":     "09/16/2025",
				"ReadyTime":     "08:00",
				"SchedFromDate": "09/17/2025",
				"SchedFromTime": "09:00",
			},
		},
		{
			name: "partial date fields",
			html: `<div class="stop-wrapper">
				<input id="req_from_date_1" value="09/16/2025" />
			</div>`,
			want: map[string]string{
				"ReadyDate": "09/16/2025",
			},
		},
		{
			name: "empty date values",
			html: `<div class="stop-wrapper">
				<input id="req_from_date_1" value="" />
				<input id="req_from_time_1" value="" />
			</div>`,
			want: map[string]string{},
		},
		{
			name: "missing input elements",
			html: `<div class="stop-wrapper">
				<p>No date inputs here</p>
			</div>`,
			want: map[string]string{},
		},
		{
			name: "scheduled to dates",
			html: `<div class="stop-wrapper">
				<input id="sched_to_date_1" value="09/18/2025" />
				<input id="sched_to_time_1" value="17:00" />
			</div>`,
			want: map[string]string{
				"SchedToDate": "09/18/2025",
				"SchedToTime": "17:00",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			stop := LocationInfo{}
			stopWrapper := doc.Find(".stop-wrapper").First()
			q.extractStopDates(stopWrapper, &stop)

			for field, expectedValue := range tt.want {
				var actualValue string
				switch field {
				case "ReadyDate":
					actualValue = stop.ReadyDate
				case "ReadyTime":
					actualValue = stop.ReadyTime
				case "SchedFromDate":
					actualValue = stop.SchedFromDate
				case "SchedFromTime":
					actualValue = stop.SchedFromTime
				case "SchedToDate":
					actualValue = stop.SchedToDate
				case "SchedToTime":
					actualValue = stop.SchedToTime
				default:
					t.Fatalf("Unknown field: %s", field)
				}

				if actualValue != expectedValue {
					t.Errorf("extractStopDates() %s = %v, want %v", field, actualValue, expectedValue)
				}
			}
		})
	}
}

func TestParseStopsHTML(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	tests := []struct {
		name string
		html string
		want int
	}{
		{
			name: "single stop",
			html: `<html><body>
				<div class="stop-wrapper" data-stop-id="123">
					<h3>Stop 1 - Pickup - CHICAGO, IL</h3>
					<a data-site-id="456">Test Company</a>
				</div>
			</body></html>`,
			want: 1,
		},
		{
			name: "no stops",
			html: `<html><body></body></html>`,
			want: 0,
		},
		{
			name: "multiple stops",
			html: `<html><body>
				<div class="stop-wrapper" data-stop-id="123"></div>
				<div class="stop-wrapper" data-stop-id="456"></div>
			</body></html>`,
			want: 2,
		},
		{
			name: "stop without data-stop-id",
			html: `<html><body>
				<div class="stop-wrapper">
					<h3>Stop 1 - Pickup - CHICAGO, IL</h3>
				</div>
			</body></html>`,
			want: 1,
		},
		{
			name: "complex stop structure with all details",
			html: `<html><body>
				<div class="stop-wrapper" data-stop-id="789">
					<h3>Stop 1 - Delivery - NEW YORK, NY</h3>
					<a data-site-id="999">Complex Company Inc</a>
					<div>123 Complex Street</div>
					<div>New York, NY 10001</div>
					<div>(555) 987-6543</div>
					<input id="req_from_date_1" value="09/16/2025" />
					<input id="req_from_time_1" value="14:30" />
				</div>
			</body></html>`,
			want: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			got := q.parseStopsHTML(ctx, doc)
			if len(got) != tt.want {
				t.Errorf("parseStopsHTML() len = %v, want %v", len(got), tt.want)
			}
		})
	}
}
