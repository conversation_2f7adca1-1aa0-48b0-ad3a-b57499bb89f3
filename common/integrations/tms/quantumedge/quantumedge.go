package quantumedge

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type QuantumEdge struct {
	tms        models.Integration
	config     *Config
	httpClient httpClient
}

// Simplified interface for testing purposes
type httpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

type Config struct {
	Username string
	Password string
	Tenant   string
}

func New(ctx context.Context, tms models.Integration) (*QuantumEdge, error) {
	ctx = log.With(ctx, zap.String("tenant", tms.Tenant), zap.Uint("tmsID", tms.ID))

	cachedClient, err := retrieveRedisClient(ctx, tms.ServiceID, tms.ID)
	if err != nil {
		return nil, err
	}

	if cachedClient != nil {
		cachedClient.tms = tms
		log.Info(ctx, "re-using existing QuantumEdge client")
		return cachedClient, nil
	}

	quantumEdge, err := initialize(ctx, tms)
	if err != nil {
		return nil, err
	}

	_, err = quantumEdge.Auth(ctx)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	quantumEdge.dumpRedisClient(ctx)

	log.Info(ctx, "successfully authenticated QuantumEdge client")

	return quantumEdge, nil
}

func initialize(ctx context.Context, tms models.Integration) (*QuantumEdge, error) {
	password, err := crypto.DecryptAESGCM(ctx, string(tms.EncryptedPassword), nil)
	if err != nil {
		return nil, fmt.Errorf("error decrypting password: %w", err)
	}

	if tms.Username == "" || password == "" {
		return nil, errors.New("missing QuantumEdge username or password")
	}

	config := &Config{
		Username: tms.Username,
		Password: password,
		Tenant:   tms.Tenant,
	}

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create QuantumEdge cookie jar: %w", err)
	}

	httpClient := otel.TracingHTTPClient(120 * time.Second)
	httpClient.Jar = cookieJar

	quantumEdge := QuantumEdge{
		tms:        tms,
		httpClient: httpClient,
		config:     config,
	}

	return &quantumEdge, nil
}

func (q *QuantumEdge) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (name models.OnboardTMSResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnboardQuantumEdge", otel.IntegrationAttrs(q.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing QuantumEdge username or password")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, onboardRequest.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		EncryptedPassword: encryptedPassword,
		Username:          onboardRequest.Username,
		Tenant:            onboardRequest.Tenant,
	}, nil
}

func (q *QuantumEdge) GetTestLoads() map[string]bool {
	return make(map[string]bool)
}

func (q *QuantumEdge) GetDefaultLoadAttributes() models.LoadAttributes {
	return models.LoadAttributes{}
}

func (q *QuantumEdge) CreateLoad(context.Context, models.Load, *models.TMSUser) (models.Load, error) {
	return models.Load{}, errtypes.NotImplemented(models.QuantumEdge, "CreateLoad")
}

func (q *QuantumEdge) UpdateLoad(context.Context, *models.Load, *models.Load) (
	models.Load, models.LoadAttributes, error) {
	return models.Load{}, models.LoadAttributes{}, errtypes.NotImplemented(models.QuantumEdge, "UpdateLoad")
}

func (q *QuantumEdge) GetLoadIDs(context.Context, models.SearchLoadsQuery) ([]string, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetLoadIDs")
}

func (q *QuantumEdge) GetLoadsByIDType(context.Context, string, string) (
	[]models.Load, models.LoadAttributes, error) {
	return nil, models.LoadAttributes{}, errtypes.NotImplemented(models.QuantumEdge, "GetLoadsByIDType")
}

func (q *QuantumEdge) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return errtypes.NotImplemented(models.QuantumEdge, "PostCheckCall")
}

func (q *QuantumEdge) GetCheckCallsHistory(context.Context, uint, string) ([]models.CheckCall, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetCheckCallsHistory")
}

func (q *QuantumEdge) PostException(context.Context, *models.Load, models.Exception) error {
	return errtypes.NotImplemented(models.QuantumEdge, "PostException")
}

func (q *QuantumEdge) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetExceptionHistory")
}

func (q *QuantumEdge) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "PostNote")
}

func (q *QuantumEdge) GetCustomers(context.Context) ([]models.TMSCustomer, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetCustomers")
}

func (q *QuantumEdge) GetUsers(context.Context) ([]models.TMSUser, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetUsers")
}

func (q *QuantumEdge) GetLocations(context.Context, ...models.TMSOption) ([]models.TMSLocation, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetLocations")
}

func (q *QuantumEdge) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "GetCarriers")
}

func (q *QuantumEdge) CreateQuote(context.Context, models.CreateQuoteBody) (*models.CreateQuoteResponse, error) {
	return nil, errtypes.NotImplemented(models.QuantumEdge, "CreateQuote")
}

func (q *QuantumEdge) GetOrder(context.Context, string) (*models.Order, models.OrderAttributes, error) {
	return nil, models.OrderAttributes{}, errtypes.NotImplemented(models.QuantumEdge, "GetOrder")
}

func (q *QuantumEdge) MapTransportTypeEnum(string) (models.TransportType, error) {
	return "", errtypes.NotImplemented(models.QuantumEdge, "MapTransportTypeEnum")
}
