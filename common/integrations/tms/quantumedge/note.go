package quantumedge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
)

type notesResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Notes []struct {
			ID         int    `json:"id"`
			Content    string `json:"content"`
			User       string `json:"user"`
			Type       string `json:"type"`
			LegSeq     any    `json:"legSeq"`
			Source     string `json:"source"`
			CreateDate string `json:"createDate"`
		} `json:"notes"`
	} `json:"data"`
}

func (q *QuantumEdge) getLoadNotes(ctx context.Context, shipmentID string) ([]Note, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/notes/ShipmentNoteAPI.cfc", q.config.Tenant)
	params := url.Values{}
	params.Set("method", "getAllForShipment")
	params.Set("shipmentId", shipmentID)

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch load notes: %w", err)
	}

	var resp notesResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to parse notes response: %w", err)
	}

	if !resp.Success {
		return nil, errors.New("failed to fetch notes: API returned unsuccessful response")
	}

	notes := make([]Note, 0, len(resp.Data.Notes))
	for _, n := range resp.Data.Notes {
		notes = append(notes, Note{
			ID:          fmt.Sprintf("%d", n.ID),
			Content:     n.Content,
			User:        n.User,
			Type:        n.Type,
			ShipmentLeg: n.LegSeq,
			Source:      n.Source,
			CreatedAt:   n.CreateDate,
		})
	}

	return notes, nil
}
