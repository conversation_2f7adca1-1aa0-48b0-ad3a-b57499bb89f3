package quantumedge

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type envVars struct {
	Username       string `envconfig:"QUANTUM_EDGE_USERNAME" required:"true"`
	Password       string `envconfig:"QUANTUM_EDGE_PASSWORD" required:"true"`
	BaseURL        string `envconfig:"QUANTUM_EDGE_BASE_URL" required:"true"`
	TestShipmentID string `envconfig:"TEST_SHIPMENT_ID" default:"test-shipment-123"`
}

var (
	testEnv envVars
)

func TestLiveQuantumEdge(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveQuantumEdge: run with LIVE_TEST=true to enable")
		return
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	err := loadEnv(ctx)
	require.NoError(t, err)

	log.Info(ctx, "parsed env", zap.Any("env", testEnv))

	ctx = log.With(ctx, zap.String("shipment", testEnv.TestShipmentID))

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, testEnv.Password, nil)
	require.NoError(t, err)

	tms := models.Integration{
		Name:              models.QuantumEdge,
		Type:              models.TMS,
		ServiceID:         1,
		Username:          testEnv.Username,
		EncryptedPassword: []byte(encryptedPassword),
		Tenant:            testEnv.BaseURL,
	}

	client, err := New(ctx, tms)
	require.NoError(t, err, "client creation should succeed")
	var load models.Load

	t.Run("Auth", func(t *testing.T) {
		startTime := time.Now()

		log.Info(ctx, "testing authentication")
		_, err := client.Auth(ctx)
		require.NoError(t, err, "authentication should succeed")

		log.Info(ctx, "Auth completed", zap.Duration("duration", time.Since(startTime)))
	})

	t.Run("GetLoad", func(t *testing.T) {
		log.Info(ctx, "calling GetLoad for test shipment ID")

		startTime := time.Now()
		var err error

		load, _, err = client.GetLoad(ctx, testEnv.TestShipmentID)
		require.NoError(t, err, "GetLoad should succeed")

		// Verify basic load structure
		assert.NotEmpty(t, load.ExternalTMSID, "load should have external TMS ID")
		assert.NotEmpty(t, load.FreightTrackingID, "load should have freight tracking ID")

		log.Info(ctx, "GetLoad completed",
			zap.String("externalTMSID", load.ExternalTMSID),
			zap.String("freightTrackingID", load.FreightTrackingID),
			zap.String("status", load.Status),
			zap.String("customerName", load.Customer.Name),
			zap.Int("stopsCount", len(load.Stops)),
			zap.Int("notesCount", len(load.Notes)),
			zap.Duration("duration", time.Since(startTime)))
	})

	t.Run("ValidateCustomerInfo", func(t *testing.T) {
		if load.Customer.Name == "" {
			t.Skip("skipping customer validation: no customer data found")
			return
		}

		// Validate customer information parsing
		assert.NotEmpty(t, load.Customer.Name, "customer should have name")
		log.Info(ctx, "customer info validated",
			zap.String("name", load.Customer.Name),
			zap.String("address", load.Customer.AddressLine1),
			zap.String("city", load.Customer.City),
			zap.String("state", load.Customer.State),
			zap.String("zip", load.Customer.Zipcode),
			zap.String("phone", load.Customer.Phone))
	})

	t.Run("ValidateStopsData", func(t *testing.T) {
		if len(load.Stops) == 0 {
			t.Skip("skipping stops validation: no stops data found")
			return
		}

		// Validate stops parsing
		for i, stop := range load.Stops {
			assert.NotEmpty(t, stop.Address.Name, "stop %d should have name", i+1)
			log.Info(ctx, "stop validated",
				zap.Int("stopNumber", stop.StopNumber),
				zap.String("stopType", stop.StopType),
				zap.String("name", stop.Address.Name),
				zap.String("city", stop.Address.City),
				zap.String("state", stop.Address.State))
		}
	})

	t.Run("ValidateNotesData", func(t *testing.T) {
		if len(load.Notes) == 0 {
			t.Skip("skipping notes validation: no notes data found")
			return
		}

		// Validate notes parsing
		for i, note := range load.Notes {
			assert.NotEmpty(t, note.Note, "note %d should have content", i+1)
			log.Info(ctx, "note validated",
				zap.String("content", note.Note),
				zap.String("updatedBy", note.UpdatedBy),
				zap.Time("createdAt", note.CreatedAt.Time))
		}
	})
}

// TestLiveAuth verifies the authentication functionality against the live QuantumEdge API.
func TestLiveAuth(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveAuth: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	client := setUpQuantumEdge(ctx, testEnv.Username, testEnv.Password, testEnv.BaseURL)
	require.NotNil(t, client, "client setup should succeed")

	_, err := client.Auth(ctx)
	require.NoError(t, err, "authentication should succeed")

	log.Info(ctx, "authentication test completed successfully")
}

// TestLiveGetLoad verifies the load retrieval functionality against the live QuantumEdge API.
func TestLiveGetLoad(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetLoad: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	client := setUpQuantumEdge(ctx, testEnv.Username, testEnv.Password, testEnv.BaseURL)
	require.NotNil(t, client, "client setup should succeed")

	load, _, err := client.GetLoad(ctx, testEnv.TestShipmentID)
	require.NoError(t, err, "GetLoad should succeed")
	require.NotEmpty(t, load, "load should not be empty")

	log.Info(ctx, "load retrieval test completed",
		zap.String("shipmentID", load.ExternalTMSID),
		zap.String("status", load.Status))
}

// setUpQuantumEdge creates a new QuantumEdge client for testing. It handles authentication setup.
func setUpQuantumEdge(ctx context.Context, username, password, baseURL string) *QuantumEdge {
	err := loadEnv(ctx)
	if err != nil {
		return nil
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return nil
	}

	tms := models.Integration{
		Name:              models.QuantumEdge,
		Type:              models.TMS,
		ServiceID:         1,
		Username:          username,
		EncryptedPassword: []byte(encryptedPassword),
		Tenant:            baseURL,
	}

	client, err := New(ctx, tms)
	if err != nil {
		log.Error(ctx, "client creation failed", zap.Error(err))
		return nil
	}

	_, err = client.Auth(ctx)
	if err != nil {
		log.Error(ctx, "authentication failed", zap.Error(err))
		return nil
	}

	return client
}

func loadEnv(ctx context.Context) error {
	if err := godotenv.Load(); err != nil {
		log.Warn(ctx, "unable to load .env file", zap.Error(err))
	}

	if err := envconfig.Process("", &testEnv); err != nil {
		return fmt.Errorf("failed to parse env vars: %w", err)
	}

	return nil
}
