package quantumedge

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// getStopsData fetches the stops data from QuantumEdge for a given shipment
func (q *QuantumEdge) getStopsData(ctx context.Context, shipmentID string) (*goquery.Document, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/shipments/view/shipment_stops_od_v2.cfm", q.config.Tenant)
	params := url.Values{}
	params.Set("_cf_containerId", "container")
	params.Set("_cf_nodebug", "false")
	params.Set("_cf_nocache", "false")
	params.Set("_cf_clientid", "0")
	params.Set("_cf_rc", "1")
	params.Set("shipment_id_od", shipmentID)
	params.Set("this_stop_type", "")
	params.Set("selectedAccordionHeader", "0")
	params.Set("selectedStopTypeId", "0")
	params.Set("openPickupStopSequence", "0")
	params.Set("openDeliveryStopSequence", "0")

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch stops data: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse stops HTML response: %w", err)
	}

	return doc, nil
}

// parseStopsHTML extracts stop information from the QuantumEdge stops HTML page
func (q *QuantumEdge) parseStopsHTML(ctx context.Context, doc *goquery.Document) []LocationInfo {
	var stops []LocationInfo

	// Find all stop wrappers
	doc.Find(".stop-wrapper").Each(func(i int, stopWrapper *goquery.Selection) {
		stop := LocationInfo{}

		// Extract stop ID from data attributes
		stopID, _ := stopWrapper.Attr("data-stop-id")

		stop.StopID = stopID
		stop.StopNumber = i + 1

		// Find the header to extract stop type and location
		header := stopWrapper.Parent().Find("h3").First()
		headerText := strings.TrimSpace(header.Text())

		// Parse header: "Stop X - Pickup/Delivery - CITY, STATE"
		if headerText != "" {
			// Extract stop type and location from header
			parts := strings.Split(headerText, " - ")
			if len(parts) >= 3 {
				// Second part is the stop type (Pickup/Delivery)
				stopTypeText := strings.TrimSpace(parts[1])
				if strings.Contains(strings.ToLower(stopTypeText), "pickup") {
					stop.StopType = "pickup"
				} else if strings.Contains(strings.ToLower(stopTypeText), "delivery") {
					stop.StopType = "delivery"
				}

				// Third part is the city, state
				locationPart := strings.TrimSpace(parts[2])
				if locationPart != "" {
					locationParts := strings.Split(locationPart, ",")
					if len(locationParts) >= 1 {
						stop.City = strings.TrimSpace(locationParts[0])
					}
					if len(locationParts) >= 2 {
						stop.State = strings.TrimSpace(locationParts[1])
					}
				}
			}
		}

		// Extract company name from the clickable link
		companyLink := stopWrapper.Find("a[data-site-id]")
		if companyLink.Length() > 0 {
			stop.Name = strings.TrimSpace(companyLink.Text())
			// Extract the site ID part if needed
			fullText := stop.Name
			if strings.Contains(fullText, " - ") {
				parts := strings.Split(fullText, " - ")
				if len(parts) >= 1 {
					stop.Name = strings.TrimSpace(parts[0])
				}
			}
		}

		// Extract address lines - look for td elements with address patterns
		stopWrapper.Find("td.td_regular_left").Each(func(_ int, td *goquery.Selection) {
			text := strings.TrimSpace(td.Text())
			if text == "" {
				return
			}

			// First non-empty td after company name is usually the street address
			if stop.Address == "" && !strings.Contains(text, ",") {
				stop.Address = text
			} else if strings.Contains(text, ",") && strings.Contains(text, " US") {
				// This looks like "CITY, STATE ZIP US" format - parse it and override header data
				// Remove " US" suffix
				addressLine := strings.TrimSuffix(text, " US")
				parts := strings.Split(addressLine, ",")
				if len(parts) >= 1 {
					stop.City = strings.TrimSpace(parts[0])
				}
				if len(parts) >= 2 {
					stateZipPart := strings.TrimSpace(parts[1])
					stateParts := strings.Fields(stateZipPart)
					if len(stateParts) >= 1 {
						stop.State = stateParts[0]
					}
					if len(stateParts) >= 2 {
						stop.Zip = stateParts[1]
					}
				}
				stop.Country = "US"
			}
		})

		// Extract phone number
		phoneSpan := stopWrapper.Find("span.form-control-static:contains('(')").First()
		if phoneSpan.Length() > 0 {
			stop.Phone = strings.TrimSpace(phoneSpan.Text())
		}

		// Extract reference number (PU# or Delv#)
		refInput := stopWrapper.Find("input[name='primary_reference_number']")
		if refInput.Length() > 0 {
			refValue, _ := refInput.Attr("value")
			stop.RefNumber = strings.TrimSpace(refValue)
		}

		// Extract service type from select dropdown
		serviceSelect := stopWrapper.Find("select[name='service_type_id'] option[selected]")
		if serviceSelect.Length() > 0 {
			stop.ServiceType = strings.TrimSpace(serviceSelect.Text())
		}

		// Extract dates and times
		q.extractStopDates(stopWrapper, &stop)

		// Extract appointment required checkbox
		apptCheckbox := stopWrapper.Find("input[name='appt_needed']")
		if apptCheckbox.Length() > 0 {
			checked, _ := apptCheckbox.Attr("checked")
			stop.AppointmentReq = checked == "checked"
		}

		// Extract stop comments/instructions
		commentsTextarea := stopWrapper.Find("textarea[name='stop_comments']")
		if commentsTextarea.Length() > 0 {
			stop.Instructions = strings.TrimSpace(commentsTextarea.Text())
		}

		log.Debug(ctx, "parsed QuantumEdge stop",
			zap.String("stopID", stop.StopID),
			zap.String("stopType", stop.StopType),
			zap.Int("stopNumber", stop.StopNumber),
			zap.String("name", stop.Name),
			zap.String("city", stop.City),
			zap.String("state", stop.State),
			zap.String("phone", stop.Phone),
			zap.String("refNumber", stop.RefNumber))

		stops = append(stops, stop)
	})

	return stops
}

func (q *QuantumEdge) extractStopDates(stopWrapper *goquery.Selection, stop *LocationInfo) {
	// Extract request dates
	reqFromDateInput := stopWrapper.Find("input[id^='req_from_date']")
	if reqFromDateInput.Length() > 0 {
		value, _ := reqFromDateInput.Attr("value")
		stop.ReadyDate = strings.TrimSpace(value)
	}

	reqFromTimeInput := stopWrapper.Find("input[id^='req_from_time']")
	if reqFromTimeInput.Length() > 0 {
		value, _ := reqFromTimeInput.Attr("value")
		stop.ReadyTime = strings.TrimSpace(value)
	}

	// Extract scheduled appointment dates
	schedFromDateInput := stopWrapper.Find("input[id^='sched_from_date']")
	if schedFromDateInput.Length() > 0 {
		value, _ := schedFromDateInput.Attr("value")
		stop.SchedFromDate = strings.TrimSpace(value)
	}

	schedFromTimeInput := stopWrapper.Find("input[id^='sched_from_time']")
	if schedFromTimeInput.Length() > 0 {
		value, _ := schedFromTimeInput.Attr("value")
		stop.SchedFromTime = strings.TrimSpace(value)
	}

	schedToDateInput := stopWrapper.Find("input[id^='sched_to_date']")
	if schedToDateInput.Length() > 0 {
		value, _ := schedToDateInput.Attr("value")
		stop.SchedToDate = strings.TrimSpace(value)
	}

	schedToTimeInput := stopWrapper.Find("input[id^='sched_to_time']")
	if schedToTimeInput.Length() > 0 {
		value, _ := schedToTimeInput.Attr("value")
		stop.SchedToTime = strings.TrimSpace(value)
	}

	// Extract driver arrival/departure dates
	driverArrivedDateInput := stopWrapper.Find("input[id^='live_start_date']")
	if driverArrivedDateInput.Length() > 0 {
		value, _ := driverArrivedDateInput.Attr("value")
		stop.DriverArrivedDate = strings.TrimSpace(value)
	}

	driverArrivedTimeInput := stopWrapper.Find("input[id^='live_start_time']")
	if driverArrivedTimeInput.Length() > 0 {
		value, _ := driverArrivedTimeInput.Attr("value")
		stop.DriverArrivedTime = strings.TrimSpace(value)
	}

	driverDepartedDateInput := stopWrapper.Find("input[id^='depart_date']")
	if driverDepartedDateInput.Length() > 0 {
		value, _ := driverDepartedDateInput.Attr("value")
		stop.DriverDepartedDate = strings.TrimSpace(value)
	}

	driverDepartedTimeInput := stopWrapper.Find("input[id^='depart_time']")
	if driverDepartedTimeInput.Length() > 0 {
		value, _ := driverDepartedTimeInput.Attr("value")
		stop.DriverDepartedTime = strings.TrimSpace(value)
	}
}

func (q *QuantumEdge) convertStopsToModels(ctx context.Context, parsedStops []LocationInfo) []models.Stop {
	var stops []models.Stop

	for _, stopInfo := range parsedStops {
		stop := models.Stop{
			StopType:          string(q.mapStopType(stopInfo.StopType)),
			StopNumber:        stopInfo.StopNumber,
			ExternalTMSStopID: stopInfo.StopID,
			Address: models.Address{
				Name:         stopInfo.Name,
				AddressLine1: stopInfo.Address,
				AddressLine2: stopInfo.AddressLine2,
				City:         stopInfo.City,
				State:        stopInfo.State,
				Zip:          stopInfo.Zip,
				Country:      stopInfo.Country,
			},
			Phone:        stopInfo.Phone,
			Contact:      stopInfo.Contact,
			Email:        stopInfo.Email,
			RefNumber:    stopInfo.RefNumber,
			ApptRequired: stopInfo.AppointmentReq,
		}

		// Parse appointment and schedule dates
		if stopInfo.ReadyDate != "" {
			if readyTime, err := q.parseDateTime(stopInfo.ReadyDate, stopInfo.ReadyTime); err == nil {
				stop.ReadyTime = models.NullTime{Time: readyTime, Valid: true}
			}
		}

		if stopInfo.SchedFromDate != "" {
			if apptTime, err := q.parseDateTime(stopInfo.SchedFromDate, stopInfo.SchedFromTime); err == nil {
				stop.ApptStartTime = models.NullTime{Time: apptTime, Valid: true}
			}
		}

		if stopInfo.SchedToDate != "" {
			if apptEndTime, err := q.parseDateTime(stopInfo.SchedToDate, stopInfo.SchedToTime); err == nil {
				stop.ApptEndTime = models.NullTime{Time: apptEndTime, Valid: true}
			}
		}

		if stopInfo.DriverArrivedDate != "" {
			if actualStartTime, err := q.parseDateTime(stopInfo.DriverArrivedDate,
				stopInfo.DriverArrivedTime); err == nil {
				stop.ActualStartTime = models.NullTime{Time: actualStartTime, Valid: true}
			}
		}

		if stopInfo.DriverDepartedDate != "" {
			if actualEndTime, err := q.parseDateTime(stopInfo.DriverDepartedDate,
				stopInfo.DriverDepartedTime); err == nil {
				stop.ActualEndTime = models.NullTime{Time: actualEndTime, Valid: true}
			}
		}

		stops = append(stops, stop)
	}

	log.Info(ctx, "converted parsed stops to models", zap.Int("stopsCount", len(stops)))
	return stops
}
