package quantumedge

import (
	"strings"
	"testing"

	"github.com/PuerkitoBio/goquery"
)

func TestGetInputValue(t *testing.T) {
	tests := []struct {
		name      string
		html      string
		fieldName string
		want      string
	}{
		{
			name:      "simple input field",
			html:      `<input name="customer_po_num" value="PO123456" />`,
			fieldName: "customer_po_num",
			want:      "PO123456",
		},
		{
			name:      "empty value",
			html:      `<input name="empty_field" value="" />`,
			fieldName: "empty_field",
			want:      "",
		},
		{
			name:      "whitespace trimmed",
			html:      `<input name="whitespace_field" value="  trimmed  " />`,
			fieldName: "whitespace_field",
			want:      "trimmed",
		},
		{
			name:      "missing field",
			html:      `<input name="other_field" value="value" />`,
			fieldName: "missing_field",
			want:      "",
		},
	}

	q := &QuantumEdge{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			if got := q.getInputValue(doc, tt.fieldName); got != tt.want {
				t.Errorf("getInputValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetSelectValue(t *testing.T) {
	tests := []struct {
		name       string
		html       string
		selectName string
		want       string
	}{
		{
			name: "selected option with explicit attribute",
			html: `<select name="test_select">
				<option value="1">Option 1</option>
				<option value="2" selected="selected">Option 2</option>
				<option value="3">Option 3</option>
			</select>`,
			selectName: "test_select",
			want:       "2",
		},
		{
			name: "selected option with shorthand",
			html: `<select name="test_select">
				<option value="1">Option 1</option>
				<option value="2" selected>Option 2</option>
			</select>`,
			selectName: "test_select",
			want:       "2",
		},
		{
			name: "no selection",
			html: `<select name="test_select">
				<option value="1">Option 1</option>
				<option value="2">Option 2</option>
			</select>`,
			selectName: "test_select",
			want:       "",
		},
	}

	q := &QuantumEdge{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			if got := q.getSelectValue(doc, tt.selectName); got != tt.want {
				t.Errorf("getSelectValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetSelectText(t *testing.T) {
	tests := []struct {
		name       string
		html       string
		selectName string
		want       string
	}{
		{
			name: "selected option text",
			html: `<select name="test_select">
				<option value="1">First Option</option>
				<option value="2" selected="selected">Second Option</option>
			</select>`,
			selectName: "test_select",
			want:       "Second Option",
		},
		{
			name: "whitespace trimmed",
			html: `<select name="test_select">
				<option value="1" selected>  Spaced Option  </option>
			</select>`,
			selectName: "test_select",
			want:       "Spaced Option",
		},
		{
			name: "no selection",
			html: `<select name="test_select">
				<option value="1">Option 1</option>
			</select>`,
			selectName: "test_select",
			want:       "",
		},
	}

	q := &QuantumEdge{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			if got := q.getSelectText(doc, tt.selectName); got != tt.want {
				t.Errorf("getSelectText() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMinInt(t *testing.T) {
	tests := []struct {
		name string
		a    int
		b    int
		want int
	}{
		{"a less than b", 3, 5, 3},
		{"a greater than b", 5, 3, 3},
		{"a equals b", 4, 4, 4},
		{"negative numbers", -2, -5, -5},
		{"zero values", 0, 0, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := minInt(tt.a, tt.b); got != tt.want {
				t.Errorf("minInt() = %v, want %v", got, tt.want)
			}
		})
	}
}
