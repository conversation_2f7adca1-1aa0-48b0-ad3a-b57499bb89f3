package quantumedge

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func (q *QuantumEdge) getLoadData(ctx context.Context, shipmentID string) (*LoadData, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/shipments/view/shipment_main_details.cfm", q.config.Tenant)
	params := url.Values{}
	params.Set("_cf_containerId", "container")
	params.Set("_cf_nodebug", "false")
	params.Set("_cf_nocache", "false")
	params.Set("_cf_clientid", "0")
	params.Set("_cf_rc", "1")
	params.Set("shipment_id", shipmentID)

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch load data: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML response: %w", err)
	}

	loadData := q.parseLoadHTML(ctx, doc, shipmentID)
	return loadData, nil
}

func (q *QuantumEdge) parseLoadHTML(ctx context.Context, doc *goquery.Document,
	shipmentID string) *LoadData {
	loadData := &LoadData{
		ShipmentID: shipmentID,
	}

	// Extract basic shipment fields
	loadData.CustomerPONum = q.getInputValue(doc, "customer_po_num")
	loadData.CustomerRef = q.getInputValue(doc, "customer_reference")
	loadData.BOLNum = q.getInputValue(doc, "bol_num")
	loadData.SealNumber = q.getInputValue(doc, "seal_number")
	loadData.ContainerNumber = q.getInputValue(doc, "imdl_container_number")
	loadData.ChassisNumber = q.getInputValue(doc, "shipment_chassis_number")
	loadData.ContainerPrefix = q.getInputValue(doc, "imdl_container_prefix")
	loadData.ContainerSuffix = q.getInputValue(doc, "imdl_container_suffix")
	loadData.CommodityDesc = q.getInputValue(doc, "commodity_desc")
	loadData.ShipmentValue = q.getInputValue(doc, "shipment_value")

	// Extract weight and dimensions
	loadData.Weight = q.getInputValue(doc, "weight")
	loadData.EmptyContainerWeight = q.getInputValue(doc, "empty_container_weight")
	loadData.Length = q.getInputValue(doc, "load_l")
	loadData.Width = q.getInputValue(doc, "load_w")
	loadData.Height = q.getInputValue(doc, "load_h")

	// Extract weight type from select
	weightTypeID := q.getSelectValue(doc, "weight_type_id")
	loadData.WeightType = q.mapWeightType(weightTypeID)

	// Extract equipment fields
	loadData.EquipmentType = q.getSelectText(doc, "equipment_type_id")

	// Extract mode from shipment_type_id select
	loadData.Mode = q.getSelectText(doc, "shipment_type_id")

	// Extract temperature fields
	loadData.Temperature = q.getInputValue(doc, "temp_amount")

	// Extract customer info from the displayed sections
	loadData.CustomerInfo = q.parseCustomerInfo(ctx, doc)

	log.Debug(ctx, "parsed QuantumEdge load data",
		zap.String("shipmentID", loadData.ShipmentID),
		zap.String("poNum", loadData.CustomerPONum),
		zap.String("mode", loadData.Mode),
		zap.String("status", loadData.Status),
		zap.String("customerName", loadData.CustomerInfo.Name),
		zap.String("commodityDesc", loadData.CommodityDesc[:minInt(50, len(loadData.CommodityDesc))]))

	return loadData
}

func (q *QuantumEdge) getInputValue(doc *goquery.Document, name string) string {
	val, _ := doc.Find(fmt.Sprintf("input[name='%s']", name)).Attr("value")
	return strings.TrimSpace(val)
}

func (q *QuantumEdge) getSelectValue(doc *goquery.Document, name string) string {
	// Try to find selected option, even in disabled selects
	val, exists := doc.Find(fmt.Sprintf("select[name='%s'] option[selected='selected']", name)).Attr("value")
	if !exists || val == "" {
		// Fallback: try without explicit selected value
		val, _ = doc.Find(fmt.Sprintf("select[name='%s'] option[selected]", name)).Attr("value")
	}
	return strings.TrimSpace(val)
}

func (q *QuantumEdge) getSelectText(doc *goquery.Document, name string) string {
	// Try to find selected option, even in disabled selects
	text := doc.Find(fmt.Sprintf("select[name='%s'] option[selected='selected']", name)).Text()
	if text == "" {
		// Fallback: try without explicit selected value
		text = doc.Find(fmt.Sprintf("select[name='%s'] option[selected]", name)).Text()
	}
	return strings.TrimSpace(text)
}

func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}
