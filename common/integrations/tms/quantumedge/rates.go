package quantumedge

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// getLoadRates fetches AR and AP rate data for a shipment
func (q *QuantumEdge) getLoadRates(ctx context.Context, externalTMSID string) (*RateData, error) {
	log.Info(ctx, "fetching load rates", zap.String("shipmentID", externalTMSID))

	// Get shipment accounting details HTML to extract sequences
	doc, err := q.getShipmentAccountingDetails(ctx, externalTMSID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch shipment accounting details: %w", err)
	}

	arSequences, apSequences := q.parseSequencesFromHTML(ctx, doc)
	log.Debug(ctx, "parsed sequences from HTML",
		zap.Ints("arSequences", arSequences),
		zap.Ints("apSequences", apSequences))

	rateData := &RateData{
		ARInvoices: make([]ARInvoiceData, 0, len(arSequences)),
		APInvoices: make([]APInvoiceData, 0, len(apSequences)),
	}

	// Fetch AR (customer) rate details for each sequence
	for _, sequence := range arSequences {
		arData, err := q.getARRateDetails(ctx, externalTMSID, sequence)
		if err != nil {
			log.Warn(ctx, "failed to fetch AR rate details, skipping sequence",
				zap.Int("sequence", sequence),
				zap.Error(err))
			continue
		}
		rateData.ARInvoices = append(rateData.ARInvoices, *arData)
	}

	// Fetch AP (carrier) rate details for each sequence
	for _, sequence := range apSequences {
		apData, err := q.getAPRateDetails(ctx, externalTMSID, sequence)
		if err != nil {
			log.Warn(ctx, "failed to fetch AP rate details, skipping sequence",
				zap.Int("sequence", sequence),
				zap.Error(err))
			continue
		}
		rateData.APInvoices = append(rateData.APInvoices, *apData)
	}

	log.Info(ctx, "successfully fetched load rates",
		zap.String("shipmentID", externalTMSID),
		zap.Int("arInvoicesCount", len(rateData.ARInvoices)),
		zap.Int("apInvoicesCount", len(rateData.APInvoices)))

	return rateData, nil
}

// getShipmentAccountingDetails fetches the shipment accounting details HTML page
func (q *QuantumEdge) getShipmentAccountingDetails(ctx context.Context, shipmentID string) (*goquery.Document, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/shipments/view/shipment_accounting_details_v2.cfm", q.config.Tenant)
	params := url.Values{}
	params.Set("shipment_id", shipmentID)

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch accounting details: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML response: %w", err)
	}

	return doc, nil
}

// parseSequencesFromHTML extracts AR and AP sequence numbers from the HTML
func (q *QuantumEdge) parseSequencesFromHTML(ctx context.Context, doc *goquery.Document) ([]int, []int) {
	var arSequences, apSequences []int

	// Parse AR sequences from billingReleaseARCheckbox elements
	doc.Find(".billingReleaseARCheckbox").Each(func(_ int, s *goquery.Selection) {
		if sequenceStr, exists := s.Attr("data-sequence"); exists {
			if sequence, err := strconv.Atoi(sequenceStr); err == nil {
				arSequences = append(arSequences, sequence)
			} else {
				log.Warn(ctx, "failed to parse AR sequence", zap.String("sequence", sequenceStr))
			}
		}
	})

	// Parse AP sequences from billingReleaseAPCheckbox elements
	doc.Find(".billingReleaseAPCheckbox").Each(func(_ int, s *goquery.Selection) {
		if sequenceStr, exists := s.Attr("data-sequence"); exists {
			if sequence, err := strconv.Atoi(sequenceStr); err == nil {
				apSequences = append(apSequences, sequence)
			} else {
				log.Warn(ctx, "failed to parse AP sequence", zap.String("sequence", sequenceStr))
			}
		}
	})

	return arSequences, apSequences
}

// getARRateDetails fetches customer rate details for a specific sequence
func (q *QuantumEdge) getARRateDetails(ctx context.Context, shipmentID string, sequence int) (*ARInvoiceData, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/shipment_select.cfc", q.config.Tenant)
	params := url.Values{}
	params.Set("method", "select_ar_detail")
	params.Set("shipment_id", shipmentID)
	params.Set("sequence", strconv.Itoa(sequence))
	params.Set("pageSize", "300")
	params.Set("returnFormat", "json")
	params.Set("start", "0")
	params.Set("limit", "300")
	params.Set("page", "1")
	params.Set("gridSortColumn", "")
	params.Set("gridSortDir", "ASC")
	params.Set("_cf_nodebug", "true")
	params.Set("_cf_nocache", "true")

	fullURL := baseURL + "?" + params.Encode()

	headers := map[string]string{
		"Accept":           "*/*",
		"X-Requested-With": "XMLHttpRequest",
		"Referer": fmt.Sprintf("https://%s.edgetms.com/index.cfm?action=shipment_main&shipment_id=%s",
			q.config.Tenant, shipmentID),
	}

	body, err := q.getWithHeaders(ctx, fullURL, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch AR rate details: %w", err)
	}

	var arData ARInvoiceData
	if err := json.Unmarshal(body, &arData); err != nil {
		return nil, fmt.Errorf("failed to parse AR JSON response: %w", err)
	}

	return &arData, nil
}

// getAPRateDetails fetches carrier rate details for a specific sequence
func (q *QuantumEdge) getAPRateDetails(ctx context.Context, shipmentID string, sequence int) (*APInvoiceData, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/shipment_select.cfc", q.config.Tenant)
	params := url.Values{}
	params.Set("method", "select_ap_detail_grid")
	params.Set("shipment_id", shipmentID)
	params.Set("sequence", strconv.Itoa(sequence))
	params.Set("pageSize", "300")
	params.Set("returnFormat", "json")
	params.Set("start", "0")
	params.Set("limit", "300")
	params.Set("page", "1")
	params.Set("gridsortcolumn", "")
	params.Set("gridsortdir", "ASC")
	params.Set("_cf_nodebug", "true")
	params.Set("_cf_nocache", "true")

	fullURL := baseURL + "?" + params.Encode()

	headers := map[string]string{
		"Accept":           "*/*",
		"X-Requested-With": "XMLHttpRequest",
		"Referer": fmt.Sprintf("https://%s.edgetms.com/index.cfm?action=shipment_main&shipment_id=%s",
			q.config.Tenant, shipmentID),
	}

	body, err := q.getWithHeaders(ctx, fullURL, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch AP rate details: %w", err)
	}

	var apData APInvoiceData
	if err := json.Unmarshal(body, &apData); err != nil {
		return nil, fmt.Errorf("failed to parse AP JSON response: %w", err)
	}

	return &apData, nil
}

// convertARDataToLineItems converts QuantumEdge AR data to models.LineItems
func (q *QuantumEdge) convertARDataToLineItems(ctx context.Context, arInvoices []ARInvoiceData) models.LineItems {
	var lineItems models.LineItems

	for _, invoice := range arInvoices {
		for _, row := range invoice.Query.Data {
			if len(row) < len(invoice.Query.Columns) {
				continue
			}

			lineItem := q.parseARLineItemFromRow(ctx, invoice.Query.Columns, row)
			if lineItem != nil {
				accessorialCharge := models.AccessorialCharge{
					Label:          lineItem.LineTypeDesc,
					UnitBasis:      lineItem.UnitTypeName,
					Quantity:       float32(lineItem.UnitQuantity),
					RatePerUnitUSD: float32(lineItem.UnitPrice),
					TotalChargeUSD: float32(lineItem.Amount),
					Note:           lineItem.Note,
				}

				lineItems = append(lineItems, accessorialCharge)
			}
		}
	}

	return lineItems
}

// convertAPDataToLineItems converts QuantumEdge AP data to models.LineItems
func (q *QuantumEdge) convertAPDataToLineItems(ctx context.Context, apInvoices []APInvoiceData) models.LineItems {
	var lineItems models.LineItems

	for _, invoice := range apInvoices {
		for _, row := range invoice.Query.Data {
			if len(row) < len(invoice.Query.Columns) {
				continue
			}

			lineItem := q.parseAPLineItemFromRow(ctx, invoice.Query.Columns, row)
			if lineItem != nil {
				accessorialCharge := models.AccessorialCharge{
					Label:          lineItem.LineTypeDesc,
					UnitBasis:      lineItem.UnitTypeName,
					Quantity:       float32(lineItem.UnitQuantity),
					RatePerUnitUSD: float32(lineItem.UnitPrice),
					TotalChargeUSD: float32(lineItem.Amount),
					Note:           lineItem.Note,
				}

				lineItems = append(lineItems, accessorialCharge)
			}
		}
	}

	return lineItems
}

// parseARLineItemFromRow parses a single AR line item from a data row
func (q *QuantumEdge) parseARLineItemFromRow(_ context.Context, columns []string, row []any) *ARLineItem {
	lineItem := &ARLineItem{}

	for i, col := range columns {
		if i >= len(row) {
			continue
		}

		switch strings.ToUpper(col) {
		case "LINE_ITEM":
			if val, ok := row[i].(float64); ok {
				lineItem.LineItem = int(val)
			}
		case "LINE_TYPE_ID":
			if val, ok := row[i].(float64); ok {
				lineItem.LineTypeID = int(val)
			}
		case "LINE_TYPE_DESC":
			if val, ok := row[i].(string); ok {
				lineItem.LineTypeDesc = val
			}
		case "AMOUNT":
			if val, ok := row[i].(float64); ok {
				lineItem.Amount = val
			}
		case "AMOUNT_EXCH":
			if val, ok := row[i].(float64); ok {
				lineItem.AmountExch = val
			}
		case "UNIT_QUANTITY":
			if val, ok := row[i].(float64); ok {
				lineItem.UnitQuantity = val
			}
		case "UNIT_PRICE":
			if val, ok := row[i].(float64); ok {
				lineItem.UnitPrice = val
			}
		case "UNIT_TYPE_NAME":
			if val, ok := row[i].(string); ok {
				lineItem.UnitTypeName = val
			}
		case "NOTE":
			if val, ok := row[i].(string); ok {
				lineItem.Note = val
			}
		case "ENTER_DATE":
			if val, ok := row[i].(string); ok {
				lineItem.EnterDate = val
			}
		case "ENTER_USER_NAME":
			if val, ok := row[i].(string); ok {
				lineItem.EnterUserName = val
			}
		default:
			// Unknown column, ignore
		}
	}

	return lineItem
}

// parseAPLineItemFromRow parses a single AP line item from a data row
func (q *QuantumEdge) parseAPLineItemFromRow(_ context.Context, columns []string, row []any) *APLineItem {
	lineItem := &APLineItem{}

	for i, col := range columns {
		if i >= len(row) {
			continue
		}

		switch strings.ToUpper(col) {
		case "LINE_ITEM":
			if val, ok := row[i].(float64); ok {
				lineItem.LineItem = int(val)
			}
		case "LINE_TYPE_ID":
			if val, ok := row[i].(float64); ok {
				lineItem.LineTypeID = int(val)
			}
		case "LINE_TYPE_DESC":
			if val, ok := row[i].(string); ok {
				lineItem.LineTypeDesc = val
			}
		case "AMOUNT":
			if val, ok := row[i].(string); ok {
				if amount, err := strconv.ParseFloat(val, 64); err == nil {
					lineItem.Amount = amount
				}
			} else if val, ok := row[i].(float64); ok {
				lineItem.Amount = val
			}
		case "AMOUNT_EXCH":
			if val, ok := row[i].(float64); ok {
				lineItem.AmountExch = val
			}
		case "UNIT_QUANTITY":
			if val, ok := row[i].(float64); ok {
				lineItem.UnitQuantity = val
			}
		case "UNIT_PRICE":
			if val, ok := row[i].(float64); ok {
				lineItem.UnitPrice = val
			}
		case "UNIT_TYPE_NAME":
			if val, ok := row[i].(string); ok {
				lineItem.UnitTypeName = val
			}
		case "NOTE":
			if val, ok := row[i].(string); ok {
				lineItem.Note = val
			}
		case "ENTER_DATE":
			if val, ok := row[i].(string); ok {
				lineItem.EnterDate = val
			}
		case "ENTER_USER_NAME":
			if val, ok := row[i].(string); ok {
				lineItem.EnterUserName = val
			}
		case "SEQUENCE":
			if val, ok := row[i].(float64); ok {
				lineItem.Sequence = int(val)
			}
		case "HEADER_SITE":
			if val, ok := row[i].(string); ok {
				lineItem.HeaderSite = val
			}
		default:
			// Unknown column, ignore
		}
	}

	return lineItem
}
