package quantumedge

import (
	"context"
	"strings"
	"testing"

	"github.com/PuerkitoBio/goquery"
)

func TestParseSequencesFromHTML(t *testing.T) {
	tests := []struct {
		name   string
		html   string
		wantAR []int
		wantAP []int
	}{
		{
			name: "AR and AP sequences",
			html: `<html><body>
				<input class="billingReleaseARCheckbox" data-sequence="1" />
				<input class="billingReleaseARCheckbox" data-sequence="2" />
				<input class="billingReleaseAPCheckbox" data-sequence="3" />
				<input class="billingReleaseAPCheckbox" data-sequence="4" />
			</body></html>`,
			wantAR: []int{1, 2},
			wantAP: []int{3, 4},
		},
		{
			name: "invalid sequence ignored",
			html: `<html><body>
				<input class="billingReleaseARCheckbox" data-sequence="1" />
				<input class="billingReleaseARCheckbox" data-sequence="invalid" />
				<input class="billingReleaseAPCheckbox" data-sequence="2" />
			</body></html>`,
			wantAR: []int{1},
			wantAP: []int{2},
		},
		{
			name:   "no sequences",
			html:   `<html><body></body></html>`,
			wantAR: []int{},
			wantAP: []int{},
		},
	}

	q := &QuantumEdge{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := goquery.NewDocumentFromReader(strings.NewReader(tt.html))
			if err != nil {
				t.Fatalf("Failed to parse HTML: %v", err)
			}

			gotAR, gotAP := q.parseSequencesFromHTML(context.Background(), doc)

			if !equalIntSlices(gotAR, tt.wantAR) {
				t.Errorf("parseSequencesFromHTML() AR = %v, want %v", gotAR, tt.wantAR)
			}
			if !equalIntSlices(gotAP, tt.wantAP) {
				t.Errorf("parseSequencesFromHTML() AP = %v, want %v", gotAP, tt.wantAP)
			}
		})
	}
}

func TestParseARLineItemFromRow(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name    string
		columns []string
		row     []any
		want    *ARLineItem
	}{
		{
			name:    "complete AR line item",
			columns: []string{"LINE_ITEM", "LINE_TYPE_DESC", "AMOUNT", "UNIT_QUANTITY", "UNIT_PRICE", "NOTE"},
			row:     []any{float64(1), "Rail Line-Haul", float64(2945.00), float64(1.0), float64(2945.00), "Test note"},
			want: &ARLineItem{
				LineItem:     1,
				LineTypeDesc: "Rail Line-Haul",
				Amount:       2945.00,
				UnitQuantity: 1.0,
				UnitPrice:    2945.00,
				Note:         "Test note",
			},
		},
		{
			name:    "missing columns",
			columns: []string{"LINE_ITEM", "AMOUNT"},
			row:     []any{float64(2), float64(500.00)},
			want: &ARLineItem{
				LineItem: 2,
				Amount:   500.00,
			},
		},
		{
			name:    "fewer row values than columns",
			columns: []string{"LINE_ITEM", "LINE_TYPE_DESC", "AMOUNT", "NOTE"},
			row:     []any{float64(3), "Fuel Surcharge"},
			want: &ARLineItem{
				LineItem:     3,
				LineTypeDesc: "Fuel Surcharge",
			},
		},
		{
			name:    "non-numeric values for numeric fields",
			columns: []string{"LINE_ITEM", "AMOUNT", "UNIT_QUANTITY"},
			row:     []any{"not-a-number", "invalid-amount", "invalid-quantity"},
			want:    &ARLineItem{},
		},
		{
			name:    "empty row",
			columns: []string{"LINE_ITEM", "AMOUNT"},
			row:     []any{},
			want:    &ARLineItem{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.parseARLineItemFromRow(context.Background(), tt.columns, tt.row)

			if got.LineItem != tt.want.LineItem {
				t.Errorf("parseARLineItemFromRow() LineItem = %v, want %v", got.LineItem, tt.want.LineItem)
			}
			if got.LineTypeDesc != tt.want.LineTypeDesc {
				t.Errorf("parseARLineItemFromRow() LineTypeDesc = %v, want %v", got.LineTypeDesc, tt.want.LineTypeDesc)
			}
			if got.Amount != tt.want.Amount {
				t.Errorf("parseARLineItemFromRow() Amount = %v, want %v", got.Amount, tt.want.Amount)
			}
			if got.UnitQuantity != tt.want.UnitQuantity {
				t.Errorf("parseARLineItemFromRow() UnitQuantity = %v, want %v", got.UnitQuantity, tt.want.UnitQuantity)
			}
			if got.UnitPrice != tt.want.UnitPrice {
				t.Errorf("parseARLineItemFromRow() UnitPrice = %v, want %v", got.UnitPrice, tt.want.UnitPrice)
			}
			if got.Note != tt.want.Note {
				t.Errorf("parseARLineItemFromRow() Note = %v, want %v", got.Note, tt.want.Note)
			}
		})
	}
}

func TestParseAPLineItemFromRow(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name    string
		columns []string
		row     []any
		want    *APLineItem
	}{
		{
			name: "complete AP line item",
			columns: []string{"LINE_ITEM", "LINE_TYPE_ID", "LINE_TYPE_DESC", "AMOUNT", "AMOUNT_EXCH",
				"UNIT_QUANTITY", "UNIT_PRICE", "UNIT_TYPE_NAME", "ENTER_DATE", "ENTER_USER_NAME",
				"NOTE", "SEQUENCE"},
			row: []any{float64(1), float64(5), "Insurance", "20.00", float64(19.50),
				float64(2.0), float64(10.00), "Days", "2025-09-16", "John Doe", "Test note",
				float64(15)},
			want: &APLineItem{
				LineItem:      1,
				LineTypeID:    5,
				LineTypeDesc:  "Insurance",
				Amount:        20.00,
				AmountExch:    19.50,
				UnitQuantity:  2.0,
				UnitPrice:     10.00,
				UnitTypeName:  "Days",
				EnterDate:     "2025-09-16",
				EnterUserName: "John Doe",
				Note:          "Test note",
				Sequence:      15,
			},
		},
		{
			name:    "basic AP line item",
			columns: []string{"LINE_ITEM", "LINE_TYPE_DESC", "AMOUNT", "SEQUENCE"},
			row:     []any{float64(1), "Insurance", "20.00", float64(15)},
			want: &APLineItem{
				LineItem:     1,
				LineTypeDesc: "Insurance",
				Amount:       20.00,
				Sequence:     15,
			},
		},
		{
			name:    "missing columns",
			columns: []string{"LINE_ITEM", "AMOUNT"},
			row:     []any{float64(2), float64(500.00)},
			want: &APLineItem{
				LineItem: 2,
				Amount:   500.00,
			},
		},
		{
			name:    "fewer row values than columns",
			columns: []string{"LINE_ITEM", "LINE_TYPE_DESC", "AMOUNT", "NOTE"},
			row:     []any{float64(3), "Fuel Surcharge"},
			want: &APLineItem{
				LineItem:     3,
				LineTypeDesc: "Fuel Surcharge",
			},
		},
		{
			name:    "non-numeric values for numeric fields",
			columns: []string{"LINE_ITEM", "AMOUNT", "SEQUENCE"},
			row:     []any{"not-a-number", "invalid-amount", "invalid-sequence"},
			want:    &APLineItem{},
		},
		{
			name:    "empty row",
			columns: []string{"LINE_ITEM", "AMOUNT"},
			row:     []any{},
			want:    &APLineItem{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.parseAPLineItemFromRow(context.Background(), tt.columns, tt.row)

			if got.LineItem != tt.want.LineItem {
				t.Errorf("parseAPLineItemFromRow() LineItem = %v, want %v", got.LineItem, tt.want.LineItem)
			}
			if got.LineTypeID != tt.want.LineTypeID {
				t.Errorf("parseAPLineItemFromRow() LineTypeID = %v, want %v", got.LineTypeID, tt.want.LineTypeID)
			}
			if got.LineTypeDesc != tt.want.LineTypeDesc {
				t.Errorf("parseAPLineItemFromRow() LineTypeDesc = %v, want %v", got.LineTypeDesc, tt.want.LineTypeDesc)
			}
			if got.Amount != tt.want.Amount {
				t.Errorf("parseAPLineItemFromRow() Amount = %v, want %v", got.Amount, tt.want.Amount)
			}
			if got.AmountExch != tt.want.AmountExch {
				t.Errorf("parseAPLineItemFromRow() AmountExch = %v, want %v", got.AmountExch, tt.want.AmountExch)
			}
			if got.UnitQuantity != tt.want.UnitQuantity {
				t.Errorf("parseAPLineItemFromRow() UnitQuantity = %v, want %v", got.UnitQuantity, tt.want.UnitQuantity)
			}
			if got.UnitPrice != tt.want.UnitPrice {
				t.Errorf("parseAPLineItemFromRow() UnitPrice = %v, want %v", got.UnitPrice, tt.want.UnitPrice)
			}
			if got.UnitTypeName != tt.want.UnitTypeName {
				t.Errorf("parseAPLineItemFromRow() UnitTypeName = %v, want %v", got.UnitTypeName, tt.want.UnitTypeName)
			}
			if got.EnterDate != tt.want.EnterDate {
				t.Errorf("parseAPLineItemFromRow() EnterDate = %v, want %v", got.EnterDate, tt.want.EnterDate)
			}
			if got.EnterUserName != tt.want.EnterUserName {
				t.Errorf("parseAPLineItemFromRow() EnterUserName = %v, want %v",
					got.EnterUserName, tt.want.EnterUserName)
			}
			if got.Note != tt.want.Note {
				t.Errorf("parseAPLineItemFromRow() Note = %v, want %v", got.Note, tt.want.Note)
			}
			if got.Sequence != tt.want.Sequence {
				t.Errorf("parseAPLineItemFromRow() Sequence = %v, want %v", got.Sequence, tt.want.Sequence)
			}
		})
	}
}

// Helper function to compare int slices
func equalIntSlices(a, b []int) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}
