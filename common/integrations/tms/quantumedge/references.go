package quantumedge

import (
	"github.com/drumkitai/drumkit/common/models"
)

func (q *QuantumEdge) buildAdditionalReferences(loadData *LoadData) models.AdditionalReferences {
	var refs models.AdditionalReferences

	if loadData.BOLNum != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "BOL",
			Number:    loadData.BOLNum,
		})
	}

	if loadData.ContainerNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "CONTAINER",
			Number:    loadData.ContainerNumber,
		})
	}

	if loadData.SealNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "SEAL",
			Number:    loadData.SealNumber,
		})
	}

	if loadData.ChassisNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "CHASSIS",
			Number:    loadData.ChassisNumber,
		})
	}

	return refs
}
