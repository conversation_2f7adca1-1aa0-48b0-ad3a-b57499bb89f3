package quantumedge

import (
	"context"
	"strings"

	"github.com/PuerkitoBio/goquery"
)

func (q *QuantumEdge) parseCustomerInfo(_ context.Context, doc *goquery.Document) CustomerInfo {
	customer := CustomerInfo{}
	customerDivs := doc.Find(".form-control-static.small")

	if customerDivs.Length() >= 5 {
		if customerDivs.Length() > 0 {
			customer.Name = strings.TrimSpace(customerDivs.Eq(0).Text())
		}

		if customerDivs.Length() > 1 {
			customer.Address = strings.TrimSpace(customerDivs.Eq(1).Text())
		}

		if customerDivs.Length() > 2 {
			cityStateZip := strings.TrimSpace(customerDivs.Eq(2).Text())
			q.parseCityStateZip(cityStateZip, &customer)
		}

		if customerDivs.Length() > 3 {
			customer.Phone = strings.TrimSpace(customerDivs.Eq(3).Text())
		}

		if customerDivs.Length() > 4 {
			customer.Contact = strings.TrimSpace(customerDivs.Eq(4).Text())
		}
	}

	return customer
}

func (q *QuantumEdge) parseCityStateZip(cityStateZip string, customer *CustomerInfo) {
	if cityStateZip == "" {
		return
	}

	parts := strings.Split(cityStateZip, ",")
	if len(parts) >= 1 {
		customer.City = strings.TrimSpace(parts[0])
	}

	if len(parts) >= 2 {
		stateZipPart := strings.TrimSpace(parts[1])
		stateParts := strings.Fields(stateZipPart)
		if len(stateParts) >= 1 {
			customer.State = stateParts[0]
		}
		if len(stateParts) >= 2 {
			customer.Zip = stateParts[1]
		}
	}
}
