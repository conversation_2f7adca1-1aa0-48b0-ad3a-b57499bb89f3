package quantumedge

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (q *QuantumEdge) GetLoad(ctx context.Context, externalTMSID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(q.tms), attribute.String("external_tms_id", externalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadQuantumEdge", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "quantumedge.GetLoad", zap.String("shipmentID", externalTMSID))

	// Use errgroup for concurrent data fetching
	g, ctx := errgroup.WithContext(ctx)

	// Results storage
	var loadData *LoadData
	var stops []LocationInfo
	var notes []Note
	var rateData *RateData

	// Load data is required - if this fails, we return error
	g.Go(func() error {
		var loadErr error
		loadData, loadErr = q.getLoadData(ctx, externalTMSID)
		return loadErr
	})

	// Optional data - if these fail, we log warnings but continue
	g.Go(func() error {
		stopsDoc, stopsErr := q.getStopsData(ctx, externalTMSID)
		if stopsErr != nil {
			return stopsErr
		}
		stops = q.parseStopsHTML(ctx, stopsDoc)
		log.Info(ctx, "successfully fetched stops data", zap.Int("stopsCount", len(stops)))
		return nil
	})

	g.Go(func() error {
		var notesErr error
		notes, notesErr = q.getLoadNotes(ctx, externalTMSID)
		if notesErr != nil {
			log.Warn(ctx, "failed to fetch notes data, continuing with main load data", zap.Error(notesErr))
			return nil // Don't fail the group for optional data
		}
		log.Info(ctx, "successfully fetched notes data", zap.Int("notesCount", len(notes)))
		return nil
	})

	g.Go(func() error {
		var rateErr error
		rateData, rateErr = q.getLoadRates(ctx, externalTMSID)
		if rateErr != nil {
			log.Warn(ctx, "failed to fetch rate data, continuing with main load data", zap.Error(rateErr))
			return nil // Don't fail the group for optional data
		}
		log.Info(ctx, "successfully fetched rate data",
			zap.Int("arInvoicesCount", len(rateData.ARInvoices)),
			zap.Int("apInvoicesCount", len(rateData.APInvoices)))
		return nil
	})

	// Wait for all goroutines to complete
	if err = g.Wait(); err != nil {
		return models.Load{}, models.LoadAttributes{}, fmt.Errorf("error fetching load data: %w", err)
	}

	// Assemble the final load data
	loadData.ParsedStops = stops
	loadData.Notes = notes
	loadData.RateData = rateData

	load := q.convertToModelLoad(ctx, loadData, externalTMSID)
	attrs := models.LoadAttributes{}

	return load, attrs, nil
}

func (q *QuantumEdge) convertToModelLoad(ctx context.Context, loadData *LoadData,
	externalTMSID string) models.Load {
	load := models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Status:   loadData.Status,
			Mode:     models.LoadMode(loadData.Mode),
			PONums:   loadData.CustomerPONum,
			Operator: loadData.CarrierName,
		},
		ServiceID:         q.tms.ServiceID,
		TMSID:             q.tms.ID,
		ExternalTMSID:     externalTMSID,
		FreightTrackingID: externalTMSID,
	}

	load.AdditionalReferences = q.buildAdditionalReferences(loadData)

	load.Customer = models.Customer{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         loadData.CustomerInfo.Name,
			AddressLine1: loadData.CustomerInfo.Address,
			City:         loadData.CustomerInfo.City,
			State:        loadData.CustomerInfo.State,
			Zipcode:      loadData.CustomerInfo.Zip,
			Phone:        loadData.CustomerInfo.Phone,
			Contact:      loadData.CustomerInfo.Contact,
		},
		RefNumber: loadData.CustomerRef,
	}

	// Set bill-to information (same as customer for now)
	load.BillTo = models.BillTo{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         loadData.CustomerInfo.Name,
			AddressLine1: loadData.CustomerInfo.Address,
			City:         loadData.CustomerInfo.City,
			State:        loadData.CustomerInfo.State,
			Zipcode:      loadData.CustomerInfo.Zip,
			Phone:        loadData.CustomerInfo.Phone,
			Contact:      loadData.CustomerInfo.Contact,
		},
	}

	load.Specifications.TotalWeight = q.parseWeight(loadData.Weight, loadData.WeightType)
	load.Specifications.ServiceType = loadData.ServiceType
	load.Specifications.TransportType = loadData.EquipmentType
	load.Specifications.TransportSize = loadData.EquipmentLength + " ft"
	load.Specifications.Commodities = loadData.CommodityDesc

	if loadData.SalesRep != "" {
		load.RateData.Salesperson1 = loadData.SalesRep
	} else if loadData.AccountManager != "" {
		load.RateData.Salesperson1 = loadData.AccountManager
	}

	// Populate rate data if available
	if loadData.RateData != nil {
		// Convert AR (customer) invoices to line items
		if len(loadData.RateData.ARInvoices) > 0 {
			load.RateData.CustomerLineItems = q.convertARDataToLineItems(ctx, loadData.RateData.ARInvoices)

			// Calculate total customer charge from all line items
			var totalCharge float32
			for _, item := range load.RateData.CustomerLineItems {
				totalCharge += item.TotalChargeUSD
			}
			if totalCharge > 0 {
				load.RateData.CustomerTotalCharge = models.ValueUnit{
					Val:  totalCharge,
					Unit: "USD",
				}
			}
		}

		// Convert AP (carrier) invoices to line items
		if len(loadData.RateData.APInvoices) > 0 {
			load.RateData.CarrierLineItems = q.convertAPDataToLineItems(ctx, loadData.RateData.APInvoices)

			// Calculate total carrier cost from all line items
			var totalCost float32
			for _, item := range load.RateData.CarrierLineItems {
				totalCost += item.TotalChargeUSD
			}
			if totalCost > 0 {
				load.RateData.CarrierCost = &totalCost
				load.RateData.CarrierCostCurrency = "USD"
			}
		}
	}

	var stops []models.Stop
	if len(loadData.ParsedStops) > 0 {
		stops = q.convertStopsToModels(ctx, loadData.ParsedStops)
	}
	load.Stops = stops

	// First pickup stop -> Pickup, Last dropoff stop -> Consignee
	if len(stops) > 0 {
		firstPickup, lastDropoff := q.findPickupAndConsigneeStops(stops)

		if firstPickup != nil {
			load.Pickup = models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         firstPickup.Address.Name,
					AddressLine1: firstPickup.Address.AddressLine1,
					City:         firstPickup.Address.City,
					State:        firstPickup.Address.State,
					Zipcode:      firstPickup.Address.Zip,
					Phone:        firstPickup.Phone,
					Contact:      firstPickup.Contact,
				},
			}
			log.Debug(ctx, "populated pickup from first pickup stop",
				zap.String("pickupName", firstPickup.Address.Name),
				zap.Int("stopNumber", firstPickup.StopNumber))
		}

		if lastDropoff != nil {
			load.Consignee = models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name:         lastDropoff.Address.Name,
					AddressLine1: lastDropoff.Address.AddressLine1,
					City:         lastDropoff.Address.City,
					State:        lastDropoff.Address.State,
					Zipcode:      lastDropoff.Address.Zip,
					Phone:        lastDropoff.Phone,
					Contact:      lastDropoff.Contact,
				},
			}
			log.Debug(ctx, "populated consignee from last dropoff stop",
				zap.String("consigneeName", lastDropoff.Address.Name),
				zap.Int("stopNumber", lastDropoff.StopNumber))
		}
	}

	if len(load.Stops) > 2 {
		load.MoreThanTwoStops = true
	}

	if len(loadData.Notes) > 0 {
		for _, note := range loadData.Notes {
			t, err := time.Parse("2006-01-02 15:04:05.0", note.CreatedAt)
			var nt models.NullTime
			if err == nil {
				nt = models.NullTime{Time: t, Valid: true}
			} else {
				nt = models.NullTime{Valid: false}
			}
			load.Notes = append(load.Notes, models.Note{
				Note:      note.Content,
				CreatedAt: nt,
				UpdatedBy: note.User,
				Source:    note.Source,
				Type:      note.Type,
				Leg:       note.ShipmentLeg,
			})
		}
	}

	log.Info(ctx, "converted QuantumEdge load data to models.Load",
		zap.String("freightTrackingID", load.FreightTrackingID),
		zap.String("customer", load.Customer.Name),
		zap.Int("stopsCount", len(load.Stops)))

	return load
}

// findPickupAndConsigneeStops finds the first pickup stop and last dropoff stop from the stops list
func (q *QuantumEdge) findPickupAndConsigneeStops(stops []models.Stop) (*models.Stop, *models.Stop) {
	var firstPickup *models.Stop
	var lastDropoff *models.Stop

	// Find first pickup stop
	for i := range stops {
		if strings.TrimSpace(strings.ToLower(stops[i].StopType)) == "pickup" {
			firstPickup = &stops[i]
			break
		}
	}

	// Find last dropoff stop
	for i := len(stops) - 1; i >= 0; i-- {
		if strings.TrimSpace(strings.ToLower(stops[i].StopType)) == "delivery" ||
			strings.TrimSpace(strings.ToLower(stops[i].StopType)) == "dropoff" {
			lastDropoff = &stops[i]
			break
		}
	}

	return firstPickup, lastDropoff
}
