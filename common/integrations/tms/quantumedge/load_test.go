package quantumedge

import (
	"context"
	"testing"

	"github.com/drumkitai/drumkit/common/models"
)

func TestFindPickupAndConsigneeStops(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name              string
		stops             []models.Stop
		wantPickupName    string
		wantConsigneeName string
	}{
		{
			name: "normal pickup and delivery stops",
			stops: []models.Stop{
				{StopNumber: 1, StopType: "pickup", Address: models.Address{Name: "Pickup Location"}},
				{StopNumber: 2, StopType: "delivery", Address: models.Address{Name: "Dropoff 1"}},
				{StopNumber: 3, StopType: "delivery", Address: models.Address{Name: "Final Dropoff"}},
			},
			wantPickupName:    "Pickup Location",
			wantConsigneeName: "Final Dropoff", // Should get the last dropoff
		},
		{
			name: "multiple pickups - should get first",
			stops: []models.Stop{
				{StopNumber: 1, StopType: "pickup", Address: models.Address{Name: "First Pickup"}},
				{StopNumber: 2, StopType: "pickup", Address: models.Address{Name: "Second Pickup"}},
				{StopNumber: 3, StopType: "delivery", Address: models.Address{Name: "Delivery"}},
			},
			wantPickupName:    "First Pickup",
			wantConsigneeName: "Delivery",
		},
		{
			name: "no pickup stops",
			stops: []models.Stop{
				{StopNumber: 1, StopType: "delivery", Address: models.Address{Name: "Delivery Only"}},
			},
			wantPickupName:    "",
			wantConsigneeName: "Delivery Only",
		},
		{
			name:              "empty stops",
			stops:             []models.Stop{},
			wantPickupName:    "",
			wantConsigneeName: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pickup, consignee := q.findPickupAndConsigneeStops(tt.stops)

			var gotPickupName, gotConsigneeName string
			if pickup != nil {
				gotPickupName = pickup.Address.Name
			}
			if consignee != nil {
				gotConsigneeName = consignee.Address.Name
			}

			if gotPickupName != tt.wantPickupName {
				t.Errorf("findPickupAndConsigneeStops() pickup name = %v, want %v", gotPickupName, tt.wantPickupName)
			}
			if gotConsigneeName != tt.wantConsigneeName {
				t.Errorf("findPickupAndConsigneeStops() consignee name = %v, want %v",
					gotConsigneeName, tt.wantConsigneeName)
			}
		})
	}
}

func TestConvertToModelLoad(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	tests := []struct {
		name     string
		loadData *LoadData
		want     map[string]any
	}{
		{
			name: "basic load conversion",
			loadData: &LoadData{
				ShipmentID:    "12345",
				Status:        "In Transit",
				Mode:          "TL",
				CustomerPONum: "PO123456",
				Weight:        "25000",
				WeightType:    "lbs",
				BOLNum:        "BOL789",
				CustomerInfo: CustomerInfo{
					Name:    "Test Customer Corp",
					Address: "123 Main St",
					City:    "New York",
					State:   "NY",
					Zip:     "10001",
				},
				SalesRep: "John Sales",
			},
			want: map[string]any{
				"ExternalTMSID":     "12345",
				"FreightTrackingID": "12345",
				"Status":            "In Transit",
				"Mode":              models.LoadMode("TL"),
				"PONums":            "PO123456",
				"CustomerName":      "Test Customer Corp",
				"CustomerCity":      "New York",
				"CustomerState":     "NY",
				"Weight":            float32(25000),
				"WeightUnit":        "lbs",
				"Salesperson":       "John Sales",
				"ReferencesCount":   1,
			},
		},
		{
			name: "load with account manager fallback",
			loadData: &LoadData{
				ShipmentID:     "12345",
				SalesRep:       "",
				AccountManager: "Account Manager Name",
			},
			want: map[string]any{
				"Salesperson": "Account Manager Name",
			},
		},
		{
			name: "load with invalid weight",
			loadData: &LoadData{
				ShipmentID: "12345",
				Weight:     "invalid-weight",
				WeightType: "lbs",
			},
			want: map[string]any{
				"Weight":     float32(0),
				"WeightUnit": "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.convertToModelLoad(ctx, tt.loadData, tt.loadData.ShipmentID)

			for field, expectedValue := range tt.want {
				var actualValue any

				switch field {
				case "ExternalTMSID":
					actualValue = got.ExternalTMSID
				case "FreightTrackingID":
					actualValue = got.FreightTrackingID
				case "Status":
					actualValue = got.Status
				case "Mode":
					actualValue = got.Mode
				case "PONums":
					actualValue = got.PONums
				case "CustomerName":
					actualValue = got.Customer.Name
				case "CustomerCity":
					actualValue = got.Customer.City
				case "CustomerState":
					actualValue = got.Customer.State
				case "Weight":
					actualValue = got.Specifications.TotalWeight.Val
				case "WeightUnit":
					actualValue = got.Specifications.TotalWeight.Unit
				case "Salesperson":
					actualValue = got.RateData.Salesperson1
				case "ReferencesCount":
					actualValue = len(got.AdditionalReferences)
				default:
					t.Fatalf("Unknown field: %s", field)
				}

				if actualValue != expectedValue {
					t.Errorf("convertToModelLoad() %s = %v, want %v", field, actualValue, expectedValue)
				}
			}
		})
	}
}

func TestConvertToModelLoadWithStops(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	loadData := &LoadData{
		ShipmentID: "12345",
		ParsedStops: []LocationInfo{
			{
				StopID:     "1",
				StopNumber: 1,
				StopType:   "pickup",
				Name:       "Origin Company",
				Address:    "123 Origin St",
				City:       "Chicago",
				State:      "IL",
			},
			{
				StopID:     "2",
				StopNumber: 2,
				StopType:   "delivery",
				Name:       "Destination Company",
				Address:    "456 Dest Ave",
				City:       "Los Angeles",
				State:      "CA",
			},
		},
	}

	got := q.convertToModelLoad(ctx, loadData, "12345")

	// Test that stops were converted
	if len(got.Stops) != 2 {
		t.Errorf("convertToModelLoad() stops len = %v, want 2", len(got.Stops))
	}

	// Test that pickup and consignee were set from stops
	if got.Pickup.Name != "Origin Company" {
		t.Errorf("convertToModelLoad() pickup name = %v, want 'Origin Company'", got.Pickup.Name)
	}
	if got.Consignee.Name != "Destination Company" {
		t.Errorf("convertToModelLoad() consignee name = %v, want 'Destination Company'", got.Consignee.Name)
	}
}

func TestConvertToModelLoadWithNotes(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	loadData := &LoadData{
		ShipmentID: "12345",
		Notes: []Note{
			{
				ID:        "123",
				Content:   "Test note 1",
				User:      "John Doe",
				Type:      "General",
				Source:    "TMS",
				CreatedAt: "2025-09-16 10:00:00.0",
			},
			{
				ID:        "124",
				Content:   "Test note 2",
				User:      "Jane Smith",
				CreatedAt: "invalid-date", // Should handle invalid date gracefully
			},
		},
	}

	got := q.convertToModelLoad(ctx, loadData, "12345")

	// Test that notes were converted
	if len(got.Notes) != 2 {
		t.Errorf("convertToModelLoad() notes len = %v, want 2", len(got.Notes))
	}

	// Test first note
	note1 := got.Notes[0]
	if note1.Note != "Test note 1" {
		t.Errorf("convertToModelLoad() note1 content = %v, want 'Test note 1'", note1.Note)
	}
	if note1.UpdatedBy != "John Doe" {
		t.Errorf("convertToModelLoad() note1 user = %v, want 'John Doe'", note1.UpdatedBy)
	}
	if !note1.CreatedAt.Valid {
		t.Error("convertToModelLoad() note1 should have valid CreatedAt")
	}

	// Test second note with invalid date
	note2 := got.Notes[1]
	if note2.CreatedAt.Valid {
		t.Error("convertToModelLoad() note2 should have invalid CreatedAt due to bad date format")
	}
}

func TestConvertToModelLoadWithCarrierInfo(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	loadData := &LoadData{
		ShipmentID:    "12345",
		CarrierName:   "Test Carrier Inc",
		DriverName:    "John Driver",
		TractorNumber: "TRACTOR123",
		TrailerNumber: "TRAILER456",
	}

	got := q.convertToModelLoad(ctx, loadData, "12345")

	if got.Operator != "Test Carrier Inc" {
		t.Errorf("convertToModelLoad() Operator = %v, want 'Test Carrier Inc'", got.Operator)
	}
}

func TestConvertToModelLoadWithEquipmentType(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	tests := []struct {
		name          string
		equipmentType string
		wantMode      string
	}{
		{
			name:          "dry van equipment",
			equipmentType: "Dry Van",
			wantMode:      "TL",
		},
		{
			name:          "reefer equipment",
			equipmentType: "Reefer",
			wantMode:      "TL",
		},
		{
			name:          "flatbed equipment",
			equipmentType: "Flatbed",
			wantMode:      "TL",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			loadData := &LoadData{
				ShipmentID:    "12345",
				EquipmentType: tt.equipmentType,
				Mode:          tt.wantMode,
			}

			got := q.convertToModelLoad(ctx, loadData, "12345")

			if string(got.Mode) != tt.wantMode {
				t.Errorf("convertToModelLoad() Mode = %v, want %v", got.Mode, tt.wantMode)
			}
		})
	}
}

func TestConvertToModelLoadWithComplexData(t *testing.T) {
	q := &QuantumEdge{}
	ctx := context.Background()

	loadData := &LoadData{
		ShipmentID:      "COMPLEX123",
		Status:          "Delivered",
		Mode:            "LTL",
		CustomerPONum:   "PO999",
		Weight:          "15000",
		WeightType:      "lbs",
		BOLNum:          "BOL999",
		ContainerNumber: "CONTAINER999",
		SealNumber:      "SEAL999",
		ChassisNumber:   "CHASSIS999",
		CustomerInfo: CustomerInfo{
			Name:    "Complex Customer",
			Address: "456 Complex Ave",
			City:    "Complex City",
			State:   "CC",
			Zip:     "99999",
			Phone:   "(*************",
			Contact: "John Complex",
		},
		CarrierName:    "Complex Carrier",
		SalesRep:       "Jane Sales",
		AccountManager: "Bob Manager",
		ParsedStops: []LocationInfo{
			{
				StopID:     "stop1",
				StopNumber: 1,
				StopType:   "pickup",
				Name:       "Origin Complex",
				ReadyDate:  "09/16/2025",
				ReadyTime:  "08:00",
			},
			{
				StopID:     "stop2",
				StopNumber: 2,
				StopType:   "delivery",
				Name:       "Dest Complex",
				ReadyDate:  "09/17/2025",
				ReadyTime:  "17:00",
			},
		},
		Notes: []Note{
			{
				ID:        "note1",
				Content:   "Complex note",
				User:      "Complex User",
				CreatedAt: "2025-09-16 10:00:00.0",
			},
		},
		RateData: &RateData{},
	}

	got := q.convertToModelLoad(ctx, loadData, "COMPLEX123")

	if got.ExternalTMSID != "COMPLEX123" {
		t.Errorf("convertToModelLoad() ExternalTMSID = %v, want 'COMPLEX123'", got.ExternalTMSID)
	}
	if got.Status != "Delivered" {
		t.Errorf("convertToModelLoad() Status = %v, want 'Delivered'", got.Status)
	}
	if string(got.Mode) != "LTL" {
		t.Errorf("convertToModelLoad() Mode = %v, want 'LTL'", got.Mode)
	}

	if got.Customer.Name != "Complex Customer" {
		t.Errorf("convertToModelLoad() Customer.Name = %v, want 'Complex Customer'", got.Customer.Name)
	}

	if got.Specifications.TotalWeight.Val != 15000 {
		t.Errorf("convertToModelLoad() Weight = %v, want 15000", got.Specifications.TotalWeight.Val)
	}

	if len(got.AdditionalReferences) != 4 {
		t.Errorf("convertToModelLoad() AdditionalReferences len = %v, want 4", len(got.AdditionalReferences))
	}

	if len(got.Stops) != 2 {
		t.Errorf("convertToModelLoad() Stops len = %v, want 2", len(got.Stops))
	}

	if got.Pickup.Name != "Origin Complex" {
		t.Errorf("convertToModelLoad() Pickup.Name = %v, want 'Origin Complex'", got.Pickup.Name)
	}
	if got.Consignee.Name != "Dest Complex" {
		t.Errorf("convertToModelLoad() Consignee.Name = %v, want 'Dest Complex'", got.Consignee.Name)
	}

	if len(got.Notes) != 1 {
		t.Errorf("convertToModelLoad() Notes len = %v, want 1", len(got.Notes))
	}

	if got.RateData.Salesperson1 != "Jane Sales" {
		t.Errorf("convertToModelLoad() Salesperson1 = %v, want 'Jane Sales'", got.RateData.Salesperson1)
	}
}
