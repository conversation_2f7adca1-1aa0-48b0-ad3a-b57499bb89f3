package quantumedge

import (
	"testing"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

func TestMapWeightType(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name         string
		weightTypeID string
		want         string
	}{
		{"LBS", "1", models.LbsUnit},
		{"KG", "2", models.KgUnit},
		{"Unknown defaults to LBS", "999", models.LbsUnit},
		{"Empty defaults to LBS", "", models.LbsUnit},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := q.mapWeightType(tt.weightTypeID); got != tt.want {
				t.Errorf("mapWeightType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapMode(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name   string
		modeID string
		want   models.LoadMode
	}{
		{"IMDL", "1", models.DrayageMode},
		{"TL", "2", models.TLMode},
		{"LTL", "3", models.LTLMode},
		{"Air", "4", models.AirMode},
		{"Rail/Ocean", "5", models.RailMode},
		{"Unknown defaults to Drayage", "999", models.DrayageMode},
		{"Empty defaults to Drayage", "", models.DrayageMode},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := q.mapMode(tt.modeID); got != tt.want {
				t.Errorf("mapMode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapStopType(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name     string
		stopType string
		want     models.StopType
	}{
		{"Pickup", "pickup", models.PickupStop},
		{"Pickup capitalized", "Pickup", models.PickupStop},
		{"Pickup uppercase", "PICKUP", models.PickupStop},
		{"Delivery", "delivery", models.DropoffStop},
		{"Delivery capitalized", "Delivery", models.DropoffStop},
		{"Delivery uppercase", "DELIVERY", models.DropoffStop},
		{"Unknown passes through", "unknown", models.StopType("unknown")},
		{"Empty passes through", "", models.StopType("")},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := q.mapStopType(tt.stopType); got != tt.want {
				t.Errorf("mapStopType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseWeight(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name      string
		weightStr string
		unitStr   string
		want      models.ValueUnit
	}{
		{
			name:      "Valid weight with LBS unit",
			weightStr: "25000",
			unitStr:   models.LbsUnit,
			want:      models.ValueUnit{Val: 25000, Unit: models.LbsUnit},
		},
		{
			name:      "Valid weight with KG unit",
			weightStr: "11340",
			unitStr:   models.KgUnit,
			want:      models.ValueUnit{Val: 11340, Unit: models.KgUnit},
		},
		{
			name:      "Valid weight with empty unit defaults to LBS",
			weightStr: "30000",
			unitStr:   "",
			want:      models.ValueUnit{Val: 30000, Unit: models.LbsUnit},
		},
		{
			name:      "Decimal weight",
			weightStr: "25000.5",
			unitStr:   models.LbsUnit,
			want:      models.ValueUnit{Val: 25000.5, Unit: models.LbsUnit},
		},
		{
			name:      "Empty weight returns empty ValueUnit",
			weightStr: "",
			unitStr:   models.LbsUnit,
			want:      models.ValueUnit{},
		},
		{
			name:      "Invalid weight returns empty ValueUnit",
			weightStr: "not-a-number",
			unitStr:   models.LbsUnit,
			want:      models.ValueUnit{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := q.parseWeight(tt.weightStr, tt.unitStr)
			if got.Val != tt.want.Val || got.Unit != tt.want.Unit {
				t.Errorf("parseWeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseDateTime(t *testing.T) {
	q := &QuantumEdge{}

	tests := []struct {
		name    string
		dateStr string
		timeStr string
		want    time.Time
		wantErr bool
	}{
		{
			name:    "Date and time with 24-hour format",
			dateStr: "09/16/2025",
			timeStr: "15:30",
			want:    time.Date(2025, 9, 16, 15, 30, 0, 0, time.UTC),
			wantErr: false,
		},
		{
			name:    "Date only",
			dateStr: "09/16/2025",
			timeStr: "",
			want:    time.Date(2025, 9, 16, 0, 0, 0, 0, time.UTC),
			wantErr: false,
		},
		{
			name:    "Date and time with 12-hour format",
			dateStr: "09/16/2025",
			timeStr: "2:30 PM",
			want:    time.Date(2025, 9, 16, 14, 30, 0, 0, time.UTC),
			wantErr: false,
		},
		{
			name:    "Invalid date format",
			dateStr: "invalid-date",
			timeStr: "15:30",
			wantErr: true,
		},
		{
			name:    "Empty date returns error",
			dateStr: "",
			timeStr: "15:30",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := q.parseDateTime(tt.dateStr, tt.timeStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseDateTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !got.Equal(tt.want) {
				t.Errorf("parseDateTime() = %v, want %v", got, tt.want)
			}
		})
	}
}
