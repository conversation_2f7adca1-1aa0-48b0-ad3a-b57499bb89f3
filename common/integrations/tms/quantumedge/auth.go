package quantumedge

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/drumkitai/drumkit/common/log"
)

func (q *QuantumEdge) Auth(ctx context.Context) (string, error) {
	log.Info(ctx, "attempting QuantumEdge login")

	body, err := q.login(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to complete QuantumEdge login flow: %w", err)
	}

	log.Info(ctx, "successfully authenticated QuantumEdge client")
	return body, nil
}

func (q *QuantumEdge) login(ctx context.Context) (string, error) {
	formData := url.Values{}
	formData.Set("resolution_width", "1366")
	formData.Set("resolution_height", "768")
	formData.Set("userBrowser", "?")
	formData.Set("username", q.config.Username)
	formData.Set("password", q.config.Password)
	formData.Set("btnLogin", "")

	// leaving this here for now because auth doesn't work without this explicit header setup.
	// (specifically origin and referer)
	headers := map[string]string{
		"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif," +
			"image/webp,image/apng,*/*;q=0.8",
		"Accept-Language": "en-GB,en;q=0.8",
		"Cache-Control":   "max-age=0",
		"Connection":      "keep-alive",
		"Content-Type":    "application/x-www-form-urlencoded",
		"Origin":          fmt.Sprintf("https://%s.edgetms.com", q.config.Tenant),
		"Referer":         fmt.Sprintf("https://%s.edgetms.com/login/", q.config.Tenant),
	}

	var loginURL = fmt.Sprintf("https://%s.edgetms.com/login/index.cfm?", q.config.Tenant)

	body, err := q.postWithHeaders(ctx, loginURL, strings.NewReader(formData.Encode()), headers)

	if err != nil {
		return "", fmt.Errorf("failed to complete QuantumEdge login flow: %w", err)
	}

	bodyStr := string(body)
	if strings.Contains(bodyStr, "Your login information is not valid") {
		return "", errors.New("invalid login credentials for QuantumEdge")
	}

	log.Info(ctx, "QuantumEdge login successful - cookies automatically managed by cookiejar")

	return bodyStr, nil
}
