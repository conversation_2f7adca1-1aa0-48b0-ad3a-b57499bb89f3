package mcleodenterprise

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	recordLength = 1000
)

// TODO: Support searching by query params (city, state, ID)
func (m *McleodEnterprise) GetLocations(
	ctx context.Context,
	_ ...models.TMSOption,
) (locs []models.TMSLocation, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	queryParams.Set("is_active", "true")
	queryParams.Set("recordLength", fmt.Sprint(recordLength))
	recordOffset := 0

	// First, check if we have a saved job state in redis
	updatedAt, cursor, err := redis.GetIntegrationState(ctx, m.tms.ID, redis.LocationJob)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
	}
	if updatedAt != "" {
		queryParams.Set("changedAfterDate", updatedAt)
	}
	if cursor != "" {
		// make sure the cursor is a valid integer
		if n, err := strconv.Atoi(cursor); err == nil {
			recordOffset = n
		} else {
			log.Warn(
				ctx,
				"invalid cursor format, using default offset",
				zap.Error(err),
				zap.Uint("integration_id", m.tms.ID),
				zap.String("cursor", cursor),
			)
		}
	}

	// if we haven't set the changedAfterDate query param by now, check the DB for the last update time
	if queryParams.Get("changedAfterDate") == "" {
		latestUpdatedAt, err := integrationDB.GetColumn(ctx, m.tms.ID, integrationDB.LastLocationUpdatedAt)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(
					ctx,
					"record not found, fetching all locations for integration",
					zap.Uint("integration_id", m.tms.ID),
				)
			} else {
				log.WarnNoSentry(
					ctx,
					"failed to get integration state, fetching all locations for integration",
					zap.Error(err),
				)
			}
		} else {
			queryParams.Set("changedAfterDate", latestUpdatedAt.Format(time.RFC3339))
		}
	}

	for {
		queryParams.Set("recordOffset", fmt.Sprint(recordOffset))

		var locationResp []LocationResp
		err = m.get(ctx, "/locations/search", queryParams, &locationResp, s3backup.TypeLocations)
		if err != nil {
			m.SetIntegrationStateWithWarning(
				ctx,
				redis.LocationJob,
				queryParams.Get("changedAfterDate"),
				queryParams.Get("recordOffset"),
			)

			break
		}

		locationsToRefresh := &[]models.TMSLocation{}
		*locationsToRefresh = make([]models.TMSLocation, 0, len(locationResp))
		for _, data := range locationResp {
			loc := data.ToLocationModel(m.tms.ID)
			*locationsToRefresh = append(*locationsToRefresh, loc)
		}

		// Upsert locations to db
		if err = tmsLocationDB.RefreshTMSLocations(ctx, locationsToRefresh); err != nil {
			m.SetIntegrationStateWithWarning(
				ctx,
				redis.LocationJob,
				queryParams.Get("changedAfterDate"),
				queryParams.Get("recordOffset"),
			)

			break
		}

		// after successfully upserting, add the locations to the function's return slice
		locs = append(locs, *locationsToRefresh...)

		recordOffset += recordLength
		if len(locationResp) < recordLength {
			// delete the redis key once we've successfully reached the end of the job
			redisErr := redis.DeleteKey(ctx, fmt.Sprintf("integration-id-%d-%s", m.tms.ID, redis.LocationJob))
			if redisErr != nil && !errors.Is(redisErr, redis.NilEntry) {
				log.Warn(ctx, "failed to delete redis key", zap.Error(redisErr))
			}
			break
		}
	}

	return locs, err
}

// NOTE: Mcleod allows duplicates
func (m *McleodEnterprise) CreateLocation(
	ctx context.Context,
	loc models.TMSLocation,
) (res models.TMSLocation, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "CreateLocationMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	cityID, err := m.getCityID(ctx, loc.City, loc.State)
	if err != nil {
		return res, err
	}

	newLoc := LocationResp{
		Type:         "location",
		CompanyID:    "TMS",
		ID:           loc.ExternalTMSID,
		Name:         loc.Name,
		Address1:     loc.AddressLine1,
		CityID:       cityID,
		CityName:     loc.City,
		State:        loc.State,
		ZipCode:      loc.Zipcode,
		IsActive:     true,
		IsShipper:    loc.IsShipper,
		IsConsignee:  loc.IsConsignee,
		ApptRequired: loc.ApptRequired,
	}

	err = m.put(ctx, "/locations/create", newLoc, &newLoc, s3backup.TypeLocations)
	if err != nil {
		return models.TMSLocation{}, err
	}

	return newLoc.ToLocationModel(m.tms.ID), nil
}

func (l LocationResp) ToLocationModel(tmsID uint) models.TMSLocation {
	loc := models.TMSLocation{
		TMSIntegrationID: tmsID,
		ApptRequired:     l.ApptRequired,
		IsShipper:        l.IsShipper,
		IsConsignee:      l.IsConsignee,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: l.ID,
			Name:          l.Name,
			AddressLine1:  l.Address1,
			AddressLine2:  "",
			City:          l.CityName,
			State:         l.State,
			Zipcode:       l.ZipCode,
		}}
	loc.FullAddress = models.ConcatAddress(loc.CompanyCoreInfo)
	loc.NameAddress = loc.Name + ", " + loc.FullAddress

	return loc
}
