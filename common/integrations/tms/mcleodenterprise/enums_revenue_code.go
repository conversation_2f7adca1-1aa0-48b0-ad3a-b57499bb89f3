package mcleodenterprise

import (
	"fmt"
	"strings"
)

func (m *McleodEnterprise) toRevenueCode(revLabel string) (revenueCode string, err error) {
	if !m.tms.FeatureFlags.IsRevenueCodeRequired && strings.TrimSpace(strings.ToLower(revLabel)) == "" {
		return "", nil
	}

	tenant := strings.ToLower(m.tms.Tenant)
	switch {
	case strings.Contains(tenant, TenantTrident):
		return m.toTridentRevenueCode(revLabel)
	case strings.Contains(tenant, TenantFetch):
		return m.toFetchFreightRevenueCode(revLabel)
	case strings.Contains(tenant, TenantSyfan):
		return m.toSyfanRevenueCode(revLabel)
	case strings.Contains(tenant, TenantTumalo):
		return m.toTumaloRevenueCode(revLabel)
	default:
		return "", m.unknownTenantError("revenue code")
	}
}

// Revenue code is required
func (m *McleodEnterprise) toTridentRevenueCode(revLabel string) (revenueCode string, err error) {
	revLabel = strings.TrimSpace(strings.ToLower(revLabel))
	switch revLabel {
	case "bradenton", "brade":
		return "BRADE", nil

	case "charleston", "chsc":
		return "CHSC", nil

	case "chattanooga", "chatt":
		return "CHATT", nil

	case "drayage", "dray":
		return "DRAY", nil

	case "enterprise", "ent":
		return "ENT", nil

	case "minneapolis", "minne":
		return "MINNE", nil

	case "tampa":
		return "TAMPA", nil
	}
	return "", fmt.Errorf("unsupported revenue code for tenant %s: %s", m.tms.Tenant, revLabel)

}

func (m *McleodEnterprise) toFetchFreightRevenueCode(revLabel string) (string, error) {
	revLabel = strings.TrimSpace(strings.ToLower(revLabel))
	switch revLabel {
	case "chandler nelson", "cnels":
		return "CNELS", nil

	case "chris ryan", "chris":
		return "CHRIS", nil

	case "connor cook", "ccook":
		return "CCOOK", nil

	case "daniel carmicha", "dc1":
		return "DC1", nil

	case "fetch freight", "fetch":
		return "FETCH", nil

	case "harrison stanley", "hstan":
		return "HSTAN", nil

	case "hunter/whitfield", "hw1":
		return "HW1", nil

	case "jenkins mock", "jmock":
		return "JMOCK", nil

	case "jerry quinn", "jquin":
		return "JQUIN", nil

	case "kade sanders", "kade":
		return "KADE", nil

	case "louis baxley", "lb1":
		return "LB1", nil

	case "mitchell lewis", "ml1":
		return "ML1", nil

	case "operations team", "ops":
		return "OPS", nil

	case "payton junkin", "pjunk":
		return "PJUNK", nil

	case "robert connor", "robco":
		return "ROBCO", nil

	case "sales team", "sales":
		return "SALES", nil
	}

	return "", fmt.Errorf("unsupported revenue code for tenant %s: %s", m.tms.Tenant, revLabel)
}

func (m *McleodEnterprise) toSyfanRevenueCode(revLabel string) (string, error) {
	revLabel = strings.TrimSpace(strings.ToLower(revLabel))
	if revenueCode, ok := syfanRevenueCodeOptions[revLabel]; ok {
		return revenueCode, nil
	}
	return "", fmt.Errorf("unsupported revenue code for tenant %s: %s", m.tms.Tenant, revLabel)
}

func (m *McleodEnterprise) toTumaloRevenueCode(revLabel string) (string, error) {
	revLabel = strings.TrimSpace(strings.ToUpper(revLabel))

	if revenueCode, ok := tumaloRevenueCodeOptions[revLabel]; ok {
		return revenueCode, nil
	}
	return "", fmt.Errorf("unsupported revenue code for tenant %s: %s", m.tms.Tenant, revLabel)
}

// Map of Syfan revenue code labels to their corresponding codes
var syfanRevenueCodeOptions = map[string]string{
	"adt1":        "ADT1",
	"bdt1":        "BDT1",
	"bnsf":        "BNSF",
	"dedicated":   "DED",
	"drayage 1":   "DRAY1",
	"dry":         "DRY",
	"expedited":   "EXP",
	"expedited 1": "EXP1",
	"expedited 2": "EXP2",
	"expedited 3": "EXP3",
	"expedited 4": "EXP4",
	"expedited 6": "EXP6",
	"fitco":       "FITCO",
	"flatbed":     "FLAT",
	"ltl1":        "LTL1",
	"midwest":     "MWD",
	"project":     "PROJ",
	"sdi":         "SDI",
	"southeast":   "SED",
	"temp":        "TEMP",
	"temp1":       "TEMP1",
	"temp2":       "TEMP2",
	"temp3":       "TEMP3",
	"training":    "TRN",
	"ups peak":    "UPSP",
}

var tumaloRevenueCodeOptions = map[string]string{
	"REBID": "REBID",
	"ABOVE": "ABOVETGT",
	"FLA":   "COLUCCI GROUP",
	"BKG":   "KING GROUP",
	"LTL":   "LTL",
	"MMG":   "MURPHY GROUP",
	"PRTL":  "PARTIAL LOAD",
	"PBC":   "PEPSI",
	"PD":    "PER DIEM",
	"QFB":   "QUAKER",
	"DET":   "QUAKER DETENTIO",
	"SPOT":  "SPOT FREIGHT",
	"USFB":  "USF-BRET",
	"USFS":  "USF-SCOTT",
}
