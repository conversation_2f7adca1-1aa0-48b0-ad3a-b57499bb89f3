package mcleodenterprise

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsIntegrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

func (m *McleodEnterprise) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) (ids []string, err error) {
	attrs := otel.IntegrationAttrs(m.tms)
	queryJSON, err := json.Marshal(query)
	if err != nil {
		log.WarnNoSentry(ctx, "error marshaling query to add to trace attributes", zap.Error(err))
	} else {
		attrs = append(attrs, attribute.String("query", string(queryJSON)))
	}

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsMcleodEnterprise", attrs)
	defer func() { metaSpan.End(err) }()

	loads, err := m.searchLoadsHelper(ctx, query)
	if err != nil {
		return nil, err
	}

	for _, load := range loads {
		ids = append(ids, load.FreightTrackingID)
	}

	return ids, nil
}

func (m *McleodEnterprise) SearchLoads(ctx context.Context, query models.SearchLoadsQuery) ([]models.Load, error) {
	return m.searchLoadsHelper(ctx, query)
}

func (m *McleodEnterprise) searchLoadsHelper(
	ctx context.Context,
	query models.SearchLoadsQuery,
) (res []models.Load, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "searchLoadsHelperMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	recordLength := 100
	queryParams := url.Values{}
	queryParams.Set("orderBy", "ordered_date DESC")
	queryParams.Set("recordLength", fmt.Sprint(recordLength))
	queryParams.Set("id", "") // Endpoint requires at least 1 filter

	if query.FromDate.Valid {
		queryParams.Set("changedAfterDate", query.FromDate.Time.Format(time.RFC3339))
	}

	// SB = Straight box truck, FH = Flatbed Hotshot
	queryParams.Set("equipment_type_id", helpers.Or(mapTransportType(query.TransportType), ""))

	queryParams.Set("shipper.city_name", helpers.Or(query.Pickup.City, ""))
	queryParams.Set("shipper.state", helpers.Or(query.Pickup.State, ""))
	// Mcleod supports * wildcards for query params, use for 3-digit zip code lookups
	queryParams.Set("shipper.zip_code", helpers.Or(
		helpers.Ternary(len(query.Pickup.Zip) < 5, query.Pickup.Zip+"*", query.Pickup.Zip),
		""))

	queryParams.Set("consignee.city_name", helpers.Or(query.Dropoff.City, ""))
	queryParams.Set("consignee.state", helpers.Or(query.Dropoff.State, ""))
	queryParams.Set("consignee.zip_code", helpers.Or(
		// Both empty string and "*" means all valid zip codes
		helpers.Ternary(len(query.Dropoff.Zip) < 5, query.Dropoff.Zip+"*", query.Dropoff.Zip),
		""))
	queryParams.Set("status", helpers.Or(toOrderStatus(query.Status), ""))

	log.Infof(ctx, "searching for loads with base query: %+v", queryParams)
	i := 0
	var ordersResp []OrderResp

	for {
		queryParams.Set("recordOffset", fmt.Sprint(i*recordLength))

		err = m.get(ctx, "/orders/search", queryParams, &ordersResp, s3backup.TypeLoads)
		if err != nil {
			return res, fmt.Errorf("get Load IDs response failed: %w", err)
		}

		// Stop pagination if no results are returned
		if len(ordersResp) == 0 {
			break
		}

		log.Debugf(ctx, "found %d orders on page %d", len(ordersResp), i)

		for _, order := range ordersResp {

			// If FromFreightTrackingID is provided, use it for manual filtering
			if query.FromFreightTrackingID != "" && order.ID < query.FromFreightTrackingID {
				continue
			}

			var movementResp MovementResp
			// Search endpoint does not return movements array, only the current movement
			movementEndpoint := fmt.Sprintf("/movement/%s", order.CurrMovementID)
			movementErr := m.get(ctx, movementEndpoint, nil, &movementResp, s3backup.TypeLoads)
			if movementErr != nil {
				// Fail-open. We primarily need movement details to parse the carrier's human-readable name.
				// If we fail to get movement details, we'll just use the carrier ID
				log.Warn(ctx, "failed to get movement details, continuing with limited carrier info",
					zap.Error(movementErr),
					zap.String("orderID", order.ID),
					zap.String("movementID", order.CurrMovementID))
			}

			res = append(res, m.toLoad(ctx, order, movementResp, [2]StopResp{}))
		}

		// Stop pagination if the last record is outside the date range
		lastRecordDate := parseTime(ordersResp[len(ordersResp)-1].OrderedDate)
		lastRecordID := ordersResp[len(ordersResp)-1].ID

		if (query.FromDate.Valid && lastRecordDate.Valid && lastRecordDate.Time.Before(query.FromDate.Time)) ||
			(query.ToDate.Valid && lastRecordDate.Valid && lastRecordDate.Time.After(query.ToDate.Time)) {
			break
		}

		if query.FromFreightTrackingID != "" && lastRecordID <= query.FromFreightTrackingID {
			break
		}

		i++
	}

	return res, nil
}

func (m *McleodEnterprise) GetLoad(
	ctx context.Context,
	externalTMSID string,
) (result models.Load, defaultAttrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(m.tms), attribute.String("external_tms_id", externalTMSID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadMcleodEnterprise", spanAttrs)
	defer func() { metaSpan.End(err) }()

	defaultAttrs = m.GetDefaultLoadAttributes()
	endPoint := fmt.Sprintf("/orders/%s", externalTMSID)
	var orderResp OrderResp
	err = m.get(ctx, endPoint, nil, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return result, defaultAttrs, fmt.Errorf("get order resp failed: %w", err)
	}
	// Mcleod returns 204s, not 404s, if the load is not found
	if orderResp.ID == "" {
		return result, defaultAttrs, errtypes.EntityNotFoundError(m.tms, externalTMSID, LoadIDType)
	}

	// getting movement/carrier details
	var movementResp MovementResp
	if len(orderResp.Movements) > 0 {
		movementEndpoint := fmt.Sprintf("/movement/%s", orderResp.Movements[0].ID)
		movementErr := m.get(ctx, movementEndpoint, nil, &movementResp, s3backup.TypeLoads)
		if movementErr != nil {
			// Fail-open. We primarily need movement details to parse the carrier's human-readable name.
			// If we fail to get movement details, we'll just use the carrier ID
			log.Warn(
				ctx,
				"failed to get movement details, continuing with limited carrier info",
				zap.Error(movementErr),
				zap.String("orderID", orderResp.ID),
				zap.String("movementID", orderResp.Movements[0].ID))
			// Continue with empty movementResp
		}
	}

	var stopResp [2]StopResp
	if orderResp.ShipperStopID != "" {
		stopEndpoint := fmt.Sprintf("/stops/%s", orderResp.ShipperStopID)
		stopErr := m.get(ctx, stopEndpoint, nil, &stopResp[0], s3backup.TypeLoads)
		if stopErr != nil {
			log.WarnNoSentry(ctx, "failed to get stop details", zap.Error(stopErr))
		}
	}
	if orderResp.ConsigneeStopID != "" {
		stopEndpoint := fmt.Sprintf("/stops/%s", orderResp.ConsigneeStopID)
		stopErr := m.get(ctx, stopEndpoint, nil, &stopResp[1], s3backup.TypeLoads)
		if stopErr != nil {
			log.WarnNoSentry(ctx, "failed to get stop details", zap.Error(stopErr))
		}
	}

	result = m.toLoad(ctx, orderResp, movementResp, stopResp)
	return result, defaultAttrs, nil
}

// NOTE: Supports creating load only for *existing* customers, pickups, and dropoffs, not new ones
func (m *McleodEnterprise) CreateLoad(
	ctx context.Context,
	reqLoad models.Load,
	tmsUser *models.TMSUser,
) (respLoad models.Load, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	orderReqBody, err := m.toMcleodEnterpriseOrder(ctx, &reqLoad, nil, tmsUser)
	if err != nil {
		return models.Load{}, err
	}

	var orderResp OrderResp
	err = m.put(ctx, "/orders/create", &orderReqBody, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return reqLoad, errtypes.NewUserFacingError(err)
	}

	loadResp := m.toLoad(ctx, orderResp, MovementResp{}, [2]StopResp{})

	return loadResp, nil
}

func (m *McleodEnterprise) UpdateLoad(
	ctx context.Context,
	curLoad *models.Load,
	reqLoad *models.Load,
) (respLoad models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(m.tms), otel.SafeIntAttribute("load_id", reqLoad.ID))
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadMcleodEnterprise", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = m.GetDefaultLoadAttributes()

	// Update carrier if applicable
	if !strings.EqualFold(reqLoad.Carrier.ExternalTMSID, curLoad.Carrier.ExternalTMSID) {
		// (Re-)assign carrier; fail-close and show user carrier-specific error on FE
		if reqLoad.Carrier.ExternalTMSID != "" {
			if err = m.assignCarrier(ctx, reqLoad, curLoad); err != nil {
				return respLoad,
					attrs,
					errtypes.WrapNewUserFacingError("failed to assign carrier", err, cleanCarrierAssignmentError)
			}
		} else {
			// Remove existing carrier
			// Fail-open; user will get models.LoadDiff error on FE
			if err = m.removeCarrier(ctx, reqLoad); err != nil {
				log.WarnNoSentry(ctx, "failed to remove carrier from load", zap.Error(err))
			}
		}
	}

	// Update order
	orderReqBody, err := m.toMcleodEnterpriseOrder(ctx, reqLoad, curLoad, nil)
	if err != nil {
		return respLoad, attrs, fmt.Errorf("error creating Mcleod Order body: %w", err)
	}
	orderReqBody.ID = reqLoad.ExternalTMSID

	var orderResp OrderResp
	err = m.put(ctx, "/orders/update", &orderReqBody, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return respLoad, attrs, fmt.Errorf("update order failed: %w", err)
	}

	var movementResp MovementResp
	if len(orderResp.Movements) > 0 {
		movementEndpoint := fmt.Sprintf("/movements/%s", orderResp.Movements[0].ID)
		err = m.get(ctx, movementEndpoint, nil, &movementResp, s3backup.TypeLoads)
		if err != nil {
			// Fail-open. We primarily need movement details to parse the carrier's human-readable name and DOT #.
			// If we fail to get movement details, we'll just use the carrier ID
			// NOTE: If this fails, FE will throw a models.LoadDiff.BuildErrMsg() error
			log.Warn(ctx, "failed to get movement details", zap.Error(err))
		}
	}

	respLoad = m.toLoad(ctx, orderResp, movementResp, [2]StopResp{})

	return respLoad, attrs, nil
}

func (m *McleodEnterprise) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) (loads []models.Load, attr models.LoadAttributes, _ error) {
	if idType == "" {
		// In order of least to most permissive regex, since refNumber can be any alpha and/or numeric string
		switch {
		case tmsIntegrationDB.TridentMcleodLoadIDPattern.MatchString(id),
			tmsIntegrationDB.FetchFreightMcleodIDPattern.MatchString(id):
			idType = LoadIDType
			// REVIEW: But what if there's a 6 digit PO #? Hmm to consider
		case tmsIntegrationDB.McleodMovementIDPattern.MatchString(id):
			idType = MovementIDType
		case tmsIntegrationDB.McleodRefNumberPattern.MatchString(id):
			idType = RefNumberIDType
		}
	}

	switch idType {
	case LoadIDType:
		load, attrs, err := m.GetLoad(ctx, id)
		return []models.Load{load}, attrs, err

	case MovementIDType, RefNumberIDType:
		return m.getLoadsByIDType(ctx, id, idType)

	default:
		return nil, m.GetDefaultLoadAttributes(), fmt.Errorf("unrecognized ID type: %s", idType)
	}
}

func mapTransportType(transportType string) string {
	switch strings.ToUpper(transportType) {
	case "V", "VAN":
		return "V"
	case "R", "REEFER":
		return "R"
	case "F", "FLATBED":
		return "F"
	case "FH", "HOTSHOT", "HOT SHOT", "HOT-SHOT", "FLATBED HOTSHOT", "FLATBED HOT SHOT", "FLATBED HOT-SHOT":
		return "FH"
	case "SB", "STRAIGHT BOX", "STRAIGHT BOX TRUCK", "BOX TRUCK":
		return "SB"
	default:
		return transportType
	}
}

// Search for loads ordered within the last 60 days
var searchFrom = time.Now().Add(-60 * 24 * time.Hour)

func (m *McleodEnterprise) getLoadsByIDType(
	ctx context.Context,
	id, idType string,
) (loads []models.Load, attrs models.LoadAttributes, err error) {

	spanAttrs := append(otel.IntegrationAttrs(m.tms), attribute.String("ID", id), attribute.String("IDType", idType))
	ctx, metaSpan := otel.StartSpan(ctx, "getLoadsByIDTypeMcleodEnterprise", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs = m.GetDefaultLoadAttributes()

	queryParams := url.Values{}
	switch idType {
	case MovementIDType:
		queryParams.Add("curr_movement_id", id)

	default:
		//  RefNumberIDType, etc
		queryParams.Add("blnum", id)
	}

	queryParams.Add("orderBy", "ordered_date DESC")
	endPoint := "/orders/search"
	var orderResp []OrderResp

	err404 := errtypes.HTTPResponseError{
		IntegrationName: m.tms.Name,
		IntegrationType: m.tms.Type,
		AxleTSPID:       m.tms.ID,
		ServiceID:       m.tms.ServiceID,
		HTTPMethod:      http.MethodGet,
		URL:             fmt.Sprintf("%s?%s", endPoint, queryParams.Encode()),
		StatusCode:      http.StatusNotFound,
	}

	err = m.get(ctx, endPoint, queryParams, &orderResp, s3backup.TypeLoads)
	if err == nil {
		switch len(orderResp) {
		case 0:
			if idType == MovementIDType {
				err404.URL = fmt.Sprintf("%s?%s", endPoint, queryParams.Encode())

				return nil, attrs, err404
			}

		default:
			for i, order := range orderResp {
				load, err := m.processSearchedOrder(ctx, order, i)
				if err != nil {
					continue
				}
				if load.ExternalTMSID != "" {
					loads = append(loads, load)
				}
			}

			if len(loads) == 0 && err == nil {
				err404.ResponseBody = []byte("all found loads older than 60 days")
				return loads, attrs, err404
			}
			// We started with at least 1 load. If all subsequent lookups failed, return the error
			return loads, attrs, err

		}
	}

	if err != nil && !errtypes.IsEntityNotFoundError(err) {
		return nil, attrs, fmt.Errorf("search order resp failed: %w", err)
	}

	log.Info(ctx, "get by blnum failed, trying consignee_refno", zap.String("ID", id), zap.String("IDType", idType))

	queryParams = url.Values{}
	queryParams.Add("consignee_refno", id)
	queryParams.Add("orderBy", "ordered_date DESC")

	err = m.get(ctx, endPoint, queryParams, &orderResp, s3backup.TypeLoads)
	if err != nil {
		return nil, attrs, fmt.Errorf("second attempt to search order resp failed: %w", err)
	}

	switch len(orderResp) {
	case 0:
		err404.URL = fmt.Sprintf("%s?%s", endPoint, queryParams.Encode())

		return nil, attrs, err404

	default:
		for i, order := range orderResp {
			load, err := m.processSearchedOrder(ctx, order, i)
			if err != nil {
				continue
			}
			if load.ExternalTMSID != "" {
				loads = append(loads, load)
			}
		}

		if len(loads) == 0 && err == nil {
			err404.ResponseBody = []byte("all found loads older than 60 days")
			return loads, attrs, err404
		}
		// We started with at least 1 load. If all subsequent lookups failed, return the error
		return loads, attrs, err

	}

}

func (m *McleodEnterprise) toLoad(
	ctx context.Context,
	orderResp OrderResp,
	movResp MovementResp, // Primarily needed to parse the carrier's human-readable name
	stopResp [2]StopResp,
) (load models.Load) {

	load.TMSID = m.tms.ID
	load.ServiceID = m.tms.ServiceID
	load.ExternalTMSID = orderResp.ID
	load.FreightTrackingID = orderResp.ID
	load.PONums = orderResp.ConsigneeRefno // Labelled as PO # in the UI. Also parsed into Consignee.RefNumber below
	load.Status = orderResp.StatusDescr
	load.MoreThanTwoStops = len(orderResp.Stops) > 2
	if len(orderResp.Movements) > 0 {
		load.Carrier.MovementID = orderResp.Movements[0].ID
	}

	if orderResp.OperationsUser0 != nil {
		load.Operator = orderResp.OperationsUser0.Name
	}

	// customer details
	if customer := orderResp.Customer; customer != nil {
		load.Customer.ExternalTMSID = customer.ID
		load.Customer.Name = customer.Name
		load.Customer.AddressLine1 = customer.Address1
		load.Customer.AddressLine2 = customer.Address2
		load.Customer.City = customer.City
		load.Customer.State = customer.StateID
		load.Customer.Zipcode = customer.ZipCode
		load.Customer.RefNumber = orderResp.Blnum
	}

	// carrier details
	var orderMov MiniMovement
	if count := len(orderResp.Movements); count > 0 {
		// NOTE: We hypothesize there's a movement for each carrier transporting part of the load.
		// Add log for future debugging and analysis
		if count > 1 {
			log.Info(ctx, "found mcleod load with more than 1 movement",
				zap.Int("countMovements", count), zap.String("loadID", load.ExternalTMSID))
		}
		orderMov = orderResp.Movements[0]
	}

	load.Carrier = models.Carrier{
		ExternalTMSID:     orderMov.OverridePayeeID,
		MovementID:        orderMov.ID,
		Name:              helpers.Or(movResp.Carrier.Name, orderMov.OverridePayeeID),
		MCNumber:          "",
		DOTNumber:         movResp.Carrier.DrsPayee.DotNumber,
		SealNumber:        orderResp.SealNumber,
		SCAC:              "",
		Phone:             orderMov.CarrierPhone,
		Email:             orderMov.CarrierEmail,
		Notes:             "",
		FirstDriverName:   orderMov.OverrideDriverNm,
		FirstDriverPhone:  orderMov.OverrideDrvrCell,
		SecondDriverName:  "",
		SecondDriverPhone: "",
		Dispatcher:        orderMov.CarrierContact,
		// DispatchSource:           movResp.DispatcherUserID,
		DispatchCity:             "",
		DispatchState:            "",
		ExternalTMSTruckID:       orderMov.CarrierTractor,
		ExternalTMSTrailerID:     orderMov.CarrierTrailer,
		RateConfirmationSent:     false,
		ConfirmationSentTime:     models.NullTime{},
		ConfirmationReceivedTime: models.NullTime{},
		DispatchedTime:           models.NullTime{},
		ExpectedPickupTime:       models.NullTime{},
		PickupStart:              models.NullTime{},
		PickupEnd:                models.NullTime{},
		ExpectedDeliveryTime:     models.NullTime{},
		DeliveryStart:            models.NullTime{},
		DeliveryEnd:              models.NullTime{},
		SignedBy:                 "",
	}

	// Pickup and Consingnee
	lastDropoff := 0
	for _, stop := range orderResp.Stops {
		if stopType := strings.ToLower(stop.StopType); stopType == "pu" && stop.OrderSequence == 1 {
			load.Pickup = models.Pickup{
				CompanyCoreInfo:      mapCompanyCoreInfo(stop, stopResp[0]),
				ExternalTMSStopID:    stop.ID,
				BusinessHours:        "",
				ApptRequired:         stop.ApptRequired,
				ApptConfirmed:        stop.Confirmed,
				ApptStartTime:        parseTime(stop.SchedArriveEarly),
				ApptEndTime:          parseTime(stop.SchedArriveLate),
				ApptNote:             extractStopNote(stop),
				AdditionalReferences: toLoadReferenceNumbers(stop.ReferenceNumbers),
			}
			tz, err := timezone.GetTimezone(ctx, stop.CityName, stop.State, "")
			if err != nil {
				// Fail-open bc timezone is not essential since McleodEnterprise timestamps include TZ info
				log.WarnNoSentry(ctx, "error fetching pickup's timezone", zap.Error(err))
			}
			load.Pickup.Timezone = tz
			load.Carrier.PickupStart = parseTime(stop.ActualArrival)
			load.Carrier.PickupEnd = parseTime(stop.ActualDeparture)

			var refs []string
			for _, ref := range stop.ReferenceNumbers {
				refs = append(refs, ref.ReferenceNumber)
			}
			load.Pickup.RefNumber = strings.Join(refs, ",")

		} else if stopType == "so" && stop.OrderSequence > lastDropoff {
			lastDropoff = stop.OrderSequence

			load.Consignee = models.Consignee{
				CompanyCoreInfo:      mapCompanyCoreInfo(stop, stopResp[1]),
				ExternalTMSStopID:    stop.ID,
				BusinessHours:        "",
				MustDeliver:          parseTime(stop.SchedArriveLate),
				ApptRequired:         stop.ApptRequired,
				ApptConfirmed:        stop.Confirmed,
				ApptStartTime:        parseTime(stop.SchedArriveEarly),
				ApptEndTime:          parseTime(stop.SchedArriveLate),
				ApptNote:             extractStopNote(stop),
				RefNumber:            orderResp.ConsigneeRefno,
				AdditionalReferences: toLoadReferenceNumbers(stop.ReferenceNumbers),
			}
			tz, err := timezone.GetTimezone(ctx, stop.CityName, stop.State, "")
			if err != nil {
				// Fail-open bc timezone is not essential since McleodEnterprise timestamps include TZ info
				log.WarnNoSentry(ctx, "error fetching pickup's timezone", zap.Error(err))
			}
			load.Consignee.Timezone = tz
			load.Carrier.DeliveryStart = parseTime(stop.ActualArrival)
			load.Carrier.DeliveryEnd = parseTime(stop.ActualDeparture)

		}
	}

	// specifications
	loadMode := models.StringToLoadMode(orderResp.OrderMode)
	if loadMode == "" {
		log.Warn(
			ctx,
			"Unknown McLeodEnterprise load mode",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("mode", orderResp.OrderMode),
		)
	}
	load.Mode = loadMode

	load.Specifications.OrderType = orderResp.OrderTypeDescr
	load.Specifications.TransportType = orderResp.EquipmentTypeDescr
	transportEnum, err := m.MapTransportTypeEnum(orderResp.EquipmentTypeDescr)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping transport type", zap.Error(err))
	} else {
		load.Specifications.TransportTypeEnum = &transportEnum
	}
	load.Specifications.Commodities = orderResp.CommodityID
	load.Specifications.TotalPieces = models.ValueUnit{Val: float32(orderResp.Pieces)}
	load.Specifications.Hazmat = orderResp.Hazmat
	load.Specifications.TotalDistance = models.ValueUnit{
		Val:  orderResp.BillDistance,
		Unit: orderResp.BillDistanceUm,
	}
	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  orderResp.Weight,
		Unit: orderResp.WeightUm,
	}
	load.Specifications.BillableWeight = load.Specifications.TotalWeight

	// Rate data
	load.RateData.CollectionMethod = orderResp.CollectionMethodDescr
	load.RateData.RevenueCode = orderResp.RevenueCodeID

	load.RateData.CustomerRateType = orderResp.RateTypeDescr
	load.RateData.CustomerRateNumUnits = orderResp.RateUnits
	load.RateData.CustomerLineHaulUnit = toRateUnit(orderResp.RateTypeDescr)
	load.RateData.CustomerLineHaulRate = orderResp.Rate
	load.RateData.CustomerLineHaulCharge = models.ValueUnit{
		Val:  orderResp.FreightCharge,
		Unit: orderResp.FreightChargeC,
	}

	load.RateData.CarrierRateType = movResp.OverrideTypeDescr
	load.RateData.CarrierRateNumUnits = movResp.OverrideUnits
	load.RateData.CarrierLineHaulUnit = toRateUnit(movResp.OverrideTypeDescr)
	load.RateData.CarrierLineHaulRate = movResp.OverridePayRate
	load.RateData.CarrierLineHaulCharge = models.ValueUnit{
		Val: movResp.OverridePayAmt, Unit: movResp.OverridePayAmtC,
	}
	load.RateData.CarrierCost = helpers.Ternary(movResp.TotalPay > 0, &movResp.TotalPay, &movResp.OverridePayRate)
	load.RateData.CarrierCostCurrency = movResp.TotalPayC

	return load
}

func (m *McleodEnterprise) assignCarrier(ctx context.Context, reqLoad *models.Load, curLoad *models.Load) (err error) {
	queryParams := url.Values{}
	queryParams.Add("carrierId", reqLoad.Carrier.ExternalTMSID)
	// If existing carrier, must provide an unassignment reason or else Mcleod will remove carrier but throw error
	if curLoad.Carrier.ExternalTMSID != "" {
		queryParams.Add("reason", "re-assigned via drumkit")
	}

	customHeaders := map[string]string{
		"Accept": "text/plain",
	}

	var resp string
	err = m.post(ctx, "carrierDispatch/assign/"+reqLoad.Carrier.MovementID, queryParams, &resp, "", customHeaders)
	if err == nil {
		return nil
	}

	// Fail-close; show user error about specific carrier
	return errtypes.NewUserFacingError(err, cleanCarrierAssignmentError)

}

func (m *McleodEnterprise) removeCarrier(ctx context.Context, reqLoad *models.Load) (err error) {
	queryParams := url.Values{}
	queryParams.Add("reason", "removed via drumkit")

	customHeaders := map[string]string{
		"Accept": "text/plain",
	}

	var resp string
	err = m.post(ctx, "carrierDispatch/clearCarrier/"+reqLoad.Carrier.MovementID, queryParams, &resp, "", customHeaders)

	return err

}

func toRateUnit(rateType string) string {
	switch rateType = strings.ToLower(rateType); rateType {
	case "flat":
		// No units
		return ""

	case "distance":
		return models.MilesUnit

	default:
		return rateType
	}
}

// For future use
//
//nolint:unused
func (m *McleodEnterprise) toMcleodEnterpriseCarrier(createLoad models.Load) (resp CarrierResp) {
	resp.Name = createLoad.Carrier.Name
	resp.Email = createLoad.Carrier.Email
	resp.PhoneNumber = createLoad.Carrier.Phone
	resp.City = createLoad.Carrier.DispatchCity
	resp.State = createLoad.Carrier.DispatchState

	// NOTE: Drivers are also relational objects. If ID not provided, Mcleod creates a new driver

	return
}

//nolint:unused
func (m *McleodEnterprise) toMcleodEnterpriseCustomer(createLoad models.Load) (resp CustomerResp) {
	timeFormat := "20060102150405-0700"
	resp.StartDate = createLoad.Carrier.ExpectedPickupTime.Time.Format(timeFormat)
	resp.DotNumber = createLoad.Carrier.DOTNumber
	resp.Name = createLoad.Customer.Name
	resp.City = createLoad.Customer.City
	resp.ZipCode = createLoad.Customer.Zipcode
	resp.Address1 = createLoad.Customer.AddressLine1
	resp.Address2 = createLoad.Customer.AddressLine2
	return
}

//nolint:unused // For future use
func (m *McleodEnterprise) toMcleodEnterpriseStop(createLoad models.Load) (resp StopResp) {
	timeFormat := "20060102150405-0700"
	resp.LocationName = createLoad.Pickup.Name
	resp.CityName = createLoad.Pickup.City
	resp.State = createLoad.Pickup.State
	resp.ZipCode = createLoad.Pickup.Zipcode
	resp.PlannedArrivalTime = createLoad.Pickup.ApptStartTime.Time.Format(timeFormat)
	return
}

func (m *McleodEnterprise) toMcleodEnterpriseOrder(
	ctx context.Context,
	reqLoad *models.Load,
	curLoad *models.Load,
	tmsUser *models.TMSUser,
) (order OrderResp, err error) {
	// NOTE: Only TL, 2-stop loads supported rn
	reqLoad.Mode = "T"

	// If no pickup ID, create new location
	// Mcleod supports no dropoff IDs so we do the same. User can manually create a dropoff location in FE
	if reqLoad.Pickup.ExternalTMSID == "" && !m.tms.FeatureFlags.IsOnlyCityStateRequired {
		newLoc := models.TMSLocation{
			TMSIntegrationID: m.tms.ID,
			CompanyCoreInfo:  reqLoad.Pickup.CompanyCoreInfo,
			ApptRequired:     reqLoad.Pickup.ApptRequired,
			IsShipper:        true,
			IsConsignee:      false,
		}

		loc, err := m.CreateLocation(ctx, newLoc)
		if err != nil {
			return order, errtypes.WrapNewUserFacingError("error creating pickup location", err)
		}
		reqLoad.Pickup.ExternalTMSID = loc.ExternalTMSID
		reqLoad.Pickup.CompanyCoreInfo = loc.CompanyCoreInfo

		if err := tmsLocationDB.Create(ctx, loc); err != nil {
			log.WarnNoSentry(ctx, "error creating pickup location in DB", zap.Error(err))
		}
	}

	// Handle tenant-dependent fields
	var revenueCode string
	var operationsUser string
	if tmsUser != nil {
		revenueCode = tmsUser.RevenueCode
		operationsUser = tmsUser.OperationsUser
	} else {
		revenueCode, err = m.toRevenueCode(reqLoad.RateData.RevenueCode)
		if err != nil {
			return order, err
		}
	}

	// Allocation code is required only for Trident and derived from their revenue code
	allocCode, err := m.toAllocationCode(reqLoad.RateData.RevenueCode)
	if err != nil {
		return order, fmt.Errorf("error converting allocation code: %w", err)
	}

	// COMMENT: Fetch's internal logic is conflicting with Drumkit's, so we're not using it for now.
	// var salesperson1 string
	// var salesperson1Percent float32

	// if tmsUser != nil {
	// 	salesperson1 = tmsUser.ExternalTMSID
	// } else if reqLoad.RateData.Salesperson1 != "" {
	// 	salesperson1 = reqLoad.RateData.Salesperson1
	// }

	// if salesperson1 != "" && reqLoad.RateData.Salesperson1Percent > 0 {
	// 	salesperson1Percent = reqLoad.RateData.Salesperson1Percent
	// }

	// As of 2025-08-07, Order Type input is shown in FE LB form for Trident bc Logan's team wants "ENT" order type.
	// Order type left as "" for Fetch because they let McLeod handle the order type.
	orderType, err := m.toOrderType(string(reqLoad.Mode), reqLoad.Specifications.OrderType)
	if err != nil {
		return order, fmt.Errorf("error converting order type: %w", err)
	}

	equipmentType, err := m.getTrailerCode(reqLoad.Specifications.TransportType)
	if err != nil {
		return order, fmt.Errorf("error getting trailer code: %w", err)
	}

	drumkitUser, err := m.toDrumkitUser()
	if err != nil {
		return order, fmt.Errorf("error getting drumkit user: %w", err)
	}

	// Handle generic enum fields
	colMethod, err := toCollectionMethod(reqLoad.RateData.CollectionMethod)
	if err != nil {
		return order, fmt.Errorf("error converting collection method: %w", err)
	}

	customerRateType, err := toRateType(reqLoad.RateData.CustomerRateType, "customer")
	if err != nil {
		return order, fmt.Errorf("error converting customer rate type: %w", err)
	}

	customerCurrency := toCurrency(reqLoad.RateData.CustomerLineHaulCharge.Unit)

	carrierRateType, err := toRateType(reqLoad.RateData.CarrierRateType, "carrier")
	if err != nil {
		return order, fmt.Errorf("error converting carrier rate type: %w", err)
	}

	pickupReferenceNumbers := toMcleodReferenceNumbers(reqLoad.Pickup.AdditionalReferences)
	consigneeReferenceNumbers := toMcleodReferenceNumbers(reqLoad.Consignee.AdditionalReferences)

	order = OrderResp{
		Type:            "orders",
		CompanyID:       "TMS",
		ID:              reqLoad.ExternalTMSID,
		Status:          toOrderStatus(reqLoad.Status),
		OrderMode:       string(reqLoad.Mode), // Only "T" for now (truckload)
		OrderTypeID:     orderType,
		OrderAllocation: allocCode,
		RevenueCodeID:   revenueCode,
		OperationsUser:  operationsUser,
		// Salesperson1:        salesperson1,
		// Salesperson1Percent: salesperson1Percent,
		CollectionMethod: colMethod,
		Rate:             reqLoad.RateData.CustomerLineHaulRate,
		RateType:         customerRateType,
		RateUnits: helpers.Ternary(
			strings.ToLower(customerRateType) == "f",
			1,
			reqLoad.RateData.CustomerRateNumUnits,
		),
		FreightCharge:   reqLoad.RateData.CustomerLineHaulCharge.Val,
		FreightChargeC:  customerCurrency,
		CustomerID:      reqLoad.Customer.ExternalTMSID,
		Blnum:           reqLoad.Customer.RefNumber,
		EnteredUserID:   drumkitUser,
		CommodityID:     reqLoad.Specifications.Commodities,
		EquipmentTypeID: equipmentType,
		PalletsRequired: reqLoad.Specifications.PalletsRequired,
		PalletsHowMany:  reqLoad.Specifications.TotalInPalletCount,
		PlanningComment: reqLoad.Specifications.PlanningComment,
		// Mcleod calculates distance upon submission
		Pieces: int(reqLoad.Specifications.TotalPieces.Val),
		Weight: reqLoad.Specifications.TotalWeight.Val,
		// On Mcleod app UI, miles/pounds are enforced
		WeightUm:        toMcleodMeasurementUnit(reqLoad.Specifications.TotalWeight.Unit),
		ShipperStopID:   reqLoad.Pickup.ExternalTMSStopID,
		ConsigneeStopID: reqLoad.Consignee.ExternalTMSStopID,
		ConsigneeRefno:  reqLoad.PONums, // aka reqLoad.Consignee.RefNumber
		Stops: []Stops{
			{
				Type:          "stop",
				TableName:     "stops",
				StopType:      "PU",
				OrderSequence: 1,
				// NOTE: For Trident Mcleod Enterprise, pickup *is* required to have a TMS object ID
				LocationID:       reqLoad.Pickup.ExternalTMSID,
				SchedArriveEarly: formatTime(reqLoad.Pickup.ApptStartTime),
				SchedArriveLate:  formatTime(reqLoad.Pickup.ApptEndTime),
				ApptRequired:     reqLoad.Pickup.ApptRequired,
				Confirmed:        reqLoad.Pickup.ApptConfirmed,
				MovementID:       reqLoad.Carrier.MovementID,
				OrderID:          reqLoad.ExternalTMSID,
				ID:               reqLoad.Pickup.ExternalTMSStopID,
				ReferenceNumbers: pickupReferenceNumbers,
			},
			{
				Type:          "stop",
				TableName:     "stops",
				StopType:      "SO",
				OrderSequence: 2,
				// NOTE: For Trident Mcleod Enterprise, dropoff is *not* required to have a TMS object ID
				LocationID:       reqLoad.Consignee.ExternalTMSID,
				SchedArriveEarly: formatTime(reqLoad.Consignee.ApptStartTime),
				SchedArriveLate:  formatTime(reqLoad.Consignee.ApptEndTime),
				ApptRequired:     reqLoad.Consignee.ApptRequired,
				Confirmed:        reqLoad.Consignee.ApptConfirmed,
				MovementID:       reqLoad.Carrier.MovementID,
				OrderID:          reqLoad.ExternalTMSID,
				ID:               reqLoad.Consignee.ExternalTMSStopID,
				ReferenceNumbers: consigneeReferenceNumbers,
			},
		},
	}

	if m.tms.FeatureFlags.IsOnlyCityStateRequired {
		if reqLoad.Pickup.ExternalTMSID == "" {
			pickupCityID, err := m.getCityID(ctx, reqLoad.Pickup.City, reqLoad.Pickup.State)
			if err != nil {
				return order, errtypes.NewUserFacingError(err)
			}
			order.Stops[0].LocationName = reqLoad.Pickup.Name
			order.Stops[0].Address = reqLoad.Pickup.AddressLine1
			order.Stops[0].Address2 = reqLoad.Pickup.AddressLine2
			order.Stops[0].CityID = pickupCityID
			order.Stops[0].CityName = reqLoad.Pickup.City
			order.Stops[0].State = reqLoad.Pickup.State
			order.Stops[0].ZipCode = reqLoad.Pickup.Zipcode

		}

		if reqLoad.Consignee.ExternalTMSID == "" {
			consigneeCityID, err := m.getCityID(ctx, reqLoad.Consignee.City, reqLoad.Consignee.State)
			if err != nil {
				return order, errtypes.NewUserFacingError(err)
			}
			order.Stops[1].LocationName = reqLoad.Consignee.Name
			order.Stops[1].Address = reqLoad.Consignee.AddressLine1
			order.Stops[1].Address2 = reqLoad.Consignee.AddressLine2
			order.Stops[1].CityID = consigneeCityID
			order.Stops[1].CityName = reqLoad.Consignee.City
			order.Stops[1].State = reqLoad.Consignee.State
			order.Stops[1].ZipCode = reqLoad.Consignee.Zipcode
		}
	}

	// CityID required if consignee ID is not provided
	if reqLoad.Consignee.ExternalTMSID == "" && !m.tms.FeatureFlags.IsOnlyCityStateRequired {
		cityID, err := m.getCityID(ctx, reqLoad.Consignee.City, reqLoad.Consignee.State)
		if err != nil {
			return order, errtypes.NewUserFacingError(err)
		}
		order.Stops[1].LocationName = reqLoad.Consignee.Name
		order.Stops[1].Address = reqLoad.Consignee.AddressLine1
		order.Stops[1].Address2 = reqLoad.Consignee.AddressLine2
		order.Stops[1].CityID = cityID
		order.Stops[1].CityName = reqLoad.Consignee.City
		order.Stops[1].State = reqLoad.Consignee.State
		order.Stops[1].ZipCode = reqLoad.Consignee.Zipcode

	}

	if curLoad != nil {
		order.Movements = []MiniMovement{
			{
				Type:      "movement",
				Name:      "movements",
				CompanyID: "TMS",
				ID:        reqLoad.Carrier.MovementID,
				// NOTE: From testing, if Driver Name (and maybe phone) does not match an existing one,
				// Mcleod creates a new one. If driver info is then updated then reverted, it will match existing
				// driver object instead of creating a duplicate one. So for now, we allow the user to free type
				// instead of pulling driver objects.
				// OverrideDriverID: ,
				OverrideDriverNm: reqLoad.Carrier.FirstDriverName,
				OverrideDrvrCell: reqLoad.Carrier.FirstDriverPhone,
				CarrierTractor:   reqLoad.Carrier.ExternalTMSTruckID,
				CarrierTrailer:   reqLoad.Carrier.ExternalTMSTrailerID,
				// For CWT and Distance types, Mcleod calculates total pay based on rate, so we don't need to set it
				// NOTE: If result OverridePayAmt > max pay, Mcleod won't throw error but will return misformed result
				OverridePayAmt:  reqLoad.RateData.CarrierLineHaulRate * reqLoad.RateData.CarrierRateNumUnits,
				OverridePayRate: reqLoad.RateData.CarrierLineHaulRate,
				OverrideType:    carrierRateType,
			},
		}
	}

	return order, nil
}

func (m *McleodEnterprise) processSearchedOrder(
	ctx context.Context,
	order OrderResp,
	index int,
) (models.Load, error) {
	orderedDate := parseTime(order.OrderedDate)
	if !orderedDate.Valid || !orderedDate.Time.After(searchFrom) {
		// Log and skip orders older than 60 days
		log.Infof(ctx, "order %d is older than 60 days (%s), skipping",
			order.ID, time.Since(orderedDate.Time))
		return models.Load{}, nil
	}

	// Search endpoint does not return movement details, which contains carrier info
	load, _, err := m.GetLoad(ctx, order.ID)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("error getting %dth order from TMS", index),
			zap.Error(err), zap.String("orderID", order.ID))
		return models.Load{}, err
	}

	return load, nil
}

func (m *McleodEnterprise) getCityID(ctx context.Context, city, state string) (int, error) {
	endPoint := "/cities"
	var queryParams = url.Values{}
	queryParams.Add("city", city)
	queryParams.Add("state", state)
	var resp []City

	err := m.get(ctx, endPoint, queryParams, &resp, "")
	if err != nil {
		return 0, errtypes.WrapNewUserFacingError(fmt.Sprintf("%s, %s city lookup failed", city, state), err)
	}

	for _, row := range resp {
		if strings.EqualFold(row.Name, city) && strings.EqualFold(row.StateID, state) {
			return row.ID, nil
		}
	}

	notFoundErr := errtypes.EntityNotFoundError(m.tms, fmt.Sprintf("%s, %s", city, state), "city")
	return 0, errtypes.NewUserFacingError(notFoundErr)
}

func (m *McleodEnterprise) unknownTenantError(entity string) error {
	return fmt.Errorf("unsupported tenant for %s: %s", entity, m.tms.Tenant)
}

func toLoadReferenceNumbers(refNumbers []ReferenceNumbers) models.AdditionalReferences {
	additionalReferences := make(models.AdditionalReferences, len(refNumbers))
	for i, ref := range refNumbers {
		additionalReferences[i] = models.AdditionalReference{
			Qualifier:          ref.ReferenceQual,
			Number:             ref.ReferenceNumber,
			Weight:             ref.Weight,
			Pieces:             ref.Pieces,
			ShouldSendToDriver: ref.SendToDriver,
		}
	}
	return additionalReferences
}

func toMcleodReferenceNumbers(additionalReferences models.AdditionalReferences) []ReferenceNumbers {
	referenceNumbers := make([]ReferenceNumbers, len(additionalReferences))
	for i, ref := range additionalReferences {
		referenceNumbers[i] = ReferenceNumbers{
			ReferenceQual:   ref.Qualifier,
			ReferenceNumber: strings.TrimSpace(ref.Number),
			Weight:          ref.Weight,
			Pieces:          ref.Pieces,
			SendToDriver:    ref.ShouldSendToDriver,
			// required
			PartnerID: "0",
			Type:      "reference_number",
			Version:   "004010",
			ElementID: "128",
			Name:      "referenceNumbers",
			CompanyID: "TMS",
		}
	}

	return referenceNumbers
}

// Collection method is required
func toCollectionMethod(methodLabel string) (methodCode string, err error) {
	switch methodLabel = strings.ToLower(methodLabel); methodLabel {
	case "prepaid", "p":
		return "P", nil

	case "collect", "c":
		return "C", nil

	case "third-party", "t":
		return "T", nil

	case "cod", "d":
		return "D", nil

	}

	return "", fmt.Errorf("unsupported collection method: %s", methodLabel)
}

// Rate type is optional; `rateFor` is either "customer" or "carrier"
func toRateType(rateType string, rateFor string) (rateCode string, err error) {
	switch rateType = strings.ToLower(rateType); rateType {
	case "":
		return "", nil

	case "flat", "f":
		// No units
		return "F", nil

	case "distance", "d", "mileage", "miles", "mile":
		return "D", nil

	case "cwt":
		return "C", nil

	case "ton", "tons":
		return "T", nil
	}

	return "", fmt.Errorf("unsupported %s rate type: %s", rateFor, rateType)
}

func toCurrency(currency string) string {
	switch strings.ToLower(currency) {
	case "usd", "us":
		return "USD"
	case "cad", "can", "ca":
		return "CAD"
	case "eur", "euro":
		return "EUR"
	}
	return currency
}

const timeFormat = "20060102150405-0700"

// Helper function to parse time strings into NullTime
func parseTime(timeStr string) models.NullTime {
	if timeStr == "" {
		return models.NullTime{}
	}
	parsedTime, err := time.Parse(timeFormat, timeStr)
	if err != nil {
		return models.NullTime{}
	}

	return models.NullTime{Time: parsedTime, Valid: true}
}

func formatTime(dt models.NullTime) (timeStr string) {
	if !dt.Valid {
		return ""
	}

	return dt.Time.In(time.UTC).Format(timeFormat)
}

func toMcleodMeasurementUnit(drumkitUnit string) (mcleodUnit string) {
	switch strings.ToLower(drumkitUnit) {
	case models.MilesUnit, "mi":
		return "MI"

	case models.KmUnit:
		return "KM" // To confirm

	case models.LbsUnit, "lb", "pounds", "pound":
		return "LB"

	case models.KgUnit, "kilograms", "kilogram":
		return "KG" // To confirm, Trident UI enforces miles/lbs
	}

	return ""
}

func toOrderStatus(status string) string {
	switch strings.ToLower(status) {
	case "a", "available":
		return "A"
	case "d", "delivered":
		return "D"
	case "q", "quote":
		return "Q"
	case "p", "progress":
		return "P"
	case "v", "void":
		return "V"
	default:
		return ""
	}
}

func mapCompanyCoreInfo(stop Stops, stopResp StopResp) models.CompanyCoreInfo {
	return models.CompanyCoreInfo{
		ExternalTMSID: stop.LocationID,
		Name:          stop.LocationName,
		AddressLine1:  stop.Address,
		AddressLine2:  stop.Address2,
		City:          stop.CityName,
		State:         stop.State,
		Zipcode:       stop.ZipCode,
		Country:       "",
		Contact:       stopResp.ContactName,
		Phone:         stopResp.Phone,
		Email:         "",
	}
}

func extractStopNote(stop Stops) string {
	var res string
	for _, note := range stop.StopNotes {
		res += " " + note.CommentTypeDescr + ": " + note.Comments
	}

	return res
}
