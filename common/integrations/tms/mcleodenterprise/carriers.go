package mcleodenterprise

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) GetCarriers(ctx context.Context) (carriers []models.TMSCarrier, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetLocationsMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	const recordLength = 1000
	i := 0
	queryParams := url.Values{}
	queryParams.Set("status", "A")
	queryParams.Set("recordLength", fmt.Sprint(recordLength))

	for {
		queryParams.Set("recordOffset", fmt.Sprint(i*recordLength))

		var carrierResp []CarrierResp
		err = m.get(ctx, "/carriers/search", queryParams, &carrierResp, s3backup.TypeCarriers)
		var httpErr errtypes.HTTPResponseError
		if err != nil && errors.As(err, &httpErr) && httpErr.StatusCode != http.StatusNotFound {
			return nil, err
		} else if err != nil {
			log.WarnNoSentry(ctx, "got 404 from Mcleod, failing open", zap.Error(err))
		}

		for _, data := range carrierResp {
			carrier := models.TMSCarrier{
				TMSIntegrationID: m.tms.ID,
				DOTNumber:        data.DrsPayee.DotNumber,
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: data.ID,
					Name:          data.Name,
					AddressLine1:  data.Address1,
					AddressLine2:  "",
					City:          data.City,
					State:         data.State,
					Zipcode:       data.ZipCode,
					Phone:         data.PhoneNumber,
					Email:         data.Email,
				}}

			carriers = append(carriers, carrier)
		}

		i++
		if len(carrierResp) < recordLength {
			break
		}
	}

	return carriers, nil
}

func (m *McleodEnterprise) GetCarrierQualification(
	ctx context.Context,
	curLoad *models.Load,
	carrierIDToCheck string,
) (isQualified bool, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCarrierQualificationMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	queryParams.Add("movement", curLoad.Carrier.MovementID)
	queryParams.Add("carrier", carrierIDToCheck)

	var headerMap = make(map[string]string)
	headerMap["Accept"] = "text/plain"

	var resp string
	if err = m.get(ctx, "/carriers/checkQualification", queryParams, &resp, "", headerMap); err != nil {
		return false, err
	}

	return strings.ToLower(resp) == "true", nil

}
