package mcleodenterprise

import (
	"context"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaspan := otel.StartSpan(ctx, "CreateQuoteMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaspan.End(err) }()

	quoteReq := m.buildCreateQuoteBody(ctx, quoteBody)
	var quoteResp QuoteReqResp
	err = m.put(ctx, "/quotes/create", quoteReq, &quoteResp, s3backup.TypeQuotes)
	if err != nil {
		return nil, err
	}

	// TODO: Remove in favor of QuoteExternalID as string; kept for FE backwards compatibility
	if quoteID, parseErr := strconv.Atoi(quoteResp.ID); parseErr == nil {
		quoteResponse.QuoteID = quoteID
	}

	quoteResponse.QuoteExternalID = quoteResp.ID
	return
}

func (m *McleodEnterprise) buildCreateQuoteBody(ctx context.Context, quoteBody models.CreateQuoteBody) QuoteReqResp {
	timeFormat := "20060102150405-0700"
	var equipmentTypeID string
	// todo : need to confirm these equipment ids. Currently, hardcoded with random values.
	switch quoteBody.TransportType {
	case models.VanTransportType:
		equipmentTypeID = "1"
	case models.ReeferTransportType:
		equipmentTypeID = "2"
	case models.FlatbedTransportType:
		equipmentTypeID = "3"
	default:
		log.WarnNoSentry(ctx,
			"couldn't match transport type while creating mcleodEnterprise quote",
			zap.String("transport_type", string(quoteBody.TransportType)),
		)
	}
	var pickupDate, deliveryDate string
	if quoteBody.PickupDate.Valid {
		pickupDate = quoteBody.PickupDate.Time.Format(timeFormat)
	}
	if quoteBody.DeliveryDate.Valid {
		deliveryDate = quoteBody.DeliveryDate.Time.Format(timeFormat)
	}
	return QuoteReqResp{
		CustomerID:       quoteBody.CustomerID,
		ShipDate:         pickupDate,
		EquipmentTypeID:  equipmentTypeID,
		ShipperZipCode:   quoteBody.PickupLocationZip,
		DeliveryDate:     deliveryDate,
		ConsigneeZipCode: quoteBody.DeliveryLocationZip,
	}
}
