package mcleodenterprise

// TODO : Need to confirm this struct

// <PERSON>ginResp a response indicating success or failure and if success, the access token.
// struct is made on the basis of above string.
type LoginResp struct {
	Status      string `json:"status,omitempty"`
	AccessToken string `json:"access_token,omitempty"`
}

type TMSCommodity struct {
	Code        string
	Description string
	Hazmat      string
	UN          string
}

type OrderResp struct {
	Type                     string         `json:"__type,omitempty"`
	ID                       string         `json:"id,omitempty"`
	CompanyID                string         `json:"company_id,omitempty"`
	BookingNo                string         `json:"booking_no,omitempty"`
	AllowRelay               bool           `json:"allow_relay,omitempty"`
	BillDistance             float32        `json:"bill_distance,omitempty"`
	BillDistanceUm           string         `json:"bill_distance_um,omitempty"`
	BillingEmptyDistance     float32        `json:"billing_empty_distance,omitempty"`
	BillingLoadedDistance    float32        `json:"billing_loaded_distance,omitempty"`
	Blnum                    string         `json:"blnum,omitempty"`
	BolReceived              bool           `json:"bol_received,omitempty"`
	CollectionMethod         string         `json:"collection_method,omitempty"`
	CommodityID              string         `json:"commodity_id,omitempty"`
	ConsigneeRefno           string         `json:"consignee_refno,omitempty"`
	ConsigneeStopID          string         `json:"consignee_stop_id,omitempty"`
	Salesperson1             string         `json:"salesperson1,omitempty"`
	Salesperson1Percent      float32        `json:"salesperson1percent,omitempty"`
	CurrMovementID           string         `json:"curr_movement_id,omitempty"`
	CustomerID               string         `json:"customer_id,omitempty"`
	JagCarrierID             string         `json:"jag_carrier_id,omitempty"`
	SealNumber               string         `json:"seal_number,omitempty"`
	Ponum                    string         `json:"ponum,omitempty"`
	DispatchOpt              bool           `json:"dispatch_opt,omitempty"`
	EnteredUserID            string         `json:"entered_user_id,omitempty"`
	EquipmentTypeID          string         `json:"equipment_type_id,omitempty"`
	EstTolls                 float32        `json:"est_tolls,omitempty"`
	EstTollsC                string         `json:"est_tolls_c,omitempty"`
	EstTollsD                string         `json:"est_tolls_d,omitempty"`
	EstTollsN                float32        `json:"est_tolls_n,omitempty"`
	EstTollsR                float32        `json:"est_tolls_r,omitempty"`
	ExciseDisableUpdate      bool           `json:"excise_disable_update,omitempty"`
	ExciseTaxable            bool           `json:"excise_taxable,omitempty"`
	ExcisetaxTotal           float32        `json:"excisetax_total,omitempty"`
	ExcisetaxTotalC          string         `json:"excisetax_total_c,omitempty"`
	ExcisetaxTotalD          string         `json:"excisetax_total_d,omitempty"`
	ExcisetaxTotalN          float32        `json:"excisetax_total_n,omitempty"`
	ExcisetaxTotalR          float32        `json:"excisetax_total_r,omitempty"`
	ExtraDeliveries          int            `json:"extra_deliveries,omitempty"`
	ExtraPickups             int            `json:"extra_pickups,omitempty"`
	ForceAssign              bool           `json:"force_assign,omitempty"`
	FreightCharge            float32        `json:"freight_charge,omitempty"`
	FreightChargeC           string         `json:"freight_charge_c,omitempty"` // currency
	FreightChargeD           string         `json:"freight_charge_d,omitempty"`
	FreightChargeN           float32        `json:"freight_charge_n,omitempty"`
	FreightChargeR           float32        `json:"freight_charge_r,omitempty"`
	Hazmat                   bool           `json:"hazmat,omitempty"`
	HighValue                bool           `json:"high_value,omitempty"`
	ImageIsPresent           bool           `json:"image_is_present,omitempty"`
	IncludeSplitPoint        bool           `json:"include_split_point,omitempty"`
	IsAutorateDist           bool           `json:"is_autorate_dist,omitempty"`
	IsDedicated              bool           `json:"is_dedicated,omitempty"`
	IsLocalMile              bool           `json:"is_local_mile,omitempty"`
	Loadboard                bool           `json:"loadboard,omitempty"`
	Ltl                      bool           `json:"ltl,omitempty"`
	OnHold                   bool           `json:"on_hold,omitempty"`
	OperationsUser           string         `json:"operations_user,omitempty"`
	OrderTypeID              string         `json:"order_type_id,omitempty"`
	OrderedBy                string         `json:"ordered_by,omitempty"`
	OrderedDate              string         `json:"ordered_date,omitempty"`
	OrderedMethod            string         `json:"ordered_method,omitempty"`
	Otherchargetotal         float32        `json:"otherchargetotal,omitempty"`
	OtherchargetotalC        string         `json:"otherchargetotal_c,omitempty"`
	OtherchargetotalD        string         `json:"otherchargetotal_d,omitempty"`
	OtherchargetotalN        float32        `json:"otherchargetotal_n,omitempty"`
	OtherchargetotalR        float32        `json:"otherchargetotal_r,omitempty"`
	PalletsRequired          bool           `json:"pallets_required,omitempty"`
	PalletsHowMany           int            `json:"pallets_how_many,omitempty"`
	Pieces                   int            `json:"pieces,omitempty"`
	Preloaded                bool           `json:"preloaded,omitempty"`
	Rate                     float32        `json:"rate,omitempty"`
	RateType                 string         `json:"rate_type,omitempty"`
	RateUnits                float32        `json:"rate_units,omitempty"`
	ReadyToBill              bool           `json:"ready_to_bill,omitempty"`
	PlanningComment          string         `json:"planning_comment,omitempty"`
	ReplyTransmitted         bool           `json:"reply_transmitted,omitempty"`
	RevenueCodeID            string         `json:"revenue_code_id,omitempty"`
	OrderAllocation          string         `json:"seg_alloc_code,omitempty"`
	RoundTrip                bool           `json:"round_trip,omitempty"`
	ShipStatusToEdi          bool           `json:"ship_status_to_edi,omitempty"`
	ShipmentID               string         `json:"shipment_id,omitempty"`
	ShipperStopID            string         `json:"shipper_stop_id,omitempty"`
	Status                   string         `json:"status,omitempty"`
	Swap                     bool           `json:"swap,omitempty"`
	TeamsRequired            bool           `json:"teams_required,omitempty"`
	TemperatureMax           float32        `json:"temperature_max,omitempty"`
	TemperatureMin           float32        `json:"temperature_min,omitempty"`
	TotalCharge              float32        `json:"total_charge,omitempty"`
	TotalChargeC             string         `json:"total_charge_c,omitempty"`
	TotalChargeD             string         `json:"total_charge_d,omitempty"`
	TotalChargeN             float32        `json:"total_charge_n,omitempty"`
	TotalChargeR             float32        `json:"total_charge_r,omitempty"`
	TotalchargeAndExcisetax  float32        `json:"totalcharge_and_excisetax,omitempty"`
	TotalchargeAndExcisetaxC string         `json:"totalcharge_and_excisetax_c,omitempty"`
	TotalchargeAndExcisetaxD string         `json:"totalcharge_and_excisetax_d,omitempty"`
	TotalchargeAndExcisetaxN float32        `json:"totalcharge_and_excisetax_n,omitempty"`
	TotalchargeAndExcisetaxR float32        `json:"totalcharge_and_excisetax_r,omitempty"`
	Weight                   float32        `json:"weight,omitempty"`
	WeightUm                 string         `json:"weight_um,omitempty"`
	Xferred2Billing          bool           `json:"xferred2billing,omitempty"`
	OrderMode                string         `json:"order_mode,omitempty"`
	OperationalStatus        string         `json:"operational_status,omitempty"`
	DefaultMatchID           string         `json:"default_match_id,omitempty"`
	SalespersonID            string         `json:"salesperson_id,omitempty"`
	StatusDescr              string         `json:"__statusDescr,omitempty"`
	CollectionMethodDescr    string         `json:"__collectionMethodDescr,omitempty"`
	RateTypeDescr            string         `json:"__rateTypeDescr,omitempty"`
	RevenueTypeDescr         string         `json:"__revenueTypeDescr,omitempty"`
	OrderTypeDescr           string         `json:"__orderTypeDescr,omitempty"`
	EquipmentTypeDescr       string         `json:"__equipmentTypeDescr,omitempty"`
	EnteredUser              *User          `json:"enteredUser,omitempty"`
	OperationsUser0          *User          `json:"operationsUser,omitempty"`
	FreightGroup             *FreightGroup  `json:"freightGroup,omitempty"`
	Customer                 *Customer      `json:"customer,omitempty"`
	Stops                    []Stops        `json:"stops,omitempty"`
	Movements                []MiniMovement `json:"movements,omitempty"`
}

type User struct {
	Type      string `json:"__type,omitempty"`
	TableName string `json:"__name,omitempty"`     // e.g. "user""
	CompanyID string `json:"company_id,omitempty"` // e.g. "TMS"

	ID                   string `json:"id,omitempty"`
	Name                 string `json:"name,omitempty"` // Actual name of the user, e.g. John Doe
	ActiveDate           string `json:"active_date,omitempty"`
	Agent                bool   `json:"agent,omitempty"`
	AgreeFieldmarshal    string `json:"agree_fieldmarshal,omitempty"`
	Available            bool   `json:"available,omitempty"`
	BrokeragePlanning    string `json:"brokerage_planning,omitempty"`
	ConfirmRecord        bool   `json:"confirm_record,omitempty"`
	DailyAdvanceLimits   string `json:"daily_advance_limits,omitempty"`
	DateFormat           string `json:"date_format,omitempty"`
	EmailAddress         string `json:"email_address,omitempty"`
	EnableAlerts         bool   `json:"enable_alerts,omitempty"`
	GridInsertSort       bool   `json:"grid_insert_sort,omitempty"`
	IsActive             bool   `json:"is_active,omitempty"`
	IsClearer            bool   `json:"is_clearer,omitempty"`
	IsCloser             bool   `json:"is_closer,omitempty"`
	IssueAtmCash         bool   `json:"issue_atm_cash,omitempty"`
	LoginID              string `json:"login_id,omitempty"`
	NotifyOccurance      string `json:"notify_occurance,omitempty"` //nolint:misspell // From Trident
	OperationsUserID     string `json:"operations_user_id,omitempty"`
	OrderTranRequest     bool   `json:"order_tran_request,omitempty"`
	Phone                string `json:"phone,omitempty"`
	Planner              bool   `json:"planner,omitempty"`
	PromptContMoves      string `json:"prompt_cont_moves,omitempty"`
	ReceiveAlerts        bool   `json:"receive_alerts,omitempty"`
	SaveScreenSizes      bool   `json:"save_screen_sizes,omitempty"`
	SelTextOnFocus       bool   `json:"sel_text_on_focus,omitempty"`
	SystemTray           string `json:"system_tray,omitempty"`
	TimeFormat           string `json:"time_format,omitempty"`
	ToolbarMultiRows     bool   `json:"toolbar_multi_rows,omitempty"`
	ToolbarSetting       string `json:"toolbar_setting,omitempty"`
	TrailerWashUser      bool   `json:"trailer_wash_user,omitempty"`
	TypeAgents           bool   `json:"type_agents,omitempty"`
	TypeCarrier          bool   `json:"type_carrier,omitempty"`
	TypeCompanyDrs       bool   `json:"type_company_drs,omitempty"`
	TypeOwnerOper        bool   `json:"type_owner_oper,omitempty"`
	WebAgent             bool   `json:"web_agent,omitempty"`
	WebUserType          string `json:"web_user_type,omitempty"`
	EmailProtocol        string `json:"email_protocol,omitempty"`
	EmailPort            int    `json:"email_port,omitempty"`
	EmailClient          string `json:"email_client,omitempty"`
	QuotePendActCreate   bool   `json:"quote_pend_act_create,omitempty"`
	QuoteCompActCreate   bool   `json:"quote_comp_act_create,omitempty"`
	EmailOverrideCompany bool   `json:"email_override_company,omitempty"`
	EmailHost            string `json:"email_host,omitempty"`
	IsDockWorker         bool   `json:"is_dock_worker,omitempty"`
	IsDockSupervisor     bool   `json:"is_dock_supervisor,omitempty"`
	AllContacts          bool   `json:"all_contacts,omitempty"`
	WebAcctStatus        string `json:"web_acct_status,omitempty"`
	MoveDates            bool   `json:"move_dates,omitempty"`
}

type FgpXBfgs struct {
	Type      string `json:"__type,omitempty"`
	FgpUID    int    `json:"fgp_uid,omitempty"`
	BfgUID    int    `json:"bfg_uid,omitempty"`
	CompanyID string `json:"company_id,omitempty"`
	FxbUID    int    `json:"fxb_uid,omitempty"`
}

type FreightGroup struct {
	Type                  string     `json:"__type,omitempty"`
	FgpStatusCode         string     `json:"fgp_status_code,omitempty"`
	OrdUID                int        `json:"ord_uid,omitempty"`
	ShipPlcUID            int        `json:"ship_plc_uid,omitempty"`
	OrigTxlUID            int        `json:"orig_txl_uid,omitempty"`
	ConsPlcUID            int        `json:"cons_plc_uid,omitempty"`
	DestTxlUID            int        `json:"dest_txl_uid,omitempty"`
	AddUserid             string     `json:"add_userid,omitempty"`
	AddTimestamp          string     `json:"add_timestamp,omitempty"`
	ModUserid             string     `json:"mod_userid,omitempty"`
	ModTimestamp          string     `json:"mod_timestamp,omitempty"`
	FgpTypeCode           string     `json:"fgp_type_code,omitempty"`
	ConveyanceOwnerPlcUID int        `json:"conveyance_owner_plc_uid,omitempty"`
	WeightUomTypeCode     string     `json:"weight_uom_type_code,omitempty"`
	LmeOrderID            string     `json:"lme_order_id,omitempty"`
	CompanyID             string     `json:"company_id,omitempty"`
	FgpUID                int        `json:"fgp_uid,omitempty"`
	BolProcessed          bool       `json:"bol_processed,omitempty"`
	FgpXBfgs              []FgpXBfgs `json:"fgpXBfgs,omitempty"`
}

type Customer struct {
	Type      string `json:"__type,omitempty"`
	CompanyID string `json:"company_id,omitempty"` // e.g. "TMS"

	Name                  string  `json:"name,omitempty"`
	AddEmptyDistanceOcc   bool    `json:"add_empty_distance_occ,omitempty"`
	AddFinanceCharge      bool    `json:"add_finance_charge,omitempty"`
	Address1              string  `json:"address1,omitempty"`
	Address2              string  `json:"address2,omitempty"`
	AllowIncInFrt         bool    `json:"allow_inc_in_frt,omitempty"`
	AverageBill           float32 `json:"average_bill,omitempty"`
	AverageBillC          string  `json:"average_bill_c,omitempty"`
	AverageBillD          string  `json:"average_bill_d,omitempty"`
	AverageBillN          float32 `json:"average_bill_n,omitempty"`
	AverageBillR          float32 `json:"average_bill_r,omitempty"`
	AveragePayDays        float32 `json:"average_pay_days,omitempty"`
	Balance               float32 `json:"balance,omitempty"`
	BalanceC              string  `json:"balance_c,omitempty"`
	BalanceD              string  `json:"balance_d,omitempty"`
	BalanceN              float32 `json:"balance_n,omitempty"`
	BalanceR              float32 `json:"balance_r,omitempty"`
	BillDueDays           int     `json:"bill_due_days,omitempty"`
	BillTransferWhen      string  `json:"bill_transfer_when,omitempty"`
	BilledLoads           int     `json:"billed_loads,omitempty"`
	BrokerageFilter       bool    `json:"brokerage_filter,omitempty"`
	City                  string  `json:"city,omitempty"`
	CityID                int     `json:"city_id,omitempty"`
	CollectionsID         string  `json:"collections_id,omitempty"`
	CollectionsOk         bool    `json:"collections_ok,omitempty"`
	ConversionDate        string  `json:"conversion_date,omitempty"`
	CreditApplication     bool    `json:"credit_application,omitempty"`
	CreditCheckDate       string  `json:"credit_check_date,omitempty"`
	CreditLimit           float32 `json:"credit_limit,omitempty"`
	CreditLimitC          string  `json:"credit_limit_c,omitempty"`
	CreditLimitD          string  `json:"credit_limit_d,omitempty"`
	CreditLimitN          float32 `json:"credit_limit_n,omitempty"`
	CreditLimitR          float32 `json:"credit_limit_r,omitempty"`
	CreditStatus          string  `json:"credit_status,omitempty"`
	CreditWarningPct      float32 `json:"credit_warning_pct,omitempty"`
	DAndBCode             string  `json:"d_and_b_code,omitempty"`
	DAndBDate             string  `json:"d_and_b_date,omitempty"`
	DAndBNumber           string  `json:"d_and_b_number,omitempty"`
	EnforceDepDates       bool    `json:"enforce_dep_dates,omitempty"`
	EnteredDate           string  `json:"entered_date,omitempty"`
	FactorReceivables     bool    `json:"factor_receivables,omitempty"`
	GpExclude             bool    `json:"gp_exclude,omitempty"`
	HighBalance           float32 `json:"high_balance,omitempty"`
	HighBalanceC          string  `json:"high_balance_c,omitempty"`
	HighBalanceD          string  `json:"high_balance_d,omitempty"`
	HighBalanceN          float32 `json:"high_balance_n,omitempty"`
	HighBalanceR          float32 `json:"high_balance_r,omitempty"`
	ID                    string  `json:"id,omitempty"`
	IncludeEmptyDistance  bool    `json:"include_empty_distance,omitempty"`
	IncludeFuelSurcharge  bool    `json:"include_fuel_surcharge,omitempty"`
	IsActive              string  `json:"is_active,omitempty"`
	IsConsignee           bool    `json:"is_consignee,omitempty"`
	IsDistributionCenter  bool    `json:"is_distribution_center,omitempty"`
	IsDropYard            bool    `json:"is_drop_yard,omitempty"`
	IsGeocoded            bool    `json:"is_geocoded,omitempty"`
	IsPalletsRequired     bool    `json:"is_pallets_required,omitempty"`
	IsShipper             bool    `json:"is_shipper,omitempty"`
	IsTrailerPoolRequired bool    `json:"is_trailer_pool_required,omitempty"`
	LastBillDate          string  `json:"last_bill_date,omitempty"`
	LastPayDate           string  `json:"last_pay_date,omitempty"`
	LastShipDate          string  `json:"last_ship_date,omitempty"`
	Latitude              float32 `json:"latitude,omitempty"`
	Longitude             float32 `json:"longitude,omitempty"`
	ManualExchange        bool    `json:"manual_exchange,omitempty"`
	MaxPayPercent         float32 `json:"max_pay_percent,omitempty"`
	NxtActionDate         string  `json:"nxt_action_date,omitempty"`
	PaidLoads             int     `json:"paid_loads,omitempty"`
	PayDaysOrders         int     `json:"pay_days_orders,omitempty"`
	PlcUID                int     `json:"plc_uid,omitempty"`
	PostSummaryDtl        bool    `json:"post_summary_dtl,omitempty"`
	PrimarySort           string  `json:"primary_sort,omitempty"`
	PrintContainers       bool    `json:"print_containers,omitempty"`
	SalespersonID         string  `json:"salesperson_id,omitempty"`
	SearchCity            string  `json:"search_city,omitempty"`
	SearchStateID         string  `json:"search_state_id,omitempty"`
	SearchZipCode         string  `json:"search_zip_code,omitempty"`
	StateID               string  `json:"state_id,omitempty"`
	SummaryBilling        string  `json:"summary_billing,omitempty"`
	TargetProfit          float32 `json:"target_profit,omitempty"`
	TypeOf                string  `json:"type_of,omitempty"`
	UseLocalMile          bool    `json:"use_local_mile,omitempty"`
	VMPartUpdate          bool    `json:"vm_part_update,omitempty"`
	ZipCode               string  `json:"zip_code,omitempty"`
	ZoneID                string  `json:"zone_id,omitempty"`
	Pd3SpAuto             bool    `json:"pd3_sp_auto,omitempty"`
	Pd3NpAuto             bool    `json:"pd3_np_auto,omitempty"`
	Pd1SpAuto             bool    `json:"pd1_sp_auto,omitempty"`
	Pd4SpAuto             bool    `json:"pd4_sp_auto,omitempty"`
	Pd4NpAuto             bool    `json:"pd4_np_auto,omitempty"`
	Pd1NpAuto             bool    `json:"pd1_np_auto,omitempty"`
	Pd2SpAuto             bool    `json:"pd2_sp_auto,omitempty"`
	Pd2NpAuto             bool    `json:"pd2_np_auto,omitempty"`
	OrderCount            int     `json:"order_count,omitempty"`
	Revenue               float32 `json:"revenue,omitempty"`
	RevenueC              string  `json:"revenue_c,omitempty"`
	RevenueD              string  `json:"revenue_d,omitempty"`
	RevenueN              float32 `json:"revenue_n,omitempty"`
	RevenueR              float32 `json:"revenue_r,omitempty"`
	ActivityCount         int     `json:"activity_count,omitempty"`
	GlobalDetention       bool    `json:"global_detention,omitempty"`
	ServiceFailuresReview bool    `json:"service_failures_review,omitempty"`
	EnableNotifications   bool    `json:"enable_notifications,omitempty"`
	ChargeDetention       bool    `json:"charge_detention,omitempty"`
	ExtendRates           string  `json:"extend_rates,omitempty"`
	OrigSalesperson       string  `json:"orig_salesperson,omitempty"`
	SalespersonLtl        string  `json:"salesperson_ltl,omitempty"`
	SalespersonDray       string  `json:"salesperson_dray,omitempty"`
}

type ReferenceNumbers struct {
	Type               string  `json:"__type,omitempty"`
	Name               string  `json:"__name,omitempty"`
	CompanyID          string  `json:"company_id,omitempty"`
	ElementID          string  `json:"element_id,omitempty"`
	ID                 string  `json:"id,omitempty"`
	PartnerID          string  `json:"partner_id,omitempty"`
	Pieces             int     `json:"pieces,omitempty"`
	ReferenceNumber    string  `json:"reference_number,omitempty"`
	ReferenceQual      string  `json:"reference_qual,omitempty"`
	SendToDriver       bool    `json:"send_to_driver,omitempty"`
	StopID             string  `json:"stop_id,omitempty"`
	Version            string  `json:"version,omitempty"`
	Weight             float32 `json:"weight,omitempty"`
	ReferenceQualDescr string  `json:"__referenceQualDescr,omitempty"`
}

type StopNotes struct {
	Type             string `json:"__type,omitempty"`
	CompanyID        string `json:"company_id,omitempty"`
	CommentType      string `json:"comment_type,omitempty"`
	Comments         string `json:"comments,omitempty"`
	EnteredUserID    string `json:"entered_user_id,omitempty"`
	ID               string `json:"id,omitempty"`
	Sequence         int    `json:"sequence,omitempty"`
	StopID           string `json:"stop_id,omitempty"`
	SystemAdded      bool   `json:"system_added,omitempty"`
	CommentTypeDescr string `json:"__commentTypeDescr,omitempty"`
	OrderID          string `json:"order_id,omitempty"`
}

type Stops struct {
	Type                   string             `json:"__type,omitempty"`
	TableName              string             `json:"__name,omitempty"` // Not to be confused with the actual Stop name
	CompanyID              string             `json:"company_id,omitempty"`
	ActualArrival          string             `json:"actual_arrival,omitempty"`
	ActualDeparture        string             `json:"actual_departure,omitempty"`
	Address                string             `json:"address,omitempty"`
	Address2               string             `json:"address2,omitempty"`
	ApptRequired           bool               `json:"appt_required,omitempty"`
	Cases                  int                `json:"cases,omitempty"`
	Confirmed              bool               `json:"confirmed,omitempty"`
	CityID                 int                `json:"city_id,omitempty"`
	CityName               string             `json:"city_name,omitempty"`
	ContactName            string             `json:"contact_name,omitempty"`
	DriverLoadUnload       string             `json:"driver_load_unload,omitempty"`
	EdiLocCode             string             `json:"edi_loc_code,omitempty"`
	ID                     string             `json:"id,omitempty"`
	LateEtaColorcode       bool               `json:"late_eta_colorcode,omitempty"`
	Latitude               float32            `json:"latitude,omitempty"`
	LocationID             string             `json:"location_id,omitempty"`
	LocationName           string             `json:"location_name,omitempty"`
	Longitude              float32            `json:"longitude,omitempty"`
	MoveDistFromPrevious   float32            `json:"move_dist_from_previous,omitempty"`
	MoveDistFromPreviousUm string             `json:"move_dist_from_previous_um,omitempty"`
	MovementID             string             `json:"movement_id,omitempty"`
	MovementSequence       int                `json:"movement_sequence,omitempty"`
	OrderID                string             `json:"order_id,omitempty"`
	OrderSequence          int                `json:"order_sequence,omitempty"`
	Phone                  string             `json:"phone,omitempty"`
	SchedArriveEarly       string             `json:"sched_arrive_early,omitempty"`
	SchedArriveLate        string             `json:"sched_arrive_late,omitempty"`
	State                  string             `json:"state,omitempty"`
	Status                 string             `json:"status,omitempty"`
	StopType               string             `json:"stop_type,omitempty"`
	Volume                 float32            `json:"volume,omitempty"`
	Weight                 float32            `json:"weight,omitempty"`
	ZipCode                string             `json:"zip_code,omitempty"`
	ZoneID                 string             `json:"zone_id,omitempty"`
	ManifestFgpUID         int                `json:"manifest_fgp_uid,omitempty"`
	TxlUID                 int                `json:"txl_uid,omitempty"`
	RequestedService       bool               `json:"requested_service,omitempty"`
	PriorUnclearedStops    bool               `json:"prior_uncleared_stops,omitempty"`
	StatusDescr            string             `json:"__statusDescr,omitempty"`
	TypeDescr              string             `json:"__typeDescr,omitempty"`
	LoadUnloadDescr        string             `json:"__loadUnloadDescr,omitempty"`
	ZoneDescr              string             `json:"__zoneDescr,omitempty"`
	GroupingKey            string             `json:"__groupingKey,omitempty"`
	ReferenceNumbers       []ReferenceNumbers `json:"referenceNumbers,omitempty"`
	StopNotes              []StopNotes        `json:"stopNotes,omitempty"`
	RateDistFromPrevious   float32            `json:"rate_dist_from_previous,omitempty"`
	RateDistFromPreviousUm string             `json:"rate_dist_from_previous_um,omitempty"`
}

type MiniMovement struct {
	Name              string `json:"__name,omitempty"`
	Type              string `json:"__type,omitempty"`
	CompanyID         string `json:"company_id,omitempty"`
	Authorized        bool   `json:"authorized,omitempty"`
	Brokerage         bool   `json:"brokerage,omitempty"`
	BrokerageStatus   string `json:"brokerage_status,omitempty"`
	CarrierContact    string `json:"carrier_contact,omitempty"`
	CarrierEmail      string `json:"carrier_email,omitempty"`
	CarrierPhone      string `json:"carrier_phone,omitempty"`
	CarrierTractor    string `json:"carrier_tractor,omitempty"`
	CarrierTrailer    string `json:"carrier_trailer,omitempty"`
	OverrideDriverID  int    `json:"override_driver_id"`
	OverrideDriverNm  string `json:"override_driver_nm"`
	OverrideDrvrCell  string `json:"override_drvr_cell"`
	OverrideDrvrEmail string `json:"override_drvr_email"`

	DestStopID              string  `json:"dest_stop_id,omitempty"`
	DispatcherUserID        string  `json:"dispatcher_user_id,omitempty"`
	ExcludeMovement         bool    `json:"exclude_movement,omitempty"`
	FuelDistance            float32 `json:"fuel_distance,omitempty"`
	FuelDistanceUm          string  `json:"fuel_distance_um,omitempty"`
	ID                      string  `json:"id,omitempty"`
	IsContainer             bool    `json:"is_container,omitempty"`
	IsDray                  bool    `json:"is_dray,omitempty"`
	IsLocalMile             bool    `json:"is_local_mile,omitempty"`
	Loaded                  string  `json:"loaded,omitempty"`
	Ltl                     bool    `json:"ltl,omitempty"`
	MaxBuy                  float32 `json:"max_buy,omitempty"`
	MaxBuyC                 string  `json:"max_buy_c,omitempty"`
	MaxBuyD                 string  `json:"max_buy_d,omitempty"`
	MaxBuyN                 float32 `json:"max_buy_n,omitempty"`
	MaxBuyR                 float32 `json:"max_buy_r,omitempty"`
	MaxPayMethod            string  `json:"max_pay_method,omitempty"`
	MaxPayUnits             float32 `json:"max_pay_units,omitempty"`
	MissedCallSent          bool    `json:"missed_call_sent,omitempty"`
	MoveDistance            float32 `json:"move_distance,omitempty"`
	MoveDistanceUm          string  `json:"move_distance_um,omitempty"`
	OperationsUser          string  `json:"operations_user,omitempty"`
	OriginStopID            string  `json:"origin_stop_id,omitempty"`
	OverridePayAmt          float32 `json:"override_pay_amt,omitempty"`
	OverridePayAmtC         string  `json:"override_pay_amt_c,omitempty"`
	OverridePayAmtD         string  `json:"override_pay_amt_d,omitempty"`
	OverridePayAmtN         float32 `json:"override_pay_amt_n,omitempty"`
	OverridePayAmtR         float32 `json:"override_pay_amt_r,omitempty"`
	OverridePayRate         float32 `json:"override_pay_rate,omitempty"`
	OverridePayeeID         string  `json:"override_payee_id,omitempty"`
	OverrideType            string  `json:"override_type,omitempty"`
	OverrideUnits           float32 `json:"override_units,omitempty"`
	ReminderSent            bool    `json:"reminder_sent,omitempty"`
	SegAllocCode            string  `json:"seg_alloc_code,omitempty"`
	Status                  string  `json:"status,omitempty"`
	TargetPay               float32 `json:"target_pay,omitempty"`
	TargetPayC              string  `json:"target_pay_c,omitempty"`
	TargetPayD              string  `json:"target_pay_d,omitempty"`
	TargetPayN              float32 `json:"target_pay_n,omitempty"`
	TargetPayR              float32 `json:"target_pay_r,omitempty"`
	TargetPayMethod         string  `json:"target_pay_method,omitempty"`
	TargetPayUnits          float32 `json:"target_pay_units,omitempty"`
	TrpUID                  int     `json:"trp_uid,omitempty"`
	MovementType            string  `json:"movement_type,omitempty"`
	EformRateConfirmation   bool    `json:"eform_rate_confirmation,omitempty"`
	EstTolls                float32 `json:"est_tolls,omitempty"`
	EstTollsC               string  `json:"est_tolls_c,omitempty"`
	EstTollsD               string  `json:"est_tolls_d,omitempty"`
	EstTollsN               float32 `json:"est_tolls_n,omitempty"`
	EstTollsR               float32 `json:"est_tolls_r,omitempty"`
	WaterfallInProgress     bool    `json:"waterfall_in_progress,omitempty"`
	Reserved                bool    `json:"reserved,omitempty"`
	IntegratedCarrierSearch bool    `json:"integrated_carrier_search,omitempty"`
	TargetExtraStopPay      float32 `json:"target_extra_stop_pay,omitempty"`
	TargetExtraStopPayC     string  `json:"target_extra_stop_pay_c,omitempty"`
	TargetExtraStopPayD     string  `json:"target_extra_stop_pay_d,omitempty"`
	TargetExtraStopPayN     float32 `json:"target_extra_stop_pay_n,omitempty"`
	TargetExtraStopPayR     float32 `json:"target_extra_stop_pay_r,omitempty"`
	TriumphpayExclude       bool    `json:"triumphpay_exclude,omitempty"`
	StatusDescr             string  `json:"__statusDescr,omitempty"`
	OverrideTypeDescr       string  `json:"__overrideTypeDescr,omitempty"`
	BrokerageStatusDescr    string  `json:"__brokerageStatusDescr,omitempty"`
}

// Full Movement object returned by /movements endpoint;
// NOTE: additional rate data fields excluded, if needed in the future
type MovementResp struct {
	Type                    string  `json:"__type,omitempty"`
	CompanyID               string  `json:"company_id,omitempty"`
	Authorized              bool    `json:"authorized,omitempty"`
	Brokerage               bool    `json:"brokerage,omitempty"`
	BrokerageStatus         string  `json:"brokerage_status,omitempty"`
	CarrierContact          string  `json:"carrier_contact,omitempty"`
	CarrierEmail            string  `json:"carrier_email,omitempty"`
	CarrierPhone            string  `json:"carrier_phone,omitempty"`
	DestStopID              string  `json:"dest_stop_id,omitempty"`
	DispatcherUserID        string  `json:"dispatcher_user_id,omitempty"`
	ExcludeMovement         bool    `json:"exclude_movement,omitempty"`
	FuelDistance            float32 `json:"fuel_distance,omitempty"`
	FuelDistanceUm          string  `json:"fuel_distance_um,omitempty"`
	ID                      string  `json:"id,omitempty"`
	IsContainer             bool    `json:"is_container,omitempty"`
	IsDray                  bool    `json:"is_dray,omitempty"`
	IsLocalMile             bool    `json:"is_local_mile,omitempty"`
	Loaded                  string  `json:"loaded,omitempty"`
	Ltl                     bool    `json:"ltl,omitempty"`
	MaxBuy                  float32 `json:"max_buy,omitempty"`
	MaxBuyC                 string  `json:"max_buy_c,omitempty"`
	MaxBuyD                 string  `json:"max_buy_d,omitempty"`
	MaxBuyN                 float32 `json:"max_buy_n,omitempty"`
	MaxBuyR                 float32 `json:"max_buy_r,omitempty"`
	MaxPayMethod            string  `json:"max_pay_method,omitempty"`
	MaxPayUnits             float32 `json:"max_pay_units,omitempty"`
	MissedCallSent          bool    `json:"missed_call_sent,omitempty"`
	MoveDistance            float32 `json:"move_distance,omitempty"`
	MoveDistanceUm          string  `json:"move_distance_um,omitempty"`
	OperationsUser          string  `json:"operations_user,omitempty"`
	OriginStopID            string  `json:"origin_stop_id,omitempty"`
	OverridePayAmt          float32 `json:"override_pay_amt,omitempty"`
	OverridePayAmtC         string  `json:"override_pay_amt_c,omitempty"`
	OverridePayAmtD         string  `json:"override_pay_amt_d,omitempty"`
	OverridePayAmtN         float32 `json:"override_pay_amt_n,omitempty"`
	OverridePayAmtR         float32 `json:"override_pay_amt_r,omitempty"`
	OverridePayRate         float32 `json:"override_pay_rate,omitempty"`
	OverridePayeeID         string  `json:"override_payee_id,omitempty"`
	OverrideType            string  `json:"override_type,omitempty"`
	OverrideUnits           float32 `json:"override_units,omitempty"`
	ReminderSent            bool    `json:"reminder_sent,omitempty"`
	SegAllocCode            string  `json:"seg_alloc_code,omitempty"`
	Status                  string  `json:"status,omitempty"`
	TargetPay               float32 `json:"target_pay,omitempty"`
	TargetPayC              string  `json:"target_pay_c,omitempty"`
	TargetPayD              string  `json:"target_pay_d,omitempty"`
	TargetPayN              float32 `json:"target_pay_n,omitempty"`
	TargetPayR              float32 `json:"target_pay_r,omitempty"`
	TargetPayMethod         string  `json:"target_pay_method,omitempty"`
	TargetPayUnits          float32 `json:"target_pay_units,omitempty"`
	TrpUID                  int     `json:"trp_uid,omitempty"`
	MovementType            string  `json:"movement_type,omitempty"`
	EformRateConfirmation   bool    `json:"eform_rate_confirmation,omitempty"`
	EstTolls                float32 `json:"est_tolls,omitempty"`
	EstTollsC               string  `json:"est_tolls_c,omitempty"`
	EstTollsD               string  `json:"est_tolls_d,omitempty"`
	EstTollsN               float32 `json:"est_tolls_n,omitempty"`
	EstTollsR               float32 `json:"est_tolls_r,omitempty"`
	WaterfallInProgress     bool    `json:"waterfall_in_progress,omitempty"`
	Reserved                bool    `json:"reserved,omitempty"`
	IntegratedCarrierSearch bool    `json:"integrated_carrier_search,omitempty"`
	TargetExtraStopPay      float32 `json:"target_extra_stop_pay,omitempty"`
	TargetExtraStopPayC     string  `json:"target_extra_stop_pay_c,omitempty"`
	TargetExtraStopPayD     string  `json:"target_extra_stop_pay_d,omitempty"`
	TargetExtraStopPayN     float32 `json:"target_extra_stop_pay_n,omitempty"`
	TargetExtraStopPayR     float32 `json:"target_extra_stop_pay_r,omitempty"`
	TriumphpayExclude       bool    `json:"triumphpay_exclude,omitempty"`
	StatusDescr             string  `json:"__statusDescr,omitempty"`
	OverrideTypeDescr       string  `json:"__overrideTypeDescr,omitempty"`
	BrokerageStatusDescr    string  `json:"__brokerageStatusDescr,omitempty"`
	FreightRevenue          float32 `json:"__freightRevenue,omitempty"`
	FreightRevenueC         string  `json:"__freightRevenue_c,omitempty"`
	FreightRevenueD         string  `json:"__freightRevenue_d,omitempty"`
	FreightRevenueR         int     `json:"__freightRevenue_r,omitempty"`
	FreightRevenueN         float32 `json:"__freightRevenue_n,omitempty"`
	OtherRevenue            float32 `json:"__otherRevenue,omitempty"`
	OtherRevenueC           string  `json:"__otherRevenue_c,omitempty"`
	OtherRevenueD           string  `json:"__otherRevenue_d,omitempty"`
	OtherRevenueR           int     `json:"__otherRevenue_r,omitempty"`
	OtherRevenueN           float32 `json:"__otherRevenue_n,omitempty"`
	TotalRevenue            float32 `json:"__totalRevenue,omitempty"`
	TotalRevenueC           string  `json:"__totalRevenue_c,omitempty"`
	TotalRevenueD           string  `json:"__totalRevenue_d,omitempty"`
	TotalRevenueR           int     `json:"__totalRevenue_r,omitempty"`
	TotalRevenueN           float32 `json:"__totalRevenue_n,omitempty"`
	OtherPay                float32 `json:"__otherPay,omitempty"`
	OtherPayC               string  `json:"__otherPay_c,omitempty"`
	OtherPayD               string  `json:"__otherPay_d,omitempty"`
	OtherPayR               int     `json:"__otherPay_r,omitempty"`
	OtherPayN               float32 `json:"__otherPay_n,omitempty"`
	TotalPay                float32 `json:"__totalPay,omitempty"`
	TotalPayC               string  `json:"__totalPay_c,omitempty"`
	TotalPayD               string  `json:"__totalPay_d,omitempty"`
	TotalPayR               int     `json:"__totalPay_r,omitempty"`
	TotalPayN               float32 `json:"__totalPay_n,omitempty"`
	Profit                  float32 `json:"__profit,omitempty"`
	ProfitC                 string  `json:"__profit_c,omitempty"`
	ProfitD                 string  `json:"__profit_d,omitempty"`
	ProfitR                 int     `json:"__profit_r,omitempty"`
	ProfitN                 float32 `json:"__profit_n,omitempty"`
	ProfitPercentage        float32 `json:"__profitPercentage,omitempty"`
	// DispatcherUser          User  `json:"dispatcherUser,omitempty"`
	// OperationsUser0         User `json:"operationsUser,omitempty"`
	Carrier Carrier `json:"carrier,omitempty"`
	// Stops                   []Stops         `json:"stops,omitempty"`
	// Orders                  []Orders        `json:"orders,omitempty"`
}

type Carrier struct {
	Type                    string   `json:"__type,omitempty"`
	CompanyID               string   `json:"company_id,omitempty"` // e.g. "TMS"
	Address1                string   `json:"address1,omitempty"`
	CheckAddress            string   `json:"check_address,omitempty"`
	CheckCity               string   `json:"check_city,omitempty"`
	CheckDate               string   `json:"check_date,omitempty"`
	CheckName               string   `json:"check_name,omitempty"`
	CheckSt                 string   `json:"check_st,omitempty"`
	CheckZip                string   `json:"check_zip,omitempty"`
	City                    string   `json:"city,omitempty"`
	CountryID               string   `json:"country_id,omitempty"`
	Email                   string   `json:"email,omitempty"`
	EmailSummary            bool     `json:"email_summary,omitempty"`
	ID                      string   `json:"id,omitempty"`
	LegalName               string   `json:"legal_name,omitempty"`
	Name                    string   `json:"name,omitempty"`
	PhoneNumber             string   `json:"phone_number,omitempty"`
	State                   string   `json:"state,omitempty"`
	Status                  string   `json:"status,omitempty"`
	FreightMatchingOverride bool     `json:"freight_matching_override,omitempty"`
	DrsPayee                DrsPayee `json:"drsPayee,omitempty"`
}

// Abridged
type DrsPayee struct {
	DotNumber string `json:"dot_number,omitempty"`
}

type CustomerResp struct {
	Type      string `json:"__type"`
	CompanyID string `json:"company_id"`
	ID        string `json:"id"`
	Name      string `json:"name"`
	Address1  string `json:"address1"`
	Address2  string `json:"address2"`
	City      string `json:"city"`
	StateID   string `json:"stateID"`
	DotNumber string `json:"dot_number"`
	StartDate string `json:"start_date"`
	ZipCode   string `json:"zip_code"`
}

type LocationResp struct {
	Type              string  `json:"__type,omitempty"`
	CompanyID         string  `json:"company_id,omitempty"`
	Address1          string  `json:"address1,omitempty"`
	ApptRequired      bool    `json:"appt_required,omitempty"`
	ChassisOwner      bool    `json:"chassis_owner,omitempty"`
	ChassisPool       bool    `json:"chassis_pool,omitempty"`
	CityID            int     `json:"city_id,omitempty"`
	CityName          string  `json:"city_name,omitempty"`
	ContainerPool     bool    `json:"container_pool,omitempty"`
	DriverLoadID      string  `json:"driver_load_id,omitempty"`
	DriverUnloadID    string  `json:"driver_unload_id,omitempty"`
	ID                string  `json:"id,omitempty"`
	IsConsignee       bool    `json:"is_consignee,omitempty"`
	IsCustomer        bool    `json:"is_customer,omitempty"`
	IsCustomsBroker   bool    `json:"is_customs_broker,omitempty"`
	IsDistCenter      bool    `json:"is_dist_center,omitempty"`
	IsDropYard        bool    `json:"is_drop_yard,omitempty"`
	IsGeocoded        bool    `json:"is_geocoded,omitempty"`
	IsProspect        bool    `json:"is_prospect,omitempty"`
	IsShipper         bool    `json:"is_shipper,omitempty"`
	IsSteamship       bool    `json:"is_steamship,omitempty"`
	IsTerminal        bool    `json:"is_terminal,omitempty"`
	IsTrailerPool     bool    `json:"is_trailer_pool,omitempty"`
	Latitude          float32 `json:"latitude,omitempty"`
	LoadUnloadExcl    bool    `json:"load_unload_excl,omitempty"`
	LoadUnloadParam   string  `json:"load_unload_param,omitempty"`
	Longitude         float32 `json:"longitude,omitempty"`
	ManualExchange    bool    `json:"manual_exchange,omitempty"`
	ModelFlag         bool    `json:"model_flag,omitempty"`
	Name              string  `json:"name,omitempty"`
	PalletsRequired   bool    `json:"pallets_required,omitempty"`
	PlcUID            int     `json:"plc_uid,omitempty"`
	State             string  `json:"state,omitempty"`
	TripStarter       bool    `json:"trip_starter,omitempty"`
	ZipCode           string  `json:"zip_code,omitempty"`
	IsOutsideTerminal bool    `json:"is_outside_terminal,omitempty"`
	IsActive          bool    `json:"is_active,omitempty"`
	LastUsedDate      string  `json:"last_used_date,omitempty"`
}

type StopResp struct {
	Type                   string  `json:"__type,omitempty"`
	CompanyID              string  `json:"company_id,omitempty"`
	PlannedArrivalTime     string  `json:"planned_arrival_time,omitempty"`
	ActualArrival          string  `json:"actual_arrival,omitempty"`
	ActualDeparture        string  `json:"actual_departure,omitempty"`
	Address                string  `json:"address,omitempty"`
	ApptRequired           bool    `json:"appt_required,omitempty"`
	CityID                 int     `json:"city_id,omitempty"`
	CityName               string  `json:"city_name,omitempty"`
	ContactName            string  `json:"contact_name,omitempty"`
	DriverLoadUnload       string  `json:"driver_load_unload,omitempty"`
	EdiLocCode             string  `json:"edi_loc_code,omitempty"`
	ID                     string  `json:"id,omitempty"`
	LateEtaColorcode       bool    `json:"late_eta_colorcode,omitempty"`
	Latitude               float32 `json:"latitude,omitempty"`
	LocationID             string  `json:"location_id,omitempty"`
	LocationName           string  `json:"location_name,omitempty"`
	Longitude              float32 `json:"longitude,omitempty"`
	MoveDistFromPrevious   float32 `json:"move_dist_from_previous,omitempty"`
	MoveDistFromPreviousUm string  `json:"move_dist_from_previous_um,omitempty"`
	MovementID             string  `json:"movement_id,omitempty"`
	MovementSequence       int     `json:"movement_sequence,omitempty"`
	OrderID                string  `json:"order_id,omitempty"`
	OrderSequence          int     `json:"order_sequence,omitempty"`
	Phone                  string  `json:"phone,omitempty"`
	SchedArriveEarly       string  `json:"sched_arrive_early,omitempty"`
	State                  string  `json:"state,omitempty"`
	Status                 string  `json:"status,omitempty"`
	StopType               string  `json:"stop_type,omitempty"`
	ZipCode                string  `json:"zip_code,omitempty"`
	ZoneID                 string  `json:"zone_id,omitempty"`
	ManifestFgpUID         int     `json:"manifest_fgp_uid,omitempty"`
	TxlUID                 int     `json:"txl_uid,omitempty"`
	RequestedService       bool    `json:"requested_service,omitempty"`
	PriorUnclearedStops    bool    `json:"prior_uncleared_stops,omitempty"`
	StatusDescr            string  `json:"__statusDescr,omitempty"`
	TypeDescr              string  `json:"__typeDescr,omitempty"`
	LoadUnloadDescr        string  `json:"__loadUnloadDescr,omitempty"`
	ZoneDescr              string  `json:"__zoneDescr,omitempty"`
	GroupingKey            string  `json:"__groupingKey,omitempty"`
}

type CarrierResp struct {
	Type                string   `json:"__type"`
	CompanyID           string   `json:"company_id"`
	Address1            string   `json:"address1"`
	Address2            string   `json:"address2"`
	CheckAddress        string   `json:"check_address"`
	CheckCity           string   `json:"check_city"`
	CheckDate           string   `json:"check_date"`
	CheckName           string   `json:"check_name"`
	CheckSt             string   `json:"check_st"`
	CheckZip            string   `json:"check_zip"`
	City                string   `json:"city"`
	Email               string   `json:"email"`
	EmailSummary        bool     `json:"email_summary"`
	FreezePay           bool     `json:"freeze_pay"`
	GpExclude           bool     `json:"gp_exclude"`
	ID                  string   `json:"id"`
	LegalName           string   `json:"legal_name"`
	Name                string   `json:"name"`
	NonOfficeEmp        bool     `json:"non_office_emp"`
	OfficeEmployee      bool     `json:"office_employee"`
	PaymentMethod       string   `json:"payment_method"`
	PhoneNumber         string   `json:"phone_number"`
	SocialSecurityNo    string   `json:"social_security_no"`
	State               string   `json:"state"`
	Status              string   `json:"status"`
	VMPartUpdate        bool     `json:"vm_part_update"`
	YtdHolidayHrs       float32  `json:"ytd_holiday_hrs"`
	YtdOvertimeHrs      float32  `json:"ytd_overtime_hrs"`
	YtdRegHrsPaid       float32  `json:"ytd_reg_hrs_paid"`
	YtdSickHrsPaid      float32  `json:"ytd_sick_hrs_paid"`
	YtdVacationHrs      float32  `json:"ytd_vacation_hrs"`
	ZipCode             string   `json:"zip_code"`
	DisablePayrollTaxes bool     `json:"disable_payroll_taxes"`
	EnableCarrierApp    bool     `json:"enable_carrier_app"`
	ExemptFromFed       bool     `json:"exempt_from_fed"`
	MultipleJobs        bool     `json:"multiple_jobs"`
	StatusDescr         string   `json:"__statusDescr"`
	DrsPayee            DrsPayee `json:"drsPayee"`
}

type CheckCallResp struct {
	Type         string  `json:"__type,omitempty"`
	Direction    string  `json:"direction,omitempty"`
	Latitude     float32 `json:"latitude,omitempty"`
	Longitude    float32 `json:"longitude,omitempty"`
	McUnitNo     string  `json:"mcUnitNo,omitempty"`
	MovementID   string  `json:"movementId,omitempty"`
	NearBCity    string  `json:"nearBCity,omitempty"`
	NearLandmark string  `json:"nearLandmark,omitempty"`
	NearSCity    string  `json:"nearSCity,omitempty"`
	OrderID      string  `json:"orderId,omitempty"`
	PositionDate string  `json:"positionDate,omitempty"`
	PositionID   string  `json:"positionId,omitempty"`
	Speed        string  `json:"speed,omitempty"`
}

type QuoteReqResp struct {
	Type             string `json:"__type,omitempty"`
	CompanyID        string `json:"company_id,omitempty"`
	ID               string `json:"id,omitempty"`
	BillDistance     string `json:"bill_distance,omitempty"`
	BillDistanceUm   string `json:"bill_distance_um,omitempty"`
	CompanyName      string `json:"company_name,omitempty"`
	ConsigneeID      string `json:"consignee_id,omitempty"`
	ConsigneeName    string `json:"consignee_name,omitempty"`
	ConsigneeState   string `json:"consignee_state,omitempty"`
	ConsigneeZipCode string `json:"consignee_zip_code,omitempty"`
	CustomerID       string `json:"customer_id,omitempty"`
	DeliveryDate     string `json:"delivery_date,omitempty"`
	EffectiveDate    string `json:"effective_date,omitempty"`
	Email            string `json:"email,omitempty"`
	EquipmentTypeID  string `json:"equipment_type_id,omitempty"`
	ExpirationDate   string `json:"expiration_date,omitempty"`
	Notes            string `json:"notes,omitempty"`
	Phone            string `json:"phone,omitempty"`
	Rate             string `json:"rate,omitempty"`
	RateID           string `json:"rate_id,omitempty"`
	RateType         string `json:"rate_type,omitempty"`
	RateUnitDesc     string `json:"rate_unit_desc,omitempty"`
	RateUnits        string `json:"rate_units,omitempty"`
	ShipDate         string `json:"ship_date,omitempty"`
	ShipperCityName  string `json:"shipper_city_name,omitempty"`
	ShipperID        string `json:"shipper_id,omitempty"`
	ShipperName      string `json:"shipper_name,omitempty"`
	ShipperState     string `json:"shipper_state,omitempty"`
	ShipperZipCode   string `json:"shipper_zip_code,omitempty"`
	Status           string `json:"status,omitempty"`
}

type UserResp struct {
	Type             string `json:"__type,omitempty"`
	CompanyID        string `json:"company_id,omitempty"`
	ID               string `json:"id,omitempty"`
	CompanyName      string `json:"company_name,omitempty"`
	EmailAddress     string `json:"email_address,omitempty"`
	EmailPassword    string `json:"email_password,omitempty"`
	EmailUsername    string `json:"email_username,omitempty"`
	EmployeeType     string `json:"employee_type,omitempty"`
	IsActive         bool   `json:"is_active,omitempty"`
	OperationsUserID string `json:"operations_user_id,omitempty"`
	LoginID          string `json:"login_id,omitempty"`
	Name             string `json:"name,omitempty"`
	Phone            string `json:"phone,omitempty"`
	RevenueCodeID    string `json:"revenue_code_id,omitempty"`
}

type CheckCallReq struct {
	Type           string `json:"__type,omitempty"`
	CompanyID      string `json:"company_id,omitempty"`
	ID             string `json:"id,omitempty"`
	AttachFilename string `json:"attach_filename,omitempty"`
	Attachment     string `json:"attachment,omitempty"`
	CarrierID      string `json:"carrier_id,omitempty"`
	Comments       string `json:"comments,omitempty"`
	GpsAccuracy    string `json:"gps_accuracy,omitempty"`
	Latitude       string `json:"latitude,omitempty"`
	Longitude      string `json:"longitude,omitempty"`
	MovementID     string `json:"movement_id,omitempty"`
	OrderID        string `json:"order_id,omitempty"`
	ParentRowID    string `json:"parent_row_id,omitempty"`
	PostedBy       string `json:"posted_by,omitempty"`
	PostedDate     string `json:"posted_date,omitempty"`
	PostedType     string `json:"posted_type,omitempty"`
	ReasonCode     string `json:"reason_code,omitempty"`
	SystemAdded    string `json:"system_added,omitempty"`
}

type City struct {
	Type            string  `json:"__type"`
	CompanyID       string  `json:"company_id"`
	County          string  `json:"county"`
	ID              int     `json:"id"`
	Inactive        bool    `json:"inactive"`
	Latitude        float64 `json:"latitude"`
	Longitude       float64 `json:"longitude"`
	Name            string  `json:"name"`
	StateID         string  `json:"state_id"`
	ThreeDigitZip   string  `json:"three_digit_zip,omitempty"`
	TimezoneID      string  `json:"timezone_id"`
	IsiID           int     `json:"isi_id,omitempty"`
	ZipCode         string  `json:"zip_code,omitempty"`
	AirportCode     string  `json:"airport_code,omitempty"`
	CentralLocation bool    `json:"central_location,omitempty"`
}
