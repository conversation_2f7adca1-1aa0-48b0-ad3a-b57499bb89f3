package mcleodenterprise

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (calls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(m.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryMcleodEnterprise", spanAttrs)
	defer func() { metaSpan.End(err) }()

	timeFormat := "20060102150405-0700"
	var checkCallResp []CheckCallResp
	endPoint := fmt.Sprintf("/orders/%s/positions", freightTrackingID)
	err = m.get(ctx, endPoint, nil, &checkCallResp, s3backup.TypeCheckCalls)
	if err != nil {
		return calls, fmt.Errorf("get checkcall response failed: %w", err)
	}

	for _, resp := range checkCallResp {
		locationDateTime, timeErr := time.Parse(timeFormat, resp.PositionDate)
		if timeErr != nil {
			log.Error(ctx, "Error parsing positionDate",
				zap.String("positionDate", resp.PositionDate), zap.Error(err))
			return calls, err
		}

		call := models.CheckCall{}
		call.LoadID = loadID
		call.FreightTrackingID = freightTrackingID
		call.DateTime = models.NullTime{
			Time:  locationDateTime,
			Valid: true,
		}
		call.City = resp.NearBCity
		call.Lat = float64(resp.Latitude)
		call.Lon = float64(resp.Longitude)

		calls = append(calls, call)
	}

	return calls, err
}

func (m *McleodEnterprise) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	checkCall models.CheckCall,
) (err error) {

	spanAttrs := append(otel.IntegrationAttrs(m.tms), otel.SafeIntAttribute("load_id", load.ID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallMcleodEnterprise", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// TODO : need to confirm post CheckCall response
	var response CheckCallResp
	reqBody := m.toMcleodEnterpriseCheckCall(checkCall, *load)
	err = m.put(ctx, "/orders/orderPostHist/create", reqBody, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return err
	}
	return nil
}

func (m *McleodEnterprise) toMcleodEnterpriseCheckCall(
	checkCall models.CheckCall,
	load models.Load,
) (callReq CheckCallReq) {
	timeFormat := "20060102150405-0700"

	callReq.OrderID = load.ExternalTMSID
	callReq.PostedDate = checkCall.DateTime.Time.Format(timeFormat)
	callReq.Latitude = strconv.FormatFloat(checkCall.Lat, 'f', -1, 64)
	callReq.Longitude = strconv.FormatFloat(checkCall.Lon, 'f', -1, 64)
	return
}
