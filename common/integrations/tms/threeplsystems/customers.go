package threeplsystems

import (
	"context"
	"strconv"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (t ThreePLSystems) GetCustomers(ctx context.Context) (customers []models.TMSCustomer, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersThreePLSystems", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	var customersResp []CustomerResp
	// todo : need startDate and endDate as query params
	// startDate: Start Modified Date
	// endDate: End Modified Date
	err = t.get(ctx, "/api/clientv1/customer", nil, &customersResp, s3backup.TypeCustomers)
	if err != nil {
		return nil, err
	}

	for _, data := range customersResp {
		var customer models.TMSCustomer
		customer.TMSIntegration = t.tms
		customer.TMSIntegrationID = t.tms.ID

		customer.Name = data.CustomerName
		customer.ExternalTMSID = strconv.Itoa(data.CustomerID)
		customer.AddressLine1 = data.Address1
		if data.Address2 != nil {
			customer.AddressLine2 = *data.Address2
		}
		customer.City = data.City
		customer.State = data.State
		customer.Country = data.Country
		customer.Zipcode = data.Zip

		customers = append(customers, customer)
	}

	return customers, nil
}
