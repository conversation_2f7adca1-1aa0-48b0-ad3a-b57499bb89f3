package threeplsystems

import (
	"context"
	"net/url"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (t ThreePLSystems) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	_ models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardThreePLSystems", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	tokenResp, err := t.getToken(ctx)
	if err != nil {
		return models.OnboardTMSResponse{}, err
	}

	return models.OnboardTMSResponse{
		AccessToken: tokenResp.AccessToken,
	}, nil
}

func (t ThreePLSystems) getToken(ctx context.Context) (*TokenResp, error) {
	data := url.Values{}
	data.Set("client_id", t.tms.Service.ThreePLSystemsClientID)
	data.Set("client_secret", t.tms.Service.ThreePLSystemsClientSecret)
	data.Set("grant_type", "client_credentials")

	var tokenResp TokenResp
	err := t.post(ctx, "/connect/token", nil, data, &tokenResp, s3backup.TypeTokens)
	if err != nil {
		return nil, err
	}
	return &tokenResp, nil
}
