package threeplsystems

type TokenResp struct {
	TokenType   string `json:"token_type"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

type LoadResp struct {
	LoadID        int     `json:"loadId"`
	Readytime     string  `json:"readytime"`
	Closingtime   string  `json:"closingtime"`
	Createdate    string  `json:"createdate"`
	Dockdate      *string `json:"dockdate"`
	PickNum       string  `json:"pickNum"`
	BolNum        *string `json:"bolNum"`
	ShipperNum    *string `json:"shipperNum"`
	Priority      string  `json:"priority"`
	DriverType    int     `json:"driverType"`
	ReferenceNo   *string `json:"referenceNo"`
	Pcf           int     `json:"pcf"`
	TargetRate    float64 `json:"targetRate"`
	Value         float64 `json:"value"`
	PerPund       float64 `json:"perPund"`
	Miles         float64 `json:"miles"`
	CodAmount     float64 `json:"codAmount"`
	Assignedto    *string `json:"assignedto"`
	Dipatcher     *string `json:"dipatcher"`
	ShipmentMode  *string `json:"shipmentMode"`
	EquipmentType string  `json:"equipmentType"`
	Items         []struct {
		Pieces             int     `json:"pieces"`
		Weight             float64 `json:"weight"`
		Class              string  `json:"class"`
		IsHazardous        bool    `json:"isHazardous"`
		Packaging          *string `json:"packaging"`
		Nmfc               string  `json:"nmfc"`
		ProductDescription *string `json:"productDescription"`
		Density            float64 `json:"density"`
		Length             float64 `json:"length"`
		Height             float64 `json:"height"`
		Width              float64 `json:"width"`
		UnitsWeight        string  `json:"unitsWeight"`
		UnitsDensity       *string `json:"unitsDensity"`
		UnitsDimension     string  `json:"unitsDimension"`
		Cost               float64 `json:"cost"`
		Billed             float64 `json:"billed"`
		Pcf                float64 `json:"pcf"`
	} `json:"items"`
	PoReference                string  `json:"poReference"`
	BillOfLandingNote          *string `json:"billOfLandingNote"`
	PickupDate                 string  `json:"pickupDate"`
	PickupOpenTime             string  `json:"pickupOpenTime"`
	PickupCloseTime            string  `json:"pickupCloseTime"`
	EstimatedDelivery          string  `json:"estimatedDelivery"`
	EstimatedDeliveryOpenTime  *string `json:"estimatedDeliveryOpenTime"`
	EstimatedDeliveryCloseTime *string `json:"estimatedDeliveryCloseTime"`
	ShipmentStatus             string  `json:"shipmentStatus"`
	Carriers                   []struct {
		CarrierScac         string  `json:"carrierScac"`
		ProviderScac        string  `json:"providerScac"`
		CarrierName         string  `json:"carrierName"`
		CarrierMCNumber     string  `json:"carrierMCNumber"`
		CarrierProNumber    *string `json:"carrierProNumber"`
		CarrierContactName  *string `json:"carrierContactName"`
		CarrierContactPhone *string `json:"carrierContactPhone"`
		CarrierContactEmail *string `json:"carrierContactEamil"`
		CarrierAddress      *string `json:"carrierAddress"`
		CarrierPhone        *string `json:"carrierPhone"`
	} `json:"carriers"`
	Stops []struct {
		Address1    string  `json:"address1"`
		Address2    *string `json:"address2"`
		City        string  `json:"city"`
		State       string  `json:"state"`
		StopType    string  `json:"stopType"`
		Zip         string  `json:"zip"`
		Country     string  `json:"country"`
		LoadPoint   *string `json:"loadPoint"`
		DropPoint   *string `json:"dropPoint"`
		AddressLine string  `json:"addressLine"`
		FullAddress string  `json:"fullAddress"`
	} `json:"stops"`
}

type CreateUpdateLoadReq struct {
	ShipperZip                 string `json:"shipperZip,omitempty"`
	ShipperCountry             string `json:"shipperCountry,omitempty"`
	ConsigneeZip               string `json:"consigneeZip,omitempty"`
	ConsigneeCountry           string `json:"consigneeCountry,omitempty"`
	ShipmentMode               string `json:"shipmentMode,omitempty"`
	EquipmentType              string `json:"equipmentType,omitempty"`
	Items                      []Item `json:"items,omitempty"`
	ShipperAddress             string `json:"shipperAddress,omitempty"`
	ShipperAddress2            string `json:"shipperAddress2,omitempty"`
	ShipperName                string `json:"shipperName,omitempty"`
	ShipperContact             string `json:"shipperContact,omitempty"`
	ShipperEmail               string `json:"shipperEmail,omitempty"`
	ShipperPhone               string `json:"shipperPhone,omitempty"`
	ConsigneeAddress           string `json:"consigneeAddress,omitempty"`
	ConsigneeAddress2          string `json:"consigneeAddress2,omitempty"`
	ConsigneeName              string `json:"consigneeName,omitempty"`
	ConsigneeContact           string `json:"consigneeContact,omitempty"`
	ConsigneeEmail             string `json:"consigneeEmail,omitempty"`
	ConsigneePhone             string `json:"consigneePhone,omitempty"`
	PoReference                string `json:"poReference,omitempty"`
	BillOfLandingNote          string `json:"billOfLandingNote,omitempty"`
	PickupDate                 string `json:"pickupDate,omitempty"`
	PickupOpenTime             string `json:"pickupOpenTime,omitempty"`
	PickupCloseTime            string `json:"pickupCloseTime,omitempty"`
	EstimatedDelivery          string `json:"estimatedDelivery,omitempty"`
	EstimatedDeliveryOpenTime  string `json:"estimatedDeliveryOpenTime,omitempty"`
	EstimatedDeliveryCloseTime string `json:"estimatedDeliveryCloseTime,omitempty"`
	ShipmentStatus             string `json:"shipmentStatus,omitempty"`
	ShipperNumber              string `json:"shipperNumber,omitempty"`
	ReferenceNumber            string `json:"referenceNumber,omitempty"`
	Carrier                    struct {
		CarrierScac  string `json:"carrierScac,omitempty"`
		ProviderScac string `json:"providerScac,omitempty"`
		ProNumber    string `json:"proNumber,omitempty"`
	} `json:"carrier,omitempty"`
	Test bool `json:"test,omitempty"`
}

type Item struct {
	ProductDescription string  `json:"productDescription,omitempty"`
	Class              string  `json:"class,omitempty"`
	Pieces             int     `json:"pieces,omitempty"`
	Weight             float64 `json:"weight,omitempty"`
	IsHazardous        bool    `json:"isHazardous,omitempty"`
	Packaging          string  `json:"packaging,omitempty"`
	Height             float64 `json:"height,omitempty"`
	Length             float64 `json:"length,omitempty"`
	Width              float64 `json:"width,omitempty"`
	Cost               string  `json:"cost,omitempty"`
	SealNumber         string  `json:"sealNumber,omitempty"`
}

type UpdateLoadResp struct {
	LoadID int `json:"loadId"`
}

type CustomerResp struct {
	Address1         string  `json:"address1"`
	Address2         *string `json:"address2"`
	City             string  `json:"city"`
	Zip              string  `json:"zip"`
	State            string  `json:"state"`
	Country          string  `json:"country"`
	CreatedDate      string  `json:"createdDate"`
	WorkPhone        *string `json:"workPhone"`
	LastModifiedDate string  `json:"lastModifiedDate"`
	CustomerName     string  `json:"customerName"`
	CustomerID       int     `json:"customerId"`
	Term             string  `json:"term"`
	Status           string  `json:"status"`
}

type CarrierResp struct {
	CarrierAddress1     string  `json:"carrierAddress1"`
	CarrierAddress2     *string `json:"carrierAddress2"`
	CarrierCity         string  `json:"carrierCity"`
	CarrierZip          string  `json:"carrierZip"`
	CarrierState        string  `json:"carrierState"`
	CarrierCountry      string  `json:"carrierCountry"`
	CreatedDate         string  `json:"createdDate"`
	LastModifiedDate    string  `json:"lastModifiedDate"`
	CarrierName         string  `json:"carrierName"`
	CarrierID           int     `json:"carrierId"`
	Mc                  *string `json:"mc"`
	Dot                 *string `json:"dot"`
	CarrierPhone        *string `json:"carrierPhone"`
	PaymentMethod       string  `json:"paymentMethod"`
	CarrierTerm         string  `json:"carrierTerm"`
	CarrierContactEmail string  `json:"carrierContactEmail"`
	Status              string  `json:"status"`
}

type GetCheckCallResp struct {
	LoadID                int     `json:"loadId"`
	Status                string  `json:"status"`
	StatusCode            string  `json:"statusCode"`
	StatusDescription     string  `json:"statusDescription"`
	EstimatedDeliveryDate string  `json:"estimatedDeliveryDate"`
	LastUpdateDate        string  `json:"lastUpdateDate"`
	PoReference           *string `json:"poReference"`
	ProNumber             string  `json:"proNumber"`
	ShipperNumber         *string `json:"shipperNumber"`
	CarrierName           string  `json:"carrierName"`
	CarrierScac           string  `json:"carrierScac"`
	Billed                int     `json:"billed"`
	ShipperAddress        string  `json:"shipperAddress"`
	ShipperAddress2       *string `json:"shipperAddress2"`
	ShipperZip            string  `json:"shipperZip"`
	ShipperName           *string `json:"shipperName"`
	ShipperCity           string  `json:"shipperCity"`
	ShipperState          string  `json:"shipperState"`
	ShipperCountry        string  `json:"shipperCountry"`
	ConsigneeAddress      string  `json:"consigneeAddress"`
	ConsigneeAddress2     *string `json:"consigneeAddress2"`
	ConsigneeName         *string `json:"consigneeName"`
	ConsigneeZip          string  `json:"consigneeZip"`
	ConsigneeCity         string  `json:"consigneeCity"`
	ConsigneeState        string  `json:"consigneeState"`
	ConsigneeCountry      string  `json:"consigneeCountry"`
	Miles                 int     `json:"miles"`
	Mode                  string  `json:"mode"`
}
