package threeplsystems

import (
	"context"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (t ThreePLSystems) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateQuoteThreePLSystems", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	req := t.quoteBodyToThreePLBody(ctx, quoteBody)
	queryParams := url.Values{}
	// todo : need to confirm the query param
	queryParams.Add("Status", "Quoted")
	err = t.post(ctx, "/api/v1/createshipment", queryParams, &req, &quoteResponse, s3backup.TypeQuotes)
	if err != nil {
		return nil, err
	}
	return quoteResponse, nil
}

func (t ThreePLSystems) quoteBodyToThreePLBody(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (body CreateUpdateLoadReq) {
	equipmentType := "NotSpecified"
	switch quoteBody.TransportType {
	case models.VanTransportType:
		equipmentType = "Van"
	case models.FTLTransportType:
		equipmentType = "FTL"
	case models.LTLTransportType:
		equipmentType = "LTL"
	case models.ReeferTransportType:
		equipmentType = "Reefer"
	case models.FlatbedTransportType:
		equipmentType = "Flatbed"
	default:
		log.WarnNoSentry(ctx,
			"couldn't match transport type while creating three PL Systems quote",
			zap.String("transport_type", string(quoteBody.TransportType)),
		)
	}
	body.ShipmentMode = equipmentType
	var item Item
	item.ProductDescription = quoteBody.CommodityDescription
	itemWeight, err := strconv.ParseFloat(quoteBody.CommodityWeight, 64)
	if err != nil {
		log.Error(ctx, "could not parse commodity weight",
			zap.String("weightString", quoteBody.CommodityWeight), zap.Error(err))
	}
	item.Weight = itemWeight
	item.Cost = strconv.Itoa(quoteBody.QuotePrice)
	body.ShipperZip = quoteBody.PickupLocationZip
	body.PickupDate = quoteBody.PickupDate.Time.Format(time.RFC3339)
	body.ConsigneeZip = quoteBody.DeliveryLocationZip
	body.EstimatedDelivery = quoteBody.DeliveryDate.Time.Format(time.RFC3339)

	return
}
