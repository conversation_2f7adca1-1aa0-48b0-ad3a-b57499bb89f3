package threeplsystems

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const tspHost = "https://3pl.hyperiontms.com"

type ThreePLSystems struct {
	tms models.Integration
}

func New(ctx context.Context, tms models.Integration) *ThreePLSystems {
	log.With(ctx, zap.Uint("axleTMSID", tms.ID), zap.String("tmsName", "ThreePLSystems"), zap.String("host", tspHost))
	return &ThreePLSystems{tms: tms}
}

func (t ThreePLSystems) GetTestLoads() map[string]bool {
	return nil
}

func (t ThreePLSystems) PostCheckCall(
	context.Context,
	*models.Load,
	models.CheckCall,
) (err error) {
	return helpers.NotImplemented(models.ThreePLSystems, "PostCheckCall")
}

func (t ThreePLSystems) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.ThreePLSystems, "GetUsers")
}

func (t ThreePLSystems) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, helpers.NotImplemented(models.ThreePLSystems, "GetLocations")
}

func (t ThreePLSystems) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.ThreePLSystems, "PostException")
}

func (t ThreePLSystems) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, helpers.NotImplemented(models.ThreePLSystems, "GetExceptionHistory")
}

func (t ThreePLSystems) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.ThreePLSystems, "PostNote")
}

func (t ThreePLSystems) MapTransportTypeEnum(string) (models.TransportType, error) {
	return "", helpers.NotImplemented(models.ThreePLSystems, "MapTransportTypeEnum")
}

func (t ThreePLSystems) get(ctx context.Context, path string, queryParams url.Values,
	dst any, dataType s3backup.DataType) error {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	headerMap := make(map[string]string)
	headerMap[""] = t.tms.APIKey
	body, _, err := httputil.GetBytesWithToken(ctx, t.tms, addr, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (t ThreePLSystems) post(ctx context.Context, path string, queryParams url.Values, reqBody any,
	dst any, dataType s3backup.DataType) (err error) {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	headerMap := make(map[string]string)
	if path == "/connect/token" {
		headerMap["Content-Type"] = "application/x-www-form-urlencoded"
	} else {
		headerMap[""] = t.tms.APIKey
	}

	body, _, err := httputil.PostBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); dst != nil && err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (t ThreePLSystems) put(ctx context.Context, path string, queryParams url.Values, reqBody any,
	dst any, dataType s3backup.DataType) (err error) {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	headerMap := make(map[string]string)
	headerMap[""] = t.tms.APIKey
	body, _, err := httputil.PutBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}
