package mercurygate

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (m MercuryGate) CreateQuote(
	ctx context.Context,
	_ models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "CreateQuoteMercuryGate", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	return nil, helpers.NotImplemented(models.MercuryGate, "CreateQuote")
}
