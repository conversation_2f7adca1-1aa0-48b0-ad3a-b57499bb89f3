package mercurygate

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/aws"
)

func (m *MercuryGate) getAPIURL() string {
	return fmt.Sprintf("https://qa-%s.mercurygate.net/MercuryGate", m.config.Tenant)
}

func (m *MercuryGate) get(
	ctx context.Context,
	url string,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return m.getWithHeaders(ctx, url, map[string]string{
		"Accept": "application/json",
	}, true, dataType)
}

func (m *MercuryGate) getWithHeaders(
	ctx context.Context,
	url string,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return m.makeHTTPRequest(ctx, http.MethodGet, url, nil, headers, useCookies, dataType)
}

func (m *MercuryGate) post(
	ctx context.Context,
	url string,
	body io.Reader,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return m.postWithHeaders(
		ctx,
		url,
		body,
		map[string]string{
			"Accept":       "application/json",
			"Content-Type": "application/json",
		},
		true,
		dataType,
	)
}

func (m *MercuryGate) postWithHeaders(ctx context.Context,
	url string,
	body io.Reader,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	return m.makeHTTPRequest(ctx, http.MethodPost, url, body, headers, useCookies, dataType)
}

// `checkRespFunc` allows route to pass in custom error response check function. `htmlBody` is the
// HTML doc in the response, and the returned value `int` is the status code. If the returned status code is >= 300,
// it returns a errtypes.HTTPResponseError
func (m *MercuryGate) makeHTTPRequest(
	ctx context.Context,
	method string,
	url string,
	body io.Reader,
	headers map[string]string,
	useCookies bool,
	dataType s3backup.DataType,
) ([]byte, []*http.Cookie, error) {
	var req *http.Request

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create %s request for MercuryGate: %w", method, err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	if useCookies {
		log.Info(ctx, "Using cookies for MercuryGate request", zap.Any("cookies", (m.cookies)))
		for _, cookie := range m.cookies {
			req.AddCookie(cookie)
		}
	}

	if err = m.rateLimitCheck(ctx); err != nil {
		return nil, nil, fmt.Errorf("rate limit check failed: %w", err)
	}

	resp, err := m.httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, m.tms, err)
		return nil, nil, fmt.Errorf("could not send %s request for MercuryGate: %w", method, err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("error reading response body: %w", err)
	}

	if aws.S3Uploader != nil && dataType != "" {
		if _, err = aws.S3Uploader.TMSResponse(ctx, m.tms, dataType,
			helpers.APIResponse{Method: req.Method, Status: resp.StatusCode, Body: string(respBody)}); err != nil {
			// fail-open: continue processing even if S3 archive failed
			log.Warn(ctx, "s3 archive failed", zap.Error(err), zap.String("dataType", string(dataType)))
			err = nil
		}
	}

	if resp.StatusCode != http.StatusOK {
		m.sessionRefreshCheck(ctx, string(respBody))
	}
	httplog.LogHTTPResponseCode(ctx, m.tms, resp.StatusCode)

	log.Debug(ctx, "MercuryGate response cookies", zap.Any("cookies", resp.Cookies()))

	return respBody, resp.Cookies(), err
}
