package mercurygate

type GlobalSearchReq struct {
	SearchValue string `json:"searchValue"`
}

type ShipmentResp struct {
	ExecutionLoads []ExecutionLoad `json:"executionLoads"`
	CustomerLoads  []CustomerLoad  `json:"customerLoads"`
}

type ExecutionLoad struct {
	Audit             AuditInfo   `json:"audit"`
	Oid               int64       `json:"oid"`
	PrimaryReference  string      `json:"primaryReference"`
	PrimaryRefType    string      `json:"primaryReferenceType"`
	OwningEnterprise  Enterprise  `json:"owningEnterprise"`
	Status            string      `json:"status"`
	CreditStatus      string      `json:"creditStatus"`
	Route             Route       `json:"route"`
	LockingInfo       LockingInfo `json:"lockingInfo"`
	Editable          bool        `json:"editable"`
	OriginDate        DateInfo    `json:"originDate"`
	Origin            Location    `json:"origin"`
	Destination       Location    `json:"destination"`
	ItemTotal         ItemTotal   `json:"itemTotal"`
	CarrierPriceSheet PriceSheet  `json:"carrierPriceSheet"`
}

type CustomerLoad struct {
	Audit              AuditInfo          `json:"audit"`
	Oid                int64              `json:"oid"`
	PrimaryReference   string             `json:"primaryReference"`
	PrimaryRefType     string             `json:"primaryReferenceType"`
	OwningEnterprise   Enterprise         `json:"owningEnterprise"`
	Status             string             `json:"status"`
	CreditStatus       string             `json:"creditStatus"`
	Route              Route              `json:"route"`
	Origin             Location           `json:"origin"`
	Destination        Location           `json:"destination"`
	CustomerPriceSheet CustomerPriceSheet `json:"customerPriceSheet"`
}

type AuditInfo struct {
	CreateDate string `json:"createDate"`
	CreateBy   string `json:"createBy"`
	UpdateDate string `json:"updateDate"`
	UpdateBy   string `json:"updateBy"`
}

type Enterprise struct {
	Name string `json:"name"`
	Oid  int64  `json:"oid"`
}

type Route struct {
	Oid           int64  `json:"oid"`
	EnterpriseOid int64  `json:"enterpriseOid"`
	RouteID       string `json:"routeId"`
}

type LockingInfo struct {
	LockingStatus string `json:"lockingStatus"`
}

type DateInfo struct {
	Type      string    `json:"type"`
	DateRange DateRange `json:"dateRange"`
}

type DateRange struct {
	Status string `json:"status"`
	Begin  string `json:"begin"`
	End    string `json:"end"`
}

type Location struct {
	Type         string `json:"type"`
	LocationCode string `json:"locationCode"`
	Name         string `json:"name"`
	Address1     string `json:"address1"`
	Address2     string `json:"address2"`
	Country      string `json:"country"`
	TimeZoneID   string `json:"timeZoneId"`
	Complete     bool   `json:"complete"`
	Residential  bool   `json:"residential"`
	PostalCode3  string `json:"postalCode3"`
	AddressLine1 string `json:"addressLine1"`
	AddressLine2 string `json:"addressLine2"`
	City         string `json:"city"`
	PostalCode   string `json:"postalCode"`
	State        string `json:"state"`
}

type ItemTotal struct {
	Quantity Quantity `json:"quantity"`
	Weight   Weight   `json:"weight"`
	Cube     Cube     `json:"cube"`
	Hazmat   bool     `json:"hazmat"`
}

type Quantity struct {
	Unit  string  `json:"unit"`
	Value float64 `json:"value"`
}

type Weight struct {
	Units string  `json:"units"`
	Value float64 `json:"value"`
}

type Cube struct {
	Unit  string  `json:"unit"`
	Value float64 `json:"value"`
}

type PriceSheet struct {
	CurrencyCode string `json:"currencyCode"`
}

type CustomerPriceSheet struct {
	Oid                    int64   `json:"oid"`
	CarrierName            string  `json:"carrierName"`
	Scac                   string  `json:"scac"`
	TotalAmount            float64 `json:"totalAmount"`
	CurrencyCode           string  `json:"currencyCode"`
	Mode                   string  `json:"mode"`
	Service                string  `json:"service"`
	NormalizedTotalAmount  float64 `json:"normalizedTotalAmount"`
	NormalizedCurrencyCode string  `json:"normalizedCurrencyCode"`
}

type GetCheckCallResp struct {
	LoadStatus         string          `json:"loadStatus"`
	StatusMessagesList []StatusMessage `json:"statusMessagesList"`
}

type StatusMessageLocation struct {
	City       string  `json:"city"`
	State      string  `json:"state"`
	PostalCode string  `json:"postalCode"`
	Country    string  `json:"country"`
	Latitude   float64 `json:"latitude"`
	Longitude  float64 `json:"longitude"`
}

type StatusMessage struct {
	Date     string                `json:"sDetailDate"`
	Location StatusMessageLocation `json:"sLocation"`
}

type CreateCheckCallRequest struct {
	ShipmentID    int           `json:"shipmentId"`
	City          string        `json:"city"`
	PostalCode    string        `json:"postal"`
	State         string        `json:"state"`
	Country       string        `json:"country"`
	Latitude      string        `json:"latitude"`
	Longitude     string        `json:"longitude"`
	Comments      string        `json:"comments"`
	DateCompleted DateCompleted `json:"dateCompleted"`
	StatusCode    string        `json:"statusCode"`
}

type DateCompleted struct {
	Date    string `json:"date"`
	Time    string `json:"time"`
	TimeUOM string `json:"timeUOM"`
}
