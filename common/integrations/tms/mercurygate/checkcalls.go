package mercurygate

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/aws/smithy-go/time"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m MercuryGate) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (calls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(m.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryMercuryGate", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := fmt.Sprintf("%s/rest/api/transports/%s/tracking", m.getAPIURL(), freightTrackingID)
	var callResp GetCheckCallResp
	resp, _, err := m.get(ctx, endPoint, s3backup.TypeCheckCalls)
	if err != nil {
		log.Error(ctx, "Error getting check call", zap.Error(err))
		return calls, err
	}

	if err = json.Unmarshal(resp, &callResp); err != nil {
		return calls, fmt.Errorf("failed to unmarshal MercuryGate response: %w", err)
	}

	for _, msg := range callResp.StatusMessagesList {
		call := models.CheckCall{}

		locationDateTime, timeErr := time.ParseDateTime(msg.Date)
		if timeErr != nil {
			log.Error(ctx, "Error parsing time", zap.Error(err))
			return calls, err
		}

		call.LoadID = loadID
		call.FreightTrackingID = freightTrackingID
		call.Status = callResp.LoadStatus
		call.DateTime = models.NullTime{
			Time:  locationDateTime,
			Valid: true,
		}
		call.City = msg.Location.City
		call.State = msg.Location.State
		call.Zip = msg.Location.PostalCode
		call.Country = msg.Location.Country
		call.Lat = msg.Location.Latitude
		call.Lon = msg.Location.Longitude

		calls = append(calls, call)
	}

	return calls, err
}

func (m MercuryGate) PostCheckCall(
	ctx context.Context,
	_ *models.Load,
	checkCall models.CheckCall,
) (err error) {

	spanAttrs := append(otel.IntegrationAttrs(m.tms), otel.SafeIntAttribute("load_id", checkCall.LoadID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallMercuryGate", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := toMercuryGateCheckcall(checkCall)
	if err != nil {
		return err
	}
	reqBodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}
	_, _, err = m.post(
		ctx,
		fmt.Sprintf("%s/rest/api/transports/%s/callcheck", m.getAPIURL(), checkCall.FreightTrackingID),
		bytes.NewReader(reqBodyBytes),
		s3backup.TypeCheckCalls,
	)
	if err != nil {
		log.Error(ctx, "Creating MercuryGate check call failed", zap.Error(err))
		return err
	}
	return err
}

func toMercuryGateCheckcall(checkCall models.CheckCall) (CreateCheckCallRequest, error) {
	var result CreateCheckCallRequest
	shipmentIDNumber, err := strconv.Atoi(checkCall.FreightTrackingID)
	if err != nil {
		return result, err
	}
	result.ShipmentID = shipmentIDNumber
	result.City = checkCall.City
	result.PostalCode = checkCall.Zip
	result.State = checkCall.State
	result.Country = checkCall.Country
	result.Latitude = strconv.FormatFloat(checkCall.Lat, 'f', -1, 64)
	result.Longitude = strconv.FormatFloat(checkCall.Lon, 'f', -1, 64)
	result.Comments = checkCall.Notes
	result.DateCompleted = DateCompleted{
		Date:    checkCall.DateTime.Time.Format("2006-01-02"),
		Time:    checkCall.DateTime.Time.Format("15:04:05"),
		TimeUOM: "EST",
	}

	return result, nil
}
