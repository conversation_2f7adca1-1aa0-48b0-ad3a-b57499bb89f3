package freightflow

import "time"

type (
	Load struct {
		ID                string         `json:"id"`
		Status            string         `json:"status"`
		Mode              *string        `json:"mode"`
		PONums            []string       `json:"po_numbers"`
		Operator          *string        `json:"operator"`
		Customer          *Customer      `json:"customer"`
		BillTo            *BillTo        `json:"bill_to"`
		Pickup            *Stop          `json:"pickup"`
		Consignee         *Stop          `json:"consignee"`
		Carrier           *Carrier       `json:"carrier"`
		CreatedAt         *time.Time     `json:"created_at"`
		UpdatedAt         *time.Time     `json:"updated_at"`
		TotalPackages     *int           `json:"totalPackages"`
		TotalWeightLbs    *string        `json:"totalWeightLbs"`
		TotalPallets      *string        `json:"totalPallets"`
		TruckTypes        []string       `json:"truckTypes"`
		Pickups           []Pickup       `json:"pickups"`
		Deliveries        []Delivery     `json:"deliveries"`
		Customers         []CustomerInfo `json:"customers"`
		Branches          []BranchInfo   `json:"branches"`
		SalesOrdersIDs    []string       `json:"salesOrdersIds"`
		PurchaseOrdersIDs []string       `json:"purchaseOrdersIds"`
		IsHighPriority    *bool          `json:"isHighPriority"`
		DurationMinutes   *int           `json:"durationMinutes"`
		LoadCost          *float64       `json:"loadCost"`
		LoadCostCurrency  *string        `json:"loadCostCurrency"`
		// Additional fields from actual API response
		ReceivedTotalPallets   *string      `json:"receivedTotalPallets"`
		ReceivedTotalTotes     *string      `json:"receivedTotalTotes"`
		ExternalIDs            []ExternalID `json:"externalIds"`
		TotalFreightChargeCost *float64     `json:"totalFreightChargeCost"`
		CreateWhseShipments    *bool        `json:"createWhseShipments"`
		CustomersIDs           []string     `json:"customersIds"`
		CreatorID              *string      `json:"creatorId"`
		TruckSizes             []string     `json:"truckSizes"`
		LoadOptions            []string     `json:"loadOptions"`
		ReceivedTotalWeight    *string      `json:"receivedTotalWeight"`
		ClaimsCount            *int         `json:"claimsCount"`
		CostItems              any          `json:"costItems"`
		SalesPersonID          *string      `json:"salesPersonId"`
		TruckBrokerID          *string      `json:"truckBrokerId"`
		IsArchived             *bool        `json:"isArchived"`
		Notes                  *string      `json:"notes"`
		Comments               any          `json:"comments"`
		BillingAmountCurrency  *string      `json:"billingAmountCurrency"`
		TotalTotes             *string      `json:"totalTotes"`
		LimitHighFarenheit     *float64     `json:"limitHighFarenheit"`
		IssueTypes             []string     `json:"issueTypes"`
		AxleTypes              []string     `json:"axleTypes"`
		BillingAmount          *float64     `json:"billingAmount"`
		BillingItems           any          `json:"billingItems"`
		TransferOrdersIDs      []string     `json:"transferOrdersIds"`
		DriverID               *string      `json:"driverId"`
		IsInvoiced             *bool        `json:"isInvoiced"`
		HasInvoicingErrors     *bool        `json:"hasInvoicingErrors"`
		ClaimTypes             []string     `json:"claimTypes"`
		LimitLowFarenheit      *float64     `json:"limitLowFarenheit"`
		IssuesCount            *int         `json:"issuesCount"`
		CarrierID              *string      `json:"carrierId"`
	}

	Customer struct {
		Name         *string `json:"name"`
		AddressLine1 *string `json:"address_line1"`
		AddressLine2 *string `json:"address_line2"`
		City         *string `json:"city"`
		State        *string `json:"state"`
		Zipcode      *string `json:"zipcode"`
		Country      *string `json:"country"`
		Contact      *string `json:"contact"`
		Phone        *string `json:"phone"`
		Email        *string `json:"email"`
		RefNumber    *string `json:"ref_number"`
	}

	BillTo struct {
		Name         *string `json:"name"`
		AddressLine1 *string `json:"address_line1"`
		AddressLine2 *string `json:"address_line2"`
		City         *string `json:"city"`
		State        *string `json:"state"`
		Zipcode      *string `json:"zipcode"`
		Country      *string `json:"country"`
		Contact      *string `json:"contact"`
		Phone        *string `json:"phone"`
		Email        *string `json:"email"`
	}

	Stop struct {
		Name          *string `json:"name"`
		AddressLine1  *string `json:"address_line1"`
		AddressLine2  *string `json:"address_line2"`
		City          *string `json:"city"`
		State         *string `json:"state"`
		Zipcode       *string `json:"zipcode"`
		Country       *string `json:"country"`
		Contact       *string `json:"contact"`
		Phone         *string `json:"phone"`
		Email         *string `json:"email"`
		RefNumber     *string `json:"ref_number"`
		ApptNote      *string `json:"appt_note"`
		ApptRequired  *bool   `json:"appt_required"`
		ApptType      *string `json:"appt_type"`
		ApptStartTime *string `json:"appt_start_time"`
		ApptEndTime   *string `json:"appt_end_time"`
		Timezone      *string `json:"timezone"`
		BusinessHours *string `json:"business_hours"`
		MustDeliver   *bool   `json:"must_deliver"`
	}

	Carrier struct {
		Name                     *string `json:"name"`
		DOTNumber                *string `json:"dot_number"`
		Phone                    *string `json:"phone"`
		Email                    *string `json:"email"`
		Dispatcher               *string `json:"dispatcher"`
		FirstDriverName          *string `json:"first_driver_name"`
		FirstDriverPhone         *string `json:"first_driver_phone"`
		ExternalTMSTruckID       *string `json:"external_tms_truck_id"`
		ExternalTMSTrailerID     *string `json:"external_tms_trailer_id"`
		PickupStart              *string `json:"pickup_start"`
		PickupEnd                *string `json:"pickup_end"`
		DeliveryStart            *string `json:"delivery_start"`
		DeliveryEnd              *string `json:"delivery_end"`
		MCNumber                 *string `json:"mc_number"`
		SealNumber               *string `json:"seal_number"`
		Notes                    *string `json:"notes"`
		SCAC                     *string `json:"scac"`
		SecondDriverName         *string `json:"second_driver_name"`
		SecondDriverPhone        *string `json:"second_driver_phone"`
		DispatchCity             *string `json:"dispatch_city"`
		DispatchState            *string `json:"dispatch_state"`
		DispatchSource           *string `json:"dispatch_source"`
		RateConfirmationSent     *bool   `json:"rate_confirmation_sent"`
		ConfirmationSentTime     *string `json:"confirmation_sent_time"`
		ConfirmationReceivedTime *string `json:"confirmation_received_time"`
		DispatchedTime           *string `json:"dispatched_time"`
		ExpectedPickupTime       *string `json:"expected_pickup_time"`
		ExpectedDeliveryTime     *string `json:"expected_delivery_time"`
	}

	LoadResponse struct {
		Loads []Load `json:"loads"`
	}

	LoadDetailResponse struct {
		Load Load `json:"load"`
	}

	CustomerInfo struct {
		Name               string       `json:"name"`
		ID                 string       `json:"id"`
		Email              string       `json:"email"`
		ExternalIDs        []ExternalID `json:"externalIds"`
		Tags               []string     `json:"tags"`
		McNumbers          []string     `json:"mcNumbers"`
		DefaultWindowStart *string      `json:"defaultWindowStart"`
		DefaultWindowEnd   *string      `json:"defaultWindowEnd"`
	}

	ExternalID struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}

	BranchInfo struct {
		IsActive bool   `json:"isActive"`
		ID       int    `json:"id"`
		Slug     string `json:"slug"`
		Name     string `json:"name"`
		TeamID   string `json:"teamId"`
	}

	Pickup struct {
		ContactID         string   `json:"contactId"`
		AppointmentNumber *string  `json:"appointmentNumber"`
		DepartedAt        *string  `json:"departedAt"`
		ID                string   `json:"id"`
		SourceOrderIDs    []string `json:"sourceOrderIds"`
		TotalPallets      string   `json:"totalPallets"`
		WindowEnd         string   `json:"windowEnd"`
		Status            *string  `json:"status"`
		Items             []Item   `json:"items"`
		AppointmentTime   *string  `json:"appointmentTime"`
		WindowStart       string   `json:"windowStart"`
	}

	Delivery struct {
		ContactID         string   `json:"contactId"`
		AppointmentNumber *string  `json:"appointmentNumber"`
		DepartedAt        *string  `json:"departedAt"`
		ID                string   `json:"id"`
		SourceOrderIDs    []string `json:"sourceOrderIds"`
		TotalPallets      string   `json:"totalPallets"`
		WindowEnd         string   `json:"windowEnd"`
		Status            *string  `json:"status"`
		Items             []Item   `json:"items"`
		AppointmentTime   *string  `json:"appointmentTime"`
		WindowStart       string   `json:"windowStart"`
	}

	Item struct {
		ProductNumber     *string  `json:"productNumber"`
		UnitPriceCurrency *string  `json:"unitPriceCurrency"`
		UnitWeightLbs     float64  `json:"unitWeightLbs"`
		UnitCostCurrency  *string  `json:"unitCostCurrency"`
		TotalPackages     int      `json:"totalPackages"`
		TotalWeightLbs    float64  `json:"totalWeightLbs"`
		Description       *string  `json:"description"`
		ReceivedPallets   *string  `json:"receivedPallets"`
		UnitPrice         *float64 `json:"unitPrice"`
		ReceivedTotes     *string  `json:"receivedTotes"`
		ReceivedWeight    *string  `json:"receivedWeight"`
		PickupNumber      string   `json:"pickupNumber"`
		UnitCost          *float64 `json:"unitCost"`
		ReceivedPackages  *string  `json:"receivedPackages"`
		UnitsPerPallet    int      `json:"unitsPerPallet"`
		EmptyPalletWeight *float64 `json:"emptyPalletWeight"`
	}
)
