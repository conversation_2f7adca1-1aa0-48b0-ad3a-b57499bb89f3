package freightflow

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// PostCheckCall implements the TMS interface
func (f *FreightFlow) PostCheckCall(
	context.Context,
	*models.Load,
	models.CheckCall,
) (err error) {
	return helpers.NotImplemented(models.FreightFlow, "PostCheckCall")
}

// GetCheckCallsHistory implements the TMS interface
func (f *FreightFlow) GetCheckCallsHistory(
	context.Context,
	uint,
	string,
) (calls []models.CheckCall, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "GetCheckCallsHistory")
}
