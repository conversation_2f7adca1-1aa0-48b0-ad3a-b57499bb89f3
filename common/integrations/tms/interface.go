package tms

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/integrations/tms/ascend"
	"github.com/drumkitai/drumkit/common/integrations/tms/freightflow"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleod"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/mercurygate"
	"github.com/drumkitai/drumkit/common/integrations/tms/quantumedge"
	"github.com/drumkitai/drumkit/common/integrations/tms/relay"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeplsystems"
	"github.com/drumkitai/drumkit/common/integrations/tms/truckmate"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/models"
)

type Interface interface {
	InitialOnboard(context.Context, models.Service, models.OnboardTMSRequest) (models.OnboardTMSResponse, error)
	// Returns map of test freightTrackingIDs; important to protect against writes to prod data
	GetTestLoads() map[string]bool
	GetDefaultLoadAttributes() models.LoadAttributes

	GetLoad(ctx context.Context, externalTMSID string) (models.Load, models.LoadAttributes, error)
	CreateLoad(context.Context, models.Load, *models.TMSUser) (models.Load, error)
	UpdateLoad(ctx context.Context, curLoad *models.Load, updatedLoad *models.Load) (
		models.Load, models.LoadAttributes, error)
	GetLoadIDs(ctx context.Context, queryParams models.SearchLoadsQuery) ([]string, error)
	// If GetLoadByIDType not implemented, just call `GetLoad()`.
	// TODO: Support any ID type via TMS's ID regex patterns as we do in McleodEnterprise, so
	// GetLoadByFreightID API handler can be more flexible
	GetLoadsByIDType(ctx context.Context, id string, idType string) ([]models.Load, models.LoadAttributes, error)

	PostCheckCall(ctx context.Context, load *models.Load, checkcall models.CheckCall) error
	GetCheckCallsHistory(ctx context.Context, loadID uint, freightTrackingID string) ([]models.CheckCall, error)

	PostException(ctx context.Context, load *models.Load, e models.Exception) error
	GetExceptionHistory(ctx context.Context, loadID uint, freightTrackingID string) ([]models.Exception, error)

	PostNote(ctx context.Context, load *models.Load, note models.Note) ([]models.Note, error)

	GetCustomers(ctx context.Context) ([]models.TMSCustomer, error)
	GetUsers(ctx context.Context) ([]models.TMSUser, error)
	GetLocations(ctx context.Context, opts ...models.TMSOption) ([]models.TMSLocation, error)
	GetCarriers(ctx context.Context) ([]models.TMSCarrier, error)

	CreateQuote(ctx context.Context, quote models.CreateQuoteBody) (*models.CreateQuoteResponse, error)

	// GetOrder retrieves an order from the TMS by its ID
	// Orders come from customers and convert to loads
	// Loads can have multiple orders associated with them
	GetOrder(ctx context.Context, id string) (*models.Order, models.OrderAttributes, error)

	/*----- Internal methods -----
	Used for mapping TMS transport types to Drumkit transport types, supports win rate calculations
	(see common/rds/quote_request/query.go) and quick quote lane history
	The parameter should be the human-readable TMS transport type label,
	not the TMS internal code for it.
	See Turvo and Aljex implementations for the mapping logic. In general:
		- Anything van related => models.VanTransportType
		- Flatbed => models.FlatbedTransportType
		- Conestega => models.FlatbedTransportType
		- Step deck => models.FlatbedTransportType
		- Reefer/Refrigerated => models.ReeferTransportType
		- Hotshot => models.HotShotTransportType
		- Box truck => models.BoxTruckTransportType
		- Sprinter => models.SprinterTransportType
		- Special => models.SpecialTransportType
		- Other => models.SpecialTransportType
	Refer to Jin, Perry, or Dhruv if you have additional questions.
	*/
	MapTransportTypeEnum(tmsTransportType string) (models.TransportType, error)
}

func New(ctx context.Context, tms models.Integration, opts ...models.TMSOption) (Interface, error) {
	switch tms.Name {
	case models.Aljex:
		return aljex.New(ctx, tms)

	case models.Ascend:
		return ascend.New(ctx, tms), nil

	case models.FreightFlow:
		return freightflow.New(ctx, tms)

	case models.GlobalTranzTMS:
		return globaltranztms.New(ctx, tms), nil

	case models.Mcleod:
		return mcleod.New(tms), nil

	case models.McleodEnterprise:
		return mcleodenterprise.New(tms)

	case models.MercuryGate:
		return mercurygate.New(ctx, tms)

	case models.Relay:
		return relay.New(ctx, tms), nil

	case models.Revenova:
		return revenova.New(ctx, tms), nil

	case models.Tai:
		return tai.New(ctx, tms), nil

	case models.ThreeG:
		return threeg.New(ctx, tms)

	case models.ThreePLSystems:
		return threeplsystems.New(ctx, tms), nil

	case models.TruckMate:
		return truckmate.New(ctx, tms), nil

	case models.Turvo:
		return turvo.New(ctx, tms, opts...), nil

	case models.QuantumEdge:
		return quantumedge.New(ctx, tms)

	default:
		return nil, fmt.Errorf("unknown TMS %s", tms.Name)
	}
}

// MustNew panics if the TMS name is not recognized.
func MustNew(ctx context.Context, tms models.Integration) Interface {
	result, err := New(ctx, tms)
	if err != nil {
		panic(err)
	}

	return result
}
