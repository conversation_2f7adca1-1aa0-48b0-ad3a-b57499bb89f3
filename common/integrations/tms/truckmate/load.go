package truckmate

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (t Truckmate) GetLoadIDs(context.Context, models.SearchLoadsQuery) (
	[]string,
	error,
) {
	return nil, helpers.NotImplemented(models.TruckMate, "GetLoadIDs")
}

func (t Truckmate) GetLoad(ctx context.Context, freightTrackingID string) (models.Load, models.LoadAttributes, error) {
	var shipmentResp OrderResp
	auth := ""
	attrs := t.GetDefaultLoadAttributes()
	endPoint := fmt.Sprintf("/order/%s", freightTrackingID)
	err := t.get(ctx, endPoint, nil, &shipmentResp, &auth, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, attrs, err
	}
	load, err := t.truckmateOrderToLoad(shipmentResp)
	if err != nil {
		return load, attrs, err
	}
	load.FreightTrackingID = freightTrackingID

	return load, attrs, err
}

func (t Truckmate) CreateLoad(ctx context.Context, load models.Load, _ *models.TMSUser) (models.Load, error) {
	body := t.loadToTruckMateShipmentReq(load)
	var reqBody PostLoadRequest
	reqBody.Orders = append(reqBody.Orders, body)
	var response PostLoadResponse
	auth := ""
	err := t.post(ctx, "/order", nil, reqBody, &response, &auth, s3backup.TypeLoads)
	if err != nil {
		return load, err
	}
	load.FreightTrackingID = strconv.Itoa(response.Orders[0].OrderID)
	return load, err
}

func (t Truckmate) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	load *models.Load,
) (result models.Load, attr models.LoadAttributes, _ error) {
	reqBody := t.loadToTruckMateShipmentReq(*load)
	endPoint := fmt.Sprintf("/order/%s", load.FreightTrackingID)
	var response OrderResp
	auth := ""
	err := t.put(ctx, endPoint, nil, reqBody, &response, &auth, s3backup.TypeLoads)
	if err != nil {
		return *load, attr, err
	}
	result, err = t.truckmateOrderToLoad(response)
	if err != nil {
		return result, attr, err
	}
	result.FreightTrackingID = load.FreightTrackingID
	return result, attr, err
}

func (t Truckmate) truckmateOrderToLoad(shipment OrderResp) (models.Load, error) {
	result := models.Load{
		FreightTrackingID: strconv.Itoa(shipment.OrderID),
		ServiceID:         t.tms.ServiceID,
		LoadCoreInfo: models.LoadCoreInfo{
			Status: shipment.Status,
		},
		TMSID:         t.tms.ID,
		ExternalTMSID: strconv.Itoa(shipment.OrderID),
	}

	result.Customer.Name = shipment.BillToCustomer.Name
	result.Customer.City = shipment.BillToCustomer.City
	result.Customer.State = shipment.BillToCustomer.Province
	result.Customer.Country = shipment.BillToCustomer.Country
	result.Customer.Zipcode = shipment.BillToCustomer.PostalCode
	result.Customer.Phone = shipment.BillToCustomer.Phone
	result.Customer.Email = shipment.BillToCustomer.Email
	result.Customer.AddressLine1 = shipment.BillToCustomer.Address1
	result.Customer.AddressLine2 = shipment.BillToCustomer.Address2
	result.Customer.ExternalTMSID = shipment.BillToCustomer.ClientID

	result.BillTo.Name = shipment.BillToCustomer.Name
	result.BillTo.Phone = shipment.BillToCustomer.Phone
	result.BillTo.State = shipment.BillToCustomer.Province
	result.BillTo.City = shipment.BillToCustomer.City
	result.BillTo.Country = shipment.BillToCustomer.Country
	result.BillTo.Zipcode = shipment.BillToCustomer.PostalCode
	result.BillTo.ExternalTMSID = shipment.BillToCustomer.ClientID
	result.BillTo.Email = shipment.BillToCustomer.Email
	result.BillTo.AddressLine1 = shipment.BillToCustomer.Address1
	result.BillTo.AddressLine2 = shipment.BillToCustomer.Address2

	// consignee details
	deliveryApptTime, timeErr := helpers.ParseDatetime(shipment.DeliveryAppt)
	if timeErr != nil {
		return result, fmt.Errorf("could not able to parse datetime %w", timeErr)
	}
	result.Consignee.ExternalTMSID = shipment.Consignee.ClientID
	result.Consignee.Name = shipment.Consignee.Name
	result.Consignee.Email = shipment.Consignee.Email
	result.Consignee.City = shipment.Consignee.City
	result.Consignee.State = shipment.Consignee.Province
	result.Consignee.Country = shipment.Consignee.Country
	result.Consignee.Phone = shipment.Consignee.Phone
	result.Consignee.Zipcode = shipment.Consignee.PostalCode
	result.Consignee.AddressLine1 = shipment.Consignee.Address1
	result.Consignee.AddressLine2 = shipment.Consignee.Address2
	result.Consignee.ApptStartTime = models.NullTime{
		Time:  deliveryApptTime,
		Valid: true,
	}

	// carrier details
	estimatedDeliveryDate, timeErr := helpers.ParseDatetime(shipment.EstimatedDeliveryDate)
	if timeErr != nil {
		return result, fmt.Errorf("could not able to parse datetime %w", timeErr)
	}
	deliverByEnd, timeErr := helpers.ParseDatetime(shipment.DeliverByEnd)
	if timeErr != nil {
		return result, fmt.Errorf("could not able to parse datetime %w", timeErr)
	}

	result.Carrier.FirstDriverName = shipment.DeliveryDriver1
	result.Carrier.SecondDriverName = shipment.DeliveryDriver2

	result.Carrier.DeliveryStart = models.NullTime{
		Time:  deliveryApptTime,
		Valid: true,
	}
	result.Carrier.ExpectedDeliveryTime = models.NullTime{
		Time:  estimatedDeliveryDate,
		Valid: true,
	}
	result.Carrier.DeliveryEnd = models.NullTime{
		Time:  deliverByEnd,
		Valid: true,
	}

	// pickup details
	pickUpApptTime, timeErr := helpers.ParseDatetime(shipment.PickUpAppt)
	if timeErr != nil {
		return result, fmt.Errorf("could not able to parse datetime %w", timeErr)
	}
	result.Pickup.ExternalTMSID = shipment.PickupAt.ClientID
	result.Pickup.Name = shipment.PickupAt.Name
	result.Pickup.Phone = shipment.PickupAt.Phone
	result.Pickup.Email = shipment.PickupAt.Email
	result.Pickup.City = shipment.PickupAt.City
	result.Pickup.State = shipment.PickupAt.Province
	result.Pickup.Country = shipment.PickupAt.Country
	result.Pickup.Zipcode = shipment.PickupAt.PostalCode
	result.Pickup.AddressLine1 = shipment.PickupAt.Address1
	result.Pickup.AddressLine2 = shipment.PickupAt.Address2
	result.Pickup.ApptStartTime = models.NullTime{
		Time:  pickUpApptTime,
		Valid: true,
	}

	return result, nil
}

func (t Truckmate) loadToTruckMateShipmentReq(load models.Load) (result PostOrUpdateOrderRequest) {
	var reqBody Order
	reqBody.Status = load.Status

	// consignee details
	reqBody.Consignee.Name = load.Consignee.Name
	reqBody.Consignee.Email = load.Consignee.Email
	reqBody.Consignee.Phone = load.Consignee.Phone
	reqBody.Consignee.City = load.Consignee.City
	reqBody.Consignee.Country = load.Consignee.Country
	reqBody.Consignee.PostalCode = load.Consignee.Zipcode
	reqBody.Consignee.Address1 = load.Consignee.AddressLine1
	reqBody.Consignee.Address2 = load.Consignee.AddressLine2
	reqBody.DeliveryAppt = load.Consignee.ApptStartTime.Time.Format(time.DateOnly)

	// carrier details
	reqBody.DeliveryDriver1 = load.Carrier.FirstDriverName
	reqBody.DeliveryDriver2 = load.Carrier.SecondDriverName
	reqBody.DeliverByEnd = load.Carrier.DeliveryEnd.Time.Format(time.DateOnly)

	// PickUpDetails
	reqBody.PickupAt.Phone = load.Pickup.Phone
	reqBody.PickupAt.Email = load.Pickup.Email
	reqBody.PickupAt.City = load.Pickup.City
	reqBody.PickupAt.Country = load.Pickup.Country
	reqBody.PickupAt.Address1 = load.Pickup.AddressLine1
	reqBody.PickupAt.Address2 = load.Pickup.AddressLine2

	result.Orders = append(result.Orders, reqBody)

	return result
}
