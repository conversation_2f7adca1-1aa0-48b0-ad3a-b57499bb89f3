package truckmate

import (
	"context"
	"fmt"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

func (t Truckmate) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrakingID string) (checkCalls []models.CheckCall, err error) {
	endPoint := fmt.Sprintf("/orders/%s/statusHistory", freightTrakingID)
	auth := ""
	var response GetCheckCallsResponse
	err = t.get(ctx, endPoint, nil, &response, &auth, s3backup.TypeCheckCalls)
	if err != nil {
		return []models.CheckCall{}, err
	}
	checkCalls, err = t.truckmateOrderHistoryToCheckCall(response, freightTrakingID, loadID)
	return checkCalls, err
}

func (t Truckmate) UpdateCheckCall(
	ctx context.Context,
	loadID uint,
	checkCall models.CheckCall,
) (models.CheckCall, error) {
	endPoint := fmt.Sprintf("/orders/%s/status", checkCall.FreightTrackingID)
	reqBody := t.toTruckmateCheckCall(checkCall)
	var response CheckCallResp
	auth := ""
	err := t.put(ctx, endPoint, nil, reqBody, &response, &auth, s3backup.TypeCheckCalls)
	checkCall.LoadID = loadID
	return checkCall, err
}

func (t Truckmate) truckmateOrderHistoryToCheckCall(
	response GetCheckCallsResponse,
	freightTrakingID string,
	loadID uint,
) ([]models.CheckCall, error) {
	result := make([]models.CheckCall, 0)
	for _, res := range response.StatusHistory {
		var checkCall models.CheckCall
		checkCallDate, timeErr := helpers.ParseDatetime(res.InsDate)
		if timeErr != nil {
			return result, fmt.Errorf("could not able to parse datetime %w", timeErr)
		}
		checkCall.Status = res.StatusCode
		checkCall.Reason = res.Reason
		checkCall.Notes = res.StatComment
		checkCall.FreightTrackingID = freightTrakingID
		checkCall.LoadID = loadID
		checkCall.DateTime = models.NullTime{
			Time:  checkCallDate,
			Valid: true,
		}
		checkCall.Author = res.UpdatedBy

		result = append(result, checkCall)
	}
	return result, nil
}

func (t Truckmate) toTruckmateCheckCall(checkcall models.CheckCall) (result UpdateCheckCallRequest) {
	result.StatusCode = checkcall.Status
	result.Reason = checkcall.Reason
	result.Latitude = int(checkcall.Lat)
	result.Longitude = int(checkcall.Lon)
	result.Time = checkcall.DateTime.Time.Format(time.DateOnly)

	return result
}
