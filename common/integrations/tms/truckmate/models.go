package truckmate

type TokenResp struct {
	JWT string `json:"JWT"`
}

type OrderResp struct {
	OrderID                int     `json:"orderId"`
	OpCode                 string  `json:"opCode"`
	BillNumber             string  `json:"billNumber"`
	StartZone              string  `json:"startZone"`
	StartZoneDescription   string  `json:"startZoneDescription"`
	EndZone                string  `json:"endZone"`
	EndZoneDescription     string  `json:"endZoneDescription"`
	Status                 string  `json:"status"`
	StatusDescription      string  `json:"statusDescription"`
	LongStatusDescription  string  `json:"longStatusDescription"`
	CurrentStatusBehaviour string  `json:"currentStatusBehaviour"`
	CurrentZone            string  `json:"currentZone"`
	CurrentZoneDescription string  `json:"currentZoneDescription"`
	ActualDelivery         string  `json:"actualDelivery"`
	ActualPickup           string  `json:"actualPickup"`
	CreatedBy              string  `json:"createdBy"`
	CreatedTime            string  `json:"createdTime"`
	ExtraStops             string  `json:"extraStops"`
	MasterOrder            int     `json:"masterOrder"`
	NoStops                int     `json:"noStops"`
	ParentOrder            int     `json:"parentOrder"`
	SiteID                 string  `json:"siteId"`
	SlmDaysSum             int     `json:"slmDaysSum"`
	BillDate               string  `json:"billDate"`
	BillNumberKey          string  `json:"billNumberKey"`
	BillToCode             string  `json:"billToCode"`
	BillTo                 string  `json:"billTo"`
	CashCollect            string  `json:"cashCollect"`
	Pallets                int     `json:"pallets"`
	Pieces                 int     `json:"pieces"`
	Length                 int     `json:"length"`
	Cube                   int     `json:"cube"`
	Weight                 int     `json:"weight"`
	IsDangerousGoods       string  `json:"isDangerousGoods"`
	HighValue              string  `json:"highValue"`
	ServiceLevel           string  `json:"serviceLevel"`
	Commodity              string  `json:"commodity"`
	PickUpBy               string  `json:"pickUpBy"`
	PickUpByEnd            string  `json:"pickUpByEnd"`
	DeliverBy              string  `json:"deliverBy"`
	DeliverByEnd           string  `json:"deliverByEnd"`
	DeliveryApptReq        string  `json:"deliveryApptReq"`
	DeliveryApptMade       string  `json:"deliveryApptMade"`
	DeliveryAppt           string  `json:"deliveryAppt"`
	DeliveryTerminal       string  `json:"deliveryTerminal"`
	PickUpApptMade         string  `json:"pickUpApptMade"`
	PickUpApptReq          string  `json:"pickUpApptReq"`
	PickUpAppt             string  `json:"pickUpAppt"`
	PickupTerminal         string  `json:"pickupTerminal"`
	PickUpDriver           string  `json:"pickUpDriver"`
	Tax1                   int     `json:"tax1"`
	Tax2                   int     `json:"tax2"`
	TotalCharges           int     `json:"totalCharges"`
	Charges                int     `json:"charges"`
	CurrencyCode           string  `json:"currencyCode"`
	DeclaredValue          int     `json:"declaredValue"`
	TerminalZone           string  `json:"terminalZone"`
	TrailerNumber          string  `json:"trailerNumber"`
	PickupDone             string  `json:"pickupDone"`
	Latitude               int     `json:"latitude"`
	Longitude              int     `json:"longitude"`
	CurrentTrip            int     `json:"currentTrip"`
	NextTrip               int     `json:"nextTrip"`
	StartStopStatus        int     `json:"startStopStatus"`
	EndStopStatus          int     `json:"endStopStatus"`
	BillType               string  `json:"billType"`
	BillingAuditNumber     int     `json:"billingAuditNumber"`
	UpdatedTimestamp       string  `json:"updatedTimestamp"`
	CreatedTimestamp       string  `json:"createdTimestamp"`
	WebUsername            string  `json:"webUsername"`
	NoCharge               string  `json:"noCharge"`
	EstimatedDeliveryDate  string  `json:"estimatedDeliveryDate"`
	IsCsa                  string  `json:"isCsa"`
	IsTarped               string  `json:"isTarped"`
	IsFast                 string  `json:"isFast"`
	IsExclusive            string  `json:"isExclusive"`
	ExclusiveOverride      string  `json:"exclusiveOverride"`
	CodAmount              int     `json:"codAmount"`
	DeliveryDriver1        string  `json:"deliveryDriver1"`
	DeliveryDriver2        string  `json:"deliveryDriver2"`
	DeliveryPowerUnit1     string  `json:"deliveryPowerUnit1"`
	DeliveryPowerUnit2     string  `json:"deliveryPowerUnit2"`
	DeliveryTrailer1       string  `json:"deliveryTrailer1"`
	DeliveryTrailer2       string  `json:"deliveryTrailer2"`
	DestinationSpotTrailer string  `json:"destinationSpotTrailer"`
	OriginSpotTrailer      string  `json:"originSpotTrailer"`
	PickupDriver2          string  `json:"pickupDriver2"`
	PickupPowerUnit1       string  `json:"pickupPowerUnit1"`
	PickupPowerUnit2       string  `json:"pickupPowerUnit2"`
	PickupTrailer1         string  `json:"pickupTrailer1"`
	PickupTrailer2         string  `json:"pickupTrailer2"`
	IsApproved             string  `json:"isApproved"`
	WebStatus              string  `json:"webStatus"`
	WebUpdate              string  `json:"webUpdate"`
	WebAccept              string  `json:"webAccept"`
	QuoteID                int     `json:"quoteId"`
	Distance               int     `json:"distance"`
	DistanceUnits          string  `json:"distanceUnits"`
	RatingTerms            string  `json:"ratingTerms"`
	Caller                 Caller  `json:"caller"`
	Shipper                Shipper `json:"shipper"`
	Consignee              Shipper `json:"consignee"`
	PickupAt               Shipper `json:"pickupAt"`
	XrefCode               struct {
		ShipperQualifier      string `json:"shipperQualifier"`
		ShipperID             string `json:"shipperId"`
		ConsigneeQualifier    string `json:"consigneeQualifier"`
		ConsigneeID           string `json:"consigneeId"`
		PickupAtQualifier     string `json:"pickupAtQualifier"`
		PickupAtID            string `json:"pickupAtId"`
		CareOfQualifier       string `json:"careOfQualifier"`
		CareOfID              string `json:"careOfId"`
		OtherContactQualifier string `json:"otherContactQualifier"`
		OtherContactID        string `json:"otherContactId"`
		ServiceLevel          string `json:"serviceLevel"`
		ShipInstructs         []struct {
			Instruction string `json:"instruction"`
		} `json:"shipInstructs"`
	} `json:"xrefCode"`
	Intermodal struct {
		ContainerCode              string `json:"containerCode"`
		ContainerNumber            string `json:"containerNumber"`
		ContainerSize              string `json:"containerSize"`
		ContainerType              string `json:"containerType"`
		ContainerOwner             string `json:"containerOwner"`
		MovementType               string `json:"movementType"`
		ContainerIso               string `json:"containerIso"`
		Pickup                     string `json:"pickup"`
		Delivery                   string `json:"delivery"`
		BillToCode                 string `json:"billToCode"`
		ChassisTermination         string `json:"chassisTermination"`
		ChassisPickup              string `json:"chassisPickup"`
		EmptyDisposition           string `json:"emptyDisposition"`
		EmptyOrigin                string `json:"emptyOrigin"`
		CardNumber                 string `json:"cardNumber"`
		SealNumber                 string `json:"sealNumber"`
		Vessel                     string `json:"vessel"`
		Voyage                     string `json:"voyage"`
		BookingNumber              string `json:"bookingNumber"`
		ExportDestination          string `json:"exportDestination"`
		EmptyNotifyDate            string `json:"emptyNotifyDate"`
		LoadedDate                 string `json:"loadedDate"`
		CheckDigit                 string `json:"checkDigit"`
		VesselAta                  string `json:"vesselAta"`
		ChassisBillingDeliveryDate string `json:"chassisBillingDeliveryDate"`
		ChassisBillingDispatchDate string `json:"chassisBillingDispatchDate"`
		ChassisBillingEndDate      string `json:"chassisBillingEndDate"`
		ChassisBillingFBPickupDate string `json:"chassisBillingFBPickupDate"`
		ChassisBillingStartDate    string `json:"chassisBillingStartDate"`
		ChassisBillingTripDate     string `json:"chassisBillingTripDate"`
		ExportVesselCutoff         string `json:"exportVesselCutoff"`
		DeliveryOrderDate          string `json:"deliveryOrderDate"`
		VesselEta                  string `json:"vesselEta"`
		ContainerlastFreeDate      string `json:"containerlastFreeDate"`
		ContainerAvailableDate     string `json:"containerAvailableDate"`
		RailEta                    string `json:"railEta"`
		ContainerReturnDate        string `json:"containerReturnDate"`
		JobNumber                  string `json:"jobNumber"`
		ShipmentNumber             string `json:"shipmentNumber"`
		Supplier                   string `json:"supplier"`
		WoReference                string `json:"woReference"`
		Bol                        string `json:"bol"`
		CustomerOrder              string `json:"customerOrder"`
		Deramp                     string `json:"deramp"`
		PickupNumber               string `json:"pickupNumber"`
		PortOfExit                 string `json:"portOfExit"`
		InBond                     string `json:"inBond"`
		OblHold                    string `json:"oblHold"`
		AgricultureHold            string `json:"agricultureHold"`
		ContainerPoolClient        string `json:"containerPoolClient"`
	} `json:"intermodal"`
	BillToCustomer struct {
		ClientID        string `json:"clientId"`
		Name            string `json:"name"`
		Address         string `json:"address"`
		Address1        string `json:"address1"`
		Address2        string `json:"address2"`
		City            string `json:"city"`
		Province        string `json:"province"`
		PostalCode      string `json:"postalCode"`
		Country         string `json:"country"`
		Zone            string `json:"zone"`
		ZoneDescription string `json:"zoneDescription"`
		Contact         string `json:"contact"`
		Phone           string `json:"phone"`
		PhoneExt        string `json:"phoneExt"`
		PhoneNumber     string `json:"phoneNumber"`
		Cell            string `json:"cell"`
		Email           string `json:"email"`
		Fax             string `json:"fax"`
		Unit            string `json:"unit"`
	} `json:"billToCustomer"`
	ACharges []struct {
		AChargeCode          string `json:"aChargeCode"`
		AChargeID            int    `json:"aChargeId"`
		AChargeType          string `json:"aChargeType"`
		AutoAssigned         string `json:"autoAssigned"`
		ChargeAmount         int    `json:"chargeAmount"`
		OrderID              int    `json:"orderId"`
		FunctionalAmount     int    `json:"functionalAmount"`
		IsManual             string `json:"isManual"`
		Notation             string `json:"notation"`
		Quantity             int    `json:"quantity"`
		Rate                 int    `json:"rate"`
		ActualQuantity       int    `json:"actualQuantity"`
		Threshold            int    `json:"threshold"`
		Increment            int    `json:"increment"`
		Description          string `json:"description"`
		AChargeCurrencyRates struct {
			Currency            string `json:"currency"`
			Quantity            int    `json:"quantity"`
			Rate                int    `json:"rate"`
			Amount              int    `json:"amount"`
			ForeignExchangeRate int    `json:"foreignExchangeRate"`
		} `json:"aChargeCurrencyRates"`
	} `json:"aCharges"`
	Details []struct {
		OrderID                int    `json:"orderId"`
		OrderDetailID          int    `json:"orderDetailId"`
		Commodity              string `json:"commodity"`
		Description            string `json:"description"`
		Pallets                int    `json:"pallets"`
		PalletUnits            string `json:"palletUnits"`
		Pieces                 int    `json:"pieces"`
		PiecesUnits            string `json:"piecesUnits"`
		Weight                 int    `json:"weight"`
		WeightUnits            string `json:"weightUnits"`
		Cube                   int    `json:"cube"`
		CubeUnits              string `json:"cubeUnits"`
		Length                 int    `json:"length"`
		LengthUnits            string `json:"lengthUnits"`
		Height                 int    `json:"height"`
		HeightUnits            string `json:"heightUnits"`
		Width                  int    `json:"width"`
		WidthUnits             string `json:"widthUnits"`
		Items                  int    `json:"items"`
		UnID                   int    `json:"unId"`
		UnNumber               string `json:"unNumber"`
		TemperatureControlled  string `json:"temperatureControlled"`
		RequestedEquipment     string `json:"requestedEquipment"`
		DangerousGoods         string `json:"dangerousGoods"`
		Area                   int    `json:"area"`
		AreaUnits              string `json:"areaUnits"`
		Cost                   int    `json:"cost"`
		DimWeight              int    `json:"dimWeight"`
		DimWeightUnits         string `json:"dimWeightUnits"`
		Discount               int    `json:"discount"`
		DiscountRate           int    `json:"discountRate"`
		Distance               int    `json:"distance"`
		DistanceUnits          string `json:"distanceUnits"`
		Rate                   int    `json:"rate"`
		RateFromZone           string `json:"rateFromZone"`
		RatePer                int    `json:"ratePer"`
		RateToZone             string `json:"rateToZone"`
		RateUnits              string `json:"rateUnits"`
		SubCost                int    `json:"subCost"`
		Temperature            int    `json:"temperature"`
		TemperatureUnits       string `json:"temperatureUnits"`
		Time                   int    `json:"time"`
		TimeUnits              string `json:"timeUnits"`
		Volume                 int    `json:"volume"`
		VolumeUnits            string `json:"volumeUnits"`
		HighValue              string `json:"highValue"`
		RegionalRestrictions   string `json:"regionalRestrictions"`
		ManuallyRated          string `json:"manuallyRated"`
		EstimatedCube          int    `json:"estimatedCube"`
		EstimatedHeight        int    `json:"estimatedHeight"`
		EstimatedLength        int    `json:"estimatedLength"`
		EstimatedPallets       int    `json:"estimatedPallets"`
		EstimatedPieces        int    `json:"estimatedPieces"`
		EstimatedWeight        int    `json:"estimatedWeight"`
		EstimatedWidth         int    `json:"estimatedWidth"`
		EstimatedVolume        int    `json:"estimatedVolume"`
		ItemCube               int    `json:"itemCube"`
		ItemPallets            int    `json:"itemPallets"`
		ItemPieces             int    `json:"itemPieces"`
		ItemTemperature        int    `json:"itemTemperature"`
		ItemWeight             int    `json:"itemWeight"`
		ItemVolume             int    `json:"itemVolume"`
		Perishable             string `json:"perishable"`
		AsRate                 int    `json:"asRate"`
		AsBreak                int    `json:"asBreak"`
		RatesheetCurrencyRates struct {
			Rate                int    `json:"rate"`
			AsRate              int    `json:"asRate"`
			DiscountRate        int    `json:"discountRate"`
			DiscountAmount      int    `json:"discountAmount"`
			SubCost             int    `json:"subCost"`
			Cost                int    `json:"cost"`
			Currency            string `json:"currency"`
			ForeignExchangeRate int    `json:"foreignExchangeRate"`
		} `json:"ratesheetCurrencyRates"`
		Barcodes []struct {
			Barcode              string `json:"barcode"`
			AltBarcode1          string `json:"altBarcode1"`
			AltBarcode2          string `json:"altBarcode2"`
			Cube                 int    `json:"cube"`
			CubeUnits            string `json:"cubeUnits"`
			Height               int    `json:"height"`
			HeightUnits          string `json:"heightUnits"`
			Length               int    `json:"length"`
			LengthUnits          string `json:"lengthUnits"`
			Location             string `json:"location"`
			LocationTerminalID   int    `json:"locationTerminalId"`
			LocationTerminalZone string `json:"locationTerminalZone"`
			LocationTimestamp    string `json:"locationTimestamp"`
			LocationTypeDesc     string `json:"locationTypeDesc"`
			LocationType         string `json:"locationType"`
			Pallets              int    `json:"pallets"`
			PalletUnits          string `json:"palletUnits"`
			Pieces               int    `json:"pieces"`
			PieceUnits           string `json:"pieceUnits"`
			Temperature          int    `json:"temperature"`
			TemperatureUnits     string `json:"temperatureUnits"`
			Status               string `json:"status"`
			StatusComment        string `json:"statusComment"`
			Volume               int    `json:"volume"`
			VolumeUnits          string `json:"volumeUnits"`
			Weight               int    `json:"weight"`
			WeightUnits          string `json:"weightUnits"`
			Width                int    `json:"width"`
			WidthUnits           string `json:"widthUnits"`
			Sequence             int    `json:"sequence"`
			BarcodeID            int    `json:"barcodeId"`
			CubePolicyID         int    `json:"cubePolicyId"`
			CubePolicyValue      int    `json:"cubePolicyValue"`
			DoorNumber           int    `json:"doorNumber"`
			HandlingNotes        string `json:"handlingNotes"`
			Intact               string `json:"intact"`
			IsVerified           string `json:"isVerified"`
			Lifts                int    `json:"lifts"`
			OrderID              int    `json:"orderId"`
			MobileScanned        string `json:"mobileScanned"`
			PieceCubing          string `json:"pieceCubing"`
			PrintStatus          string `json:"printStatus"`
			TripNumber           int    `json:"tripNumber"`
		} `json:"barcodes"`
	} `json:"details"`
	Osds []struct {
		OsdID                 int    `json:"osdId"`
		OrderID               int    `json:"orderId"`
		TxType                string `json:"txType"`
		ReportedDate          string `json:"reportedDate"`
		ReportedBy            string `json:"reportedBy"`
		CreatedBy             string `json:"createdBy"`
		CreatedWhen           string `json:"createdWhen"`
		Description           string `json:"description"`
		OsdDept               string `json:"osdDept"`
		OsdDriver             string `json:"osdDriver"`
		OsdDriverExternal     string `json:"osdDriverExternal"`
		OsdInterliner         string `json:"osdInterliner"`
		OsdInterlinerExternal string `json:"osdInterlinerExternal"`
		CurrentStatus         string `json:"currentStatus"`
		OsdTerminal           string `json:"osdTerminal"`
		OsdDetails            []struct {
			OsdTldtlID    int    `json:"osdTldtlId"`
			OsdID         int    `json:"osdId"`
			Quantity      int    `json:"quantity"`
			QuantityUnits string `json:"quantityUnits"`
			OsdType       string `json:"osdType"`
			OrderDetailID int    `json:"orderDetailId"`
		} `json:"osdDetails"`
	} `json:"osds"`
	ShipInstructs []struct {
		InstructionID int    `json:"instructionId"`
		Instruction   string `json:"instruction"`
		AppliesTo     string `json:"appliesTo"`
		Description   string `json:"description"`
		ShortDesc     string `json:"shortDesc"`
		OrderID       int    `json:"orderId"`
		AutoAssigned  string `json:"autoAssigned"`
		Active        string `json:"active"`
		ModifiedBy    string `json:"modifiedBy"`
		ModifiedDate  string `json:"modifiedDate"`
	} `json:"shipInstructs"`
	TraceNumbers []struct {
		OrderID      int    `json:"orderId"`
		TraceID      int    `json:"traceId"`
		TraceType    string `json:"traceType"`
		TraceNumber  string `json:"traceNumber"`
		Description  string `json:"description"`
		RefQualifier string `json:"refQualifier"`
	} `json:"traceNumbers"`
	Notes []struct {
		ProgTable    string `json:"progTable"`
		NoteType     string `json:"noteType"`
		IDKey        string `json:"idKey"`
		Seq          int    `json:"seq"`
		TheNote      string `json:"theNote"`
		ModifiedBy   string `json:"modifiedBy"`
		ModifiedTime string `json:"modifiedTime"`
		CreatedBy    string `json:"createdBy"`
		CreatedTime  string `json:"createdTime"`
	} `json:"notes"`
	Interliners []struct {
		OrderInterlinerID int    `json:"orderInterlinerId"`
		MovementType      string `json:"movementType"`
		InterlinerID      string `json:"interlinerId"`
		FromZone          string `json:"fromZone"`
		ToZone            string `json:"toZone"`
		Probill           string `json:"probill"`
		Currency          string `json:"currency"`
		ChargesType       string `json:"chargesType"`
		TotalCharges      int    `json:"totalCharges"`
		SubTotal          int    `json:"subTotal"`
		ExtraCharges      int    `json:"extraCharges"`
		InterlinerName    string `json:"interlinerName"`
		DropLocation      struct {
			Name            string `json:"name"`
			Address1        string `json:"address1"`
			Address2        string `json:"address2"`
			City            string `json:"city"`
			StartDate       string `json:"startDate"`
			EndDate         string `json:"endDate"`
			Province        string `json:"province"`
			PostalCode      string `json:"postalCode"`
			ZoneDescription string `json:"zoneDescription"`
			Country         string `json:"country"`
			Phone           string `json:"phone"`
			PhoneExt        string `json:"phoneExt"`
			Cell            string `json:"cell"`
			Contact         string `json:"contact"`
			Email           string `json:"email"`
		} `json:"dropLocation"`
		PickupLocation struct {
			Name            string `json:"name"`
			Address1        string `json:"address1"`
			Address2        string `json:"address2"`
			City            string `json:"city"`
			StartDate       string `json:"startDate"`
			EndDate         string `json:"endDate"`
			Province        string `json:"province"`
			PostalCode      string `json:"postalCode"`
			ZoneDescription string `json:"zoneDescription"`
			Country         string `json:"country"`
			Phone           string `json:"phone"`
			PhoneExt        string `json:"phoneExt"`
			Cell            string `json:"cell"`
			Contact         string `json:"contact"`
			Email           string `json:"email"`
		} `json:"pickupLocation"`
		ACharges []struct {
			OrderInterlinerID        int    `json:"orderInterlinerId"`
			AChargeID                int    `json:"aChargeId"`
			AChargeCode              string `json:"aChargeCode"`
			AChargeType              string `json:"aChargeType"`
			Notation                 string `json:"notation"`
			Quantity                 int    `json:"quantity"`
			Rate                     int    `json:"rate"`
			ChargeAmount             int    `json:"chargeAmount"`
			FunctionalAmount         int    `json:"functionalAmount"`
			Currency                 string `json:"currency"`
			AdjustedFunctionalAmount int    `json:"adjustedFunctionalAmount"`
			IsManual                 string `json:"isManual"`
			AutoAssigned             string `json:"autoAssigned"`
			CreatedTimestamp         string `json:"createdTimestamp"`
			UpdatedTimestamp         string `json:"updatedTimestamp"`
		} `json:"aCharges"`
	} `json:"interliners"`
	StatusHistory []struct {
		OrderID           int    `json:"orderId"`
		Changed           string `json:"changed"`
		StatusCode        string `json:"statusCode"`
		StatusDescription string `json:"statusDescription"`
		StatComment       string `json:"statComment"`
		Reason            string `json:"reason"`
		ReasonDescription string `json:"reasonDescription"`
		UpdatedBy         string `json:"updatedBy"`
		TripNumber        int    `json:"tripNumber"`
		InsDate           string `json:"insDate"`
		User1             string `json:"user1"`
		User2             string `json:"user2"`
		User3             string `json:"user3"`
	} `json:"statusHistory"`
	Pods []struct {
		PodID              int    `json:"podId"`
		OrderID            int    `json:"orderId"`
		TxType             string `json:"txType"`
		Description        string `json:"description"`
		DriverCounted      string `json:"driverCounted"`
		ReceiverSigned     string `json:"receiverSigned"`
		SignedClean        string `json:"signedClean"`
		CopyReceived       string `json:"copyReceived"`
		TraceNo            string `json:"traceNo"`
		SignedBy           string `json:"signedBy"`
		SignedOn           string `json:"signedOn"`
		CreatedWhen        string `json:"createdWhen"`
		CreatedBy          string `json:"createdBy"`
		ModifiedWhen       string `json:"modifiedWhen"`
		ModifiedBy         string `json:"modifiedBy"`
		Driver             string `json:"driver"`
		DriverExternal     string `json:"driverExternal"`
		Interliner         string `json:"interliner"`
		InterlinerExternal string `json:"interlinerExternal"`
		ChildSplitID       int    `json:"childSplitId"`
		ChildBillNumber    string `json:"childBillNumber"`
		Signature          struct {
			SignatureID int    `json:"signatureId"`
			SigneeName  string `json:"signeeName"`
			SignedTime  string `json:"signedTime"`
			ImageWidth  int    `json:"imageWidth"`
			ImageHeight int    `json:"imageHeight"`
			CreatedTime string `json:"createdTime"`
			CreatedBy   string `json:"createdBy"`
			ImageType   string `json:"imageType"`
			ImageData   string `json:"imageData"`
		} `json:"signature"`
	} `json:"pods"`
	DangerousGoods []struct {
		OrderID                  int    `json:"orderId"`
		DgID                     int    `json:"dgId"`
		OrderDetailID            int    `json:"orderDetailId"`
		UnNumber                 string `json:"unNumber"`
		Class                    string `json:"class"`
		SubClass                 string `json:"subClass"`
		CompatibilityGroup       string `json:"compatibilityGroup"`
		PackingGroup             string `json:"packingGroup"`
		Volume                   int    `json:"volume"`
		VolumeUnits              string `json:"volumeUnits"`
		Mass                     int    `json:"mass"`
		MassUnits                string `json:"massUnits"`
		ExplosiveQuantity        int    `json:"explosiveQuantity"`
		ExplosiveUnits           string `json:"explosiveUnits"`
		Containers               int    `json:"containers"`
		ControlTemp              int    `json:"controlTemp"`
		EmergencyTemp            int    `json:"emergencyTemp"`
		MarinePollutant          string `json:"marinePollutant"`
		RequiredQuantity         string `json:"requiredQuantity"`
		MarinePollutantPrintReq  string `json:"marinePollutantPrintReq"`
		LimitedQuantity          string `json:"limitedQuantity"`
		InhalationHazardZone     string `json:"inhalationHazardZone"`
		InhalationHazardZoneType string `json:"inhalationHazardZoneType"`
		ActualName               string `json:"actualName"`
		ContactName              string `json:"contactName"`
		ContactNumber            string `json:"contactNumber"`
		CountryCode              string `json:"countryCode"`
		Hazchem                  string `json:"hazchem"`
	} `json:"dangerousGoods"`
	CustomDefs []struct {
		CustomLabel string `json:"customLabel"`
		SourceID    string `json:"sourceId"`
		CustomDefID int    `json:"customDefId"`
		CustomValue string `json:"customValue"`
	} `json:"customDefs"`
	Checklist []struct {
		OrderID         int    `json:"orderId"`
		Description     string `json:"description"`
		Comment         string `json:"comment"`
		ID              int    `json:"id"`
		IsComplete      string `json:"isComplete"`
		UpdatedBy       string `json:"updatedBy"`
		UpdatedWhen     string `json:"updatedWhen"`
		AssignedTo      string `json:"assignedTo"`
		AssignedDate    string `json:"assignedDate"`
		IsRequired      string `json:"isRequired"`
		IsRequiredAvail string `json:"isRequiredAvail"`
		AddedBy         string `json:"addedBy"`
		AddedWhen       string `json:"addedWhen"`
		CompletedBy     string `json:"completedBy"`
		CompletedWhen   string `json:"completedWhen"`
	} `json:"checklist"`
	TerminalPlans []struct {
		TerminalPlanID       int    `json:"terminalPlanId"`
		OrderID              int    `json:"orderId"`
		LoadPlanID           int    `json:"loadPlanId"`
		LoadPlanOption       int    `json:"loadPlanOption"`
		Sequence             int    `json:"sequence"`
		TripNumber           int    `json:"tripNumber"`
		TripStatus           string `json:"tripStatus"`
		RouteID              string `json:"routeId"`
		TerminalZone         string `json:"terminalZone"`
		NextZone             string `json:"nextZone"`
		Type                 string `json:"type"`
		TypeDescription      string `json:"typeDescription"`
		ClientID             string `json:"clientId"`
		DepartureDate        string `json:"departureDate"`
		ArrivalDate          string `json:"arrivalDate"`
		TerminalMode         string `json:"terminalMode"`
		TravelMode           string `json:"travelMode"`
		AvailableDate        string `json:"availableDate"`
		OutboundDoor         string `json:"outboundDoor"`
		InboundDoor          string `json:"inboundDoor"`
		ManualTransfer       string `json:"manualTransfer"`
		ServiceType          string `json:"serviceType"`
		ServiceClass         string `json:"serviceClass"`
		IntermediateJunction string `json:"intermediateJunction"`
	} `json:"terminalPlans"`
}

type Caller struct {
	ClientID   string `json:"clientId"`
	Name       string `json:"name"`
	Address1   string `json:"address1"`
	Address2   string `json:"address2"`
	City       string `json:"city"`
	Province   string `json:"province"`
	PostalCode string `json:"postalCode"`
	Contact    string `json:"contact"`
	Phone      string `json:"phone"`
	PhoneExt   string `json:"phoneExt"`
	Country    string `json:"country"`
	Email      string `json:"email"`
	Fax        string `json:"fax"`
	Cell       string `json:"cell"`
	Unit       string `json:"unit"`
}

type Shipper struct {
	ClientID        string `json:"clientId"`
	Name            string `json:"name"`
	Address1        string `json:"address1"`
	Address2        string `json:"address2"`
	City            string `json:"city"`
	Province        string `json:"province"`
	PostalCode      string `json:"postalCode"`
	Zone            string `json:"zone"`
	ZoneDescription string `json:"zoneDescription"`
	Contact         string `json:"contact"`
	Phone           string `json:"phone"`
	PhoneExt        string `json:"phoneExt"`
	Cell            string `json:"cell"`
	Country         string `json:"country"`
	Email           string `json:"email"`
	Fax             string `json:"fax"`
	Unit            string `json:"unit"`
}

type PostOrUpdateOrderRequest struct {
	Orders []Order `json:"orders"`
}

type Order struct {
	Caller    Caller  `json:"caller"`
	Shipper   Shipper `json:"shipper"`
	Consignee Shipper `json:"consignee"`
	PickupAt  Shipper `json:"pickupAt"`
	Details   []struct {
		Area                  int    `json:"area"`
		AreaUnits             string `json:"areaUnits"`
		Commodity             string `json:"commodity"`
		Cube                  int    `json:"cube"`
		CubeUnits             string `json:"cubeUnits"`
		DangerousGoods        string `json:"dangerousGoods"`
		Description           string `json:"description"`
		Discount              int    `json:"discount"`
		Distance              int    `json:"distance"`
		DistanceUnits         string `json:"distanceUnits"`
		Field1                string `json:"field1"`
		Field2                string `json:"field2"`
		Field3                string `json:"field3"`
		Field4                string `json:"field4"`
		Field5                string `json:"field5"`
		Field6                string `json:"field6"`
		Field7                string `json:"field7"`
		Field8                string `json:"field8"`
		Field9                string `json:"field9"`
		Field10               string `json:"field10"`
		Field11               int    `json:"field11"`
		Field12               int    `json:"field12"`
		Field13               int    `json:"field13"`
		Field14               int    `json:"field14"`
		Field15               int    `json:"field15"`
		Field16               int    `json:"field16"`
		Field17               int    `json:"field17"`
		Field18               int    `json:"field18"`
		Field19               int    `json:"field19"`
		Field20               int    `json:"field20"`
		Field21               string `json:"field21"`
		Field22               string `json:"field22"`
		Field23               string `json:"field23"`
		Field24               string `json:"field24"`
		Field25               string `json:"field25"`
		Height                int    `json:"height"`
		HeightUnits           string `json:"heightUnits"`
		Length                int    `json:"length"`
		LengthUnits           string `json:"lengthUnits"`
		Pallets               int    `json:"pallets"`
		PalletUnits           string `json:"palletUnits"`
		Pieces                int    `json:"pieces"`
		PiecesUnits           string `json:"piecesUnits"`
		Rate                  int    `json:"rate"`
		RateUnits             string `json:"rateUnits"`
		RatePer               int    `json:"ratePer"`
		RequestedEquipment    string `json:"requestedEquipment"`
		SubCost               int    `json:"subCost"`
		TemperatureControlled string `json:"temperatureControlled"`
		Temperature           int    `json:"temperature"`
		TemperatureUnits      string `json:"temperatureUnits"`
		Time                  int    `json:"time"`
		TimeUnits             string `json:"timeUnits"`
		Volume                int    `json:"volume"`
		VolumeUnits           string `json:"volumeUnits"`
		Width                 int    `json:"width"`
		WidthUnits            string `json:"widthUnits"`
		Weight                int    `json:"weight"`
		WeightUnits           string `json:"weightUnits"`
		Items                 int    `json:"items"`
		UnNumber              string `json:"unNumber"`
		HighValue             string `json:"highValue"`
		ManuallyRated         string `json:"manuallyRated"`
		UserFieldInt1         int    `json:"userFieldInt1"`
		UserFieldInt2         int    `json:"userFieldInt2"`
		UserFieldInt3         int    `json:"userFieldInt3"`
		UserFieldInt4         int    `json:"userFieldInt4"`
		UserFieldInt5         int    `json:"userFieldInt5"`
		UserFieldInt6         int    `json:"userFieldInt6"`
		EstimatedCube         int    `json:"estimatedCube"`
		EstimatedHeight       int    `json:"estimatedHeight"`
		EstimatedLength       int    `json:"estimatedLength"`
		EstimatedPallets      int    `json:"estimatedPallets"`
		EstimatedPieces       int    `json:"estimatedPieces"`
		EstimatedWeight       int    `json:"estimatedWeight"`
		EstimatedWidth        int    `json:"estimatedWidth"`
		EstimatedVolume       int    `json:"estimatedVolume"`
		Perishable            string `json:"perishable"`
		AsRate                int    `json:"asRate"`
		AsBreak               int    `json:"asBreak"`
		Barcodes              []struct {
			BarcodeID          int    `json:"barcodeId"`
			AltBarcode1        string `json:"altBarcode1"`
			AltBarcode2        string `json:"altBarcode2"`
			Cube               int    `json:"cube"`
			CubeUnits          string `json:"cubeUnits"`
			Height             int    `json:"height"`
			HeightUnits        string `json:"heightUnits"`
			Length             int    `json:"length"`
			LengthUnits        string `json:"lengthUnits"`
			LocationTerminalID int    `json:"locationTerminalId"`
			LocationTimestamp  string `json:"locationTimestamp"`
			LocationType       string `json:"locationType"`
			Pallets            int    `json:"pallets"`
			PalletUnits        string `json:"palletUnits"`
			Pieces             int    `json:"pieces"`
			PieceUnits         string `json:"pieceUnits"`
			Temperature        int    `json:"temperature"`
			TemperatureUnits   string `json:"temperatureUnits"`
			StatusComment      string `json:"statusComment"`
			Volume             int    `json:"volume"`
			VolumeUnits        string `json:"volumeUnits"`
			Weight             int    `json:"weight"`
			WeightUnits        string `json:"weightUnits"`
			Width              int    `json:"width"`
			WidthUnits         string `json:"widthUnits"`
			HandlingNotes      string `json:"handlingNotes"`
			Intact             string `json:"intact"`
			Lifts              int    `json:"lifts"`
			MobileScanned      string `json:"mobileScanned"`
			PieceCubing        string `json:"pieceCubing"`
			PrintStatus        string `json:"printStatus"`
			User1              string `json:"user1"`
			User2              string `json:"user2"`
			User3              string `json:"user3"`
			User4              string `json:"user4"`
			User5              string `json:"user5"`
		} `json:"barcodes"`
	} `json:"details"`
	TraceNumbers []struct {
		TraceType    string `json:"traceType"`
		TraceNumber  string `json:"traceNumber"`
		RefQualifier string `json:"refQualifier"`
		User1        string `json:"user1"`
		User2        string `json:"user2"`
		User3        string `json:"user3"`
		User4        string `json:"user4"`
		User5        string `json:"user5"`
		User6        string `json:"user6"`
		User7        string `json:"user7"`
		User8        string `json:"user8"`
		User9        string `json:"user9"`
		User10       string `json:"user10"`
	} `json:"traceNumbers"`
	DangerousGoods []struct {
		UnNumber                 string `json:"unNumber"`
		Class                    string `json:"class"`
		SubClass                 string `json:"subClass"`
		CompatibilityGroup       string `json:"compatibilityGroup"`
		PackingGroup             string `json:"packingGroup"`
		Volume                   int    `json:"volume"`
		VolumeUnits              string `json:"volumeUnits"`
		Mass                     int    `json:"mass"`
		MassUnits                string `json:"massUnits"`
		ExplosiveQuantity        int    `json:"explosiveQuantity"`
		ExplosiveUnits           string `json:"explosiveUnits"`
		Containers               int    `json:"containers"`
		ControlTemp              int    `json:"controlTemp"`
		EmergencyTemp            int    `json:"emergencyTemp"`
		MarinePollutant          string `json:"marinePollutant"`
		RequiredQuantity         string `json:"requiredQuantity"`
		MarinePollutantPrintReq  string `json:"marinePollutantPrintReq"`
		LimitedQuantity          string `json:"limitedQuantity"`
		InhalationHazardZone     string `json:"inhalationHazardZone"`
		InhalationHazardZoneType string `json:"inhalationHazardZoneType"`
		ActualName               string `json:"actualName"`
		ContactName              string `json:"contactName"`
		ContactNumber            string `json:"contactNumber"`
		CountryCode              string `json:"countryCode"`
	} `json:"dangerousGoods"`
	CustomDefs []struct {
		CustomLabel string `json:"customLabel"`
		CustomDefID int    `json:"customDefId"`
		CustomValue string `json:"customValue"`
	} `json:"customDefs"`
	ACharges []struct {
		AChargeCode string `json:"aChargeCode"`
		Quantity    int    `json:"quantity"`
		Rate        int    `json:"rate"`
		Threshold   int    `json:"threshold"`
		Increment   int    `json:"increment"`
		User1       string `json:"user1"`
		User2       string `json:"user2"`
		User3       string `json:"user3"`
		User4       string `json:"user4"`
		User5       string `json:"user5"`
		User6       string `json:"user6"`
		User7       string `json:"user7"`
		User8       string `json:"user8"`
		User9       string `json:"user9"`
		User10      string `json:"user10"`
	} `json:"aCharges"`
	ShipInstructs []struct {
		InstructionID int `json:"instructionId"`
	} `json:"shipInstructs"`
	Notes []struct {
		NoteType string `json:"noteType"`
		TheNote  string `json:"theNote"`
	} `json:"notes"`
	Intermodal struct {
		ContainerCode              string `json:"containerCode"`
		ContainerNumber            string `json:"containerNumber"`
		ContainerSize              string `json:"containerSize"`
		ContainerType              string `json:"containerType"`
		ContainerOwner             string `json:"containerOwner"`
		MovementType               string `json:"movementType"`
		ContainerIso               string `json:"containerIso"`
		Pickup                     string `json:"pickup"`
		Delivery                   string `json:"delivery"`
		BillToCode                 string `json:"billToCode"`
		ChassisTermination         string `json:"chassisTermination"`
		ChassisPickup              string `json:"chassisPickup"`
		EmptyDisposition           string `json:"emptyDisposition"`
		EmptyOrigin                string `json:"emptyOrigin"`
		CardNumber                 string `json:"cardNumber"`
		SealNumber                 string `json:"sealNumber"`
		Vessel                     string `json:"vessel"`
		Voyage                     string `json:"voyage"`
		BookingNumber              string `json:"bookingNumber"`
		ExportDestination          string `json:"exportDestination"`
		EmptyNotifyDate            string `json:"emptyNotifyDate"`
		LoadedDate                 string `json:"loadedDate"`
		CheckDigit                 string `json:"checkDigit"`
		UserDateTime1              string `json:"userDateTime1"`
		UserDateTime2              string `json:"userDateTime2"`
		UserDateTime3              string `json:"userDateTime3"`
		UserDateTime4              string `json:"userDateTime4"`
		UserDateTime5              string `json:"userDateTime5"`
		UserString1                string `json:"userString1"`
		UserString2                string `json:"userString2"`
		UserString3                string `json:"userString3"`
		UserString4                string `json:"userString4"`
		UserString5                string `json:"userString5"`
		UserString6                string `json:"userString6"`
		UserString7                string `json:"userString7"`
		UserString8                string `json:"userString8"`
		UserString9                string `json:"userString9"`
		UserString10               string `json:"userString10"`
		UserInt1                   int    `json:"userInt1"`
		UserInt2                   int    `json:"userInt2"`
		UserInt3                   int    `json:"userInt3"`
		UserInt4                   int    `json:"userInt4"`
		UserInt5                   int    `json:"userInt5"`
		UserDouble1                int    `json:"userDouble1"`
		UserDouble2                int    `json:"userDouble2"`
		UserDouble3                int    `json:"userDouble3"`
		UserDouble4                int    `json:"userDouble4"`
		UserDouble5                int    `json:"userDouble5"`
		UserTrace1                 string `json:"userTrace1"`
		UserTrace2                 string `json:"userTrace2"`
		VesselAta                  string `json:"vesselAta"`
		ChassisBillingDeliveryDate string `json:"chassisBillingDeliveryDate"`
		ChassisBillingDispatchDate string `json:"chassisBillingDispatchDate"`
		ChassisBillingEndDate      string `json:"chassisBillingEndDate"`
		ChassisBillingFBPickupDate string `json:"chassisBillingFBPickupDate"`
		ChassisBillingStartDate    string `json:"chassisBillingStartDate"`
		ChassisBillingTripDate     string `json:"chassisBillingTripDate"`
		ExportVesselCutoff         string `json:"exportVesselCutoff"`
		DeliveryOrderDate          string `json:"deliveryOrderDate"`
		VesselEta                  string `json:"vesselEta"`
		ContainerlastFreeDate      string `json:"containerlastFreeDate"`
		ContainerAvailableDate     string `json:"containerAvailableDate"`
		RailEta                    string `json:"railEta"`
		ContainerReturnDate        string `json:"containerReturnDate"`
		JobNumber                  string `json:"jobNumber"`
		ShipmentNumber             string `json:"shipmentNumber"`
		Supplier                   string `json:"supplier"`
		WoReference                string `json:"woReference"`
		Bol                        string `json:"bol"`
		CustomerOrder              string `json:"customerOrder"`
		Deramp                     string `json:"deramp"`
		PickupNumber               string `json:"pickupNumber"`
		PortOfExit                 string `json:"portOfExit"`
		ContainerPoolClient        string `json:"containerPoolClient"`
		InBond                     string `json:"inBond"`
		OblHold                    string `json:"oblHold"`
		AgricultureHold            string `json:"agricultureHold"`
	} `json:"intermodal"`
	Pods []struct {
		TxType             string `json:"txType"`
		Description        string `json:"description"`
		DriverCounted      string `json:"driverCounted"`
		ReceiverSigned     string `json:"receiverSigned"`
		SignedClean        string `json:"signedClean"`
		CopyReceived       string `json:"copyReceived"`
		TraceNo            string `json:"traceNo"`
		SignedBy           string `json:"signedBy"`
		SignedOn           string `json:"signedOn"`
		Driver             string `json:"driver"`
		DriverExternal     string `json:"driverExternal"`
		Interliner         string `json:"interliner"`
		InterlinerExternal string `json:"interlinerExternal"`
	} `json:"pods"`
	Status            string `json:"status"`
	StatusDescription string `json:"statusDescription"`
	PickUpBy          string `json:"pickUpBy"`
	PickUpByEnd       string `json:"pickUpByEnd"`
	PickUpApptReq     string `json:"pickUpApptReq"`
	PickUpAppt        string `json:"pickUpAppt"`
	PickUpApptMade    string `json:"pickUpApptMade"`
	DeliverBy         string `json:"deliverBy"`
	DeliverByEnd      string `json:"deliverByEnd"`
	DeliveryAppt      string `json:"deliveryAppt"`
	DeliveryApptMade  string `json:"deliveryApptMade"`
	DeliveryApptReq   string `json:"deliveryApptReq"`
	ServiceLevel      string `json:"serviceLevel"`
	StartZone         string `json:"startZone"`
	EndZone           string `json:"endZone"`
	SiteID            string `json:"siteId"`
	OpCode            string `json:"opCode"`
	BillNumber        string `json:"billNumber"`
	BillTo            string `json:"billTo"`
	BillToCode        string `json:"billToCode"`
	DeclaredValue     int    `json:"declaredValue"`
	CurrencyCode      string `json:"currencyCode"`
	PickupDriver      string `json:"pickupDriver"`
	NoCharge          string `json:"noCharge"`
	Interliners       []struct {
		MovementType string `json:"movementType"`
		InterlinerID string `json:"interlinerId"`
		FromZone     string `json:"fromZone"`
		ToZone       string `json:"toZone"`
		Currency     string `json:"currency"`
		Probill      string `json:"probill"`
		ChargesType  string `json:"chargesType"`
	} `json:"interliners"`
	IsCsa                  string `json:"isCsa"`
	IsTarped               string `json:"isTarped"`
	IsFast                 string `json:"isFast"`
	IsExclusive            string `json:"isExclusive"`
	ExclusiveOverride      string `json:"exclusiveOverride"`
	CodAmount              int    `json:"codAmount"`
	DeliveryDriver1        string `json:"deliveryDriver1"`
	DeliveryDriver2        string `json:"deliveryDriver2"`
	DeliveryPowerUnit1     string `json:"deliveryPowerUnit1"`
	DeliveryPowerUnit2     string `json:"deliveryPowerUnit2"`
	DeliveryTrailer1       string `json:"deliveryTrailer1"`
	DeliveryTrailer2       string `json:"deliveryTrailer2"`
	DestinationSpotTrailer string `json:"destinationSpotTrailer"`
	OriginSpotTrailer      string `json:"originSpotTrailer"`
	PickupDriver2          string `json:"pickupDriver2"`
	PickupPowerUnit1       string `json:"pickupPowerUnit1"`
	PickupPowerUnit2       string `json:"pickupPowerUnit2"`
	PickupTrailer1         string `json:"pickupTrailer1"`
	PickupTrailer2         string `json:"pickupTrailer2"`
	IsApproved             string `json:"isApproved"`
}

type PostLoadRequest struct {
	Orders []PostOrUpdateOrderRequest `json:"orders"`
}

type PostLoadResponse struct {
	Orders []OrderResp `json:"orders"`
}

type GetCheckCallsResponse struct {
	Offset        int             `json:"offset"`
	Limit         int             `json:"limit"`
	Count         int             `json:"count"`
	StatusHistory []CheckCallResp `json:"statusHistory"`
}

type UpdateCheckCallRequest struct {
	StatusCode          string `json:"statusCode"`
	Reason              string `json:"reason"`
	Zone                string `json:"zone"`
	Comment             string `json:"comment"`
	Time                string `json:"time"`
	Latitude            int    `json:"latitude"`
	Longitude           int    `json:"longitude"`
	LocationDescription string `json:"locationDescription"`
	ZoneIDLookup        string `json:"zoneIdLookup"`
}

type CheckCallResp struct {
	OrderID          int    `json:"orderId"`
	Changed          string `json:"changed"`
	StatusCode       string `json:"statusCode"`
	StatComment      string `json:"statComment"`
	UpdatedBy        string `json:"updatedBy"`
	TripNumber       int    `json:"tripNumber"`
	Zone             string `json:"zone"`
	InsDate          string `json:"insDate"`
	Reason           string `json:"reason"`
	User1            string `json:"user1"`
	User2            string `json:"user2"`
	User3            string `json:"user3"`
	CurrentLocClient string `json:"currentLocClient"`
	AadTripStatus    string `json:"aadTripStatus"`
	AadArDp          string `json:"aadArDp"`
	SignatureID      int    `json:"signatureId"`
	OperationID      int    `json:"operationId"`
}
