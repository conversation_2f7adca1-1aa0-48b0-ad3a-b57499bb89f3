package aljex

import "github.com/drumkitai/drumkit/common/models"

const PROIDType = "pro"
const CustomerRefIDType = "customerRef"

// maps specific transport types to Aljex equipment codes
var transportTypeToAljexCode = map[string]string{
	"V":           "V",
	"VA":          "VA",
	"VC":          "VC",
	"VLG":         "VLG",
	"VP":          "VP",
	"VR":          "VR",
	"R":           "R",
	"F":           "F",
	"FA":          "FA",
	"FOD":         "FOD",
	"FOW":         "FOW",
	"FSD":         "FSD",
	"FSDC":        "FSDC",
	"HS":          "HS",
	"HSDT":        "HSDT",
	"HSOD":        "HSOD",
	"HSWR":        "HSWR",
	"HSWW":        "HSWW",
	"STR":         "STR",
	"SPRI":        "SPRI",
	"STEP":        "STEP",
	"SDC":         "SDC",
	"SDOD":        "SDOD",
	"SDR":         "SDR",
	"STRD":        "STRD",
	"STRE":        "STRE",
	"STRF":        "STRF",
	"LB":          "LB",
	"RGN":         "RGN",
	"RGNW":        "RGNW",
	"RGOD":        "RGOD",
	"RGOW":        "RGOW",
	"DD":          "DD",
	"LAND":        "LAND",
	"CON":         "CON",
	"PO":          "PO",
	"POOD":        "POOD",
	"POOW":        "POOW",
	"PH":          "PH",
	"HOP":         "HOP",
	"DRVR":        "DRVR",
	"WRE":         "WRE",
	"ODS":         "ODS",
	"PARF":        "PARF",
	"TORD":        "TORD",
	"vanliftgate": "VLG",
	"vanlift":     "VLG",
	"liftgate":    "VLG",
}

// maps specific transport types to general TransportTypeEnum categories
var equipmentToTransportTypeEnum = map[string]models.TransportType{
	// Value codes
	"rgod": models.FlatbedTransportType,
	"con":  models.FlatbedTransportType,
	"dd":   models.FlatbedTransportType,
	"drvr": models.BoxTruckTransportType,
	"f":    models.FlatbedTransportType,
	"fa":   models.FlatbedTransportType,
	"fod":  models.FlatbedTransportType,
	"fow":  models.FlatbedTransportType,
	"fsd":  models.FlatbedTransportType,
	"fsdc": models.FlatbedTransportType,
	"hop":  models.BoxTruckTransportType,
	"hs":   models.HotShotTransportType,
	"hsdt": models.HotShotTransportType,
	"hsod": models.HotShotTransportType,
	"hswr": models.HotShotTransportType,
	"hsww": models.HotShotTransportType,
	"land": models.FlatbedTransportType,
	"lb":   models.FlatbedTransportType,
	"ods":  models.BoxTruckTransportType,
	"parf": models.BoxTruckTransportType,
	"ph":   models.BoxTruckTransportType,
	"po":   models.BoxTruckTransportType,
	"pood": models.BoxTruckTransportType,
	"poow": models.BoxTruckTransportType,
	"r":    models.ReeferTransportType,
	"rgn":  models.FlatbedTransportType,
	"rgnw": models.FlatbedTransportType,
	"rgow": models.FlatbedTransportType,
	"sdc":  models.FlatbedTransportType,
	"sdod": models.FlatbedTransportType,
	"sdr":  models.FlatbedTransportType,
	"spri": models.BoxTruckTransportType,
	"step": models.FlatbedTransportType,
	"str":  models.BoxTruckTransportType,
	"strd": models.FlatbedTransportType,
	"stre": models.FlatbedTransportType,
	"strf": models.FlatbedTransportType,
	"tord": models.BoxTruckTransportType,
	"v":    models.VanTransportType,
	"va":   models.VanTransportType,
	"vc":   models.VanTransportType,
	"vlg":  models.VanTransportType,
	"vp":   models.VanTransportType,
	"vr":   models.VanTransportType,
	"wre":  models.SpecialTransportType,

	// Labels (lowercase)
	"rgn od":                   models.FlatbedTransportType,
	"conestoga":                models.FlatbedTransportType,
	"double drop":              models.FlatbedTransportType,
	"drive away":               models.BoxTruckTransportType,
	"flatbed":                  models.FlatbedTransportType,
	"air-ride flatbed":         models.FlatbedTransportType,
	"flatbed od":               models.FlatbedTransportType,
	"flatbed ow":               models.FlatbedTransportType,
	"flatbed - stepdeck":       models.FlatbedTransportType,
	"flatbed -stepdeck - con.": models.FlatbedTransportType,
	"hopper":                   models.BoxTruckTransportType,
	"hotshot":                  models.HotShotTransportType,
	"hs pullout or dovetail":   models.HotShotTransportType,
	"hotshot od":               models.HotShotTransportType,
	"hotshot wramps":           models.HotShotTransportType,
	"hotshot w winch":          models.HotShotTransportType,
	"landoll":                  models.FlatbedTransportType,
	"lowboy":                   models.FlatbedTransportType,
	"open deck + service":      models.BoxTruckTransportType,
	"partial - open deck":      models.BoxTruckTransportType,
	"power only pintle hitch":  models.BoxTruckTransportType,
	"power only":               models.BoxTruckTransportType,
	"power only od":            models.BoxTruckTransportType,
	"power only ow":            models.BoxTruckTransportType,
	"reefer":                   models.ReeferTransportType,
	"rgn w winch":              models.FlatbedTransportType,
	"rgn ow":                   models.FlatbedTransportType,
	"stepdeck conestoga":       models.FlatbedTransportType,
	"stepdeck od":              models.FlatbedTransportType,
	"stepdeck wramps":          models.FlatbedTransportType,
	"sprinter":                 models.SprinterTransportType,
	"stepdeck":                 models.FlatbedTransportType,
	"straight truck":           models.BoxTruckTransportType,
	"stretch - stepdeck":       models.FlatbedTransportType,
	"stretch - rgn":            models.FlatbedTransportType,
	"stretch - flatbed":        models.FlatbedTransportType,
	"truck ordered not used":   models.SpecialTransportType,
	"van":                      models.VanTransportType,
	"air-ride van":             models.VanTransportType,
	"curtain van":              models.VanTransportType,
	"van liftgate":             models.VanTransportType,
	"van partial":              models.VanTransportType,
	"van or reefer":            models.VanTransportType,
	"wrecker":                  models.SpecialTransportType,
	"drayage":                  "",
	"flatbed w/ tarps":         models.FlatbedTransportType,
	"ocean":                    "",
	"reefer team":              models.ReeferTransportType,
	"step or drop deck":        models.FlatbedTransportType,
	"straight box truck":       models.BoxTruckTransportType,
	"truckload":                models.VanTransportType,
	"van drop trailer":         models.VanTransportType,
	"van team":                 models.VanTransportType,
	"less than tl":             "", // Technically a mode
	"box truck":                models.BoxTruckTransportType,
	"van hazmat":               models.VanTransportType,
	"van sprinter":             models.SprinterTransportType,
	"flatbed w/ sides":         models.FlatbedTransportType,
}
