package aljex

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/PuerkitoBio/goquery"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func TestGetOperators(t *testing.T) {
	client := Aljex{tms: models.Integration{Model: gorm.Model{ID: 4}}}

	htmlString := `
	<select id="carrep">
		<option value="1">Option 1</option>
		<option value="2">Option 2</option>
		<option value="3">Option 3</option>
	</select>
	`

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlString))
	require.NoError(t, err)

	res := client.parseUsers(doc)
	expected := []models.TMSUser{
		{ExternalTMSID: "1", Username: "1", TMSID: 4},
		{ExternalTMSID: "2", Username: "2", TMSID: 4},
		{ExternalTMSID: "3", Username: "3", TMSID: 4},
	}
	assert.Equal(t, expected, res)
}

func TestLiveGetOperators(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetOperators: run with LIVE_TEST=true to enable")
		return
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	require.NoError(t, loadEnv())
	require.NotEmpty(t, env)

	integration := aljexTestClient(ctx, env.AppID, env.Password, env.TwoFactorSecret)
	client, err := New(ctx, integration)
	require.NoError(t, err)

	operators, err := client.GetUsers(ctx)
	require.NoError(t, err)

	assert.NotEmpty(t, operators)
	log.Info(ctx, "operators found", zap.Int("count", len(operators)))
	assert.NotContains(t, operators, "")
}
