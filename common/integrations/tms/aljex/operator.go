package aljex

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Assigns an operator from the broker to a load.

// NOTE: While aljex.UpdateLoad() would update the field in Aljex, it does not trigger
// the email to the operator; we must use this specific endpoint to assign & email an operator.
// In contrast, to remove an operator, the Drumkit API can simply call aljex.UpdateLoad()
// with the Operator field set to "".
func (a *Aljex) AssignOperator(
	ctx context.Context,
	freightTrackingID,
	operator string,
) (res models.Load, attrs models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), attribute.String("freight_tracking_id", freightTrackingID))
	ctx, metaSpan := otel.StartSpan(ctx, "AssignOperatorAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.AssignOperator")

	formData := url.Values{}
	formData.Set("pro", freightTrackingID)
	formData.Set("rep", operator)
	formData.Set("prcnam", "wrtrep")
	formData.Set("options", "price")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)

	resp, _, err := a.get(ctx, a.getAPIURL()+"/?"+formData.Encode(), s3backup.TypeUsers)
	if err != nil {
		return res, attrs, fmt.Errorf("failed to assign Aljex operator: %w", err)
	}

	// Upon success, request returns 200 with empty response body
	if len(resp) > 0 {
		return res, attrs, fmt.Errorf("GET request to update operator failed: %s", string(resp))
	}

	return a.GetLoad(ctx, freightTrackingID)
}

// In order to update the operator from one person to another, it's not enough to just call `AssignOperator`.
// We must first remove the old one, then update with the new one.
func (a *Aljex) UpdateOperator(
	ctx context.Context,
	freightTrackingID,
	newOperator string,
) (_ models.Load, _ models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), attribute.String("freight_tracking_id", freightTrackingID))
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateOperatorAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.UpdateOperator")

	curLoad, attrs, err := a.GetLoad(ctx, freightTrackingID)
	if err != nil {
		return curLoad, attrs, err
	}

	emptyOperatorLoad := curLoad
	emptyOperatorLoad.Operator = ""

	removeRes, attrs, err := a.UpdateLoad(ctx, &curLoad, &emptyOperatorLoad)
	if err != nil {
		return removeRes, attrs, fmt.Errorf("error removing current operator: %w", err)
	}

	// Depending on the PRO's status, the operator field is not editable. Observed cases
	// include when the PRO is on HOLD, COVERED, or DELIVERED but this list is not necessarily exhaustive.
	// parseLoadOperator checks for this nuance proactively but we double-check retroactively as well here
	if removeRes.Operator != "" {
		return removeRes, attrs, fmt.Errorf("current operator '%s' is not empty as expected", removeRes.Operator)
	}

	return a.AssignOperator(ctx, freightTrackingID, newOperator)
}

// Get organization's list of operators
func (a *Aljex) GetUsers(ctx context.Context) (users []models.TMSUser, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetUsersAljex", otel.IntegrationAttrs(a.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetUsers")

	formData := url.Values{}

	// "prorecno" aka  PRO Record # is required. To avoid multiple GETs and speed up this lookup, hardcode 0.
	// TODO: does hard-coding 0 work for all Aljex accounts?
	formData.Set("prorecno", "0")
	formData.Set("prcnam", "ssweb")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)
	formData.Set("sys", "3a")
	formData.Set("prcnam", "ssweb")
	formData.Set("type", "search")

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeUsers)
	if err != nil {
		return users, fmt.Errorf("failed to get Aljex users: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return users, fmt.Errorf("error creating doc: %w", err)
	}

	users = a.parseUsers(doc)
	// We expect at least 1 operator. HTTP might return 200 but HTML is different than expected,
	// which would be why we're unable to parse users so we return an error.
	if len(users) == 0 {
		return users, errors.New("no users found")
	}

	return users, nil

}

func (a *Aljex) parseUsers(doc *goquery.Document) (operators []models.TMSUser) {
	options := doc.Find("select#carrep option")

	options.Each(func(_ int, option *goquery.Selection) {
		if name := option.AttrOr("value", ""); strings.TrimSpace(name) != "" {
			operators = append(operators, models.TMSUser{
				Username:      name,
				ExternalTMSID: name,
				TMSID:         a.tms.ID})
		}
	})

	return operators
}
