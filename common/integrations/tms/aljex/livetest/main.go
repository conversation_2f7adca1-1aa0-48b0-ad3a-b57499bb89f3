package main

import (
	"context"
	"fmt"
	"time"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type envVars struct {
	AppID    string `envconfig:"NFI_APP_ID" required:"true"`
	Password string `envconfig:"NFI_PASS" required:"true"`
	PRO      string `envconfig:"PRO" required:"true"`
	Update   bool   `envconfig:"UPDATE" default:"false"`
	// Config allows assigning/updating operator without updating rest of PRO
	AssignOperator bool `envconfig:"ASSIGN_OPERATOR" default:"false"`
	UpdateOperator bool `envconfig:"UPDATE_OPERATOR" default:"false"`
}

var (
	env envVars
)

func main() {
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	if err := loadEnv(ctx); err != nil {
		log.Fatal(ctx, "loadEnv failed", zap.Error(err))
	}

	log.Info(ctx, "parsed env", zap.Any("env", env))

	ctx = log.With(ctx, zap.String("pro", env.PRO))

	startTime := time.Now()

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, env.Password, nil)
	if err != nil {
		log.Fatal(ctx, "failed to encrypt test aljex client password", zap.Error(err))
	}

	integration := models.Integration{
		Name:              models.Aljex,
		Type:              models.TMS,
		ServiceID:         1,
		AppID:             env.AppID,
		EncryptedPassword: []byte(encryptedPassword),
	}

	client, err := aljex.New(ctx, integration)
	if err != nil {
		log.Fatal(ctx, "failed to build aljex client", zap.Error(err))
	}

	testPROs := client.GetTestLoads()
	var curLoad = &models.Load{}
	curLoad.FreightTrackingID = env.PRO

	//nolint:staticcheck
	if !(env.Update || env.AssignOperator || env.UpdateOperator) || !testPROs[env.PRO] {
		log.Info(ctx, "running in read-only mode")
	}

	// NOTE: that because this is a POST request, empty values will reset the fields.
	if env.Update && testPROs[env.PRO] {
		// WARNING: never call UpdateLoad for real data
		log.Info(ctx, "calling UpdateLoad for test PRO")

		if _, _, err := client.UpdateLoad(ctx, curLoad, &sampleUpdatedLoad); err != nil {
			log.Error(ctx, "UpdateLoad failed", zap.Error(err))
		} else {
			log.Info(ctx, "UpdateLoad completed", zap.Duration("duration", time.Since(startTime)))
		}
	}

	if env.AssignOperator && testPROs[env.PRO] {
		// WARNING: never call AssignOperator for real data
		log.Info(ctx, "calling AssignOperator for test PRO")
		if _, _, err := client.AssignOperator(ctx, env.PRO, "AXLEAPI"); err != nil {
			log.Error(ctx, "AssignOperator failed", zap.Error(err))
		} else {
			log.Info(ctx, "AssignOperator completed", zap.Duration("duration", time.Since(startTime)))
		}
	}

	// WARNING: never call UpdateOperator for real data
	if env.UpdateOperator && testPROs[env.PRO] {
		log.Info(ctx, "calling UpdateOperator for test PRO")
		if _, _, err := client.UpdateOperator(ctx, env.PRO, "AXLEAPI"); err != nil {
			log.Error(ctx, "UpdateOperator failed", zap.Error(err))
		} else {
			log.Info(ctx, "UpdateOperator completed", zap.Duration("duration", time.Since(startTime)))
		}
	}

	load, _, err := client.GetLoad(ctx, env.PRO)
	if err != nil {
		log.Error(ctx, "GetLoad failed", zap.Error(err))
	} else {
		log.Info(ctx, "GetLoad completed",
			zap.Any("load", load), zap.Duration("duration", time.Since(startTime)))
	}
}

func loadEnv(ctx context.Context) error {
	if err := godotenv.Load(); err != nil {
		log.Warn(ctx, "unable to load .env file", zap.Error(err))
	}

	if err := envconfig.Process("", &env); err != nil {
		return fmt.Errorf("failed to parse env vars: %w", err)
	}

	return nil
}
