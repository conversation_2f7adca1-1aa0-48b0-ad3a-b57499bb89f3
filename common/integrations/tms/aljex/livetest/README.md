# Live Test Aljex Get/Update

This script is helpful to directly test the Aljex integration - AWS and RDS are not required.

Create a `.env` file in the same directory:

```shell
# Lookup these values from beacon-api secret
NFI_APP_ID=...
NFI_PASS=...

# See main.go for the set of test PROs that can be modified
PRO=2080915
```

Then run the script: `go run .`

By default, this will perform the read-only `GetLoad` operation.
When `UPDATE=true` *and* it's a test PRO, it will update with fixed sample data (see sample.go), and then call `GetLoad` again.
When `ASSIGN_OPERATOR=true`, it will assign the operator to our demo user, `AXLEAPI`.
When `UPDATE_OPERATOR=true`, it will remove then re-assign the operator.
