<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>All Loads - Aljex Vision</title>

<meta name="tablesorter">

<script type="text/javascript" src="/common/import.min.js"></script>
<script type="text/javascript" src="/common/v2_loader.js"></script>

<!--
***********************************************
PAGE-SPECIFIC CSS GOES HERE
***********************************************
-->
<style>
.box {
background:#FFFFFF;
border:5px solid #427BD6;
border-radius:8px;
}

th.header {
cursor: pointer;
}

</style>

<!--
***********************************************
PAGE-SPECIFIC JAVASCRIPT FUNCTIONS GO HERE
***********************************************
-->

<script language=JavaScript>

function page_init()
{
	setMultiCurTypes();

// make table rows easier to read
colorRows();

// sort table
$('#loads').tablesorter();

//assign the sortEnd event
$("table").bind("sortEnd", function () {
colorRows();
});

// make table rows easier to view
function colorRows() {
$('#loads tr:even').css("background-color", "#EFEFEF");
$('#loads tr:odd').css("background-color", "#FFFFFF");
}
}

// **************************
// job# 17222 - 021215 gc
// display tag page for pro #
// **************************
function getLoad(prono) {

if (prono) {
document.web.pro.value = prono;
document.web.prcnam.value = 'retin';
document.web.target = '_form';
document.web.submit();
}
}

// **************************
// job# 17222 - 021215 gc
// display customer pg
// **************************
function getCust(custid) {

if (custid) {
document.cust.custid.value = custid;
document.cust.type.value = 'update';
document.cust.target = '_form';
document.cust.submit();
}
}

// **************************
// job# 17222 - 021215 gc
// display carrier pg
// **************************
function getCarr(carrid) {

if (carrid) {
document.carr.carid.value = carrid;
document.carr.type.value = 'update';
document.carr.target = '_form';
document.carr.submit();
}
}

// **********************
// job# 17222 - 021215 gc
// repeat the load
// **********************
function doRepeat(prono) {

if (prono) {
document.web.pro.value = prono;
document.web.prcnam.value = 't3atag';
document.web.type.value = 'repeat';
document.web.target = '_form';
document.web.submit();
}
}


// *****************************
// job# 17222 - 021215 gc
// go to details page (trucking)
// *****************************
function getDetail(prono, ldtype) {

if (prono) {
if ('3a' + 'x' == 'tx' || ldtype == "T") {
document.web.pro.value = prono;
document.web.prcnam.value = 'triplegs';
document.web.type.value = 'lookup';
document.web.target = '_form';
document.web.submit();
} else {
alert('Details Option Used For Trucking Loads Only');
return false;
}
}
}


function getcar(id)
{
	if(id > 0)
	{o
	document.car.carid.value = id;
	document.car.submit();
	return true;
	}
}

function confDelete(fname)
{
//	alert(fname.login.value)
var answer
answer = prompt("OK to Delete Item Code: " + fname.itemcode.value + "?","Yes")
if (answer == "Yes")
{
fname.type.value="delete";
						fname.submit();
						return true;
}
else
{
return false;
						}
}

function doadd()
{
	document.form.sku.value = document.one.sku.value;
	document.form.desc.value = document.one.desc.value;
	alert(document.one.desc.value + ' ' + document.form.desc.value);
	document.form.submit();
}

function dooldx(num,ltype)
{
	if('3a' +'x' == 'tx' || ltype=="T")
	{
	frm = eval("document.oldx" + num);
	frm.submit();
	return true;
	}
else
{
alert("Details Only Works for Trucking Loads");
}
}

function setMultiCurTypes() {
if( $('#multi_currency').val() !== 'Y') {
$('.carrier_currency').hide();
$('.customer_currency').hide();
}
}

</script>

<!--
***********************************************
END OF PAGE-SPECIFIC JAVASCRIPT FUNCTIONS
***********************************************
-->

</head>

<!--- html template: vresults3.htm --->
<body >
<div align="center">

<input type=hidden id=multi_currency value="Y">

<span id="page_header"></span>
<input type=hidden id=user_perms value="">
<input id=goctrl700 type=hidden value="Y">

<p>

<!-- END OPENING bkb styling #############################################-->

<!-- BUTTONS -->

<table border=0 align=center><tr>
<td align="center"><input type="button" value="Home" onclick="dohome();"></td>
<td align="center"><input type="button" value="Back" onclick="history.go(-1);"></td>
</tr></table>
<p>

<!-- END BUTTONS -->

<table border="0" cellpadding="0" cellspacing="0">
<tr><td>
<table class="tab" bordercolorlight="#CCC000" bordercolordark="#CCC000" bgcolor="#ffffff" border="2" bordercolor="#CCC000" cellpadding="2" cellspacing="1" width="570">


<tr><!-- %%%%%%%%%%%% -->
<td class="headrow stitle" style="text-align:right;">&nbsp;
<div style="float: left; text-align: left; font-size:12px; font-weight: bold;">Click Column Sort</div>
<div style="float: right; text-align: right">All Loads</div>
</td>
</tr><!-- %%%%%%%%%%%% -->



<tr><td align=center>
<table id="loads" class="tablesorter" cellpadding="2" cellspacing="0" border="1" class="box" style="border: 1px solid black; padding: 3px; border-collapse: collapse; line-height: 16px;">

<thead>
<tr>
<th align="center" bgcolor="#cfdfef"><b>Pro #</b></th>
<th align="center" bgcolor="#cfdfef"><b>Status</b></th>
<th align="center" bgcolor="#cfdfef"><b>Customer</b></th>
<th align="center" bgcolor="#cfdfef"><b>Cust $ <br> Truck $</b></th>
<th align="center" bgcolor="#cfdfef"><b>Ref #'s</b></th>
<th align="center" bgcolor="#cfdfef"><b>Origin</b></th>
<th align="center" bgcolor="#cfdfef"><b>Destination</b></th>
<th align="center" bgcolor="#cfdfef"><b>P/S</b></th>
<th align="center" bgcolor="#cfdfef"><b>Pieces <br>Weight</b></th>	<!-- job 31348 jh 012518 -->
<th align="center" bgcolor="#cfdfef"><b>Footage </b></th>		<!-- job 31342 jh 020118 -->
<th align="center" bgcolor="#cfdfef"><b>Miles</b></th>  		<!-- job 31348 jh 012518 -->
<th align="center" bgcolor="#cfdfef"><b>Ready <br> Date</b></th>
<th align="center" bgcolor="#cfdfef"><b>PU <br>Appt</b></th>
<th align="center" bgcolor="#cfdfef"><b>Del <br>Appt</b></th>
<th align="center" bgcolor="#cfdfef"><b>Del <br>Date</b></th>
<th align="center" bgcolor="#cfdfef"><b>Carrier</b></th>
<th align="center" bgcolor="#cfdfef"><b>Type</b></th>
<th align="center" bgcolor="#cfdfef"><b>Mode</b></th>
<th align="center" bgcolor="#cfdfef"><b>Disp</b></th>
<th align="center" bgcolor="#cfdfef"><b>Office</b></th>
<!-- jh 022215 we dont need this on the page
<th align="center" bgcolor="#cfdfef"><b>Svc Rep</b></th>
-->
<th align="center" bgcolor="#cfdfef"><b>Actions</b></th>
</tr>
</thead>
<tbody>
<form name=show method=post action=/route.php>
<tr>
<td align="center">&nbsp;<a href="#" onclick="getLoad('2153447');">2153447</a>&nbsp;</td>
<td align="center">DELIVERED</td>
<td align="left"><nobr><a href="#" onclick="getCust('143706');">ABC CUSTOMER</a>&nbsp;</nobr></td>
<td align="right"><nobr>
$600.&nbsp;<span class="customer_currency" data-id="143706"></span><br></span>
$385.00&nbsp;<span class="carrier_currency" data-id="300009748"></nobr></td>
<td align="left"><nobr> 80551161 <br>&nbsp; 4008328260 <br>&nbsp; 4008328260 <br>&nbsp; 39.3408 <br>&nbsp; -81.4506 <br>&nbsp; N <br>&nbsp; 4008328260 <br>&nbsp; 2404114437 <br>&nbsp; 23.65 <br>&nbsp; 533.5 <br>&nbsp; 4008328260 <br>&nbsp; 400001111222           </td>
<td align="left"><nobr>MARTINS<br>WV</nobr></td>
<td align="left"><nobr>PLANT ORTK <br>WV PARKERSBURG</nobr></td>
<td align="center">&nbsp;1/1 </td>
<td align="center"><nobr>35560</nobr><br>40309</td>
<td align="center">&nbsp;53 </td>   <!-- job 31342 jh 020118 -->
<td align="center">&nbsp;55 </td>   <!-- job 31348 jh 012518 -->
<td align="center">&nbsp;06/11/24 </td>
<td align="center">&nbsp;06/11/24 </td>
<td align="center">&nbsp;06/11/24 </td>
<td align="center">&nbsp;06/11/24 </td>
<td align="left"><nobr><a href="#" onclick="getCarr('300009748');">FAST TRUCKERS</a>&nbsp;</nobr><br>BILL<br>(111) 222-3344</td>
<td align="center">V </td>
<td align="center">&nbsp;BROKERAGE </td>
<td align="center">&nbsp;JTUCK </td>
<td align="center">&nbsp;TX </td>
<!-- jh 022215 we dont need this on the page
<td align="center">&nbsp;COVES </td>
-->
<td align="center"><nobr>&nbsp;
<!-- jh 022315 pro # already does this
<a href="#" onclick="getLoad('2153447');"><img src="/images/icons/report.png" title="View" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="getLoad('2153447');">View</a>&nbsp;&nbsp;
-->
<a href="#" onclick="doRepeat('2153447');"><img src="/images/icons/folder_table.png" title="Repeat" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="doRepeat('2153447');">Repeat</a>&nbsp;&nbsp;
<br>
<!-- jh 022415 they can go into the shipment trucking is small % we dont need this
<a href="#" onclick="getDetail('2153447','B');"><img src="/images/icons/query.png" title="Details" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="getDetail('2153447','B');">Details</a>&nbsp;
-->
</nobr>
</td>
</tr>
</form>
</tbody>


<tr>
<td colspan="27" align="center">&nbsp;<br><center><big>From Date: 05/01/24 To Date: 07/01/24<br>&nbsp;
</td>
</tr>

<tfoot>

</tfoot></table></td>
</tr>
<tr>
<td align="middle" width="100%">
<table class="tab" bordercolorlight="white" bordercolordark="white" bgcolor="#ffffff" border="0" bordercolor="#000000" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td colspan="5" align="middle" class="headrow">&nbsp;</td>
</tr>
<tr>
<td align="center"> <table>
<tr>
<td align="center"><input type="button" value="Home" onclick="dohome();"></td>
<td align="center"><input type="button" value="Back" onclick="history.go(-1);"></td>
</tr></table>
</td></tr></table>
</td>
</tr>


</table>
</td>
</tr>
</table>


</div>

</body>
</html>


