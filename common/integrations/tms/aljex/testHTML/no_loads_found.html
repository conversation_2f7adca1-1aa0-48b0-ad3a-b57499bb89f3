<table border=0 align=center>
    <tr>
        <td align="center"><input type="button" value="Home" onclick="dohome();"></td>
        <td align="center"><input type="button" value="Back" onclick="history.go(-1);"></td>
    </tr>
</table>
<p>

    <!-- END BUTTONS -->

<table border="0" cellpadding="0" cellspacing="0">
    <tr>
        <td>
            <table class="tab" bordercolorlight="#CCC000" bordercolordark="#CCC000" bgcolor="#ffffff" border="2"
                bordercolor="#CCC000" cellpadding="2" cellspacing="1" width="570">


                <tr>
                    <!-- %%%%%%%%%%%% -->
                    <td class="headrow stitle" style="text-align:right;">&nbsp;
                        <div style="float: left; text-align: left; font-size:12px; font-weight: bold;">Click
                            Column Sort</div>
                        <div style="float: right; text-align: right">All Loads</div>
                    </td>
                </tr><!-- %%%%%%%%%%%% -->



                <tr>
                    <td align=center>
                        <table id="loads" class="tablesorter" cellpadding="2" cellspacing="0" border="1"
                            class="box"
                            style="border: 1px solid black; padding: 3px; border-collapse: collapse; line-height: 16px;">

                            <thead>
                                <tr>
                                    <th align="center" bgcolor="#cfdfef"><b>Pro #</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Status</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Customer</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Cust $ <br> Truck $</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Ref #'s</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Origin</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Destination</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>P/S</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Pieces <br>Weight</b></th>
                                    <!-- job 31348 jh 012518 -->
                                    <th align="center" bgcolor="#cfdfef"><b>Footage </b></th>
                                    <!-- job 31342 jh 020118 -->
                                    <th align="center" bgcolor="#cfdfef"><b>Miles</b></th>
                                    <!-- job 31348 jh 012518 -->
                                    <th align="center" bgcolor="#cfdfef"><b>Ready <br> Date</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>PU <br>Appt</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Del <br>Appt</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Del <br>Date</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Carrier</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Type</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Mode</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Disp</b></th>
                                    <th align="center" bgcolor="#cfdfef"><b>Office</b></th>
                                    <!-- jh 022215 we dont need this on the page
<th align="center" bgcolor="#cfdfef"><b>Svc Rep</b></th>
-->
                                    <th align="center" bgcolor="#cfdfef"><b>Actions</b></th>
                                </tr>
                            </thead>
                            <tbody>
                                <form name=show method=post action=/route.php>
                                    <tr>
                                        <td align="center">&nbsp;<a href="#" onclick="getLoad('');"></a>&nbsp;
                                        </td>
                                        <td align="center"></td>
                                        <td align="left">
                                            <nobr><a href="#" onclick="getCust('');"></a>&nbsp;</nobr>
                                        </td>
                                        <td align="right">
                                            <nobr>
                                                &nbsp;<span class="customer_currency" data-id=""></span><br></span>
                                                &nbsp;<span class="carrier_currency" data-id=""></nobr></td>
<td align="left"><nobr> </td>
<td align="left"><nobr> <br> </nobr></td>
<td align="left"><nobr> <br> </nobr></td>
<td align="center">&nbsp;/ </td>
<td align="center"><nobr></nobr><br></td>
<td align="center">&nbsp; </td>   <!-- job 31342 jh 020118 -->
<td align="center">&nbsp; </td>   <!-- job 31348 jh 012518 -->
<td align="center">&nbsp; </td>
<td align="center">&nbsp; </td>
<td align="center">&nbsp; </td>
<td align="center">&nbsp; </td>
<td align="left"><nobr><a href="#" onclick="getCarr('');"></a>&nbsp;</nobr><br><br></td>
<td align="center"> </td>
<td align="center">&nbsp; </td>
<td align="center">&nbsp; </td>
<td align="center">&nbsp; </td>
<!-- jh 022215 we dont need this on the page
<td align="center">&nbsp; </td>
-->
<td align="center"><nobr>&nbsp;
<!-- jh 022315 pro # already does this
<a href="#" onclick="getLoad('');"><img src="/images/icons/report.png" title="View" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="getLoad('');">View</a>&nbsp;&nbsp;
-->
<a href="#" onclick="doRepeat('');"><img src="/images/icons/folder_table.png" title="Repeat" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="doRepeat('');">Repeat</a>&nbsp;&nbsp;
<br>
<!-- jh 022415 they can go into the shipment trucking is small % we dont need this
<a href="#" onclick="getDetail('','');"><img src="/images/icons/query.png" title="Details" style="vertical-align:middle;" /></a>
&nbsp;<a href="#" onclick="getDetail('','');">Details</a>&nbsp;
-->
</nobr>
</td>
</tr>


</tbody>


<tr>
<td colspan="27" align="center">&nbsp;<br><center><big>No Loads Found: 05/01/24 07/01/24 blahblah <br>&nbsp;
</td>
</tr>

<tfoot>

</tfoot></table></td>
</tr>
<tr>
<td align="middle" width="100%">
<table class="tab" bordercolorlight="white" bordercolordark="white" bgcolor="#ffffff" border="0" bordercolor="#000000" cellpadding="0" cellspacing="0" width="100%">
<tr>
<td colspan="5" align="middle" class="headrow">&nbsp;</td>
</tr>
<tr>
<td align="center"> <table>
<tr>
<td align="center"><input type="button" value="Home" onclick="dohome();"></td>
<td align="center"><input type="button" value="Back" onclick="history.go(-1);"></td>
</tr></table>
</td></tr></table>
</td>
</tr>


</table>
</td>
</tr>
</table>
