package aljex

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/url"
	"slices"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (a *Aljex) PostException(ctx context.Context, load *models.Load, e models.Exception) (err error) {
	spanAttrs := append(
		otel.IntegrationAttrs(a.tms),
		otel.SafeIntAttribute("load_id", e.LoadID),
		attribute.String("freight_tracking_id", load.FreightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "PostExceptionAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.PostException")

	// event codes are returned from Aljext & stored in db as the event name + abbr in one
	// we need to parse out both parts to send back to Aljex as 2 separate params
	// NOTE: for some reason only LATEPU (late pickup) triggers an email notif, unsure why right now
	eventName, eventAbbr := parseEventCode(e.EventCode)
	if eventName == "" || eventAbbr == "" {
		return errors.New("eventName or eventAbbr cannot be empty")
	}
	params := url.Values{}

	// Note that Aljex allows duplicates
	params.Set("prcnam", "carexcept")
	params.Set("type", "asave")
	params.Set("qual", a.creds.Qual)
	params.Set("pro", load.FreightTrackingID)
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("sys", "3a")
	params.Set("pro", load.FreightTrackingID)
	params.Set("fld1", load.FreightTrackingID)
	params.Set("fld2", "exception")
	params.Set("fld4", a.creds.Name)
	params.Set("crow", "")
	params.Set("saved", "")
	params.Set("ctlrec", "")
	params.Set("ctlval", "")
	params.Set("nrec", "7144")
	params.Set("excno", "")
	params.Set("from", "nadd")
	params.Set("webprint", "")
	params.Set("event", eventName)
	params.Set("ecode", eventAbbr)
	params.Set("notehed12", "")
	// this is called event_list but only ever takes in one event code - code of event being posted
	params.Set("event_list", eventAbbr)
	params.Set("fld25", e.Driver)
	params.Set("fld6", e.Trailer)
	params.Set("fld27", e.Carrier)
	params.Set("fld8", e.Fault)
	params.Set("notes", e.Note)
	params.Set("fld3", e.DateTimeWithoutTimezone.Time.In(time.UTC).Format("1/2/06"))
	params.Set("fld7", e.DateTimeWithoutTimezone.Time.In(time.UTC).Format("15:04:05"))
	params.Set("fld19", a.creds.Name)

	log.Info(ctx, "params", zap.Any("params", params))

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(params.Encode()), true, s3backup.TypeExceptions)
	if err != nil {
		return fmt.Errorf("failed to post Aljex exception: %w", err)
	}

	// Ignore resp variable by using _
	_ = resp

	return err

}

func (a *Aljex) GetExceptionHistory(
	ctx context.Context,
	loadID uint, freightTrackingID string,
) (history []models.Exception, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(a.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetExceptionHistoryAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.GetExceptionHistory")

	params := url.Values{}
	params.Set("prcnam", "carexcept")
	params.Set("type", "login")
	params.Set("qual", a.creds.Qual)
	params.Set("pro", freightTrackingID)
	params.Set("name", a.creds.Name)
	params.Set("c_tok", a.creds.Token)
	params.Set("ccby", "axleapi")
	params.Set("sys", "3a")
	params.Set("isfbox", "0")

	resp, _, err := a.get(ctx, a.getAPIURL()+"?"+params.Encode(), s3backup.TypeExceptions)
	if err != nil {
		return history, fmt.Errorf("failed to get Aljex exception history: %w", err)
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		return history, err
	}

	rows := doc.Find("table.tab tbody tr.lo") // Selecting all table rows with class 'lo'

	rows.Each(func(index int, row *goquery.Selection) {
		cells := row.Find("td") // Selecting all cells in the row

		// Extracting data from each cell
		eventCode := strings.ReplaceAll(strings.TrimSpace(cells.Eq(0).Text()), "\u00a0", " ")
		whoEntered := strings.TrimSpace(cells.Eq(1).Text())
		dateTimeRaw := strings.TrimSpace(cells.Eq(2).Text())
		carrier := strings.TrimSpace(cells.Eq(3).Text())
		driver := strings.TrimSpace(cells.Eq(4).Text())
		fault := strings.TrimSpace(cells.Eq(5).Text())
		trailer := strings.TrimSpace(cells.Eq(6).Text())
		note := strings.TrimSpace(cells.Eq(7).Text())
		status := strings.TrimSpace(cells.Eq(8).Text())

		// Need to replace non-breaking space characters with regular spaces so time parsing works
		dateTimeRaw = strings.ReplaceAll(dateTimeRaw, "\xc2\xa0", " ")

		// NOTE: (from check calls) NFI interprets timestamps in the timezone of the check call's location
		// But Aljex strings exclude TZ info so default everything to UTC to preserve raw values.
		dateTime, err := time.ParseInLocation("01/02/06 - 15:04:05", dateTimeRaw, time.UTC)
		if err != nil {
			log.Warn(ctx, "error parsing exception datetime", zap.Error(err), zap.Int("row", index+1))

			return
		}

		exception := models.Exception{
			LoadID:                  loadID,
			EventCode:               eventCode,
			WhoEntered:              whoEntered,
			DateTime:                dateTimeRaw,
			DateTimeWithoutTimezone: models.NullTime{Time: dateTime, Valid: true},
			Carrier:                 carrier,
			Driver:                  driver,
			Fault:                   fault,
			Trailer:                 trailer,
			Note:                    note,
			Status:                  status,
		}
		history = append(history, exception)
	})

	return history, nil

}

// Aljex-defined "event codes"
func EventCodeLongFormToAbbrv(s string) string {
	switch strings.ToLower(s) {
	case "hold":
		return "HD"
	case "puappt":
		return "PU"
	case "latepu":
		return "LP"
	case "punumb":
		return "PU"
	case "brkdwn":
		return "BK"
	case "delappt":
		return "DA"
	case "latedel":
		return "LD"
	case "detent":
		return "DT"
	case "delnumb":
		return "DN"
	case "tnu":
		return "TN"
	case "notready":
		return "NR"
	case "damage":
		return "DM"
	case "claim":
		return "CL"
	case "acct":
		return "AC"
	case "lumper":
		return "LM"
	case "pod":
		return "PD"
	case "carrinv":
		return "VR"
	case "custinv":
		return "CI"
	case "other":
		return "OT"
	default:
		return ""
	}
}

// Case-insensitive
func IsValidEventCode(s string) bool {
	s = strings.ToLower(s)
	return slices.Contains(EventCodeEnums, strings.ToLower(s))
}

// Static list of Aljex's event codes
var EventCodeEnums = []string{
	"hold",
	"puappt",
	"latepu",
	"punumb",
	"brkdwn",
	"delappt",
	"latedel",
	"detent",
	"delnumb",
	"tnu",
	"notready",
	"damage",
	"claim",
	"acct",
	"lumper",
	"pod",
	"carrinv",
	"custinv",
	"other",
}

func parseEventCode(eventCode string) (string, string) {
	parts := strings.Split(eventCode, "-")

	if len(parts) != 2 {
		return "", ""
	}

	name := strings.TrimSpace(parts[0])

	abbr := strings.TrimSpace(parts[1]) // trim spaces
	if len(abbr) >= 2 {
		// just get the first two chars
		abbr = abbr[:2]
	}

	return name, abbr
}
