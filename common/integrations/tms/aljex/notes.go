package aljex

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (a *Aljex) PostNote(
	ctx context.Context,
	load *models.Load,
	note models.Note,
) (notes []models.Note, err error) {
	spanAttrs := append(otel.IntegrationAttrs(a.tms), attribute.String("freight_tracking_id", load.FreightTrackingID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostNoteAljex", spanAttrs)
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "aljex.PostNote")

	formData := url.Values{}
	formData.Set("pro", load.FreightTrackingID)
	formData.Set("recno", "")
	formData.Set("notenum", "")
	formData.Set("func1", "")
	formData.Set("type", "nsave")
	formData.Set("notesfield", note.Note)
	formData.Set("prcnam", "tag")
	formData.Set("sys", "3a")
	formData.Set("isfbox", "1")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("c_tok", a.creds.Token)

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeNotes)
	if err != nil {
		return notes, fmt.Errorf("failed to create Aljex note: %w", err)
	}

	// Upon success, request returns 200 with empty response body

	if !strings.Contains(strings.ToLower(string(resp)), "close fancybox") {
		return notes, fmt.Errorf("POST request to create notes failed: %s", string(resp))
	}

	refreshedLoad, _, err := a.GetLoad(ctx, load.FreightTrackingID)
	return refreshedLoad.Notes, err
}
