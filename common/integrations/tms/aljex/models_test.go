package aljex

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestAljexStringsToTime(t *testing.T) {
	ctx := context.Background()
	t.Run("Date only", func(t *testing.T) {
		date := "08/29/23"
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 29, 0, 0, 0, 0, time.UTC),
			Valid: true,
		}
		res := stringsToTime(ctx, date, "")

		assert.Equal(t, expected, res)
	})

	t.Run("Date & time", func(t *testing.T) {
		date := "08/29/23"
		clock := "15:57"
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 29, 15, 57, 0, 0, time.UTC),
			Valid: true,
		}
		res := stringsToTime(ctx, date, clock)

		assert.Equal(t, expected, res)
	})

	t.Run("Empty", func(t *testing.T) {
		res := stringsToTime(ctx, "", "")

		assert.Equal(t, models.NullTime{}, res)
	})

	t.Run("Invalid clock", func(t *testing.T) {
		date := "08/29/23"
		clock := "DROP"
		expected := models.NullTime{
			Time:  time.Date(2023, 8, 29, 0, 0, 0, 0, time.UTC),
			Valid: true,
		}
		res := stringsToTime(ctx, date, clock)

		assert.Equal(t, expected, res)
	})
}
