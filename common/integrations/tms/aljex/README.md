# Aljex TMS

## Design Decision Record
* **User Sign-In:** Customers may request for Drumkit to allow users to sign into their own Aljex accounts so that the Aljex captures show which user made which edit (on a PRO page, click Updates tab then "View Log File" button). The blocker to this is that if users have MFA, our scraper no longer works.

    **Current Solution:**

    All Drumkit-to-Aljex requests are made using our Aljex NFI account, which does not implement MFA. As a compromise for NFI's request, we've appended the user's email handle to the `Text` field of check calls, and developers should do this wherever possible when developing new features (for example, event management text field).
        * NOTE: We don't assign the `CheckCall.Author` field because Aljex expects its own username format, but we currently can't map the user's email (what Drumkit sees) to their Aljex username. Plus, it's helpful for debugging/auditing to know which requests came directly from the user vs. which came via Drumkit.

    **Potential Long-Term Solutions:**
    * Allow only users without MFA to sign-in (we can check during sign-in if we encounter the MFA challenge/error)
    * Build an API for Aljex for the good of all mankind
    * *If you wanna be cool, Follow one simple rule, Don't mess with the flo-flo-floooow, Stick to the status quoooooo*

* **Timezones:** Aljex timestamps do not include timezone data. Go's `time.Time` type requires a location/UTC offset, so to make our data also timezone agnostic, **both the front-end and back-end parse/display timestamps in UTC** for NFI/Aljex (TBD on other Aljex customers). [models.CheckCall](../../../models/checkcalls.go) makes this explicit with the `DateTimeWithoutTimezone` field; since there are multiple datetime fields in [models.Load](../../../models/load.go), all the `NullTime` fields do this implicitly.
    * Note how [stringsToTime](./models.go) defaults to `time.UTC` location when parsing.
    * Note how [timeToAljexDate & timeToAljexTime](./models.go) explicitly set the time object to UTC before updating Aljex in case the DB changed the offset (such as when running locally vs. on AWS) because the objective is to preserve the raw date and clock values as they appear in Aljex.

## Tips for Contributing & Debugging
* You can find Aljex credentials in 1Password. If you don't have access, ask an administrator.
* If you need to update an element, first use the browser inspector to find the form name of the element.
![Chrome Inspector Screenshot](./images/inspector.png)
Then, click the "Save" button to generate the POST request while the inspector is still open. Look for the `route.php` Doc network request whose payload contains hundreds of paramaters.

<img src="./images/payload.png" alt="Chrome Inspector Post Payload" width="400"/>

Copy & paste the payload into an editor of choice then Ctrl + Find for the value in your element of interest (in the picture above, DHL). Note that your inputted value should be unique across the entire Aljex page. If that value appears multiple times in the POST payload, then you likely need to include all of its form names in the POST request in order for the update to work. This is the case for carrier dispatched date & time and other fields (see code for examples).
