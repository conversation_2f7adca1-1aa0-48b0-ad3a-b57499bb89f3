package aljex

import "testing"

func TestExtractPRO(t *testing.T) {
	tests := []struct {
		name     string
		cellText string
		want     string
	}{
		{
			name: "simple",
			cellText: `
			<td class="lo" style="text-align:center;">
				<a href="/route.php?pro=2080005" target="_blank">2080005</a>
			</td>
			`,
			want: "2080005",
		},
		{
			name: "complex",
			cellText: `
			<td class="lo" style="text-align:center;">
				<a href="/route.php?pro=2080914" target="_blank">2080914 Pcs: 15     Wgt: 15000 FASTENERS </a>
			</td>
			`,
			want: "2080914",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := extractPRO(tt.cellText); got != tt.want {
				t.Errorf("extractPRO() = %v, want %v", got, tt.want)
			}
		})
	}
}
