package aljex

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/aws/aws-sdk-go-v2/config"
	lambdasdk "github.com/aws/aws-sdk-go-v2/service/lambda"

	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

// NOTE: Aljex web scraper lambda to be deprecated in favor of Go function
// lambdaFn is a var and not const because of Lambda SDK pointer requirements
var lambdaFn = "beacon-prod-aljex"

type TMSLambdaRequest struct {
	PRONumber string   `json:"pro_number"`
	DoUpdates bool     `json:"do_updates"`
	Updates   LoadData `json:"updates"`
}

func invokeAljexLambda(ctx context.Context, req TMSLambdaRequest) error {
	// Even if the PRO is already in the DB, poll the TMS to get latest information
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return err
	}
	lambdaClient := lambdasdk.NewFromConfig(cfg)

	requestBytes, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("json.Marshal failed: %w", err)
	}

	response, err := lambdaClient.Invoke(ctx, &lambdasdk.InvokeInput{FunctionName: &lambdaFn, Payload: requestBytes})
	if err != nil {
		return fmt.Errorf("lambda.Invoke(%s) failed: %w", lambdaFn, err)
	}

	if response.FunctionError != nil {
		return fmt.Errorf("lambda.Invoke(%s) returned an error: %s", lambdaFn, string(response.Payload))
	}

	payload := string(response.Payload)
	if payload == "null" {
		return fmt.Errorf("load %s not found in Aljex", req.PRONumber)
	}

	// Clean JSON output before marshaling
	modifiedPayload := payload

	numericStringFields := []string{"#_of_hours", "customer_lh_rate_us", "fsc_%",
		"fsc_mile", "carrier_lh_rate_usd", "max_rate"}

	for _, numericString := range numericStringFields {
		modifiedPayload = strings.ReplaceAll(modifiedPayload, numericString+`": ""`, numericString+`": "0"`)
	}

	// A regex to match decimal values without a leading zero as they fail JSON unmarshaling
	pattern := `(\D|^)(\.\d+)`

	// Compile the regular expression
	re := regexp.MustCompile(pattern)

	// Replace matched values with a leading zero
	modifiedPayload = re.ReplaceAllString(modifiedPayload, "${1}0${2}")

	var aljexPRO LoadData
	if err = json.Unmarshal([]byte(modifiedPayload), &aljexPRO); err != nil {
		return fmt.Errorf("error unmarshaling payload into Aljex stuct: %w", err)
	}

	// If this is not really used anywhere, just assign a placeholder service to
	// suppress errors
	load := aljexPRO.ToLoadModel(ctx, req.PRONumber, 1, 1)
	if err = loadDB.UpsertLoad(ctx, load, &models.Service{}); err != nil {
		return err
	}

	return err
}

// DEPRECATED
func LambdaGetLoad(ctx context.Context, freightTrackingID string) error {
	return invokeAljexLambda(ctx, TMSLambdaRequest{PRONumber: freightTrackingID})
}
