package tai

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (t Tai) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	_, metaSpan := otel.StartSpan(ctx, "InitialOnBoardTai", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.APIKey == "" {
		return models.OnboardTMSResponse{}, errors.New("missing Tai API credentials")
	}
	return models.OnboardTMSResponse{
		APIKey: onboardRequest.APIKey,
	}, nil
}
