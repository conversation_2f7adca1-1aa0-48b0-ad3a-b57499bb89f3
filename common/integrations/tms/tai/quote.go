package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (t Tai) CreateQuote(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "CreateQuoteTai", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	commodityWeight, err := strconv.ParseFloat(quoteBody.CommodityWeight, 64)

	quoteLoad := buildCreateQuoteBody(ctx, quoteBody, commodityWeight)

	reqBody, err := t.LoadToTaiShipmentReqSimple(quoteLoad)
	if err != nil {
		return nil, fmt.Errorf("error building request body: %w", err)
	}
	queryParams := url.Values{}
	var response LoadResponse

	err = t.post(ctx, "/PublicApi/Shipping/v2/Shipments", queryParams, reqBody, &response, s3backup.TypeQuotes)
	if err != nil {
		return quoteResponse, err
	}

	return &models.CreateQuoteResponse{
		QuoteID:         response.ShipmentID,
		QuoteExternalID: strconv.Itoa(response.ShipmentID),
	}, err
}

func buildCreateQuoteBody(
	ctx context.Context,
	quoteBody models.CreateQuoteBody,
	commodityWeight float64,
) models.Load {

	loadMode := models.StringToLoadMode(string(quoteBody.TransportType))
	if loadMode == "" {
		log.Warn(
			ctx,
			"Unknown Tai load mode",
			zap.String("mode", string(quoteBody.TransportType)),
		)
	}

	return models.Load{
		LoadCoreInfo: models.LoadCoreInfo{
			Mode: loadMode,
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: quoteBody.CustomerID,
				},
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Zipcode: quoteBody.PickupLocationZip,
				},
				ReadyTime: quoteBody.PickupDate,
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					Zipcode: quoteBody.DeliveryLocationZip,
				},
				MustDeliver: quoteBody.DeliveryDate,
			},
		},
		Commodities: []models.Commodity{{
			WeightTotal: commodityWeight,
			Description: quoteBody.CommodityDescription,
		},
		},
	}
}
