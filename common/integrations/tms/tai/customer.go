package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

type CustomerAddressResp struct {
	CustomerFavoriteAddressID int    `json:"customerFavoriteAddressId"`
	OrganizationID            int    `json:"organizationId"`
	CompanyName               string `json:"companyName"`
	Address                   struct {
		StreetAddress    string `json:"streetAddress"`
		StreetAddressTwo string `json:"streetAddressTwo"`
		City             string `json:"city"`
		State            string `json:"state"`
		ZipCode          string `json:"zipCode"`
		Country          string `json:"country"`
	} `json:"address"`
	Instructions    string   `json:"instructions"`
	Note            string   `json:"note"`
	ReferenceNumber string   `json:"referenceNumber"`
	LocationType    string   `json:"locationType"`
	OpenTime        string   `json:"openTime"`
	CloseTime       string   `json:"closeTime"`
	TsaCompliant    bool     `json:"tsaCompliant"`
	Accessorials    []string `json:"accessorials"`
}

func (t Tai) GetCustomerAddresses(ctx context.Context, customerID int) ([]CustomerAddressResp, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.Int("customer_id", customerID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomerAddressesTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := "/PublicApi/Customer/v2/Addresses"
	queryParams := url.Values{}
	queryParams.Set("customerId", strconv.Itoa(customerID))

	var addresses []CustomerAddressResp
	err = t.get(ctx, endPoint, queryParams, &addresses, s3backup.TypeCustomerAddresses)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer addresses: %w", err)
	}

	return addresses, nil
}

func (t Tai) TaiAddressToLocation(address CustomerAddressResp) models.TMSLocation {
	location := models.TMSLocation{
		TMSIntegrationID: t.tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(address.OrganizationID),
			Name:          address.CompanyName,
			AddressLine1:  address.Address.StreetAddress,
			AddressLine2:  address.Address.StreetAddressTwo,
			City:          address.Address.City,
			State:         address.Address.State,
			Zipcode:       address.Address.ZipCode,
			Country:       address.Address.Country,
		},
		Instructions:   address.Instructions,
		Note:           address.Note,
		RefNumber:      address.ReferenceNumber,
		LocationType:   address.LocationType,
		OpenTime:       address.OpenTime,
		CloseTime:      address.CloseTime,
		IsTSACompliant: address.TsaCompliant,
		Accessorials:   address.Accessorials,
	}

	location.FullAddress = models.ConcatAddress(location.CompanyCoreInfo)
	location.NameAddress = location.Name + ", " + location.FullAddress

	return location
}

func (t Tai) LoadCustomerAddresses(ctx context.Context, customerID int) ([]models.TMSLocation, error) {
	addresses, err := t.GetCustomerAddresses(ctx, customerID)
	if err != nil {
		return nil, err
	}

	var locations []models.TMSLocation
	for _, address := range addresses {
		locations = append(locations, t.TaiAddressToLocation(address))
	}

	return locations, nil
}

func (t Tai) TaiCustomerToCustomer(customer CustomerResp) models.TMSCustomer {
	var contactName string
	if customer.Address.ContactName != nil {
		contactName = *customer.Address.ContactName
	}

	var webAddress string
	if customer.WebAddress != nil {
		webAddress = *customer.WebAddress
	}

	return models.TMSCustomer{
		TMSIntegrationID: t.tms.ID,
		ExternalID:       strconv.Itoa(customer.OrganizationID),
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(customer.OrganizationID),
			Name:          customer.Name,
			AddressLine1:  customer.Address.StreetAddress,
			AddressLine2:  customer.Address.StreetAddressTwo,
			City:          customer.Address.City,
			State:         customer.Address.State,
			Zipcode:       customer.Address.ZipCode,
			Country:       customer.Address.Country,
			Contact:       contactName,
			Phone:         customer.Phone,
			Email:         webAddress,
		},
	}
}
