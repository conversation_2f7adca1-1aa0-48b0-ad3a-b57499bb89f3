# Tai TMS integration


## Webhook Integration

The Tai TMS integration uses webhooks to receive real-time updates for shipments and customers. Since Tai TMS does not support pulling data directly from their API, webhooks are the primary method for data synchronization.

### Webhook Token

The integration uses a special persistent webhook token for authentication. This token is generated using the JWT system and has a 5-year expiration period. The token is used to authenticate incoming webhook requests from Tai TMS.

#### Creating a Webhook Token

Looking at the context, I can see from `fn/api/routes/user/webhook_token.go` that the endpoint is `/api/user/webhooktoken`.

### Webhook Endpoints

The integration provides two webhook endpoints:

1. **Load Webhook** (`/api/integrations/tai/load`) - Handles shipment updates and creation
2. **Customer Webhook** (`/api/integrations/tai/customer`) - Handles customer updates and creation

### Setting Up Webhooks in Tai TMS

To configure webhooks in Tai TMS:

1. Log into your Tai TMS account
2. Navigate to the webhook configuration section
3. Add the following webhook URLs:
   - **Shipment Webhook**: `https://your-domain.com/api/integrations/tai/load`
   - **Customer Webhook**: `https://your-domain.com/api/integrations/tai/customer`
4. Configure the webhook to send data in the required format
5. Set the authentication method to "Bearer Token" and provide your webhook token
6. Configure the Authorization header as: `Bearer YOUR_WEBHOOK_TOKEN`

For detailed setup instructions, refer to the [Tai TMS Webhook Integration Setup Guide](https://learn.tai-software.com/knowledge/webhook-integration-setup).

### Webhook Data Flow

1. Tai TMS sends webhook data to the configured endpoints with Bearer token authentication
2. The webhook handlers authenticate the request using the webhook token
3. The data is transformed from Tai TMS format to internal models
4. The transformed data is upserted into the database
5. A success response is sent back to Tai TMS

### Supported Webhook Events

- **ShipmentCreate** - New shipment creation
- **ShipmentUpdate** - Existing shipment updates
- **CustomerCreate** - New customer creation
- **CustomerUpdate** - Existing customer updates
