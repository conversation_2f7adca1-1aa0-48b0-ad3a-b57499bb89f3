package tai

import (
	"encoding/json"
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

type Customer struct {
	Name                 string `json:"name"`
	StaffID              int    `json:"staffID"`
	StaffName            string `json:"staffName"`
	SalesRepNames        string `json:"salesRepNames"`
	BillToOrganizationID int    `json:"billToOrganizationId"`
	OfficeOrganizationID int    `json:"officeOrganizationId"`
	OfficeName           string `json:"officeName"`
}

type Carrier struct {
	CarrierMasterID int     `json:"carrierMasterId"`
	Name            string  `json:"name"`
	Scac            string  `json:"scac"`
	DotNumber       string  `json:"dotNumber"`
	McNumber        string  `json:"mcNumber"`
	TrackingURL     string  `json:"trackingURL"`
	City            string  `json:"city"`
	State           string  `json:"state"`
	ZipCode         string  `json:"zipCode"`
	PhoneNumber     string  `json:"phoneNumber"`
	TransitType     string  `json:"transitType"`
	Status          string  `json:"status"`
	Buy             float64 `json:"buy"`
	Sell            float64 `json:"sell"`
}

type Attachment struct {
	AttachmentURL  string `json:"attachmentUrl"`
	AttachmentType string `json:"attachmentType"`
	DocumentID     int    `json:"documentId"`
}

type ShipmentReferenceNumber struct {
	ReferenceType string `json:"referenceType"`
	Value         string `json:"value"`
}

type StopType string

const (
	FirstPickup StopType = "First Pickup"
	LastDrop    StopType = "Last Drop"
)

type Stop struct {
	CompanyName              string                    `json:"companyName"`
	StreetAddress            string                    `json:"streetAddress"`
	City                     string                    `json:"city"`
	State                    string                    `json:"state"`
	ZipCode                  string                    `json:"zipCode"`
	Country                  string                    `json:"country"`
	ContactName              string                    `json:"contactName,omitempty"`
	Phone                    string                    `json:"phone,omitempty"`
	Email                    string                    `json:"email,omitempty"`
	ReferenceNumber          []ShipmentReferenceNumber `json:"shipmentStopReferenceNumbers,omitempty"`
	EstimatedReadyDateTime   models.NullTime           `json:"estimatedReadyDateTime"`
	EstimatedCloseDateTime   models.NullTime           `json:"estimatedCloseDateTime"`
	AppointmentReadyDateTime models.NullTime           `json:"appointmentReadyDateTime"`
	AppointmentCloseDateTime models.NullTime           `json:"appointmentCloseDateTime"`
	ActualArrivalDateTime    models.NullTime           `json:"actualArrivalDateTime"`
	ActualDepartureDateTime  models.NullTime           `json:"actualDepartureDateTime"`
	Notes                    string                    `json:"notes"`
	StopType                 StopType                  `json:"stopType"`
	StreetAddressTwo         string                    `json:"streetAddressTwo,omitempty"`
	Instructions             string                    `json:"instructions,omitempty"`
}

type Commodity struct {
	HandlingQuantity  int     `json:"handlingQuantity"`
	PackagingType     any     `json:"packagingType"`
	WeightTotal       float64 `json:"weightTotal"`
	HazardousMaterial bool    `json:"hazardousMaterial"`
	PiecesTotal       int     `json:"piecesTotal"`
	FreightClass      string  `json:"freightClass"`
	Description       string  `json:"description"`
	HazardClasses     []any   `json:"hazardClasses"`
}

type AccessorialCode struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

type ShipmentAlert struct {
	// Type is usually a string but sometimes it's an int? idfk
	Type        json.RawMessage `json:"type"`
	CreatedDate time.Time       `json:"createdDate"`
	Resolved    bool            `json:"resolved"`
}

type ShipmentResp struct {
	ShipmentID               int                       `json:"shipmentId"`
	Latitude                 float64                   `json:"latitude"`
	Longitude                float64                   `json:"longitude"`
	LastLocationUpdate       string                    `json:"lastLocationUpdate"`
	Mileage                  float64                   `json:"mileage"`
	Customer                 Customer                  `json:"customer"`
	TotalBuy                 float64                   `json:"totalBuy"`
	TotalSell                float64                   `json:"totalSell"`
	Status                   string                    `json:"status"`
	CarrierList              []Carrier                 `json:"carrierList"`
	Attachments              []Attachment              `json:"attachments"`
	ShipmentType             string                    `json:"shipmentType"`
	Stackable                bool                      `json:"stackable"`
	TrailerType              string                    `json:"trailerType"`
	TrailerSize              string                    `json:"trailerSize"`
	WeightUnits              string                    `json:"weightUnits"`
	DimensionUnits           string                    `json:"dimensionUnits"`
	ServiceLevel             string                    `json:"serviceLevel"`
	ShipmentReferenceNumbers []ShipmentReferenceNumber `json:"shipmentReferenceNumbers"`
	Stops                    []Stop                    `json:"stops"`
	Commodities              []Commodity               `json:"commodities"`
	AccessorialCodes         []AccessorialCode         `json:"accessorialCodes"`
	ShipmentAlerts           []ShipmentAlert           `json:"shipmentAlerts"`
	DriverCellPhoneNumber    string                    `json:"driverCellPhoneNumber"`
}

type ShipmentTrackingUpdateReq struct {
	ShipmentID     int            `json:"shipmentId"`
	ProNumber      string         `json:"proNumber"`
	TransitType    string         `json:"transitType"`
	TrackingUpdate TrackingUpdate `json:"trackingUpdate"`
}

type TrackingUpdate struct {
	PickupNumber                     string    `json:"pickupNumber"`
	ShipmentStatus                   string    `json:"shipmentStatus"`
	PickupReadyDateTime              time.Time `json:"pickupReadyDateTime"`
	PickupCloseDateTime              time.Time `json:"pickupCloseDateTime"`
	DeliveryEstimatedDateTime        time.Time `json:"deliveryEstimatedDateTime"`
	DeliveryCloseDateTime            time.Time `json:"deliveryCloseDateTime"`
	ProofOfDeliveryArrivalDateTime   time.Time `json:"proofOfDeliveryArrivalDateTime"`
	ProofOfDeliveryDepartureDateTime time.Time `json:"proofOfDeliveryDepartureDateTime"`
	ProofOfDeliverySignedBy          string    `json:"proofOfDeliverySignedBy"`
	ActualPickupArrivalDateTime      time.Time `json:"actualPickupArrivalDateTime"`
	ActualPickupDepartureDateTime    time.Time `json:"actualPickupDepartureDateTime"`
	PickupAppointmentBeginDateTime   time.Time `json:"pickupAppointmentBeginDateTime"`
	PickupAppointmentEndDateTime     time.Time `json:"pickupAppointmentEndDateTime"`
	DeliveryAppointmentBeginDateTime time.Time `json:"deliveryAppointmentBeginDateTime"`
	DeliveryAppointmentEndDateTime   time.Time `json:"deliveryAppointmentEndDateTime"`
}

type ShipmentRefsUpdateReq []ReferenceNumber

type ShipmentTrackingRefsUpdateReq struct {
	ReferenceNumbers []ReferenceNumber `json:"referenceNumbers"`
}

type ShipmentUpdateSimpleReq struct {
	IsCommitted  bool                            `json:"isCommitted"`
	RateShipment bool                            `json:"rateShipment"`
	CustomerID   int                             `json:"customerId"`
	ShipmentType ShipmentType                    `json:"shipmentType"`
	TrailerType  string                          `json:"trailerType"`
	WeightUnits  string                          `json:"weightUnits"`
	Stops        []ShipmentUpdateStopSimple      `json:"stops"`
	Commodities  []ShipmentUpdateCommoditySimple `json:"commodities"`
}

type ReferenceNumber struct {
	ReferenceType string `json:"referenceType"`
	Value         string `json:"value"`
}

type ShipmentUpdateStop struct {
	CompanyName                  string            `json:"companyName,omitempty"`
	StreetAddress                string            `json:"streetAddress,omitempty"`
	StreetAddressTwo             string            `json:"streetAddressTwo,omitempty"`
	City                         string            `json:"city,omitempty"`
	State                        string            `json:"state,omitempty"`
	ZipCode                      string            `json:"zipCode,omitempty"`
	Country                      string            `json:"country,omitempty"`
	ContactName                  string            `json:"contactName,omitempty"`
	Phone                        string            `json:"phone,omitempty"`
	Fax                          string            `json:"fax,omitempty"`
	Email                        string            `json:"email,omitempty"`
	Instructions                 string            `json:"instructions,omitempty"`
	Notes                        string            `json:"notes,omitempty"`
	ReferenceNumber              []ReferenceNumber `json:"referenceNumber,omitempty"`
	AirportOrTerminalCode        string            `json:"airportOrTerminalCode,omitempty"`
	EstimatedReadyDateTime       *time.Time        `json:"estimatedReadyDateTime,omitempty"`
	EstimatedCloseDateTime       *time.Time        `json:"estimatedCloseDateTime,omitempty"`
	AppointmentReadyDateTime     *time.Time        `json:"appointmentReadyDateTime,omitempty"`
	AppointmentCloseDateTime     *time.Time        `json:"appointmentCloseDateTime,omitempty"`
	ActualArrivalDateTime        *time.Time        `json:"actualArrivalDateTime,omitempty"`
	ActualDepartureDateTime      *time.Time        `json:"actualDepartureDateTime,omitempty"`
	StopType                     string            `json:"stopType"`
	ShipmentStopReferenceNumbers []ReferenceNumber `json:"shipmentStopReferenceNumbers,omitempty"`
}

type ShipmentUpdateStopSimple struct {
	ZipCode  string `json:"zipCode"`
	StopType string `json:"stopType"`
}

type ShipmentUpdateCommoditySimple struct {
	WeightTotal  float64 `json:"weightTotal"`
	Description  string  `json:"description"`
	PiecesTotal  float64 `json:"piecesTotal"`
	FreightClass string  `json:"freightClass"`
}

type ShipmentType string

const (
	SmallPackageShipmentType ShipmentType = "small package"
	TruckloadShipmentType    ShipmentType = "Truckload"
)

// todo : need to check response type while testing
type LoadResponse struct {
	ShipmentID int    `json:"shipmentID"`
	Status     string `json:"status"`
}

type GetCheckCallResp struct {
	ShipmentID          int     `json:"shipmentId"`
	ShipmentStatus      string  `json:"shipmentStatus"`
	PickupDate          string  `json:"pickupDate"`
	PickupAppointment   string  `json:"pickupAppointment"`
	EstimatedDelivery   string  `json:"estimatedDelivery"`
	DeliveryAppointment string  `json:"deliveryAppointment"`
	Latitude            float64 `json:"latitude"`
	Longitude           float64 `json:"longitude"`
	LastLocation        string  `json:"lastLocation"`
	LocationHistory     []struct {
		LocationDateTime string  `json:"locationDateTime"`
		Latitude         float64 `json:"latitude"`
		Longitude        float64 `json:"longitude"`
		LocationString   string  `json:"locationString"`
	} `json:"locationHistory"`
}

type UpdateCheckcallRequest struct {
	ShipmentID       int    `json:"shipmentId"`
	LocationDateTime string `json:"locationDateTime"`
	LocationString   string `json:"locationString"`
}

type CustomerResp struct {
	AccountingProfile struct {
		AllowZeroMarginInvoiceAutomation  bool     `json:"allowZeroMarginInvoiceAutomation"`
		BolRequiredWithInvoice            bool     `json:"bolRequiredWithInvoice"`
		CollectionNoticeDeliveryFrequency string   `json:"collectionNoticeDeliveryFrequency"`
		CreditLimit                       *float64 `json:"creditLimit"`
		EnablePastDueNotification         bool     `json:"enablePastDueNotification"`
		IncludeShipmentOwnerOnInvoice     bool     `json:"includeShipmentOwnerOnInvoice"`
		InvoiceDeliveryInstructions       *string  `json:"invoiceDeliveryInstructions"`
		InvoicingAutomationMethod         string   `json:"invoicingAutomationMethod"`
		PaymentPreference                 string   `json:"paymentPreference"`
		PaymentTerms                      *string  `json:"paymentTerms"`
		PodRequiredWithInvoice            bool     `json:"podRequiredWithInvoice"`
		PreferredInvoicingMethod          string   `json:"preferredInvoicingMethod"`
		StatementDeliveryFrequency        string   `json:"statementDeliveryFrequency"`
		TaxNumber                         *string  `json:"taxNumber"`
	} `json:"accountingProfile"`
	Address struct {
		City             string  `json:"city"`
		ContactName      *string `json:"contactName"`
		Country          string  `json:"country"`
		State            string  `json:"state"`
		StreetAddress    string  `json:"streetAddress"`
		StreetAddressTwo string  `json:"streetAddressTwo"`
		ZipCode          string  `json:"zipCode"`
	} `json:"address"`
	BillToAddress struct {
		City             string  `json:"city"`
		ContactName      *string `json:"contactName"`
		Country          string  `json:"country"`
		State            string  `json:"state"`
		StreetAddress    string  `json:"streetAddress"`
		StreetAddressTwo string  `json:"streetAddressTwo"`
		ZipCode          string  `json:"zipCode"`
	} `json:"billToAddress"`
	CreatedDate      string  `json:"createdDate"`
	Enabled          bool    `json:"enabled"`
	Fax              string  `json:"fax"`
	Mobile           string  `json:"mobile"`
	ModifiedByID     int     `json:"modifiedById"`
	ModifiedByName   string  `json:"modifiedByName"`
	ModifiedDate     string  `json:"modifiedDate"`
	Name             string  `json:"name"`
	Notes            string  `json:"notes"`
	OrganizationID   int     `json:"organizationId"`
	OrganizationType string  `json:"organizationType"`
	ParentID         int     `json:"parentId"`
	ParentIDs        string  `json:"parentIds"`
	Phone            string  `json:"phone"`
	ReferenceNumber  string  `json:"referenceNumber"`
	ShipmentID       *int    `json:"shipmentId"`
	WebAddress       *string `json:"webAddress"`
}
