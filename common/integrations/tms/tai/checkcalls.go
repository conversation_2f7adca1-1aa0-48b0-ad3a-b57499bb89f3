package tai

import (
	"context"
	"fmt"
	"strconv"

	"github.com/aws/smithy-go/time"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (t Tai) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (calls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(t.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := fmt.Sprintf("/PublicApi/Location/v2/ShipmentLocationHistory/%s", freightTrackingID)
	var callResp GetCheckCallResp
	err = t.get(ctx, endPoint, nil, &callResp, s3backup.TypeCheckCalls)
	if err != nil {
		log.Error(ctx, "Error getting check call", zap.Error(err))
		return calls, err
	}
	for _, stop := range callResp.LocationHistory {
		call := models.CheckCall{}

		locationDateTime, timeErr := time.ParseDateTime(stop.LocationDateTime)
		if timeErr != nil {
			log.Error(ctx, "Error parsing time", zap.Error(err))
			return calls, err
		}

		call.LoadID = loadID
		call.FreightTrackingID = freightTrackingID
		call.Status = callResp.ShipmentStatus
		call.DateTime = models.NullTime{
			Time:  locationDateTime,
			Valid: true,
		}
		call.City = stop.LocationString
		call.Lat = stop.Latitude
		call.Lon = stop.Longitude

		calls = append(calls, call)
	}

	return calls, err
}

func (t Tai) PostCheckCall(
	ctx context.Context,
	_ *models.Load,
	checkCall models.CheckCall,
) (err error) {

	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.SafeIntAttribute("load_id", checkCall.LoadID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := toTaiCheckcall(checkCall)
	if err != nil {
		return err
	}
	err = t.post(ctx, "/PublicApi/Location/v2/ShipmentLocations", nil, reqBody,
		nil, s3backup.TypeCheckCalls)
	if err != nil {
		log.Error(ctx, "Creating Tai check call failed", zap.Error(err))
		return err
	}
	return err
}

func toTaiCheckcall(checkCall models.CheckCall) (UpdateCheckcallRequest, error) {
	var result UpdateCheckcallRequest
	shipmentIDNumber, err := strconv.Atoi(checkCall.FreightTrackingID)
	if err != nil {
		return result, err
	}
	result.ShipmentID = shipmentIDNumber
	result.LocationDateTime = checkCall.DateTime.Time.Format("2006-01-02T15:04:05")
	result.LocationString = checkCall.City + ", " + checkCall.State

	return result, nil
}
