package tai

import "github.com/drumkitai/drumkit/common/models"

const LoadIDType = "loadId"

var mapTaiTrailersToEnum = map[string]models.TransportType{
	"none":                                      "",
	"other":                                     "",
	"flatbed":                                   models.FlatbedTransportType,
	"refrigerated":                              models.ReeferTransportType,
	"van":                                       models.VanTransportType,
	"sprinter van | dry":                        models.SprinterTransportType,
	"sprinter van | heat":                       models.SprinterTransportType,
	"sprinter van | reefer":                     models.SprinterTransportType,
	"24 ft city truck w/ liftgate | dry":        models.BoxTruckTransportType,
	"24 ft city truck w/ liftgate | heat":       models.BoxTruckTransportType,
	"24 ft city truck w/ liftgate | reefer":     models.ReeferTransportType,
	"28 ft city truck w/ liftgate | dry":        models.BoxTruckTransportType,
	"28 ft city truck w/ liftgate | heat":       models.BoxTruckTransportType,
	"28 ft city truck w/ liftgate | reefer":     models.ReeferTransportType,
	"40 ft straight truck w/ tailgate | dry":    models.BoxTruckTransportType,
	"40 ft straight truck w/ tailgate | heat":   models.BoxTruckTransportType,
	"40 ft straight truck w/ tailgate | reefer": models.ReeferTransportType,
	"48 ft van | dry":                           models.VanTransportType,
	"48 ft van | heat":                          models.VanTransportType,
	"48 ft van | reefer":                        models.ReeferTransportType,
	"48 ft van w/ tailgate | dry":               models.VanTransportType,
	"48 ft van w/ tailgate | heat":              models.VanTransportType,
	"48 ft van w/ tailgate | reefer":            models.ReeferTransportType,
	"53 ft van | dry tandem":                    models.VanTransportType,
	"53 ft van | dry tandem w/ liftgate":        models.VanTransportType,
	"53 ft van | dry tandem heat":               models.VanTransportType,
	"53 ft van | dry tandem reefer":             models.ReeferTransportType,
	"53 ft van | dry tri-axle":                  models.VanTransportType,
	"53 ft van | heat tri-axle":                 "",
	"53 ft van | reefer tri-axle":               models.ReeferTransportType,
	"53 ft van | dry quad-axle":                 models.VanTransportType,
	"53 ft van | heat quad-axle":                models.VanTransportType,
	"53 ft van | reefer quad-axle":              models.ReeferTransportType,
	"48 ft flatbed":                             models.FlatbedTransportType,
	"48 ft conestoga / curtain side":            models.FlatbedTransportType,
	"53 ft flatbed":                             models.FlatbedTransportType,
	"53 ft conestoga / curtain side":            models.FlatbedTransportType,
	"53 ft super b":                             models.FlatbedTransportType,
	"53 ft double drop":                         models.FlatbedTransportType,
	"low boy / rg":                              "",
	"power only":                                "",
	"53 ft van | plate trailer":                 models.VanTransportType,
	"48 ft step deck":                           models.FlatbedTransportType,
	"53 ft step deck":                           models.FlatbedTransportType,
	"53 ft van | reefer":                        models.ReeferTransportType,
	"container - standard":                      "",
	"container - heavy":                         "",
	"container - oog":                           "",
	"container - intermodal":                    "",
	"53 ft van | dry":                           models.VanTransportType,
	"26 ft straight truck":                      models.BoxTruckTransportType,
	"van | flatbed":                             models.VanTransportType,
	"50 ft flatbed":                             models.FlatbedTransportType,
	"50 ft van | dry":                           models.VanTransportType,
	"53 ft van | air ride":                      models.VanTransportType,
	"32 ft hotshot":                             models.HotShotTransportType,
	"36 ft hotshot":                             models.HotShotTransportType,
	"40 ft hotshot":                             models.HotShotTransportType,
	"24 ft straight truck":                      models.BoxTruckTransportType,
	"48 ft low pro step deck":                   models.FlatbedTransportType,
	"53 ft low pro step deck":                   models.FlatbedTransportType,
	"48 ft step deck | conestoga":               models.FlatbedTransportType,
	"53 ft step deck | conestoga":               models.FlatbedTransportType,
	"48 ft flatbed | conestoga":                 models.FlatbedTransportType,
	"53 ft flatbed | conestoga":                 models.FlatbedTransportType,
	"open car carrier":                          "",
	"enclosed car carrier":                      "",
	"tanker | aluminum":                         "",
	"tanker | intermodal":                       "",
	"tanker | steel":                            "",
	"48 ft dry van | reefer":                    models.VanTransportType,
	"53 ft dry van | reefer":                    models.VanTransportType,
	"hopper":                                    "",
	"dump":                                      "",
	"12 ft straight truck":                      models.BoxTruckTransportType,
	"48 ft flatbed | tarp":                      models.FlatbedTransportType,
	"50 ft flatbed | tarp":                      models.FlatbedTransportType,
	"53 ft flatbed | tarp":                      models.FlatbedTransportType,
	"container - high cube":                     "", // ¯\_(ツ)_/¯
	"53 ft reefer":                              models.ReeferTransportType,
	"livestock":                                 "",
	"vented | van":                              models.VanTransportType,
	"toter":                                     "",
}
