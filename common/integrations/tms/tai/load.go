package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	UpdateShipmentTrackingPath     = "/PublicApi/Shipping/v2/ShipmentTracking/%s"
	UpdateShipmentRefsPath         = "/PublicApi/Shipping/v2/ShipmentReferenceNumbers/%s"
	UpdateShipmentTrackingRefsPath = "/PublicApi/Shipping/v2/ShipmentTrackingReferenceNumbers/%s"
	GetUpdatedShipmentPath         = "/PublicApi/Shipping/v2/Shipments/%s"
)

func (t Tai) GetLoadIDs(context.Context, models.SearchLoadsQuery) ([]string, error) {
	return nil, helpers.NotImplemented(models.Tai, "GetLoads")
}

func (t Tai) GetLoadsByIDType(ctx context.Context, id string, _ string) ([]models.Load, models.LoadAttributes, error) {
	load, attrs, err := t.GetLoad(ctx, id)
	return []models.Load{load}, attrs, err
}

func (t Tai) GetLoad(ctx context.Context, shipmentID string) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.String("freight_tracking_id", shipmentID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := fmt.Sprintf("/PublicApi/Shipping/v2/Shipments/%s", shipmentID)
	queryParams := url.Values{}
	var shipmentData ShipmentResp
	err = t.get(ctx, endPoint, queryParams, &shipmentData, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, err
	}

	loadData := t.TaiShipmentToLoad(ctx, shipmentData)
	loadData.FreightTrackingID = shipmentID

	return loadData, DefaultLoadAttributes, nil
}

func (t Tai) CreateLoad(_ context.Context, _ models.Load, _ *models.TMSUser) (models.Load, error) {
	result := models.Load{}
	return result, helpers.NotImplemented(models.Tai, "CreateLoad")
}

func (t Tai) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	reqLoad *models.Load,
) (models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.LoadAttrs(*reqLoad)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Update appointment times
	_, err = t.updateShipmentTracking(ctx, *reqLoad)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("updating shipment tracking failed: %w", err)
	}

	// Update ref numbers
	_, err = t.updateShipmentReferences(ctx, *reqLoad)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("updating shipment references failed: %w", err)
	}

	// Update tracking ref numbers
	_, err = t.updateShipmentTrackingReferences(ctx, *reqLoad)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("updating shipment tracking references failed: %w", err)
	}

	// Get the final updated shipment data
	result, err := t.getUpdatedShipment(ctx, reqLoad.FreightTrackingID)
	if err != nil {
		return models.Load{}, DefaultLoadAttributes, fmt.Errorf("fetching updated shipment failed: %w", err)
	}

	return result, DefaultLoadAttributes, nil
}

func (t Tai) updateShipmentTracking(ctx context.Context, load models.Load) (ShipmentResp, error) {
	response := ShipmentResp{}

	req, err := t.LoadToTaiShipmentTracking(load)
	if err != nil {
		return response, err
	}

	path := fmt.Sprintf(UpdateShipmentTrackingPath, load.FreightTrackingID)
	err = t.put(ctx, path, nil, req, &response, s3backup.TypeLoads)
	return response, err
}

func (t Tai) updateShipmentReferences(ctx context.Context, load models.Load) (ShipmentResp, error) {
	response := ShipmentResp{}

	req := t.LoadToTaiShipmentRefs(load)
	if len(req) == 0 {
		return response, nil // No references to update
	}

	path := fmt.Sprintf(UpdateShipmentRefsPath, load.FreightTrackingID)
	err := t.put(ctx, path, nil, req, &response, s3backup.TypeLoads)
	return response, err
}

func (t Tai) updateShipmentTrackingReferences(ctx context.Context, load models.Load) (ShipmentResp, error) {
	response := ShipmentResp{}

	req := t.LoadToTaiShipmentTrackingRefs(load)
	if len(req.ReferenceNumbers) == 0 {
		return response, nil // No tracking references to update
	}

	path := fmt.Sprintf(UpdateShipmentTrackingRefsPath, load.FreightTrackingID)
	err := t.put(ctx, path, nil, req, &response, s3backup.TypeLoads)
	return response, err
}

func (t Tai) getUpdatedShipment(ctx context.Context, freightTrackingID string) (models.Load, error) {
	path := fmt.Sprintf(GetUpdatedShipmentPath, freightTrackingID)
	var resp ShipmentResp
	err := t.get(ctx, path, nil, &resp, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, err
	}

	result := t.TaiShipmentToLoad(ctx, resp)
	result.FreightTrackingID = strconv.Itoa(resp.ShipmentID)
	return result, nil
}

func (t Tai) TaiShipmentToLoad(ctx context.Context, shipmentData ShipmentResp) models.Load {
	var result models.Load

	result.ExternalTMSID = strconv.Itoa(shipmentData.ShipmentID)
	result.FreightTrackingID = strconv.Itoa(shipmentData.ShipmentID)
	result.Status = shipmentData.Status
	result.ServiceID = t.tms.ServiceID
	result.TMSID = t.tms.ID

	// Customer
	result.Customer.ExternalTMSID = strconv.Itoa(shipmentData.Customer.BillToOrganizationID)
	result.Customer.Name = shipmentData.Customer.Name
	result.Customer.Contact = shipmentData.Customer.SalesRepNames

	// BillTo
	result.BillTo.ExternalTMSID = strconv.Itoa(shipmentData.Customer.BillToOrganizationID)
	result.BillTo.Name = shipmentData.Customer.OfficeName

	// RateData
	netProfit := float32(shipmentData.TotalSell - shipmentData.TotalBuy)
	result.RateData.NetProfitUSD = netProfit
	carrierCost := float32(shipmentData.TotalBuy)
	result.RateData.CarrierCost = &carrierCost
	result.RateData.CarrierCostCurrency = "USD"
	// result.RateData.CustomerLHRateUSD = float32(shipmentData.TotalSell)
	// result.RateData.CarrierLHRateUSD = float32(shipmentData.TotalBuy)

	if shipmentData.TotalBuy > 0 {
		result.RateData.ProfitPercent = (netProfit / float32(shipmentData.TotalBuy)) * 100
	}

	// Carrier
	if len(shipmentData.CarrierList) > 0 {
		carrier := shipmentData.CarrierList[0]
		result.Carrier.ExternalTMSID = strconv.Itoa(carrier.CarrierMasterID)
		result.Carrier.Name = carrier.Name
		result.Carrier.DOTNumber = carrier.DotNumber
		result.Carrier.MCNumber = carrier.McNumber
		result.Carrier.SCAC = carrier.Scac
		result.Carrier.Phone = carrier.PhoneNumber
		result.Carrier.DispatchCity = carrier.City
		result.Carrier.DispatchState = carrier.State
		result.Carrier.FirstDriverPhone = shipmentData.DriverCellPhoneNumber
	}

	// Commodities
	var totalWeight float64
	for _, comm := range shipmentData.Commodities {
		var packagingType string
		switch v := comm.PackagingType.(type) {
		case string:
			packagingType = v
		case float64:
			packagingType = strconv.FormatFloat(v, 'f', -1, 64)
		case int:
			packagingType = strconv.Itoa(v)
		default:
			packagingType = ""
		}

		commodity := models.Commodity{
			HandlingQuantity:  comm.HandlingQuantity,
			PackagingType:     packagingType,
			WeightTotal:       comm.WeightTotal,
			HazardousMaterial: comm.HazardousMaterial,
			FreightClass:      comm.FreightClass,
			Description:       comm.Description,
		}
		result.Commodities = append(result.Commodities, commodity)
		totalWeight += comm.WeightTotal
	}

	// Update NumCommodities and TotalWeight
	result.Specifications.NumCommodities = len(result.Commodities)
	result.Specifications.TotalWeight = models.ValueUnit{Val: float32(totalWeight), Unit: shipmentData.WeightUnits}
	// REVIEW: DimensinoUnits enums are  ['in', 'cm', 'ft', 'm'], unclear if they're used for mileage
	result.Specifications.TotalDistance = models.ValueUnit{Val: float32(shipmentData.Mileage), Unit: "mi"}

	// Stops
	for _, stop := range shipmentData.Stops {
		if stop.StopType == FirstPickup {
			result.Pickup = mapStopToPickup(stop)
			result.Carrier.ExpectedPickupTime = stop.AppointmentReadyDateTime
			result.Carrier.PickupStart = stop.ActualArrivalDateTime
			result.Carrier.PickupEnd = stop.ActualDepartureDateTime
		}
		if stop.StopType == LastDrop {
			result.Consignee = mapStopToConsignee(stop)
			result.Carrier.ExpectedDeliveryTime = stop.AppointmentReadyDateTime
			result.Carrier.DeliveryStart = stop.ActualArrivalDateTime
			result.Carrier.DeliveryEnd = stop.ActualDepartureDateTime
		}
	}

	// Additional fields
	result.DeclaredValueUSD = float32(shipmentData.TotalSell) // Assuming declared value is the total sell amount
	result.Specifications.TransportType = shipmentData.TrailerType
	result.Specifications.TransportSize = shipmentData.TrailerSize
	transportEnum, err := t.MapTransportTypeEnum(shipmentData.TrailerType)
	if err != nil {
		log.WarnNoSentry(ctx, "error mapping transport type", zap.Error(err))
	} else {
		result.Specifications.TransportTypeEnum = &transportEnum
	}

	// Specifications
	if len(shipmentData.Commodities) > 0 {
		result.Specifications.Hazmat = shipmentData.Commodities[0].HazardousMaterial
	}

	return result
}

func (t Tai) LoadToTaiShipmentTracking(load models.Load) (ShipmentTrackingUpdateReq, error) {
	// Using ExternalTMSID rather than FreightTrackingID because we're indexing by the TMS's main ID
	shipmentID, err := strconv.Atoi(load.ExternalTMSID)
	if err != nil {
		return ShipmentTrackingUpdateReq{}, fmt.Errorf("error converting freight tracking ID to int: %w", err)
	}

	return ShipmentTrackingUpdateReq{
		ShipmentID: shipmentID,
		TrackingUpdate: TrackingUpdate{
			// Pickup fields
			PickupReadyDateTime:            load.Pickup.ReadyTime.Time,
			PickupAppointmentBeginDateTime: load.Pickup.ApptStartTime.Time,
			PickupAppointmentEndDateTime:   load.Pickup.ApptEndTime.Time,
			// Pickup fields mapped to carrier internally
			ActualPickupArrivalDateTime:   load.Carrier.PickupStart.Time,
			ActualPickupDepartureDateTime: load.Carrier.PickupEnd.Time,
			// Delivery fields
			DeliveryEstimatedDateTime:        load.Consignee.MustDeliver.Time,
			DeliveryAppointmentBeginDateTime: load.Consignee.ApptStartTime.Time,
			DeliveryAppointmentEndDateTime:   load.Consignee.ApptEndTime.Time,
			// Delivery fields mapped to carrier internally
			ProofOfDeliveryDepartureDateTime: load.Carrier.DeliveryStart.Time,
			ProofOfDeliveryArrivalDateTime:   load.Carrier.DeliveryEnd.Time,
		},
	}, nil
}

func (t Tai) LoadToTaiShipmentRefs(load models.Load) ShipmentRefsUpdateReq {
	var refNumbers ShipmentRefsUpdateReq

	if load.Customer.RefNumber != "" {
		refNumbers = append(refNumbers, ReferenceNumber{
			ReferenceType: "Customer",
			Value:         load.Customer.RefNumber,
		})
	}

	return refNumbers
}

func (t Tai) LoadToTaiShipmentTrackingRefs(load models.Load) ShipmentTrackingRefsUpdateReq {
	var trackingRefNumbers []ReferenceNumber

	if load.Pickup.RefNumber != "" {
		trackingRefNumbers = append(trackingRefNumbers, ReferenceNumber{
			ReferenceType: "Pickup",
			Value:         load.Pickup.RefNumber,
		})
	}

	if load.Consignee.RefNumber != "" {
		trackingRefNumbers = append(trackingRefNumbers, ReferenceNumber{
			ReferenceType: "Consignee",
			Value:         load.Consignee.RefNumber,
		})
	}

	return ShipmentTrackingRefsUpdateReq{
		ReferenceNumbers: trackingRefNumbers,
	}
}

func (t Tai) LoadToTaiShipmentReqSimple(load models.Load) (ShipmentUpdateSimpleReq, error) {
	commodities := make([]ShipmentUpdateCommoditySimple, 0)

	for i := range load.Commodities {
		commodities = append(commodities, ShipmentUpdateCommoditySimple{
			WeightTotal:  load.Commodities[i].WeightTotal,
			Description:  load.Commodities[i].Description,
			PiecesTotal:  1,
			FreightClass: "No Class",
		})
	}

	customerID, err := strconv.Atoi(load.Customer.ExternalTMSID)
	if err != nil {
		return ShipmentUpdateSimpleReq{}, fmt.Errorf("error converting customer ID: %w", err)
	}

	return ShipmentUpdateSimpleReq{
		IsCommitted:  false,
		RateShipment: false,
		CustomerID:   customerID,
		ShipmentType: TruckloadShipmentType,
		WeightUnits:  "lbs",
		Stops: []ShipmentUpdateStopSimple{
			{
				ZipCode:  load.Pickup.Zipcode,
				StopType: "First Pickup",
			},
			{
				ZipCode:  load.Consignee.Zipcode,
				StopType: "Last Drop",
			},
		},
		Commodities: commodities,
	}, nil
}

func mapStopToPickup(stop Stop) models.Pickup {
	return models.Pickup{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         stop.CompanyName,
			AddressLine1: stop.StreetAddress,
			AddressLine2: stop.StreetAddressTwo,
			City:         stop.City,
			State:        stop.State,
			Zipcode:      stop.ZipCode,
			Country:      stop.Country,
			Contact:      stop.ContactName,
			Phone:        stop.Phone,
			Email:        stop.Email,
		},
		ApptNote:      stop.Notes,
		ReadyTime:     stop.EstimatedReadyDateTime,
		ApptStartTime: stop.AppointmentReadyDateTime,
		ApptEndTime:   stop.AppointmentCloseDateTime,
	}
}

func mapStopToConsignee(stop Stop) models.Consignee {
	return models.Consignee{
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name:         stop.CompanyName,
			AddressLine1: stop.StreetAddress,
			AddressLine2: stop.StreetAddressTwo,
			City:         stop.City,
			State:        stop.State,
			Zipcode:      stop.ZipCode,
			Country:      stop.Country,
			Contact:      stop.ContactName,
			Phone:        stop.Phone,
			Email:        stop.Email,
		},
		ApptNote:      stop.Notes,
		MustDeliver:   stop.EstimatedCloseDateTime,
		ApptStartTime: stop.AppointmentReadyDateTime,
		ApptEndTime:   stop.AppointmentCloseDateTime,
	}
}

// Unused for now, will come into play once we support updating more info
// func mapPackagingTypeToTai(input string) string {
// 	// Unified mapping: keys are lowercased input, values are canonical TAI values
// 	mapping := map[string]string{
// 		// Your system units mapped to TAI equivalents
// 		"pcs":  "Piece",
// 		"pal":  "Pallet",
// 		"box":  "Box",
// 		"drum": "Drum",
// 		"roll": "Roll",
// 		"skid": "Skid",
// 	}

// 	inputLower := strings.ToLower(input)
// 	if val, ok := mapping[inputLower]; ok {
// 		return val
// 	}

// 	return "Pallet" // default
// }
