package relay

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestExceptionToCheckCall(t *testing.T) {
	vTrue := true
	t.Run("valid exception to check call", func(t *testing.T) {
		exception := models.Exception{
			LoadID:                  123,
			Source:                  "dispatcher",
			Note:                    "Delayed due to traffic",
			IsOnTime:                &vTrue,
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2024, 8, 19, 10, 30, 0, 0, time.UTC), Valid: true},
		}

		freightTrackingID := "FT123456"
		checkCall := exceptionToCheckCall(exception, freightTrackingID)

		require.NotNil(t, checkCall)
		assert.Equal(t, freightTrackingID, checkCall.FreightTrackingID)
		assert.Equal(t, "add tracking note", checkCall.Status)
		assert.Equal(t, exception.Note, checkCall.Notes)
		assert.Equal(t, exception.IsOnTime, checkCall.IsOnTime)
		assert.NotNil(t, checkCall.IsException)
		assert.Equal(t, true, *checkCall.IsException)
		assert.Equal(t, exception.DateTimeWithoutTimezone, checkCall.DateTimeWithoutTimezone)
		assert.Equal(t, exception.Source, checkCall.Source)
	})
}

func TestCheckCallToException(t *testing.T) {
	t.Run("valid check call to exception", func(t *testing.T) {
		checkCall := models.CheckCall{
			LoadID:            123,
			FreightTrackingID: "FT123456",
			Notes:             "Delayed due to traffic",
			IsOnTime:          nil,
			IsException:       nil,
			CapturedDatetime:  models.NullTime{Time: time.Date(2024, 8, 19, 11, 0, 0, 0, time.UTC), Valid: true},
			Source:            "dispatcher",
		}

		loadID := uint(123)
		exception := checkCallToException(checkCall, loadID)

		require.NotNil(t, exception)
		assert.Equal(t, loadID, exception.LoadID)
		assert.Equal(t, checkCall.Author, exception.WhoEntered)
		assert.Equal(t, checkCall.Source, exception.Source)
		assert.Equal(t, checkCall.Notes, exception.Note)
		assert.Equal(t, checkCall.Status, exception.Status)
		assert.Equal(t, checkCall.IsOnTime, exception.IsOnTime)
		assert.Equal(t, checkCall.CapturedDatetime.Time.In(time.UTC).Format(time.RFC3339), exception.DateTime)
	})
}
