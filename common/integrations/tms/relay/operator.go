package relay

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

func (r *Relay) GetUsers(ctx context.Context) (users []models.TMSUser, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetUsersRelay", otel.IntegrationAttrs(r.tms))
	defer func() { metaSpan.End(err) }()

	// Get tracking board HTML to parse users list
	query := url.Values{}
	// Search for a value with no data because otherwise, tracking page contains 1000+ loads
	query.Set("filters[search_term]", "nada")
	query.Set("filters[booked_by_id]", "")
	query.Set("filters[customer_id]", "")
	query.Set("filters[delivery_appt]", "")
	query.Set("filters[pickup_appt]", "")
	query.Set("filters[status]", "all")
	query.Set("sort_by[1][direction]", "desc")
	query.Set("sort_by[1][field]", "booking_id")

	respBody, err := r.getHTML(ctx, "tracking_board", query, s3backup.TypeUsers)
	if err != nil {
		return users, errors.New("error loading tracking board")
	}

	return r.parseUsersFromHTML(respBody)
}

func (r *Relay) parseUsersFromHTML(respBody []byte) ([]models.TMSUser, error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return nil, fmt.Errorf("error reading document: %w", err)
	}

	selectElt := doc.Find("#booked_by_id")

	users := []models.TMSUser{{Username: "Open Board", ExternalTMSID: "Open Board", TMSID: r.tms.ID}}

	selectElt.Find("option").Each(func(_ int, s *goquery.Selection) {
		id := s.AttrOr("value", "")
		if id != "" {
			users = append(users, models.TMSUser{
				Username:      strings.TrimSpace(s.Text()),
				ExternalTMSID: id,
				TMSID:         r.tms.ID,
			})
		}
	})

	if count := len(users); count == 1 {
		return users, errtypes.EntityNotFoundError(r.tms, "", "allUsers")
	}

	return users, nil
}

func (r *Relay) assignOperator(ctx context.Context, updatedLoad *models.Load) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("externalTMSID", updatedLoad.ExternalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "assignOperator", spanAttrs)
	defer func() { metaSpan.End(err) }()

	var modalEvent, formEvent string
	switch strings.ToLower(updatedLoad.Operator) {
	case "":
		modalEvent = ""
		formEvent = "UnassignPlan"

	case "open board":
		modalEvent = ""
		formEvent = "AssignToOpenBoard"

	default:
		modalEvent = "AssignCds"
		formEvent = "AssignCds"
	}

	params, err := r.initPlanningBoardAction(ctx, updatedLoad, modalEvent, false)
	if err != nil {
		return fmt.Errorf("initPlanningBoardAction error: %w", err)
	}

	var updateMsg wsPayload
	switch strings.ToLower(updatedLoad.Operator) {

	case "", "open board":
		updateObj := make([]any, 5)
		updateObj[0] = "4"
		updateObj[1] = "9"
		updateObj[2] = params.PhxID
		updateObj[3] = "event"
		updateObj[4] = map[string]any{
			"type":  "click",
			"event": "action",
			"value": map[string]string{
				"type":                   formEvent,
				"relay_reference_number": updatedLoad.ExternalTMSID,
			},
		}

		updateMsg = wsPayload{Message: updateObj, NumExpectedResponses: 2}

	default:
		operatorID, err := r.getOperatorExternalTMSID(ctx, updatedLoad)
		if err != nil {
			return fmt.Errorf("error mapping username to externalTMSID: %w", err)
		}

		resp, err := r.sendWebSocketMessages(ctx, false, "planning_board/live/websocket",
			params.QueryParams, false, params.ModalObj)
		if err != nil {
			return fmt.Errorf("websocket error opening AssignCDS modal: %w", err)
		}

		assignmentID, planID, err := parseAssignmentIDs(resp)
		if err != nil {
			return err
		}

		updateQuery := url.Values{
			"_csrf_token":                        {params.CSRFToken},
			"assign_cds[plan_id]":                {planID},
			"assign_cds[relay_reference_number]": {updatedLoad.ExternalTMSID},
			"assign_cds[assignment_id]":          {assignmentID},
			"assign_cds[cds]":                    {operatorID},
		}

		updates := PatchSchedulePlan{
			Type:  "form",
			Event: "save",
			Value: updateQuery.Encode(),
			Cid:   6,
		}

		updateObj := make([]any, 5)
		updateObj[0] = "4"
		updateObj[1] = "10"
		updateObj[2] = params.PhxID
		updateObj[3] = "event"
		updateObj[4] = updates

		updateMsg = wsPayload{Message: updateObj, NumExpectedResponses: 1}
	}

	_, err = r.sendWebSocketMessages(ctx, true, "planning_board/live/websocket", params.QueryParams, true, updateMsg)
	if err != nil {
		return fmt.Errorf("error sending operator %s msg: %w", formEvent, err)
	}

	return nil
}

// For unit testing
var dbGetTMSUserFunc = tmsUserDB.GetByUsername

func (r *Relay) getOperatorExternalTMSID(ctx context.Context, updatedLoad *models.Load) (string, error) {
	operator := updatedLoad.Operator

	user, err := dbGetTMSUserFunc(ctx, operator, r.tms.ID)
	if err == nil {
		return user.ExternalTMSID, nil
	}

	log.WarnNoSentry(ctx, "error getting TMSUser from DB, falling back to just-in-time lookup", zap.Error(err))

	users, err := r.GetUsers(ctx)
	if err != nil {
		return "", fmt.Errorf("error getting users from TMS: %w", err)
	}

	for _, user := range users {
		if strings.EqualFold(user.Username, operator) {
			return user.ExternalTMSID, nil
		}
	}

	return "", errtypes.EntityNotFoundError(r.tms, operator, "userExternalTMSID")
}

// Parses necessary assignment and plan ID from Assign CDS modal
func parseAssignmentIDs(input []byte) (assignmentID string, planID string, err error) {
	// Define the regex pattern to match the entire input element and capture the value attribute
	//nolint:lll
	reAssignment := regexp.MustCompile(`input\s+id=\\"assign\-cds\-form_assignment_id\\"\s+name=\\"assign_cds\[assignment_id\]\\"\s+type=\\"hidden\\"\s+value=\\"([a-zA-Z0-9\-]+)\\"`)
	matches := reAssignment.FindStringSubmatch(string(input))

	// Check if the match was found
	if len(matches) < 2 {
		return "", "", errors.New("assignmentID not found")
	}
	assignmentID = matches[1]

	//nolint:lll
	rePlanID := regexp.MustCompile(`input\s+id=\\"assign\-cds\-form_plan_id\\"\s+name=\\"assign_cds\[plan_id\]\\"\s+type=\\"hidden\\"\s+value=\\"([a-zA-Z0-9\-]+)\\"`)
	matches = rePlanID.FindStringSubmatch(string(input))

	// Check if the match was found
	if len(matches) < 2 {
		return "", "", errors.New("planID not found")
	}
	planID = matches[1]

	return assignmentID, planID, nil
}
