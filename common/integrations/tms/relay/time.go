package relay

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/models"
)

var timeNowFunc = time.Now

// The only YYYY-MM-DD format this supports is if the year is 4 digits.
// Otherwise, some variation of MM-DD(-YY(YY?)?) format is assumed.
// 1) to extract the date from the appt reference on the tracking page
// 2) to parse the date-time without timezone on the load detail page
var timeRegexNoTZStr = `` +
	// Leading year:YYYY- or YYYY/
	`(?:` +
	`(?P<leadingYear>\d{4})` +
	`(?P<leadingYearSep>\-|\/)` +
	`)?` +
	`(?P<month>\d{1,2})` + // M or MM
	`(?P<sep1>\-|\/)` + // "-" or "/" date separator
	`(?P<date>\d{1,2})` + // D or DD
	// Support either /YYYY or /YY or nothing
	`(?:` +
	`(?P<sep2>\-|\/)` + // optional second separator
	`(?P<year>\d{2,4})` + // 2 or 4 digit year
	`)?` +
	`(?P<sep3>,?\s+)?` + // ", " date-time separator, allows for multiple spaces
	`(?P<minute>\d{1,2}:\d{2})?` + // hour:minute
	`(?P<second>:\d{2})?` + // :second
	`(?P<sep4>\s+)?` + // " " time-of-day separator, allows for multiple spaces
	`(?P<tod>[AP]{1}[M]{1})?` // AM or PM -- rare in Relay, usually always 24-hr format

var timeRegex = regexp.MustCompile(
	timeRegexNoTZStr +
		`(?P<timezone>[A-Za-z]{2,3})?$`, // timezone abbreviation
)

var timeNoTZRegex = regexp.MustCompile(
	timeRegexNoTZStr, // date-time without timezone
)

// parseCustomDateTime parses a time string.
// 'tzOverride' is an optional timezone to use for the parsed time. If provided, it will
// override the default UTC timezone/the timezone in the timestamp.
// When the year is omitted from the input string, it is inferred based on the current date.
// The function also accepts a `inferFutureYear` hint to handle dates near the end of the year.
// If `inferFutureYear` is true, it assumes the date is in the future (e.g., an ETA).
// If `inferFutureYear` is false, it assumes the date is in the past
// (e.g., a timestamp for an event that already occurred).
//
// Supports time formats such as:
//
//	2023-10-06
//	2023-10-06 8:00
//	10/06/2023
//	10-06-2023 (dashes)
//	10/06/23 (2-digit year)
//	10-06-23 (dashes, 2-digit year)
//	10/06/2023 8:00 (no comma)
//	10-06-23, 8:00 (has comma)
//	10/06 8:00 (no year, no comma) -- most common in Relay
//	10/6 8:00 (no year, no comma) -- most common in Relay
//	10/06/2023, 8:00:00 PM/AM
//	10/06/2023, 8:00 PM/AM (no seconds)
//	10/06/2023, 14:00PM (will be converted to 02:00PM)
//	10/06/2023, 8:00 MST (timezone)
//	10/06/2023, 8:00:00 PM MST (timezone)
//
// See tests for more examples. Adapted from common/models/utils.go
func parseCustomDateTime(timestamp string, inferFutureYear bool, tzOverride *time.Location) (models.NullTime, error) {
	timestamp = strings.TrimSpace(timestamp)
	timestamp = handleHoursOutsideOfRangeGracefully(timestamp)

	groups := timeRegex.FindStringSubmatch(timestamp)
	if len(groups) < 14 {
		return models.NullTime{}, fmt.Errorf("timestamp did not match expected pattern: %q", timestamp)
	}

	// groups[0] is the entire match
	leadingYear := groups[1]
	leadingYearSep := groups[2]
	monthStr := groups[3]
	dateStr := groups[5]
	sep1 := groups[4]
	sep2 := groups[6]
	year := groups[7]
	sep3 := groups[8]
	hourMinute := groups[9]
	second := groups[10]
	sep4 := groups[11]
	tod := groups[12]
	tzAbbr := groups[13]

	// Build layout string
	var layout strings.Builder
	yearProvided := false

	if len(leadingYear) == 4 {
		layout.WriteString("2006")
		layout.WriteString(leadingYearSep)
		yearProvided = true
	}
	layout.WriteString("1")
	layout.WriteString(sep1)
	layout.WriteString("2")
	layout.WriteString(sep2)

	if len(year) == 4 {
		layout.WriteString("2006")
		yearProvided = true
	} else if len(year) == 2 {
		layout.WriteString("06")
		yearProvided = true
	}

	if sep3 != "" {
		layout.WriteString(sep3)
	}
	if hourMinute != "" {
		layout.WriteString("15:04")
	}
	if second != "" {
		layout.WriteString(":05")
	}
	if sep4 != "" {
		layout.WriteString(" ")
	}
	if tod != "" {
		layout.WriteString("PM")
	}

	// Build time string for parsing
	var timeStr strings.Builder
	if leadingYear != "" {
		timeStr.WriteString(leadingYear)
		timeStr.WriteString(leadingYearSep)
	}
	timeStr.WriteString(monthStr)
	timeStr.WriteString(sep1)
	timeStr.WriteString(dateStr)
	timeStr.WriteString(sep2)
	if year != "" {
		timeStr.WriteString(year)
	}
	if sep3 != "" {
		timeStr.WriteString(sep3)
	}
	if hourMinute != "" {
		timeStr.WriteString(hourMinute)
	}
	if second != "" {
		timeStr.WriteString(second)
	}
	if sep4 != "" {
		timeStr.WriteString(sep4)
	}
	if tod != "" {
		timeStr.WriteString(tod)
	}

	loc := time.UTC
	if tzOverride != nil {
		loc = tzOverride
	} else if tzAbbr != "" {
		if l, err := timezone.GetLocationByAbbrv(tzAbbr); err == nil {
			loc = l
		}
	}

	layoutStr := layout.String()
	builtTimeStr := timeStr.String()
	parsedTime, err := time.ParseInLocation(layoutStr, builtTimeStr, loc)
	if err != nil {
		return models.NullTime{}, fmt.Errorf("error parsing time: %w", err)
	}

	// If year is missing, infer it based on current date and future/past logic (default: past)
	if !yearProvided {
		now := timeNowFunc().In(loc)
		parsedTime = time.Date(
			now.Year(),
			parsedTime.Month(),
			parsedTime.Day(),
			parsedTime.Hour(),
			parsedTime.Minute(),
			parsedTime.Second(),
			parsedTime.Nanosecond(),
			loc,
		)

		if inferFutureYear {
			// If the parsed date is in the past, it's likely for the next year.
			// (e.g. Today is 12/25/2024, but the timestamp is 1/1 so we assume it's 1/1/2025)
			if parsedTime.Before(now) {
				parsedTime = parsedTime.AddDate(1, 0, 0)
			}
		} else {
			// If the parsed date is in the future, it's likely from the previous year.
			// Helpful if we're parsing an old load and the year is not provided.
			// But it looks like Relay provides the year if it's before the current year so we shouldn't need this.
			if parsedTime.After(now) {
				parsedTime = parsedTime.AddDate(-1, 0, 0)
			}
		}
	}

	return models.NullTime{Time: parsedTime, Valid: true}, nil
}

// Handles 24-hour format with AM/PM indicators
// Returns the timestamp with the hour converted to 12-hour format if it's outside of the range
func handleHoursOutsideOfRangeGracefully(timestamp string) string {
	hourMinutePattern := regexp.MustCompile(`(\d{1,2}):(\d{2})`)
	matches := hourMinutePattern.FindStringSubmatch(timestamp)

	if len(matches) > 2 && strings.Contains(timestamp, "PM") {
		hourStr := matches[1]
		hour, err := strconv.Atoi(hourStr)
		if err != nil {
			return timestamp
		}

		if hour >= 13 && hour <= 23 {
			// Convert 24-hour format to 12-hour when AM/PM is present
			newHour := hour - 12
			timestamp = strings.Replace(
				timestamp,
				matches[0],
				fmt.Sprintf("%d:%s", newHour, matches[2]),
				1,
			)
		}
	}

	return timestamp
}
