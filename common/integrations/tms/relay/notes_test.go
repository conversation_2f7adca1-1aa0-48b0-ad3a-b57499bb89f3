package relay

import (
	"bytes"
	"context"
	"os"
	"testing"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestParseNotes(t *testing.T) {
	ctx := context.Background()
	html, err := os.ReadFile("./testdata/checkcalls.html")
	require.NoError(t, err)

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(html))
	require.NoError(t, err)

	notes := parseNotes(ctx, doc)

	loc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	// vTrue := true
	vFalse := false

	expected := []models.Note{
		{

			Note:        "Updated DEL ETA 7/16 1800",
			UpdatedBy:   "Jane Doe",
			Source:      "dispatcher",
			IsException: &vFalse,
			CreatedAt: models.NullTime{Time: time.Date(2024, 7, 16, 11, 22, 0, 0, loc),
				Valid: true},
		},
		{
			Note:        "PU ETA 7/13 driver held up at prior load",
			UpdatedBy:   "Jane Doe",
			Source:      "dispatcher",
			IsException: &vFalse,
			CreatedAt: models.NullTime{Time: time.Date(2024, 7, 12, 9, 33, 0, 0, loc),
				Valid: true},
		},
	}

	for i, cc := range notes {
		require.Equalf(t, expected[i], cc, "note %d does not match", i+1)
	}
}
