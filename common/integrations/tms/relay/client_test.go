package relay

import (
	"context"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func startMockWebSocketServer(t *testing.T, handler func(*websocket.Conn)) *httptest.Server {
	h := func(w http.ResponseWriter, r *http.Request) {
		upgrader := websocket.Upgrader{}
		conn, err := upgrader.Upgrade(w, r, nil)
		require.NoError(t, err)
		defer conn.Close()
		handler(conn)
	}
	server := httptest.NewServer(http.HandlerFunc(h))

	return server
}

func TestGetLoadViaWebSocket(t *testing.T) {
	var msgCount uint
	var mu sync.Mutex

	server := startMockWebSocketServer(t, func(conn *websocket.Conn) {
		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				return
			}

			mu.Lock()
			msgCount++
			switch msgCount {
			case 1:
				err = conn.WriteMessage(websocket.TextMessage,
					[]byte(`["3","3","load_board_v2:all","phx_reply",{"status":"ok","response":{}}]`))
				require.NoError(t, err)

			case 2:
				err = conn.WriteMessage(websocket.TextMessage,
					[]byte(`["3",null,"load_board_v2:all","refresh",{"data":{}}]`))
				require.NoError(t, err)

			default:
				log.Printf("received msg %d: %s", msgCount, string(message))
			}
			mu.Unlock()

		}
	})
	defer server.Close()

	host := strings.TrimPrefix(server.URL, "http://")
	wsScheme = "ws"

	relay := &Relay{tms: models.Integration{APIKey: "your-api-key", Name: models.Relay, Tenant: host}}
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	res, err := relay.getLoadViaWebSocket(ctx, "freightTrackingID")

	assert.NoError(t, err)
	assert.Equal(t, `["3",null,"load_board_v2:all","refresh",{"data":{}}]`, string(res))
}
