package relay

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const apiKey = ""
const testLoadID = "2004215"

func TestLiveGetLoadViaWebSocket(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetLoadViaWebSocket: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	load, _, err := r.GetLoad(ctx, testLoadID)
	require.NoError(t, err)
	require.NotEmpty(t, load)

	log.Infof(ctx, "load result: %#v\n\n", load)

}

func TestLiveGetCheckCalls(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetCheckCalls: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)
	checkCalls, err := r.GetCheckCallsHistory(ctx, 0, "7004096")
	require.NoError(t, err)

	for _, cc := range checkCalls {
		log.Infof(ctx, "%#v\n\n", cc)
	}
}

func TestLiveGetLoadHTML(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetLoadHTML: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	load, err := r.parseLoadDetailHTML(ctx, testBookingID)
	require.NoError(t, err)
	require.NotEmpty(t, load)

	log.Infof(ctx, "load result: %#v\n\n", load)

}

func TestLiveGetCarrier(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetCarrier: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	load := models.Load{FreightTrackingID: "8094570"} // aka Booking ID, changes if carrier is changed
	err := r.parseCarrierInfoHTML(ctx, &load)
	require.NoError(t, err)
	require.NotEmpty(t, load)

	log.Infof(ctx, "carrier result: %#v\n\n", load.Carrier)

}

func TestLiveUpdateSchedulePlan(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveUpdateSchedulePlan: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	dt := time.Now().Add(24 * time.Hour)

	load := models.Load{
		// TODO why doesn't 2004215 work?
		ExternalTMSID:     "2004219",
		FreightTrackingID: "7004099",
		LoadCoreInfo: models.LoadCoreInfo{
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{},
				BusinessHours:   "",
				RefNumber:       "PickupRefTest",
				ReadyTime:       models.NullTime{},
				ApptStartTime:   models.NullTime{Time: dt, Valid: true},
				ApptEndTime:     models.NullTime{},
				ApptNote:        "",
				Timezone:        "America/New_York",
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{},
				BusinessHours:   "",
				RefNumber:       "DropoffRefTest",
				ApptStartTime:   models.NullTime{Time: dt.Add(24 * time.Hour), Valid: true},
				ApptEndTime:     models.NullTime{},
				ApptNote:        "",
				Timezone:        "America/Chicago",
			},
		},
	}

	res, _, err := r.UpdateLoad(ctx, &load, &load)
	require.NoError(t, err)

	assert.Equal(t, load.Pickup.RefNumber, res.Pickup.RefNumber)
	require.True(t, res.Pickup.ApptStartTime.Valid)
	assert.WithinDuration(t, load.Pickup.ApptEndTime.Time, res.Pickup.ApptEndTime.Time, 1*time.Second)

	assert.Equal(t, load.Consignee.RefNumber, res.Consignee.RefNumber)
	require.True(t, res.Consignee.ApptStartTime.Valid)
	assert.WithinDuration(t, load.Consignee.ApptEndTime.Time, res.Consignee.ApptEndTime.Time, 1*time.Second)
}

func TestLiveUpdateDriverEquipment(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveUpdateDriverEquipment: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	os.Setenv("DEBUG", "true")
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	load := models.Load{
		ExternalTMSID:     "2004215",
		FreightTrackingID: "7004096",
		LoadCoreInfo: models.LoadCoreInfo{
			Carrier: models.Carrier{
				ExternalTMSID:        "",
				MCNumber:             "",
				DOTNumber:            "",
				Name:                 "",
				Phone:                "",
				Dispatcher:           "",
				SealNumber:           "",
				SCAC:                 "",
				FirstDriverName:      "Jason Statham",
				FirstDriverPhone:     "************",
				SecondDriverName:     "Vin Diesel",
				SecondDriverPhone:    "************",
				Email:                "",
				DispatchCity:         "Louisville",
				DispatchState:        "KY",
				ExternalTMSTruckID:   "TestTruck000",
				ExternalTMSTrailerID: "TestTrailer111",
			},
		},
	}

	start := time.Now()
	err := r.addDriverEquipmentInfo(ctx, &load)
	require.NoError(t, err)
	log.Infof(ctx, "addDriverEquipmentInfo took %v to complete", time.Since(start))

	res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
	require.NoError(t, err)

	assert.Equal(t, load.Carrier.FirstDriverName, res.Carrier.FirstDriverName)
	assert.Equal(t, load.Carrier.FirstDriverPhone, res.Carrier.FirstDriverPhone)
	assert.Equal(t, load.Carrier.SecondDriverName, res.Carrier.SecondDriverName)
	assert.Equal(t, load.Carrier.SecondDriverPhone, res.Carrier.SecondDriverPhone)
	assert.Equal(t, load.Carrier.ExternalTMSTruckID, res.Carrier.ExternalTMSTruckID)
	assert.Equal(t, load.Carrier.ExternalTMSTrailerID, res.Carrier.ExternalTMSTrailerID)
}

func TestLiveAddCarrierContactInfo(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveAddCarrierContactInfo: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	os.Setenv("DEBUG", "true")
	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)

	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	load := models.Load{
		ExternalTMSID:     testLoadID,
		FreightTrackingID: testBookingID,
		LoadCoreInfo: models.LoadCoreInfo{
			Carrier: models.Carrier{
				Phone:      "************",
				Dispatcher: "Test Dispatcher",
				Email:      "<EMAIL>",
				Notes:      "test at " + time.Now().Format(time.RFC3339),
			},
		},
	}

	start := time.Now()
	err := r.addCarrierContactInfo(ctx, &load)
	require.NoError(t, err)
	log.Infof(ctx, "addCarrierContactInfo took %v to complete", time.Since(start))

	res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
	require.NoError(t, err)

	assert.Equal(t, load.Carrier.Dispatcher, res.Carrier.Dispatcher)
	assert.Equal(t, load.Carrier.Phone, res.Carrier.Phone)
	assert.Equal(t, load.Carrier.Email, res.Carrier.Email)
	assert.Equal(t, load.Carrier.Notes, res.Carrier.Notes)
}
