
<!DOCTYPE html><html lang="en">
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta content="Relay TMS" name="description">
    <meta name="author">
<meta content="PSc2Bw1-IQ1cciFJAl0eBgAof0NAcDM8pfgVh-IEeCm-Q-zWq_5126as" name="csrf-token">
    <title>Relay - Tracking Detail • 3306384 • 8355821 • Danbury, CT → Breinigsville, PA</title>
    <link href="/images/favicon/nfi-load-apple.png" rel="apple-touch-icon">
    <link href="/images/favicon/nfi-load.svg" rel="mask-icon">
    <link href="/images/favicon/nfi-load.ico" rel="shortcut icon" type="image/x-icon">
    <link href="/css/app-221052c6f1a499b9306cfce67c44ce17.css?vsn=d" rel="stylesheet">
    <link href="/css/components-5355e4051c50c74454f2f7bd40c4bd48.css?vsn=d" rel="stylesheet">
  </head>
  <body data-spy="scroll" data-target="#toc">
<header class="relay__header">
  <div class="relay__header-logo-wrap">
    <div class="relay__header-logo">
    </div>
    <h1>Tracking Detail • 3306384 • 8355821 • Danbury, CT → Breinigsville, PA</h1>
  </div>
<div></div>
  <span>

Jinyan Zang |
<a data-csrf="PSc2Bw1-IQ1cciFJAl0eBgAof0NAcDM8pfgVh-IEeCm-Q-zWq_5126as" data-method="delete" data-to="/auth/logout" href="/auth/logout" rel="nofollow">Logout</a>

  </span>
</header>

    <div class="container-fluid">
      <div class="row">




        <div class="alert alert-danger mb-0 d-none" id="error-message" role="alert">error</div>
      </div>
      <div class="row">
        <div class="d-flex d-inline-flex flex-grow-1">



          <main class="flex-grow-1" role="main">
<div id="tracking-details" class="load-detail-template tracking-updates">
  <div class="container border rounded px-0">
    <div class="load-detail-content">
<div class="row load-detail-header border-bottom m-0 align-items-center justify-content-between">
  <div class="d-flex align-items-center">
    <div class="ml-3 align-baseline title">
    <i class="icon icon-md nfi-load-eagle"></i>
    <div class="d-inline pl-3">
      <div class="relay-reference-number d-inline">
        3306384
      </div>
      
        <h4 class="text-info pl-3 d-inline">
          8355821
        </h4>
      
    </div>
 </div>
    
    
  <div class="ml-3 pt-1 d-inline">
    TL
  </div>



  <div class="ml-2">
  <div class="p-0 d-inline" data-html="true" data-toggle="tooltip" data-title="" data-placement="left">
  <div class="badge badge-pill badge-nfi-dark-blue">
    
  
  
  </div>
</div>
  </div>


    <div class="d-inline ml-3 pt-1">
      <a href="https://metabase.app.relaytms.com/question/4877-relay-audit-log-by-relay-ref-or-shipment-id?load=3306384" class="" target="_blank" rel="noopener">Audit Log (Metabase)<i class="fa fa-external-link ml-2"></i></a>
    </div>
  </div>
  <div class="d-flex align-items-center mr-2">
    

    
    
  <div class="ml-5 ml-sm-auto mr-2 pb-2 pb-sm-0 d-flex" data-toggle="tooltip" data-html="true" data-template="<div id='tooltip-{@booking_id}' class='tooltip popover mw-100' role='tooltip'>
                      <div class='tooltip-arrow'></div>
                      <div class='tooltip-inner mw-100 bg-white text-darker' style='max-width: 400px;'></div>
                    </div>" data-title="
  &lt;div class=&#39;text-left&#39;&gt;
    &lt;span&gt;&lt;strong&gt;OFFICE: &lt;/strong&gt;TG&lt;/span&gt;
    &lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;primary: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;support: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;support: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;support: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;support: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;sales: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;non: &lt;/strong&gt;<EMAIL>&lt;/span&gt;&lt;span&gt;&lt;strong class=&#39;text-uppercase&#39;&gt;non: &lt;/strong&gt;<EMAIL>&lt;/span&gt;
  &lt;/div&gt;
  ">
    
      
        <div class="customer-initial-icon color-target d-inline-block" title="target">
          <div class="span letter">T</div>
        </div>
      
    
  </div>
  </div>
</div>
      <div class="load-detail-body">
        <div class="row load-detail-stops m-0">
          <div class="col-lg-9">
            <div class="row px-3">

<div class="col-xs-12 col-sm-6 col-md-5 stop-detail">
  <div class="label stop-label">
Origin
  </div>
  <div class="d-flex">
    <div class="stop-detail-group-icon">

        <div class="icon icon-md location-origin"></div>




    </div>
    <div class="location-address d-inline-block">
      <div class="title">
LESSEREVIL INC
      </div>
      <div class="subtitle">
        <div class="address-1">
41 Eagle Rd
        </div>
        <div class="address-2">

        </div>
        <div class="city-state-zip">
Danbury,&nbsp;CT&nbsp;06810
        </div>
        <div class="phone-number">

        </div>


          <div class="stop-detail-appt-time float-none mt-3" title="Appointment Date">
            <div class="stop-detail-group-icon">
              <div class="icon icon-md time"></div>
            </div>
            <div class="stop-appt d-inline-block">

7/03 09:00



                <button class="pl-1 btn btn-outline-info btn-xs btn-xs-mt-n2px ml-1" title="Appt Reference" type="button" data-toggle="popover" data-content="57935558">
                  <i class="fa fa-hashtag" title="Appt Reference"></i>
                </button>
                <div class="d-inline-block pl-1">57935558</div>


            </div>
          </div>

      </div>
    </div>
  </div>
</div>


<div class="col-xs-12 col-sm-6 col-md-5 stop-detail">
  <div class="label stop-label">
Destination
  </div>
  <div class="d-flex">
    <div class="stop-detail-group-icon">




        <div class="icon icon-md location-destination"></div>

    </div>
    <div class="location-address d-inline-block">
      <div class="title">
NFI CROSS DOCK
      </div>
      <div class="subtitle">
        <div class="address-1">
910 Nestle Way
        </div>
        <div class="address-2">

        </div>
        <div class="city-state-zip">
Breinigsville,&nbsp;PA&nbsp;18031
        </div>
        <div class="phone-number">

        </div>


          <div class="stop-detail-appt-time float-none mt-3" title="Appointment Date">
            <div class="stop-detail-group-icon">
              <div class="icon icon-md time"></div>
            </div>
            <div class="stop-appt d-inline-block">

7/03 18:30



                <button class="pl-1 btn btn-outline-info btn-xs btn-xs-mt-n2px ml-1" title="Appt Reference" type="button" data-toggle="popover" data-content="2507033412">
                  <i class="fa fa-hashtag" title="Appt Reference"></i>
                </button>
                <div class="d-inline-block pl-1">2507033412</div>


            </div>
          </div>

      </div>
    </div>
  </div>
</div>


            </div>
            <hr>

            <div class="load-detail-cargo">
              <div class="row px-5">
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Carrier</div>
                  <div class="value">

GS TRUCKING INC
                  </div>

                    <span>USDOT 2514703</span>

                </div>
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Driver</div>

                    <div class="value">Jesus</div>
                    <span>484-538-9561</span>





                    <a class="btn btn-primary mt-1" href="/tracking/text_driver/6b29f1a2-86e7-4b30-ba5c-2e0a7e3668a1" target="_blank">Text Driver(s)</a>

                </div>
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Truck/Trailer Number</div>
                  <div class="value">221</div>
                  <span>3234</span>
                </div>
              </div>
            </div>
            <hr>

            <hr>
            <div class="load-detail-cargo">
              <div class="row px-5">
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Weight</div>
                  <div class="value">6681.0</div>
                </div>
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Pallets</div>
                  <div class="value">0</div>
                </div>
                <div class="col-sm-4 col-md-4 label-value-group">
                  <div class="label">Pieces</div>
                  <div class="value">1354</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="load-detail-money">
              <div class="row px-5">
                <div class="col-sm-6 col-md-4 label-value-group">
                  <div class="label">Miles</div>
                  <div class="value">164.1</div>
                </div>
                <div class="col-sm-6 col-md-4 label-value-group">
                  <div class="label">Shipment Id</div>
                  <div class="value">57935558</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="load-detail-money">
              <div class="row px-5">
                <div class="col-sm-6 col-md-6 label-value-group">
                  <div class="label">Customer Money</div>
                  <div class="value">


  <table>
    <tr>
      <td>

<span class=""><small class="mr-1">USD</small><span class="font-size-largest d-inline">$659.84</span></span>
      </td>
      <td class="pl-3 text-muted font-size-large">
        Total
      </td>
    </tr>

      <tr>
        <td class="text-right font-size-large">
$577.53
        </td>
        <td class="pl-3 text-muted font-size-large">
Linehaul
        </td>
      </tr>

      <tr>
        <td class="text-right font-size-large">
$82.31
        </td>
        <td class="pl-3 text-muted font-size-large">
Fuel surcharge
        </td>
      </tr>


  </table>


                  </div>
                </div>
                <div class="col-sm-6 col-md-6 label-value-group">
                  <div class="label">Vendor Money</div>
                  <div class="value">


  <table>
    <tr>
      <td>

<span class=""><small class="mr-1">USD</small><span class="font-size-largest d-inline">$600.00</span></span>
      </td>
      <td class="pl-3 text-muted font-size-large">
        Total
      </td>
    </tr>

      <tr>
        <td class="text-right font-size-large">
$600.00
        </td>
        <td class="pl-3 text-muted font-size-large">
Linehaul
        </td>
      </tr>


  </table>


                  </div>
                </div>
              </div>
            </div>
            <hr>
            <div class="load-detail-contact mb-0 mb-lg-4">
              <div class="row px-5">
                <div class="col-sm-6 col-md-6 label-value-group mb-2">
                  <div class="label">Tracking Contact</div>
                  <div class="value">Manu</div>
                  <span>484-542-2315</span>
                  <span><EMAIL></span>
                </div>
                <div class="col-sm-6 col-md-6 label-value-group mb-2">
                  <div class="label">Contact notes</div>
                  <div class="value"></div>
                </div>
                <div class="col-sm-6 col-md-6 label-value-group">
                  <div class="label">Additional Contacts</div>



                </div>
              </div>
            </div>
            <hr class="d-block d-lg-none">
            <div class="d-flex w-100">
              <div class="d-none d-sm-inline-block flex-grow-1"></div>
            </div>
          </div>
          <div class="col-lg-3 stop-detail">
            <div class="col text-center">
              <a href="https://app.fourkites.com/tracking/#/loads?ids=8355821&per_page=10" target="_blank" rel="noopener">View Fourkites History<i class="fa fa-external-link ml-2"></i></a>
            </div>

              <div class="update">

  <div class="tracking-update kt-tracking-event" v-pre>
    <div class="kt ">
  <div class="kt kt-flex kt-bg-white kt-gap-2">
    <div class="kt">
  <div class="kt kt-h-full kt-relative kt-mr-1">
    <span class="kt kt-circle kt-w-3.5 kt-h-3.5 kt-bg-gray-200 kt-absolute kt-top-0 kt-left-2.5 kt-rounded-xl">
    </span>
    <div class="kt kt-h-full">
      <div class="kt kt-timeline kt-bg-gray-200 kt-w-[1px] kt-h-full kt-ml-[17px] kt-self-stretch kt-align-center">
      </div>
    </div>
  </div>
</div>
    <div class="kt kt-event-content kt-font-sans kt-pb-8 -kt-mt-1">
      <div class="kt kt-flex kt-gap-2 kt-items-center ">
  <h3 class="kt kt-font-semibold kt-text-sm kt-text-label">
    
      <div class="kt ">
  <div class="kt kt-font-sans kt-inline-flex kt-px-2.5 kt-py-1 kt-rounded-full kt-text-xs kt-leading-4 kt-font-medium kt-relative kt-font-medium kt-uppercase kt-bg-green-100 kt-text-green-800">
    
        
          Marked Delivered
        
      
  </div>
</div>
    
  </h3>
  <div class="kt kt-flex">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_6eGX12MBLRk">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        On Time: False
      </div>
    </div>
  </div>
</div>
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_HoWJZmKyrxA">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Has Issue
      </div>
    </div>
  </div>
</div>
  </div>
</div>
      <div class="kt kt-pl-1.5 kt-pt-1">
        
        
        
        
        
        
        
        <div class="kt kt-bg-gray-100 kt-rounded-sm kt-px-2 kt-mt-1 kt-mb-1.5 kt-py-0.5 kt-text-sm kt-inline-block ">
  
  
  <div class="kt kt-font-sans kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-mr-1">Delivered: </span>7/03 19:00
  </div>
</div>
        
        
        
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_tUar8vQObuE">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M406.5 399.6C387.4 352.9 341.5 320 288 320l-64 0c-53.5 0-99.4 32.9-118.5 79.6C69.9 362.2 48 311.7 48 256C48 141.1 141.1 48 256 48s208 93.1 208 208c0 55.7-21.9 106.2-57.5 143.6zm-40.1 32.7C334.4 452.4 296.6 464 256 464s-78.4-11.6-110.5-31.7c7.3-36.7 39.7-64.3 78.5-64.3l64 0c38.8 0 71.2 27.6 78.5 64.3zM256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-272a40 40 0 1 1 0-80 40 40 0 1 1 0 80zm-88-40a88 88 0 1 0 176 0 88 88 0 1 0 -176 0z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          Web
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Source
      </div>
    </div>
  </div>
</div>
</div>
        <row class="kt kt-flex kt-gap-1 kt-pl-[22px]">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_NjwhyUpaei0">
    
    <p class="kt kt-text-gray-500 kt-text-sm kt-text-nowrap">
      7/03 19:32
    </p>
    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Captured at
      </div>
    </div>
  </div>
</div>
  
</row>
      </div>
    </div>
  </div>
</div>
  </div>

              </div>

              <div class="update">

  <div class="tracking-update kt-tracking-event" v-pre>
    <div class="kt ">
  <div class="kt kt-flex kt-bg-white kt-gap-2">
    <div class="kt">
  <div class="kt kt-h-full kt-relative kt-mr-1">
    <span class="kt kt-circle kt-w-3.5 kt-h-3.5 kt-bg-gray-200 kt-absolute kt-top-0 kt-left-2.5 kt-rounded-xl">
    </span>
    <div class="kt kt-h-full">
      <div class="kt kt-timeline kt-bg-gray-200 kt-w-[1px] kt-h-full kt-ml-[17px] kt-self-stretch kt-align-center">
      </div>
    </div>
  </div>
</div>
    <div class="kt kt-event-content kt-font-sans kt-pb-8 -kt-mt-1">
      <div class="kt kt-flex kt-gap-2 kt-items-center ">
  <h3 class="kt kt-font-semibold kt-text-sm kt-text-label">
    
      <div class="kt ">
  <div class="kt kt-font-sans kt-inline-flex kt-px-2.5 kt-py-1 kt-rounded-full kt-text-xs kt-leading-4 kt-font-medium kt-relative kt-font-medium kt-uppercase kt-bg-green-100 kt-text-green-800">
    
        
          Stop Marked Delivered
        
      
  </div>
</div>
    
  </h3>
  <div class="kt kt-flex">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_gT_w6I5uyRw">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        On Time: False
      </div>
    </div>
  </div>
</div>
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_e2I6Uksg5ac">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Has Issue
      </div>
    </div>
  </div>
</div>
  </div>
</div>
      <div class="kt kt-pl-1.5 kt-pt-1">
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_ceG8tybeJOw">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] !kt-fill-[#9510AC] !kt-w-4 !kt-h-4 !kt-mt-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          <span class="kt kt-font-semibold">NFI CROSS DOCK</span> <br>
          Breinigsville, PA
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Stop
      </div>
    </div>
  </div>
</div>
</div>
        
        
        
        
        
        
        <div class="kt kt-bg-gray-100 kt-rounded-sm kt-px-2 kt-mt-1 kt-mb-1.5 kt-py-0.5 kt-text-sm kt-inline-block kt-ml-5">
  <div class="kt kt-font-sans kt-in-time kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-mr-1 kt-w-7">In: </span>7/03 18:00
  </div>
  <div class="kt kt-font-sans kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-w-7 kt-mr-1">Out: </span>7/03 19:00
  </div>
  
</div>
        
        
        
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_881b2FcsU48">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M406.5 399.6C387.4 352.9 341.5 320 288 320l-64 0c-53.5 0-99.4 32.9-118.5 79.6C69.9 362.2 48 311.7 48 256C48 141.1 141.1 48 256 48s208 93.1 208 208c0 55.7-21.9 106.2-57.5 143.6zm-40.1 32.7C334.4 452.4 296.6 464 256 464s-78.4-11.6-110.5-31.7c7.3-36.7 39.7-64.3 78.5-64.3l64 0c38.8 0 71.2 27.6 78.5 64.3zM256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-272a40 40 0 1 1 0-80 40 40 0 1 1 0 80zm-88-40a88 88 0 1 0 176 0 88 88 0 1 0 -176 0z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          Web
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Source
      </div>
    </div>
  </div>
</div>
</div>
        <row class="kt kt-flex kt-gap-1 kt-pl-[22px]">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_wzELg29ZZF0">
    
    <p class="kt kt-text-gray-500 kt-text-sm kt-text-nowrap">
      7/03 19:32
    </p>
    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Captured at
      </div>
    </div>
  </div>
</div>
  <div class="kt kt-bg-gray-200 kt-rounded-lg kt-text-xs kt-self-center kt-px-1.5">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_MAtBLVTnY6Q">
    
      MR
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Miguel Ramirez
      </div>
    </div>
  </div>
</div>
  </div>
</row>
      </div>
    </div>
  </div>
</div>
  </div>

              </div>

              <div class="update">

  <div class="tracking-update kt-tracking-event" v-pre>
    <div class="kt ">
  <div class="kt kt-flex kt-bg-white kt-gap-2">
    <div class="kt">
  <div class="kt kt-h-full kt-relative kt-mr-1">
    <span class="kt kt-circle kt-w-3.5 kt-h-3.5 kt-bg-gray-200 kt-absolute kt-top-0 kt-left-2.5 kt-rounded-xl">
    </span>
    <div class="kt kt-h-full">
      <div class="kt kt-timeline kt-bg-gray-200 kt-w-[1px] kt-h-full kt-ml-[17px] kt-self-stretch kt-align-center">
      </div>
    </div>
  </div>
</div>
    <div class="kt kt-event-content kt-font-sans kt-pb-8 -kt-mt-1">
      <div class="kt kt-flex kt-gap-2 kt-items-center ">
  <h3 class="kt kt-font-semibold kt-text-sm kt-text-label">
    
      <div class="kt ">
  <div class="kt kt-font-sans kt-inline-flex kt-px-2.5 kt-py-1 kt-rounded-full kt-text-xs kt-leading-4 kt-font-medium kt-relative kt-font-medium kt-uppercase kt-bg-sky-100 kt-text-sky-800">
    
        
          Loaded
        
      
  </div>
</div>
    
  </h3>
  <div class="kt kt-flex">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_vEsQCVJ9PZI">
    
      <svg class="kt-icon kt kt-h-3.5 kt-fill-red-700 kt-mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        On Time: False
      </div>
    </div>
  </div>
</div>
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_DS7fOfZJgdE">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Has Issue
      </div>
    </div>
  </div>
</div>
  </div>
</div>
      <div class="kt kt-pl-1.5 kt-pt-1">
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_K2Vp1TYYMqM">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] !kt-fill-[#0CA789] !kt-w-4 !kt-h-4 !kt-mt-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M182.6 137.4c-12.5-12.5-32.8-12.5-45.3 0l-128 128c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8l256 0c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-128-128z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          <span class="kt kt-font-semibold">LESSEREVIL INC</span> <br>
          Danbury, CT
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Stop
      </div>
    </div>
  </div>
</div>
</div>
        
        
        
        
        
        
        <div class="kt kt-bg-gray-100 kt-rounded-sm kt-px-2 kt-mt-1 kt-mb-1.5 kt-py-0.5 kt-text-sm kt-inline-block kt-ml-5">
  <div class="kt kt-font-sans kt-in-time kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-mr-1 kt-w-7">In: </span>7/03 11:00
  </div>
  <div class="kt kt-font-sans kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-w-7 kt-mr-1">Out: </span>7/03 13:00
  </div>
  
</div>
        
        
        
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_-gj7FnYq2rc">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M406.5 399.6C387.4 352.9 341.5 320 288 320l-64 0c-53.5 0-99.4 32.9-118.5 79.6C69.9 362.2 48 311.7 48 256C48 141.1 141.1 48 256 48s208 93.1 208 208c0 55.7-21.9 106.2-57.5 143.6zm-40.1 32.7C334.4 452.4 296.6 464 256 464s-78.4-11.6-110.5-31.7c7.3-36.7 39.7-64.3 78.5-64.3l64 0c38.8 0 71.2 27.6 78.5 64.3zM256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-272a40 40 0 1 1 0-80 40 40 0 1 1 0 80zm-88-40a88 88 0 1 0 176 0 88 88 0 1 0 -176 0z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          Web
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Source
      </div>
    </div>
  </div>
</div>
</div>
        <row class="kt kt-flex kt-gap-1 kt-pl-[22px]">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_FXdj-VxeClc">
    
    <p class="kt kt-text-gray-500 kt-text-sm kt-text-nowrap">
      7/03 19:32
    </p>
    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Captured at
      </div>
    </div>
  </div>
</div>
  <div class="kt kt-bg-gray-200 kt-rounded-lg kt-text-xs kt-self-center kt-px-1.5">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_SWspXUMu9Ho">
    
      MR
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Miguel Ramirez
      </div>
    </div>
  </div>
</div>
  </div>
</row>
      </div>
    </div>
  </div>
</div>
  </div>

              </div>

              <div class="update">

  <div class="tracking-update kt-tracking-event" v-pre>
    <div class="kt ">
  <div class="kt kt-flex kt-bg-white kt-gap-2">
    <div class="kt">
  <div class="kt kt-h-full kt-relative kt-mr-1">
    <span class="kt kt-circle kt-w-3.5 kt-h-3.5 kt-bg-gray-200 kt-absolute kt-top-0 kt-left-2.5 kt-rounded-xl">
    </span>
    <div class="kt kt-h-full">
      <div class="kt kt-timeline kt-bg-gray-200 kt-w-[1px] kt-h-full kt-ml-[17px] kt-self-stretch kt-align-center">
      </div>
    </div>
  </div>
</div>
    <div class="kt kt-event-content kt-font-sans kt-pb-8 -kt-mt-1">
      <div class="kt kt-flex kt-gap-2 kt-items-center ">
  <h3 class="kt kt-font-semibold kt-text-sm kt-text-label">
    
      <div class="kt ">
  <div class="kt kt-font-sans kt-inline-flex kt-px-2.5 kt-py-1 kt-rounded-full kt-text-xs kt-leading-4 kt-font-medium kt-relative kt-font-medium kt-uppercase kt-bg-green-100 kt-text-green-800">
    
        
          Marked Delivered
        
      
  </div>
</div>
    
  </h3>
  <div class="kt kt-flex">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_LcZ_mTwa_Bs">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        On Time: False
      </div>
    </div>
  </div>
</div>
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_MihqdRmysHU">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Has Issue
      </div>
    </div>
  </div>
</div>
  </div>
</div>
      <div class="kt kt-pl-1.5 kt-pt-1">
        
        
        
        
        
        
        
        <div class="kt kt-bg-gray-100 kt-rounded-sm kt-px-2 kt-mt-1 kt-mb-1.5 kt-py-0.5 kt-text-sm kt-inline-block ">
  
  
  <div class="kt kt-font-sans kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-mr-1">Delivered: </span>7/03 18:59
  </div>
</div>
        
        
        
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_iFylx4Kjn6s">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M406.5 399.6C387.4 352.9 341.5 320 288 320l-64 0c-53.5 0-99.4 32.9-118.5 79.6C69.9 362.2 48 311.7 48 256C48 141.1 141.1 48 256 48s208 93.1 208 208c0 55.7-21.9 106.2-57.5 143.6zm-40.1 32.7C334.4 452.4 296.6 464 256 464s-78.4-11.6-110.5-31.7c7.3-36.7 39.7-64.3 78.5-64.3l64 0c38.8 0 71.2 27.6 78.5 64.3zM256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-272a40 40 0 1 1 0-80 40 40 0 1 1 0 80zm-88-40a88 88 0 1 0 176 0 88 88 0 1 0 -176 0z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          nfi_three_g
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Source
      </div>
    </div>
  </div>
</div>
</div>
        <row class="kt kt-flex kt-gap-1 kt-pl-[22px]">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_bgLzuqMEEXw">
    
    <p class="kt kt-text-gray-500 kt-text-sm kt-text-nowrap">
      7/03 19:01
    </p>
    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Captured at
      </div>
    </div>
  </div>
</div>
  
</row>
      </div>
    </div>
  </div>
</div>
  </div>

              </div>

              <div class="update">

  <div class="tracking-update kt-tracking-event" v-pre>
    <div class="kt ">
  <div class="kt kt-flex kt-bg-white kt-gap-2">
    <div class="kt">
  <div class="kt kt-h-full kt-relative kt-mr-1">
    <span class="kt kt-circle kt-w-3.5 kt-h-3.5 kt-bg-gray-200 kt-absolute kt-top-0 kt-left-2.5 kt-rounded-xl">
    </span>
    <div class="kt kt-h-full">
      <div class="kt kt-timeline kt-bg-gray-200 kt-w-[1px] kt-h-full kt-ml-[17px] kt-self-stretch kt-align-center">
      </div>
    </div>
  </div>
</div>
    <div class="kt kt-event-content kt-font-sans kt-pb-8 -kt-mt-1">
      <div class="kt kt-flex kt-gap-2 kt-items-center ">
  <h3 class="kt kt-font-semibold kt-text-sm kt-text-label">
    
      <div class="kt ">
  <div class="kt kt-font-sans kt-inline-flex kt-px-2.5 kt-py-1 kt-rounded-full kt-text-xs kt-leading-4 kt-font-medium kt-relative kt-font-medium kt-uppercase kt-bg-sky-100 kt-text-sky-800">
    
        
          Arrival at Stop 1
        
      
  </div>
</div>
    
  </h3>
  <div class="kt kt-flex">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_EWYHNiRNXEs">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        On Time: False
      </div>
    </div>
  </div>
</div>
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_9r6_5irOk3M">
    
      
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Has Issue
      </div>
    </div>
  </div>
</div>
  </div>
</div>
      <div class="kt kt-pl-1.5 kt-pt-1">
        
        
        
        
        
        
        
        <div class="kt kt-bg-gray-100 kt-rounded-sm kt-px-2 kt-mt-1 kt-mb-1.5 kt-py-0.5 kt-text-sm kt-inline-block ">
  <div class="kt kt-font-sans kt-in-time kt-text-nowrap kt-flex kt-gap-0.5">
    <span class="kt kt-font-semibold kt-mr-1 ">In: </span>7/03 11:00
  </div>
  
  
</div>
        
        
        
        <div class="kt kt-text-label kt-text-sm  kt-text-label kt-text-sm kt-mb-1">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_ommIkEs689M">
    
    <div class="kt kt-flex kt-gap-2">
      <div class="kt kt-w-3.5">
        <svg class="kt kt-w-3.5 kt-h-3.5 kt-fill-gray-300 kt-mt-[3px] " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc. --><path d="M406.5 399.6C387.4 352.9 341.5 320 288 320l-64 0c-53.5 0-99.4 32.9-118.5 79.6C69.9 362.2 48 311.7 48 256C48 141.1 141.1 48 256 48s208 93.1 208 208c0 55.7-21.9 106.2-57.5 143.6zm-40.1 32.7C334.4 452.4 296.6 464 256 464s-78.4-11.6-110.5-31.7c7.3-36.7 39.7-64.3 78.5-64.3l64 0c38.8 0 71.2 27.6 78.5 64.3zM256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-272a40 40 0 1 1 0-80 40 40 0 1 1 0 80zm-88-40a88 88 0 1 0 176 0 88 88 0 1 0 -176 0z"/></svg>
        
      </div>
      <p class="kt kt-text-wrap">
        
          Dispatcher
        
      </p>
    </div>

    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Source
      </div>
    </div>
  </div>
</div>
</div>
        <row class="kt kt-flex kt-gap-1 kt-pl-[22px]">
  <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_fu7yLsAkzKM">
    
    <p class="kt kt-text-gray-500 kt-text-sm kt-text-nowrap">
      7/03 11:57
    </p>
    
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-right-full kt-top-1/2 kt--translate-y-1/2 kt-mr-3">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--right-1.5 kt-top-1/2 kt--translate-y-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Captured at
      </div>
    </div>
  </div>
</div>
  <div class="kt kt-bg-gray-200 kt-rounded-lg kt-text-xs kt-self-center kt-px-1.5">
    <div class="kt ">
  <div class="kt kt-tooltip-target kt-group/tooltip kt-relative" id="kt-tooltip-_MJUmJpSA3SM">
    
      JB
      
    <div class="kt kt-tooltip-wrapper group-hover/tooltip:!kt-visible kt-invisible kt-absolute kt-origin-center kt-z-40 kt-rounded-md kt-border-solid kt-border kt-border-gray-200 kt-top-full kt-left-1/2 kt--translate-x-1/2 kt-mt-2">
      <div class="kt kt-tooltip-arrow kt-bg-white kt-w-3 kt-h-3 kt-rotate-45 kt-absolute kt-border-solid kt-border kt-border-gray-200 kt-z-[-1] kt-rounded-xs kt--top-1.5 kt-left-1/2 kt--translate-x-1/2">
      </div>
      <div class="kt kt-tooltip-box kt-whitespace-nowrap kt-bg-white kt-p-3 kt-text-sm kt-font-sans kt-w-full kt-h-full kt-rounded-md">
        Juan Buitrago
      </div>
    </div>
  </div>
</div>
  </div>
</row>
      </div>
    </div>
  </div>
</div>
  </div>

              </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', event => {
    const moment = require('moment');
    const components = require('js/components');

    new Vue({
      el: "#tracking-details",
      mounted() {
        $(this.$el).find('[data-toggle="tooltip"]').tooltip({
          container: '#tracking-details',
          placement: 'bottom',
        });
      },
      methods: {
        formatInternalNoteCapturedAt(utcDate) {
          return moment(utcDate).utcOffset(window.USER_TZ).format('MM/DD/YYYY HH:mm');
        },
        formatInternalNoteCapturedAtFromNow(utcDate) {
          return moment(utcDate).utcOffset(window.USER_TZ).fromNow();
        }
      }
    });
  });
  </script>

          </main>
        </div>
      </div>
    </div>
    <script src="/js/app-ec0d1ce772403dc2a87cfca65a1baab9.js?vsn=d"></script>
  </body>
</html>
<script>
  document.addEventListener("DOMContentLoaded", () => {
    require("js/app").App.run();

    if (!!document.querySelector('#sticky-nav > .side-nav-relay')) {
      new Vue({
        el: "#sticky-nav",
        data: {
          navBarClass: '',
          navClass: '',
        },
        created() {
          window.addEventListener('scroll', this.handleScroll)
        },
        destroyed() {
          window.removeEventListener('scroll', this.handleScroll)
        },
        methods: {
          handleScroll() {
            if (window.pageYOffset > 54) {
                this.navClass = 'nav-stuck'
                this.navBarClass = 'nav-scrolled-filter'
            } else {
                this.navClass = ''
                this.navBarClass = ''
            }
          },
        }
      })
    }
  });
</script>
