<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="Relay TMS" name="description" />
    <meta name="author" />
    <meta
      content="dy4SOXcGECJWJj8zKwEYQjInYDoaajMb1WxpDLyr3itfBsVzHM1hiSzu"
      name="csrf-token"
    />
    <title>
      Relay - Tracking Detail • 3011313 • 8081111 • Durham, NC → Tupelo, MS
    </title>
    <link href="/images/favicon/nfi-load-apple.png" rel="apple-touch-icon" />
    <link href="/images/favicon/nfi-load.svg" rel="mask-icon" />
    <link
      href="/images/favicon/nfi-load.ico"
      rel="shortcut icon"
      type="image/x-icon"
    />
    <link
      href="/css/app-577473922e740e1c70e1458003b37647.css?vsn=d"
      rel="stylesheet"
    />
    <link
      href="/css/components-3d8a38832554b63d22e4e008f33a7e59.css?vsn=d"
      rel="stylesheet"
    />
  </head>

  <body data-spy="scroll" data-target="#toc">
    <header class="relay__header"></header>

    <div class="container-fluid">
      <div class="row">
        <div
          class="alert alert-danger mb-0 d-none"
          id="error-message"
          role="alert"
        >
          error
        </div>
      </div>
      <div class="row">
        <div class="d-flex d-inline-flex flex-grow-1">
          <main class="flex-grow-1" role="main">
            <div
              id="tracking-details"
              class="load-detail-template tracking-updates"
            >
              <div class="container border rounded px-0">
                <div class="load-detail-content">
                  <div
                    class="row load-detail-header border-bottom m-0 align-items-center justify-content-between"
                  >
                    <div class="d-flex align-items-center">
                      <div class="ml-3 align-baseline title">
                        <i class="icon icon-md nfi-load-eagle"></i>
                        <div class="d-inline pl-3">
                          <div class="relay-reference-number d-inline">
                            3054047
                          </div>
                          <h4 class="text-info pl-3 d-inline">8081291</h4>
                        </div>
                      </div>
                      <div class="ml-3 pt-1 d-inline">TL</div>
                      <div class="ml-2">
                        <div
                          data-html="true"
                          data-toggle="tooltip"
                          data-title=""
                          data-placement="left"
                          class="p-0 d-inline"
                          data-original-title=""
                          title=""
                        >
                          <div
                            class="badge badge-pill badge-nfi-dark-blue"
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div class="d-flex align-items-center mr-2">
                      <div
                        data-toggle="tooltip"
                        data-html="true"
                        data-template="<div id='tooltip-{@booking_id}' class='tooltip popover mw-100' role='tooltip'>
                      <div class='tooltip-arrow'></div>
                      <div class='tooltip-inner mw-100 bg-white text-darker' style='max-width: 400px;'></div>
                    </div>"
                        data-title="
  <div class='text-left'>
    <span><strong>OFFICE: </strong>OR</span>
    <span><strong class='text-uppercase'>primary: </strong><EMAIL></span><span><strong class='text-uppercase'>support: </strong><EMAIL></span><span><strong class='text-uppercase'>sales: </strong><EMAIL></span>
  </div>
  "
                        class="ml-5 ml-sm-auto mr-2 pb-2 pb-sm-0 d-flex"
                        data-original-title=""
                        title=""
                      >
                        <span class="badge badge-pill badge-secondary"
                          >Test Customer</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="load-detail-body">
                    <div class="row load-detail-stops m-0">
                      <div class="col-lg-9">
                        <div class="row px-3">
                          <div class="col-xs-12 col-sm-6 col-md-5 stop-detail">
                            <div class="label stop-label">Origin</div>
                            <div class="d-flex">
                              <div class="stop-detail-group-icon">
                                <div class="icon icon-md location-origin"></div>
                              </div>
                              <div class="location-address d-inline-block">
                                <div class="title">Test Shipper</div>
                                <div class="subtitle">
                                  <div class="address-1">389 Fitzgerald Rd</div>
                                  <div class="address-2"></div>
                                  <div class="city-state-zip">
                                    Durham,&nbsp;NC&nbsp;27299
                                  </div>
                                  <div class="phone-number"></div>
                                  <div
                                    title="Appointment Date"
                                    class="stop-detail-appt-time float-none mt-3"
                                  >
                                    <div class="stop-detail-group-icon">
                                      <div class="icon icon-md time"></div>
                                    </div>
                                    <div class="stop-appt d-inline-block">
                                      7/12/2024 10:00
                                      <div
                                        class="spacing-arrow ml-1 ml-sm-2 d-inline"
                                      >
                                        <img
                                          src="/images/right-arrow.svg"
                                          height="11px"
                                          width="11px"
                                        />
                                      </div>
                                      <div class="stop-appt-2">
                                        7/12/2024 22:00
                                      </div>
                                      <button
                                        title=""
                                        type="button"
                                        data-toggle="popover"
                                        data-content="301859858"
                                        class="pl-1 btn btn-outline-info btn-xs btn-xs-mt-n2px ml-1"
                                        data-original-title="Appt Reference"
                                      >
                                        <i
                                          title="Appt Reference"
                                          class="fa fa-hashtag"
                                        ></i>
                                      </button>
                                      <div class="d-inline-block pl-1">
                                        301859858
                                      </div>
                                      <div class="d-inline-block pl-1">
                                        <svg
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M2 4.99V14h1v3.358c0 .358.416.646.774.646.358 0 .728-.288.728-.646V14H22V5L2 4.99z"
                                            fill="#000"
                                          ></path>
                                          <path
                                            d="M14 15c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 3c-.522 0-1-.478-1-1.003 0-.526.475-.997 1-.997s1 .471 1 .997c0 .525-.475 1.003-1 1.003zM19 15c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 3c-.522 0-1-.478-1-1.003 0-.526.475-.997 1-.997s1 .471 1 .997c0 .525-.475 1.003-1 1.003z"
                                            fill="#000"
                                          ></path>
                                        </svg>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-xs-12 col-sm-6 col-md-5 stop-detail">
                            <div class="label stop-label">Destination</div>
                            <div class="d-flex">
                              <div class="stop-detail-group-icon">
                                <div
                                  class="icon icon-md location-destination"
                                ></div>
                              </div>
                              <div class="location-address d-inline-block">
                                <div class="title">Test Receiver</div>
                                <div class="subtitle">
                                  <div class="address-1">228 Newbury Dr</div>
                                  <div class="address-2"></div>
                                  <div class="city-state-zip">
                                    Tupelo,&nbsp;MS&nbsp;38671
                                  </div>
                                  <div class="phone-number"></div>
                                  <div
                                    title="Appointment Date"
                                    class="stop-detail-appt-time float-none mt-3"
                                  >
                                    <div class="stop-detail-group-icon">
                                      <div class="icon icon-md time"></div>
                                    </div>
                                    <div class="stop-appt d-inline-block">
                                      7/13/2024 00:01
                                      <div
                                        class="spacing-arrow ml-1 ml-sm-2 d-inline"
                                      >
                                        <img
                                          src="/images/right-arrow.svg"
                                          height="11px"
                                          width="11px"
                                        />
                                      </div>
                                      <div class="stop-appt-2">
                                        7/13/2024 23:00
                                      </div>
                                      <button
                                        title=""
                                        type="button"
                                        data-toggle="popover"
                                        data-content="301859858"
                                        class="pl-1 btn btn-outline-info btn-xs btn-xs-mt-n2px ml-1"
                                        data-original-title="Appt Reference"
                                      >
                                        <i
                                          title="Appt Reference"
                                          class="fa fa-hashtag"
                                        ></i>
                                      </button>
                                      <div class="d-inline-block pl-1">
                                        301859858
                                      </div>
                                      <div class="d-inline-block pl-1">
                                        <svg
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M2 4.99V14h1v3.358c0 .358.416.646.774.646.358 0 .728-.288.728-.646V14H22V5L2 4.99z"
                                            fill="#000"
                                          ></path>
                                          <path
                                            d="M14 15c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 3c-.522 0-1-.478-1-1.003 0-.526.475-.997 1-.997s1 .471 1 .997c0 .525-.475 1.003-1 1.003zM19 15c-1.103 0-2 .897-2 2s.897 2 2 2 2-.897 2-2-.897-2-2-2zm0 3c-.522 0-1-.478-1-1.003 0-.526.475-.997 1-.997s1 .471 1 .997c0 .525-.475 1.003-1 1.003z"
                                            fill="#000"
                                          ></path>
                                        </svg>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <hr />
                        <div class="load-detail-cargo">
                          <div class="row px-5">
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Carrier</div>
                              <div class="value">SUPERFAST TRANSPORT LLC</div>
                            </div>
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Driver</div>
                              <div class="value">Doe, John</div>
                              <span>(888) 577 2211</span>
                              <div class="value">Roman</div>
                              <span> (919) 665 4433.</span>
                              <a
                                href="/tracking/text_driver/27f7c912-eb24-4967-ae1d-171e0ce8e2ea"
                                target="_blank"
                                class="btn btn-primary mt-1"
                                >Text Driver(s)</a
                              >
                            </div>
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Truck/Trailer Number</div>
                              <div class="value">505</div>
                              <span>0001</span>
                            </div>
                          </div>
                        </div>
                        <hr />
                        <hr />
                        <div class="load-detail-cargo">
                          <div class="row px-5">
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Weight</div>
                              <div class="value">230.0</div>
                            </div>
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Pallets</div>
                              <div class="value"></div>
                            </div>
                            <div class="col-sm-4 col-md-4 label-value-group">
                              <div class="label">Pieces</div>
                              <div class="value">1</div>
                            </div>
                          </div>
                        </div>
                        <hr />

                        <div class="load-detail-money">
                          <div class="row px-5">
                            <div class="col-sm-6 col-md-4 label-value-group">
                              <div class="label">Miles</div>
                              <div class="value">652.5</div>
                            </div>
                            <div class="col-sm-6 col-md-4 label-value-group">
                              <div class="label">Shipment Id</div>
                              <div class="value">987678124</div>
                            </div>
                          </div>
                        </div>
                        <hr />
                        <div class="load-detail-money">
                          <div class="row px-5">
                            <div class="col-sm-6 col-md-6 label-value-group">
                              <div class="label">Customer Money</div>
                              <div class="value">
                                <table>
                                  <tbody>
                                    <tr>
                                      <td>
                                        <span
                                          ><small class="mr-1">USD</small
                                          ><span
                                            class="font-size-largest d-inline"
                                            >$1,291.67</span
                                          ></span
                                        >
                                      </td>
                                      <td
                                        class="pl-3 text-muted font-size-large"
                                      >
                                        Total
                                      </td>
                                    </tr>
                                    <tr>
                                      <td class="text-right font-size-large">
                                        $998.00
                                      </td>
                                      <td
                                        class="pl-3 text-muted font-size-large"
                                      >
                                        Linehaul
                                      </td>
                                    </tr>
                                    <tr>
                                      <td class="text-right font-size-large">
                                        $293.67
                                      </td>
                                      <td
                                        class="pl-3 text-muted font-size-large"
                                      >
                                        Fuel surcharge
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div class="col-sm-6 col-md-6 label-value-group">
                              <div class="label">Vendor Money</div>
                              <div class="value">
                                <table>
                                  <tbody>
                                    <tr>
                                      <td>
                                        <span
                                          ><small class="mr-1">USD</small
                                          ><span
                                            class="font-size-largest d-inline"
                                            >$1,200.00</span
                                          ></span
                                        >
                                      </td>
                                      <td
                                        class="pl-3 text-muted font-size-large"
                                      >
                                        Total
                                      </td>
                                    </tr>
                                    <tr>
                                      <td class="text-right font-size-large">
                                        $1,200.00
                                      </td>
                                      <td
                                        class="pl-3 text-muted font-size-large"
                                      >
                                        Linehaul
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                        <hr />
                        <div class="load-detail-contact mb-0 mb-lg-4">
                          <div class="row px-5">
                            <div
                              class="col-sm-6 col-md-6 label-value-group mb-2"
                            >
                              <div class="label">Tracking Contact</div>
                              <div class="value">Jane Doe</div>
                              <span>222-414-5500</span>
                              <span><EMAIL></span>
                            </div>
                            <div
                              class="col-sm-6 col-md-6 label-value-group mb-2"
                            >
                              <div class="label">Contact notes</div>
                              <div class="value">
                                Contact dispatcher via email only
                              </div>
                            </div>
                            <div class="col-sm-6 col-md-6 label-value-group">
                              <div class="label">Additional Contacts</div>
                            </div>
                          </div>
                        </div>
                        <hr class="d-block d-lg-none" />
                        <div class="d-flex w-100">
                          <div
                            class="d-none d-sm-inline-block flex-grow-1"
                          ></div>
                        </div>
                      </div>

                      <div class="col-lg-3 stop-detail">
                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Marked Delivered</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>
                            <div class="delivery-time">
                              Delivery Time 7/16/2024 19:00
                            </div>
                            <div class="has-issue">Issue:</div>
                            <div class="notes">
                              Notes:
                              <span></span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/17/2024 09:33 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">
                                Stop Marked Delivered
                              </div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>

                            <div class="name">
                              Receiver:
                              <span>SOUTHHAVEN WAREHOUSE</span>
                              <span>Southaven, MS</span>
                            </div>

                            <div class="in-date-time">In: 7/16/2024 18:00</div>
                            <div class="out-date-time">
                              Out: 7/16/2024 19:00
                            </div>
                            <div class="user">
                              Stop Marked Delivered By User: Jane Doe
                            </div>
                            <div class="notes">
                              Notes:
                              <span></span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/17/2024 09:33 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Note Captured</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>
                            <div class="user">Notes By User: Jane Doe</div>
                            <div class="issue">Issue: false</div>
                            <div class="on-time">On Time:</div>
                            <div class="notes">
                              Note:
                              <span>Updated DEL ETA 7/16 1800</span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/16/2024 11:22 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">In Transit</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>
                            <div class="relay-reference">
                              Relay Reference Number: 1234567
                            </div>
                            <div class="user">
                              Update Captured By User: Jane Doe
                            </div>
                            <div class="truck-location">Truck Location:</div>
                            <div class="truck-location-date-time">
                              Datetime: 7/16/2024 10:00
                            </div>
                            <div class="eta">ETA: 7/16/2024 18:00</div>
                            <div class="notes">
                              Notes:
                              <span>Updated DEL ETA 1800</span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/16/2024 11:22 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">In Transit</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>
                            <div class="relay-reference">
                              Relay Reference Number: 1234567
                            </div>
                            <div class="user">
                              Update Captured By User: Jane Doe
                            </div>
                            <div class="truck-location">Truck Location:</div>
                            <div class="truck-location-date-time">
                              Datetime: 7/15/2024 08:30
                            </div>
                            <div class="eta">ETA: 7/15/2024 20:00</div>
                            <div class="notes">
                              Notes:
                              <span>DEL ETA 7/15 20:00</span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/15/2024 09:41 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Marked Loaded</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source:
                                  four_kites_status_update_process_manager
                                </div>
                              </span>
                            </div>

                            <div class="name">
                              Shipper:
                              <span>NEWBURY MILL</span>
                              <span>Linwood, NC</span>
                            </div>

                            <div class="in-date-time">In: 7/13/2024 19:32</div>
                            <div class="out-date-time">
                              Out: 7/13/2024 20:30
                            </div>
                            <div class="user">Marked Loaded By User:</div>
                            <div class="notes">
                              Notes:
                              <span>Marked loaded by FourKites</span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/13/2024 20:31 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Departed from Stop</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source:
                                  four_kites_status_update_process_manager
                                </div>
                              </span>
                            </div>
                            <div class="relay-reference">
                              Relay Reference Number: 1234567
                            </div>
                            <div class="stop-sequence">Stop: 1</div>
                            <div class="out-date-time">
                              Out Datetime: 7/13/2024 20:30
                            </div>
                            <div class="on-time">On Time:</div>
                            <div class="captured-at">
                              Captured at: 7/13/2024 20:31 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Arrival At Stop</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source:
                                  four_kites_status_update_process_manager
                                </div>
                              </span>
                            </div>
                            <div class="relay-reference">
                              Relay Reference Number: 1234567
                            </div>
                            <div class="stop-sequence">Stop: 1</div>
                            <div class="user">Marked Arrived By User:</div>
                            <div class="in-date-time">
                              In Datetime: 7/13/2024 19:32
                            </div>
                            <div class="on-time">On Time:</div>
                            <div class="notes">
                              Notes:
                              <span>Marked arrived at stop via FourKites</span>
                            </div>
                            <div class="captured-at">
                              Captured at: 7/13/2024 19:33 CST
                            </div>
                          </div>
                        </div>

                        <div class="update">
                          <div class="tracking-update">
                            <div class="update-type">
                              <div class="update-title">Note Captured</div>
                              <span class="d-inline-block">
                                <div class="source d-inline">
                                  Source: dispatcher
                                </div>
                              </span>
                            </div>
                            <div class="user">Notes By User: Jane Doe</div>
                            <div class="issue">Issue: false</div>
                            <div class="on-time">On Time:</div>
                            <div class="notes">
                              Note:
                              <span
                                >PU ETA 7/13 driver held up at prior load</span
                              >
                            </div>
                            <div class="captured-at">
                              Captured at: 7/12/2024 09:33 CST
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  </body>
</html>
