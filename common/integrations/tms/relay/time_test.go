package relay

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseCustomDateTime_Year(t *testing.T) {
	defer func() { timeNowFunc = time.Now }()

	// Fixed reference time: July 1, 2024, 12:00 UTC
	mockNow := time.Date(2024, 7, 1, 12, 0, 0, 0, time.UTC)
	timeNowFunc = func() time.Time { return mockNow }

	tests := []struct {
		name            string
		input           string
		inferFutureYear bool
		tzOverride      *time.Location
		expected        time.Time
		expectsError    bool
	}{
		// Year format tests
		{
			name:            "Full year",
			input:           "6/20/2023 20:20",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Short year",
			input:           "6/20/23 20:20",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "No year, inferFutureYear=false, date in past",
			input:           "6/20 20:20",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2024, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "No year, inferFutureYear=false, date in future (should go to previous year)",
			input:           "12/31 23:59",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 12, 31, 23, 59, 0, 0, time.UTC),
		},
		{
			name:            "No year, inferFutureYear=true, date in future",
			input:           "8/15 10:00",
			inferFutureYear: true,
			tzOverride:      nil,
			expected:        time.Date(2024, 8, 15, 10, 0, 0, 0, time.UTC),
		},
		{
			name:            "No year, inferFutureYear=true, date in past (should go to next year)",
			input:           "1/5 08:00",
			inferFutureYear: true,
			tzOverride:      nil,
			expected:        time.Date(2025, 1, 5, 8, 0, 0, 0, time.UTC),
		},
		{
			name:            "YYYY-MM-DD format",
			input:           "2023-10-06 20:20",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 10, 6, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "YYYY-MM-DD format, no time",
			input:           "2023-10-06",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 10, 6, 0, 0, 0, 0, time.UTC),
		},
		{
			// YYYY/MM/DD format is unlikely but also valid; defining characteristic is that the first # is 4 digits
			name:            "YYYY/MM/DD format",
			input:           "2023/10/06 20:11",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 10, 6, 20, 11, 0, 0, time.UTC),
		},
		{
			name:            "YYYY/MM/DD format, no time",
			input:           "2023/10/06",
			inferFutureYear: false,
			tzOverride:      nil,
			expected:        time.Date(2023, 10, 6, 0, 0, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parsed, err := parseCustomDateTime(tt.input, tt.inferFutureYear, tt.tzOverride)
			if tt.expectsError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, parsed.Time)
			}
		})
	}
}

func TestParseCustomDateTime_Timezone(t *testing.T) {
	defer func() { timeNowFunc = time.Now }()

	mockNow := time.Date(2024, 7, 1, 12, 0, 0, 0, time.UTC)
	timeNowFunc = func() time.Time { return mockNow }

	pstLoc, err := time.LoadLocation("America/Los_Angeles")
	require.NoError(t, err)
	cstLoc, err := time.LoadLocation("America/Chicago")
	require.NoError(t, err)

	tests := []struct {
		name         string
		input        string
		inferredYear bool
		tzOverride   *time.Location
		expected     time.Time
		expectedLoc  *time.Location
		expectsError bool
	}{
		{
			name:         "Explicit CST timezone, full year",
			input:        "1/5/2025 08:00 CST",
			inferredYear: false,
			tzOverride:   nil,
			expected:     time.Date(2025, 1, 5, 8, 0, 0, 0, cstLoc),
			expectedLoc:  cstLoc,
		},
		{
			name:         "Explicit PST timezone, full year",
			input:        "1/5/2025 08:00 PST",
			inferredYear: false,
			tzOverride:   nil,
			expected:     time.Date(2025, 1, 5, 8, 0, 0, 0, pstLoc),
			expectedLoc:  pstLoc,
		},
		{
			name:         "Explicit CST timezone, no year, inferFutureYear=false",
			input:        "6/20 20:20 CST",
			inferredYear: false,
			tzOverride:   nil,
			expected:     time.Date(2024, 6, 20, 20, 20, 0, 0, cstLoc),
			expectedLoc:  cstLoc,
		},
		{
			name:         "Explicit PST timezone, no year, inferFutureYear=true",
			input:        "1/5 08:00 PST",
			inferredYear: true,
			tzOverride:   nil,
			expected:     time.Date(2025, 1, 5, 8, 0, 0, 0, pstLoc),
			expectedLoc:  pstLoc,
		},
		{
			name:         "Override timezone parameter takes precedence",
			input:        "1/5/2025 08:00 CST",
			inferredYear: false,
			tzOverride:   pstLoc,
			expected:     time.Date(2025, 1, 5, 8, 0, 0, 0, pstLoc),
			expectedLoc:  pstLoc,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parsed, err := parseCustomDateTime(tt.input, tt.inferredYear, tt.tzOverride)
			if tt.expectsError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, parsed.Time)
				assert.Equal(t, tt.expectedLoc, parsed.Time.Location())
			}
		})
	}
}

func TestParseCustomDateTime_Separators(t *testing.T) {
	defer func() { timeNowFunc = time.Now }()

	mockNow := time.Date(2024, 7, 1, 12, 0, 0, 0, time.UTC)
	timeNowFunc = func() time.Time { return mockNow }

	tests := []struct {
		name            string
		input           string
		inferFutureYear bool
		expected        time.Time
		expectsError    bool
	}{
		{
			name:            "All slashes, 4-digit year",
			input:           "6/20/2023 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "All hyphens, 4-digit year",
			input:           "6-20-2023 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Mixed slash-hyphen, 4-digit year",
			input:           "6/20-2023 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Mixed hyphen-slash, 4-digit year",
			input:           "6-20/2023 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "All slashes, 2-digit year",
			input:           "6/20/23 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "All hyphens, 2-digit year",
			input:           "6-20-23 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Mixed slash-hyphen, 2-digit year",
			input:           "6/20-23 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Mixed hyphen-slash, 2-digit year",
			input:           "6-20/23 20:20",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "All slashes, no year",
			input:           "6/20 20:20",
			inferFutureYear: false,
			expected:        time.Date(2024, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "All hyphens, no year",
			input:           "6-20 20:20",
			inferFutureYear: false,
			expected:        time.Date(2024, 6, 20, 20, 20, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parsed, err := parseCustomDateTime(tt.input, tt.inferFutureYear, nil)
			if tt.expectsError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, parsed.Time)
				assert.True(t, parsed.Valid)
			}
		})
	}
}

func TestParseCustomDateTime_EdgeCases(t *testing.T) {
	defer func() { timeNowFunc = time.Now }()

	mockNow := time.Date(2024, 7, 1, 12, 0, 0, 0, time.UTC)
	timeNowFunc = func() time.Time { return mockNow }

	tests := []struct {
		name            string
		input           string
		inferFutureYear bool
		expected        time.Time
		expectsError    bool
	}{
		{
			name:            "Extra whitespace and comma",
			input:           " 6/20/2023,   20:20  ",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "Comma and AM",
			input:           "6/20/2023, 8:20 AM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 8, 20, 0, 0, time.UTC),
		},
		{
			name:            "Comma and PM",
			input:           "6/20/2023, 8:20 PM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "No seconds, AM",
			input:           "6/20/2023, 8:20AM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 8, 20, 0, 0, time.UTC),
		},
		{
			name:            "No seconds, PM",
			input:           "6/20/2023, 8:20PM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 0, 0, time.UTC),
		},
		{
			name:            "With seconds, AM",
			input:           "6/20/2023, 8:20:05 AM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 8, 20, 5, 0, time.UTC),
		},
		{
			name:            "With seconds, PM",
			input:           "6/20/2023, 8:20:05 PM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 20, 20, 5, 0, time.UTC),
		},
		{
			name:            "24-hour format with PM (should convert 14:00 PM to 2:00 PM)",
			input:           "6/20/2023, 14:00 PM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 14, 0, 0, 0, time.UTC),
		},
		{
			name:            "24-hour format with AM (should be 14:00)",
			input:           "6/20/2023, 14:00 AM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 20, 14, 0, 0, 0, time.UTC),
		},
		{
			name:            "Odd but valid: single digit month and day, AM",
			input:           "6/2/2023, 8:20 AM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 2, 8, 20, 0, 0, time.UTC),
		},
		{
			name:            "Odd but valid: single digit month and day, PM",
			input:           "6/2/2023, 8:20 PM",
			inferFutureYear: false,
			expected:        time.Date(2023, 6, 2, 20, 20, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parsed, err := parseCustomDateTime(tt.input, tt.inferFutureYear, nil)
			if tt.expectsError {
				require.Error(t, err, "expected error for input: %q, got error: %v, result: %v", tt.input, err, parsed)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tt.expected, parsed.Time)
				assert.True(t, parsed.Valid)
			}
		})
	}
}
