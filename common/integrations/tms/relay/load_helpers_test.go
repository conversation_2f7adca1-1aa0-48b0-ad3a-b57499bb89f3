package relay

import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseTrackingBoardIDs(t *testing.T) {
	tests := []struct {
		name                 string
		input                []byte
		stopIDsRequiredInput bool
		expectedLoadID       string
		expectedCustomerID   string
		expectedStopIDs      []string
		expectedErr          error
	}{
		{
			name: "OK",
			input: []byte(
				//nolint:lll
				` "0": {
				"0": " phx-value-truck_load_thing_id=\"a8058787-471f-4013-b619-3c5149023f4f\"",
				"1": " phx-value-customer_id=\"allbirds\"",
				"<option value=\"6be50db0-3d95-4a86-3571-35463572\">1. AngryBirds, KY</option><option value=\"845f4879-f1b7-4165-a935-92359235\">2. SSC Elk Village, IL</option>"
			}`),
			stopIDsRequiredInput: true,
			expectedLoadID:       "a8058787-471f-4013-b619-3c5149023f4f",
			expectedCustomerID:   "allbirds",
			expectedStopIDs: []string{
				"6be50db0-3d95-4a86-3571-35463572",
				"845f4879-f1b7-4165-a935-92359235",
			},
			expectedErr: nil,
		},

		{
			name: "both IDs empty",
			input: []byte(` "0": {
				"0": " phx-value-truck_load_thing_id=\"\"",
				"1": " phx-value-customer_id=\"\""
			}`),
			stopIDsRequiredInput: false,
			expectedLoadID:       "",
			expectedCustomerID:   "",
			expectedStopIDs:      nil,
			expectedErr:          errors.New("parsed empty truckLoadThingID () and/or customerID ()"),
		},
		// Should not happen in production unless Relay UI changes
		{
			name: "empty customerID",
			input: []byte(` "0": {
				"0": " phx-value-truck_load_thing_id=\"a8058787-471f-4013-b619-3c5149023f4f\"",
				"1": " phx-value-customer_id=\"\""
			}`),
			stopIDsRequiredInput: false,
			expectedLoadID:       "a8058787-471f-4013-b619-3c5149023f4f",
			expectedCustomerID:   "",
			expectedStopIDs:      nil,
			expectedErr: errors.New(
				"parsed empty truckLoadThingID (a8058787-471f-4013-b619-3c5149023f4f) and/or customerID ()"),
		},
		// Should not happen in production unless Relay UI changes
		{
			name: "empty truckLoadThingID",
			input: []byte(` "0": {
				"0": " phx-value-truck_load_thing_id=\"\"",
				"1": " phx-value-customer_id=\"allbirds\""
			}`),
			stopIDsRequiredInput: false,
			expectedLoadID:       "",
			expectedCustomerID:   "allbirds",
			expectedStopIDs:      nil,
			expectedErr:          errors.New("parsed empty truckLoadThingID () and/or customerID (allbirds)"),
		},
		{
			name: "only 1 stopID",
			input: []byte(
				`"0": {
				"0": " phx-value-truck_load_thing_id=\"a8058787-471f-4013-b619-3c5149023f4f\"",
				"1": " phx-value-customer_id=\"allbirds\"",
				"<option value=\"6be50db0-3d95-4a86-3571-35463572\">1. AngryBirds, KY</option>"
			}`),
			stopIDsRequiredInput: true,
			expectedLoadID:       "a8058787-471f-4013-b619-3c5149023f4f",
			expectedCustomerID:   "allbirds",
			expectedStopIDs:      nil,
		},
	}

	for i, test := range tests {
		loadThingID, customerID, err := parseTrackingBoardIDs(test.input)
		if test.expectedErr != nil {
			require.Error(t, err, "test case %d", i)
			require.Equal(t, err, test.expectedErr, "test case %d", i)
		} else {
			require.NoError(t, err, "test case %d", i)
		}
		assert.Equal(t, test.expectedLoadID, loadThingID, "test case %d", i)
		assert.Equal(t, test.expectedCustomerID, customerID, "test case %d", i)
	}
}

func TestParseScheduleID(t *testing.T) {
	tests := []struct {
		name          string
		input         []byte
		want          scheduleParams
		expectedError error
	}{
		{
			name: "OK",
			input: []byte(
				//nolint:lll
				`
				{
					"3": {
						"0": "<input id=\"schedule-plan-form_relay_reference_number\" name=\"schedule_plan[relay_reference_number]\" type=\"hidden\" value=\"2004215\">",
						"1": "<input id=\"schedule-plan-form_schedule_id\" name=\"schedule_plan[schedule_id]\" type=\"hidden\" value=\"eab81951-15eb-484a-af83-75a24f95afec\">",
						<input id=\"schedule-plan-form_stops_0_stop_id\" name=\"schedule_plan[stops][0][stop_id]\" type=\"hidden\" value=\"10153295-4285-403e-b86a-3c46c2d18f77\">",
						"1":"<input id=\"schedule-plan-form_stops_0_stop_type\" name=\"schedule_plan[stops][0][stop_type]\" type=\"hidden\" value=\"pickup\">","2":"<input id=\"schedule-plan-form_stops_0_state\" 
						name=\"schedule_plan[stops][0][state]\" type=\"hidden\" value=\"display\">","3":"1","4":"2","5":"RISE BAKING COMPANY - EAGAN MN","6":{"0":"<label for=\"schedule-plan-form_stops_0_schedule_type\">
						Schedule Type</label>","1":"<input id=\"hidden_schedule_type_0\" name=\"schedule_plan[stops][0][schedule_type]\" type=\"hidden\" value=\"appointment\">",
						"2":"<select class=\"schedule-type form-control custom-select\" disabled id=\"schedule_type_0\" name=\"schedule_plan[stops][0][schedule_type]\" phx-target=\"#modalContents\" 
						phx-valid-stop-id=\"10153295-4285-403e-b86a-3c46c2d18f77\"><option selected value=\"appointment\">Appointment</option><option value=\"fcfs\">FCFS</option>
						<option value=\"drop trailer pickup\">Drop Trailer Pickup</option></select>","s":2},"7":"","8":"","9":"","10":{"0":7,"s":3},"11":"","12":{"0":" phx-value-index=\"0\"",
						"1":" phx-target=\"6\"","2":"0","3":" phx-value-index=\"0\"","4":" phx-target=\"6\"","5":"0","s":4},"s":5}],[{"0":{"d":[[" name=\"schedule_plan[stops][1][_persistent_id]\""," value=\"1\""]],"s":0},"s":1},
						{"0":"<input id=\"schedule-plan-form_stops_1_stop_id\" name=\"schedule_plan[stops][1][stop_id]\" type=\"hidden\" value=\"1c6495af-c864-4e15-90d3-3b40a0b36ca2\">",
						"1":"<input id=\"schedule-plan-form_stops_1_stop_type\" name=\"schedule_plan[stops][1][stop_type]\" type=\"hidden\" value=\"delivery\">","2":"<input id=\"schedule-plan-form_stops_1_state\" 
						name=\"schedule_plan[stops][1][state]\" type=\"hidden\" value=\"display\">","3":"2","4":"2","5":"DINGMANS DAIRY","6":{"0":"<label for=\"schedule-plan-form_stops_1_schedule_type\">Schedule Type</label>",
						"1":"<input id=\"hidden_schedule_type_1\" name=\"schedule_plan[stops][1][schedule_type]\" type=\"hidden\" value=\"appointment\">","2":"<select class=\"schedule-type form-control custom-select\" disabled id=\"schedule_type_1\" name=\"schedule_plan[stops][1][schedule_type]\" phx-target=\"#modalContents\" phx-valid-stop-id=\"1c6495af-c864-4e15-90d3-3b40a0b36ca2\">`),
			want: scheduleParams{
				ScheduleID: "eab81951-15eb-484a-af83-75a24f95afec",
				OrderedStopIDs: []string{
					"10153295-4285-403e-b86a-3c46c2d18f77",
					"1c6495af-c864-4e15-90d3-3b40a0b36ca2"},
			},
			expectedError: nil,
		},
		{
			name: "missing schedule ID input",
			input: []byte(
				//nolint:lll
				`
				{
					"3": {
						"0": "<input id=\"schedule-plan-form_relay_reference_number\" name=\"schedule_plan[relay_reference_number]\" type=\"hidden\" value=\"2004215\">",
						"1": "",
						"2": "",
						"3": {
			}`),
			want:          scheduleParams{},
			expectedError: errors.New("unexpected scheduleMatches regex matches length: 0"),
		},
		{
			name: "empty schedule ID",
			input: []byte(
				//nolint:lll
				`
				{
					"3": {
						"0": "<input id=\"schedule-plan-form_relay_reference_number\" name=\"schedule_plan[relay_reference_number]\" type=\"hidden\" value=\"2004215\">",
						"1": "<input id=\"schedule-plan-form_schedule_id\" name=\"schedule_plan[schedule_id]\" type=\"hidden\" value=\"\">",
						"2": "",
						"3": {
			}`),
			want:          scheduleParams{ScheduleID: ""},
			expectedError: errors.New("parsed empty scheduleID"),
		},
		{
			name: "invalid scheduleID input",
			input: []byte(
				//nolint:lll
				`
				{
					"3": {
						"0": "<input id=\"schedule-plan-form_relay_reference_number\" name=\"schedule_plan[relay_reference_number]\" type=\"hidden\" value=\"2004215\">",
						"1": "<input id=\"other-form_schedule_id\" name=\"other[schedule_id]\" type=\"hidden\" value=\"\">",
						"2": "",
						"3": {
			}`),
			want:          scheduleParams{},
			expectedError: errors.New("unexpected scheduleMatches regex matches length: 0"),
		},
		{
			name: "invalid stopIDs input",
			input: []byte(
				//nolint:lll
				`
				{
					"3": {
						"0": "<input id=\"schedule-plan-form_relay_reference_number\" name=\"schedule_plan[relay_reference_number]\" type=\"hidden\" value=\"2004215\">",
						"1": "<input id=\"schedule-plan-form_schedule_id\" name=\"schedule_plan[schedule_id]\" type=\"hidden\" value=\"eab81951-15eb-484a-af83-75a24f95afec\">",
						<input id=\"schedule-plan-form_stops_0_stop_id\" name=\"schedule_plan[stops][0][stop_id]\" type=\"hidden\" value=\"10153295-4285-403e-b86a-3c46c2d18f77\">",
						"1":"<input id=\"schedule-plan-form_stops_0_stop_type\" name=\"schedule_plan[stops][0][stop_type]\" type=\"hidden\" value=\"pickup\">","2":"<input id=\"schedule-plan-form_stops_0_state\" 
						name=\"schedule_plan[stops][0][state]\" type=\"hidden\" value=\"display\">","3":"1","4":"2","5":"RISE BAKING COMPANY - EAGAN MN","6":{"0":"<label for=\"schedule-plan-form_stops_0_schedule_type\">
						Schedule Type</label>","1":"<input id=\"hidden_schedule_type_0\" name=\"schedule_plan[stops][0][schedule_type]\" type=\"hidden\" value=\"appointment\">",
						"2":"<select class=\"schedule-type form-control custom-select\" disabled id=\"schedule_type_0\" name=\"schedule_plan[stops][0][schedule_type]\" phx-target=\"#modalContents\" 
						phx-valid-stop-id=\"10153295-4285-403e-b86a-3c46c2d18f77\"><option selected value=\"appointment\">Appointment</option><option value=\"fcfs\">FCFS</option>
						<option value=\"drop trailer pickup\">Drop Trailer Pickup</option></select>","s":2},"7":"","8":"","9":"","10":{"0":7,"s":3},"11":"","12":{"0":" phx-value-index=\"0\"",
						"1":" phx-target=\"6\"","2":"0","3":" phx-value-index=\"0\"","4":" phx-target=\"6\"","5":"0","s":4},"s":5}],[{"0":{"d":[[" name=\"schedule_plan[stops][1][_persistent_id]\""," value=\"1\""]],"s":0},"s":1},
						`),
			want: scheduleParams{ScheduleID: "eab81951-15eb-484a-af83-75a24f95afec"},
			expectedError: fmt.Errorf("unexpected number of stopID matches: %d (%v)", 1,
				[][]string{
					//nolint:lll
					{`<input id=\"schedule-plan-form_stops_0_stop_id\" name=\"schedule_plan[stops][0][stop_id]\" type=\"hidden\" value=\"10153295-4285-403e-b86a-3c46c2d18f77\"`,
						"10153295-4285-403e-b86a-3c46c2d18f77"}}),
		},
	}

	for i, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			actual, err := parseSchedulePlanIDs(test.input)
			if test.expectedError != nil {
				require.Error(t, err, "test case %d", i)
				require.Equal(t, test.expectedError, err, "test case: %s", test.name)
			} else {
				require.NoError(t, err, "test case: %s", test.name)
			}
			require.Equal(t, test.want, actual, "test case: %s", test.name)
		})
	}
}
