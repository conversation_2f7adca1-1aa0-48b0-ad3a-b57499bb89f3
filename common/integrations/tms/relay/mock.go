package relay

import (
	"context"
	"encoding/json"
	"os"
)

type mockClient struct{}

func (m mockClient) getLoadViaWebSocket(_ context.Context, freightID string) (data []byte, err error) {

	switch freightID {
	case "force error":
		data, err = os.ReadFile("testdata/get_load_failure.json")

	default:
		data, err = os.ReadFile("testdata/get_load_success.json")
	}

	if err != nil {
		return nil, err
	}
	var arrayList []any
	err = json.Unmarshal(data, &arrayList)
	if err != nil {
		return nil, err
	}
	loadDataInBytes, err := json.Marshal(arrayList[4])
	if err != nil {
		return nil, err
	}

	return loadDataInBytes, nil
}
