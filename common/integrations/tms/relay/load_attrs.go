package relay

import (
	tmsutil "github.com/drumkitai/drumkit/common/integrations/tms/util"
	"github.com/drumkitai/drumkit/common/models"
)

func (r *Relay) GetDefaultLoadAttributes() models.LoadAttributes {
	return r.trackingBoardAttributes()
}

func (r *Relay) planningBoardAttributes() (attrs models.LoadAttributes) {
	attrs = loadBoardAttributes()
	attrs.Pickup.AddressLine1 = models.FieldAttributes{IsReadOnly: true}
	attrs.Pickup.AddressLine2 = models.FieldAttributes{IsReadOnly: true}

	attrs.Consignee.AddressLine1 = models.FieldAttributes{IsReadOnly: true}
	attrs.Consignee.AddressLine2 = models.FieldAttributes{IsReadOnly: true}

	tmsutil.ApplyTMSFeatureFlags(&r.tms, &attrs)

	return attrs
}

// Attributes when the load is on the tracking board, aka a carrier is booked
func (r *Relay) trackingBoardAttributes() (attrs models.LoadAttributes) {
	attrs = loadBoardAttributes()
	attrs.Pickup.AddressLine1 = models.FieldAttributes{IsReadOnly: true}
	attrs.Pickup.AddressLine2 = models.FieldAttributes{IsReadOnly: true}

	attrs.Consignee.AddressLine1 = models.FieldAttributes{IsReadOnly: true}
	attrs.Consignee.AddressLine2 = models.FieldAttributes{IsReadOnly: true}

	// Carrier details are on tracking board
	attrs.Carrier = models.CarrierAttributes{
		ExternalTMSID:        models.FieldAttributes{IsReadOnly: true},
		MCNumber:             models.FieldAttributes{IsNotSupported: true},
		DOTNumber:            models.FieldAttributes{IsNotSupported: true},
		Name:                 models.FieldAttributes{IsReadOnly: true},
		Phone:                models.FieldAttributes{IsReadOnly: false},
		Dispatcher:           models.FieldAttributes{IsReadOnly: false},
		Notes:                models.FieldAttributes{IsReadOnly: false},
		SealNumber:           models.FieldAttributes{IsNotSupported: true},
		SCAC:                 models.FieldAttributes{IsNotSupported: true},
		FirstDriverName:      models.FieldAttributes{IsReadOnly: false},
		FirstDriverPhone:     models.FieldAttributes{IsReadOnly: false},
		SecondDriverName:     models.FieldAttributes{IsReadOnly: false},
		SecondDriverPhone:    models.FieldAttributes{IsReadOnly: false},
		Email:                models.FieldAttributes{IsReadOnly: false},
		DispatchCity:         models.FieldAttributes{IsReadOnly: false},
		DispatchState:        models.FieldAttributes{IsReadOnly: false},
		DispatchedTime:       models.FieldAttributes{IsReadOnly: false},
		DispatchSource:       models.FieldAttributes{IsReadOnly: false},
		ExpectedPickupTime:   models.FieldAttributes{IsReadOnly: false}, // In models.CheckCall.NextStopETA
		ExternalTMSTruckID:   models.FieldAttributes{IsReadOnly: false},
		ExternalTMSTrailerID: models.FieldAttributes{IsReadOnly: false},
		RateConfirmationSent: models.FieldAttributes{IsReadOnly: true},

		ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true},
		ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true},
		PickupStart:              models.FieldAttributes{IsNotSupported: true}, // In check calls
		PickupEnd:                models.FieldAttributes{IsNotSupported: true}, // In check calls
		ExpectedDeliveryTime:     models.FieldAttributes{IsNotSupported: true}, // In check calls
		DeliveryStart:            models.FieldAttributes{IsNotSupported: true}, // In check calls
		DeliveryEnd:              models.FieldAttributes{IsNotSupported: true}, // In check calls
		SignedBy:                 models.FieldAttributes{IsNotSupported: true},
	}

	return attrs

}

// Attributes when the load is on the load board, aka does not have a carrier booked yet
// NOTE: Pickup/Consignee.AddressLine1 and AddressLine2 are not provided in Load Board JSON,
// which is important for appt scheduling and warehouse matching logic, so load board code is deprecated
func loadBoardAttributes() (attrs models.LoadAttributes) {
	return models.LoadAttributes{
		ExternalTMSID:     models.FieldAttributes{IsReadOnly: true},
		FreightTrackingID: models.FieldAttributes{IsReadOnly: true},
		Commodities:       models.FieldAttributes{},
		LoadCoreInfoAttributes: models.LoadCoreInfoAttributes{
			PONums:           models.FieldAttributes{IsReadOnly: true},
			Mode:             models.FieldAttributes{IsReadOnly: true},
			MoreThanTwoStops: models.FieldAttributes{IsReadOnly: true},
			Status:           models.FieldAttributes{IsReadOnly: true},
			Operator:         models.FieldAttributes{IsReadOnly: false},
			// NOTE: Some rates values are parsed but are not yet displayed on FE.
			RateData: models.InitUnsupportedRateData,
			Customer: models.CustomerAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					Name: models.FieldAttributes{IsReadOnly: true},

					ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true},
					City:          models.FieldAttributes{IsNotSupported: true},
					State:         models.FieldAttributes{IsNotSupported: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: true},
					Country:       models.FieldAttributes{IsNotSupported: true},
					Contact:       models.FieldAttributes{IsNotSupported: true},
					Phone:         models.FieldAttributes{IsNotSupported: true},
					Email:         models.FieldAttributes{IsNotSupported: true},
				},
				RefNumber:           models.FieldAttributes{IsReadOnly: true},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
			},

			BillTo: models.BillToAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsNotSupported: true},
					Name:          models.FieldAttributes{IsNotSupported: true},
					AddressLine1:  models.FieldAttributes{IsNotSupported: true},
					AddressLine2:  models.FieldAttributes{IsNotSupported: true},
					City:          models.FieldAttributes{IsNotSupported: true},
					State:         models.FieldAttributes{IsNotSupported: true},
					Zipcode:       models.FieldAttributes{IsNotSupported: true},
					Country:       models.FieldAttributes{IsNotSupported: true},
					Contact:       models.FieldAttributes{IsNotSupported: true},
					Phone:         models.FieldAttributes{IsNotSupported: true},
					Email:         models.FieldAttributes{IsNotSupported: true},
				},
			},

			Pickup: models.PickupAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					// Address not provided in Load Board JSON
					AddressLine1: models.FieldAttributes{IsNotSupported: true},
					AddressLine2: models.FieldAttributes{IsNotSupported: true},
					City:         models.FieldAttributes{IsReadOnly: true},
					State:        models.FieldAttributes{IsReadOnly: true},
					Zipcode:      models.FieldAttributes{IsReadOnly: true},
					Country:      models.FieldAttributes{IsNotSupported: true},
					Contact:      models.FieldAttributes{IsNotSupported: true},
					Phone:        models.FieldAttributes{IsNotSupported: true},
					Email:        models.FieldAttributes{IsNotSupported: true},
				},
				ExternalTMSStopID:   models.FieldAttributes{IsReadOnly: true},
				BusinessHours:       models.FieldAttributes{IsNotSupported: true},
				RefNumber:           models.FieldAttributes{IsReadOnly: false},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
				// NOTE: Appt block on Load Detail page can be either ready time or start time,
				// while only confirmed appointments appear on Tracking page so we parse from there instead.
				// To avoid confusion and inconsistencies, we don't support Relay's ReadyTime for now
				ReadyTime:     models.FieldAttributes{IsNotSupported: true}, // Editable when appt type is Drop Trailer
				ApptStartTime: models.FieldAttributes{},
				ApptEndTime:   models.FieldAttributes{}, // Editable when appt type is FCFS (first-come, first-serve)
				ApptRequired:  models.FieldAttributes{IsNotSupported: true},
				ApptType:      models.FieldAttributes{}, // TODO support other appt types in schedulePlan
				ApptNote:      models.FieldAttributes{IsNotSupported: true},
				Timezone:      models.FieldAttributes{IsReadOnly: true},
			},

			Consignee: models.ConsigneeAttributes{
				CompanyCoreInfoAttributes: models.CompanyCoreInfoAttributes{
					ExternalTMSID: models.FieldAttributes{IsReadOnly: true},
					Name:          models.FieldAttributes{IsReadOnly: true},
					// Address not provided in Load Board JSON
					AddressLine1: models.FieldAttributes{IsNotSupported: true},
					AddressLine2: models.FieldAttributes{IsNotSupported: true},
					City:         models.FieldAttributes{IsReadOnly: true},
					State:        models.FieldAttributes{IsReadOnly: true},
					Zipcode:      models.FieldAttributes{IsReadOnly: true},
					Country:      models.FieldAttributes{IsNotSupported: true},
					Contact:      models.FieldAttributes{IsNotSupported: true},
					Phone:        models.FieldAttributes{IsNotSupported: true},
					Email:        models.FieldAttributes{IsNotSupported: true},
				},
				ExternalTMSStopID:   models.FieldAttributes{IsReadOnly: true},
				BusinessHours:       models.FieldAttributes{IsNotSupported: true},
				RefNumber:           models.FieldAttributes{},
				RefNumberCandidates: models.FieldAttributes{IsNotSupported: true},
				MustDeliver:         models.FieldAttributes{IsNotSupported: true},
				ApptRequired:        models.FieldAttributes{IsNotSupported: true},
				ApptStartTime:       models.FieldAttributes{},
				// ApptEndTime editable when appt type is Drop Trailer or FCFS (first-come, first-serve)
				ApptEndTime: models.FieldAttributes{},
				ApptNote:    models.FieldAttributes{IsNotSupported: true},
				Timezone:    models.FieldAttributes{IsNotSupported: true},
			},

			// Carrier details are on tracking board
			Carrier: models.CarrierAttributes{
				ExternalTMSID:        models.FieldAttributes{IsReadOnly: true},
				MCNumber:             models.FieldAttributes{IsNotSupported: true},
				DOTNumber:            models.FieldAttributes{IsNotSupported: true},
				Name:                 models.FieldAttributes{IsReadOnly: true},
				Phone:                models.FieldAttributes{IsReadOnly: true},
				Dispatcher:           models.FieldAttributes{IsReadOnly: true},
				Notes:                models.FieldAttributes{IsReadOnly: true},
				SealNumber:           models.FieldAttributes{IsNotSupported: true},
				SCAC:                 models.FieldAttributes{IsNotSupported: true},
				FirstDriverName:      models.FieldAttributes{IsReadOnly: true},
				FirstDriverPhone:     models.FieldAttributes{IsReadOnly: true},
				SecondDriverName:     models.FieldAttributes{IsReadOnly: true},
				SecondDriverPhone:    models.FieldAttributes{IsReadOnly: true},
				Email:                models.FieldAttributes{IsReadOnly: true},
				DispatchCity:         models.FieldAttributes{IsNotSupported: true}, // In check calls
				DispatchState:        models.FieldAttributes{IsNotSupported: true}, // In check calls
				ExternalTMSTruckID:   models.FieldAttributes{IsReadOnly: true},
				ExternalTMSTrailerID: models.FieldAttributes{IsReadOnly: true},

				RateConfirmationSent:     models.FieldAttributes{IsReadOnly: true},
				ConfirmationSentTime:     models.FieldAttributes{IsNotSupported: true},
				ConfirmationReceivedTime: models.FieldAttributes{IsNotSupported: true},
				DispatchedTime:           models.FieldAttributes{IsNotSupported: true},

				ExpectedPickupTime:   models.FieldAttributes{IsNotSupported: true}, // In models.CheckCall.NextStopETA
				PickupStart:          models.FieldAttributes{IsNotSupported: true}, // In check calls
				PickupEnd:            models.FieldAttributes{IsNotSupported: true}, // In check calls
				ExpectedDeliveryTime: models.FieldAttributes{IsNotSupported: true}, // In check calls
				DeliveryStart:        models.FieldAttributes{IsNotSupported: true}, // In check calls
				DeliveryEnd:          models.FieldAttributes{IsNotSupported: true}, // In check calls
				SignedBy:             models.FieldAttributes{IsNotSupported: true},
			},

			Specifications: models.SpecificationsAttributes{
				OrderType:           models.FieldAttributes{IsNotSupported: true},
				TotalOutPalletCount: models.FieldAttributes{IsReadOnly: true},
				TotalPieces:         models.FieldAttributes{IsReadOnly: true},
				Commodities:         models.FieldAttributes{IsReadOnly: true},
				NumCommodities:      models.FieldAttributes{IsReadOnly: true},
				TotalWeight:         models.FieldAttributes{IsReadOnly: true},
				TotalDistance:       models.FieldAttributes{IsReadOnly: true},
				TotalInPalletCount:  models.FieldAttributes{IsNotSupported: true},
				BillableWeight:      models.FieldAttributes{IsNotSupported: true},

				IsRefrigerated:    models.FieldAttributes{IsNotSupported: true},
				MinTempFahrenheit: models.FieldAttributes{IsNotSupported: true},
				MaxTempFahrenheit: models.FieldAttributes{IsNotSupported: true},
				LiftgatePickup:    models.FieldAttributes{IsNotSupported: true},
				LiftgateDelivery:  models.FieldAttributes{IsNotSupported: true},
				InsidePickup:      models.FieldAttributes{IsNotSupported: true},
				InsideDelivery:    models.FieldAttributes{IsNotSupported: true},
				Tarps:             models.FieldAttributes{IsNotSupported: true},
				Oversized:         models.FieldAttributes{IsNotSupported: true},
				Hazmat:            models.FieldAttributes{IsNotSupported: true},
				Straps:            models.FieldAttributes{IsNotSupported: true},
				Permits:           models.FieldAttributes{IsNotSupported: true},
				Escorts:           models.FieldAttributes{IsNotSupported: true},
				Seal:              models.FieldAttributes{IsNotSupported: true},
				CustomBonded:      models.FieldAttributes{IsNotSupported: true},
				Labor:             models.FieldAttributes{IsNotSupported: true},
			},
		},
	}
}
