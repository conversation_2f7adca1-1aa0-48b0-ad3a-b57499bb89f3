package relay

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type LoadResp struct {
	Data struct {
		TotalRows int    `json:"total_rows"`
		Loads     []Load `json:"loads"`
	} `json:"data"`
}

type Load struct {
	LoadNumber                   int              `json:"load_number"`
	BookingID                    int              `json:"booking_id"`
	ShipmentIDs                  []string         `json:"shipment_ids"`
	TeamDriver                   bool             `json:"team_driver?"`
	AssignmentID                 string           `json:"assignment_id"`
	MaxBuyCurrency               any              `json:"max_buy_currency"`
	FirstPickupAppointment       string           `json:"first_pickup_appointment"`
	SecondPickupAppointment      string           `json:"second_pickup_appointment"`
	ProfitCenter                 string           `json:"profit_center"`
	ReservedCarrierName          *string          `json:"reserved_carrier_name"`
	SecondPickupShipperName      string           `json:"second_pickup_shipper_name"`
	AssignedUserName             string           `json:"assigned_user_name"`
	Committed                    bool             `json:"committed?"`
	Customer                     string           `json:"customer"`
	MaxBuy                       string           `json:"max_buy"`
	AssignedToOpenBoard          bool             `json:"assigned_to_open_board?"`
	Pickups                      []Pickup         `json:"pickups"`
	Deliveries                   []Delivery       `json:"deliveries"`
	Cancelled                    bool             `json:"cancelled?"`
	Mode                         string           `json:"mode"`
	ReadyDate                    string           `json:"ready_date"`
	FirstPickupAppointmentLocal  *string          `json:"first_pickup_appointment_local"`
	Orders                       []Order          `json:"orders"`
	FirstPickupShipperName       string           `json:"first_pickup_shipper_name"`
	TotalMiles                   float64          `json:"total_miles"`
	Advertised                   bool             `json:"advertised?"`
	MaxBuySetBy                  *string          `json:"max_buy_set_by"`
	LastDeliveryCity             string           `json:"last_delivery_city"`
	SecondPickupCity             string           `json:"second_pickup_city"`
	SecondPickupState            string           `json:"second_pickup_state"`
	TotalWeight                  float64          `json:"total_weight"`
	Reserved                     bool             `json:"reserved?"`
	AdvertisedRate               any              `json:"advertised_rate"`
	ReceiverLocation             string           `json:"receiver_location"`
	AdvertisedCarrierName        *string          `json:"advertised_carrier_name"`
	MaxBuySetAt                  any              `json:"max_buy_set_at"`
	LastDeliveryState            string           `json:"last_delivery_state"`
	AssignedTo                   int              `json:"assigned_to"`
	HighRisk                     bool             `json:"high_risk?"`
	ReadyTime                    *string          `json:"ready_time"`
	Stops                        []Stop           `json:"stops"`
	CargoValue                   CargoValue       `json:"cargo_value"`
	CarrierBooked                bool             `json:"carrier_booked?"`
	AccountManagers              []AccountManager `json:"account_managers"`
	IsRefrigerated               bool             `json:"is_refrigerated"`
	ServiceLine                  ServiceLine      `json:"service_line"`
	TotalPieces                  *int             `json:"total_pieces"`
	FirstPickupCity              string           `json:"first_pickup_city"`
	Held                         bool             `json:"held?"`
	AssignedInitials             string           `json:"assigned_initials"`
	LastDeliveryAppointment      string           `json:"last_delivery_appointment"`
	LastDeliveryAppointmentLocal *string          `json:"last_delivery_appointment_local"`
	ShipperLocation              string           `json:"shipper_location"`
	TotalPallets                 int              `json:"total_pallets"`
	ReservedCarrierID            *string          `json:"reserved_carrier_id"`
	RelayReferenceNumber         int              `json:"relay_reference_number"`
	Offers                       []any            `json:"offers"`
	Bounced                      bool             `json:"bounced?"`
}

// Struct for common stop details
type Stop struct {
	Zip                      string  `json:"zip"`
	StopType                 string  `json:"stop_type"`
	StopID                   string  `json:"stop_id"`
	State                    string  `json:"state"`
	SequenceNumber           int     `json:"sequence_number"`
	Preload                  bool    `json:"preload?"`
	Longitude                float64 `json:"longitude"`
	Latitude                 float64 `json:"latitude"`
	Fcfs                     bool    `json:"fcfs?"`
	DropTrailer              bool    `json:"drop_trailer?"`
	City                     string  `json:"city"`
	AppointmentType          string  `json:"appointment_type"`
	AppointmentReference     *string `json:"appointment_reference"`
	AppointmentDatetimeLocal string  `json:"appointment_datetime_local"`
	AppointmentCloseDatetime *string `json:"appointment_close_datetime"`
}

type Pickup struct {
	Stop
	ShipperName string  `json:"shipper_name"`
	ShipperID   string  `json:"shipper_id"`
	ScheduleID  *string `json:"schedule_id"`
}

type Delivery struct {
	Stop
	ReceiverName string  `json:"receiver_name"`
	ReceiverID   string  `json:"receiver_id"`
	DeliveryID   *string `json:"delivery_id"`
}

type CargoValue struct {
	Currency string `json:"currency"`
	Amount   int    `json:"amount"`
}

type AccountManager struct {
	RelayUserID     int    `json:"relay_user_id"`
	Email           string `json:"email"`
	AssociationType string `json:"association_type"`
}

type DeclaredValue struct {
	Currency string `json:"currency"`
	Amount   int    `json:"amount"`
}

type Order struct {
	OrderNumber            string        `json:"order_number"`
	MustCheckDeclaredValue bool          `json:"must_check_declared_value?"`
	DeclaredValue          DeclaredValue `json:"declared_value"`
}

type ServiceLine struct {
	Version               int     `json:"version"`
	Type                  string  `json:"type"`
	RequiredEquipmentType *string `json:"required_equipment_type"`
}

func (r *Relay) toLoadModel(ctx context.Context, data Load) (load models.Load, err error) {
	timeLayout := "2006-01-02T15:04:05"
	dateLayout := "2006-01-02"

	load.TMSID = r.tms.ID
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = strconv.Itoa(data.LoadNumber)
	load.FreightTrackingID = strconv.Itoa(data.BookingID)

	// Mode
	loadMode := models.StringToLoadMode(strings.ToUpper(data.Mode))
	if loadMode == "" {
		log.Warn(
			ctx,
			"Unknown Relay load mode",
			zap.String("loadID", load.ExternalTMSID),
			zap.String("mode", data.Mode),
		)
	}
	load.Mode = loadMode

	// Status
	if data.CarrierBooked {
		load.Status = "Booked"
	}
	if data.Cancelled {
		load.Status = "Cancelled"
	}

	// Operator
	load.Operator = data.AssignedUserName

	for _, po := range data.Orders {
		if load.PONums == "" {
			load.PONums = po.OrderNumber
		} else {
			load.PONums += "," + po.OrderNumber
		}
	}

	// Customer
	{
		load.Customer.Name = data.Customer
		if len(data.ShipmentIDs) > 1 {
			log.WarnNoSentry(
				ctx,
				"Relay load with multiple shipment IDs (aka customer refs)",
				zap.Int("relayLoadNum", data.LoadNumber),
				zap.Int("relayBookingID", data.BookingID),
			)
		}
		load.Customer.RefNumber = strings.Join(data.ShipmentIDs, ",")
	}

	// Pickup
	if len(data.Pickups) > 0 {
		load.Pickup.Name = data.Pickups[0].ShipperName
		load.Pickup.ExternalTMSID = data.Pickups[0].ShipperID
		load.Pickup.ExternalTMSStopID = data.Pickups[0].StopID
		load.Pickup.Zipcode = data.Pickups[0].Zip
		load.Pickup.State = data.Pickups[0].State
		load.Pickup.City = data.Pickups[0].City
		load.Pickup.ApptType = data.Pickups[0].AppointmentType
		if ref := data.Pickups[0].AppointmentReference; ref != nil {
			load.Pickup.RefNumber = *ref
		}

		city := strings.TrimSpace(data.Pickups[0].City)
		state := strings.TrimSpace(data.Pickups[0].State)
		pickUpTimezone, err := timezone.GetTimezone(ctx, city, state, "")
		if err != nil {
			return models.Load{}, fmt.Errorf("unable to get timezone of pickup city (%s), state (%s): %w",
				data.Pickups[0].City, data.Pickups[0].State, err)
		}
		load.Pickup.Timezone = pickUpTimezone

		pickUpLocation, err := time.LoadLocation(pickUpTimezone)
		if err != nil {
			return models.Load{}, fmt.Errorf("unable to load location of pickup timezone (%s): %w", pickUpTimezone, err)
		}

		if apptTimeStr := data.Pickups[0].AppointmentDatetimeLocal; apptTimeStr != "" {
			var layout string
			if strings.Contains(apptTimeStr, "T") {
				layout = timeLayout
			} else {
				layout = time.DateOnly
			}
			appointTime, err := time.ParseInLocation(layout, apptTimeStr, pickUpLocation)
			if err != nil {
				return models.Load{}, fmt.Errorf("unable to parse pickup appointment time: %w", err)
			}

			load.Pickup.ApptStartTime = models.NullTime{
				Time:  appointTime,
				Valid: true,
			}
		}

		if data.ReadyDate != "" {
			readyDatetime := data.ReadyDate
			layout := dateLayout

			if data.ReadyTime != nil {
				readyDatetime = data.ReadyDate + "T" + *data.ReadyTime
				layout = timeLayout
			}

			readyDate, err := time.ParseInLocation(layout, readyDatetime, pickUpLocation)
			if err != nil {
				return models.Load{}, fmt.Errorf("unable to parse ready date: %w", err)
			}

			load.Pickup.ReadyTime = models.NullTime{
				Time:  readyDate,
				Valid: true,
			}
		}

	}

	// Delivery
	if len(data.Deliveries) > 0 {
		load.Consignee.Name = data.Deliveries[0].ReceiverName
		load.Consignee.ExternalTMSID = data.Deliveries[0].ReceiverID
		load.Consignee.ExternalTMSStopID = data.Deliveries[0].StopID
		load.Consignee.Zipcode = data.Deliveries[0].Zip
		load.Consignee.State = data.Deliveries[0].State
		load.Consignee.City = data.Deliveries[0].City
		load.Consignee.ApptType = data.Deliveries[0].AppointmentType

		if ref := data.Deliveries[0].AppointmentReference; ref != nil {
			load.Consignee.RefNumber = *ref
		}

		city := strings.TrimSpace(data.Deliveries[0].City)
		state := strings.TrimSpace(data.Deliveries[0].State)
		deliveryTimezone, err := timezone.GetTimezone(ctx, city, state, "")
		if err != nil {
			return models.Load{},
				fmt.Errorf("unable to get timezone of delivery city (%s), state (%s): %w",
					data.Deliveries[0].City, data.Deliveries[0].State, err)
		}
		load.Consignee.Timezone = deliveryTimezone

		deliveryLocation, err := time.LoadLocation(deliveryTimezone)
		if err != nil {
			return models.Load{},
				fmt.Errorf("unable to load location of delivery timezone (%s): %w", deliveryTimezone, err)
		}

		if apptTimeStr := data.Deliveries[0].AppointmentDatetimeLocal; apptTimeStr != "" {
			var layout string
			if strings.Contains(apptTimeStr, "T") {
				layout = timeLayout
			} else {
				layout = time.DateOnly
			}
			appointTime, err := time.ParseInLocation(layout, apptTimeStr, deliveryLocation)
			if err != nil {
				return models.Load{}, fmt.Errorf("unable to parse delivery appointment time: %w", err)
			}

			load.Consignee.ApptStartTime = models.NullTime{
				Time:  appointTime,
				Valid: true,
			}
		}

		// Parse deliver by date from AppointmentCloseDatetime
		if closeDatetime := data.Deliveries[0].AppointmentCloseDatetime; closeDatetime != nil && *closeDatetime != "" {
			var layout string
			if strings.Contains(*closeDatetime, "T") {
				layout = timeLayout
			} else {
				layout = time.DateOnly
			}
			deliverByTime, err := time.ParseInLocation(layout, *closeDatetime, deliveryLocation)
			if err != nil {
				return models.Load{}, fmt.Errorf("unable to parse delivery close datetime: %w", err)
			}

			load.Consignee.MustDeliver = models.NullTime{
				Time:  deliverByTime,
				Valid: true,
			}
		}
	}

	// Specifications/details
	load.Specifications.TotalWeight = models.ValueUnit{Val: float32(data.TotalWeight), Unit: models.LbsUnit}
	load.Specifications.TotalOutPalletCount = data.TotalPallets
	load.Specifications.TotalDistance = models.ValueUnit{Val: float32(data.TotalMiles), Unit: models.MilesUnit}

	return load, nil
}

type WebSocketMsgCore struct {
	Type  string `json:"type,omitempty"`
	Event string `json:"event,omitempty"`
	Value string `json:"value,omitempty"` // URL-encoded `Value`
}

type (
	PlanningBoardConnectMessage struct {
		URL     string `json:"url,omitempty"`
		Params  Params `json:"params,omitempty"`
		Session string `json:"session,omitempty"`
		Static  string `json:"static,omitempty"`
	}
	Params struct {
		CsrfToken   string   `json:"_csrf_token,omitempty"`
		Timezone    string   `json:"timezone,omitempty"`
		TrackStatic []string `json:"_track_static,omitempty"`
		Mounts      int      `json:"_mounts,omitempty"`
	}

	PatchSchedulePlan struct {
		Type  string `json:"type,omitempty"`
		Event string `json:"event,omitempty"`
		Value string `json:"value,omitempty"` // URL-encoded `Value`
		Cid   int    `json:"cid,omitempty"`   // ¯\_(ツ)_/¯
	}
)
