# Relay TMS

#### Internal TMS for NFI Industries

## Setup

1. To login, go to [Ok<PERSON>](https://nfi.okta.com/oauth2/v1/authorize?client_id=okta.2b1959c8-bcc0-56eb-a589-cfcfb7422f26&code_challenge=7fH-ciUxPu3rONEGeuq2TCT5XbVgKudwx-9NeU88NIM&code_challenge_method=S256&nonce=e3u3xiuL8H7cQYjvjF71H43enU07hRa9LLQMdGHwP048MYgkqFiShk7g30yAauBT&redirect_uri=https%3A%2F%2Fnfi.okta.com%2Fenduser%2Fcallback&response_type=code&state=DpU6hvaUcPGNiWRckzZf6LMKABApieeQfUfFVtLjSDSaRqtsyqP10BxZkwbNOqdg&scope=openid%20profile%20email%20okta.users.read.self%20okta.users.manage.self%20okta.internal.enduser.read%20okta.internal.enduser.manage%20okta.enduser.dashboard.read%20okta.enduser.dashboard.manage%20okta.myAccount.sessions.manage) and use 1Password to sign in <NAME_EMAIL> account.
1. From within the Okta dashboard, click the "Gmail Workspace Account" tile then login to Gmail.
1. Go to [relaytms.com](relaytms.com) (or [training.relaytms.com](https://training.relaytms.com) for dev) and login with the Gmail account.
1. Voila! You're logged in to Relay TMS.
1. To set up the integration locally, use Chrome Inspector on RelayTMS to capture the `_relay_web_key` and paste that into the `api_key` column in the `integrations` table.

## Reverse Engineering Relay Playbook

Notion: https://www.notion.so/drumkitai/Relay-3a55d1325fdf4b85ae6c327f013632b6

## Troubleshooting

1. Depending on a load's status/board, different data is provided. Namely, if a load does not have a carrier booked, the Load Detail page does not display rate details and reference/PO numbers.
1. If our integration stops working out of the blue, check for small differences between code output and Relay website. For example, POST check call was broken because the [CID in the POST request needed to be updated from 2 to 4](https://github.com/drumkitai/drumkit/pull/1439).
