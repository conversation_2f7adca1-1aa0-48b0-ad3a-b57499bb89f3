package relay

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const testBookingID = "7004096"

func TestLivePostCheckCall(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLivePostCheckCall: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS, Tenant: "training.relaytms.com"}
	r := New(ctx, tms)

	var fetchedLoad models.Load
	fetchedLoad, _, err := r.GetLoad(ctx, testLoadID)
	require.NoError(t, err)

	vTrue := true
	vFalse := false

	t.Run("Note", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID: fetchedLoad.FreightTrackingID,
			Status:            "Add Tracking Note",
			Notes:             "test note @ " + dt.Format(time.DateTime),
			Source:            "dispatcher",
			IsOnTime:          &vTrue,
			IsException:       &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "Note Captured"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Dispatch", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Dispatch Driver",
			Notes:                      "test dispatch @ " + dt.Format(time.DateTime),
			NextStopETAWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			Source:                     "dispatcher",
			IsOnTime:                   &vTrue,
			City:                       "Lexington",
			State:                      "KY",
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "Driver Dispatched"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("In Transit", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			City:                       "Chicago",
			State:                      "IL",
			Status:                     "In Transit Update",
			Reason:                     "",
			Notes:                      "test check call @ " + dt.Format(time.DateTime),
			Author:                     "",
			DateTime:                   models.NullTime{},
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{},
			Timezone:                   "",
			NextStopETAWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			NextStopID:                 "pickup",
			CapturedDatetime:           models.NullTime{},
			Source:                     "dispatcher",
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		cc.Status = "In Transit"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Arrived at pickup", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:       fetchedLoad.FreightTrackingID,
			Status:                  "Mark Arrived at Pickup",
			Reason:                  "",
			Notes:                   "test at pickup check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone: models.NullTime{Time: dt, Valid: true},
			Source:                  "dispatcher",
			// IsOnTime:                nil,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		time.Sleep(200 * time.Millisecond)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Arrival At Stop"
		cc.City = "Stop 1"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Loaded", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Mark Loaded",
			Reason:                     "",
			Notes:                      "test loaded check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour), Valid: true},
			Source:                     "dispatcher",
			// IsOnTime:                   &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Marked Loaded"
		cc.City = "Allbirds, Shepherdsville, KY"
		cc.State = "Shepherdsville, KY"
		// NOTE: Mark Loaded/Delivered produces 2 check calls on Relay's end:
		// 1 Arrival At Stop, and 1 Marked Loaded. Only the former actually retains the OnTime information
		cc.IsOnTime = nil
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Arrived at dropoff", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:       fetchedLoad.FreightTrackingID,
			Status:                  "Mark Arrived at Delivery",
			Reason:                  "",
			Notes:                   "test at dropoff check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone: models.NullTime{Time: dt, Valid: true},
			Source:                  "dispatcher",
			IsOnTime:                &vFalse,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Modify expected object to match what Relay returns
		cc.Status = "Arrival at Stop"
		cc.City = "Stop 2"
		checkTest(ctx, t, checkCalls, cc)
	})

	t.Run("Delivered", func(t *testing.T) {
		dt := time.Now().Add(-4 * time.Hour).Truncate(time.Minute)

		cc := models.CheckCall{
			FreightTrackingID:          fetchedLoad.FreightTrackingID,
			Status:                     "Mark Delivered",
			Reason:                     "",
			Notes:                      "test delivered check call @ " + dt.Format(time.DateTime),
			DateTimeWithoutTimezone:    models.NullTime{Time: dt, Valid: true},
			EndDateTimeWithoutTimezone: models.NullTime{Time: dt.Add(2 * time.Hour)},
			Source:                     "dispatcher",
			IsOnTime:                   &vTrue,
		}

		start := time.Now()
		err := r.PostCheckCall(ctx, &fetchedLoad, cc)
		log.Infof(ctx, "PostCheckCall took %v to complete", time.Since(start))
		require.NoError(t, err)

		checkCalls, err := r.GetCheckCallsHistory(ctx, 0, testBookingID)
		require.NoError(t, err)

		// Relay prefixes statuses with "Marked" so update the expected object
		cc.Status = "Marked Delivered"
		// NOTE: Mark Loaded/Delivered produces 2 check calls on Relay's end:
		// 1 Arrival At Stop, and 1 Marked Delivered. Only the former actually retains the OnTime information
		cc.IsOnTime = nil
		checkTest(ctx, t, checkCalls, cc)
	})

}

func checkTest(ctx context.Context, t *testing.T, checkCalls []models.CheckCall, expected models.CheckCall) {

	var res models.CheckCall
	for i, c := range checkCalls {
		if strings.EqualFold(c.Notes, expected.Notes) {
			log.Infof(ctx, "found submitted check call at index %d", i)
			res = c
			break
		}
	}

	require.NotEmpty(t, res)
	assert.NotEmpty(t, res.CapturedDatetime)
	assert.Equal(t, expected.Status, res.Status)
	assert.Equal(t, expected.Source, res.Source)
	assert.Equal(t, expected.Notes, res.Notes)
	assert.Equal(t, expected.City, res.City)
	assert.Equal(t, expected.State, res.State)

	assert.True(t, res.DateTimeWithoutTimezone.Equal(expected.DateTimeWithoutTimezone),
		"expected: %s, actual: %s", expected.DateTimeWithoutTimezone, res.DateTimeWithoutTimezone)
	assert.True(t, res.EndDateTimeWithoutTimezone.Equal(expected.EndDateTimeWithoutTimezone),
		"expected: %s, actual: %s", expected.EndDateTimeWithoutTimezone, res.EndDateTimeWithoutTimezone)

	if expected.IsException != nil {
		assert.Equal(t, *expected.IsException, *res.IsException)
	}
	if expected.IsOnTime != nil {
		assert.Equal(t, *expected.IsOnTime, *res.IsOnTime)
	}

}

func TestParseCheckCallHTML(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	html, err := os.ReadFile("./testdata/checkcalls.html")
	require.NoError(t, err)

	checkCalls, err := r.parseCheckCallHTML(ctx, html, 123, "FREIGHT123")
	require.NoError(t, err)
	require.NotEmpty(t, checkCalls, "should parse at least one check call")

	loc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	vTrue := true
	vFalse := false

	expected := []models.CheckCall{
		{
			LoadID:                  123,
			FreightTrackingID:       "FREIGHT123",
			Status:                  "Marked Delivered",
			Source:                  "Web",
			IsOnTime:                &vFalse,
			IsException:             &vTrue,
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2025, 7, 3, 19, 0, 0, 0, time.UTC), Valid: true},
			CapturedDatetime:        models.NullTime{Time: time.Date(2025, 7, 3, 19, 32, 0, 0, loc), Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			City:              "Breinigsville",
			State:             "PA",
			Status:            "Stop Marked Delivered",
			Source:            "Web",
			Author:            "Miguel Ramirez",
			IsOnTime:          &vFalse,
			IsException:       &vTrue,
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2025, 7, 3, 18, 0, 0, 0, time.UTC),
				Valid: true,
			},
			EndDateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2025, 7, 3, 19, 0, 0, 0, time.UTC),
				Valid: true,
			},
			Timezone:         "America/New_York",
			CapturedDatetime: models.NullTime{Time: time.Date(2025, 7, 3, 19, 32, 0, 0, loc), Valid: true},
		},
		{
			LoadID:            123,
			FreightTrackingID: "FREIGHT123",
			City:              "Danbury",
			State:             "CT",
			Status:            "Loaded",
			Source:            "Web",
			Author:            "Miguel Ramirez",
			IsOnTime:          &vFalse,
			IsException:       &vTrue,
			DateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2025, 7, 3, 11, 0, 0, 0, time.UTC),
				Valid: true,
			},
			EndDateTimeWithoutTimezone: models.NullTime{
				Time:  time.Date(2025, 7, 3, 13, 0, 0, 0, time.UTC),
				Valid: true,
			},
			Timezone:         "America/New_York",
			CapturedDatetime: models.NullTime{Time: time.Date(2025, 7, 3, 19, 32, 0, 0, loc), Valid: true},
		},
		{
			LoadID:                  123,
			FreightTrackingID:       "FREIGHT123",
			Status:                  "Marked Delivered",
			Source:                  "nfi_three_g",
			IsOnTime:                &vFalse,
			IsException:             &vTrue,
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2025, 7, 3, 18, 59, 0, 0, time.UTC), Valid: true},
			CapturedDatetime:        models.NullTime{Time: time.Date(2025, 7, 3, 19, 1, 0, 0, loc), Valid: true},
		},
		{
			LoadID:                  123,
			FreightTrackingID:       "FREIGHT123",
			Status:                  "Arrival at Stop 1",
			Source:                  "Dispatcher",
			Author:                  "Juan Buitrago",
			IsOnTime:                &vFalse,
			IsException:             &vTrue,
			DateTimeWithoutTimezone: models.NullTime{Time: time.Date(2025, 7, 3, 11, 0, 0, 0, time.UTC), Valid: true},
			CapturedDatetime:        models.NullTime{Time: time.Date(2025, 7, 3, 11, 57, 0, 0, loc), Valid: true},
		},
	}

	// Verify expected number of check calls
	require.Equal(t, len(expected), len(checkCalls), "should parse exactly %d check calls", len(expected))

	// Verify each check call matches expected values
	for i, cc := range checkCalls {
		t.Run(fmt.Sprintf("CheckCall_%d_%s", i+1, cc.Status), func(t *testing.T) {
			exp := expected[i]

			// Basic fields
			assert.Equal(t, exp.LoadID, cc.LoadID, "LoadID should match")
			assert.Equal(t, exp.FreightTrackingID, cc.FreightTrackingID, "FreightTrackingID should match")
			assert.Equal(t, exp.Source, cc.Source, "Source should match")
			assert.Equal(t, exp.Status, cc.Status, "Status should match")
			assert.Equal(t, exp.Notes, cc.Notes, "Notes should match")
			assert.Equal(t, exp.Author, cc.Author, "Author should match")
			assert.Equal(t, exp.City, cc.City, "City should match")
			assert.Equal(t, exp.State, cc.State, "State should match")

			// Date fields
			assert.True(t, cc.DateTimeWithoutTimezone.Equal(exp.DateTimeWithoutTimezone),
				"DateTimeWithoutTimezone should match: expected %s, got %s",
				exp.DateTimeWithoutTimezone, cc.DateTimeWithoutTimezone)
			assert.True(t, cc.EndDateTimeWithoutTimezone.Equal(exp.EndDateTimeWithoutTimezone),
				"EndDateTimeWithoutTimezone should match: expected %s, got %s",
				exp.EndDateTimeWithoutTimezone, cc.EndDateTimeWithoutTimezone)
			assert.True(t, cc.NextStopETAWithoutTimezone.Equal(exp.NextStopETAWithoutTimezone),
				"NextStopETAWithoutTimezone should match: expected %s, got %s",
				exp.NextStopETAWithoutTimezone, cc.NextStopETAWithoutTimezone)
			assert.True(t, cc.CapturedDatetime.Equal(exp.CapturedDatetime),
				"CapturedDatetime should match: expected %s, got %s",
				exp.CapturedDatetime, cc.CapturedDatetime)

			// Boolean fields
			if exp.IsException != nil {
				require.NotNil(t, cc.IsException, "IsException should not be nil")
				assert.Equal(t, *exp.IsException, *cc.IsException, "IsException should match")
			} else {
				assert.Nil(t, cc.IsException, "IsException should be nil")
			}

			if exp.IsOnTime != nil {
				require.NotNil(t, cc.IsOnTime, "IsOnTime should not be nil")
				assert.Equal(t, *exp.IsOnTime, *cc.IsOnTime, "IsOnTime should match")
			} else {
				assert.Nil(t, cc.IsOnTime, "IsOnTime should be nil")
			}
		})
	}
}

// TestParseCheckCallHTML_EmptyHTML tests parsing behavior with empty HTML
func TestParseCheckCallHTML_EmptyHTML(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	// Test with empty HTML
	emptyHTML := []byte("<html><body></body></html>")
	checkCalls, err := r.parseCheckCallHTML(ctx, emptyHTML, 123, "FREIGHT123")
	require.NoError(t, err)
	assert.Empty(t, checkCalls, "should return empty slice for HTML with no tracking updates")
}

// TestParseCheckCallHTML_InvalidHTML tests parsing behavior with malformed HTML that contains no tracking updates
func TestParseCheckCallHTML_InvalidHTML(t *testing.T) {
	ctx := context.Background()
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	// Test with malformed HTML (valid for parsing but contains no tracking updates)
	invalidHTML := []byte("not valid html")
	checkCalls, err := r.parseCheckCallHTML(ctx, invalidHTML, 123, "FREIGHT123")
	require.NoError(t, err, "should handle malformed HTML gracefully")
	assert.Empty(t, checkCalls, "should return empty slice for HTML with no tracking updates")
}

// TestParseCheckCallHTML_HTMLParsingFailure tests parsing behavior when HTML parsing actually fails
func TestParseCheckCallHTML_HTMLParsingFailure(t *testing.T) {
	// Create a reader that will fail - this will cause goquery.NewDocumentFromReader to fail
	// We'll simulate this by passing nil as the reader input to the function
	// Since we can't easily make goquery fail, we'll test the error path by mocking or
	// using a different approach. For now, let's document this limitation.
	t.Skip("Skipping HTML parsing failure test - goquery is very resilient to malformed HTML")
}

// Backward compatibility alias for the old test name
func TestGetCheckCalls(t *testing.T) {
	TestParseCheckCallHTML(t)
}
