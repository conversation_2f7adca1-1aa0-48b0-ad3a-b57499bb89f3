package relay

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zapcore"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func TestLiveGetUsers(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetUsers: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.InfoLevel)
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	users, err := r.GetUsers(ctx)
	require.NoError(t, err)
	// There should be at least 2 users: Open Board which we manually create
	// and 1+ system user
	assert.Truef(t, len(users) > 1, "unexpected length: %d", len(users))
	log.Infof(ctx, "fetched %d users", len(users))
}

// Best if these tests run sequentially since they add then remove the same field
func TestLiveAssignOperator(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveGetUsers: run with LIVE_TEST=true to enable")
		return
	}
	if apiKey == "" {
		t.Skip("No Relay API key, skipping live test")
	}

	ctx := log.New(context.Background(), log.Config{AppEnv: "dev"}, zapcore.DebugLevel)
	tms := models.Integration{APIKey: apiKey, Name: models.Relay, Type: models.TMS}
	r := New(ctx, tms)

	t.Run("Assign Operator via Live UserID Lookup", func(t *testing.T) {
		var load models.Load
		load.ExternalTMSID = testLoadID
		load.Operator = "Jinyan Zang"

		dbGetTMSUserFunc = func(context.Context, string, uint) (res models.TMSUser, err error) {
			return res, gorm.ErrRecordNotFound
		}

		start := time.Now()
		err := r.assignOperator(ctx, &load)
		require.NoError(t, err)
		log.Infof(ctx, "assignOperator took %v to complete", time.Since(start))

		res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
		require.NoError(t, err)
		assert.Equal(t, load.Operator, res.Operator)
	})

	t.Run("Unassign", func(t *testing.T) {
		var load models.Load
		load.ExternalTMSID = testLoadID
		load.Operator = ""

		// dbGetTMSUserFunc // Should not be called in this test

		start := time.Now()
		err := r.assignOperator(ctx, &load)
		require.NoError(t, err)
		log.Infof(ctx, "assignOperator took %v to complete", time.Since(start))

		res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
		require.NoError(t, err)
		assert.Equal(t, "No Assignment", res.Operator)
	})

	t.Run("Assign Operator via DB UserID Lookup", func(t *testing.T) {
		var load models.Load
		load.ExternalTMSID = testLoadID
		load.Operator = "Jinyan Zang"

		dbGetTMSUserFunc = func(context.Context, string, uint) (res models.TMSUser, err error) {
			return models.TMSUser{ExternalTMSID: "651", Username: "Jinyan Zang"}, nil
		}

		start := time.Now()
		err := r.assignOperator(ctx, &load)
		require.NoError(t, err)
		log.Infof(ctx, "assignOperator took %v to complete", time.Since(start))

		res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
		require.NoError(t, err)
		assert.Equal(t, load.Operator, res.Operator)
	})

	t.Run("Open Board", func(t *testing.T) {
		var load models.Load
		load.ExternalTMSID = testLoadID
		load.Operator = "Open Board"

		// dbGetTMSUserFunc // Should not be called in this test

		start := time.Now()
		err := r.assignOperator(ctx, &load)
		require.NoError(t, err)
		log.Infof(ctx, "assignOperator took %v to complete", time.Since(start))

		res, _, err := r.GetLoad(ctx, load.ExternalTMSID)
		require.NoError(t, err)
		assert.Equal(t, load.Operator, res.Operator)
	})
}

func TestParseUsersFromHTML(t *testing.T) {
	r := &Relay{tms: models.Integration{Model: gorm.Model{ID: 1}}}

	t.Run("Successful parsing with multiple users", func(t *testing.T) {
		html := `
		<html>
			<body>
				<select id="booked_by_id">
					<option value="">Select a user</option>
					<option value="1">User1 </option>
					<option value="2"> User2</option>
					<option value="3">  User3  </option>
				</select>
			</body>
		</html>
		`
		users, err := r.parseUsersFromHTML([]byte(html))

		assert.NoError(t, err)
		assert.Equal(t, []models.TMSUser{
			{Username: "Open Board", ExternalTMSID: "Open Board", TMSID: 1},
			{Username: "User1", ExternalTMSID: "1", TMSID: 1},
			{Username: "User2", ExternalTMSID: "2", TMSID: 1},
			{Username: "User3", ExternalTMSID: "3", TMSID: 1},
		}, users)
	})

	t.Run("No users found", func(t *testing.T) {
		html := `
		<html>
			<body>
				<select id="booked_by_id">
					<option value="">Select a user</option>
				</select>
			</body>
		</html>
		`
		users, err := r.parseUsersFromHTML([]byte(html))
		require.Error(t, err)
		assert.Equal(t, errtypes.EntityNotFoundError(r.tms, "", "allUsers"), err)
		assert.Len(t, users, 1)
	})
}
