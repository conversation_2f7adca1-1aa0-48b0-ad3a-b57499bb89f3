package relay

import (
	"context"
	"time"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

func (r *Relay) PostException(ctx context.Context, load *models.Load, e models.Exception) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*load)...)

	ctx, metaSpan := otel.StartSpan(ctx, "PostExceptionRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	cc := exceptionToCheckCall(e, load.FreightTrackingID)

	return r.PostCheck<PERSON>all(ctx, load, cc)
}

func (r *Relay) GetExceptionHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (exceptions []models.Exception, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), attribute.String("freight_tracking_id", freightTrackingID))

	ctx, metaSpan := otel.StartSpan(ctx, "GetExceptionHistoryRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	checkCalls, err := r.GetCheckCallsHistory(ctx, loadID, freightTrackingID)
	if err != nil {
		return nil, err
	}

	for _, cc := range checkCalls {
		if cc.IsException != nil && *cc.IsException {
			exceptions = append(exceptions, checkCallToException(cc, loadID))
		}
	}

	return exceptions, nil
}

func exceptionToCheckCall(e models.Exception, freightTrackingID string) models.CheckCall {
	vTrue := true
	return models.CheckCall{
		LoadID:                  e.LoadID,
		FreightTrackingID:       freightTrackingID,
		Status:                  "add tracking note",
		Notes:                   e.Note,
		IsOnTime:                e.IsOnTime,
		IsException:             &vTrue,
		DateTimeWithoutTimezone: e.DateTimeWithoutTimezone,
		Source:                  e.Source,
	}
}

func checkCallToException(cc models.CheckCall, loadID uint) models.Exception {
	return models.Exception{
		LoadID:     loadID,
		WhoEntered: cc.Author,
		Source:     cc.Source,
		Note:       cc.Notes,
		Status:     cc.Status,
		IsOnTime:   cc.IsOnTime,
		DateTime:   cc.CapturedDatetime.Time.In(time.UTC).Format(time.RFC3339),
	}
}
