package carrierverification

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/integrations/carrierverification/freightvalidate"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification/highway"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification/mycarrierportal"
	"github.com/drumkitai/drumkit/common/integrations/carrierverification/truckstop"
	"github.com/drumkitai/drumkit/common/models"
)

type Interface interface {
	InitialOnboard(context.Context, models.Service, models.OnboardCarrierVerificationRequest,
	) (models.OnboardCarrierVerificationResponse, error)
	GetCarrier(ctx context.Context, email string) (carrier models.CarrierVerificationResponse, err error)
	InviteCarrier(ctx context.Context, dotnumber, email string) (err error)
}

func New(ctx context.Context, carrierverification models.Integration) (Interface, error) {
	switch carrierverification.Name {
	case models.Highway:
		return highway.New(ctx, carrierverification)
	case models.MyCarrierPortal:
		return mycarrierportal.New(ctx, carrierverification)
	case models.FreightValidate:
		return freightvalidate.New(ctx, carrierverification)
	case models.Truckstop:
		return truckstop.New(ctx, carrierverification)
	default:
		return nil, fmt.Errorf("unknown Carrier Verification %s", carrierverification.Name)
	}
}

// MustNew panics if the TMS name is not recognized.
func MustNew(ctx context.Context, tms models.Integration) Interface {
	result, err := New(ctx, tms)
	if err != nil {
		panic(err)
	}

	return result
}
