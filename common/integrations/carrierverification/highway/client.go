package highway

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

type Client struct {
	integration models.Integration
}

func NewClient(integration models.Integration) API {
	return Client{integration: integration}
}

type API interface {
	Authenticate(ctx context.Context, clientID, clientSecret, redirectURI, accessCode string) (TokenResp, error)
	RefreshToken(ctx context.Context, clientID, clientSecret, refreshToken string) (RefreshTokenResp, error)
	EmailSearch(ctx context.Context, email string) (EmailSearchResp, error)
	GetCarrierByIdentifier(ctx context.Context, identifierPrefix, identifierValue string) (GetCarrierResp, error)
	GetCarrierByID(ctx context.Context, carrierID int) (GetCarrierResp, error)
	InviteCarrier(ctx context.Context, email, carrierID string) error
}

func (c Client) Authenticate(
	ctx context.Context,
	clientID, clientSecret, redirectURI, accessCode string,
) (TokenResp, error) {
	reqBody := map[string]string{
		"grant_type":    "authorization_code",
		"client_id":     clientID,
		"client_secret": clientSecret,
		"redirect_uri":  redirectURI,
		"code":          accessCode,
	}

	var tokenResp TokenResp
	body, err := json.Marshal(reqBody)
	if err != nil {
		return tokenResp, err
	}
	err = c.post(ctx, "/core/oauth/tokens", body, &tokenResp)
	if err != nil {
		return tokenResp, err
	}

	return tokenResp, nil
}

func (c Client) RefreshToken(
	ctx context.Context,
	clientID, clientSecret, refreshToken string,
) (RefreshTokenResp, error) {
	reqBody := map[string]string{
		"grant_type":    "refresh_token",
		"client_id":     clientID,
		"client_secret": clientSecret,
		"refresh_token": refreshToken,
	}
	var tokenResp RefreshTokenResp
	body, err := json.Marshal(reqBody)
	if err != nil {
		return tokenResp, err
	}
	// endpoint is exactly same as Authentication
	err = c.post(ctx, "/core/oauth/tokens", body, &tokenResp)
	if err != nil {
		return tokenResp, err
	}

	return tokenResp, nil
}

func (c Client) EmailSearch(ctx context.Context, email string) (EmailSearchResp, error) {
	reqBody := map[string]string{
		"email": email,
	}
	body, err := json.Marshal(reqBody)
	if err != nil {
		return EmailSearchResp{}, err
	}
	var emailSearch EmailSearchResp
	err = c.post(ctx, "/core/connect/external_api/v1/carriers/email_search_associated_carriers", body, &emailSearch)
	if err != nil {
		return EmailSearchResp{}, err
	}
	return emailSearch, nil
}

func (c Client) GetCarrierByIdentifier(
	ctx context.Context,
	identifierPrefix, identifierValue string,
) (GetCarrierResp, error) {
	endPoint := fmt.Sprintf("/core/connect/external_api/v1/carriers/%s/%s/by_identifier",
		identifierPrefix, identifierValue)
	var carrierResp GetCarrierResp
	err := c.get(ctx, endPoint, &carrierResp)
	if err != nil {
		return GetCarrierResp{}, err
	}
	return carrierResp, nil
}

func (c Client) GetCarrierByID(
	ctx context.Context,
	carrierID int,
) (GetCarrierResp, error) {

	endPoint := fmt.Sprintf("/core/connect/external_api/v1/carriers/%d",
		carrierID)
	var carrierResp GetCarrierResp
	err := c.get(ctx, endPoint, &carrierResp)
	if err != nil {
		return GetCarrierResp{}, err
	}
	return carrierResp, nil
}

func (c Client) InviteCarrier(ctx context.Context, email, carrierID string) error {
	postBody := map[string]string{
		"email":      email,
		"carrier_id": carrierID,
	}
	body, err := json.Marshal(postBody)
	if err != nil {
		return err
	}
	err = c.post(ctx, "/core/connect/external_api/v1/connection_invitations", body, nil)
	if err != nil {
		return err
	}
	return nil
}
