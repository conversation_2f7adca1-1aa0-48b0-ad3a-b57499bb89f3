package highway

type TokenResp struct {
	AccessToken   string `json:"access_token"`
	IDToken       string `json:"id_token"`
	RefreshToken  string `json:"refresh_token"`
	ExpiresIn     int    `json:"expires_in"`
	TokenType     string `json:"token_type"`
	Scope         string `json:"scope"`
	Sub           string `json:"sub"`
	Iss           string `json:"iss"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	UpdatedAt     string `json:"updated_at"` // time
	Connection    struct {
		ID             int    `json:"id"`
		Status         string `json:"status"`
		IssuerID       int    `json:"issuer_id"`
		ReceiverID     int    `json:"receiver_id"`
		CreatedAt      string `json:"created_at"` // time
		UpdatedAt      string `json:"updated_at"` // time
		IsOauthCreated bool   `json:"is_oauth_created"`
		Identifiers    []struct {
			IsType string `json:"is_type"`
			Value  string `json:"value"`
		} `json:"identifiers"`
		CarrierID   int  `json:"carrier_id"`
		IsMonitored bool `json:"is_monitored"`
	} `json:"connection"`
	Company struct {
		ID        int    `json:"id"`
		LegalName string `json:"legal_name"`
		CarrierID int    `json:"carrier_id"`
	} `json:"company"`
	Carrier struct {
		ID          int    `json:"id"`
		LegalName   string `json:"legal_name"`
		Name        string `json:"name"`
		IDentifiers []struct {
			IsType         string `json:"is_type"`
			Value          string `json:"value"`
			ValueNonPadded string `json:"value_non_padded"`
		} `json:"identifiers"`
	} `json:"carrier"`
}

type RefreshTokenResp struct {
	AccessToken  string `json:"access_token"`
	IDToken      string `json:"id_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
	Sub          int    `json:"sub"`
}

type EmailSearchResp struct {
	Email                                  string    `json:"email"`
	EmailDomain                            string    `json:"email_domain"`
	ContactName                            string    `json:"contact_name"`
	ContactPhoneE164                       string    `json:"contact_phone_e164"`
	EmailAddressIsTypes                    []string  `json:"email_address_is_types"`
	EmailAddressIDentityAlertsContactNames []string  `json:"email_address_identity_alerts_contact_names"`
	EmailAddressIDentityAlertsPhoneE164S   []string  `json:"email_address_identity_alerts_phone_e164s"`
	EmailAddressIDs                        []int     `json:"email_address_ids"`
	EmailAddressCarrierIDentityAlertsIDs   []int     `json:"email_address_carrier_identity_alerts_ids"`
	EmailAddressIDentityAlertIDs           []int     `json:"email_address_identity_alert_ids"`
	UserCompanyCarrierIDentityAlertIDs     []int     `json:"user_company_carrier_identity_alert_ids"`
	FlaggedEmailDomainIsType               []string  `json:"flagged_email_domain_is_type"`
	EmailAddressCarrierIDs                 []int     `json:"email_address_carrier_ids"`
	Carriers                               []Carrier `json:"carriers"`
	IDentityAlerts                         []struct {
		ID                        int      `json:"id"`
		CarrierID                 int      `json:"carrier_id"`
		DisclosedDate             string   `json:"disclosed_date"`
		IsType                    string   `json:"is_type"`
		IsPublished               bool     `json:"is_published"`
		MessageForDisplay         []string `json:"message_for_display"`
		Email                     string   `json:"email"`
		Phone                     *string  `json:"phone"`
		PhoneE164                 *string  `json:"phone_e164"`
		ContactName               *string  `json:"contact_name"`
		AddressMd5DigestFormatted *string  `json:"address_md5_digest_formatted"`
		LicensePlateState         *string  `json:"license_plate_state"`
		LicensePlateNumber        *string  `json:"license_plate_number"`
		Vin                       *string  `json:"vin"`
		VehicleType               *string  `json:"vehicle_type"`
	} `json:"identity_alerts"`
}

type Carrier struct {
	BaseCarrier
	Connection              *string             `json:"connection"`
	AuthorityAssessment     AuthorityAssessment `json:"authority_assessment"`
	Safety                  Safety              `json:"safety"`
	Authority               Authority           `json:"authority"`
	InsightsCount           int                 `json:"insights_count"`
	IDentityAlertsCount     int                 `json:"identity_alerts_count"`
	TotalObservedPowerUnits int                 `json:"total_observed_power_units"`
	TotalObservedTrailers   int                 `json:"total_observed_trailers"`
}

type GetCarrierResp struct {
	BaseCarrier
	Connection     *string      `json:"connection"`
	Identifiers    []Identifier `json:"identifiers"`
	TaxIdentifiers []struct {
		IsType string `json:"is_type"`
		Value  string `json:"value"`
		Source string `json:"source"`
	} `json:"tax_identifiers"`
	AuthorityAssessment AuthorityAssessment `json:"authority_assessment"`
	Authority           Authority           `json:"authority"`
	Addresses           []struct {
		IsType      string `json:"is_type"`
		Street1     string `json:"street1"`
		Street2     string `json:"street2"`
		City        string `json:"city"`
		PostalCode  string `json:"postal_code"`
		State       string `json:"state"`
		Nationality string `json:"nationality"`
	} `json:"addresses"`
	RulesAssessment struct {
		OverallResult string `json:"overall_result"`
		Summary       struct {
			OverallResult string `json:"overall_result"`
		} `json:"summary"`
		Classifications []struct {
			Name   string `json:"name"`
			Result string `json:"result"`
		} `json:"classifications"`
	} `json:"rules_assessment"`
	Safety struct {
		SafetyRating              string `json:"safety_rating"`
		SafetyRatingEffectiveDate string `json:"safety_rating_effective_date"`
		SafetyRatingReviewDate    string `json:"safety_rating_review_date"`
		SafetyRatingReviewType    string `json:"safety_rating_review_type"`
		LatestSmsBasic            struct {
			FileDate                          string  `json:"file_date"`
			DriverFitnessMeasure              string  `json:"driver_fitness_measure"`
			IsDriverFitnessAcuteCritical      bool    `json:"is_driver_fitness_acute_critical"`
			DriverFitnessPercentile           int     `json:"driver_fitness_percentile"`
			DriverFitnessOverThreshold        bool    `json:"driver_fitness_over_threshold"`
			HosComplianceMeasure              string  `json:"hos_compliance_measure"`
			IsHosComplianceAcuteCritical      bool    `json:"is_hos_compliance_acute_critical"`
			HosCompliancePercentile           float64 `json:"hos_compliance_percentile"`
			HosComplianceOverThreshold        bool    `json:"hos_compliance_over_threshold"`
			SubstanceAlcoholMeasure           string  `json:"substance_alcohol_measure"`
			IsSubstanceAlcoholAcuteCritical   bool    `json:"is_substance_alcohol_acute_critical"`
			SubstanceAlcoholOverThreshold     bool    `json:"substance_alcohol_over_threshold"`
			UnsafeDrivingMeasure              string  `json:"unsafe_driving_measure"`
			IsUnsafeDrivingAcuteCritical      bool    `json:"is_unsafe_driving_acute_critical"`
			UnsafeDrivingPercentile           float64 `json:"unsafe_driving_percentile"`
			UnsafeDrivingOverThreshold        bool    `json:"unsafe_driving_over_threshold"`
			VehicleMaintenanceMeasure         string  `json:"vehicle_maintenance_measure"`
			IsVehicleMaintenanceAcuteCritical bool    `json:"is_vehicle_maintenance_acute_critical"`
			VehicleMaintenancePercentile      float64 `json:"vehicle_maintenance_percentile"`
			VehicleMaintenanceOverThreshold   bool    `json:"vehicle_maintenance_over_threshold"`
		} `json:"latest_sms_basic"`
		SmsBasics []struct {
			FileDate                          string `json:"file_date"`
			DriverFitnessMeasure              string `json:"driver_fitness_measure"`
			IsDriverFitnessAcuteCritical      bool   `json:"is_driver_fitness_acute_critical"`
			HosComplianceMeasure              string `json:"hos_compliance_measure"`
			IsHosComplianceAcuteCritical      bool   `json:"is_hos_compliance_acute_critical"`
			SubstanceAlcoholMeasure           string `json:"substance_alcohol_measure"`
			IsSubstanceAlcoholAcuteCritical   bool   `json:"is_substance_alcohol_acute_critical"`
			UnsafeDrivingMeasure              string `json:"unsafe_driving_measure"`
			IsUnsafeDrivingAcuteCritical      bool   `json:"is_unsafe_driving_acute_critical"`
			VehicleMaintenanceMeasure         string `json:"vehicle_maintenance_measure"`
			IsVehicleMaintenanceAcuteCritical bool   `json:"is_vehicle_maintenance_acute_critical"`
		} `json:"sms_basics"`
	} `json:"safety"`
	Certifications struct {
		All []struct {
			IsType     string `json:"is_type"`
			ExternalID string `json:"external_id"`
			FileDate   string `json:"file_date"`
			IsVerified bool   `json:"is_verified"`
		} `json:"all"`
		Verified struct {
			Carb           bool   `json:"carb"`
			CarbTru        bool   `json:"carb_tru"`
			Hazmat         bool   `json:"hazmat"`
			SmartWay       bool   `json:"smart_way"`
			SmartWayNumber string `json:"smart_way_number"`
		} `json:"verified"`
	} `json:"certifications"`
	ContactInformation struct {
		DispatchContact struct {
			Name string `json:"name"`
		} `json:"dispatch_contact"`
		LineItemContacts []struct {
			IsType string `json:"is_type"`
			Name   string `json:"name"`
		} `json:"line_item_contacts"`
		Contacts []struct {
			IsType string `json:"is_type"`
			Name   string `json:"name"`
		} `json:"contacts"`
		Phones []struct {
			IsType string `json:"is_type"`
			Value  string `json:"value"`
		} `json:"phones"`
		EmailAddresses []struct {
			IsType string `json:"is_type"`
			Value  string `json:"value"`
		} `json:"email_addresses"`
	} `json:"contact_information"`
	Inspections struct {
		TotalInspections                 int `json:"total_inspections"`
		DriverFitnessInspections         int `json:"driver_fitness_inspections"`
		DriverFitnessOosInspections      int `json:"driver_fitness_oos_inspections"`
		HazmatInspections                int `json:"hazmat_inspections"`
		HazmatOosInspections             int `json:"hazmat_oos_inspections"`
		VehicleMaintenanceInspections    int `json:"vehicle_maintenance_inspections"`
		VehicleMaintenanceOosInspections int `json:"vehicle_maintenance_oos_inspections"`
	} `json:"inspections"`
	Crashes struct {
		Total               int `json:"total"`
		FatalCrashesTotal   int `json:"fatal_crashes_total"`
		InjuryCrashesTotal  int `json:"injury_crashes_total"`
		TowAwayCrashesTotal int `json:"tow_away_crashes_total"`
	} `json:"crashes"`
	Insurance struct {
		BipdRequired      int `json:"bipd_required"`
		InsurancePolicies []struct {
			Status                 string `json:"status"`
			IsType                 string `json:"is_type"`
			Number                 string `json:"number"`
			Limit                  string `json:"limit"`
			EffectiveDate          string `json:"effective_date"`
			ExpirationDate         string `json:"expiration_date"`
			InsuranceCertificateID int    `json:"insurance_certificate_id"`
			ScheduledAutos         bool   `json:"scheduled_autos,omitempty"`
			AnyAuto                bool   `json:"any_auto,omitempty"`
			OwnedAutos             bool   `json:"owned_autos,omitempty"`
			HiredAutos             bool   `json:"hired_autos,omitempty"`
			NonOwnedAutos          bool   `json:"non_owned_autos,omitempty"`
			InsuranceInsurer       struct {
				Name       string `json:"name"`
				ShortName  string `json:"short_name"`
				Naic       string `json:"naic"`
				Street1    string `json:"street1"`
				City       string `json:"city"`
				State      string `json:"state"`
				Phone      string `json:"phone"`
				PostalCode string `json:"postal_code"`
				Website    string `json:"website"`
			} `json:"insurance_insurer"`
			IsRefrigerationBreakdownIncluded bool `json:"is_refrigeration_breakdown_included,omitempty"`
		} `json:"insurance_policies"`
		InsuranceCertificates []struct {
			ID   int `json:"id"`
			File struct {
				ID          int    `json:"id"`
				Filename    string `json:"filename"`
				ContentType string `json:"content_type"`
				URL         string `json:"url"`
			} `json:"file"`
		} `json:"insurance_certificates"`
	} `json:"insurance"`
	CarrierModes []struct {
		IsType string `json:"is_type"`
	} `json:"carrier_modes"`
	Operations struct {
		Mcs150FormDate      string   `json:"mcs150_form_date"`
		Mcs150Year          int      `json:"mcs150_year"`
		Mcs150Mileage       int      `json:"mcs150_mileage"`
		Classifications     []string `json:"classifications"`
		OperatingStatus     string   `json:"operating_status"`
		OperatingStatusDate string   `json:"operating_status_date"`
		FleetSize           string   `json:"fleet_size"`
		CargoCarried        []struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"cargo_carried"`
		EntityTypes []struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"entity_types"`
		TotalPowerUnits    int `json:"total_power_units"`
		OwnedTractors      int `json:"owned_tractors"`
		OwnedTrailers      int `json:"owned_trailers"`
		TermLeasedTractors int `json:"term_leased_tractors"`
		TermLeasedTrailers int `json:"term_leased_trailers"`
		TotalDrivers       int `json:"total_drivers"`
	} `json:"operations"`
	Equipment struct {
		Summary struct {
			VanTrailers             int `json:"van_trailers"`
			ReeferTrailers          int `json:"reefer_trailers"`
			FlatbedTrailers         int `json:"flatbed_trailers"`
			TankerTrailers          int `json:"tanker_trailers"`
			IntermodalTrailers      int `json:"intermodal_trailers"`
			PneumaticTrailers       int `json:"pneumatic_trailers"`
			DumpsTrailers           int `json:"dumps_trailers"`
			CarHaulerTrailers       int `json:"car_hauler_trailers"`
			DoubleDropTrailers      int `json:"double_drop_trailers"`
			LowboyTrailers          int `json:"lowboy_trailers"`
			GrainTrailers           int `json:"grain_trailers"`
			LivestockTrailers       int `json:"livestock_trailers"`
			HeavyHaulTrailers       int `json:"heavy_haul_trailers"`
			DropDeckTrailers        int `json:"drop_deck_trailers"`
			RefuseTransferTrailers  int `json:"refuse_transfer_trailers"`
			ForestryTrailers        int `json:"forestry_trailers"`
			UncategorizedTrailers   int `json:"uncategorized_trailers"`
			TotalObservedTrailers   int `json:"total_observed_trailers"`
			TruckTractors           int `json:"truck_tractors"`
			CargoVans               int `json:"cargo_vans"`
			StraightBoxTrucks       int `json:"straight_box_trucks"`
			Buses                   int `json:"buses"`
			UncategorizedTrucks     int `json:"uncategorized_trucks"`
			TotalObservedPowerUnits int `json:"total_observed_power_units"`
		} `json:"summary"`
	} `json:"equipment"`
}

type InviteCarrierResp struct {
	ID          int    `json:"id"`
	Email       string `json:"email"`
	CreatedAt   string `json:"created_at"`
	DeliveredAt string `json:"delivered_at"`
	Status      string `json:"status"`
	CarrierID   int    `json:"carrier_id"`
	Issuer      struct {
		ID                    int    `json:"id"`
		LegalName             string `json:"legal_name"`
		Status                string `json:"status"`
		CompanyType           string `json:"company_type"`
		SignInWithHighwayLink string `json:"sign_in_with_highway_link"`
	} `json:"issuer"`
	Creator struct {
		ID              int      `json:"id"`
		Email           string   `json:"email"`
		IsVerifiedEmail bool     `json:"is_verified_email"`
		IsVerifiedPhone bool     `json:"is_verified_phone"`
		DbaName         *string  `json:"dba_name"`
		FirstName       string   `json:"first_name"`
		LastName        string   `json:"last_name"`
		Scopes          []string `json:"scopes"`
		IsActive        bool     `json:"is_active"`
	} `json:"creator"`
	Receiver struct {
		ID          int     `json:"id"`
		LegalName   string  `json:"legal_name"`
		DbaName     *string `json:"dba_name"`
		CarrierID   int     `json:"carrier_id"`
		Status      string  `json:"status"`
		CompanyType string  `json:"company_type"`
	} `json:"receiver"`
}

// Common base structures that are reused across multiple types

type Authority struct {
	Authorities struct {
		CommonAuthorityStatus                string `json:"common_authority_status"`
		ContinuousAuthorityGrantDateCommon   string `json:"continuous_authority_grant_date_common"`
		ContractAuthorityStatus              string `json:"contract_authority_status"`
		ContinuousAuthorityGrantDateContract string `json:"continuous_authority_grant_date_contract"`
		BrokerAuthorityStatus                string `json:"broker_authority_status"`
		ContinuousAuthorityGrantDateBroker   string `json:"continuous_authority_grant_date_broker"`
	} `json:"authorities"`
}

type AuthorityAssessment struct {
	Rating                             string `json:"rating"`
	CarrierInterstateAuthorityCheck    string `json:"carrier_interstate_authority_check"`
	BrokerInterstateAuthorityCheck     string `json:"broker_interstate_authority_check"`
	IsActiveInsurance                  bool   `json:"is_active_insurance"`
	NoOutOfServiceOrder                bool   `json:"no_out_of_service_order"`
	IsSatisfactorySafetyRating         bool   `json:"is_satisfactory_safety_rating"`
	IsOperatingStatusActive            bool   `json:"is_operating_status_active"`
	IsInspectionHistoryGreaterThanZero bool   `json:"is_inspection_history_greater_than_zero"`
	LatestSafetyRating                 string `json:"latest_safety_rating"`
}

type Safety struct {
	SafetyRating string `json:"safety_rating"`
}

type BaseCarrier struct {
	ID        int     `json:"id"`
	LegalName string  `json:"legal_name"`
	DbaName   *string `json:"dba_name"`
	DotNumber int     `json:"dot_number"`
	McNumber  int     `json:"mc_number"`
}

type Identifier struct {
	IsType string `json:"is_type"`
	Value  string `json:"value"`
}
