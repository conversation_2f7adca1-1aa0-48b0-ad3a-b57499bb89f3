package freightvalidate

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
)

func (c Client) get(
	ctx context.Context,
	endPoint string,
	dst any,
) error {
	queryURL := baseURL + endPoint
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, queryURL, nil)
	if err != nil {
		return fmt.Errorf("failed to build GET request: %w", err)
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Authorization", "Bearer "+c.integration.AccessToken)

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(c.integration, req, resp, body)
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (c Client) post(
	ctx context.Context,
	endPoint string,
	postRequest []byte,
	dst any,
) error {
	queryURL := baseURL + endPoint
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, queryURL, bytes.NewBuffer(postRequest))
	if err != nil {
		return fmt.Errorf("failed to build POST request: %w", err)
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")

	if endPoint != "/api/login" {
		req.Header.Add("Authorization", "Bearer "+c.integration.AccessToken)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(c.integration, req, resp, body)
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}
