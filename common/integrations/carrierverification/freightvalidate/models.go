package freightvalidate

import "time"

type TokenResp struct {
	Data struct {
		AccessToken string `json:"token"`
		ExpiresAt   string `json:"expires_at"`
	}
}

type EmailSearchResp struct {
	Success bool `json:"success"`
	Data    struct {
		Email     string `json:"email"`
		CompanyID int    `json:"company_id"`
	} `json:"data"`
	Message string `json:"message"`
}

type GetCompanyResp struct {
	Success bool `json:"success"`
	Data    struct {
		ID           int    `json:"id"`
		Name         string `json:"name"`
		DocketNumber string `json:"docket_number"`
		DotNumber    int    `json:"dot_number"`
		//TODO: Validate if carrier in future
		CompanyTypeID int    `json:"company_type_id"`
		Address       string `json:"address"`
		City          string `json:"city"`
		State         string `json:"state"`
		Zip           string `json:"zip"`
	} `json:"data"`
	Message string `json:"message"`
}

type GetCarrierResp struct {
	Data struct {
		DocketNumber      string             `json:"docketNumber"`
		DotNumber         int                `json:"dotNumber"`
		Status            string             `json:"status"`
		VettingConcerns   VettingConcerns    `json:"vettingConcerns"`
		CompanyName       string             `json:"companyName"`
		CompanyType       string             `json:"companyType"`
		CompanyAddress    string             `json:"companyAddress"`
		CompanyContact    string             `json:"companyContact"`
		CompanyEmail      string             `json:"companyEmail"`
		CompanyPhone      string             `json:"companyPhone"`
		CompanyCell       string             `json:"companyCell"`
		ValidationDetails []ValidationDetail `json:"validationDetails"`
		Authority         Authority          `json:"authority"`
		UpdatedAt         string             `json:"updated_at"`
	} `json:"data"`
}

type VettingConcerns struct {
	Authority bool `json:"authority"`
	Address   bool `json:"address"`
	Insurance bool `json:"insurance"`
	Phone     bool `json:"phone"`
	Cell      bool `json:"cell"`
	Email     bool `json:"email"`
}

type ValidationDetail struct {
	Rule    string `json:"rule"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

type Authority struct {
	AuthCommon             string `json:"authCommon"`
	AuthCommonRevocation   bool   `json:"authCommonRevocation"`
	AuthContract           string `json:"authContract"`
	AuthContractRevocation bool   `json:"authContractRevocation"`
	AuthBroker             string `json:"authBroker"`
	AuthBrokerRevocation   bool   `json:"authBrokerRevocation"`
	DotStatus              string `json:"dotStatus"`
}

type CompanyValidation struct {
	DocketNumber      string             `json:"docketNumber"`
	DotNumber         int                `json:"dotNumber"`
	Status            string             `json:"status"`
	VettingConcerns   VettingConcerns    `json:"vettingConcerns"`
	CompanyName       string             `json:"companyName"`
	CompanyType       string             `json:"companyType"`
	CompanyAddress    string             `json:"companyAddress"`
	CompanyContact    string             `json:"companyContact"`
	CompanyEmail      string             `json:"companyEmail"`
	CompanyPhone      string             `json:"companyPhone"`
	CompanyCell       string             `json:"companyCell"`
	ValidationDetails []ValidationDetail `json:"validationDetails"`
	Authority         Authority          `json:"authority"`
	CreatedAt         time.Time          `json:"created_at"`
	UpdatedAt         time.Time          `json:"updated_at"`
}

type Response struct {
	Success bool              `json:"success"`
	Data    CompanyValidation `json:"data"`
	Message string            `json:"message"`
}
