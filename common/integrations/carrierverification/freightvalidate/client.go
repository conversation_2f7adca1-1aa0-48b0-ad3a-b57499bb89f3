package freightvalidate

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
)

type Client struct {
	integration models.Integration
}

func NewClient(integration models.Integration) API {
	return Client{integration: integration}
}

type API interface {
	Authenticate(ctx context.Context, username, password string) (TokenResp, error)
	EmailSearch(ctx context.Context, email string) (EmailSearchResp, error)
	ShowCompany(ctx context.Context, companyID int) (GetCompanyResp, error)
	GetCarrierByID(ctx context.Context, carrierID int) (GetCarrierResp, error)
}

func (c Client) Authenticate(
	ctx context.Context,
	username, password string,
) (TokenResp, error) {
	reqBody := map[string]string{
		"email":    username,
		"password": password,
	}

	var tokenResp TokenResp
	body, err := json.Marshal(reqBody)
	if err != nil {
		return tokenResp, err
	}
	err = c.post(ctx, "/api/login", body, &tokenResp)
	if err != nil {
		return tokenResp, err
	}

	return tokenResp, nil
}

func (c Client) RefreshToken(
	ctx context.Context,
	username, password string,
) (TokenResp, error) {
	return c.Authenticate(ctx, username, password)
}

func (c Client) EmailSearch(ctx context.Context, email string) (EmailSearchResp, error) {
	var emailSearch EmailSearchResp
	err := c.get(ctx, fmt.Sprintf("/api/v1/users/search/?email=%s", email), &emailSearch)
	if err != nil {
		return EmailSearchResp{}, err
	}
	return emailSearch, nil
}

func (c Client) ShowCompany(
	ctx context.Context,
	companyID int,
) (GetCompanyResp, error) {
	endPoint := fmt.Sprintf("/api/v1/company/%d", companyID)
	var companyResp GetCompanyResp
	err := c.get(ctx, endPoint, &companyResp)
	if err != nil {
		return GetCompanyResp{}, err
	}
	return companyResp, nil
}

func (c Client) GetCarrierByID(
	ctx context.Context,
	id int,
) (GetCarrierResp, error) {
	endPoint := fmt.Sprintf("/api/v1/company/validation/dotNumber/%d",
		id)
	var carrierResp GetCarrierResp
	err := c.get(ctx, endPoint, &carrierResp)
	if err != nil {
		return GetCarrierResp{}, err
	}
	return carrierResp, nil
}
