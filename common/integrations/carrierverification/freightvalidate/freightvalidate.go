package freightvalidate

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

const baseURL = "https://freightvalidate.com"

type FreightValidate struct {
	integration models.Integration
}

func New(ctx context.Context, integration models.Integration) (FreightValidate, error) {
	log.With(ctx, zap.Uint("drumkitIntegrationID", integration.ID), zap.String("integration", "freightvalidate"))

	if integration.AccessToken != "" && integration.NeedsRefresh() {
		client := NewClient(integration)
		decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(integration.EncryptedPassword), nil)
		if err != nil {
			return FreightValidate{}, fmt.Errorf("error decrypting password: %w", err)
		}

		refreshTokenResp, err := client.Authenticate(ctx, integration.Username,
			decryptedPassword)
		if err != nil {
			return FreightValidate{}, err
		}
		integration.AccessToken = refreshTokenResp.Data.AccessToken
		tokenExpTime, err := helpers.ParseDatetime(refreshTokenResp.Data.ExpiresAt)
		if err != nil {
			return FreightValidate{}, err
		}

		integration.AccessTokenExpirationDate = models.NullTime{
			Time:  tokenExpTime,
			Valid: true,
		}

		if integration.ID != 0 {
			if err = integrationDB.Update(ctx, &integration); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update freightvalidate info on integration db",
					zap.Any("integration", integration),
				)
				return FreightValidate{}, fmt.Errorf("integration db update failed: %w", err)
			}
		}
	}

	return FreightValidate{integration: integration}, nil
}

func (f FreightValidate) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	request models.OnboardCarrierVerificationRequest,
) (models.OnboardCarrierVerificationResponse, error) {
	tokenResp, err := NewClient(f.integration).Authenticate(ctx, request.Username, request.Password)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, err
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, request.Password, nil)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	// calculating token expiration time
	tokenExpTime, err := helpers.ParseDatetime(tokenResp.Data.ExpiresAt)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, err
	}

	return models.OnboardCarrierVerificationResponse{
		AccessToken:               tokenResp.Data.AccessToken,
		Username:                  request.Username,
		EncryptedPassword:         encryptedPassword,
		AccessTokenExpirationDate: tokenExpTime,
	}, nil
}

// GetCarrier docs: https://docs.freightvalidate.com/reference/email-search
func (f FreightValidate) GetCarrier(
	ctx context.Context,
	email string,
) (carrier models.CarrierVerificationResponse, err error) {

	client := NewClient(f.integration)
	emailData, err := client.EmailSearch(ctx, email)
	if err != nil {
		return carrier, err
	}

	companyData, err := client.ShowCompany(ctx, emailData.Data.CompanyID)
	if err != nil {
		return carrier, err
	}

	carrierData, err := client.GetCarrierByID(ctx, companyData.Data.DotNumber)
	if err != nil {
		return carrier, err
	}

	carrier.CarrierName = carrierData.Data.CompanyName
	carrier.DOTNumber = carrierData.Data.DotNumber
	carrier.RiskRating = carrierData.Data.Status
	carrier.IntegrationName = string(models.FreightValidate)

	return carrier, nil
}

// InviteCarrier docs : https://docs.freightvalidate.com/reference/create-2
func (f FreightValidate) InviteCarrier(context.Context, string, string) (err error) {
	return
}
