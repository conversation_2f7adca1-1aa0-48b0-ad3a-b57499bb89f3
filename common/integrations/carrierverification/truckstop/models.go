package truckstop

type TokenResp struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Claims       string `json:"claims"`
}

type EmailSearchResponse struct {
	DOTNumbers []string `json:"dotNumbers"`
}

type ResultCount struct {
	QueriedCarrierIDCount int      `json:"queriedCarrierIdCount"`
	KnownCarrierIDCount   int      `json:"knownCarrierIdCount"`
	UnknownCarrierIDCount int      `json:"unknownCarrierIdCount"`
	UnknownCarrierIDs     []string `json:"unknownCarrierIds"`
}

type RiskSubVector struct {
	RiskLevel   string   `json:"riskLevel"`
	SubCategory string   `json:"subCategory"`
	ContextData []string `json:"contextData"`
}

type RiskVector struct {
	RiskLevel      string          `json:"riskLevel"`
	Category       string          `json:"category"`
	RiskSubVectors []RiskSubVector `json:"riskSubVectors"`
}

type Identifiers struct {
	DOTNumber    string   `json:"dotNumber"`
	MCNumbers    []string `json:"mcNumbers"`
	Emails       []string `json:"emails"`
	PhoneNumbers []string `json:"phoneNumbers"`
	Addresses    []string `json:"addresses"`
	IPAddresses  []string `json:"ipAddresses"`
	VINNumbers   []string `json:"vinNumbers"`
}

type Report struct {
	Identifiers   Identifiers  `json:"identifiers"`
	ReportSummary string       `json:"reportSummary"`
	ReportDetails string       `json:"reportDetails"`
	RiskVectors   []RiskVector `json:"riskVectors"`
	UpdatedAt     string       `json:"updatedAt"`
}
