package truckstop

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const BaseURL = "https://api.truckstop.com/"

type (
	Client struct {
		integration models.Integration
		host        string
	}

	API interface {
		Authenticate(ctx context.Context, code, redirectURI string) (TokenResp, error)
		RefreshToken(ctx context.Context) (TokenResp, error)
		GetCarrier(ctx context.Context, email string) (Report, error)
		InviteCarrier(ctx context.Context, dotnumber, email string) error
	}
)

func NewClient(integration models.Integration) API {
	host := "api.truckstop.com"

	return &Client{
		integration: integration,
		host:        host,
	}
}

func (c Client) Authenticate(ctx context.Context, code, redirectURI string) (TokenResp, error) {
	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", redirectURI)

	var token TokenResp
	resp, err := c.do(ctx, http.MethodPost, "/auth/token", nil, strings.NewReader(data.Encode()), &token)

	if err != nil {
		log.Error(ctx, "truckstop authentication failed",
			zap.Error(err),
			zap.Int("status", resp.Status),
			zap.String("body", resp.Body))
	} else {
		log.Info(ctx, "truckstop authentication successful")
	}

	return token, err
}

func (c Client) GetCarrier(ctx context.Context, email string) (Report, error) {
	emailSearchResponse := EmailSearchResponse{}
	carrierResponse := Report{}

	query := url.Values{}
	query.Add("email", email)

	_, err := c.Get(
		ctx,
		"riskfactoranalysis/v1/carriers/search",
		query,
		nil,
		&emailSearchResponse,
	)
	if err != nil {
		return Report{}, err
	}

	if len(emailSearchResponse.DOTNumbers) == 0 {
		return Report{}, errors.New("no associated carriers found")
	}

	_, err = c.Get(
		ctx,
		fmt.Sprintf("riskfactoranalysis/v1/carriers/%s", emailSearchResponse.DOTNumbers[0]),
		nil,
		nil,
		&carrierResponse,
	)
	return carrierResponse, err
}

func (c Client) InviteCarrier(context.Context, string, string) error {
	return nil
}

func (c Client) RefreshToken(ctx context.Context) (TokenResp, error) {
	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", c.integration.RefreshToken)

	var token TokenResp
	_, err := c.do(ctx, http.MethodPost, "/auth/token?scope=truckstop", nil, strings.NewReader(data.Encode()), &token)
	return token, err
}
