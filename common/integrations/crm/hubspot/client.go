package hubspot

import (
	"context"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type HubSpot struct {
	crm models.Integration
}

type API interface {
	SubmitForm(ctx context.Context, formID, portalID string, req *FormSubmission) (*FormSubmissionResponse, error)
}

func New(ctx context.Context, crm models.Integration) API {
	log.With(ctx, zap.Uint("axleCRMID", crm.ID), zap.String("crmName", "hubspot"))

	return &HubSpot{crm: crm}
}
