package hubspot

type (
	RefreshTokenResponse struct {
		RefreshToken string `json:"refresh_token"`
		AccessToken  string `json:"access_token"`
		ExpiresIn    int    `json:"expires_in"`
	}
)

// https://developers.hubspot.com/docs/api/oauth-quickstart-guide#refreshing_tokens
// Not needed for private apps which have a static access token, but required for public, marketplace apps
// func (h *HubSpot) refreshToken(ctx context.Context, clientID, _, refreshToken string) error {
// 	path := "oauth/v1/token"

// 	query := make(url.Values)
// 	query.Add("client_id", clientID)
// 	query.Add("client_secret", clientID)
// 	query.Add("refresh_token", refreshToken)

// 	url := (&url.URL{
// 		Scheme:   "https",
// 		Host:     "api.hubapi.com",
// 		Path:     path,
// 		RawQuery: query.Encode(),
// 	}).String()

// 	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, nil)
// 	if err != nil {
// 		return fmt.Errorf("failed to build %s %s request: %w", http.MethodPost, url, err)
// 	}

// 	resp, err := otel.TracingHTTPClient().Do(req)
// 	if err != nil {
// 		return fmt.Errorf("failed to send %s %s request: %w", http.MethodPost, url, err)
// 	}
// 	defer resp.Body.Close()

// 	body, err := io.ReadAll(resp.Body)
// 	if err != nil {
// 		return fmt.Errorf("failed to read %s response body: %w", url, err)
// 	}

// 	var res RefreshTokenResponse

// 	if err = json.Unmarshal(body, &res); err != nil {
// 		h.crm.AccessToken = res.AccessToken
// 		h.crm.RefreshToken = res.RefreshToken

// 		if err = integrationDB.Update(ctx, &h.crm); err != nil {
// 			return fmt.Errorf("failed to update %s token in DB: %w", url, err)
// 		}
// 	}

// 	return nil
// }
