# HubSpot Go SDK

This is an internal integration for now, therefore onboarding is manual for now.

## Usage

### Onboarding
Onboarding a HubSpot integration is similar to onboarding a [TSP](https://github.com/drumkitai/oracle/tree/main/integrations) in our [API product](https://github.com/drumkitai/mercury). The only difference is that Drumkit doesn't have a sister product like [Link](https://github.com/drumkitai/link) to streamline onboarding.

That's why we have to onboard it manually outlined [here](https://developers.hubspot.com/docs/api/working-with-oauth). After onboarding, we have create a database insertion in the Integrations table manually with the access / refresh tokens and corresponding associations.
