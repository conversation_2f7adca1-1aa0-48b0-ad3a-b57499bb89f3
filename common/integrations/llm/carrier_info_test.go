package llm

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

func TestLiveCarrierInfoExtractionWithSimpleSignature(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveCarrierInfoExtractionWithSignatures: run with LIVE_TEST=true to enable")
		return
	}

	mockRDS := new(MockRDS)
	openaiService := new(MockOpenaiService)
	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Sender:    "<NAME_EMAIL>",
		Body: `This is the same info for the load 2128013, please double check
		*FILIPE OLIVEIRA*
		Software Engineering | Axle Technologies
		[image: emailAddress] <EMAIL>`,
		Loads: []models.Load{mockedLoad},
	}

	var expectedResult []models.SuggestedLoadChange

	// Mocking external calls
	mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

	// Live testing with OpenAI
	result, err := ExtractCarrierInfoSuggestion(context.Background(), mockedEmail, openaiService, mockRDS)
	result[0].BraintrustSpanID = ""

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockRDS.AssertExpectations(t)
}

func TestLiveCarrierInfoExtractionWithComplexSignature(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveCarrierInfoExtractionWithSignatures: run with LIVE_TEST=true to enable")
		return
	}

	mockRDS := new(MockRDS)
	openaiService := new(MockOpenaiService)
	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Sender:    "<NAME_EMAIL>",
		Body: `This is the same info for the load 2128013, please double check
		Emma SanabriaLogistics CoordinatorNFI Industries (856)-<EMAIL>
		www.abcindustries.com24hr Shipment Support: 855-409-0012  <EMAIL>
		Terms and Conditions`,
		Loads: []models.Load{mockedLoad},
	}

	var expectedResult []models.SuggestedLoadChange

	// Mocking external calls
	mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

	// Live testing with OpenAI
	result, err := ExtractCarrierInfoSuggestion(context.Background(), mockedEmail, openaiService, mockRDS)
	result[0].BraintrustSpanID = ""

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockRDS.AssertExpectations(t)
}

func TestCarrierInfoExtraction(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)
	mockRDS := new(MockRDS)
	ctx := context.Background()

	var responseContent = `
	{
		"carrier_info": {
				"first_driver_name": "Alice Bob",
				"first_driver_phone": "************",
				"second_driver_name": "John Doe",
				"second_driver_phone": "************",
				"truck_number": "222",
				"trailer_number": "333",
				"dispatch_city": "Chicago",
				"dispatch_state": "IL",
				"expected_pickup_date": "2024-09-13",
				"expected_pickup_time": "11:00"
			}
		}`

	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body: `Please see below for driver details,
                       Driver: Alice Bob, ************
                       John Doe, ************
                       Truck: 222
                       Trailer: 333
                       Currently in Chicago,IL. ETA is 1100`,
		Loads: []models.Load{mockedLoad},
	}

	var expectedResult = []models.SuggestedLoadChange{
		{
			Account:           "<EMAIL>",
			FreightTrackingID: "2080005",
			ServiceID:         1,
			Suggested: models.SuggestedChanges{
				Changes: &models.Changes{
					FirstDriverName:    "Alice Bob",
					FirstDriverPhone:   "************",
					SecondDriverName:   "John Doe",
					SecondDriverPhone:  "************",
					TruckNumber:        "222",
					TrailerNumber:      "333",
					DispatchCity:       "Chicago",
					DispatchState:      "IL",
					ExpectedPickupTime: StringsToTime(ctx, "2024-09-13", "11:00", ""),
				},
			},
			Applied:  models.SuggestedChanges{},
			Status:   models.Pending,
			Category: models.CarrierInfo,
			Pipeline: models.CarrierInfoPipeline,
			LoadID:   &mockedLoad.ID,
			Load:     mockedLoad,
			EmailID:  mockedEmail.ID,
			ThreadID: mockedEmail.ThreadID,
			Email:    mockedEmail,
		},
	}

	// Mocking external calls
	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(responseContent), nil)
	mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

	result, err := ExtractCarrierInfoSuggestion(context.Background(), mockedEmail, mockOpenAI, mockRDS)
	result[0].BraintrustSpanID = ""

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockOpenAI.AssertExpectations(t)
	mockRDS.AssertExpectations(t)
}

func TestExtractCarrierInfoNotFound(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)
	mockRDS := new(MockRDS)

	var responseContent = `
		{
		"carrier_info": {}
		}`

	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body: `Can you confirm driver truck and trailer info?
		TIME: 1100`,
		Loads: []models.Load{mockedLoad},
	}

	var expectedResult = []models.SuggestedLoadChange{
		{
			Account:           "<EMAIL>",
			FreightTrackingID: "2080005",
			ServiceID:         1,
			Suggested: models.SuggestedChanges{
				Changes: &models.Changes{},
			},
			Applied:  models.SuggestedChanges{},
			Status:   models.Pending,
			Category: models.CarrierInfo,
			Pipeline: models.CarrierInfoPipeline,
			LoadID:   &mockedLoad.ID,
			Load:     mockedLoad,
			EmailID:  mockedEmail.ID,
			ThreadID: mockedEmail.ThreadID,
			Email:    mockedEmail,
		},
	}
	// Mocking external calls with correct parameters
	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(responseContent), nil)

	mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

	result, err := ExtractCarrierInfoSuggestion(context.Background(), mockedEmail, mockOpenAI, mockRDS)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockOpenAI.AssertExpectations(t)
	mockRDS.AssertExpectations(t)
}
