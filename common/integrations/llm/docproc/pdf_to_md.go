package docproc

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	md "github.com/<PERSON><PERSON>/html-to-markdown"
	plugins "github.com/<PERSON><PERSON>/html-to-markdown/plugin"
	"github.com/gen2brain/go-fitz"
	"github.com/microcosm-cc/bluemonday"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/processor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
)

// PDFToMD converts a PDF file to markdown using gofitz and OpenAI's vision API.
//
// It converts each page to an image (with configurable DPI and size), encodes it,
// and sends it to OpenAI's vision model for markdown conversion.
//
// Returns concatenated markdown content from all pages.
func PDFToMD(
	ctx context.Context,
	openaiService openai.Service,
	fileData []byte,
	options ...Option,
) (string, error) {
	opts := &Options{
		SystemPrompt:      constants.PDFToMDSystemPrompt,
		ImageDensity:      constants.DefaultDPI,
		ImageHeight:       constants.DefaultImageHeight,
		ImageWidth:        constants.DefaultImageWidth,
		ImageFormat:       constants.DefaultImageFormat,
		JPEGQuality:       constants.DefaultJPEGQuality,
		Concurrency:       constants.DefaultConcurrency,
		MaxPagesToProcess: constants.DefaultMaxPagesToProcess,
		MaintainFormat:    false,
		ProcessBlankPages: false,
		FileName:          "NOT_PROVIDED",
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	pages, _, err := processPDFPages(ctx, openaiService, fileData, opts)
	if err != nil {
		return "", err
	}

	// Extract markdown from all pages
	var markdownPages []string
	for _, page := range pages {
		if page.Content != "" {
			markdownPages = append(markdownPages, page.Content)
		}
	}

	return strings.Join(markdownPages, "\n\n"), nil
}

// PDFToMDWithOutput converts a PDF to markdown and returns timing, and per-page results.
func PDFToMDWithOutput(
	ctx context.Context,
	openaiService openai.Service,
	fileData []byte,
	options ...Option,
) (*PDFToMDOutput, error) {

	ctx, span := otel.StartSpan(ctx, "docproc.PDFToMD", nil)
	defer span.End(nil)

	startTime := time.Now()

	opts := &Options{
		SystemPrompt:      constants.PDFToMDSystemPrompt,
		ImageDensity:      constants.DefaultDPI,
		ImageHeight:       constants.DefaultImageHeight,
		ImageWidth:        constants.DefaultImageWidth,
		ImageFormat:       constants.DefaultImageFormat,
		JPEGQuality:       constants.DefaultJPEGQuality,
		Concurrency:       constants.DefaultConcurrency,
		MaxPagesToProcess: constants.DefaultMaxPagesToProcess,
		MaintainFormat:    false,
		ProcessBlankPages: false,
		FileName:          "NOT_PROVIDED",
	}
	for _, opt := range options {
		opt(opts)
	}
	ctx = LogWithFileName(ctx, opts.FileName)

	log.Info(
		ctx,
		"Processing PDF",
		zap.String("model", getModel(opts)),
		zap.Int("imageDensity", opts.ImageDensity),
		zap.Uint("imageHeight", opts.ImageHeight),
		zap.String("imageFormat", string(opts.ImageFormat)),
		zap.Int("jpegQuality", opts.JPEGQuality),
		zap.Bool("maintainFormat", opts.MaintainFormat),
		zap.Bool("processBlankPages", opts.ProcessBlankPages),
	)

	pages, totalPages, err := processPDFPages(ctx, openaiService, fileData, opts)
	if err != nil {
		return nil, err
	}

	completionTime := time.Since(startTime).Milliseconds()

	log.Info(
		ctx,
		"PDF processing complete",
		zap.String("model", getModel(opts)),
		zap.Int("totalPages", totalPages),
		zap.Int("successfulPages", len(pages)),
		zap.Int64("completionTimeMs", completionTime),
		zap.Int("imageDensity", opts.ImageDensity),
		zap.String("imageFormat", string(opts.ImageFormat)),
		zap.Int("jpegQuality", opts.JPEGQuality),
		zap.Bool("maintainFormat", opts.MaintainFormat),
	)

	// Extract markdown from all pages
	var markdownPages []string
	for _, page := range pages {
		if page.Content != "" {
			markdownPages = append(markdownPages, page.Content)
		}
	}

	markdown := strings.Join(markdownPages, "\n\n")

	return &PDFToMDOutput{
		CompletionTime: float64(completionTime),
		FileName:       opts.FileName,
		Markdown:       markdown,
		Pages:          pages,
	}, nil
}

// processPDFPages converts a PDF to per-page markdown content using go-fitz and OpenAI's vision API.
// It returns the successfully processed pages and the total number of pages in the document.
func processPDFPages(
	ctx context.Context,
	openaiService openai.Service,
	fileData []byte,
	opts *Options,
) ([]Page, int, error) {

	var doc *fitz.Document
	var err error

	if opts.FilePath != "" {
		doc, err = fitz.New(opts.FilePath)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to open PDF from file path %s: %w", opts.FilePath, err)
		}
	} else {
		doc, err = fitz.NewFromMemory(fileData)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to open PDF from memory: %w", err)
		}
	}
	defer doc.Close()

	numPages := doc.NumPage()
	if numPages == 0 {
		return nil, 0, errors.New("PDF has no pages")
	}

	pagesToProcess := opts.SelectPages
	if pagesToProcess == nil {
		// Process all pages
		pagesToProcess = make([]int, numPages)
		for i := 0; i < numPages; i++ {
			pagesToProcess[i] = i + 1 // 1-indexed
		}
	}

	// Enforce a maximum number of pages to process.
	// Behavior: processes the first N pages. If SelectPages is provided,
	// only the first N entries from SelectPages are used.
	if opts.MaxPagesToProcess > 0 && len(pagesToProcess) > opts.MaxPagesToProcess {
		log.Info(
			ctx,
			"Processing limited to first N pages",
			zap.Int("pagesToProcess", opts.MaxPagesToProcess),
			zap.Int("totalPages", numPages),
		)
		pagesToProcess = pagesToProcess[:opts.MaxPagesToProcess]
	}

	var pages []Page

	useConcurrentProcessing := !opts.MaintainFormat && opts.Concurrency > 1

	if useConcurrentProcessing {
		log.Info(
			ctx,
			"Using concurrent page processing",
			zap.Int("concurrency", opts.Concurrency),
			zap.Int("totalPages", len(pagesToProcess)),
		)

		// Filter valid page indices (based on SelectPages option)
		var validPageIndices []int
		var validPageNums []int
		for _, pageNum := range pagesToProcess {
			pageIdx := pageNum - 1
			if pageIdx >= 0 && pageIdx < numPages {
				validPageIndices = append(validPageIndices, pageIdx)
				validPageNums = append(validPageNums, pageNum)
			} else {
				log.WarnNoSentry(
					ctx,
					"Page number out of range, skipping",
					zap.Int("page", pageNum),
					zap.Int("totalPages", numPages),
				)
			}
		}

		results := processor.ProcessPagesConcurrently(
			ctx,
			doc,
			validPageIndices,
			openaiService,
			processor.ProcessPageOptions{
				SystemPrompt:      opts.SystemPrompt,
				Model:             getModel(opts),
				FilePath:          opts.FilePath,
				ImageDensity:      opts.ImageDensity,
				ImageWidth:        opts.ImageWidth,
				ImageHeight:       opts.ImageHeight,
				JPEGQuality:       opts.JPEGQuality,
				Format:            opts.ImageFormat,
				MaintainFormat:    false, // Always false for concurrent processing
				ProcessBlankPages: opts.ProcessBlankPages,
				PriorPage:         "",
			},
			opts.Concurrency,
		)

		// Collect successful results
		for i, result := range results {
			if result.Error != nil {
				log.Error(ctx, "Failed to process page", zap.Int("page", validPageNums[i]), zap.Error(result.Error))
				continue
			}

			if result.Content != "" {
				pages = append(pages, Page{
					Content:       result.Content,
					ContentLength: len(result.Content),
					Page:          validPageNums[i],
				})
			}
		}
	} else {
		// Sequential processing (default or when MaintainFormat is true)
		log.Debug(
			ctx,
			"Using sequential page processing",
			zap.Bool("maintainFormat", opts.MaintainFormat),
			zap.Int("totalPages", len(pagesToProcess)),
		)

		var priorPage string
		for _, pageNum := range pagesToProcess {
			// Convert to 0-indexed
			pageIdx := pageNum - 1

			if pageIdx < 0 || pageIdx >= numPages {
				log.WarnNoSentry(
					ctx,
					"Page number out of range, skipping",
					zap.Int("page", pageNum),
					zap.Int("totalPages", numPages),
				)
				continue
			}

			result := processor.ProcessPage(ctx, doc, pageIdx, openaiService, processor.ProcessPageOptions{
				SystemPrompt:      opts.SystemPrompt,
				Model:             getModel(opts),
				FilePath:          opts.FilePath,
				ImageDensity:      opts.ImageDensity,
				ImageWidth:        opts.ImageWidth,
				ImageHeight:       opts.ImageHeight,
				JPEGQuality:       opts.JPEGQuality,
				Format:            opts.ImageFormat,
				MaintainFormat:    opts.MaintainFormat,
				ProcessBlankPages: opts.ProcessBlankPages,
				PriorPage:         priorPage,
			})

			if result.Error != nil {
				log.Error(
					ctx, "Failed to process page",
					zap.Int("page", pageNum),
					zap.Error(result.Error),
				)
				// Continue processing other pages
				continue
			}

			if result.Content != "" {
				pages = append(pages, Page{
					Content:       result.Content,
					ContentLength: len(result.Content),
					Page:          pageNum,
				})

				// Update prior page for maintain_format
				if opts.MaintainFormat {
					priorPage = result.Content
				}
			}
		}
	}

	if len(pages) == 0 {
		return nil, numPages, errors.New("no pages were successfully processed")
	}

	return pages, numPages, nil
}

// PDFToMDViaHTML is a fallback method for PDF to MD processing and does not rely on OpenAI's vision API
// Converts PDF -> HTML -> Sanitized HTML -> Markdown
func PDFToMDViaHTML(
	fileData []byte,
	options ...PDFToMDViaHTMLOptions,
) (string, error) {

	var markdown string

	doc, err := fitz.NewFromMemory(fileData)
	if err != nil {
		return markdown, fmt.Errorf("fitz.NewFromMemory error: %w", err)
	}
	defer doc.Close()

	// Initialize cleaner and converter before loop
	p := bluemonday.UGCPolicy()
	p.AllowElements("body")

	converter := md.NewConverter("", true, nil)
	converter.Use(plugins.GitHubFlavored())

	numPages := doc.NumPage()
	if len(options) > 0 && options[0].NumPages > 0 {
		numPages = options[0].NumPages
	}

	var builder strings.Builder
	// Convert each page of the PDF to HTML, sanitize it, and convert to Markdown
	for i := 0; i < numPages; i++ {
		html, err := doc.HTML(i, true)
		if err != nil {
			return markdown, fmt.Errorf("failed to get HTML from PDF: %w", err)
		}

		sanitizedHTML := p.Sanitize(html)

		markdownText, err := converter.ConvertString(sanitizedHTML)
		if err != nil {
			return markdown, fmt.Errorf("failed to convert HTML to Markdown: %w", err)
		}

		builder.WriteString(markdownText)
		builder.WriteString("\n\n")
	}

	return builder.String(), nil
}
