package processor

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"

	openaiShared "github.com/openai/openai-go/v2/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// For blank page detection, we use 99.8% whiteness and 250/255 brightness for pixels as our thresholds.
const (
	DefaultWhitenessThreshold = 99.8 // 99.8% of pixels must be white
	// At 99.8% white pixels we support as low as ~15-20 words being on a page (which is fairly empty-but still
	// worth processing).
	DefaultBrightnessThreshold = 250 // Brightness of 250+ out of 255 is considered "white"
)

// EncodeImageToBase64 encodes an image to base64 string.
func EncodeImageToBase64(img image.Image, format constants.ImageFormat, quality int) (string, error) {
	var buf bytes.Buffer

	switch format {
	case constants.JPEGImageFormat:
		if err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality}); err != nil {
			return "", fmt.Errorf("failed to encode image as JPEG: %w", err)
		}
	case constants.PNGImageFormat:
		if err := png.Encode(&buf, img); err != nil {
			return "", fmt.Errorf("failed to encode image as PNG: %w", err)
		}
	default:
		return "", fmt.Errorf("unsupported image format: %s", format)
	}

	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

// TODO: Implement ResizeImage()
// ResizeImage is a placeholder for image resizing.
// Currently returns the original image as gofitz handles DPI-based rendering.
// If needed, implement using golang.org/x/image/draw or similar library.
func ResizeImage(img image.Image, _, _ uint) image.Image {
	// Keep as no-op to avoid additional dependencies; DPI controls size upstream.
	return img
}

// IsBlankPage analyzes an image to determine if it's a blank/white page.
//
// Parameters:
//   - img: The image to analyze
//   - whitenessThreshold: Minimum percentage (0-100) of near-white pixels to consider page blank
//   - brightnessThreshold: Minimum brightness (0-255) for a pixel to be considered "white"
//
// Returns true if the page appears to be blank (mostly white/empty).
func IsBlankPage(ctx context.Context, img image.Image, whitenessThreshold float64, brightnessThreshold uint8) bool {

	bounds := img.Bounds()
	width := bounds.Max.X - bounds.Min.X
	height := bounds.Max.Y - bounds.Min.Y

	if width == 0 || height == 0 {
		return true
	}

	// Scan by sampling every 4th pixel in both dimensions (avg scan time is ~5ms @ 4 pixel increment)
	sampleStep := 4
	totalSamples := 0
	whitePixels := 0

	for y := bounds.Min.Y; y < bounds.Max.Y; y += sampleStep {
		for x := bounds.Min.X; x < bounds.Max.X; x += sampleStep {
			totalSamples++

			// Get pixel color
			r, g, b, _ := img.At(x, y).RGBA()

			// Calculate brightness on 16-bit scale, then downscale to 8-bit without narrowing casts
			brightness := ((r + g + b) / 3) >> 8

			// Check if pixel is "white" (above brightness threshold)
			if brightness >= uint32(brightnessThreshold) {
				whitePixels++
			}
		}
	}

	if totalSamples == 0 {
		return true
	}

	// Calculate percentage of white pixels
	whitenessPercentage := (float64(whitePixels) / float64(totalSamples)) * 100

	log.Debug(
		ctx, "Page Whiteness percentage",
		zap.Float64("whitenessPercentage", whitenessPercentage),
	)

	return whitenessPercentage >= whitenessThreshold
}

// IsBlankPageWithDefaults checks if a page is blank using default thresholds.
func IsBlankPageWithDefaults(ctx context.Context, img image.Image) bool {
	return IsBlankPage(ctx, img, DefaultWhitenessThreshold, DefaultBrightnessThreshold)
}

// ProcessImageOptions mirrors ProcessPageOptions but for raw images
type ProcessImageOptions struct {
	SystemPrompt      string
	Model             openaiShared.ChatModel
	ImageWidth        uint
	ImageHeight       uint
	JPEGQuality       int
	Format            constants.ImageFormat
	MaintainFormat    bool
	ProcessBlankPages bool
	PriorPage         string
}

// ProcessImage converts a single image to markdown using OpenAI's vision API.
func ProcessImage(
	ctx context.Context,
	img image.Image,
	openaiService openai.Service,
	opts ProcessImageOptions,
) (string, error) {
	bounds := img.Bounds()
	w := bounds.Dx()
	h := bounds.Dy()

	logWithMemory(
		ctx,
		"Processing image",
		zap.Int("width", w),
		zap.Int("height", h),
	)

	if IsBlankPageWithDefaults(ctx, img) && !opts.ProcessBlankPages {
		log.Info(ctx, "Skipping blank image page")
		return "", nil
	}

	resized := ResizeImage(img, opts.ImageWidth, opts.ImageHeight)
	base64Image, err := EncodeImageToBase64(resized, opts.Format, opts.JPEGQuality)
	if err != nil {
		return "", fmt.Errorf("failed to encode image: %w", err)
	}

	imageFormat := constants.JPEGImageFormat
	if opts.Format == constants.PNGImageFormat {
		imageFormat = constants.PNGImageFormat
	}
	imageDataURL := fmt.Sprintf("data:image/%s;base64,%s", string(imageFormat), base64Image)

	responseOpts := openai.ResponseOptions{
		DeveloperPrompt: opts.SystemPrompt,
		Model:           &opts.Model,
		ImageContent:    imageDataURL,
	}
	if opts.MaintainFormat && opts.PriorPage != "" {
		responseOpts.DeveloperPrompt = fmt.Sprintf(
			"%s\n\nMarkdown must maintain consistent formatting with the following page:\n\n\"\"\"%s\"\"\"",
			opts.SystemPrompt,
			opts.PriorPage,
		)
	}

	response, err := openaiService.GetResponse(
		ctx,
		models.Email{},
		models.Attachment{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		responseOpts,
	)
	if err != nil {
		return "", fmt.Errorf("failed to get response for image: %w", err)
	}
	if response.Content == "" {
		return "", nil
	}
	return FormatMarkdown(response.Content), nil
}
