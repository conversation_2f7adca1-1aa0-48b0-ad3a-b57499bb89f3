package processor

import (
	"regexp"
)

var (
	// matchMarkdownBlocks matches markdown code blocks like ```markdown\n...\n```
	matchMarkdownBlocks = regexp.MustCompile("^```[a-z]*\n([\\s\\S]*?)\n```$")

	// matchCodeBlocks matches generic code blocks like ```\n...\n```
	matchCodeBlocks = regexp.MustCompile("^```\n([\\s\\S]*?)\n```$")
)

// FormatMarkdown formats markdown text by removing markdown and code block delimiters.
func FormatMarkdown(text string) string {
	// Remove markdown blocks (```markdown\n...\n```)
	formatted := matchMarkdownBlocks.ReplaceAllString(text, "$1")

	// Remove code blocks (```\n...\n```)
	formatted = matchCodeBlocks.ReplaceAllString(formatted, "$1")

	return formatted
}
