package processor

import (
	"context"
	"image"
	"math"
	"runtime"
	"sync"

	"github.com/gen2brain/go-fitz"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// computeTargetDPIFromSample calculates a safe target DPI given a sample render and desired caps.
// Guarantees a minimum of 1 and never exceeds maxDensity.
func computeTargetDPIFromSample(
	sampleW,
	sampleH,
	maxW,
	maxH,
	sampleDPI,
	maxDensity int,
) int {
	// Default to maxDensity if we can't compute a finite scale
	targetDPI := maxDensity

	scaleW := math.Inf(1)
	scaleH := math.Inf(1)
	if maxW > 0 && sampleW > 0 {
		scaleW = float64(maxW) / float64(sampleW)
	}
	if maxH > 0 && sampleH > 0 {
		scaleH = float64(maxH) / float64(sampleH)
	}
	scale := math.Min(scaleW, scaleH)
	if !math.IsInf(scale, 0) && scale > 0 {
		// Allow DPI to go below sample DPI if necessary, but never below 1.
		dpi := int(math.Floor(float64(sampleDPI) * scale))
		targetDPI = clampInt(dpi, 1, maxDensity)
	}
	return targetDPI
}

// logWithMemory logs a message with provided fields and current memory usage.
func logWithMemory(ctx context.Context, message string, fields ...zap.Field) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	allocMB := float64(m.Alloc) / (1024.0 * 1024.0)
	heapInuseMB := float64(m.HeapInuse) / (1024.0 * 1024.0)
	sysMB := float64(m.Sys) / (1024.0 * 1024.0)

	fields = append(fields,
		zap.Float64("allocMB", allocMB),
		zap.Float64("heapInuseMB", heapInuseMB),
		zap.Float64("sysMB", sysMB),
	)
	log.Info(ctx, message, fields...)
}

// preflightDetermineDPI renders a low-DPI sample, optionally detects blank pages, logs memory,
// and returns a target DPI that respects provided caps and max density.
// Returns (targetDPI, isBlank).
func preflightDetermineDPI(
	ctx context.Context,
	doc *fitz.Document,
	pageNum int,
	opts ProcessPageOptions,
) (int, bool) {

	// Establish caps; if unset, default to approx letter size at 300 DPI (2550x3300)
	maxW := safeUintToInt(opts.ImageWidth)
	maxH := safeUintToInt(opts.ImageHeight)
	if maxW == 0 && maxH == 0 {
		maxW = 2550
		maxH = 3300
	}

	const sampleDPI = 36

	sampleImg, err := imageDPIWithGuard(ctx, doc, pageNum, float64(sampleDPI))
	if err != nil {
		// Fallback to configured density without logging sample stats
		return opts.ImageDensity, false
	}

	sBounds := sampleImg.Bounds()
	sW := sBounds.Dx()
	sH := sBounds.Dy()

	// Early blank-page skip using the lightweight sample
	if IsBlankPageWithDefaults(ctx, sampleImg) && !opts.ProcessBlankPages {
		log.Info(ctx, "Skipping blank page (sample)", zap.Int("page", pageNum+1))
		return opts.ImageDensity, true
	}

	targetDPI := computeTargetDPIFromSample(sW, sH, maxW, maxH, sampleDPI, opts.ImageDensity)

	logWithMemory(
		ctx,
		"Preflight render (sample)",
		zap.Int("page", pageNum+1),
		zap.Int("sampleWidth", sW),
		zap.Int("sampleHeight", sH),
		zap.Int("sampleDpi", sampleDPI),
		zap.Int("targetDpi", targetDPI),
		zap.Int("maxWidth", maxW),
		zap.Int("maxHeight", maxH),
	)

	return targetDPI, false
}

// docMuKey is the context key for a per-document mutex to serialize MuPDF access.
// used for thread safety during concurrent page rendering operations | ex: pdf page -> image using ImageDPI()
type docMuKey struct{}

// withDocMu attaches a mutex to the context.
func withDocMu(ctx context.Context, mu *sync.Mutex) context.Context {
	return context.WithValue(ctx, docMuKey{}, mu)
}

// getDocMu retrieves the mutex from context if present.
func getDocMu(ctx context.Context) *sync.Mutex {
	if v := ctx.Value(docMuKey{}); v != nil {
		if mu, ok := v.(*sync.Mutex); ok {
			return mu
		}
	}
	return nil
}

// imageDPIWithGuard serializes calls to Document.ImageDPI using a context-scoped mutex.
func imageDPIWithGuard(ctx context.Context, doc *fitz.Document, pageNum int, dpi float64) (image.Image, error) {
	if mu := getDocMu(ctx); mu != nil {
		mu.Lock()
		defer mu.Unlock()
	}
	return doc.ImageDPI(pageNum, dpi)
}

// clampInt constrains value to the inclusive range [lowerBound, upperBound].
func clampInt(value int, lowerBound int, upperBound int) int {
	if value < lowerBound {
		return lowerBound
	}
	if value > upperBound {
		return upperBound
	}
	return value
}

// safeUintToInt converts a uint to int, clamping to MaxInt if it would overflow.
func safeUintToInt(u uint) int {
	maxInt := ^uint(0) >> 1
	if u > maxInt {
		return int(maxInt)
	}
	return int(u)
}
