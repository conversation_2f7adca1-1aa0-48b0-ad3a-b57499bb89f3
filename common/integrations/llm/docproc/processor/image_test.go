package processor

import (
	"context"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/gen2brain/go-fitz"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Run tests with: go test ./common/integrations/llm/docproc/processor/... -v
// This file contains basic unit tests for blank page detection (generated images) and images from processed PDFs.

func TestIsBlankPage(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name                string
		img                 image.Image
		whitenessThreshold  float64
		brightnessThreshold uint8
		expected            bool
	}{
		{
			name:                "completely white page",
			img:                 createSolidColorImage(100, 100, color.RGBA{R: 255, G: 255, B: 255, A: 255}),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            true,
		},
		{
			name:                "near white page (252 brightness)",
			img:                 createSolidColorImage(100, 100, color.RGBA{R: 252, G: 252, B: 252, A: 255}),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            true,
		},
		{
			name:                "slightly off-white page (245 brightness)",
			img:                 createSolidColorImage(200, 100, color.RGBA{R: 245, G: 245, B: 245, A: 255}),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            false,
		},
		{
			name:                "black page",
			img:                 createSolidColorImage(100, 100, color.RGBA{R: 0, G: 0, B: 0, A: 255}),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            false,
		},
		{
			name:                "gray page",
			img:                 createSolidColorImage(100, 200, color.RGBA{R: 128, G: 128, B: 128, A: 255}),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            false,
		},
		{
			name:                "page with some content (95% white) below blank page threshold",
			img:                 createPageWithContent(100, 100, 0.95),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            false,
		},
		{
			name:                "page with minimal content (99% white) above blank page threshold",
			img:                 createPageWithContent(100, 200, 0.99),
			whitenessThreshold:  DefaultWhitenessThreshold,
			brightnessThreshold: DefaultBrightnessThreshold,
			expected:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsBlankPage(ctx, tt.img, tt.whitenessThreshold, tt.brightnessThreshold)
			if result != tt.expected {
				t.Errorf("IsBlankPage() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// TestIsBlankPageWithDefaults verifies the default threshold behavior
func TestIsBlankPageWithDefaults(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name     string
		img      image.Image
		expected bool
	}{
		{
			name:     "completely white page",
			img:      createSolidColorImage(100, 100, color.RGBA{R: 255, G: 255, B: 255, A: 255}),
			expected: true,
		},
		{
			name:     "page with text (should not be blank)",
			img:      createPageWithContent(100, 100, 0.85),
			expected: false,
		},
		{
			name:     "near white page",
			img:      createSolidColorImage(100, 100, color.RGBA{R: 250, G: 250, B: 250, A: 255}),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsBlankPageWithDefaults(ctx, tt.img)
			if result != tt.expected {
				t.Errorf("IsBlankPageWithDefaults() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// TestIsBlankPageEmptyImage verifies behavior with empty/zero-size images
func TestIsBlankPageEmptyImage(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name     string
		img      image.Image
		expected bool
	}{
		{
			name:     "zero width image",
			img:      image.NewRGBA(image.Rect(0, 0, 0, 100)),
			expected: true,
		},
		{
			name:     "zero height image",
			img:      image.NewRGBA(image.Rect(0, 0, 100, 0)),
			expected: true,
		},
		{
			name:     "zero size image",
			img:      image.NewRGBA(image.Rect(0, 0, 0, 0)),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsBlankPageWithDefaults(ctx, tt.img)
			if result != tt.expected {
				t.Errorf("IsBlankPageWithDefaults() = %v, expected %v for %s", result, tt.expected, tt.name)
			}
		})
	}
}

// TestBlankPageDetectionWithPDFs tests that blank pages are skipped but pages with small amounts of content are not.
// This uses sample PDFs with known blank/nearly blank second pages to verify detection logic.
// Run with: go test -v ./common/integrations/llm/docproc/processor/... -run TestBlankPageDetectionWithPDFs
// Set OUTPUT_PAGES_AS_PNG=true to save processed page images as PNG files for debugging.
func TestBlankPageDetectionWithPDFs(t *testing.T) {
	if os.Getenv("TEST_DOC_PROC") != "true" {
		t.Skip("TEST_DOC_PROC not set to true, skipping test")
	}

	ctx := context.Background()

	sharedFilePath := "../sample_files/shared/"

	testCases := []struct {
		filePath        string
		expectedResults map[int]bool // page number -> expected is_blank result
		description     string
	}{
		{
			filePath: sharedFilePath + "2ndPageBlank.pdf",
			expectedResults: map[int]bool{
				1: false, // Page 1 has content, should not be detected as blank
				2: true,  // Page 2 should be blank
			},
			description: "2ndPageBlank.pdf - page 1 has content, page 2 is blank",
		},
		{
			filePath: sharedFilePath + "2ndPageAlmostBlank.pdf",
			expectedResults: map[int]bool{
				1: false, // Page 1 has content, should not be detected as blank
				2: true,  // Page 2 should be detected as blank (almost completely blank—1 word on the page)
			},
			description: "2ndPageAlmostBlank.pdf - page 1 has content, page 2 is almost completely blank",
		},
		{
			filePath: sharedFilePath + "2ndPageSmallContent.pdf",
			expectedResults: map[int]bool{
				1: false, // Page 1 has content, should not be detected as blank
				2: false, // Page 2 should NOT be blank (page has small amount of content)
			},
			description: "2ndPageSmallContent.pdf - page 1 has content, page 2 has small amount of content",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.description, func(t *testing.T) {
			log.Info(ctx, "Analyzing PDF", zap.String("file", testCase.filePath))

			pdfData, err := os.ReadFile(testCase.filePath)
			if err != nil {
				t.Fatalf("failed to read %s: %v", testCase.filePath, err)
			}

			doc, err := fitz.NewFromMemory(pdfData)
			if err != nil {
				t.Fatalf("failed to open PDF %s: %v", testCase.filePath, err)
			}
			defer doc.Close()

			// Check if PNG output is enabled
			outputPagesAsPNG := os.Getenv("OUTPUT_PAGES_AS_PNG") == "true"

			numPages := doc.NumPage()
			logFields := []zap.Field{
				zap.String("file", testCase.filePath),
				zap.Int("totalPages", numPages),
			}
			if outputPagesAsPNG {
				// Extract filename without extension for directory creation
				baseFileName := filepath.Base(testCase.filePath)
				fileNameWithoutExt := strings.TrimSuffix(baseFileName, filepath.Ext(baseFileName))
				outputDir := filepath.Join("..", "sample_files", "personal", fileNameWithoutExt)
				logFields = append(logFields, zap.String("outputDir", outputDir))
			}
			log.Info(ctx, "PDF info", logFields...)

			// Use preflight to compute a safe target DPI and optionally skip blank pages
			opts := ProcessPageOptions{
				ImageDensity:      300,
				ImageWidth:        0,     // preserve aspect; cap height only
				ImageHeight:       1650,  // default cap height
				ProcessBlankPages: false, // allow preflight to mark true blanks
				MaintainFormat:    false,
			}

			for pageIdx := 0; pageIdx < numPages; pageIdx++ {
				pageNum := pageIdx + 1

				// Preflight to determine DPI and early blank detection
				targetDPI, isBlankPre := preflightDetermineDPI(ctx, doc, pageIdx, opts)
				var (
					img        image.Image
					isBlank    bool
					width      int
					height     int
					outputPath string
				)

				if isBlankPre {
					// Treat as blank without rendering full-size
					isBlank = true
					logFields = []zap.Field{
						zap.String("file", testCase.filePath),
						zap.Int("page", pageNum),
						zap.Bool("isBlank", isBlank),
						zap.Int("dpi", targetDPI),
					}
					log.Info(ctx, "Page analysis (preflight blank)", logFields...)
				} else {
					// Render using computed DPI
					img, err = doc.ImageDPI(pageIdx, float64(targetDPI))
					if err != nil {
						t.Errorf("Failed to extract image from %s page %d: %v", testCase.filePath, pageNum, err)
						continue
					}
					width = img.Bounds().Dx()
					height = img.Bounds().Dy()
					isBlank = IsBlankPageWithDefaults(ctx, img)
					if outputPagesAsPNG {
						outputPath = savePNGIfEnabled(ctx, img, testCase.filePath, pageNum)
					}

					logFields = []zap.Field{
						zap.String("file", testCase.filePath),
						zap.Int("page", pageNum),
						zap.Bool("isBlank", isBlank),
						zap.Int("width", width),
						zap.Int("height", height),
						zap.Int("dpi", targetDPI),
					}
					if outputPath != "" {
						logFields = append(logFields, zap.String("savedTo", outputPath))
					}
					log.Info(ctx, "Page analysis", logFields...)
				}

				// Check if we have expected results for this page
				if expectedBlank, hasExpectation := testCase.expectedResults[pageNum]; hasExpectation {
					if isBlank != expectedBlank {
						t.Errorf("Page %d of %s: expected isBlank=%v, got isBlank=%v",
							pageNum, testCase.filePath, expectedBlank, isBlank)
					} else {
						log.Info(ctx, "Page detection correct",
							zap.String("file", testCase.filePath),
							zap.Int("page", pageNum),
							zap.Bool("expectedBlank", expectedBlank),
							zap.Bool("actualBlank", isBlank),
						)
					}
				}
			}
		})
	}
}

// TestComputeTargetDPIFromSample validates DPI calculation given sample dimensions and caps.
func TestComputeTargetDPIFromSample(t *testing.T) {
	// Example: sample render 4284x5712 @36 DPI,
	// cap height 1650 => scale=1650/5712 ~= 0.2888, targetDPI=floor(36*scale)=10
	sampleW := 4284
	sampleH := 5712
	sampleDPI := 36
	maxDensity := 300

	// Height-only cap
	dpi := computeTargetDPIFromSample(sampleW, sampleH, 0, 1650, sampleDPI, maxDensity)
	if dpi != 10 {
		t.Errorf("expected target DPI 10, got %d", dpi)
	}

	// Both width and height caps; min(scaleW, scaleH) should be used
	scaleW := float64(2550) / float64(sampleW)
	scaleH := float64(3300) / float64(sampleH)
	expected := int(math.Floor(float64(sampleDPI) * math.Min(scaleW, scaleH)))
	dpi2 := computeTargetDPIFromSample(sampleW, sampleH, 2550, 3300, sampleDPI, maxDensity)
	if dpi2 != expected {
		t.Errorf("expected target DPI %d, got %d", expected, dpi2)
	}
}

// TestPreflightDetermineDPI_AdjustsFinalSize ensures the preflight picks a DPI that respects caps.
// Run with: go test -v ./common/integrations/llm/docproc/processor/... -run TestPreflightDetermineDPI_AdjustsFinalSize
func TestPreflightDetermineDPI_AdjustsFinalSize(t *testing.T) {
	if os.Getenv("TEST_DOC_PROC") != "true" {
		t.Skip("TEST_DOC_PROC not set to true, skipping test")
	}

	ctx := context.Background()

	pdfPath := "../sample_files/personal/picture-non-scan.pdf"
	data, err := os.ReadFile(pdfPath)
	if err != nil {
		t.Fatalf("failed to read %s: %v", pdfPath, err)
	}

	doc, err := fitz.NewFromMemory(data)
	if err != nil {
		t.Fatalf("failed to open PDF %s: %v", pdfPath, err)
	}
	defer doc.Close()

	// Use a height cap equivalent to defaults; width cap 0 to preserve aspect
	opts := ProcessPageOptions{
		ImageDensity:      300,
		ImageWidth:        0,
		ImageHeight:       1650,
		ProcessBlankPages: true,
		MaintainFormat:    false,
		SystemPrompt:      "",
	}

	// Page 0 only for this check
	targetDPI, isBlank := preflightDetermineDPI(ctx, doc, 0, opts)
	if isBlank {
		t.Fatalf("unexpected blank detection for %s page 1", pdfPath)
	}
	if targetDPI < 1 || targetDPI > opts.ImageDensity {
		t.Fatalf("targetDPI out of bounds: %d", targetDPI)
	}

	// Final render should respect the cap height
	img, err := doc.ImageDPI(0, float64(targetDPI))
	if err != nil {
		t.Fatalf("final render failed: %v", err)
	}
	capHeight := safeUintToInt(opts.ImageHeight)
	if img.Bounds().Dy() > capHeight {
		t.Fatalf("final image height %d exceeds cap %d", img.Bounds().Dy(), capHeight)
	}

	// Optionally save PNG for debugging
	if os.Getenv("OUTPUT_PAGES_AS_PNG") == "true" {
		outputPath := savePNGIfEnabled(ctx, img, pdfPath, 1)
		if outputPath == "" {
			t.Fatal("expected PNG to be saved with OUTPUT_PAGES_AS_PNG=true")
		}
	}
}

// savePNGIfEnabled saves a page image as PNG if OUTPUT_PAGES_AS_PNG=true.
// Returns the output path if saved, empty string otherwise.
func savePNGIfEnabled(ctx context.Context, img image.Image, filePath string, pageNum int) string {
	if os.Getenv("OUTPUT_PAGES_AS_PNG") != "true" {
		return ""
	}

	// Extract filename without extension for directory creation
	baseFileName := filepath.Base(filePath)
	fileNameWithoutExt := strings.TrimSuffix(baseFileName, filepath.Ext(baseFileName))

	// Create output directory for this PDF (relative to processor directory)
	outputDir := filepath.Join("..", "sample_files", "personal", fileNameWithoutExt)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Error(
			ctx,
			"Failed to create output directory",
			zap.String("file", filePath),
			zap.Int("page", pageNum),
			zap.String("outputDir", outputDir),
			zap.Error(err),
		)

		return ""
	}

	// Create output file path
	outputFileName := fmt.Sprintf("%d.png", pageNum)
	outputPath := filepath.Join(outputDir, outputFileName)

	// Create and write PNG file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		log.Error(
			ctx,
			"Failed to create output file",
			zap.String("file", filePath),
			zap.Int("page", pageNum),
			zap.String("outputPath", outputPath),
			zap.Error(err),
		)

		return ""
	}
	defer outputFile.Close()

	if err := png.Encode(outputFile, img); err != nil {
		log.Error(
			ctx, "Failed to encode PNG",
			zap.String("file", filePath),
			zap.Int("page", pageNum),
			zap.String("outputPath", outputPath),
			zap.Error(err),
		)

		return ""
	}

	return outputPath
}

// createSolidColorImage creates a 100x100 image filled with a single color
func createSolidColorImage(height int, width int, c color.Color) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			img.Set(x, y, c)
		}
	}
	return img
}

// createPageWithContent creates an image with a specified percentage of white pixels
// The remaining pixels are black to simulate text/content
func createPageWithContent(width, height int, whitePercentage float64) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	totalPixels := width * height
	whitePixels := int(float64(totalPixels) * whitePercentage)

	pixelCount := 0
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if pixelCount < whitePixels {
				img.Set(x, y, color.RGBA{R: 255, G: 255, B: 255, A: 255})
			} else {
				img.Set(x, y, color.RGBA{R: 0, G: 0, B: 0, A: 255})
			}
			pixelCount++
		}
	}
	return img
}
