package processor

import (
	"context"
	"fmt"
	"sync"

	"github.com/gen2brain/go-fitz"
	openaiShared "github.com/openai/openai-go/v2/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// ProcessPageOptions contains options for processing a single page.
type ProcessPageOptions struct {
	SystemPrompt string
	Model        openaiShared.ChatModel

	// FilePath optionally specifies the path to the PDF file on disk.
	// If provided, the file will be read from disk instead of in memory.
	FilePath string

	// ImageDensity is the DPI for rendering
	ImageDensity int

	// ImageWidth and ImageHeight are max dimensions (0 = no limit)
	ImageWidth  uint
	ImageHeight uint

	// JPEGQuality is the quality for JPEG encoding
	JPEGQuality int

	// Format is the image format (png or jpeg)
	Format constants.ImageFormat

	// MaintainFormat indicates whether to include prior page for consistency
	MaintainFormat bool

	// ProcessBlankPages enables processing of blank/white pages (default: false)
	// Not named "SkipBlankPages" because prevents boolean direct assignment in options parsing
	ProcessBlankPages bool

	// PriorPage is the previous page's markdown content
	PriorPage string
}

// PageResult contains the result of processing a single page.
type PageResult struct {
	Content string // Markdown content
	Error   error
}

// ProcessPage converts a single PDF page to markdown using OpenAI's vision API.
// Used for sequential processing (when MaintainFormat is true)
func ProcessPage(
	ctx context.Context,
	doc *fitz.Document,
	pageNum int,
	openaiService openai.Service,
	opts ProcessPageOptions,
) PageResult {

	ctx, span := otel.StartSpan(ctx, "docproc.processor.ProcessPage", nil)
	defer span.End(nil)

	result := PageResult{}

	// Render and encode the page to a data URL using the guarded helper
	imageDataURL, isBlank, err := renderPageDataURL(ctx, doc, pageNum, opts)
	if err != nil {
		result.Error = err
		return result
	}
	if isBlank {
		return result
	}

	responseOpts := openai.ResponseOptions{
		DeveloperPrompt: opts.SystemPrompt,
		Model:           &opts.Model,
		ImageContent:    imageDataURL,
	}

	// Add prior page context if maintain_format is enabled
	if opts.MaintainFormat && opts.PriorPage != "" {
		responseOpts.DeveloperPrompt = fmt.Sprintf(
			"%s\n\nMarkdown must maintain consistent formatting with the following page:\n\n\"\"\"%s\"\"\"",
			opts.SystemPrompt,
			opts.PriorPage,
		)
	}

	response, err := openaiService.GetResponse(
		ctx,
		models.Email{},      // TODO: pass email down so we can pass it here
		models.Attachment{}, // TODO: pass attachment info down so we can pass it here
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		responseOpts,
	)
	if err != nil {
		result.Error = fmt.Errorf("failed to get response for page %d: %w", pageNum+1, err)
		return result
	}

	if response.Content == "" {
		log.Warn(ctx, "Empty response from OpenAI", zap.Int("page", pageNum+1))
		return result
	}

	formattedContent := FormatMarkdown(response.Content)

	result.Content = formattedContent

	log.Info(
		ctx,
		"Page processed successfully",
		zap.Int("page", pageNum+1),
	)

	return result
}

// renderPageDataURL renders a page image as a base64 encoded string for LLM image input.
// uses thread safe imageDPIWithGuard(), applies blank detection and resizing.
// Returns (base64Image, isBlank, error).
func renderPageDataURL(
	ctx context.Context,
	doc *fitz.Document,
	pageNum int,
	opts ProcessPageOptions,
) (string, bool, error) {

	// Preflight calculation to determine target DPI and blank page detection
	targetDPI, isBlank := preflightDetermineDPI(ctx, doc, pageNum, opts)
	if isBlank {
		return "", true, nil
	}

	// Final render at target DPI
	img, err := imageDPIWithGuard(ctx, doc, pageNum, float64(targetDPI))
	if err != nil {
		return "", false, fmt.Errorf("failed to extract image for page %d: %w", pageNum+1, err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	logWithMemory(
		ctx,
		"Rendered PDF page image",
		zap.Int("page", pageNum+1),
		zap.Int("width", width),
		zap.Int("height", height),
		zap.Int("dpi", targetDPI),
	)

	// Skip page if blank
	if IsBlankPageWithDefaults(ctx, img) {
		if !opts.ProcessBlankPages {
			log.Info(ctx, "Skipping blank page", zap.Int("page", pageNum+1))
			return "", true, nil
		}
	}

	// Resize if needed and encode
	resizedImg := ResizeImage(img, opts.ImageWidth, opts.ImageHeight)

	base64Image, err := EncodeImageToBase64(resizedImg, opts.Format, opts.JPEGQuality)
	if err != nil {
		return "", false, fmt.Errorf("failed to encode image for page %d: %w", pageNum+1, err)
	}

	imageFormat := constants.JPEGImageFormat
	if opts.Format == constants.PNGImageFormat {
		imageFormat = constants.PNGImageFormat
	}
	imageDataURL := fmt.Sprintf("data:image/%s;base64,%s", string(imageFormat), base64Image)

	return imageDataURL, false, nil
}

// ProcessPagesConcurrently processes multiple PDF pages concurrently.
// Note: This should only be used when MaintainFormat is false, as concurrent processing
// doesn't maintain page-to-page formatting consistency.
//
// Two stage concurrent page processing pipeline:
// 1. Render: Pages render one-at-a-time per document because ImageDPI() is not thread-safe.
// 2. LLM: Base64 encoded images are sent to the LLM for processing concurrently, bounded by the concurrency config.
func ProcessPagesConcurrently(
	ctx context.Context,
	doc *fitz.Document,
	pageIndices []int, // 0-indexed page numbers
	openaiService openai.Service,
	opts ProcessPageOptions,
	concurrency int,
) []PageResult {

	ctx, span := otel.StartSpan(ctx, "docproc.processor.ProcessPagesInBatches", nil)
	defer span.End(nil)

	if len(pageIndices) == 0 {
		return []PageResult{}
	}
	if concurrency <= 0 {
		concurrency = 1
	}

	// Two-stage pipeline semaphores:
	// - renderSemaphore serializes MuPDF renders (size 1 per document)
	// - llmSemaphore bounds concurrent LLM calls (size = concurrency)
	renderSemaphore := make(chan struct{}, 1)
	llmSemaphore := make(chan struct{}, concurrency)
	results := make([]PageResult, len(pageIndices))

	var wg sync.WaitGroup

	// Attach a per-document mutex to the context to serialize MuPDF access
	// across concurrent goroutines. MuPDF is not fully thread-safe per document.
	ctxWithMu := withDocMu(ctx, &sync.Mutex{})

	// Process pages concurrently
	for i, pageIdx := range pageIndices {
		wg.Add(1)

		go func(idx int, pageNum int) {
			defer wg.Done()
			defer func() {
				// Recover from panics to prevent deadlocks
				if r := recover(); r != nil {
					log.Error(
						ctx,
						"Panic in page processing goroutine",
						zap.Int("page", pageNum+1),
						zap.Any("panic", r),
					)
					results[idx] = PageResult{
						Error: fmt.Errorf("panic processing page %d: %v", pageNum+1, r),
					}
				}
			}()

			// Track whether we've acquired the render semaphore to ensure proper release
			renderHeld := false

			// Check for context cancellation before acquiring render semaphore
			select {
			case <-ctxWithMu.Done():
				results[idx] = PageResult{
					Error: fmt.Errorf("context cancelled for page %d: %w", pageNum+1, ctxWithMu.Err()),
				}
				return
			case renderSemaphore <- struct{}{}:
				// Acquired render slot
				renderHeld = true
			}

			// Ensure render semaphore is released on any exit path after acquisition (panic/early return)
			defer func() {
				if renderHeld {
					<-renderSemaphore
					renderHeld = false
				}
			}()

			// Check for context cancellation again before processing
			select {
			case <-ctxWithMu.Done():
				results[idx] = PageResult{
					Error: fmt.Errorf("context cancelled for page %d: %w", pageNum+1, ctxWithMu.Err()),
				}
				// Release immediately if we hold the render semaphore
				if renderHeld {
					<-renderSemaphore
					renderHeld = false
				}
				return
			default:
				// Process page in two stages
				// We don't simply use ProcessPage() as we can want to run LLM calls concurrently

				// Phase 1: render one page at a time under renderSemaphore (MuPDF guarded by mutex internally)
				imageDataURL, isBlank, err := renderPageDataURL(ctxWithMu, doc, pageNum, opts)
				// Release render slot immediately after rendering
				if renderHeld {
					<-renderSemaphore
					renderHeld = false
				}
				if err != nil {
					results[idx] = PageResult{Error: err}
					return
				}
				if isBlank {
					// Return empty result when skipping blank pages
					results[idx] = PageResult{}
					return
				}

				// Phase 2: LLM call bounded by the concurrency configuration
				// Acquire llmSemaphore to prevent concurrent LLM calls from exceeding the concurrency config
				select {
				case <-ctxWithMu.Done():
					results[idx] = PageResult{
						Error: fmt.Errorf("context cancelled for page %d: %w", pageNum+1, ctxWithMu.Err()),
					}
					return
				case llmSemaphore <- struct{}{}:
				}
				defer func() { <-llmSemaphore }()
				responseOpts := openai.ResponseOptions{
					DeveloperPrompt: opts.SystemPrompt,
					Model:           &opts.Model,
					ImageContent:    imageDataURL,
				}

				response, err := openaiService.GetResponse(
					ctxWithMu,
					models.Email{},      // TODO: pass email down so we can pass it here
					models.Attachment{}, // TODO: pass attachment info down so we can pass it here
					braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
					responseOpts,
				)
				if err != nil {
					results[idx] = PageResult{
						Error: fmt.Errorf("failed to get response for page %d: %w", pageNum+1, err),
					}
					return
				}
				if response.Content == "" {
					log.Warn(ctxWithMu, "Empty response from OpenAI", zap.Int("page", pageNum+1))
					results[idx] = PageResult{}
					return
				}
				results[idx] = PageResult{Content: FormatMarkdown(response.Content)}
			}
		}(i, pageIdx)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	return results
}
