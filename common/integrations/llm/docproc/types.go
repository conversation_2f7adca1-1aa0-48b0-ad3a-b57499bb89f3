package docproc

import (
	openaiShared "github.com/openai/openai-go/v2/shared"

	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
)

// Page represents a single page of processed document content.
type Page struct {
	// Content is the markdown content of the page
	Content string `json:"content"`

	// ContentLength is the length of the content in bytes
	ContentLength int `json:"content_length"`

	// Page is the page number (1-indexed)
	Page int `json:"page"`
}

// PDFToMDOutput represents the complete output from PDF to markdown conversion.
type PDFToMDOutput struct {
	// CompletionTime is the total time taken in milliseconds
	CompletionTime float64 `json:"completion_time"`

	// FileName is the name of the processed file
	FileName string `json:"file_name"`

	// Markdown contains the complete markdown output
	Markdown string `json:"markdown"`

	// Pages contains the processed markdown content for each page
	Pages []Page `json:"pages"`
}

// PDFToMDOptions provides configuration for PDF to Image to markdown conversion using gofitz (MuPDF) and LLMs.
type PDFToMDOptions struct {
	Model        *openaiShared.ChatModel
	SystemPrompt string // If empty, uses constants.DefaultSystemPrompt

	// ImageDensity sets the DPI for PDF to image conversion (default: 300)
	// Higher values produce better quality but larger images
	ImageDensity int

	// ImageHeight sets the max height for images in pixels (default: 1650)
	// Set to 0 to use original size. Maintains aspect ratio.
	ImageHeight uint

	// ImageWidth sets the max width for images in pixels (default: 0 = original)
	// Set to 0 to use original size. Maintains aspect ratio.
	ImageWidth uint

	// ImageFormat sets the image format for conversion
	ImageFormat constants.ImageFormat

	// JPEGQuality sets the quality for JPEG encoding (1-100, default: 85)
	// Only used when format is JPEG
	JPEGQuality int

	// Concurrency limits how many pages to process in parallel (default: 4) set in constants/conversion.go
	Concurrency int

	// MaxPagesToProcess caps how many pages to process from the document.
	// The first N pages are processed. When SelectPages is provided, the first N
	// entries from SelectPages are used. Set to 0 or negative to disable capping.
	MaxPagesToProcess int

	// MaintainFormat indicates whether to maintain formatting consistency across pages
	// When true, includes previous page content in the prompt
	MaintainFormat bool

	// ProcessBlankPages enables processing of blank/white pages (default: false)
	// Not named "SkipBlankPages" because prevents boolean direct assignment in options parsing
	ProcessBlankPages bool

	// SelectPages optionally specifies which pages to process (1-indexed)
	// If nil, all pages are processed
	SelectPages []int

	FileName string

	// FilePath optionally specifies the path to the PDF file on disk.
	// If provided, the file will be read from disk instead of using the fileData parameter.
	FilePath string
}

// PDFToMDViaHTMLOptions provides configuration for PDF to markdown conversion via HTML.
type PDFToMDViaHTMLOptions struct {
	NumPages int
}
