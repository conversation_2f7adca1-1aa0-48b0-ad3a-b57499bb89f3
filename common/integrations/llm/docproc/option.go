package docproc

import (
	openaiShared "github.com/openai/openai-go/v2/shared"

	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
)

type (
	// Options provides configuration for PDF to markdown conversion.
	Options struct {
		Model             *openaiShared.ChatModel
		SystemPrompt      string
		ImageDensity      int
		ImageHeight       uint
		ImageWidth        uint
		ImageFormat       constants.ImageFormat
		JPEGQuality       int
		Concurrency       int
		MaxPagesToProcess int
		MaintainFormat    bool
		ProcessBlankPages bool
		SelectPages       []int
		FileName          string
		FilePath          string
	}

	// Option is a function that configures Options.
	Option func(o *Options)
)

// WithModel sets the OpenAI chat model to use for conversion.
func WithModel(model *openaiShared.ChatModel) Option {
	return func(o *Options) {
		o.Model = model
	}
}

// WithSystemPrompt sets the system prompt for the LLM.
func WithSystemPrompt(prompt string) Option {
	return func(o *Options) {
		o.SystemPrompt = prompt
	}
}

// WithImageDensity sets the DPI for PDF to image conversion.
// Higher values produce better quality but larger images (default: 300).
func WithImageDensity(density int) Option {
	return func(o *Options) {
		o.ImageDensity = density
	}
}

// WithImageHeight sets the max height for images in pixels (default: 1650).
// Set to 0 to use original size. Maintains aspect ratio.
func WithImageHeight(height uint) Option {
	return func(o *Options) {
		o.ImageHeight = height
	}
}

// WithImageWidth sets the max width for images in pixels (default: 0 = original).
// Set to 0 to use original size. Maintains aspect ratio.
func WithImageWidth(width uint) Option {
	return func(o *Options) {
		o.ImageWidth = width
	}
}

// WithImageFormat sets the image format for conversion.
func WithImageFormat(format constants.ImageFormat) Option {
	return func(o *Options) {
		o.ImageFormat = format
	}
}

// WithJPEGQuality sets the quality for JPEG encoding (1-100, default: 85).
// Only used when format is JPEG.
func WithJPEGQuality(quality int) Option {
	return func(o *Options) {
		o.JPEGQuality = quality
	}
}

// WithConcurrency limits how many pages to process in parallel (default: 4).
func WithConcurrency(concurrency int) Option {
	return func(o *Options) {
		o.Concurrency = concurrency
	}
}

// WithMaxPagesToProcess caps how many pages to process from the document.
// If the document has more pages than this value, only the first N pages are processed.
// When SelectPages is provided, only the first N entries from SelectPages are used.
// Set to 0 or negative to disable capping.
func WithMaxPagesToProcess(maxPagesToProcess int) Option {
	return func(o *Options) {
		o.MaxPagesToProcess = maxPagesToProcess
	}
}

// WithMaintainFormat indicates whether to maintain formatting consistency across pages.
// When true, includes previous page content in the prompt.
func WithMaintainFormat(maintain bool) Option {
	return func(o *Options) {
		o.MaintainFormat = maintain
	}
}

// WithProcessBlankPages enables processing of blank/white pages (default: false).
func WithProcessBlankPages(process bool) Option {
	return func(o *Options) {
		o.ProcessBlankPages = process
	}
}

// WithSelectPages optionally specifies which pages to process (1-indexed).
// If not set, all pages are processed.
func WithSelectPages(pages []int) Option {
	return func(o *Options) {
		o.SelectPages = pages
	}
}

// WithFileName sets the filename for logging purposes.
func WithFileName(fileName string) Option {
	return func(o *Options) {
		o.FileName = fileName
	}
}

// WithFilePath optionally specifies the path to the PDF file on disk.
// If provided, the file will be read from disk instead of using the fileData parameter.
func WithFilePath(filePath string) Option {
	return func(o *Options) {
		o.FilePath = filePath
	}
}

// getModel returns the model to use from options, or the default model if not set.
func getModel(opts *Options) openaiShared.ChatModel {
	if opts.Model != nil {
		return *opts.Model
	}
	return constants.DefaultModel
}
