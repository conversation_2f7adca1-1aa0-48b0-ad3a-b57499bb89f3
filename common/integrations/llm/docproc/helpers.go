package docproc

import (
	"context"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// LogWithFileName creates a new context with the filename added as a log field for debugging.
// This is useful for tracking which file is being processed in document processing operations.
//
// Example usage:
//
//	ctx := LogWithFileName(ctx, "document.pdf")
//	log.Info(ctx, "Processing document") // Will include filename="document.pdf" in logs
func LogWithFileName(ctx context.Context, filename string) context.Context {
	return log.With(ctx, zap.String("filename", filename))
}
