package docproc

import (
	"context"
	"os"
	"testing"

	openaiShared "github.com/openai/openai-go/v2/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/docproc/constants"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
)

// Instructions to run this test live with OpenAI response:
// 1. Make sure you have a valid OPENAI_API_KEY in your environment.
//  a. Set OPENAI_API_KEY in .env file at root of `drumkit` or run `export OPENAI_API_KEY=your_api_key` in terminal.
// 2. Set TEST_DOC_PROC=true in your environment by running `export TEST_DOC_PROC=true` in terminal.
// 3. Run the test: `go test -v ./common/integrations/llm/docproc -run TestPDFToMD`
//  a. You can also override the path to the sample PDF file by setting TEST_DOC_PROC_FILE in your environment.
//  b. Set with `export TEST_DOC_PROC_FILE=sample_files/personal/[file_name].ext` in terminal.

// TestPDFToMD tests PDF to MD conversion which converts pdfs to images then use vision models to convert
// content to markdown.
func TestPDFToMD(t *testing.T) {
	if os.Getenv("TEST_DOC_PROC") != "true" {
		t.Skip("TEST_DOC_PROC not set to true, skipping test")
	}

	ctx := context.Background()
	ctx = LogWithFileName(ctx, "sample.pdf")

	// Default path to sample PDF file
	filePath := "sample_files/shared/sample.pdf"
	// Override path to sample PDF file if TEST_DOC_PROC_FILE is set
	// Set with `export TEST_DOC_PROC_FILE=sample_files/personal/[file_name].ext` in terminal
	customFilePath := os.Getenv("TEST_DOC_PROC_FILE")
	if customFilePath != "" {
		filePath = customFilePath
		log.Info(ctx, "processing file:", zap.String("filePath", filePath))
	}

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		t.Fatalf("Failed to create OpenAI service: %v", err)
	}

	pdfData, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read sample PDF: %v", err)
	}

	log.Info(ctx, "Running PDF To MD with GPT4oMini")
	gpt4ominiModel := openaiShared.ChatModelGPT4oMini
	markdown, err := PDFToMDWithOutput(ctx, openaiService, pdfData,
		WithModel(&gpt4ominiModel),
		WithFileName(filePath),
	)
	if err != nil {
		t.Fatalf("Failed to convert PDF to MD: %v", err)
	}

	log.Info(ctx, "Running PDF To MD with GPT5Nano")
	gpt5nanoModel := openaiShared.ChatModelGPT5Nano
	markdown2, err2 := PDFToMDWithOutput(ctx, openaiService, pdfData,
		WithModel(&gpt5nanoModel),
		WithImageFormat(constants.PNGImageFormat),
		WithFileName(filePath),
	)
	if err2 != nil {
		t.Fatalf("Failed to convert PDF to MD in second test: %v", err2)
	}

	// TODO: Do some string comparisons against expected text contained within markdown vs original doc/file
	//       disable the test if there is a custom file path set (as we will have static tests against our
	// 		 sample/default pdfs)

	log.Info(ctx, "=--------------GPT4oMini Results------------------=")
	log.Info(ctx, markdown.Markdown)

	log.Info(ctx, "=--------------GPT5Nano  Results------------------=")
	log.Info(ctx, markdown2.Markdown)
}

// TestPDFToMDConcurrent tests concurrent batch processing of PDF pages.
func TestPDFToMDConcurrent(t *testing.T) {
	if os.Getenv("TEST_DOC_PROC") != "true" {
		t.Skip("TEST_DOC_PROC not set to true, skipping test")
	}

	ctx := context.Background()

	// Default path to sample PDF file
	filePath := "sample_files/shared/sample.pdf"
	customFilePath := os.Getenv("TEST_DOC_PROC_FILE")
	if customFilePath != "" {
		filePath = customFilePath
		log.Info(ctx, "processing file:", zap.String("filePath", filePath))
	}

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		t.Fatalf("Failed to create OpenAI service: %v", err)
	}

	pdfData, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read sample PDF: %v", err)
	}

	gpt4ominiModel := openaiShared.ChatModelGPT4oMini

	log.Info(ctx, "Running PDF To MD with concurrent processing (concurrency=10)")
	markdownConcurrent, err := PDFToMDWithOutput(ctx, openaiService, pdfData,
		WithModel(&gpt4ominiModel),
		WithFileName(filePath),
		WithConcurrency(10), // Process 10 pages concurrently
	)
	if err != nil {
		t.Fatalf("Failed to convert PDF to MD with concurrent processing: %v", err)
	}

	log.Info(ctx, "Running PDF To MD with sequential processing (maintain_format=true)")
	markdownSequential, err := PDFToMDWithOutput(ctx, openaiService, pdfData,
		WithModel(&gpt4ominiModel),
		WithFileName(filePath),
		WithMaintainFormat(true), // This forces sequential processing
		WithConcurrency(10),      // This will be ignored due to MaintainFormat=true
	)
	if err != nil {
		t.Fatalf("Failed to convert PDF to MD with sequential processing: %v", err)
	}

	// Verify both methods produced results
	if len(markdownConcurrent.Markdown) == 0 {
		t.Error("Concurrent processing produced empty markdown")
	}
	if len(markdownSequential.Markdown) == 0 {
		t.Error("Sequential processing produced empty markdown")
	}

	// Verify same number of pages were processed
	if len(markdownConcurrent.Pages) != len(markdownSequential.Pages) {
		t.Errorf("Page count mismatch: concurrent=%d, sequential=%d",
			len(markdownConcurrent.Pages), len(markdownSequential.Pages))
	}

	log.Info(ctx, "=-------------Concurrent vs Sequential PDF to MD time comparison-----------------=")
	log.Info(
		ctx, "converted pages",
		zap.Int("concurrent", len(markdownConcurrent.Pages)),
		zap.Int("sequential", len(markdownSequential.Pages)),
	)
	log.Info(
		ctx,
		"Concurrent vs Sequential pdf to md conversion time",
		zap.Float64("concurrentCompletionTimeMs", markdownConcurrent.CompletionTime),
		zap.Float64("sequentialCompletionTimeMs", markdownSequential.CompletionTime),
	)
}

// TestBlankPageDetectionWithLLM tests the blank page detection with LLM processing
// Run with: go test -v ./common/integrations/llm/docproc -run TestBlankPageDetectionWithLLM
func TestBlankPageDetectionWithLLM(t *testing.T) {
	if os.Getenv("TEST_DOC_PROC") != "true" {
		t.Skip("TEST_DOC_PROC not set to true, skipping test")
	}

	ctx := context.Background()
	openaiService, err := openai.NewService(ctx)
	if err != nil {
		t.Fatalf("Failed to create OpenAI service: %v", err)
	}

	gpt4ominiModel := openaiShared.ChatModelGPT4oMini

	// Test 1: PDF with blank second page - should skip the blank page and only process 1 page
	t.Run("BlankSecondPage", func(t *testing.T) {
		filePath := "sample_files/shared/2ndPageBlank.pdf"
		pdfData, err := os.ReadFile(filePath)
		if err != nil {
			t.Fatalf("Failed to read %s: %v", filePath, err)
		}

		log.Info(ctx, "Processing PDF with blank second page", zap.String("file", filePath))
		result, err := PDFToMDWithOutput(ctx, openaiService, pdfData,
			WithModel(&gpt4ominiModel),
			WithFileName(filePath),
		)
		if err != nil {
			t.Fatalf("Failed to convert PDF to MD: %v", err)
		}

		// Should only process 1 page (first page), skipping the blank second page
		if len(result.Pages) != 1 {
			t.Errorf("Expected 1 page processed (blank page skipped), got %d pages", len(result.Pages))
		}

		log.Info(
			ctx,
			"Blank page test results",
			zap.String("file", filePath),
			zap.Int("pagesProcessed", len(result.Pages)),
		)
	})

	// Test 2: PDF with second page containing small amount of content - should NOT skip the second page
	t.Run("SmallContentSecondPage", func(t *testing.T) {
		filePath := "sample_files/shared/2ndPageSmallContent.pdf"

		pdfData, err := os.ReadFile(filePath)
		if err != nil {
			t.Fatalf("Failed to read %s: %v", filePath, err)
		}

		log.Info(
			ctx,
			"Processing PDF with second page containing small amount of content",
			zap.String("file", filePath),
		)

		result, err := PDFToMDWithOutput(ctx, openaiService, pdfData,
			WithModel(&gpt4ominiModel),
			WithFileName(filePath),
		)
		if err != nil {
			t.Fatalf("Failed to convert PDF to MD: %v", err)
		}

		// Should process both pages (2nd page with small amount of content should NOT be skipped)
		if len(result.Pages) != 2 {
			t.Errorf(
				"Expected 2 pages processed (2nd page with small amount of content not skipped), got %d pages",
				len(result.Pages),
			)
		}

		log.Info(
			ctx,
			"Almost-blank page test results",
			zap.String("file", filePath),
			zap.Int("pagesProcessed", len(result.Pages)),
		)
	})
}
