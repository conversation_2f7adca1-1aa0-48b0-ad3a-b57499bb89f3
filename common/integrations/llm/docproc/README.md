# Document Processing with `docproc`

A Go package for processing documents, currently just PDFs, using AI vision models. This package is structured
similarly to [pyzerox](https://github.com/getomni-ai/zerox) and follows some of its design patterns.

## Features

- **PDF to Markdown Conversion**: Convert PDF documents to markdown using OpenAI's vision API
- **Configurable Image Processing**: Control DPI, image dimensions, and quality
- **Selective Page Processing**: Process specific pages or page ranges
- **Format Consistency**: Optional `maintain_format` mode for consistent styling across pages
- **Enhanced Logging**: Built-in filename logging and debugging utilities
- **Timing & Telemetry**: Completion time tracking and OpenTelemetry tracing support
- **Error Resilient**: Continues processing even if individual pages fail
- **Concurrent Processing**: Configurable parallel page processing for better performance

## Usage

### Basic Usage

```go
import (
    "context"
    "fmt"
    "os"

    "github.com/drumkitai/drumkit/common/integrations/llm/docproc"
    "github.com/drumkitai/drumkit/common/integrations/llm/openai"
)

func main() {
    ctx := context.Background()

    // Initialize OpenAI service (uses OPENAI_API_KEY)
    openaiService, err := openai.NewService(ctx)
    if err != nil {
        panic(err)
    }

    // Read PDF file
    pdfData, err := os.ReadFile("document.pdf")
    if err != nil {
        panic(err)
    }

    // Convert to markdown with filename for logging
    markdown, err := docproc.PDFToMD(ctx, openaiService, pdfData, docproc.PDFToMDOptions{
        FileName: "document.pdf", // Enables filename logging for debugging
    })
    if err != nil {
        panic(err)
    }

    fmt.Println(markdown)
}
```

### Get Detailed Output

```go
// Get detailed information including completion time and per-page content
output, err := docproc.PDFToMDWithOutput(ctx, openaiService, pdfData, docproc.PDFToMDOptions{
    FileName: "document.pdf", // Enables filename logging for debugging
})
if err != nil {
    panic(err)
}

fmt.Printf("Processed %d pages in %.0f ms\n", len(output.Pages), output.CompletionTime)
fmt.Printf("File: %s\n", output.FileName)

// Access individual pages
for _, page := range output.Pages {
    fmt.Printf("Page %d (len=%d)\n", page.Page, page.ContentLength)
}

// Get the complete markdown
fmt.Println(output.Markdown)
```

### Advanced Configuration

```go
import (
    openaiShared "github.com/openai/openai-go/v2/shared"
)

model := openaiShared.ChatModelGPT5Nano

markdown, err := docproc.PDFToMD(ctx, openaiService, pdfData, docproc.PDFToMDOptions{
    FileName:       "my-document.pdf", // For logging and output
    Model:          &model,
    SystemPrompt:   "Custom extraction instructions...",
    ImageDensity:   300,            // DPI (default: 300)
    ImageHeight:    1650,           // Max height in pixels (default: 1650)
    ImageWidth:     0,              // Max width (0 = no limit)
    JPEGQuality:    85,             // JPEG quality 1-100 (default: 85)
    MaintainFormat: true,           // Maintain formatting across pages
    SelectPages:    []int{1, 3, 5}, // Process specific pages only
    Concurrency:    1,              // Concurrent processing (default: 10)
})
```

### Process Specific Pages

```go
// Process only pages 1, 2, and 10
output, err := docproc.PDFToMDWithOutput(ctx, openaiService, pdfData, docproc.PDFToMDOptions{
    FileName:    "report.pdf",
    SelectPages: []int{1, 2, 10},
})
```

## How It Works

1. **PDF Rendering**: Each PDF page is converted to an image using go-fitz (MuPDF wrapper)
2. **Image Encoding**: Images are encoded to PNG/JPEG and then base64 for transmission
3. **Vision API**: Images are sent to OpenAI's vision model with conversion instructions
4. **Markdown Formatting**: Responses are processed to remove code block delimiters
5. **Concatenation**: All pages are concatenated with double newlines

## Concurrency and Thread Safety

MuPDF (via `go-fitz`) is not thread-safe for concurrent rendering using `ImageDPI()` on the same `*fitz.Document`. To prevent crashes when using `ImageDPI`, this package enforces a per-document critical section during rendering and a two-stage pipeline:

- Per-document mutex: All calls that enter MuPDF rendering (`ImageDPI`) are serialized using a context-scoped mutex.
- Two-stage pipeline:
  - Stage 1 (Render): Pages render one-at-a-time per document. We also preflight at low DPI to determine target DPI and optionally skip blanks.
  - Stage 2 (LLM): Encoded images are sent to the LLM concurrently, bounded by a configurable semaphore.

This design preserves stability while still achieving high parallelism on the LLM-bound portion of work.

### Semaphores

- Render semaphore: Capacity 1 per document; ensures only a single render runs concurrently for a document.
- LLM semaphore: Capacity equals the configured `Concurrency`; bounds the number of simultaneous outbound LLM requests.

### Ordering Guarantees

Per-page results are written back into a fixed index in a `results` slice based on page index. This guarantees deterministic output order regardless of which goroutine finishes first. Final markdown is produced by iterating the `results` slice by index.

### Alternatives

- Document cloning: For true parallel renders, open a separate `*fitz.Document` per worker (higher memory usage).
- Concurrency tuning: Increase or decrease the LLM semaphore independently of render serialization. If needed, set `Concurrency` to 1 for fully sequential processing.
- Future extensions: If additional MuPDF APIs are used, guard them similarly if they prove unsafe concurrently. `Image()` from past usage was safe concurrently.

## Debugging and Logging

The package includes built-in logging utilities for better debugging:

### Filename Logging

The `LogWithFileName` utility automatically adds filename context to all log messages:

```go
import "github.com/drumkitai/drumkit/common/integrations/llm/docproc/utils"

// Add filename to logging context
ctx = LogWithFileName(ctx, "document.pdf")

// All subsequent log messages will include filename="document.pdf"
log.Info(ctx, "Processing document")
log.Error(ctx, "Failed to process page", zap.Error(err))
```

### Automatic Filename Logging

When you provide a `FileName` in `PDFToMDOptions`, it's automatically added to the logging context:

```go
output, err := docproc.PDFToMDWithOutput(ctx, openaiService, pdfData, docproc.PDFToMDOptions{
    FileName: "contract.pdf", // Automatically logged with all messages
})
```

### OpenTelemetry Tracing

The package supports OpenTelemetry tracing with spans for:

- Overall PDF processing
- Individual page processing
- OpenAI API calls

Traces include filename and processing metadata for comprehensive observability.

## Output Structures

### Page

```go
type Page struct {
    Content       string `json:"content"`        // Markdown content
    ContentLength int    `json:"content_length"` // Length in bytes
    Page          int    `json:"page"`           // Page number (1-indexed)
}
```

### PDFToMDOutput

```go
type PDFToMDOutput struct {
    CompletionTime float64 `json:"completion_time"` // Time in milliseconds
    FileName       string  `json:"file_name"`       // Document name
    Markdown       string  `json:"markdown"`        // Complete markdown content
    Pages          []Page  `json:"pages"`           // Processed pages
}
```

## Environment Variables

Requires OpenAI API configuration (handled by `openai.NewService`):

- `OPENAI_API_KEY`: Your OpenAI API key

## Performance Considerations

- **Image Quality**: Higher DPI produces better results but larger images and higher costs
- **Page Selection**: Process only needed pages to reduce costs and time
- **Format Consistency**: `MaintainFormat` adds prior page context, increasing token usage
- **Concurrency**: Higher concurrency can improve speed but uses more resources and API quota
- **Filename Logging**: Minimal performance impact, provides significant debugging value

## Testing

To run a live test against OpenAI using the included sample files:

```bash
export OPENAI_API_KEY=your_api_key
export TEST_DOC_PROC=true
go test -v ./common/integrations/llm/docproc -run TestPDFToMD
```

Optionally, choose a specific file:

```bash
export TEST_DOC_PROC_FILE=sample_files/personal/LoadTender.pdf
```

## Error Handling

- Individual page failures are logged but don't stop processing
- Empty responses from OpenAI are logged as warnings
- All errors include page numbers and filenames for easy debugging
- Failed pages are excluded from final output but logged for investigation

## Dependencies

- **gofitz** (`github.com/gen2brain/go-fitz`): MuPDF wrapper for PDF rendering
- **OpenAI Go SDK** (`github.com/openai/openai-go/v2`): OpenAI API client
- Internal: `common/integrations/llm/openai`, `common/log`, `common/helpers/otel`

## Utils Package

The `utils` subpackage provides helpful utilities:

### LogWithFileName

```go
import "github.com/drumkitai/drumkit/common/integrations/llm/docproc/utils"

// Add filename to logging context for debugging
ctx = LogWithFileName(ctx, "document.pdf")
```

This utility is automatically used by the main docproc functions when a `FileName` is provided in options.
