package constants

// PDFToMDSystemPrompt is the system prompt used for PDF to markdown conversion.
// Prompt is inspired by Zerox's system prompt - https://github.com/getomni-ai/zerox
const PDFToMDSystemPrompt = `Convert the following document to markdown.
Return only the markdown with no explanation text. Do not include delimiters like ` + "```markdown or ```html." + `

RULES:
  - You must include all information on the page. Do not exclude headers, footers, or subtext.
  - Return tables in a simple HTML format. Do not include any additional styling or formatting.
  - Charts & infographics must be interpreted to a markdown format. Prefer table format when applicable.
  - Logos should be wrapped in brackets. Ex: <logo>Coca-Cola<logo>
  - Watermarks should be wrapped in brackets. Ex: <watermark>OFFICIAL COPY<watermark>
  - Page numbers should be wrapped in brackets. Ex: <page_number>14<page_number> or <page_number>9/22<page_number>
  - Prefer using ☐ and ☑ for check boxes.`
