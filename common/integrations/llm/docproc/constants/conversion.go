package constants

import openaiShared "github.com/openai/openai-go/v2/shared"

type ImageFormat string

const (
	PNGImageFormat  ImageFormat = "png"
	JPEGImageFormat ImageFormat = "jpeg"
)

// PDFConversionDefaults
// Contains default options for converting PDFs to images.
const (
	// TODO: Make default model ChatModelGPT5Nano if outputs are good
	DefaultModel = openaiShared.ChatModelGPT4oMini

	// 1 page processing takes 70-120MB per page. With a concurrency of 10 (we were processing 10 pages at once)
	// spiking memory more than lambda configured memory could handle. So we set to 4 to work with ~1-2gb memory limit.
	// DefaultConcurrency is the default concurrency for converting PDFs to images
	DefaultConcurrency = 4

	// DefaultDPI is the default resolution for PDF to image conversion
	DefaultDPI = 300

	// DefaultImageFormat is the default image format for conversion
	DefaultImageFormat = PNGImageFormat

	// DefaultJPEGQuality is the default quality for JPEG encoding when using JPEG format
	DefaultJPEGQuality = 85

	// DefaultImageHeight is the default max height for images (maintains aspect ratio)
	// Used to cap resolution/image dimensions and for calculating target DPI.
	// Other acceptable values 2200, 3300 (for higher resolutions). Would not recommend going lower than 1650
	// Set to 0 to use original size. pyzerox uses 1056.
	DefaultImageHeight = 1650

	// DefaultImageWidth is the default max width for images (maintains aspect ratio)
	// Set to 0 to use original size. pyzerox uses None (0).
	DefaultImageWidth = 0

	// DefaultMaxPagesToProcess caps how many pages we process from a document
	DefaultMaxPagesToProcess = 50
)
