package llm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func ExtractApptConfirmationSuggestions(
	ctx context.Context,
	email models.Email,
	openAI openai.Service,
	rds RDSInterface,
) (res []models.SuggestedLoadChange, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractApptConfirmationSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	type AppointmentInfo struct {
		RoutingNumbers []string `json:"routing_numbers" jsonschema_description:"The related routing numbers of the load."`
		Status         string   `json:"status" jsonschema_description:"Status of the appointment. If confirmed, set to 'confirmed'."`
		Date           string   `json:"date" jsonschema_description:"Date formatted in YYYY-MM-DD"`
		Time           string   `json:"time" jsonschema_description:"24h time string formatted as HH:MM"`
		Timezone       string   `json:"timezone" jsonschema_description:"The IANA timezone, such as America/New_York. If the user does not specify the timezone, leave the field empty."`
	}

	type PromptResponse struct {
		Appointments []AppointmentInfo `json:"appointments"`
	}

	var thisYear = fmt.Sprint(time.Now().Local().Year())
	var prompt = `You are Drumkit - a bleeding edge logistics scheduling assistant that interfaces via email.
	Make sure your final answers are definitive, complete and well formatted.

	You'll receive an e-mail body from the user. You should look for dates and routing numbers.
	Common names for routing numbers are PO and PRO. They do not contain spaces.
	You should identify when an appointment was confirmed by one party and extract the
	date and time of the confirmation.

	One e-mail can contain multiple appointments/routing numbers.

	Your responses should be a JSON with this type structure:

	{
	  "appointments": [
	    {
	      "routing_numbers": []string,
	      "status": string,
	      "date": string,
	      "timezone": string,
	    }
	  ]
	}

	"routing_numbers" should be the related routing numbers of the load.
	"date" should be a date formatted in Year - Month - Days. If year is not specified, infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `). 
		For example, if today's month is June and the email references July, then it's the same year.
		But if today's month is December and the email references January, then the year is next year.

	"time" should be a 24h time string.
	"timezone" should be the IANA timezone, such as America/New_York, America/Phoenix.
	If the user does not specify the timezone, leave the field empty.

	Example 1:

	Appointment is confirmed for 12/14 @ 1750 PRO 457HY

	Response:

	{
	  "appointments": [
	    {
	      "routing_numbers":["457HY"],
	      "status": "confirmed",
	      "date": "` + thisYear + `-12-14",
	      "time": "17:50"
		  "timezone": "",
	    }
	  ]
	}

	Example 2:

	457HY - confirmed for 12/14 @ 1750 ET

	12442-24235 - 12/18 @ 8:00 PT

	Response:

	{
	  "appointments": [
	    {
	      "routing_numbers":["457HY"],
	      "status": "confirmed",
	      "date": "` + thisYear + `-12-14",
	      "time": "17:50"
		  "timezone": "America/New_York",
	    },
		{
			"routing_numbers":["12442-24235"],
			"status": "confirmed",
			"date": "` + thisYear + `-12-18",
			"time": "8:00"
			"timezone": "America/Los_Angeles",
		},
	  ]
	}

	If no appointment was confirmed or found, return:

	{
	  "appointments": []
	}

	The user e-mails are in English - US date format.`

	userPrompt := email.Subject + "\n" + email.BodyWithoutSignature

	response, err := openAI.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		openai.ResponseOptions{
			UserPrompt:      userPrompt,
			DeveloperPrompt: prompt,
			Schema:          extractor.GenerateSchema[PromptResponse](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("LLM appointment confirmation call failed: %w", err)
	}

	promptResponse, err := extractor.StructExtractor[PromptResponse](response.Content)
	if err != nil {
		return res, fmt.Errorf("failed to extract JSON from OpenAI response: %w", err)
	}

	log.Debug(ctx, fmt.Sprintf("OpenAI response: %s", promptResponse))

	for _, appointment := range promptResponse.Appointments {
		if appointment.Status != "confirmed" {
			log.Debugf(ctx, "status not confirmed, skipping suggestion")
			continue
		}

		for _, loadCode := range appointment.RoutingNumbers {
			// NOTE: We can't rely on email upsert in processor to populate email.Loads.IDs; it does for loads created
			// but not updated ones. So we explicitly look it up so we can associate it with suggestion
			load, err := rds.GetLoadByFreightID(ctx, email.ServiceID, loadCode)
			if err != nil {
				log.WarnNoSentry(
					ctx,
					"error looking up load by freightID, trying externalTMSID",
					zap.Error(err),
					zap.String("loadCode", loadCode),
				)

				load, err = rds.GetLoadByExternalTMSID(ctx, email.ServiceID, loadCode)
				if err != nil {
					log.WarnNoSentry(
						ctx,
						"error looking up load by externalTMSID	",
						zap.Error(err),
						zap.String("loadCode", loadCode),
					)

					continue

				}
			}

			var loc string
			// Always keep timestamps for Aljex users TZ agnostic
			if load.TMS.Name != models.Aljex {
				loc = or(appointment.Timezone, load.Pickup.Timezone)
			}

			sugTime := StringsToTime(ctx, appointment.Date, appointment.Time, loc)
			if !sugTime.Valid {
				continue
			}

			suggestedLoadChange := models.SuggestedLoadChange{
				Account:           email.Account,
				FreightTrackingID: load.FreightTrackingID,
				ServiceID:         email.ServiceID,
				Suggested: models.SuggestedChanges{
					Changes: &models.Changes{
						PickupApptTime:     sugTime,
						PickupApptTimezone: loc,
					},
				},
				Applied:  models.SuggestedChanges{},
				Pipeline: models.AppointmentConfirmation,
				Category: models.PickupInfo,
				Status:   models.Pending,
				EmailID:  email.ID,
				ThreadID: email.ThreadID,
				Email:    email,
				LoadID:   &load.ID,
			}

			res = append(res, suggestedLoadChange)
		}
	}

	return res, err
}

// Returns first non-empty, non-blank string. Otherwise, returns an empty string.
func or(a, b string) string {
	a = strings.TrimSpace(a)
	b = strings.TrimSpace(b)

	if a != "" {
		return a
	}

	return b
}
