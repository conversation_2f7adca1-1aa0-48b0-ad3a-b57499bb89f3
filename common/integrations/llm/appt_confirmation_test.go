package llm

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

type TestCase struct {
	Body               string
	TMSName            models.IntegrationName
	ExpectedSuggestion *models.Changes
}

func TestExtractApptConfirmationSuggestions(t *testing.T) {
	testCases := map[string]TestCase{
		"NoTimezone": {
			Body:    "",
			TMSName: models.Turvo,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2023-08-29", "11:00", ""),
				PickupApptTimezone: "",
			},
		},
		"WithTimezone": {
			Body:    "America/New_York",
			TMSName: models.Turvo,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2023-08-29", "11:00", "America/New_York"),
				PickupApptTimezone: "America/New_York",
			},
		},
		"WithTimezoneButAljexTMS": {
			Body:    "America/New_York",
			TMSName: models.Aljex,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2023-08-29", "11:00", ""),
				PickupApptTimezone: "",
			},
		},
	}

	for name, testCase := range testCases {

		t.Run(name, func(t *testing.T) {

			mockOpenAI := new(MockOpenaiService)
			mockRDS := new(MockRDS)

			mockOpenAI.On("GetResponse",
				mock.Anything,
				mock.Anything,
			).Return(
				wrapTestExpectedToOpenAI(
					fmt.Sprintf(`{
						"appointments": [
							{
								"routing_numbers": ["2080005"],
								"status": "confirmed",
								"date": "2023-08-29",
								"time": "11:00",
								"timezone": "%s"
							}
						]
					}`, testCase.Body),
				),
				nil,
			)

			var mockedLoad = models.Load{
				FreightTrackingID: "2080005",
				ServiceID:         1,
				TMS:               models.Integration{Name: testCase.TMSName},
				LoadCoreInfo: models.LoadCoreInfo{
					Status:         "open",
					PONums:         "1, 2, 3",
					Operator:       "none",
					Specifications: models.Specifications{TotalOutPalletCount: 1},
				},
			}

			var mockedEmail = models.Email{
				Account:   "<EMAIL>",
				UserID:    0,
				ServiceID: 1,
				Body: `Please see below for confirmed appointment,

			PO: 2080005
			Appointment Number  : A123
			Shmt/Load Number  . : SH123
			DATE: 08/29
			TIME: 1100` + testCase.Body,
			}

			var expectedResult = []models.SuggestedLoadChange{
				{
					Account:           "<EMAIL>",
					FreightTrackingID: "2080005",
					ServiceID:         1,
					Suggested: models.SuggestedChanges{
						Changes: testCase.ExpectedSuggestion,
					},
					Applied:  models.SuggestedChanges{},
					Status:   models.Pending,
					Category: models.PickupInfo,
					Pipeline: models.AppointmentConfirmation,
					LoadID:   &mockedLoad.ID,
					EmailID:  mockedEmail.ID,
					ThreadID: mockedEmail.ThreadID,
					Email:    mockedEmail,
				},
			}

			// Mock the RDS call
			mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

			result, err := ExtractApptConfirmationSuggestions(
				context.Background(),
				mockedEmail,
				mockOpenAI,
				mockRDS,
			)
			result[0].BraintrustSpanID = ""
			assert.NoError(t, err)
			assert.Equal(t, expectedResult, result)

			// Assert that the mocked methods were indeed called
			mockOpenAI.AssertExpectations(t)
			mockRDS.AssertExpectations(t)
		})
	}
}

func TestExtractPickupApptConfirmationNoFounds(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)
	mockRDS := new(MockRDS)

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body: `Please see below for confirmed appointment,

		PO: 2080005
		Appointment Number  : A123
		Shmt/Load Number  . : SH123
		DATE: 08/29
		TIME: 1100`,
	}

	var expectedResult []models.SuggestedLoadChange

	// Mocking external calls
	mockOpenAI.On("GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(
		wrapTestExpectedToOpenAI(
			`{"appointments": [
				{
					"routing_numbers": ["2080005"],
					"status": "unknown"
				}
			]}`,
		),
		nil,
	)

	result, err := ExtractApptConfirmationSuggestions(context.Background(), mockedEmail, mockOpenAI, mockRDS)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockOpenAI.AssertExpectations(t)
	mockRDS.AssertNotCalled(t, "GetLoadByFreightID")
}

func TestLiveExtractApptConfirmationSuggestions(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveExtractPickupApptConfirmation: run with LIVE_TEST=true to enable")
		return
	}

	mockRDS := new(MockRDS)
	openaiService := new(MockOpenaiService)
	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	testCases := map[string]TestCase{
		"NoTZ": {
			Body:    "ok appt confirmed for 5/10 0800",
			TMSName: models.Turvo,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2024-05-10", "08:00", ""),
				PickupApptTimezone: "",
			},
		},
		"NewYork": {
			Body:    "ok appt confirmed for 5/10 0800 ET",
			TMSName: models.Turvo,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2024-05-10", "08:00", "America/New_York"),
				PickupApptTimezone: "America/New_York",
			},
		},
		"LosAngeles": {
			Body:    "ok appt confirmed for 5/23 10:00 PT",
			TMSName: models.Turvo,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2024-05-23", "10:00", "America/Los_Angeles"),
				PickupApptTimezone: "America/Los_Angeles",
			},
		},
		"LosAngelesButAljexTMS": {
			Body:    "ok appt confirmed for 5/23 10:00 PT",
			TMSName: models.Aljex,
			ExpectedSuggestion: &models.Changes{
				PickupApptTime:     StringsToTime(context.Background(), "2024-05-23", "10:00", "America/Los_Angeles"),
				PickupApptTimezone: "America/Los_Angeles",
			},
		},
	}

	for name, testCase := range testCases {
		mockedLoad.TMS = models.Integration{Name: testCase.TMSName}

		var mockedEmail = models.Email{
			Account:   "<EMAIL>",
			UserID:    0,
			ServiceID: 1,
			Sender:    "<NAME_EMAIL>",
			Subject:   "2080005 Appt",
			Body: testCase.Body +
				`*FILIPE OLIVEIRA*
			Software Engineering | Axle Technologies
			[image: emailAddress] <EMAIL>`,
			Loads: []models.Load{mockedLoad},
		}

		var expectedResult = []models.SuggestedLoadChange{
			{
				Account:           "<EMAIL>",
				FreightTrackingID: "2080005",
				ServiceID:         1,
				Suggested: models.SuggestedChanges{
					Changes: testCase.ExpectedSuggestion,
				},
				Status:   models.Pending,
				Category: models.PickupInfo,
				Pipeline: models.AppointmentConfirmation,
				LoadID:   &mockedLoad.ID,
				EmailID:  mockedEmail.ID,
				ThreadID: mockedEmail.ThreadID,
				Email:    mockedEmail,
			},
		}
		t.Run(name, func(t *testing.T) {

			// Mock RDS call
			mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)
			// Live test openAI
			result, err := ExtractApptConfirmationSuggestions(context.Background(), mockedEmail,
				openaiService, mockRDS)
			result[0].BraintrustSpanID = ""

			assert.NoError(t, err)
			assert.Equal(t, expectedResult, result)
			mockRDS.AssertExpectations(t)
		})
	}

}
