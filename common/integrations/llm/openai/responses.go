package openai

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/braintrustdata/braintrust-go"
	"github.com/braintrustdata/braintrust-go/shared"
	"github.com/google/uuid"
	openaiSDK "github.com/openai/openai-go/v2"
	openaiOption "github.com/openai/openai-go/v2/option"
	openaiParam "github.com/openai/openai-go/v2/packages/param"
	openaiResponses "github.com/openai/openai-go/v2/responses"
	openaiShared "github.com/openai/openai-go/v2/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

var (
	openaiClient openaiSDK.Client
	clientOnce   sync.Once
	clientErr    error

	defaultModel           = openaiShared.ChatModelGPT5Nano
	defaultReasoningEffort = ReasoningEffortLow
)

func floatPtr(f float64) *float64 {
	return &f
}

// Temperature constants for common use cases.
// The temperature parameter controls the randomness of the model's output, scaling from 0.0 to 2.0.
// A lower temperature (e.g., 0.0) makes the model more deterministic and repeatable,
// while a higher temperature (e.g., 0.7) increases creativity and variability.
//
// Recommended values:
// - TemperatureDeterministic (0.0): Use for data extraction, summarization, and other tasks needing consistency.
// - TemperatureBalanced (0.3): A general-purpose setting for balanced, coherent output.
// - TemperatureCreative (0.7): Use for tasks like brainstorming or creative writing.
//
// For more details, see the official OpenAI API documentation on temperature.
var (
	TemperatureDeterministic = floatPtr(0.0)
	TemperatureBalanced      = floatPtr(0.3)
	TemperatureCreative      = floatPtr(0.7)
)

// Reasoning effort constants for GPT-5 series models.
// Higher efforts generally produce better qualtiy responses but take longer and cost marginally more due to extra
// thinking tokens.
var (
	ReasoningEffortMinimal = openaiShared.ReasoningEffortMinimal
	ReasoningEffortLow     = openaiShared.ReasoningEffortLow
	ReasoningEffortMedium  = openaiShared.ReasoningEffortMedium
	ReasoningEffortHigh    = openaiShared.ReasoningEffortHigh
)

type (
	ResponseOptions struct {
		// DeveloperPrompt is the extraction instructions.
		DeveloperPrompt string
		// UserPrompt is the attachment or email data.
		UserPrompt         string
		Model              *openaiShared.ChatModel
		Schema             any
		PreviousResponseID string
		Temperature        *float64
		ReasoningEffort    *openaiShared.ReasoningEffort
		// ExtraMetadata allows passing additional metadata to be logged in Braintrust
		ExtraMetadata map[string]any
		// ImageContent allows sending base64-encoded images to vision models
		// Format: "data:image/jpeg;base64,..." or "data:image/png;base64,..."
		ImageContent string
	}

	GetResponseOutput struct {
		Content         string
		ResponseID      string
		BraintrustLogID string
	}
)

// GetResponse creates a response using the OpenAI responses API.
func (s *service) GetResponse(
	ctx context.Context,
	email models.Email,
	attachment models.Attachment,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	options ...ResponseOptions,
) (res GetResponseOutput, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "openai.GetResponse", nil)
	defer func() { metaSpan.End(err) }()

	params := s.buildResponseParams(options...)

	var braintrustInput braintrustsdk.BraintrustLogInput
	if len(options) > 0 {
		model := defaultModel
		if options[0].Model != nil {
			model = *options[0].Model
		}

		reasoningEffort := defaultReasoningEffort
		if options[0].ReasoningEffort != nil {
			reasoningEffort = *options[0].ReasoningEffort
		}

		braintrustInput = braintrustsdk.BraintrustLogInput{
			ID:                   uuid.NewString(),
			Email:                email,
			Attachment:           attachment,
			ProjectDetails:       braintrustProjectDetails,
			DeveloperPrompt:      options[0].DeveloperPrompt,
			UserPrompt:           options[0].UserPrompt,
			OpenAIConversationID: options[0].PreviousResponseID,
			Model:                model,
			ReasoningEffort:      reasoningEffort,
			Temperature:          options[0].Temperature,
			ExtraMetadata:        options[0].ExtraMetadata,
		}
	}

	responseContent, responseID, err := s.getResponse(ctx, braintrustInput, params)

	return GetResponseOutput{
		Content:         responseContent,
		ResponseID:      responseID,
		BraintrustLogID: braintrustInput.ID,
	}, err
}

// buildChatParams builds the parameters for the OpenAI responses API.
// The parameters are model, developer prompt, user prompt, and JSON schema for structured output.
func (s *service) buildResponseParams(options ...ResponseOptions) openaiResponses.ResponseNewParams {
	opts := ResponseOptions{}
	if len(options) > 0 {
		opts = options[0]
	}

	model := defaultModel
	if opts.Model != nil {
		model = *opts.Model
	}

	params := openaiResponses.ResponseNewParams{
		Model: model,
	}

	// GPT-5 series models do not support the temperature parameter
	if opts.Temperature != nil && !isGPT5Model(model) {
		params.Temperature = openaiSDK.Opt(*opts.Temperature)
	}

	// Set reasoning effort for GPT-5 series models, defaulting to "low" if not specified
	if isGPT5Model(model) {
		reasoningEffort := defaultReasoningEffort
		if opts.ReasoningEffort != nil {
			reasoningEffort = *opts.ReasoningEffort
		}
		params.Reasoning = openaiShared.ReasoningParam{
			Effort: reasoningEffort,
		}
	}

	if opts.PreviousResponseID != "" {
		params.PreviousResponseID = openaiSDK.String(opts.PreviousResponseID)
	}

	var messages []openaiResponses.ResponseInputItemUnionParam

	if opts.DeveloperPrompt != "" {
		messages = append(messages, openaiResponses.ResponseInputItemUnionParam{
			OfMessage: &openaiResponses.EasyInputMessageParam{
				Role: openaiResponses.EasyInputMessageRoleDeveloper,
				Content: openaiResponses.EasyInputMessageContentUnionParam{
					OfString: openaiSDK.String(opts.DeveloperPrompt),
				},
			},
		})
	}

	// If the user prompt is provided and the previous message ID is not present, add the user prompt to the messages.
	// This is to ensure that the user prompt is always the first message in the conversation.
	// The user prompt is the shipment information that we are extracting from.
	if (opts.UserPrompt != "" || opts.ImageContent != "") && !params.PreviousResponseID.Valid() {
		// If we have image content, use multi-part content
		if opts.ImageContent != "" {
			contentParts := openaiResponses.ResponseInputMessageContentListParam{}

			// Add text if provided
			if opts.UserPrompt != "" {
				contentParts = append(contentParts, openaiResponses.ResponseInputContentUnionParam{
					OfInputText: &openaiResponses.ResponseInputTextParam{
						Text: opts.UserPrompt,
						Type: "input_text",
					},
				})
			}

			// Add image
			contentParts = append(contentParts, openaiResponses.ResponseInputContentUnionParam{
				OfInputImage: &openaiResponses.ResponseInputImageParam{
					Detail:   openaiResponses.ResponseInputImageDetailHigh,
					ImageURL: openaiSDK.String(opts.ImageContent),
					Type:     "input_image",
				},
			})

			messages = append(messages, openaiResponses.ResponseInputItemUnionParam{
				OfMessage: &openaiResponses.EasyInputMessageParam{
					Role: openaiResponses.EasyInputMessageRoleUser,
					Content: openaiResponses.EasyInputMessageContentUnionParam{
						OfInputItemContentList: contentParts,
					},
				},
			})
		} else {
			// Text-only message
			messages = append(messages, openaiResponses.ResponseInputItemUnionParam{
				OfMessage: &openaiResponses.EasyInputMessageParam{
					Role: openaiResponses.EasyInputMessageRoleUser,
					Content: openaiResponses.EasyInputMessageContentUnionParam{
						OfString: openaiSDK.String(opts.UserPrompt),
					},
				},
			})
		}
	}

	params.Input = openaiResponses.ResponseNewParamsInputUnion{
		OfInputItemList: messages,
	}

	if opts.Schema != nil {
		format := openaiResponses.ResponseFormatTextConfigParamOfJSONSchema("json_schema", opts.Schema.(map[string]any))
		if format.OfJSONSchema != nil {
			format.OfJSONSchema.Strict = openaiSDK.Bool(true)
		}
		params.Text = openaiResponses.ResponseTextConfigParam{Format: format}
	}

	return params
}

// For logging purposes
var openAIObject = models.Integration{Name: models.OpenAI}

// getResponse sends a responses API request to OpenAI and returns the response content and ID.
func (s *service) getResponse(
	ctx context.Context,
	braintrustInput braintrustsdk.BraintrustLogInput,
	params openaiResponses.ResponseNewParams,
) (_ string, _ string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "openai.getResponse", nil)
	defer func() { metaSpan.End(err) }()

	if openaiParam.IsOmitted(params.Input.OfString) && openaiParam.IsOmitted(params.Input.OfInputItemList) {
		return "", "", errors.New("no input messages provided, please provide a developer prompt and/or user prompt")
	}

	// default is 2 retries, 60s timeout for each retry
	response, err := s.client.Responses.New(ctx, params, openaiOption.WithRequestTimeout(60*time.Second))
	if err != nil {
		var apiErr *openaiSDK.Error
		if errors.As(err, &apiErr) && apiErr != nil {
			httplog.LogHTTPResponseCode(ctx, openAIObject, apiErr.StatusCode)

			if helpers.IsArrayElmInString(
				strings.ToLower(apiErr.Message),
				[]string{"timeout", "timed out", "client.timeout"},
			) {
				return "", "", fmt.Errorf("OpenAI API timeout: %w", err)
			}

			log.Error(
				ctx,
				"OpenAI error",
				zap.String("Status Code", apiErr.Code),
				zap.String("Message", apiErr.Message),
			)
		}

		httplog.LogHTTPRequestFailed(ctx, openAIObject, err)

		var httpErr *net.OpError
		if errors.As(err, &httpErr) && httpErr != nil {
			if httpErr.Timeout() {
				return "", "", fmt.Errorf("HTTP request timed out: %w", err)
			}
		}
		return "", "", fmt.Errorf("failed to get completion: %w", err)
	}
	httplog.LogHTTPResponseCode(ctx, openAIObject, http.StatusOK)

	// Only build and submit Braintrust logs when a valid project is provided.
	if braintrustInput.ProjectDetails.ID != braintrustsdk.NoProject {
		braintrustErr := braintrustsdk.SubmitLog(
			ctx,
			braintrustInput.ProjectDetails,
			getBraintrustLogForChat(ctx, braintrustInput, response),
		)
		if braintrustErr != nil {
			if env.Vars.AppEnv == "prod" {
				log.WarnNoSentry(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
			} else {
				log.Debug(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
			}
		}
	}

	content := response.OutputText()
	if content == "" {
		return "", "", fmt.Errorf("empty message content from LLM for completion %s", response.ID)
	}

	return content, response.ID, nil
}

func getBraintrustLogForChat(
	ctx context.Context,
	logInput braintrustsdk.BraintrustLogInput,
	openAIResponse *openaiResponses.Response,
) braintrust.InsertProjectLogsEventParam {

	env := os.Getenv("APP_ENV")
	service, err := rds.GetServiceByID(ctx, logInput.Email.ServiceID)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"error getting service for braintrust log",
			zap.Error(err),
			zap.Uint("serviceID", logInput.Email.ServiceID),
		)
	}

	return braintrust.InsertProjectLogsEventParam{
		ID: braintrust.String(logInput.ID),
		Input: braintrust.Raw[any](
			map[string]any{
				"user_prompt":      logInput.UserPrompt,
				"developer_prompt": logInput.DeveloperPrompt,
			},
		),
		Output: braintrust.Raw[any](openAIResponse.OutputText()),
		Tags:   braintrust.Raw[[]string](logInput.ProjectDetails.GetTags()),
		Metrics: braintrust.Raw[shared.InsertProjectLogsEventMetricsParam](
			shared.InsertProjectLogsEventMetricsParam{
				CompletionTokens: braintrust.Int(openAIResponse.Usage.TotalTokens),
				PromptTokens:     braintrust.Int(openAIResponse.Usage.InputTokens),
				Tokens:           braintrust.Int(openAIResponse.Usage.OutputTokens + openAIResponse.Usage.InputTokens),
			},
		),
		Metadata: braintrust.Raw[shared.InsertProjectLogsEventMetadataParam](
			shared.InsertProjectLogsEventMetadataParam{
				Model: braintrust.String(logInput.Model),
				ExtraFields: map[string]any{
					"environment":      env,
					"conversation_id":  logInput.OpenAIConversationID,
					"reasoning_effort": safeReasoningEffortString(logInput.ReasoningEffort),
					"temperature":      safeTemperatureFloat(logInput.Temperature),

					// Email fields
					"email_address":     logInput.Email.Account,
					"email_id":          logInput.Email.ID,
					"email_subject":     logInput.Email.Subject,
					"email_external_id": logInput.Email.ExternalID,
					"email_thread_id":   logInput.Email.ThreadID,

					// User and Service fields
					"user_id":               logInput.Email.UserID,
					"service_id":            logInput.Email.ServiceID,
					"service_name":          service.Name,
					"service_email_domains": service.EmailDomains,

					// Attachment fields
					"attachment_name": logInput.Attachment.OriginalFileName,
					"attachment_url":  logInput.Attachment.S3URL,
					"attachment_type": logInput.Attachment.MimeType,
					"extra_metadata":  logInput.ExtraMetadata,
				},
			},
		),
		SpanAttributes: braintrust.Raw[shared.SpanAttributesParam](
			shared.SpanAttributesParam{
				Name: braintrust.String(string(logInput.ProjectDetails.StepName)),
			},
		),
	}
}

// isGPT5Model checks if the given model is a GPT-5 series model.
// GPT-5 series models do not support the temperature parameter.
func isGPT5Model(model openaiShared.ChatModel) bool {
	return strings.HasPrefix(model, "gpt-5") ||
		strings.HasPrefix(model, "gpt5")
}

func safeReasoningEffortString(effort openaiShared.ReasoningEffort) string {
	if effort == "" {
		return ""
	}
	return string(effort)
}

func safeTemperatureFloat(temp *float64) any {
	if temp == nil {
		return nil
	}
	return *temp
}
