package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/log"
)

var Vars envVars
var Secrets envVars

type envVars struct {
	// For prod only
	SecretARN string `envconfig:"SECRET_ARN"`
	// Deployment stage: "dev" or "prod"
	AppEnv                 string `envconfig:"APP_ENV" required:"true"`
	OpenAIAPIKey           string `envconfig:"OPENAI_API_KEY"`
	EnableVectorEmbeddings string `envconfig:"ENABLE_VECTOR_EMBEDDINGS"`
}

// Format in AWS secrets manager
type envSecrets struct {
	OpenAIAPIKey string `json:"OPENAI_API_KEY"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file found")
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.OpenAIAPIKey == "" {
			log.WarnNoSentry(ctx, "missing OPENAI_API_KEY env var")
		}
		Secrets.OpenAIAPIKey = Vars.OpenAIAPIKey

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.SecretARN, &secret); err != nil {
			return err
		}

		if secret.OpenAIAPIKey == "" {
			return fmt.Errorf("%s is missing some fields", Vars.SecretARN)
		}

		Vars.OpenAIAPIKey = secret.OpenAIAPIKey
		Secrets.OpenAIAPIKey = secret.OpenAIAPIKey
	}

	return nil
}
