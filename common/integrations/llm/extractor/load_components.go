package extractor

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	promptBuilder "github.com/drumkitai/drumkit/common/integrations/llm/prompt_builder"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsUserDB "github.com/drumkitai/drumkit/common/rds/tmsuser"
)

//nolint:lll
const (
	GenericInstructions = `
	Missing Information: If any required information is absent in the shipping order, set the field's value to empty unless otherwise specified on a per field basis.
	Data Integrity: Only extract information directly from the shipping order; do not make any assumptions or add any data not explicitly provided.
	IMPORTANT: Provide ONLY the JSON object in your response, nothing else. Do not include any explanatory text, markdown formatting, or code blocks around the JSON.
	Do not use the example output provided here in the final response. The example is for reference purposes only.
	`
)

//nolint:lll
var (
	dateTimeNotes = `
	Date/Time Formatting:
	If a specific date & time is provided, format it as "mm/dd/yyyy, hh:MM+AM/PM".
		Example: "03/07/2024, 02:00AM".
	Do not mix formats - if time is in 24-hour format, always convert to 12-hour time with AM/PM indicators.
	If no specific time is given, use only the date in the format "mm/dd/yyyy".
		Example: "03/07/2024".
	If year is not specified, infer based on today's date (` +
		time.Now().Local().Format(time.DateOnly) + `).
	Avoid partial or incorrect formats, such as "03/07/2024, " or "03/07". If the date is incomplete (lacking month, day, or year), set the field to null.
	`
)

// extractBasicInfo extracts basic load information from either an email body or attachment
func extractBasicInfo(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	initialResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
) (result BasicInfo, rawResult BasicInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractBasicInfo", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	var refNumNotes string

	// Handle special case for MJB to add specific instructions
	if email.ServiceID == TridentServiceID {
		if check := checkForStringInUserPrompt("MJB", userPrompt); check {
			refNumNotes = `Note in the case of MJB, the ref # is the shipping order number listed after 'Ship To'.
            And the Customer PO should be assigned to the poNums field.`
		}
	}

	//nolint:lll
	prompt := fmt.Sprintf(`
    Extract the following basic information with reference number candidates:

	**IMPORTANT: Always look for multiple reference numbers in the email and include them as candidates.**
	**If you see numbers like "Order #: ORD-123, Invoice: INV-456, BOL: BOL-789, Release #: 123456, Load #: 123456", include those values as reference number candidates.**

    Instructions:
    * "refNumber": Extract the shipping order number or the release number. Never extract a warehouse code as the refNumber.
		- refNumber should be the waybill number if there is one associated with the shipment.
		- If there is no waybill number mentioned, then it should be the order/release/shipping/load order number.
        - If both a PO # and a order/release/shipping/load order # are provided, this field should be the order/release/shipping/load order number, not the PO #.
        - Never include additional text here, only extract the ref number itself.
		%s
    * "poNums": Extract the PO #s if provided. If both a PO # and a order/release/shipping order # are provided, this field should be the PO #. If there are multiple potential PO #s provided, this field should be a comma separated list of the PO #s.
	* "refNumberCandidates": Extract alternative reference numbers that could reasonably be used as the primary reference number.
		- Include numbers with different labels (Order #, Invoice #, BOL #, Sales Order #, Customer Ref #, Release #, Load #, Conf #, etc.)
		- Include numbers from different sections of the document (header, body, signature, attachments)
		- Include numbers that might be confused with the primary reference number
		- Only include numbers that are clearly reference numbers (not phone numbers, addresses, or other data)
		- Exclude the primary refNumber and poNums from this list
		- Provide 2-5 alternative options when available
		- EXAMPLES: If email contains "Order #: ORD-123, Invoice: INV-456, BOL: BOL-789", 
		  then refNumber="ORD-123" and refNumberCandidates=["INV-456", "BOL-789"]

	Format your response as a JSON object with:
	{
		"refNumber": "ORD-2024-1128",
		"poNums": "84X701801",
		"refNumberCandidates": [
			"INV-789456",
			"BOL-2024-1128-001",
			"SO-2024-1128",
			"CR-37TH-BAKERY-001"
		]
	}

    %s
    `, refNumNotes, GenericInstructions)

	// Add detailed example for candidate generation
	prompt += `

### **CANDIDATE GENERATION EXAMPLE**

**Input Email:**
"Order #: ORD-2024-1128
PO# 84X701801
Invoice: INV-789456
BOL: BOL-2024-1128-001
Sales Order: SO-2024-1128
Customer Ref: CR-37TH-BAKERY-001"

**Expected Output:**
{
    "refNumber": "ORD-2024-1128",
    "poNums": "84X701801", 
    "refNumberCandidates": [
        "INV-789456",
        "BOL-2024-1128-001",
        "SO-2024-1128",
        "CR-37TH-BAKERY-001"
    ]
}

**Key Points:**
- refNumber = the primary order/release number (ORD-2024-1128)
- poNums = the PO number (84X701801)
- refNumberCandidates = ALL other reference numbers found in the email
- Include numbers with different prefixes (INV-, BOL-, SO-, CR-, etc.)
- Include numbers that could reasonably be used as reference numbers
`

	// Conditionally add the historical data for pattern recognition
	if len(historicalLoadData) > 0 {
		// Use a formatted string or JSON to clearly present the examples
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.CustomerRefNumber != "" || load.PoNums != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s, poNums: %v\n", load.CustomerRefNumber, load.PoNums)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`

        Use the following historical data to help identify patterns in the refNumber and poNums.
        Analyze these patterns for:
        1. Length: How many characters are typically used
        2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
        3. Prefix patterns: Common starting characters or sequences
        4. Numeric patterns: Position and length of numeric sequences
        5. Delimiter usage: Common separators like '-', '_', or '/'

        Historical examples:
        %s

        Use these patterns to identify new reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
        `, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"basicInfo",
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom basicInfo prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			Schema:             GenerateSchema[BasicInfo](),
			DeveloperPrompt:    prompt,
			PreviousResponseID: initialResponseID,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[BasicInfo](response.Content)
	if err != nil {
		return result, result, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return result, result, response.BraintrustLogID, nil
}

// extractCustomer extracts customer information from either an email body or attachment
func extractCustomer(
	ctx context.Context,
	openaiService openai.Service,
	tmsID uint,
	email models.Email,
	attachment models.Attachment,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (
	tmsCustomer *models.TMSCustomer,
	rawLLMCustomer CustomerInfo,
	initialResponseID string,
	braintrustLogID string,
	err error,
) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractCustomer", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	accountDomain := extractDomain(email.Account)

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics analyst. Your task is to identify the **shipper's company name** and the **original sender's email address** from the provided context.

**Context:**
You will be given a set of information organized with the following tags:
- <email_sender>: The email address of the party who sent the email to us.
- <email_subject>: The subject line of the email.
- <email_body>: The full text content of the email. This is often the most important section. Look for company names in signatures or greetings.
- <pdf_content>: The text content extracted from any PDF attachment. This is also a primary source for the shipper's name.

**Extraction Instructions:**
1.  **shipper_name**:
    - Your primary goal is to find the name of the company that is requesting the shipment (the shipper).
    - **Prioritize the <email_body>**. Look for company names in email signatures, greetings (e.g., "This is Jane from..."), or forwarded messages.
    - If the body is unhelpful, look for the shipper's name in the <pdf_content>, often near labels like "Shipper:", "From:", or at the top of the document.
    - The shipper is the company sending the goods, not a logistics provider or a warehouse, unless they are the same entity.
    - **Do NOT** include suffixes like "LLC", "INC", or "CO". Extract the clean company name.
    - If no company name can be found, leave the field empty.

2.  **original_sender_email**:
    - Find the email address of the person who originally sent the request.
    - Check the <email_body> for forwarded message blocks. Look for a 'From:' line that has a different domain than our company's domain, which is '%s'.
    - If there is no forwarded block, use the value from <email_sender>.

**Examples:**

### Example 1: Name in Email Body
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>FW: New Load</email_subject>
<email_body>
Hi team,
Please see attached from our customer.
Thanks,
--
From: "Jane Doe" <<EMAIL>>
Date: Tuesday, July 30, 2024 at 11:00 AM
Subject: New Load

Please quote the attached.
</email_body>
<pdf_content>
SHIPPER: LMNOP Goods
123 Main St...
</pdf_content>

**Output:**
{
  "shipper_name": "LMNOP Goods",
  "original_sender_email": "<EMAIL>"
}

### Example 2: Name in PDF Only
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Load for you</email_subject>
<email_body>
See attached for the new load.
</email_body>
<pdf_content>
Load Confirmation
Shipper: Alpha Logistics, LLC
Pickup: Warehouse B
...
</pdf_content>

**Output:**
{
  "shipper_name": "Alpha Logistics",
  "original_sender_email": "<EMAIL>"
}

### Example 3: Forwarded Email Chain
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Fwd: Load Tender 123</email_subject>
<email_body>
---------- Forwarded message ---------
From: Bob Johnson <<EMAIL>>
Date: Mon, Jul 29, 2024 at 10:00 AM
Subject: Load Tender 123
To: <EMAIL>

Hi, please see the attached file for details on the new load.
</email_body>
<pdf_content>
...
</pdf_content>

**Output:**
{
  "shipper_name": "Shipper Company",
  "original_sender_email": "<EMAIL>"
}

%s
        `, accountDomain, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForTMS(
		ctx,
		tmsID,
		"loadBuilding",
		models.PromptExtractorNameCustomer,
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom customer prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			Schema:          GenerateSchema[CustomerInfo](),
			UserPrompt:      userPrompt,
			Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("error getting response: %w", err)
	}

	result, err := StructExtractor[CustomerInfo](response.Content)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	rawLLMCustomer = result

	// Try looking up by name parsed from LLM
	if result.ShipperName != "" && !strings.Contains(strings.ToLower(result.ShipperName), "lmnop") {
		// If the shipper name is not LMNOP, then we need to find the original sender email
		// and use that to map to a TMSCustomer
		mappedCustomer, err := MapCustomer(
			ctx, tmsID, result.ShipperName, userPrompt,
		)
		if err != nil {
			return tmsCustomer,
				rawLLMCustomer,
				response.ResponseID,
				response.BraintrustLogID,
				fmt.Errorf("failed to map customer %s to customer: %w", result.ShipperName, err)
		} else if mappedCustomer != nil {
			return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
		}
	}

	// If no mapped customer, then try looking up by original sender email
	if result.OriginalSenderEmail == "" || strings.Contains(strings.ToLower(result.OriginalSenderEmail), "lmnop") {
		log.WarnNoSentry(
			ctx, "no original sender email found",
			zap.String("originalSenderEmail", result.OriginalSenderEmail),
		)

		return tmsCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
	}

	domain := ExtractSecondLevelDomain(result.OriginalSenderEmail)
	if domain == "" {
		domain = result.OriginalSenderEmail
	}

	mappedCustomer, err := MapCustomer(
		ctx, tmsID, domain, userPrompt,
	)
	if err != nil {
		return tmsCustomer,
			rawLLMCustomer,
			response.ResponseID,
			response.BraintrustLogID,
			fmt.Errorf("failed to map domain %s to customer: %w", domain, err)
	}

	return mappedCustomer, rawLLMCustomer, response.ResponseID, response.BraintrustLogID, nil
}

// extractRateData extracts rate information from either an email body or attachment
func extractRateData(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (result RateDataInfo, rawResult RateDataInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractRateData", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics data extractor. Your task is to extract rate and charge information from the provided shipping documents and format it into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
    "rateData": {
        "collectionMethod": "Prepaid",
        "customerRateType": "Flat",
        "customerLineHaulRate": 1500.00,
        "customerRateNumUnits": 1
    }
}

### **Instructions**

*   **collectionMethod**: Must be one of "COD", "Collect", "Third-Party", or "Prepaid". If not explicitly stated, default to "Prepaid".
*   **customerRateType**: Must be one of "Flat", "Distance", "CWT" (hundredweight), or "Tons". If not specified, default to "Flat". The rate type should not contain any other text besides the options explicitly mentioned.
*   **customerLineHaulRate**: Extract the customer rate. The load data may refer to "carrier pay" or "carrier rates", which should be treated as the customer rate. **CRITICAL: If no rate, price, or charge is mentioned anywhere in the document, this value MUST be set to 0. It is a severe error to invent a number.**
*   **customerRateNumUnits**: This is the number of units for the rate. For example, if the rate is distance-based, this would be the number of miles. If the rate type is "Flat", this MUST be set to 1.

### **Examples**

1.  **Rate Provided**:
    *   **Input**:
        """
        Hi Team,
        Please see the attached BOL for the load going from Chicago to Miami.
        The carrier pay is set at $3200. Let me know if you have any questions.
        Thanks,
        John
        """
    *   **Output**:
        {
            "rateData": {
                "collectionMethod": "Prepaid",
                "customerRateType": "Flat",
                "customerLineHaulRate": 3200.00,
                "customerRateNumUnits": 1
            }
        }

2.  **No Rate Provided**:
    *   **Input**:
        """
        Good morning,
        Attached is the rate confirmation for load #54321. Please have the driver sign and return.
        Pickup is at 8am tomorrow.
        Best,
        Sarah
        """
    *   **Output**:
        {
            "rateData": {
                "collectionMethod": "Prepaid",
                "customerRateType": "Flat",
                "customerLineHaulRate": 0,
                "customerRateNumUnits": 1
            }
        }

%s
`, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"rateData",
		prompt,
	)

	if err != nil {
		log.Warn(ctx, "error building custom rateData prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[RateDataSchema](),
			PreviousResponseID: previousResponseID,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[RateDataInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.RateData.CustomerRateType = normalizeRateType(ctx, result.RateData.CustomerRateType)

	if strings.EqualFold(result.RateData.CustomerRateType, "flat") {
		result.RateData.CustomerLineHaulCharge = models.ValueUnit{
			Val:  result.RateData.CustomerLineHaulRate,
			Unit: helpers.Or(result.RateData.CustomerLineHaulUnit, "USD"),
		}

		result.RateData.CustomerRateNumUnits = 1
	}

	if tms.Name != models.McleodEnterprise {
		// Skip Mcleod specific rate data processing
		return result, rawResult, response.BraintrustLogID, nil
	}

	if !helpers.IsStringInArray(
		[]string{"COD", "Collect", "Third-Party", "Prepaid"},
		result.RateData.CollectionMethod,
	) {
		// HACK: Historical data shows most of Payton's customers are using Third-Party
		if strings.Contains(email.Account, "<EMAIL>") ||
			strings.Contains(email.Account, "<EMAIL>") {

			result.RateData.CollectionMethod = "Third-Party"
		} else {
			result.RateData.CollectionMethod = "Prepaid"
		}
	}

	// HACK: Hard-code revenue code for Payton because his email is different in Mcleod
	if strings.Contains(email.Account, "<EMAIL>") ||
		strings.Contains(email.Account, "<EMAIL>") {

		result.RateData.RevenueCode = "Charleston"

	} else {
		tmsUser, err := tmsUserDB.GetByEmail(ctx, email.Account, tms.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error getting TMS user by email for revenue code",
				zap.Error(err),
				zap.String("emailAddress", email.Account),
			)
		} else {
			result.RateData.RevenueCode = tmsUser.RevenueCode
		}
	}

	if !helpers.IsStringInArray([]string{"Flat", "Distance", "CWT", "Tons"}, result.RateData.CustomerRateType) {
		result.RateData.CustomerRateType = "Flat"
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// QN is hh:MMam/pm a typo? There should be a space between the time and the AM/PM?
// extractPickup extracts pickup information from either an email body or attachment
func extractPickup(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
	options Options,
) (result PickupInfo, rawResult PickupInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractPickup", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	tmsSpecificPickupTimeInstructions := getPrompt(tms.Name, PromptPickupTimeInstructions)
	tmsSpecificPickupInstructions := getPrompt(tms.Name, PromptPickupInstructions)
	tmsSpecificPickupExamples := getPrompt(tms.Name, PromptPickupExamples)

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract pickup location details and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
    "pickup": {
        "name": "Company Name",
        "addressLine1": "Street Address",
        "addressLine2": "Suite/Unit",
        "city": "City",
        "state": "ST",
        "zipCode": "12345",
        "country": "US",
        "contact": "Contact Name",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "Business Hours",
        "refNumber": "Reference Number",
        "readyTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptType": "By appointment",
        "apptStartTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptEndTime": "mm/dd/yyyy, hh:MMam/pm",
        "apptNote": "Appointment notes",
        "timezone": "EDT",
		"refNumberCandidates": [
			"ORD-PU-1128",
			"WH-RCK-001"
		]
    }
}

### **Extraction Rules**
Apply these rules when extracting pickup data:

* **Pickup Location Identification**: Look for pickup information such as loading locations, ship from addresses, origin points, or shipper details.
    * Look for sections labeled "Ship From:", "Pickup From:", "Origin:", "Shipper:", "Loading Location:", "Load From:", etc.
    * **CRITICAL**: Do NOT extract "Ship To:" or destination information - only extract pickup/origin details
    * If multiple locations are present, extract only the pickup/origin location
	* Pickup appointment times are associated with the pickup/release location, look for keywords such as "Pickup By", "Pickup Time", "Req Ship Date", "Release Date", etc.

* **Field Requirements**:
    * **name**: Company or facility name (exclude address from this field)
    * **addressLine1**: Street address  
    * **addressLine2**: Suite, unit, etc. (leave empty if not specified)
    * **city**: City name
    * **state**: State code (e.g., "GA", "TX")  
    * **zipCode**: Postal code
    * **country**: Country code (default to "US")
    * **contact**: Contact person name. If multiple contacts are specified, extract the first one. Note the contact is **never** from %s company or has email domains %s.
    * **phone**: Phone number (format: ************). If multiple phone numbers are specified, extract the first one.
    * **email**: Email address. If multiple email addresses are specified, extract the first one.
    * **businessHours**: Business hours if specified
    * **refNumber**: Reference number specific to the pickup location. 
        - Look for numbers labeled as "Pickup Number", "Order Number", "Shipper Reference", or similar pickup-specific identifiers
        - AVOID numbers labeled as "Load ID", "BOL", "PRO", or "PO Number" - these belong at other levels
        - Pay attention to document structure: numbers appearing in pickup sections, near pickup addresses, or under pickup headings are more likely to be pickup-specific
        - When multiple reference numbers are present, prioritize:
          1) Numbers explicitly labeled as pickup-specific
          2) Numbers that appear only for this pickup stop (not repeated across all stops)
          3) Numbers that are clearly pickup-specific rather than shipment-wide
    %s
    * **apptNote**: Notes about the appointment or pickup instructions
    * **timezone**: Timezone code (e.g., "EDT", "CST"). Look for timezone indicators near pickup times.
	* **refNumberCandidates**: Additional reference numbers and identifiers for the pickup location.
		- Include alternative pickup reference numbers with different labels (Order Number, Warehouse Code, Pickup Ref, etc.)
		- Include numbers that might be confused with the primary pickup reference number
		- Include numbers from different sections of the pickup information
		- Only include numbers that are clearly pickup-related reference numbers
		- Exclude the primary refNumber from this list
		- Provide as many alternative options that fit these criteria as possible
		- EXAMPLE: If pickup section contains "Ref: 84X701802, Order Number: ORD-PU-1128, Warehouse Code: WH-RCK-001",
		  then refNumber="84X701802" and refNumberCandidates=["ORD-PU-1128", "WH-RCK-001"]

%s

%s

### **IMPORTANT: Reference Number Clarification**
Documents often contain many reference numbers. For pickup refNumber fields:
- EXTRACT: Numbers labeled as "Pickup Number", "Order Number", "Shipper Reference", or similar pickup-specific identifiers
- IGNORE: Numbers labeled as "Load ID", "BOL", "PRO" (these are shipment-level)
- IGNORE: Numbers labeled as "PO Number" (this goes in the customer poNums field)

When in doubt, choose the number that is most specific to this particular pickup stop rather than the overall shipment.

%s

%s
    `, options.Service.Name, options.Service.EmailDomains, tmsSpecificPickupTimeInstructions, tmsSpecificPickupInstructions, dateTimeNotes, tmsSpecificPickupExamples, GenericInstructions)

	// Append the historical data to the prompt if it exists.
	if len(historicalLoadData) > 0 {
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.PickupRefNumber != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s\n", load.PickupRefNumber)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`
### **Historical Data for Pattern Recognition**
The following are examples of real, confirmed pickup reference numbers. Analyze these patterns for:
1. Length: How many characters are typically used
2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
3. Prefix patterns: Common starting characters or sequences (e.g., "PU-", "PICK-")
4. Numeric patterns: Position and length of numeric sequences
5. Delimiter usage: Common separators like '-', '_', or '/'

Historical examples:
%s

Use these patterns to validate new pickup reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
`, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"pickup",
		prompt,
	)

	if err != nil {
		log.Warn(ctx, "error building custom pickup prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_pickup_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[PickupSchema](),
			PreviousResponseID: previousResponseID,
			Temperature:        openai.TemperatureDeterministic,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[PickupInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if tms.Name == models.Aljex {
		result.Pickup.Phone, err = FormatPhoneCustom(result.Pickup.Phone)
		if err != nil {
			log.WarnNoSentry(ctx, "error formatting phone number", zap.Error(err))
		}
		// Normalize ZIP code to 5 digits for Aljex (remove +4 extension)
		result.Pickup.Zipcode = helpers.NormalizeZipCode(result.Pickup.Zipcode)
	} else {

		// McLeodEnterprise: Use readyTime as fallback for appointment times if they're not set
		if tms.Name == models.McleodEnterprise {
			if !result.Pickup.ApptStartTime.Valid && result.Pickup.ReadyTime.Valid {
				result.Pickup.ApptStartTime = result.Pickup.ReadyTime
			}
		}

		tz, err := timezone.GetTimezoneByZipOrCity(
			ctx, result.Pickup.Zipcode, result.Pickup.City, result.Pickup.State, "",
		)
		if err != nil {
			result.Pickup.Timezone = ""
			log.WarnNoSentry(ctx, "error getting pickup timezone", zap.Error(err))
		} else {
			result.Pickup.Timezone = tz
			// Start time and end time are in UTC, so we need to denormalize them to the pickup's timezone
			denormalizedTime, err := timezone.DenormalizeUTC(
				result.Pickup.ApptStartTime.Time, tz)
			if err == nil {
				result.Pickup.ApptStartTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ApptEndTime.Time, tz)
			if err == nil {
				result.Pickup.ApptEndTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Pickup.ReadyTime.Time, tz)
			if err == nil {
				result.Pickup.ReadyTime.Time = denormalizedTime
			}
		}
	}

	result.Pickup.CompanyCoreInfo = ValidateStop(ctx, "pickup", result.Pickup.CompanyCoreInfo, tms.ID)

	// Check if ExternalTMSID is empty or a null character, and set it to an empty string
	//nolint:staticcheck
	if result.Pickup.CompanyCoreInfo.ExternalTMSID == "\x00" ||
		result.Pickup.CompanyCoreInfo.ExternalTMSID == "\u0000" {
		result.Pickup.CompanyCoreInfo.ExternalTMSID = ""
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// extractConsignee extracts consignee information from either an email body or attachment
func extractConsignee(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	historicalLoadData []models.LoadReferenceInfo,
	options Options,
) (result ConsigneeInfo, rawResult ConsigneeInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractConsignee", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	tmsSpecificDeliveryTimeInstructions := getPrompt(tms.Name, PromptConsigneeTimeInstructions)
	tmsSpecificDeliveryInstructions := getPrompt(tms.Name, PromptConsigneeInstructions)
	tmsSpecificDeliveryExamples := getPrompt(tms.Name, PromptConsigneeExamples)

	//nolint:lll
	prompt := fmt.Sprintf(`You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract consignee (delivery destination) details and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
	"consignee": {
		"name": "Company Name",
		"addressLine1": "Street Address",
		"addressLine2": "Suite/Unit",
		"city": "City",
		"state": "ST",
		"zipCode": "12345",
		"country": "US",
		"contact": "Contact Name",
		"phone": "************",
		"email": "<EMAIL>",
		"businessHours": "Business Hours",
		"refNumber": "Reference Number",
		"mustDeliver": "mm/dd/yyyy, hh:MMam/pm",
		"apptType": "By appointment",
		"apptStartTime": "mm/dd/yyyy, hh:MMam/pm",
		"apptEndTime": "mm/dd/yyyy, hh:MMam/pm",
		"apptNote": "Appointment notes",
		"timezone": "EDT",
		"externalTMSID": null,
		"refNumberCandidates": [
			"DEL-EC-1128",
			"CO-2024-1128"
		]
	}
}

### **Extraction Rules**
Apply these rules when extracting consignee data:

* **Consignee Location Identification**: Look for delivery information such as destination addresses, deliver to locations, or consignee details.
    * Look for sections labeled "Ship To:", "Deliver To:", "Consignee:", "Destination:", "Delivery Location:", etc.
    * **CRITICAL**: Do NOT extract "Ship From:" or origin information - only extract delivery/destination details
    * The consignee is the FINAL DELIVERY LOCATION for the shipment
    * If there are multiple stops, extract the LAST stop as the consignee
	* Consignee appointment times are associated with the dropoff/destination location, look for keywords such as "Delivery By", "Delivery Date", "Arrival By", etc.

* **Field Requirements**:
    * **name**: Company or facility name (exclude address from this field)
    * **addressLine1**: Street address
    * **addressLine2**: Suite, unit, etc. (leave empty if not specified)
    * **city**: City name
    * **state**: State code (e.g., "IL", "TX")
    * **zipCode**: Postal code
    * **country**: Country code (default to "US")
    * **contact**: Contact person name. If multiple contacts are specified, extract the first one. Note the contact is **never** from %s company or has email domains %s.
    * **phone**: Phone number (format: ************). If multiple phone numbers are specified, extract the first one.
    * **email**: Email address
    * **businessHours**: Business hours if specified
    * **refNumber**: Reference number specific to the delivery location.
        - Look for numbers labeled as "Delivery Number", "Consignee Order", "Receiver Reference", or similar delivery-specific identifiers
        - AVOID numbers labeled as "Load ID", "BOL", "PRO", or "PO Number" - these belong at other levels
        - Pay attention to document structure: numbers appearing in delivery sections, near consignee addresses, or under delivery headings are more likely to be delivery-specific
        - When multiple reference numbers are present, prioritize:
          1) Numbers explicitly labeled as delivery-specific
          2) Numbers that appear only for this delivery stop (not repeated across all stops)
          3) Numbers that are clearly delivery-specific rather than shipment-wide
	%s
    * **apptNote**: Notes about the appointment or delivery instructions
    * **timezone**: Timezone code (e.g., "EDT", "CST"). Look for timezone indicators near delivery times.
    * **externalTMSID**: Set to null
	* **refNumberCandidates**: Additional reference numbers and identifiers for the consignee location.
		- Include alternative delivery reference numbers with different labels (Delivery Ref, Customer Order, Receiver Reference, etc.)
		- Include numbers that might be confused with the primary delivery reference number
		- Include numbers from different sections of the delivery information
		- Only include numbers that are clearly delivery-related reference numbers
		- Exclude the primary refNumber from this list
		- Provide as many alternative options that fit these criteria as possible
		- EXAMPLE: If delivery section contains "Ref: 84X701803, Delivery Ref: DEL-EC-1128, Customer Order: CO-2024-1128",
		  then refNumber="84X701803" and refNumberCandidates=["DEL-EC-1128", "CO-2024-1128"]

%s

%s

### **IMPORTANT: Reference Number Clarification**
Documents often contain many reference numbers. For consignee refNumber fields:
- EXTRACT: Numbers labeled as "Delivery Number", "Consignee Order", "Receiver Reference", or similar delivery-specific identifiers
- IGNORE: Numbers labeled as "Load ID", "BOL", "PRO" (these are shipment-level)
- IGNORE: Numbers labeled as "PO Number" (this goes in the customer poNums field)

When in doubt, choose the number that is most specific to this particular delivery stop rather than the overall shipment.

%s

%s
    `, options.Service.Name, options.Service.EmailDomains, tmsSpecificDeliveryTimeInstructions, tmsSpecificDeliveryInstructions, dateTimeNotes, tmsSpecificDeliveryExamples, GenericInstructions)

	// Append the historical data to the prompt if it exists.
	if len(historicalLoadData) > 0 {
		historicalDataStr := ""
		for _, load := range historicalLoadData {
			if load.ConsigneeRefNumber != "" {
				historicalDataStr += fmt.Sprintf(" - refNumber: %s\n", load.ConsigneeRefNumber)
			}
		}

		//nolint:lll
		prompt += fmt.Sprintf(`
### **Historical Data for Pattern Recognition**
The following are examples of real, confirmed consignee/delivery reference numbers. Analyze these patterns for:
1. Length: How many characters are typically used
2. Format: Whether it uses only numbers, mix of letters/numbers, or special characters
3. Prefix patterns: Common starting characters or sequences (e.g., "DEL-", "CONS-")
4. Numeric patterns: Position and length of numeric sequences
5. Delimiter usage: Common separators like '-', '_', or '/'

Historical examples:
%s

Use these patterns to validate new consignee/delivery reference numbers against the established format. You should NOT use this historical data in your response, they are only meant to serve as a reference.
`, historicalDataStr)
	}

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		"consignee",
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom consignee prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	// Prepare reference number metadata for Braintrust
	extraMetadata := map[string]any{
		"historical_consignee_ref_numbers": historicalLoadData,
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[ConsigneeSchema](),
			PreviousResponseID: previousResponseID,
			Temperature:        openai.TemperatureDeterministic,
			ExtraMetadata:      extraMetadata,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[ConsigneeInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	if tms.Name == models.Aljex {
		// Aljex rejects phones with country codes, and FE's FormatPhoneNumber changes the number by
		// converting the country code to the leading digit.
		result.Consignee.Phone, err = FormatPhoneCustom(result.Consignee.Phone)
		if err != nil {
			log.WarnNoSentry(ctx, "error formatting phone number", zap.Error(err))
		}
		// Normalize ZIP code to 5 digits for Aljex (remove +4 extension)
		result.Consignee.Zipcode = helpers.NormalizeZipCode(result.Consignee.Zipcode)
	} else {

		if tms.Name == models.McleodEnterprise {
			// McLeodEnterprise: Use mustDeliver as fallback for appointment times if they're not set
			if !result.Consignee.ApptStartTime.Valid && result.Consignee.MustDeliver.Valid {
				result.Consignee.ApptStartTime = result.Consignee.MustDeliver
			}
		}

		tz, err := timezone.GetTimezoneByZipOrCity(ctx,
			result.Consignee.Zipcode, result.Consignee.City, result.Consignee.State, "")
		if err != nil {
			result.Consignee.Timezone = ""
			log.WarnNoSentry(ctx, "error getting consignee timezone", zap.Error(err))
		} else {
			result.Consignee.Timezone = tz
			// Start time and end time are in UTC, so we need to denormalize them to the consignee's timezone
			denormalizedTime, err := timezone.DenormalizeUTC(
				result.Consignee.ApptStartTime.Time, tz)
			if err == nil {
				result.Consignee.ApptStartTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(
				result.Consignee.ApptEndTime.Time, tz)
			if err == nil {
				result.Consignee.ApptEndTime.Time = denormalizedTime
			}

			denormalizedTime, err = timezone.DenormalizeUTC(result.Consignee.MustDeliver.Time, tz)
			if err == nil {
				result.Consignee.MustDeliver.Time = denormalizedTime
			}

		}
	}

	result.Consignee.CompanyCoreInfo = ValidateStop(ctx, "consignee", result.Consignee.CompanyCoreInfo, tms.ID)

	// Check if ExternalTMSID is empty or a null character, and set it to an empty string
	//nolint:staticcheck
	if result.Consignee.CompanyCoreInfo.ExternalTMSID == "\x00" ||
		result.Consignee.CompanyCoreInfo.ExternalTMSID == "\u0000" {
		result.Consignee.CompanyCoreInfo.ExternalTMSID = ""
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// extractSpecifications extracts specification information from either an email body or attachment
func extractSpecifications(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	attachment models.Attachment,
	tms models.Integration,
	customerID uint,
	previousResponseID string,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	options Options,
) (result SpecificationsInfo, rawResult SpecificationsInfo, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractSpecifications", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	tmsSpecificTransportTypeInstructions := getPrompt(tms.Name, PromptSpecificationsTransportTypeInstructions)

	//nolint:lll
	prompt := fmt.Sprintf(`
You are an expert logistics data extractor. Your primary task is to extract shipment specifications from the provided text and format them into a single, valid JSON object.

### **Output Format**
Your response MUST be a single JSON object that strictly follows this structure:

{
	"specifications": {
		"serviceType": "Any",
		"transportType": "VAN",
		"transportSize": "53 ft",
		"totalInPalletCount": 0,
		"commodities": "",
		"totalWeight": {
			"val": 0,
			"unit": "lb"
		},
		"billableWeight": {
			"val": 0,
			"unit": "lb"
		},
		"minTempFahrenheit": 0,
		"maxTempFahrenheit": 0,
		"totalPieces": {
			"val": 0,
			"unit": "Units"
		},
		"planningComment": ""
	}
}

### **Extraction Rules**
Apply these rules when extracting data:

* **serviceType**: Always set to "Any".

%s

* **transportSize**: Extract the vehicle size, such as "53 ft". If no size is specified, use these defaults based on transport type:
    * **VAN** or **REEFER**: Default to **"53 ft"**
    * **FLATBED**: Default to **"48 ft"**
    * All other transport types: Default to an empty string

* **totalInPalletCount**: Extract the number of pallets or crates. If none are found, default to **0**.

* **totalPieces**: Extract the total quantity/number of items/units being shipped. 
	Look for terms like "pieces", "units", "items", "qty", "quantity", "count".
	Examples: "585 PC" → 585 pieces, "24 units" → 24 units, "100 boxes" → 100 boxes.
	If unit type is specified (boxes, pieces, units, etc.), include it in the unit field.
	ALWAYS use proper casing for the unit type. ie "Boxes" not "boxes"
	If there are multiple line items with the same name/type, sum the quantities.

* **commodities**: Provide a brief description of the goods being shipped.

* **Weights**: Extract "totalWeight" and "billableWeight" and their corresponding units. Default to "lb" if a unit is not specified. If no weight value is found, default to **0**.
	* *totalWeight* is defined as the sum of all the item weights in the shipment. If the text contains multiple weight values, set *totalWeight* to the weight number mentioned in front of terms such as  "total" or "avg net weight".

* **Temperatures**: For REEFER loads, extract the minimum and maximum temperatures in Fahrenheit. The example text may provide a single temperature, in which case set both "minTempFahrenheit" and "maxTempFahrenheit" to that value. If the text uses a phrase like "Maintain X" (e.g., "Maintain -10", "Maintain 35"), set both "minTempFahrenheit" and "maxTempFahrenheit" to that single value. If no temperature is specified for a REEFER load, set both "minTempFahrenheit" and "maxTempFahrenheit" to **0**.

* **Planning Comment**: Any special instructions, handling requirements, or delivery notes


%s`, tmsSpecificTransportTypeInstructions, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tms.ID,
		customerID,
		"loadBuilding",
		models.PromptExtractorNameSpecifications,
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom specifications prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[SpecificationsSchema](),
			PreviousResponseID: previousResponseID,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[SpecificationsInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.Specifications.TransportType = string(
		validateTransportType(
			ctx, models.TransportType(result.Specifications.TransportType), userPrompt, options.Config),
	)

	// @Sophie plans to push code to introduce a solution to handle TMS specific fields.
	// For now, we conditionally set fields based on the TMS;

	switch tms.Name {
	case models.Turvo:
		turvoSpecs := turvo.BuildTurvoSpecifications(result.Specifications)
		result.Specifications = turvoSpecs
	case models.McleodEnterprise:
		// associate commodities with their Mcleod commodity code
		mcleodCommodityCode := mcleodenterprise.ToTMSCommodity(ctx, tms.Tenant, result.Specifications.Commodities)
		result.Specifications.Commodities = mcleodCommodityCode
	}

	return result, rawResult, response.BraintrustLogID, nil
}

// extracts Aljex-specific detailed commodities, additional references, and special instructions
// this function specific to aljex
func extractCommoditiesAndRefs(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	tmsObj models.Integration,
	attachment models.Attachment,
	customer models.TMSCustomer,
	previousResponseID string,
	_ string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (
	result CommoditiesAndRefsInfo,
	rawResult CommoditiesAndRefsInfo,
	braintrustLogID string,
	err error,
) {

	ctx, metaSpan := otel.StartSpan(ctx, "extractCommoditiesAndRefs", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := fmt.Sprintf(`
    You are an expert logistics data extractor. Based on the shipping information from our previous conversation, extract commodity items, additional reference numbers, and notes and format them into a single, valid JSON object.

    COMMODITIES: Extract individual commodity items with full details:
    - description: Specific product description
    - quantity: Number of units/pieces
    - weightTotal: Total weight per commodity - extract EXACTLY as mentioned in the email
      (preserve original units and values. Do not convert, round, or sum up different line items into one row)
    - length, width, height: Dimensions - extract EXACTLY as mentioned in the email
      (preserve original units and values, do not convert, round, or sum up different line items into one line item)
	- dimensionsUnit: The unit of measurement for length, width, and height. If it's not mentioned in the email, default to empty string
    - referenceNumber: Product SKU, item number, or product code

    ADDITIONAL REFERENCES: Extract all reference numbers and identifiers:
    - Bill of Lading (BOL) numbers
    - Load numbers
    - Purchase Order (PO) numbers
    - Release numbers
    - Shipment tracking numbers
    - Customer reference numbers
    - Any other identifying codes

    SPECIAL INSTRUCTIONS: Extract any special handling, pickup, or delivery instructions:
    - Temperature requirements
    - Handling instructions (fragile, hazmat, etc.)
    - Delivery requirements (liftgate, appointment, etc.)
    - Pickup instructions
    - General notes or comments

    Format your response as a JSON object with:
    {
      "commodities": [
        {
          "description": "Product description",
          "quantity": 0,
          "weightTotal": 0.0,
          "length": 0.0,
          "width": 0.0,
          "height": 0.0,
		  "dimensionsUnit": "in",
          "referenceNumber": ""
        }
      ],
      "additionalReferences": [
        {
          "qualifier": "BOL",
          "number": "1234",
        },
        {
          "qualifier": "LOAD",
          "number": "4567",
        },
        {
          "qualifier": "PO",
          "number": "7890",
        },
        {
          "qualifier": "REF",
          "number": "1234",
        }

      ],
    }

	%s
    `, GenericInstructions)

	// Get custom prompt if available
	customPrompt, err := promptBuilder.BuildPromptForExtractor(
		ctx,
		tmsObj.ID,
		customer.ID,
		"loadBuilding",
		models.PromptExtractNameCommoditiesAndRefs,
		prompt,
	)
	if err != nil {
		log.Warn(ctx, "error building custom commoditiesRefs prompt (loadBuilding)", zap.Error(err))
	} else {
		prompt = customPrompt
	}

	response, err := openaiService.GetResponse(
		ctx,
		email,
		attachment,
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt:    prompt,
			Schema:             GenerateSchema[CommoditiesAndRefsSchema](),
			PreviousResponseID: previousResponseID,
			UserPrompt:         "", // Not needed, already in conversation context
		},
	)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	result, err = StructExtractor[CommoditiesAndRefsInfo](response.Content)
	if err != nil {
		return result, rawResult, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	rawResult = result

	result.Commodities = validateAljexCommodities(ctx, &tmsObj, customer.Name, result.Commodities)

	return result, rawResult, response.BraintrustLogID, nil
}

// IsAttachmentNewLoadInformation checks if a userPrompt contains load building or quote request information
// We currently do not use this function, but it is kept here for future reference.
func IsAttachmentNewLoadInformation(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	userPrompt string,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
) (isLoadBuilding bool, braintrustLogID string, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "IsAttachmentNewLoadInformation", otel.EmailAttrs(&email))
	defer func() { metaSpan.End(err) }()

	//nolint:lll
	prompt := `
	You are an expert logistics email analyzer. Your task is to identify if this email contains load building or quote request information.

	Load building definition:
	- load building: About creating/building a new load or order with specific details
			Detailed Example Email:
				Subject: New shipment details - please create order

				Warehouse Code:
				Address:
				115-0010
				Ship To:
				5019494
				08/21/2024
				Shipping Order
				Shipping Order No.:
				Order Date:
				Customer PO:
				Ship From:
				Kronospan PB, LLC
				Kronospan PB, LLC
				1 Kronospan Way
				Eastaboga, AL United States
				MJB Anniston
				1608 Frank Akers Road
				Anniston, AL 36207
				1585 High Meadows Way
				Cedar Hill, TX 75104
				www.mjbwood.com
				************
				Page 1 of 1
				Phone No.:
				Phone No.:
				Salesperson
				Ship Mode
				Freight Terms
				VAN
				MILL
				Due Date
				09/30/2024
				Order Qty.
				Balance
				Received
				Cust. Part No.
				Description
				Footage
				Est. Weight
				Line Item Ref. No.
				Qty. / Pallet
				585
				45
				585 PC
				500544 - 5/8" x 49" x 97" PBD Raw
				Kronospan-Eastaboga PBD(95086)
				0.00 SF
				41,842.73 lbs
				Location:
				Shipping Notes:
				Total Footage:
				Total Estimated Weight:
				Carrier Name:
				Only the products that are identified as such on this document are FSC® certified.
				Unique Item Count:
				Total Qty.:
				1
				585 PC
				Carrier is to deliver to specified "Ship To" address only. If a change of delivery address is requested, please contact your
				MJB Wood Group representative.
				EPA TSCA Title VI & CARB 93120 Ph2 Compliant, for applicable products.
				Load must be 100% tarped - no exposed material. Load must be completely tarped before leaving the loading facility -
				do NOT untarp until instructed to do so by the receiver.
				0.00 SF
				41,842.73 lbs
				Total Pallets:
				13

	Quote request definition:
	- quote request: Explicitly requests pricing for shipping services
		Detailed Example Email:
			Subject: Need rate for Orlando to Miami shipment ASAP

					Hello,

					We are looking for a competitive rate for the following shipment:

					Origin: Orlando, FL
					Destination: Miami, FL
					Pickup Date: July 25, 2023
					Delivery Date: July 26, 2023
					Commodity: Electronics (palletized)
					Weight: 5,000 lbs
					Dimensions: 4 pallets, 48"x40"x50" each
					Special Requirements: Liftgate needed at delivery

					Can you please provide your best rate for this lane? We need to book this shipment by tomorrow.

					Thank you,
					Procurement Team



    Answer with ONLY "YES" if this is a new load or quote request document or "NO" if it is not.
    `

	response, err := openaiService.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: prompt,
			UserPrompt:      userPrompt,
			// TODO: Enable temperature for all extraction prompts when we have the time to test it.
			// Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return false, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	return strings.Contains(strings.ToUpper(response.Content), "YES"), response.BraintrustLogID, nil
}

func validateAljexCommodities(
	ctx context.Context,
	tmsObj *models.Integration,
	customerName string,
	items []models.Commodity,
) (result []models.Commodity) {
	_, metaSpan := otel.StartSpan(ctx, "validateAljexCommodities", otel.IntegrationAttrs(*tmsObj))
	defer func() { metaSpan.End(nil) }()

	if tmsObj.Name != models.Aljex || !strings.EqualFold(tmsObj.Tenant, "abts") {
		return items
	}

	customerName = strings.ToLower(customerName)
	if !strings.Contains(customerName, "ptr") && !strings.Contains(customerName, "premier truck") {
		return items
	}

	// Because of Aljex's stringent character limits, Able has a unique way of circumventing them:
	// they list the dimensions unit (ft, in) in the commodity.ReferenceNumber aka ProductCode field.
	// Then they add dummy rows to complete the description.
	// See load 112827 for an example, and https://shorturl.at/cgoRb at 9-min mark
	const descriptionCharLimit = 40

	for _, item := range items {
		originalDescription := item.Description
		originalReferenceNumber := item.ReferenceNumber

		newDescription := originalDescription
		if !strings.Contains(originalDescription, originalReferenceNumber) {
			newDescription += "," + originalReferenceNumber
		}

		if len(strings.TrimSpace(newDescription)) <= descriptionCharLimit {
			newItem := item
			newItem.ReferenceNumber = item.DimensionsUnit
			newItem.Description = newDescription
			result = append(result, newItem)

			continue
		}

		splitDescriptions := strings.Split(newDescription, ",")
		for i, splitDescription := range splitDescriptions {
			if i == 0 {
				newItem := item
				newItem.ReferenceNumber = item.DimensionsUnit
				newItem.Description = splitDescription
				result = append(result, newItem)
			} else {
				newItem := models.Commodity{
					Description: splitDescription,
				}
				result = append(result, newItem)
			}
		}
	}

	return result
}

// normalizeRateType validates and normalizes the rate type to one of the allowed values
// Returns the normalized rate type or defaults to "Flat" if invalid
func normalizeRateType(ctx context.Context, rateType string) string {
	rateType = strings.TrimSpace(rateType)
	reNonAlpha := regexp.MustCompile(`[^A-Za-z]+`)
	rateType = reNonAlpha.ReplaceAllString(rateType, "")
	rateLower := strings.ToLower(rateType)

	validTypes := []string{"Flat", "Distance", "CWT", "Tons"}
	for _, valid := range validTypes {
		if strings.EqualFold(rateType, valid) {
			return valid
		}
	}

	// This handles cases like "FlatA", "Flat rate", "Distance-based", etc.
	if strings.Contains(rateLower, "distance") {
		return "Distance"
	}
	if strings.Contains(rateLower, "cwt") || strings.Contains(rateLower, "hundredweight") {
		return "CWT"
	}
	if strings.Contains(rateLower, "ton") {
		return "Tons"
	}
	if strings.Contains(rateLower, "flat") {
		return "Flat"
	}

	log.WarnNoSentry(
		ctx,
		"invalid rate type received from OpenAI, defaulting to Flat",
		zap.String("receivedRateType", rateType))
	return "Flat"
}
