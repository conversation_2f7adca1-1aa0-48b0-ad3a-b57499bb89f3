package extractor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"strings"

	"github.com/invopop/jsonschema"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/docproc"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

var (
	TridentServiceID      = uint(562)
	TridentCustomers      = []string{"MJB", "ARGO"}
	FetchFreightCustomers = []string{
		// These are the same customer
		"Steel Equipment Specialists",
		"SES, LLC",
		"seseng",
	}
	AbleCustomers = []string{
		// Same customer
		"ISCO",
		"ISCO Industries",
	}
)

// extractDomain extracts the domain from an email address.
// e.g. "<EMAIL>" -> "example.com"
func extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 1 {
		return parts[1]
	}

	return ""
}

// ExtractSecondLevelDomain extracts the second-level domain from an email address or domain string.
// FYI, where "LD" stands for "level domain", format for domains is {3rdLD}.{2ndLD}.{1stLD(s)},
// e.g. app.drumkit.ai or app.company.co.uk. For these examples, function returns "drumkit" and "company".
// See tests for cases supported. If the domain is not recognized, returns "".
func ExtractSecondLevelDomain(input string) string {
	input = strings.TrimPrefix(input, "@")

	parts := strings.Split(input, "@")
	domain := input
	if len(parts) == 2 {
		domain = parts[1]
	}

	domainParts := strings.Split(domain, ".")
	n := len(domainParts)

	if n < 2 {
		return ""
	}

	// Simple case: domain.com - return the first part
	if n == 2 {
		return domainParts[0]
	}

	// At this point, n > 2 and could be sub.domain.com, domain.co.uk, or sub.domain.co.uk, etc.
	// We'll use a heuristic based on common patterns:
	// 1. Most multi-part TLDs have exactly 2 parts (co.uk, com.au)
	// 2. Most multi-part TLDs have a short first part (2-3 chars like "co", "com", "org")

	secondToLastPart := domainParts[n-2]

	// If the second-to-last part is very short (2-3 chars), it's likely part of a multi-part TLD
	// Examples: co.uk, com.au, ac.uk, etc.
	if len(secondToLastPart) <= 3 && n >= 3 {
		// This is likely domain.co.uk pattern, return the part before the multi-part TLD
		return domainParts[n-3]
	}

	// Otherwise, it's likely a sub.domain.com pattern
	return domainParts[n-2]
}

// isInsufficientAddressData checks if the address data is insufficient to create a new load suggestion.
// NOTE: We must adhere to the common denominator between LB and QR suggestions,
// which is a minimum of zipcode OR city & state.
func IsInsufficientAddressData(addr models.CompanyCoreInfo) bool {
	if addr.Zipcode != "" {
		return false // Zipcode present = sufficient
	}
	return addr.City == "" || addr.State == "" // Need both city AND state if no zipcode
}

func ValidateStop(
	ctx context.Context,
	stopType string,
	stop models.CompanyCoreInfo,
	tmsID uint,
) models.CompanyCoreInfo {
	// Handle when LLM is silly
	if stop.AddressLine1 == fmt.Sprintf("%s, %s", stop.City, stop.State) {
		stop.AddressLine1 = ""
	}

	// NOTE: For Trident Mcleod Enterprise, pickup *is* required to have a TMS object ID
	if tmsID > 0 {
		mappedLocation, err := tmsLocationDB.GetLocationByAddress(ctx, tmsID, stop)
		if err == nil && mappedLocation.ID > 0 {
			return mappedLocation.CompanyCoreInfo
		}
		log.WarnNoSentry(
			ctx,
			fmt.Sprintf("unable to match %s location to TMS location by address", stopType),
			zap.Error(err),
		)
	}

	// If LLM parsed address line and city, assume it's correct
	if stop.AddressLine1 != "" {
		return stop
	}

	// New Load pipeline is responsible for both load building and quote requests
	// If LLM parsed only city/state and/or zip, we validate zip to avoid hallucinations
	validateZip(ctx, &stop)

	return stop
}

func validateZip(ctx context.Context, loc *models.CompanyCoreInfo) {
	if helpers.IsBlank(loc.Zipcode) {
		return // Zip is optional; assume that LLM parsed city, state correctly
	}

	// Normalize ZIP code to 5 digits (remove +4 extension if present)
	loc.Zipcode = helpers.NormalizeZipCode(loc.Zipcode)

	location, err := helpers.AwsLocationLookup(ctx, "", "", loc.Zipcode)
	if err != nil {
		log.WarnNoSentry(ctx, "validateZip: error looking up zip", zap.Error(err))
		return
	}

	if len(location.Results) == 0 || location.Results[0].Place == nil {
		log.WarnNoSentry(ctx, "validateZip:no location found for zip", zap.String("zip", loc.Zipcode))
		// If no location found, clear zipcode to avoid hallucinations and fallback to city, state
		// If both pickup & dropoff are missing zipcode, caller will skip QR suggestion
		loc.Zipcode = ""

		return
	}

	placeRes := location.Results[0].Place
	if placeRes.Municipality != nil && *placeRes.Municipality != loc.City {
		log.WarnNoSentry(ctx, "LLM hallucinated city, using AWS result",
			zap.String("llmCity", loc.City),
			zap.String("awsCity", *placeRes.Municipality),
		)

		loc.City = *placeRes.Municipality
	}

	if placeRes.Region != nil {
		stateAbbrv := helpers.GetStateAbbreviation(ctx, *placeRes.Region)
		if stateAbbrv == "Unknown State" {
			stateAbbrv = loc.State
		}

		if loc.State != stateAbbrv {
			log.WarnNoSentry(ctx, "LLM hallucinated state, using AWS result",
				zap.String("llmState", loc.State),
				zap.String("awsState", *placeRes.Region),
			)
			loc.State = stateAbbrv
		}
	}
}

// MapCustomer maps the extracted customer name to a TMSCustomer from the database
// handling both email-based and attachment-based extraction scenarios
func MapCustomer(
	ctx context.Context,
	tmsID uint,
	parsedName string,
	userPrompt string,
) (mappedCustomer *models.TMSCustomer, err error) {

	if tmsID == 0 {
		return nil, errors.New("unable to map customer, TMS ID is 0")
	}

	var name string

	// Check email & attachment data for hard coded edge case customers
	edgeCaseCustomers := slices.Concat(FetchFreightCustomers, TridentCustomers, AbleCustomers)

	for _, customer := range edgeCaseCustomers {
		check := checkForStringInUserPrompt(
			customer, userPrompt,
		)

		if check {
			switch customer {
			case "MJB":
				name = "Liberty Woods International"
			case "ARGO":
				name = "ARGO Fine Imports"
			case "Steel Equipment Specialists", "SES, LLC", "seseng":
				name = "SES"

			// 'ISCO' is correctly mapped to 'ISCO Industries' by ILIKE query, but 'ISCO Industries' is
			// incorrectly mapped to 'SWARCO Industries' by DB fuzzy lookup. SuggestionEnhancer would probably correct
			// this eventually, but since we know this is a special case, we handle it explicitly here.
			case "ISCO", "ISCO Industries":
				name = "ISCO"

			}
			break
		}
	}

	if name == "" {
		name = parsedName
	}

	// If we still don't have a name, we can't map to a customer
	if name == "" {
		return nil, errors.New("no customer name found for mapping")
	}

	dbCustomer, err := tmsCustomerDB.GetCustomerByName(ctx, tmsID, name)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, tmsCustomerDB.ErrCustomerNotFound) {
			log.WarnNoSentry(ctx, "customer not found in database", zap.String("name", name))
			return nil, nil
		}
		return nil, fmt.Errorf("db error getting customer: %w", err)
	}

	return &dbCustomer, nil
}

// validateTransportType normalizes and validates the given transport type.
// It converts the input to uppercase and matches it against known transport types.
// If the input doesn't match any known type, it defaults to VAN and logs a warning.
func validateTransportType(
	ctx context.Context,
	input models.TransportType,
	userPrompt string,
	config models.QuickQuoteConfig,
) models.TransportType {

	// Check for special equipment types in email body/attachment content if any
	for _, equipment := range config.SpecialEquipment {
		if check := checkForStringInUserPrompt(
			equipment, userPrompt,
		); check {
			return models.SpecialTransportType
		}
	}

	if helpers.IsBlank(string(input)) {
		return models.VanTransportType
	}

	validTypes := models.ListLoadBuildingTransportTypes()
	for _, otherType := range config.OtherTransportTypes {
		// Add other transport types to validTypes if not already in validTypes
		if !slices.Contains(validTypes, models.TransportType(otherType)) {
			validTypes = append(validTypes, models.TransportType(otherType))
		}
	}

	normalizedInput := strings.ToUpper(string(input))
	normalizedInput = strings.Join(strings.Fields(normalizedInput), " ") // Remove extra spaces
	// Handle case where LLM input is "HOT SHOT" and validType is "HOTSHOT"
	normalizedInput = strings.ReplaceAll(normalizedInput, "HOT SHOT", "HOTSHOT")

	if slices.Contains(validTypes, models.TransportType(normalizedInput)) {
		return models.TransportType(normalizedInput)
	}

	// If no exact match, try partial matching
	for _, validType := range validTypes {
		if strings.Contains(normalizedInput, strings.ToUpper(string(validType))) ||
			// Handle case where LLM input is "BOX" and validType is "BOX TRUCK"
			strings.Contains(string(validType), normalizedInput) {
			return validType
		}
	}

	log.WarnNoSentry(ctx,
		"unknown transport type, defaulting to Van",
		zap.String("input", string(input)),
		zap.String("normalized", normalizedInput),
	)
	return models.VanTransportType
}

// checkForStringInUserPrompt checks if a string is present in the user prompt
func checkForStringInUserPrompt(
	str string,
	userPrompt string,
) bool {
	if userPrompt == "" || str == "" {
		return false
	}

	// do string matching with regex
	regex := regexp.MustCompile(fmt.Sprintf("(?i)\\b%s\\b", regexp.QuoteMeta(str)))
	return regex.MatchString(userPrompt)
}

// checkForSeparateRateConCustomersInUserPrompt checks if any of the strings in SeparateRateConCustomers
// is present in the user prompt
func checkForRateConCustomerInUserPrompt(userPrompt string) bool {
	if userPrompt == "" {
		return false
	}

	for _, customer := range SeparateRateConCustomers {
		if checkForStringInUserPrompt(customer, userPrompt) {
			return true
		}
	}
	return false
}

// StructExtractor attempts to unmarshal a JSON response into a struct.
// If the response is not valid JSON, it will attempt to extract JSON from the response.
func StructExtractor[T any](response string) (T, error) {
	var result T

	// Try to unmarshal directly first
	err := json.Unmarshal([]byte(response), &result)
	if err != nil {
		// If direct unmarshal fails, try extracting JSON from the response
		jsonStr, err := ExtractJSONFromText(response)
		if err != nil {
			return result, fmt.Errorf("failed to extract JSON: %w", err)
		}

		err = json.Unmarshal([]byte(jsonStr), &result)
		if err != nil {
			return result, fmt.Errorf("failed to unmarshal JSON: %w", err)
		}
	}

	return result, nil
}

// MergeRateConfirmationSuggestions merges the rate confirmation suggestion with the main suggestion.
// Context: Load Building pipeline assumes max 1 attachment per shipment.
// However, shippers often send multiple documents (usually BOL + Rate Confirmation) that brokers then synthesize
// into one shipment (e.g. Trident with MJB rate cons, Able with Premier Truck Rental (PTR) rate cons).
// This function merges the rate confirmation suggestion with the main suggestion.
// TODO: ENG-4196
func MergeRateConfirmationSuggestions(
	ctx context.Context,
	mainSugs []models.SuggestedLoadChange,
	// Suggestion from rate confirmation document, usually less detailed than the main suggestion
	rateConSugs map[string]*models.SuggestedLoadChange,
) []models.SuggestedLoadChange {
	ctx, metaSpan := otel.StartSpan(ctx, "MergeRateConfirmationSuggestions", nil)
	defer func() { metaSpan.End(nil) }()

	if len(rateConSugs) == 0 {
		return mainSugs
	}

	log.Infof(ctx, "found rate confirmations", zap.Int("count", len(rateConSugs)))
	var newSugs []models.SuggestedLoadChange

	for i := range mainSugs {
		sug := mainSugs[i].Suggested.LoadChanges
		originalDoc := mainSugs[i].S3Attachment

		if sug == nil {
			continue
		}

		// If found, map the rate confirmation data to the suggested load data
		if rateConSug, ok := rateConSugs[sug.Customer.RefNumber]; ok {
			rateCon := rateConSug.Suggested.LoadChanges.RateData

			log.Infof(
				ctx,
				"matched rate confirmation for %s",
				sug.Customer.RefNumber,
				zap.String("originalDoc", originalDoc.OriginalFileName),
				zap.String("matchedRateConDoc", rateConSug.S3Attachment.OriginalFileName),
			)

			sug.RateData.CustomerLineHaulRate = helpers.Or(
				rateCon.CustomerLineHaulRate, sug.RateData.CustomerLineHaulRate,
			)
			sug.RateData.CustomerLineHaulCharge = helpers.Or(
				rateCon.CustomerLineHaulCharge, sug.RateData.CustomerLineHaulCharge,
			)
			sug.RateData.CustomerTotalCharge = helpers.Or(
				rateCon.CustomerTotalCharge, sug.RateData.CustomerTotalCharge,
			)
			sug.RateData.CustomerRateType = helpers.Or(
				rateCon.CustomerRateType, sug.RateData.CustomerRateType,
			)

			mainSugs[i].Suggested.LoadChanges = sug
		}
	}

	result := slices.Concat(mainSugs, newSugs)
	return result
}

// ExtractJSONFromText attempts to extract JSON content from a potentially larger text block
func ExtractJSONFromText(text string) (string, error) {
	// First, clean up the text by trimming whitespace
	trimmedText := strings.TrimSpace(text)

	// Try to extract from code blocks first
	if jsonFromBlock, found := extractFromCodeBlock(trimmedText); found {
		return jsonFromBlock, nil
	}

	// Find the JSON start position and type
	startPos, startChar, endChar, err := findJSONStart(trimmedText)
	if err != nil {
		return "", err
	}

	// Extract the JSON content by finding the matching end character
	jsonContent, err := extractJSONContent(trimmedText, startPos, startChar, endChar)
	if err != nil {
		return "", err
	}

	// Validate the extracted JSON
	if err := validateJSON(jsonContent); err != nil {
		return "", err
	}

	return jsonContent, nil
}

// extractFromCodeBlock extracts JSON from markdown code blocks if present
func extractFromCodeBlock(text string) (string, bool) {
	if strings.HasPrefix(text, "```json") && strings.HasSuffix(text, "```") {
		// Extract the JSON content from the code block
		jsonContent := text[7:]                        // Skip the ```json prefix
		jsonContent = jsonContent[:len(jsonContent)-3] // Remove the closing ```
		return strings.TrimSpace(jsonContent), true
	}

	if strings.HasPrefix(text, "```") && strings.HasSuffix(text, "```") {
		// Generic code block
		jsonContent := text[3:]                        // Skip the ``` prefix
		jsonContent = jsonContent[:len(jsonContent)-3] // Remove the closing ```
		return strings.TrimSpace(jsonContent), true
	}

	return "", false
}

// findJSONStart locates the beginning of a JSON object or array in the text
func findJSONStart(text string) (startPos int, startChar byte, endChar byte, err error) {
	startIdxBrace := strings.Index(text, "{")
	startIdxBracket := strings.Index(text, "[")

	if startIdxBrace != -1 && (startIdxBracket == -1 || startIdxBrace < startIdxBracket) {
		// JSON object found
		return startIdxBrace, '{', '}', nil
	}

	if startIdxBracket != -1 {
		// JSON array found
		return startIdxBracket, '[', ']', nil
	}

	return -1, 0, 0, fmt.Errorf("no JSON object or array found in text: %s", text)
}

// extractJSONContent parses the text to find the matching end character for the JSON
func extractJSONContent(text string, startPos int, startChar byte, endChar byte) (string, error) {
	bracketCount := 1
	inString := false
	escaped := false

	for i := startPos + 1; i < len(text); i++ {
		// Handle escape sequences
		if escaped {
			escaped = false
			continue
		}

		c := text[i]

		// Handle string literals
		if c == '\\' && !escaped {
			escaped = true
			continue
		}

		if c == '"' && !escaped {
			inString = !inString
			continue
		}

		// Only count brackets outside of string literals
		if !inString {
			switch c {
			case startChar:
				bracketCount++
			case endChar:
				bracketCount--
				if bracketCount == 0 {
					// Found matching bracket - extract the JSON string
					return text[startPos : i+1], nil
				}
			}
		}
	}

	return "", errors.New("no valid JSON object found: unclosed brackets")
}

// validateJSON ensures the extracted JSON content is valid
func validateJSON(jsonStr string) error {
	var js any
	if err := json.Unmarshal([]byte(jsonStr), &js); err != nil {
		return fmt.Errorf("extracted invalid JSON: %w", err)
	}
	return nil
}

// GenerateSchema generates a JSON schema for a given struct type
func GenerateSchema[T any]() map[string]any {
	// Structured Outputs uses a subset of JSON schema
	// These flags are necessary to comply with the subset
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	var v T
	schema := reflector.Reflect(v)

	// Convert schema to map[string]any format
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		return map[string]any{}
	}
	var schemaMap map[string]any
	err = json.Unmarshal(schemaBytes, &schemaMap)
	if err != nil {
		return map[string]any{}
	}

	return schemaMap
}

// FormatPhoneCustom formats a phone number as: "************" (US format)
// Aljex rejects phones with country codes, and FE's FormatPhoneNumber changes the number by
// converting the country code to the leading digit.
func FormatPhoneCustom(phoneStr string) (string, error) {
	if helpers.IsBlank(phoneStr) {
		return "", nil
	}

	regex := regexp.MustCompile(`(\d{3})(?:[^a-zA-Z0-9]*)?(\d{3})(?:[^a-zA-Z0-9]*)?(\d{4})`)
	matches := regex.FindStringSubmatch(phoneStr)
	if len(matches) == 0 {
		return "", fmt.Errorf("invalid phone number: %s", phoneStr)
	}
	return fmt.Sprintf("%s-%s-%s", matches[1], matches[2], matches[3]), nil
}

// PDFToMarkdownWithFallbacks attempts to convert a PDF to markdown using multiple methods with fallbacks.
// It tries in order: docproc.PDFToMD -> Zerox -> docproc.PDFToMDViaHTML.
// Returns the markdown content (string) and an error if all methods fail.
//
// Parameters:
//   - openaiService: OpenAI service for vision-based PDF conversion (can be nil to skip docproc.PDFToMD)
//   - fileData: Raw PDF file bytes
//   - fileName: Original filename for logging purposes
func PDFToMarkdownWithFallbacks(
	ctx context.Context,
	openaiService openai.Service,
	fileData []byte,
	fileName string,
) (string, error) {
	// 1. First try PDFToMD (our internal replacement for Zerox)
	if openaiService != nil {
		markdown, err := docproc.PDFToMD(ctx, openaiService, fileData,
			docproc.WithFileName(fileName),
		)
		// If successful, return the markdown
		if err == nil && strings.TrimSpace(markdown) != "" {
			log.Debug(ctx, "successfully converted PDF using docproc PDFToMD", zap.String("fileName", fileName))

			return markdown, nil
		}
		log.Warn(
			ctx,
			"docproc PDFToMD failed, falling back to Zerox",
			zap.String("fileName", fileName),
			zap.Error(err),
		)
	}

	// 2. Try Zerox
	// TODO: Deprecate Zerox (remove from `drumkit` and `cyclops` repos once docproc is considered stable)
	markdown, err := GetZeroxMarkdown(ctx, fileName, fileData)
	// If successful, return the markdown
	if err == nil && strings.TrimSpace(markdown) != "" {
		log.Debug(ctx, "successfully converted PDF using Zerox", zap.String("fileName", fileName))

		return markdown, nil
	}
	log.WarnNoSentry(
		ctx,
		"Zerox conversion failed, falling back to GoFitz",
		zap.String("fileName", fileName),
		zap.Error(err),
	)

	// 3. Try GoFitz pdf->html->markdown method as fallback
	markdown, err = docproc.PDFToMDViaHTML(fileData)
	// If successful, return the markdown
	if err == nil && strings.TrimSpace(markdown) != "" {
		log.Debug(ctx, "successfully converted PDF using GoFitz", zap.String("fileName", fileName))

		return markdown, nil
	}
	log.WarnNoSentry(
		ctx,
		"GoFitz conversion failed, all PDF conversion methods exhausted",
		zap.String("fileName", fileName),
		zap.Error(err),
	)

	return "", fmt.Errorf("all PDF conversion methods failed for file: %s", fileName)
}
