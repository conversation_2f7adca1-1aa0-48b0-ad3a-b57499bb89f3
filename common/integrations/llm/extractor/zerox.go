package extractor

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

// Default max number of Markdown pages to return from Zerox
const MaxNumPages = 5

type (
	MarkdownOptions struct {
		// Since GetZeroxMarkdown is used for verifying email labels,
		// we need to allow a limit on the number of pages we convert to markdown.
		// Otherwise, the LLM will struggle to handle the request.
		NumPages int
	}

	ZeroxRequest struct {
		File         []byte `json:"file"`
		Model        string `json:"model"`
		CustomPrompt string `json:"custom_prompt"`
	}

	ZeroxResponse struct {
		Success bool        `json:"success"`
		Message string      `json:"message"`
		Result  ZeroxResult `json:"result"`
	}

	ZeroxResult struct {
		CompletionTime float64     `json:"completion_time"`
		FileName       string      `json:"file_name"`
		InputTokens    int         `json:"input_tokens"`
		OutputTokens   int         `json:"output_tokens"`
		Pages          []ZeroxPage `json:"pages"`
	}

	ZeroxPage struct {
		Content       string `json:"content"`
		ContentLength int    `json:"content_length"`
		Page          int    `json:"page"`
	}
)

// GetZeroxMarkdown converts a file attachment to markdown using the Zerox service.
// The following document types are accepted by the extract endpoints as either a file buffer or URL:
// .pdf, .doc, .docx, .png, .jpg, .jpeg, .tiff, .odt, .ott, .rtf, .txt, .html, .htm, .xml, .wps, .wpd,
// .ods, .ots, .ppt, .pptx, .odp, .otp
func GetZeroxMarkdown(
	ctx context.Context,
	fileName string,
	fileData []byte,
	options ...MarkdownOptions,
) (string, error) {

	cyclopsAddress := os.Getenv("CYCLOPS_URL")
	if cyclopsAddress == "" {
		return "", errors.New("missing cyclops ec2 address")
	}

	return processFileWithZerox(ctx, fileData, fileName, cyclopsAddress, options...)
}

// processFileWithZerox sends the file to the Zerox API and returns the markdown content
func processFileWithZerox(
	ctx context.Context,
	fileData []byte,
	fileName string,
	cyclopsAddress string,
	options ...MarkdownOptions,
) (string, error) {

	requestBody, contentType, err := createMultipartForm(fileData, fileName, options...)
	if err != nil {
		return "", fmt.Errorf("failed to create multipart form: %w", err)
	}

	log.Debug(ctx, "sending zerox request")

	response, err := sendZeroxRequest(ctx, requestBody, contentType, cyclopsAddress)
	if err != nil {
		return "", err
	}

	if len(response.Result.Pages) == 0 {
		return "", errors.New("no pages returned from Zerox API")
	}

	log.Info(
		ctx,
		fmt.Sprintf("zerox conversion succeeded with %d pages", len(response.Result.Pages)),
		zap.Any("response", response),
	)

	// If numPages is provided in options, use it to limit the number of pages returned
	var numPages int
	if len(options) == 0 || options[0].NumPages == 0 {
		numPages = MaxNumPages
	} else {
		if options[0].NumPages > MaxNumPages {
			log.Warn(
				ctx,
				"num pages provided exceeds max allowed, using max allowed",
				zap.Int("maxNumPages", MaxNumPages),
				zap.Int("numPages", options[0].NumPages),
			)

			numPages = MaxNumPages
		} else {
			numPages = options[0].NumPages
		}
	}

	if numPages > len(response.Result.Pages) {
		log.Info(
			ctx,
			"num pages exceeds the number of pages in the document, using the number of pages in the document",
			zap.Int("numPages", numPages),
			zap.Int("numPagesInDocument", len(response.Result.Pages)),
		)

		numPages = len(response.Result.Pages)
	}

	// Concatenate the content of the pages
	concatenatedContent := ""
	for _, page := range response.Result.Pages[:numPages] {
		concatenatedContent += page.Content
		concatenatedContent += "\n\n"
	}

	return concatenatedContent, nil
}

// createMultipartForm creates a multipart form for the Zerox API request
func createMultipartForm(fileData []byte, fileName string, options ...MarkdownOptions) (*bytes.Buffer, string, error) {
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	fileField, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create form file: %w", err)
	}
	if _, err = io.Copy(fileField, bytes.NewReader(fileData)); err != nil {
		return nil, "", fmt.Errorf("failed to copy file: %w", err)
	}

	if err = writer.WriteField("model", "gpt-4o-mini"); err != nil {
		return nil, "", fmt.Errorf("failed to write model field: %w", err)
	}

	// Add numPages field if provided in options
	if len(options) > 0 && options[0].NumPages > 0 {
		if err = writer.WriteField("num_pages", fmt.Sprintf("%d", options[0].NumPages)); err != nil {
			return nil, "", fmt.Errorf("failed to write num_pages field: %w", err)
		}
	}

	contentType := writer.FormDataContentType()
	if err = writer.Close(); err != nil {
		return nil, "", fmt.Errorf("failed to close multipart writer: %w", err)
	}

	return &requestBody, contentType, nil
}

// sendZeroxRequest sends the request to the Zerox API and parses the response
func sendZeroxRequest(
	ctx context.Context,
	requestBody *bytes.Buffer,
	contentType, cyclopsAddress string,
) (*ZeroxResponse, error) {
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/zerox/markdown", cyclopsAddress),
		requestBody,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create zerox request: %w", err)
	}
	req.Header.Set("Content-Type", contentType)

	client := otel.TracingHTTPClient(60 * time.Second)
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make zerox request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read zerox response body: %w", err)
	}

	var zeroxResponse ZeroxResponse
	if err = json.Unmarshal(body, &zeroxResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal zerox response: %w", err)
	}

	if !zeroxResponse.Success {
		return nil, fmt.Errorf("zerox API error: %s", zeroxResponse.Message)
	}

	return &zeroxResponse, nil
}
