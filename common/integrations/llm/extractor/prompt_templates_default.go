package extractor

//nolint:lll
const (
	defaultPickupTimeInstructions = `* **readyTime**: ONLY set when explicitly stated as "ready time", "ready date", "available from", etc. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from appointment times.
    * **apptType**: "By appointment", "FCFS", or appointment type
    * **apptStartTime**: ONLY set when there is an explicit appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from ready times.
    * **apptEndTime**: ONLY set when there is an explicit appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from ready times.`

	defaultPickupInstructions = ``

	defaultPickupExamples = `**Examples:**

### Example 1: Standard Pickup with Appointment
**Input:**
A shipping document contains:
SHIP FROM:
Mohawk Industries
1405 HWY 41 S
CALHOUN, GA 30701
Contact: <PERSON>
Phone: ************
Email: <EMAIL>
Ready Time: 08/26/2025, 8:00AM
Appointment Required: 9:00AM-10:00AM EDT
Special Instructions: Driver must check in at front desk

**Output:**
{
    "pickup": {
        "name": "Mohawk Industries",
        "addressLine1": "1405 HWY 41 S",
        "addressLine2": "",
        "city": "CALHOUN",
        "state": "GA",
        "zipCode": "30701",
        "country": "US",
        "contact": "Sarah Johnson",
        "phone": "************",
        "email": "<EMAIL>",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/26/2025, 08:00AM",
        "apptType": "By appointment",
        "apptStartTime": "08/26/2025, 09:00AM",
        "apptEndTime": "08/26/2025, 10:00AM",
        "apptNote": "Driver must check in at front desk",
        "timezone": "EDT",
		"refNumberCandidates": []
    }
}

### Example 2: Multiple Reference Numbers
**Input:**
Stop 1 (pickup)
Mobil, 1001 Billingsport Rd., Paulsboro, NJ 08066
Leah Scalise Phone: (*************
Pickup: 09/12/2025 07:00AM - 09/12/2025 03:00PM

- SN834918 (BOL)
- 2608089680 (Delivery/Order Number)  
- 4700829 (PO Number)
- LD385193 (Load ID)
- LD385193 (PRO)

**Output:**
{
    "pickup": {
        "name": "Mobil",
        "addressLine1": "1001 Billingsport Rd.",
        "addressLine2": "",
        "city": "Paulsboro",
        "state": "NJ",
        "zipCode": "08066",
        "country": "US",
        "contact": "Leah Scalise",
        "phone": "************",
        "email": "",
        "businessHours": "",
        "refNumber": "2608089680",
        "readyTime": "09/12/2025, 07:00AM",
        "apptType": "By appointment",
        "apptStartTime": "09/12/2025, 07:00AM",
        "apptEndTime": "09/12/2025, 03:00PM",
        "apptNote": "",
        "timezone": "",
		"refNumberCandidates": [
			"2608089680",
			"4700829",
			"LD385193"
		]
    }
}

### Example 3: Multiple Locations - Extract Origin Only
**Input:**
SHIP FROM:
Origin Warehouse LLC
500 Commerce Dr
Atlanta, GA 30309
Ready: 08/28/2025, 2:00PM

SHIP TO:
Destination Corp
789 Delivery St
Miami, FL 33101
Deliver by: 08/30/2025

**Output:**
{
    "pickup": {
        "name": "Origin Warehouse LLC",
        "addressLine1": "500 Commerce Dr",
        "addressLine2": "",
        "city": "Atlanta",
        "state": "GA",
        "zipCode": "30309",
        "country": "US",
        "contact": "",
        "phone": "",
        "email": "",
        "businessHours": "",
        "refNumber": "",
        "readyTime": "08/28/2025, 02:00PM",
        "apptType": "",
        "apptStartTime": "08/28/2025, 02:00PM",
        "apptEndTime": "",
        "apptNote": "",
        "timezone": "",
		"refNumberCandidates": []
    }
}
`

	defaultConsigneeTimeInstructions = `* **mustDeliver**: ONLY set when explicitly stated as "must deliver by", "delivery deadline", "deliver no later than", etc. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from appointment times.
	    * **apptType**: "By appointment", "FCFS", or appointment type
	    * **apptStartTime**: ONLY set when there is an explicit delivery appointment or scheduled window start time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from must-deliver times.
	    * **apptEndTime**: ONLY set when there is an explicit delivery appointment or scheduled window end time. Format as "mm/dd/yyyy, hh:MMam/pm" or just "mm/dd/yyyy". Do NOT infer from must-deliver times.
	`
	defaultConsigneeInstructions = ``

	defaultConsigneeExamples = `**Examples:**

	### Example 1: Standard Delivery with Appointment
	**Input:**
	A shipping document contains:
	SHIP TO:
	Acme Distribution Center
	123 Main St, Suite 400
	Springfield, IL 62704
	Contact: John Doe
	Phone: ************
	Email: <EMAIL>
	Business Hours: Mon-Fri, 8:00 AM - 5:00 PM
	Delivery Appointment: 08/30/2025, 1:00PM-3:00PM CST
	Special Instructions: Call 30 minutes before arrival
	Delivery PO: DEL-987654
	Main Shipment BOL: BOL-555123

	**Output:**
	{
		"consignee": {
			"name": "Acme Distribution Center",
			"addressLine1": "123 Main St",
			"addressLine2": "Suite 400",
			"city": "Springfield",
			"state": "IL",
			"zipCode": "62704",
			"country": "US",
			"contact": "John Doe",
			"phone": "************",
			"email": "<EMAIL>",
			"businessHours": "Mon-Fri, 8:00 AM - 5:00 PM",
			"refNumber": "DEL-987654",
			"mustDeliver": "08/30/2025, 03:00PM",
			"apptType": "By appointment",
			"apptStartTime": "08/30/2025, 01:00PM",
			"apptEndTime": "08/30/2025, 03:00PM",
			"apptNote": "Call 30 minutes before arrival",
			"timezone": "CST",
			"externalTMSID": null,
			"refNumberCandidates": [
				"DEL-987654"
			]
		}
	}

	### Example 2: Multiple Reference Numbers
	**Input:**
	Stop 2 (drop)
	Moove, 8120 S. Orange Avenue, Orlando, FL 32809
	Mike Dvorak Phone: ************
	Delivery: 09/15/2025 07:00AM - 09/15/2025 03:00PM

	- SN834918 (BOL)
	- 2608089680 (Delivery/Order Number)
	- 4700829 (PO Number)
	- LD385193 (Load ID)
	- LD385193 (PRO)

	**Output:**
	{
		"consignee": {
			"name": "Moove",
			"addressLine1": "8120 S. Orange Avenue",
			"addressLine2": "",
			"city": "Orlando",
			"state": "FL",
			"zipCode": "32809",
			"country": "US",
			"contact": "Mike Dvorak",
			"phone": "************",
			"email": "",
			"businessHours": "",
			"refNumber": "2608089680",
			"mustDeliver": "09/15/2025, 03:00PM",
			"apptType": "By appointment",
			"apptStartTime": "09/15/2025, 07:00AM",
			"apptEndTime": "09/15/2025, 03:00PM",
			"apptNote": "",
			"timezone": "",
			"externalTMSID": null,
			"refNumberCandidates": [
				"2608089680",
				"4700829",
				"LD385193"
			]
		}
	}

	### Example 3: Multiple Stops - Extract Final Destination
	**Input:**
	Stop 1 - Intermediate Stop:
	Gamma Logistics Hub
	789 Transfer Ave
	Denver, CO 80202

	Final Destination:
	Delta Manufacturing
	321 Factory Road
	Salt Lake City, UT 84101
	Contact: Robert Kim
	Phone: ************
	Delivery Window: 09/02/2025, 10:00AM-12:00PM MST

	**Output:**
	{
		"consignee": {
			"name": "Delta Manufacturing",
			"addressLine1": "321 Factory Road",
			"addressLine2": "",
			"city": "Salt Lake City",
			"state": "UT",
			"zipCode": "84101",
			"country": "US",
			"contact": "Robert Kim",
			"phone": "************",
			"email": "",
			"businessHours": "",
			"refNumber": "",
			"mustDeliver": "09/02/2025, 12:00PM",
			"apptType": "By appointment",
			"apptStartTime": "09/02/2025, 10:00AM",
			"apptEndTime": "09/02/2025, 12:00PM",
			"apptNote": "",
			"timezone": "MST",
			"externalTMSID": null,
			"refNumberCandidates": []
		}
	}`

	defaultSpecificationsTransportTypeInstructions = `* **transportType**: Determine the correct transport type.
    * **CRITICAL RULE**: If the text contains any indication of a temperature-controlled environment, you MUST set this field to **"REEFER"**. Look for explicit mentions of temperature (e.g., "degrees," "temp"), refrigeration, chilling, or freezing. Also, look for phrases describing the product, such as "chilled beef," "frozen goods," or "refrigerated produce." Any reference to "freezer locations" or maintaining a specific temperature is a definitive signal.
    * If no temperature-related requirement is found, check for other specified types like **"VAN"**, **"FLATBED"**, **"BOX TRUCK"**, or **"HOTSHOT"**.
    * If no type is specified, default to **"VAN"**.`
)
