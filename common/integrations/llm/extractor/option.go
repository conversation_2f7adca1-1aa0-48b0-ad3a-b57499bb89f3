package extractor

import "github.com/drumkitai/drumkit/common/models"

type Options struct {
	Config  models.QuickQuoteConfig
	Service models.Service
}

type Option func(*Options)

// WithSpecialEquipment sets the Special Equipment slice with normalized values.
func WithConfig(config models.QuickQuoteConfig) Option {
	return func(o *Options) {
		o.Config = config
	}
}

func WithService(service models.Service) Option {
	return func(o *Options) {
		o.Service = service
	}
}
