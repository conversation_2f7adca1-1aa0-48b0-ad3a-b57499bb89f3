package llm

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func ExtractCarrierInfoSuggestion(
	ctx context.Context,
	email models.Email,
	openAI openai.Service,
	rds RDSInterface,
) (res []models.SuggestedLoadChange, err error) {
	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractCarrierInfoSuggestion", attrs)
	defer func() { metaSpan.End(err) }()

	if len(email.Loads) == 0 {
		log.Info(ctx, "no freightTrackingID found for message, skipping sending to OpenAI",
			zap.String("threadID", email.ThreadID))
		return res, nil
	}

	// NOTE: We can't rely on email upsert in processor to always populate email.Loads.IDs; it does for loads created
	// during upsert, but not updated ones. So we explicitly look it up so we can associate it with suggestion
	freightTrackingID := email.Loads[0].FreightTrackingID // Carrier info emails are typically associated with 1 load
	load, err := rds.GetLoadByFreightID(ctx, email.ServiceID, freightTrackingID)
	if err != nil {
		// Optimization: Don't sent to OpenAI if load does not exist
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no load associated with freightTrackingID, skipping sending to OpenAI")
			return res, nil
		}
		return res, fmt.Errorf("error getting loadID: %w", err)
	}

	//nolint:lll
	type CarrierInfo struct {
		FirstDriverName    string `json:"first_driver_name" jsonschema_description:"The name of the first driver, formatted in Title Case."`
		FirstDriverPhone   string `json:"first_driver_phone" jsonschema_description:"The phone number of the first driver."`
		SecondDriverName   string `json:"second_driver_name" jsonschema_description:"The name of the second driver, formatted in Title Case."`
		SecondDriverPhone  string `json:"second_driver_phone" jsonschema_description:"The phone number of the second driver."`
		TruckNumber        string `json:"truck_number" jsonschema_description:"The truck number, often labeled as truck, truck#, tr#, tk#, TK, trk#, or similar."`
		TrailerNumber      string `json:"trailer_number" jsonschema_description:"The trailer number, often labeled as trailer, trailer#, tl, tl#, TL, trl, or similar."`
		DispatchCity       string `json:"dispatch_city" jsonschema_description:"The city the driver is in at the time of dispatch."`
		DispatchState      string `json:"dispatch_state" jsonschema_description:"The 2-letter abbreviation of the state the driver is in at the time of dispatch."`
		DispatchTimezone   string `json:"dispatch_timezone" jsonschema_description:"The IANA timezone of the driver's dispatch location. Should be guessed if not explicitly provided."`
		ExpectedPickupDate string `json:"expected_pickup_date" jsonschema_description:"The driver's ETA date to the pickup warehouse, formatted as YYYY-MM-DD."`
		ExpectedPickupTime string `json:"expected_pickup_time" jsonschema_description:"The driver's ETA time to the pickup warehouse, in 24-hour format."`
	}

	type PromptResponse struct {
		CarrierInfo CarrierInfo `json:"carrier_info"`
	}

	var prompt = `You are Drumkit - a bleeding edge logistics scheduling assistant that interfaces via email.
    Make sure your final answers are definitive, complete and well formatted.

    You'll receive an e-mail body from the user. You should look for driver and carrier information.
    The information we're most concerned with are 2 driver names and corresponding phone numbers, truck number,
    and trailer number. These can appear in the emails in any number of formats. They will usually appear all
    together in the same area of the email body. Some emails contain a name at the end of an email within an
	email signature, which is the email of the sender and NOT a driver name. This name may have a salutation before
	it, but sometimes it doesn't and just appears at the end of the email after the main body of text, usually
	after some newlines. Avoid detecting signature names as driver names at all costs.

	Crucially, do not consider the sender of the email as possible first or second driver. This information,
	identifiable by names, emails, and phone numbers, is often found in the signature block and
	is not relevant to driver or carrier information. It's common for email body to have the sender name or
	phone number within the email signature block. Ensure to exclude these details from your extraction process.

    Drivers are sometimes labeled as "driver" in the email, other times it's just a name. Sometimes the driver
    is labeled as D, or driver, or DRV, or any combination of letters in the word driver. Other times it's just
    a person's name. Sometimes the driver name appears next to a phone number. Do your best to review the text
    parse out which names, if any, belongs to drivers and the phone numbers which usually comes after.
    Driver names can be of any nationality. The name can be in the email in any case (upper case, lowercase,
    capitalized). After extracting the name, for the output, format the driver's name to be Title Case - so
    the first letters of names are capitalized and all other letters are lowercase. Do your best to retrieve
    only driver' names, and not names of the email sender in the case no driver names are found.

    Truck numbers and trailer numbers usually appear below or next to the driver information. Some common formats
    for referring to truck numbers are truck, truck#, tr#, tk#, TK, trk#, or any other combination of letters in
    in the word "truck" abbreviated. Some common formats for referring to trailer numbers are trailer, trailer#, tl,
    tl#, TL, trl, or any other combination of letters in the word "trailer" abbreviated.

	You should also capture the driver's ETA to the pickup location, aka the next stop.

    One e-mail can contain multiple names and numbers. If the driver info is in the email, it will usually appear
    in the beginning of the email. If there are multiple, do your best to get the first two, excluding signatures.


    Your responses should be a JSON with this type structure:

       {
         "carrier_info: {
           "first_driver_name": string,
           "first_driver_phone": string,
           "second_driver_name": string,
           "second_driver_phone": string,
           "truck_number": string,
           "trailer_number": string,
		   "dispatch_city": string,
		   "dispatch_state": string,
		   "dispatch_timezone": string,
		   "expected_pickup_date": string,
		   "expected_pickup_time": string,
		   "expected_pickup_timezone": string,
         }
       }

    Here's an example:

       {
         "carrier_info: {
           "first_driver_name": "First last",
           "first_driver_phone": "************",
           "second_driver_name": "First last",
           "second_driver_phone": "************",
           "truck_number": "12345",
           "trailer_number": "XYZ123",
		   "dispatch_city": "Chicago",
		   "dispatch_state": "IL",
		   "dispatch_timezone": "America/Chicago",
		   "expected_pickup_date": "2024-08-30",
		   "expected_pickup_time": "13:00",
		   "expected_pickup_timezone": "America/New_York",
         }
       }

    "first_driver_name" should be the name of the first driver.
    "first_driver_phone" should be the phone number of the first driver.
    "second_driver_name" should be the name of the second driver.
    "second_driver_phone" should be the phone number of the second driver.
    "truck_number" should be the truck number.
    "trailer_number" should be the trailer number.
	"dispatch_city" is the city the driver's in at the time of dispatch.
	"dispatch_state" is the 2-letter abbreviation of the state the driver's in at the time of dispatch.
	"dispatch_timezone" is the IANA timezone of the driver's dispatch location. You should guess the dispatch timezone
	if none explicitly provided.
	"expected_pickup_date" and "expected_pickup_time" are the driver's ETA to the pickup warehouse.
	Date should be formatted YYYY-MM-DD. Time should be in 24 hour format. If none provided, leave these fields empty.

    Example:

    Driver here is Haley Morgan

    ************

    John Doe, ************

    Truck 915

    Trailer 222

    ELD fourkites : 915-Haley

    Eta 20 min to first pick.

    From: Firstname, Lastname <<EMAIL>>

    Sent: Thursday, November 16, 2024 1:11 PM

    To: User <<EMAIL>>

    Cc: Drumkit <<EMAIL>>

	Possible signature block structure in the body:
	Firstname Lastname (or Lastname, Firstname)
	Role in the company
	Company
	Phone number
	Email
	Website
	Company's Phone number

    Response:

    {
      "carrier_info": {
        "first_driver_name": "Haley Morgan",
        "first_driver_phone": "************",
        "second_driver_name": "John Doe",
        "second_driver_phone": "************",
        "truck_number": "915",
        "trailer_number": "222"
		"dispatch_city": "Chicago",
		"dispatch_state": "IL",
		"expected_pickup_date": "2024-11-16",
		"expected_pickup_time": "12:31",
      }
    }

    If no driver or carrier information was found, return:

    {
      "carrier_info": {}
    }

    The user e-mails are in English - US date format.`

	userPrompt := "From: " + email.Sender + "\n" + "Body: " + email.BodyWithoutSignature

	response, err := openAI.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.NoProjectStep, false),
		openai.ResponseOptions{
			UserPrompt:      userPrompt,
			DeveloperPrompt: prompt,
			Schema:          extractor.GenerateSchema[PromptResponse](),
		},
	)
	if err != nil {
		return res, fmt.Errorf("LLM appointment confirmation call failed: %w", err)
	}

	promptResponse, err := extractor.StructExtractor[PromptResponse](response.Content)
	if err != nil {
		return res, fmt.Errorf("failed to extract JSON from OpenAI response: %w", err)
	}

	log.Debug(ctx, fmt.Sprintf("OpenAI response: %s", response.Content))

	var tz string
	// Always keep timestamps for Aljex users TZ agnostic
	if load.TMS.Name != models.Aljex {
		tz = promptResponse.CarrierInfo.DispatchTimezone
	}

	suggestedLoadChange := models.SuggestedLoadChange{
		Account:           email.Account,
		FreightTrackingID: freightTrackingID,
		ServiceID:         email.ServiceID,
		Suggested: models.SuggestedChanges{
			Changes: &models.Changes{
				FirstDriverName:   promptResponse.CarrierInfo.FirstDriverName,
				FirstDriverPhone:  promptResponse.CarrierInfo.FirstDriverPhone,
				SecondDriverName:  promptResponse.CarrierInfo.SecondDriverName,
				SecondDriverPhone: promptResponse.CarrierInfo.SecondDriverPhone,
				TruckNumber:       promptResponse.CarrierInfo.TruckNumber,
				TrailerNumber:     promptResponse.CarrierInfo.TrailerNumber,
				DispatchCity:      promptResponse.CarrierInfo.DispatchCity,
				DispatchState:     promptResponse.CarrierInfo.DispatchState,
				ExpectedPickupTime: StringsToTime(ctx, promptResponse.CarrierInfo.ExpectedPickupDate,
					promptResponse.CarrierInfo.ExpectedPickupTime, tz),
			},
		},
		Applied:  models.SuggestedChanges{},
		Pipeline: models.CarrierInfoPipeline,
		Category: models.CarrierInfo,
		Status:   models.Pending,
		EmailID:  email.ID,
		ThreadID: email.ThreadID,
		Email:    email,
		LoadID:   &load.ID,
		Load:     load,
	}

	log.Debug(ctx, "suggested load change", zap.Any("loadChange", suggestedLoadChange))

	res = []models.SuggestedLoadChange{suggestedLoadChange}
	return res, err
}
