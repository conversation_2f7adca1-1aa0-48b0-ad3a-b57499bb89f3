package llm

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

func TestCheckCallExtraction(t *testing.T) {
	ctx := context.Background()

	type testCase struct {
		TMSName            models.IntegrationName
		EmailTimezone      string
		AITimezoneResponse string
		ExpectedTimestamp  models.NullTime
	}

	testCases := map[string]testCase{
		"NoTimezone": {
			EmailTimezone:      "",
			AITimezoneResponse: "",
			ExpectedTimestamp:  StringsToTime(ctx, "2023-03-29", "15:30", ""),
		},
		"LATimezone": {
			EmailTimezone:      " PT",
			AITimezoneResponse: "America/Los_Angeles",
			ExpectedTimestamp:  StringsToTime(ctx, "2023-03-29", "15:30", "America/Los_Angeles"),
		},
		"LATimezoneButAljexTMS": {
			EmailTimezone:      " PT",
			TMSName:            models.Aljex,
			AITimezoneResponse: "",
			ExpectedTimestamp:  StringsToTime(ctx, "2023-03-29", "15:30", ""),
		},
	}

	for name, tc := range testCases {
		mockOpenAI := new(MockOpenaiService)
		mockRDS := new(MockRDS)

		var mockedOpenAIResponse = fmt.Sprintf(`
						{
							"check_call": [
							  {
								"freight_tracking_id": "2080005",
								"status": "loaded",
								"date": "2023-03-29",
								"time": "15:30",
								"timezone": "%s",
								"city": "Strafford",
								"state": "MO",
								"notes": "Checkin was at 5:00pm. Checkout was at 8:00pm."
							  }
							]
						  }
						`, tc.AITimezoneResponse)

		var mockedLoad = models.Load{
			Model: gorm.Model{
				ID: 1,
			},
			FreightTrackingID: "2080005",
			ServiceID:         1,
			TMS:               models.Integration{Name: tc.TMSName},
			LoadCoreInfo: models.LoadCoreInfo{
				Status:   "open",
				PONums:   "1, 2, 3",
				Operator: "none",
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						City:  "Stratford",
						State: "MO",
					},
				},
				Specifications: models.Specifications{TotalOutPalletCount: 1},
			},
		}

		var mockedEmail = models.Email{
			Account:   "<EMAIL>",
			UserID:    0,
			ServiceID: 1,
			Body: fmt.Sprintf(`2120469
						   Loaded
						   checkin @5pm 3/29%s
						   checkout : 8pm 3/29%s
						   Temp : -10`,
				tc.EmailTimezone, tc.EmailTimezone),
			Loads: []models.Load{mockedLoad},
		}

		var expectedResult = []models.SuggestedLoadChange{
			{
				Account:           "<EMAIL>",
				FreightTrackingID: "2080005",
				ServiceID:         1,
				Suggested: models.SuggestedChanges{
					CheckCallChanges: &models.CheckCallChanges{
						Status:    "loaded",
						Timestamp: tc.ExpectedTimestamp,
						Timezone:  tc.AITimezoneResponse,
						City:      mockedLoad.Pickup.City,
						State:     mockedLoad.Pickup.State,
						Notes:     "Checkin was at 5:00pm. Checkout was at 8:00pm.",
					},
				},
				Applied:  models.SuggestedChanges{},
				Status:   models.Pending,
				Pipeline: models.CheckCallPipeline,
				Category: models.CheckCallResponse,
				LoadID:   &mockedLoad.ID,
				EmailID:  mockedEmail.ID,
				ThreadID: mockedEmail.ThreadID,
				Email:    mockedEmail,
			},
		}

		t.Run(name, func(t *testing.T) {
			// Mocking external calls
			mockOpenAI.On(
				"GetResponse",
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
			).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponse), nil)
			mockRDS.On("GetLoadByFreightID", mock.Anything, mock.Anything, mock.Anything).Return(mockedLoad, nil)

			result, err := ExtractCheckCallSuggestions(context.Background(), mockedEmail, mockOpenAI, mockRDS)
			result[0].BraintrustSpanID = ""

			require.NoError(t, err)
			assert.Equal(t, expectedResult, result)

			// Assert that the mocked methods were indeed called
			mockOpenAI.AssertExpectations(t)
			mockRDS.AssertExpectations(t)
		})
	}

}

func TestExtractCheckCallSuggestionsNotFound(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)
	mockRDS := new(MockRDS)

	var mockedOpenAIResponse = `{ "check_call": [] }`

	var mockedLoad = models.Load{
		Model: gorm.Model{
			ID: 1,
		},
		FreightTrackingID: "2080005",
		ServiceID:         1,
		TMS:               models.Integration{Name: models.Aljex},
		LoadCoreInfo: models.LoadCoreInfo{
			Status:         "open",
			PONums:         "1, 2, 3",
			Operator:       "none",
			Specifications: models.Specifications{TotalOutPalletCount: 1},
		},
	}

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body:      `2120490 : Driver is not answering , i will update you shortly`,
		Loads:     []models.Load{mockedLoad},
	}

	var expectedResult []models.SuggestedLoadChange

	// Mocking external calls
	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponse), nil)

	result, err := ExtractCheckCallSuggestions(context.Background(), mockedEmail, mockOpenAI, mockRDS)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockOpenAI.AssertExpectations(t)
}
