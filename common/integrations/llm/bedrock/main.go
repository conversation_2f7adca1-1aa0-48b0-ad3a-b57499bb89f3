// The Bedrock package is stubbed out for now as we are using OpenAI Go SDK for all LLM calls.
// This package is kept here for future reference and in case we need to use Bedrock again.
package bedrock

// import (
// 	"context"
// 	"sync"

// 	"github.com/aws/aws-sdk-go-v2/config"
// 	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
// 	"go.uber.org/zap"

// 	"github.com/drumkitai/drumkit/common/log"
// )

// type (
// 	Request struct {
// 		AnthropicVersion string    `json:"anthropic_version"`
// 		MaxTokens        int       `json:"max_tokens"`
// 		Messages         []Message `json:"messages"`
// 		Temperature      float64   `json:"temperature"`
// 		TopP             float64   `json:"top_p"`
// 	}

// 	Response struct {
// 		ID           string    `json:"id"`
// 		Type         string    `json:"type"`
// 		Role         string    `json:"role"`
// 		Model        string    `json:"model"`
// 		Content      []Content `json:"content"`
// 		StopReason   string    `json:"stop_reason"`
// 		StopSequence *string   `json:"stop_sequence"`
// 		Usage        Usage     `json:"usage"`
// 	}

// 	Content struct {
// 		Type string `json:"type"`
// 		Text string `json:"text"`
// 	}

// 	Usage struct {
// 		InputTokens  int `json:"input_tokens"`
// 		OutputTokens int `json:"output_tokens"`
// 	}

// 	Message struct {
// 		Role    string `json:"role"`
// 		Content string `json:"content"`
// 	}

// 	Runtime interface {
// 		InvokeModel(
// 			ctx context.Context,
// 			params *bedrockruntime.InvokeModelInput,
// 			opts ...func(*bedrockruntime.Options),
// 		) (*bedrockruntime.InvokeModelOutput, error)
// 	}
// )

// var (
// 	bedrockClient Runtime
// 	once          sync.Once
// )

// func GetBedrockClient(ctx context.Context) Runtime {
// 	once.Do(func() {
// 		cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
// 		if err != nil {
// 			log.Error(ctx, "failed to load AWS SDK config", zap.Error(err))
// 		}

// 		bedrockClient = bedrockruntime.NewFromConfig(cfg)
// 	})

// 	return bedrockClient
// }
