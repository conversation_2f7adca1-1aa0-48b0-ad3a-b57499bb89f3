package llm

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

func TestCarrierExtractQuote(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)

	var responseContent = `
	{
		"totalCost": 899,
		"isAvailable": true,
		"notes": "FSC separate"
	}
	`

	var mockedEmail = models.Email{
		Model:      gorm.Model{ID: 1},
		Account:    "<EMAIL>",
		UserID:     1,
		ServiceID:  2,
		ExternalID: "email1",
		ThreadID:   "thread1",
		Subject:    "Mock quote",
		Body: `Hey <PERSON>!\n\nWe don't do a whole lot of AZ so our price will probably be too inflated.\n
		We could get it for $3499 all-in.`,
	}

	var expectedResult = models.CarrierQuote{
		EmailID:     mockedEmail.ID,
		RecipientID: mockedEmail.UserID,
		ServiceID:   mockedEmail.ServiceID,
		Status:      models.RespondedCarrierQuoteStatus,
		SuggestedQuote: &models.CarrierQuoteData{
			TotalCost:   899,
			Currency:    "USD",
			IsAvailable: true,
			Notes:       "FSC separate",
		},
		ThreadID: mockedEmail.ThreadID,
	}

	// Mocking external calls
	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(responseContent), nil)

	result, err := ExtractCarrierQuoteResponse(context.Background(), mockedEmail, mockOpenAI)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	// Assert that the mocked methods were indeed called
	mockOpenAI.AssertExpectations(t)
}
