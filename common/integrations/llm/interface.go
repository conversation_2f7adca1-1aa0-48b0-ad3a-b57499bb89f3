package llm

import (
	"context"

	"github.com/stretchr/testify/mock"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	tmsIntegrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

// Define interfaces to allow for mocking in unit tests
type RDSInterface interface {
	GetLoadByFreightID(ctx context.Context, serviceID uint, loadCode string) (models.Load, error)
	GetLoadByExternalTMSID(ctx context.Context, serviceID uint, loadCode string) (models.Load, error)
	GetNumberOfEmailsByThreadIDAndUserID(ctx context.Context, threadID string, userID uint) (int, error)
	GetTMSListByServiceID(ctx context.Context, serviceID uint) ([]models.Integration, error)
	GetLocationByAddress(ctx context.Context, serviceID uint, addr models.CompanyCoreInfo) (models.TMSLocation, error)
	GetCustomerByName(ctx context.Context, serviceID uint, name string) (models.TMSCustomer, error)
}

type StandardRDS struct{}

func (s StandardRDS) GetLoadByFreightID(
	ctx context.Context,
	serviceID uint,
	loadCode string,
) (models.Load, error) {
	return loadDB.GetLoadByFreightIDAndService(ctx, serviceID, loadCode)
}

func (s StandardRDS) GetLoadByExternalTMSID(
	ctx context.Context,
	serviceID uint,
	loadCode string,
) (models.Load, error) {
	return loadDB.GetLoadByExternalTMSIDAndService(ctx, serviceID, loadCode)
}

func (s StandardRDS) GetNumberOfEmailsByThreadIDAndUserID(
	ctx context.Context,
	threadID string,
	userID uint,
) (int, error) {
	return emailDB.GetNumberOfEmailsByThreadIDAndUserID(ctx, threadID, userID)
}

func (s StandardRDS) GetTMSListByServiceID(ctx context.Context, serviceID uint) ([]models.Integration, error) {
	return tmsIntegrationDB.GetTMSListByServiceID(ctx, serviceID)
}

func (s StandardRDS) GetLocationByAddress(
	ctx context.Context,
	serviceID uint,
	addr models.CompanyCoreInfo,
) (models.TMSLocation, error) {
	return tmsLocationDB.GetLocationByAddress(ctx, serviceID, addr)
}

func (s StandardRDS) GetCustomerByName(
	ctx context.Context,
	tmsID uint,
	name string,
) (models.TMSCustomer, error) {
	return tmsCustomerDB.GetCustomerByName(ctx, tmsID, name)
}

// MockOpenAI is a mock for OpenAI service
type MockOpenaiService struct {
	mock.Mock
}

func (m *MockOpenaiService) GetResponse(
	ctx context.Context,
	_ models.Email,
	_ models.Attachment,
	_ braintrustsdk.ProjectDetails,
	opts ...openai.ResponseOptions,
) (openai.GetResponseOutput, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(openai.GetResponseOutput), args.Error(1)
}

func (m *MockOpenaiService) GetEmbedding(
	ctx context.Context,
	input string,
) ([]float64, error) {
	args := m.Called(ctx, input)
	return args.Get(0).([]float64), args.Error(1)
}

// MockRDS is a mock for RDS service
type MockRDS struct {
	mock.Mock
}

func (m *MockRDS) GetLoadByFreightID(ctx context.Context, serviceID uint, loadCode string) (models.Load, error) {
	args := m.Called(ctx, serviceID, loadCode)
	return args.Get(0).(models.Load), args.Error(1)
}

func (m *MockRDS) GetLoadByExternalTMSID(ctx context.Context, serviceID uint, loadCode string) (models.Load, error) {
	args := m.Called(ctx, serviceID, loadCode)
	return args.Get(0).(models.Load), args.Error(1)
}

func (m *MockRDS) GetNumberOfEmailsByThreadIDAndUserID(
	ctx context.Context,
	threadID string,
	userID uint,
) (int, error) {
	args := m.Called(ctx, threadID, userID)
	return args.Get(0).(int), args.Error(1)
}

func (m *MockRDS) GetTMSListByServiceID(ctx context.Context, serviceID uint) ([]models.Integration, error) {
	args := m.Called(ctx, serviceID)
	return args.Get(0).([]models.Integration), args.Error(1)
}

func (m *MockRDS) GetLocationByAddress(
	ctx context.Context,
	serviceID uint,
	addr models.CompanyCoreInfo) (models.TMSLocation, error) {
	args := m.Called(ctx, serviceID, addr)
	return args.Get(0).(models.TMSLocation), args.Error(1)
}

func (m *MockRDS) GetCustomerByName(
	ctx context.Context,
	tmsID uint,
	name string) (models.TMSCustomer, error) {
	args := m.Called(ctx, tmsID, name)
	return args.Get(0).(models.TMSCustomer), args.Error(1)
}
