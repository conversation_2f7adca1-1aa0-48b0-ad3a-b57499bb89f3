package llm

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

var (
	markdownLinkPattern = regexp.MustCompile(`\[([^\]]+)\]\((https?://[^)\s]+)\)`)
	urlPattern          = regexp.MustCompile(`https?://[^\s)\]]+`)
)

func StringsToTime(ctx context.Context, date, clock, timezone string) models.NullTime {
	if date == "" {
		return models.NullTime{}
	}

	dateObj, err := time.Parse("2006-01-02", date)
	if err != nil {
		log.ErrorNoSentry(ctx, "error parsing date", zap.Error(err))

		return models.NullTime{}
	}

	clockObj, err := time.Parse("15:04", clock)
	if err != nil {
		log.WarnNoSentry(ctx, "skipping parsing clock", zap.String("value", clock), zap.Error(err))
	}

	loc := time.UTC
	if timezone != "" {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			log.ErrorNoSentry(ctx, "error loading location", zap.String("timezone", timezone))

			return models.NullTime{}
		}
	}
	return models.NullTime{
		Time: time.Date(dateObj.Year(), dateObj.Month(), dateObj.Day(),
			clockObj.Hour(), clockObj.Minute(), clockObj.Second(), clockObj.Nanosecond(), loc),
		Valid: true,
	}
}

// validateTransportType normalizes and validates the given transport type.
// It converts the input to uppercase and matches it against known transport types.
// If the input doesn't match any known type, it defaults to an empty state and logs a warning.
func validateTransportType(
	ctx context.Context,
	email models.Email,
	attachmentData any, // Markdown string, AWS Textract data, or nil
	input models.TransportType,
	config models.QuickQuoteConfig,
) (models.TransportType, error) {

	// Check for special equipment types in email body/attachment content if any
	joinedEmailData := fmt.Sprintf("%s\n\n%s", email.Subject, email.Body)
	for _, equipment := range config.SpecialEquipment {
		if isStringInAttachment(ctx, equipment, attachmentData) ||
			helpers.IsStringFullMatch(joinedEmailData, equipment) {
			return models.SpecialTransportType, nil
		}
	}

	if helpers.IsBlank(string(input)) {
		return "", errors.New("unknown transport type")
	}

	// Get standard transport types and append service specific transport types to respect via QQ config if any
	validTypes := models.ListQuickQuoteTransportTypes()
	for _, otherType := range config.OtherTransportTypes {
		// Add other transport types to validTypes if not already in validTypes
		if !slices.Contains(validTypes, models.TransportType(otherType)) {
			validTypes = append(validTypes, models.TransportType(otherType))
		}
	}

	normalizedInput := strings.ToUpper(string(input))
	normalizedInput = strings.Join(strings.Fields(normalizedInput), " ") // Remove extra spaces

	// Handle variations of "HOT SHOT" (e.g. "HOT-SHOT")
	re := regexp.MustCompile(`HOT.{1,2}SHOT`)
	normalizedInput = re.ReplaceAllString(normalizedInput, "HOTSHOT")

	if slices.Contains(validTypes, models.TransportType(normalizedInput)) {
		return models.TransportType(normalizedInput), nil
	}

	// If no exact match, try matching with all whitespace removed
	if slices.Contains(validTypes, models.TransportType(strings.Join(strings.Fields(normalizedInput), ""))) {
		return models.TransportType(strings.Join(strings.Fields(normalizedInput), "")), nil
	}

	// If no exact match, try partial matching
	for _, validType := range validTypes {
		if strings.Contains(normalizedInput, strings.ToUpper(string(validType))) ||
			// Handle case where LLM input is "BOX" and validType is "BOX TRUCK"
			strings.Contains(string(validType), normalizedInput) {
			return validType, nil
		}
	}

	log.WarnNoSentry(
		ctx,
		"unknown transport type",
		zap.String("input", string(input)),
		zap.String("normalized", normalizedInput),
	)

	return "", errors.New("unknown transport type")
}

func isStringInAttachment(ctx context.Context, str string, attachmentData any) bool {
	if attachmentData == nil {
		return false
	}

	switch t := attachmentData.(type) {
	case textract.ShippingOrder:
		return helpers.IsStringInArray(t.Lines, str)

	case string:
		return helpers.IsStringFullMatch(t, str)

	default:
		log.Warn(
			ctx,
			"attachment data type not supported",
			zap.Any("attachmentData", t),
		)
	}

	return false
}

// isHallucination returns true if both pickup and dropoff locations are missing from the email body,
// handling cases where LLM returns zipcode and/or city.
//
// Note that we don't validate pickup and dropoff individually because
// LLM may have formatted data in a way that's slightly different from the email but still
// correct (e.g. sender misspelled Bostno and LLM corrected to Boston, or LLM formats "ny" as "New York").
// However, if BOTH pickup and dropoff are missing from the email, it's likely a hallucination.
func isHallucination(userPrompt string, pickup, dropoff models.Address) bool {

	pickupInEmail := (!helpers.IsBlank(pickup.Zip) && helpers.IsStringFullMatch(userPrompt, pickup.Zip)) ||
		(!helpers.IsBlank(pickup.City) && helpers.IsStringFullMatch(userPrompt, pickup.City))

	dropoffInEmail := (!helpers.IsBlank(dropoff.Zip) && helpers.IsStringFullMatch(userPrompt, dropoff.Zip)) ||
		(!helpers.IsBlank(dropoff.City) && helpers.IsStringFullMatch(userPrompt, dropoff.City))

	// If pickup or dropoff is found in the email (either zip or city), it's not a hallucination
	return !pickupInEmail && !dropoffInEmail
}

// RemoveLinks removes URLs from input string using regex. For markdown links, keeps the text but removes the URL.
func RemoveLinks(input string) string {
	// Replace markdown links [text](url) with just the text ($1 isolates the text from within the square brackets [])
	input = markdownLinkPattern.ReplaceAllString(input, "$1")

	input = urlPattern.ReplaceAllString(input, "")

	return input
}

// RemoveLinksIfString removes URLs if the input is a string, otherwise returns the input unchanged.
func RemoveLinksIfString(content any) any {
	if str, ok := content.(string); ok {
		return RemoveLinks(str)
	}
	return content
}
