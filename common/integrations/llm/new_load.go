package llm

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	MarkdownInputContentType string = "markdown"
	TextInputContentType     string = "text"
	TextractInputContentType string = "aws textract"
)

// Lists of our users customers to handle edge cases
var (
	TridentCustomers      = []string{"MJB", "ARGO"}
	FetchFreightCustomers = []string{
		// These are the same customer
		"Steel Equipment Specialists",
		"SES, LLC",
		"seseng",
	}
)

// ExtractNewLoadSuggestions parses available load info from the email body/attachments (customer, pickup, dropoff, etc)
// and generates a quote request & load building suggestion for each potential load.
// Generating linked suggestions accomplishes a few things:
// 1. We capture when a quote is won and converted to a load
// 2. We capture customer for both quotes and new loads
// 3. The user sees just 1 "New Load" suggestion in sidebar instead of 2, which eliminates redundancy and confusion.
// 4. Metrics can still differentiate between quotes and load building suggestions.
// View ADR on Notion for more details https://rb.gy/fa4ybv.
//
// NOTE: ExtractNewLoadSuggestions handles the following cases:
// 1) emails with 1+ attachment(s), and 2) emails with no attachments and just 1 quote request/load in the body.
// Emails with no attachments & multiple quote requests in the email body are handled by runQuoteRequestExtract().
// Suggestions where both pickup and dropoff addresses are missing/hallucinations are removed to reduce false positives.
func ExtractNewLoadSuggestions(
	ctx context.Context,
	email models.Email,
	tms models.Integration,
	opts ...extractor.Option,
) (results []models.SuggestedLoadChange, err error) {
	options := &extractor.Options{
		Config:  models.QuickQuoteConfig{},
		Service: models.Service{},
	}

	for _, opt := range opts {
		opt(options)
	}

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "ExtractNewLoadSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		return nil, fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	if email.HasPDFs {
		return extractor.ExtractLoadInfoFromAttachments(ctx, email, openaiService, tms, *options)
	}

	result, err := extractor.ExtractLoadInfoFromEmailBody(ctx, email, openaiService, tms, *options)
	if err != nil {
		log.Error(ctx, "Error extracting load info from email body", zap.Error(err))
		return nil, err
	}

	// Skip suggestion if both pickup and dropoff address are missing to reduce false positives
	if extractor.IsInsufficientAddressData(result.Suggested.LoadChanges.Pickup.CompanyCoreInfo) &&
		extractor.IsInsufficientAddressData(result.Suggested.LoadChanges.Consignee.CompanyCoreInfo) {
		log.Info(
			ctx,
			"Skipping suggestion missing both pickup and dropoff addresses",
			zap.Any("suggestedPickup", result.Suggested.LoadChanges.Pickup.CompanyCoreInfo),
			zap.Any("suggestedConsignee", result.Suggested.LoadChanges.Consignee.CompanyCoreInfo),
		)
		return nil, nil
	}

	return []models.SuggestedLoadChange{result}, nil
}
