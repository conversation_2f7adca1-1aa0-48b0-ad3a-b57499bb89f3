package llm

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	promptBuilder "github.com/drumkitai/drumkit/common/integrations/llm/prompt_builder"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

// promptLLMForCustomer prompts the LLM to extract a customer's name and email address from the email body.
// Used by ExtractQuoteRequestSuggestions and ExtractNewLoadSuggestions.
func promptLLMForCustomer(
	ctx context.Context,
	tmsID uint,
	email models.Email,
	attachmentData any, // Markdown string, Textract, or nil
	openaiService openai.Service,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	rds RDSInterface,
) (_ *models.TMSCustomer, braintrustLogID string, err error) {

	attrs := otel.EmailAttrs(&email)
	ctx, metaSpan := otel.StartSpan(ctx, "promptLLMForCustomer", attrs)
	defer func() { metaSpan.End(err) }()

	accountDomain := extractDomain(email.Account)

	//nolint:lll
	developerPrompt := fmt.Sprintf(
		`
You are a highly reliable data extraction engine. Your sole function is to parse and extract two data points from the provided text and return them as a JSON object. You must not generate any text other than the JSON output.

**Data to Extract:**
- **shipper_name**: The name of the company requesting the quote/shipment/pricing/service. Look for company names in signatures, forwarded message blocks, or attachments. Prioritize a company name over a person's name. In most cases, the company name will appear multiple times (especially in forwarded messages). Trim legal suffixes like "LLC," "INC," and "CORP" from the name. If no clear company name is found, return an empty string.

- **original_sender_email**: The email address of the person or system that initiated the request. If it's a forwarded message, find the 'From:' line in the body and extract the first email address whose domain (or subdomain) is different from our company's domain, which is '%s'. If it's not a forwarded message, use the email address from the <email_sender> tag. If no valid email address from an external domain can be found, return an empty string.


**Examples:**
### Example 1: Name in Email Body & Attachment
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>FW: New Load Quote</email_subject>
<email_body>Hi team, please find the quote request from our client below. Thanks, -- From: "Jane Doe" <<EMAIL>> Date: Tuesday, August 12, 2025 at 11:00 AM Subject: Quote for Shipment Hi, we need a quote for this shipment. See attached.</email_body>
<attachment_data>
**Argo Fine Imports**
123 Main Street
Anytown, USA
</attachment_data>
**Output:**
{
"shipper_name": "Argo Fine Imports",
"original_sender_email": "<EMAIL>"
}

### Example 2: Name in Attachment Only
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>New Quote Request</email_subject>
<email_body>Please see the attached file for a new request.</email_body>
<attachment_data>Shipment Request Form
Shipper: **Global Solutions, Inc.**
Pickup Location: Warehouse A</attachment_data>
**Output:**
{
"shipper_name": "Global Solutions",
"original_sender_email": "<EMAIL>"
}

### Example 3: Forwarded Email Chain (No Attachments)
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Fwd: Freight Quote Needed</email_subject>
<email_body>
---------- Forwarded message ---------
From: Bob Johnson <<EMAIL>>
Date: Mon, August 11, 2025 at 10:00 AM
Subject: Freight Quote Needed
Hello, please provide a rate for the attached shipment.</email_body>
<attachment_data></attachment_data>
**Output:**
{
"shipper_name": "Shipper Company",
"original_sender_email": "<EMAIL>"
}

### Example 4: No Recognizable Sender or Company
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Shipping</email_subject>
<email_body>Hey, can you ship something for me? Let me know.</email_body>
<attachment_data></attachment_data>
**Output:**
{
"shipper_name": "",
"original_sender_email": ""
}

### Example 5: Forwarded Message (Internal Employee)
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>New Quote Request</email_subject>
<email_body>Team, Please see the forwarded message below for a quote request. --- Forwarded Message --- From: "Alex Johnson" <<EMAIL>> Date: August 12, 2025 Subject: Quote Request Hi, we need a quote.</email_body>
<attachment_data></attachment_data>
**Output:**
{
"shipper_name": "Customer Shipper",
"original_sender_email": "<EMAIL>"
}

### Example 6: Carrier-to-Carrier Quote Request
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Houston, TX to Longwood, FL for 08/14</email_subject>
<email_body>Is this still available? Best regards, Brent Donnigan ABC Transports LLC MC 1590824</email_body>
<attachment_data></attachment_data>
**Output:**
{
"shipper_name": "ABC Transports",
"original_sender_email": "<EMAIL>"
}

### Example 7: Automated Notification from a Third-Party Platform
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Spot Shipment Available</email_subject>
<email_body>You are receiving this message because you have chosen to be notified of newly available spot shipments on CarrierPoint. The following spot shipments have been posted by your partners since the last e-mail you received: Cool Steel DSM ID | Stops | Weight | Equipment | Date Range For Stop | Actions 60073004 | White Cloud, MI US<br>Frostproof, FL US | 38347.0 lbs | Flatbed - Tarps | 8/15/2025 - 8/15/2025<br>8/18/2025 - 8/18/2025 | review | respond</email_body>
<attachment_data></attachment_data>
**Output:**
{
"shipper_name": "Cool Steel",
"original_sender_email": "<EMAIL>"
}

### Example 8: Carrier as Sender
**Input:**
<email_sender><EMAIL></email_sender>
<email_subject>Load Question</email_subject>
<email_body>Hey, is this load still available?
--
John Smith
XYZ Freight, LLC</email_body>
<attachment_data>
</attachment_data>
**Output:**
{
"shipper_name": "XYZ Freight",
"original_sender_email": "<EMAIL>"
}
`, accountDomain)

	customPrompt, err := promptBuilder.BuildPromptForTMS(ctx, tmsID, "quoteRequest", "customer", developerPrompt)
	if err != nil {
		log.Warn(ctx, "error building custom customer prompt (quoteRequest)", zap.Error(err))
	} else {
		developerPrompt = customPrompt
	}

	userPrompt := fmt.Sprintf(
		`
<email_sender> %s </email_sender>
<email_subject> %s </email_subject>
<email_body> %s </email_body>
<attachment_data> %v </attachment_data>
`,
		email.Sender,
		email.Subject,
		email.Body,
		attachmentData,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustProjectDetails,
		openai.ResponseOptions{
			DeveloperPrompt: developerPrompt,
			UserPrompt:      userPrompt,
			Schema:          extractor.GenerateSchema[CustomerInfo](),
			Temperature:     openai.TemperatureDeterministic,
		},
	)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("error getting response: %w", err)
	}

	llmOutput, err := extractor.StructExtractor[CustomerInfo](response.Content)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	log.Debug(ctx, "customer LLM output", zap.Any("customerLLMOutput", llmOutput))
	// Try lookup by name parsed by LLM
	if llmOutput.ShipperName != "" && !strings.Contains(strings.ToLower(llmOutput.ShipperName), "lmnop") {
		mappedCustomer, err := mapCustomer(ctx, llmOutput.ShipperName, tmsID, email, attachmentData, rds)
		if err != nil {
			log.WarnNoSentry(ctx, "error mapping customer by LLM parsed name",
				zap.Error(err),
				zap.String("name", llmOutput.ShipperName))
		} else if mappedCustomer != nil {
			return mappedCustomer, response.BraintrustLogID, nil
		}
	}

	// If error or no name found, lookup by domain
	if llmOutput.OriginalSenderEmail == "" ||
		strings.Contains(strings.ToLower(llmOutput.OriginalSenderEmail), "lmnop") {

		return nil,
			response.BraintrustLogID,
			fmt.Errorf("LLM did not return a customer name or domain: %v", llmOutput)
	}

	domain := extractor.ExtractSecondLevelDomain(llmOutput.OriginalSenderEmail)
	if domain == "" {
		domain = llmOutput.OriginalSenderEmail
	}

	mappedCustomer, err := mapCustomer(ctx, domain, tmsID, email, attachmentData, rds)
	if err != nil {
		return nil, response.BraintrustLogID, fmt.Errorf("failed to map domain %s to customer: %w", domain, err)
	}

	return mappedCustomer, response.BraintrustLogID, nil
}

func mapCustomer(
	ctx context.Context,
	parsedName string,
	tmsID uint,
	email models.Email,
	attachmentData any,
	rds RDSInterface,
) (mappedCustomer *models.TMSCustomer, err error) {

	if tmsID == 0 {
		return nil, errors.New("unable to map customer, TMS ID is 0")
	}

	var name string
	joinedEmail := fmt.Sprintf("%s %s", email.Subject, email.Body)
	edgeCaseCustomers := append(FetchFreightCustomers, TridentCustomers...) //nolint:gocritic
	// Check email & attachment data for hard coded Trident customers
	for _, customer := range edgeCaseCustomers {
		check := isStringInAttachment(ctx, customer, attachmentData) || // Attachment case
			helpers.IsStringFullMatch(joinedEmail, customer) // Email case
		if check {
			switch customer {
			case "MJB":
				name = "Liberty Woods International"
			case "ARGO":
				name = "ARGO Fine Imports"

			case "Steel Equipment Specialists", "SES, LLC", "seseng":
				name = "SES"
			}
			break
		}
	}
	if name == "" {
		name = parsedName
	}

	dbCustomer, err := rds.GetCustomerByName(ctx, tmsID, name)
	if err != nil {
		if errors.Is(err, tmsCustomerDB.ErrCustomerNotFound) {
			log.WarnNoSentry(ctx, "customer not found in database", zap.String("name", name))
			return nil, nil
		}
		return nil, fmt.Errorf("db error getting customer: %w", err)
	}

	return &dbCustomer, nil
}
