package braintrustsdk

const (
	// Project IDs
	EmailClassificationProjectID        = "c9ee636e-fdba-4826-8cbc-f70d66207799"
	EmailSignatureStrippingProjectID    = "c0df2f91-d769-4d0a-89d9-42fa401d30bf"
	QuoteRequestProjectID               = "23241dc3-2e24-4dc4-aaa9-9d41701a6d76"
	LoadBuildingProjectID               = "2fd038ea-2b5b-40f1-b419-fd31530ddb18"
	CarrierQuoteProjectID               = "99e10536-cd58-4ff7-838f-02741ac81724"
	QuoteRequestPortalScrapingProjectID = "80acbd66-8e01-4cff-babf-a33848a530ed"
	NoProject                           = "No Project" // For LLM calls that don't log to Braintrust

	// Email Classification Steps
	EmailClassification    ProjectStepName = "Email Classification"
	EmailCategoryLabelling ProjectStepName = "Email Category Labelling"

	// Email Signature Stripping Steps
	EmailSegmentation ProjectStepName = "Email Segmentation"

	// Quote Request Steps
	QRBeginConversation ProjectStepName = "Begin Conversation"
	QRGetCustomer       ProjectStepName = "Get QR Customer"
	QRIsQREmail         ProjectStepName = "Is QR Email"
	QRIsMultiQREmail    ProjectStepName = "Is Multi QR Email"

	// Load Building Steps
	LBGetBasicInfo          ProjectStepName = "Get Basic Info"
	LBGetCustomer           ProjectStepName = "Get LB Customer"
	LBGetRateData           ProjectStepName = "Get Rate Data"
	LBGetPickup             ProjectStepName = "Get Pickup"
	LBGetConsignee          ProjectStepName = "Get Consignee"
	LBGetSpecifications     ProjectStepName = "Get Specifications"
	LBGetCommoditiesAndRefs ProjectStepName = "Get Commodities and References"
	LBIsLBAttachment        ProjectStepName = "Is LB Attachment"

	// Carrier Quote Steps
	CQExtractCarrierQuoteResponse ProjectStepName = "Extract Carrier Quote Response"

	// Quote Request Portal Scraping Steps
	QRPSExtractData ProjectStepName = "Extract Quote Request Data"

	NoProjectStep ProjectStepName = "No Project" // For LLM calls that don't log to Braintrust
)

func CreateProjectDetails(stepName ProjectStepName, hasAttachments bool) ProjectDetails {
	return ProjectDetails{
		ID:             getProjectIDByStepName(stepName),
		StepName:       stepName,
		HasAttachments: hasAttachments,
	}
}

func (p ProjectDetails) GetTags() []string {
	contentTypeTag := "Attachments"

	if !p.HasAttachments {
		contentTypeTag = "Email Body"
	}

	return []string{
		contentTypeTag,
		string(p.StepName),
	}
}

func getProjectIDByStepName(stepName ProjectStepName) string {
	switch stepName {
	case EmailClassification, EmailCategoryLabelling:
		return EmailClassificationProjectID
	case EmailSegmentation:
		return EmailSignatureStrippingProjectID
	case QRBeginConversation, QRGetCustomer, QRIsQREmail, QRIsMultiQREmail:
		return QuoteRequestProjectID
	case LBGetBasicInfo,
		LBGetCustomer,
		LBGetRateData,
		LBGetPickup,
		LBGetConsignee,
		LBGetSpecifications,
		LBGetCommoditiesAndRefs,
		LBIsLBAttachment:
		return LoadBuildingProjectID
	case CQExtractCarrierQuoteResponse:
		return CarrierQuoteProjectID
	case QRPSExtractData:
		return QuoteRequestPortalScrapingProjectID
	case NoProjectStep:
		return NoProject
	}
	return NoProject
}
