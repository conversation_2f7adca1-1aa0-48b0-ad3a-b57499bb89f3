package braintrustsdk

import (
	"context"
	"errors"
	"fmt"

	"github.com/braintrustdata/braintrust-go"

	"github.com/drumkitai/drumkit/common/helpers/otel"
)

func SubmitLog(
	ctx context.Context,
	projectDetails ProjectDetails,
	event braintrust.InsertProjectLogsEventParam,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "braintrustsdk.SubmitLog", nil)
	defer func() { metaSpan.End(err) }()

	if projectDetails.ID == NoProject {
		return errors.New("no project ID provided, skipping log submission")
	}

	_, err = GetClient().client.Projects.Logs.Insert(
		ctx,
		projectDetails.ID,
		braintrust.ProjectLogInsertParams{
			Events: braintrust.Raw[[]braintrust.InsertProjectLogsEventParam](
				[]braintrust.InsertProjectLogsEventParam{event},
			),
		},
	)
	if err != nil {
		return fmt.Errorf("failed to submit log: %w", err)
	}

	return nil
}
