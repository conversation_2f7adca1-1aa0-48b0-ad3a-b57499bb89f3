package braintrustsdk

import (
	"context"
	"errors"
	"fmt"

	"github.com/braintrustdata/braintrust-go"
	"github.com/braintrustdata/braintrust-go/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

func BuildFeedbackRequestBody(
	ctx context.Context,
	logID string,
	expected,
	suggested any,
) braintrust.ProjectLogFeedbackParams {
	similarityScore, err := LevenshteinScorer(expected, suggested)
	if err != nil {
		log.ErrorNoSentry(ctx, "error calculating similarity score for LB basic info feedback", zap.Error(err))
		similarityScore = 0
	}

	return braintrust.ProjectLogFeedbackParams{
		Feedback: braintrust.Raw[[]shared.FeedbackProjectLogsItemParam](
			[]shared.FeedbackProjectLogsItemParam{
				{
					ID:       braintrust.String(logID),
					Expected: braintrust.Raw[any](expected),
					Scores: braintrust.Raw[map[string]float64](
						map[string]float64{
							"similarity": similarityScore,
						},
					),
				},
			},
		),
	}
}

func SubmitFeedback(
	ctx context.Context,
	projectID string,
	feedback braintrust.ProjectLogFeedbackParams,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "braintrustsdk.SubmitFeedback", nil)
	defer func() { metaSpan.End(err) }()

	if projectID == NoProject {
		return errors.New("no project ID provided, skipping log feedback submission")
	}

	_, err = GetClient().client.Projects.Logs.Feedback(ctx, projectID, feedback)
	if err != nil {
		return fmt.Errorf("failed to submit feedback: %w", err)
	}

	return nil
}
