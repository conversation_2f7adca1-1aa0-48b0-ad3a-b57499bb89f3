## Braintrust

In order to run this locally, you will need to create a `.env` file in the `drumkit/common/helpers/braintrust` directory with the following environment variables:

```bash
BRAINTRUST_BASE_URL=<your-braintrust-base-url>
BRAINTRUST_API_KEY=<your-braintrust-api-key>
BRAINTRUST_PROJECT_ID=<your-project-id>
```

A straight forward way to test this is directly is to add `labels = append(labels, string(QuoteRequestLabel))` in the `RunActions` function in `common/emails/actions.go`
This will allow you to use the `/inboxWebhook` endpoint (either with curl or <PERSON><PERSON>) when you have the processor and the api running locally to have the LLM parse the message contents and log the process and responses. 

## Important Notes
For adding additional fields or manipulating the log data in the future, you can reference the Braintrust documentation here:

[Braintrust Tracing Integration Documentation](https://www.braintrust.dev/docs/guides/traces/integrations)

and more generally:

[Braintrust API Documentation](https://www.braintrust.dev/docs/reference/api/Logs)


1. **Score Validation**: All feedback scores must be between 0 and 1.
2. **Source Validation**: Feedback source must be one of: "external", "app", or "api".

## OpenTelemetry Integration

The service integrates with OpenTelemetry for distributed tracing:

- Creates OTLP exporters for trace data
- Configures trace providers
- Supports span attributes for LLM operations