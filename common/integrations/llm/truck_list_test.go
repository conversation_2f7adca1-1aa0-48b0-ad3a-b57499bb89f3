package llm

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestTruckListExtraction(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)

	var mockedOpenAIResponseContent = `
                                          {
                                            "carrier": {
                                              "name": "",
                                              "mc": "",
                                              "dot": "",
                                              "contact_email": "",
                                              "contact_name": ""
                                            },
                                            "truck_list": [
                                              {
                                                "pickup_date": "07/20/2024",
                                                "pickup_location": {
                                                  "city": "Memphis",
                                                  "state": "TN",
                                                  "zip_code": "",
                                                  "country": "US"
                                                },
                                                "dropoff_date": "07/25/2024",
                                                "dropoff_location": {
                                                  "city": "",
                                                  "state": "",
                                                  "zip_code": "",
                                                  "country": "US"
                                                },
                                                "type": "VAN",
                                                "length": 53,
                                                "notes": ""
                                              }
                                            ]
                                          }
                                        `

	var mockedEmail = models.Email{
		UserID:    0,
		ServiceID: 1,
		Body: `
                  Good morning,
                  I have a truck for pickup in Memphis 07/20 dropoff on Thursday.`,
	}

	truck := models.Truck{
		UserID:    mockedEmail.UserID,
		ServiceID: mockedEmail.ServiceID,
		EmailID:   mockedEmail.ID,
		ThreadID:  mockedEmail.ThreadID,
	}

	truck.PickupDate.Suggestion = models.NullTime{Time: time.Date(2024, 07, 20, 00, 00, 0, 0, time.UTC),
		Valid: true}
	truck.PickupLocation.Suggestion = models.Address{
		City:    "Memphis",
		State:   "TN",
		Zip:     "",
		Country: "US",
	}
	truck.DropoffDate.Suggestion = models.NullTime{Time: time.Date(2024, 07, 25, 00, 00, 0, 0, time.UTC),
		Valid: true}
	truck.DropoffLocation.Suggestion = models.Address{
		City:    "",
		State:   "",
		Zip:     "",
		Country: "US",
	}
	truck.Type.Suggestion = models.VanTruckType
	truck.Length.Suggestion = 53
	truck.Notes.Suggestion = ""

	var expectedResult = models.TruckList{
		UserID:    mockedEmail.UserID,
		ServiceID: mockedEmail.ServiceID,
		EmailID:   mockedEmail.ID,
		ThreadID:  mockedEmail.ThreadID,
		Trucks:    []models.Truck{truck},
		IsDraft:   true,
	}

	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponseContent), nil)

	result, err := ExtractTruckListSuggestions(context.Background(), mockedEmail, mockOpenAI)
	result.BraintrustSpanID = ""

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	mockOpenAI.AssertExpectations(t)
}

func TestExtractTruckListNotFound(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)

	var mockedOpenAIResponseContent = `{ "carrier": {}, "truck_list": [] }`

	var mockedEmail = models.Email{
		Account:   "<EMAIL>",
		UserID:    0,
		ServiceID: 1,
		Body:      `2120490 : Driver is not answering , i will update you shortly`,
	}

	var expectedResult models.TruckList
	expectedResult.ServiceID = 1
	expectedResult.IsDraft = true

	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponseContent), nil)

	result, err := ExtractTruckListSuggestions(context.Background(), mockedEmail, mockOpenAI)
	result.BraintrustSpanID = ""

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	mockOpenAI.AssertExpectations(t)
}

func TestMultipleTrucksWithVariousAddresses(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)

	var mockedOpenAIResponseContent = `
                                          {
                                            "carrier": {
                                              "name": "",
                                              "mc": "",
                                              "dot": "",
                                              "contact_email": "<EMAIL>",
                                              "contact_name": ""
                                            },
                                            "truck_list": [
                                              {
                                                "pickup_date": "08/01/2024",
                                                "pickup_location": {
                                                  "city": "Chicago",
                                                  "state": "IL",
                                                  "zip_code": "60601",
                                                  "country": "US"
                                                },
                                                "dropoff_date": "08/03/2024",
                                                "dropoff_location": {
                                                  "city": "New York",
                                                  "state": "NY",
                                                  "zip_code": "10001",
                                                  "country": "US"
                                                },
                                                "type": "REEFER",
                                                "length": 53,
                                                "notes": "Full city-state-zip"
                                              },
                                              {
                                                "pickup_date": "08/05/2024",
                                                "pickup_location": {
                                                  "state": "CA",
                                                  "country": "US"
                                                },
                                                "dropoff_date": "08/07/2024",
                                                "dropoff_location": {
                                                  "zip_code": "98101",
                                                  "country": "US"
                                                },
                                                "type": "FLATBED",
                                                "length": 53,
                                                "notes": "State-only pickup, ZIP-only dropoff"
                                              },
                                              {
                                                "pickup_date": "08/10/2024",
                                                "pickup_location": {
                                                  "city": "Invalid",
                                                  "country": "US"
                                                },
                                                "dropoff_date": "08/12/2024",
                                                "dropoff_location": {
                                                  "city": "Invalid",
                                                  "country": "US"
                                                },
                                                "type": "Van",
                                                "length": 53,
                                                "notes": "Invalid locations"
                                              }
                                            ]
                                          }
                                        `

	var mockedEmail = models.Email{
		UserID:    1,
		ServiceID: 2,
		ThreadID:  "thread456",
		Body:      "Multiple trucks with various address formats",
	}

	carrier := models.CarrierInformation{
		ContactEmail: "<EMAIL>",
	}

	firstTruck := models.Truck{
		UserID:    1,
		ServiceID: 2,
		ThreadID:  "thread456",
	}
	firstTruck.PickupDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 1, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}
	firstTruck.PickupLocation.Suggestion = models.Address{
		City:    "Chicago",
		State:   "IL",
		Zip:     "60601",
		Country: "US",
	}
	firstTruck.DropoffDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 3, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}
	firstTruck.DropoffLocation.Suggestion = models.Address{
		City:    "New York",
		State:   "NY",
		Zip:     "10001",
		Country: "US",
	}
	firstTruck.Type.Suggestion = models.ReeferTruckType
	firstTruck.Length.Suggestion = 53
	firstTruck.Notes.Suggestion = "Full city-state-zip"

	secondTruck := models.Truck{
		UserID:    1,
		ServiceID: 2,
		ThreadID:  "thread456",
	}
	secondTruck.PickupDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 5, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}
	secondTruck.PickupLocation.Suggestion = models.Address{
		State:   "CA",
		Country: "US",
	}
	secondTruck.DropoffDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 7, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}
	secondTruck.DropoffLocation.Suggestion = models.Address{
		Zip:     "98101",
		Country: "US",
	}
	secondTruck.Type.Suggestion = models.FlatbedTruckType
	secondTruck.Length.Suggestion = 53
	secondTruck.Notes.Suggestion = "State-only pickup, ZIP-only dropoff"

	var expectedResult = models.TruckList{
		UserID:    mockedEmail.UserID,
		ServiceID: mockedEmail.ServiceID,
		EmailID:   mockedEmail.ID,
		ThreadID:  mockedEmail.ThreadID,
		Carrier:   carrier,
		Trucks:    []models.Truck{firstTruck, secondTruck},
		IsDraft:   true,
	}

	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponseContent), nil)

	result, err := ExtractTruckListSuggestions(context.Background(), mockedEmail, mockOpenAI)
	result.BraintrustSpanID = ""

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	mockOpenAI.AssertExpectations(t)
}

func TestContactEmailOmissionForSameDomain(t *testing.T) {
	mockOpenAI := new(MockOpenaiService)

	mockedOpenAIResponseContent := `
                                          {
                                            "carrier": {
                                              "name": "Test Carrier",
                                              "mc": "123456",
                                              "dot": "654321",
                                              "contact_email": "<EMAIL>",
                                              "contact_name": "John Doe"
                                            },
                                            "truck_list": [
                                              {
                                                "pickup_date": "08/15/2024",
                                                "pickup_location": {
                                                  "city": "Los Angeles",
                                                  "state": "CA",
                                                  "zip_code": "90001",
                                                  "country": "US"
                                                },
                                                "dropoff_date": "08/17/2024",
                                                "dropoff_location": {
                                                  "city": "San Francisco",
                                                  "state": "CA",
                                                  "zip_code": "94105",
                                                  "country": "US"
                                                },
                                                "type": "VAN",
                                                "length": 48,
                                                "notes": "Test note"
                                              }
                                            ]
                                          }
                                        `

	mockedEmail := models.Email{
		Account:    "<EMAIL>",
		Recipients: "<EMAIL>",
		UserID:     1,
		ServiceID:  3,
		ThreadID:   "thread789",
		Body:       "Test Email Body",
	}

	// Expected to be empty if ContactEmail domain matches account or recipients
	expectedCarrier := models.CarrierInformation{
		Name:         "",
		MC:           "",
		DOT:          "",
		ContactEmail: "",
		ContactName:  "",
	}

	expectedTruck := models.Truck{
		UserID:    mockedEmail.UserID,
		ServiceID: mockedEmail.ServiceID,
		EmailID:   mockedEmail.ID,
		ThreadID:  mockedEmail.ThreadID,
	}

	expectedTruck.PickupDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 15, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}

	expectedTruck.PickupLocation.Suggestion = models.Address{
		City:    "Los Angeles",
		State:   "CA",
		Zip:     "90001",
		Country: "US",
	}

	expectedTruck.DropoffDate.Suggestion = models.NullTime{
		Time:  time.Date(2024, 8, 17, 0, 0, 0, 0, time.UTC),
		Valid: true,
	}

	expectedTruck.DropoffLocation.Suggestion = models.Address{
		City:    "San Francisco",
		State:   "CA",
		Zip:     "94105",
		Country: "US",
	}

	expectedTruck.Type.Suggestion = models.VanTruckType
	expectedTruck.Length.Suggestion = 48
	expectedTruck.Notes.Suggestion = "Test note"

	expectedResult := models.TruckList{
		UserID:    mockedEmail.UserID,
		ServiceID: mockedEmail.ServiceID,
		EmailID:   mockedEmail.ID,
		ThreadID:  mockedEmail.ThreadID,
		Carrier:   expectedCarrier,
		Trucks:    []models.Truck{expectedTruck},
		IsDraft:   true,
	}

	mockOpenAI.On(
		"GetResponse",
		mock.Anything,
		mock.Anything,
	).Return(wrapTestExpectedToOpenAI(mockedOpenAIResponseContent), nil)

	result, err := ExtractTruckListSuggestions(context.Background(), mockedEmail, mockOpenAI)
	result.BraintrustSpanID = ""

	require.NoError(t, err)
	assert.Equal(t, expectedResult, result)

	mockOpenAI.AssertExpectations(t)
}
