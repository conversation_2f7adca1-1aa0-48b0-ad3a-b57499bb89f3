package gmailclient

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"html"
	"io"
	"mime/multipart"
	"mime/quotedprintable"
	"net/http"
	"net/textproto"
	"strings"
	"time"

	"github.com/k3a/html2text"
	"go.uber.org/zap"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient/gmailbatch"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/user/useraccessor"
)

const (
	// Special value for userId to complete the request for the user authenticated
	// in the associated Gmail service instance
	me           = "me"
	InboxBaseURL = "https://mail.google.com/mail/u/0/#inbox/"
)

type NewAttachment struct {
	Data     string `json:"data"` // Base64 encoded file data
	FileName string `json:"fileName"`
	MimeType string `json:"mimeType"`
}

type (
	// Client is the set of exposed Gmail operations used by Drumkit services
	Client interface {
		BatchGetMessages(context.Context, []string) ([]*gmail.Message, error)
		GetMessage(context.Context, string) (*gmail.Message, error)
		ListMessagesAfterDate(context.Context, time.Time) ([]string, error)
		ListMessagesBetweenDates(context.Context, time.Time, time.Time) ([]string, error)
		ListHistory(context.Context, uint64) (*gmail.ListHistoryResponse, error)
		WatchInbox(context.Context, *gmail.WatchRequest) (*gmail.WatchResponse, error)
		StopWatchingInbox(context.Context) error
		SendMessage(
			ctx context.Context,
			genEmail *models.GeneratedEmail,
			signatureHTML string,
			newAttachments []NewAttachment,
		) (*gmail.Message, error)
		GetAttachment(ctx context.Context, msgID, id string) (*gmail.MessagePartBody, error)
		// Returns service.User
		GetAuthenticatedUser() models.UserAccessor
		// Returns the user's signature from API if it exists.
		// If there's a transient error, it returns the signature from the DB, if it exists.
		// Users should call this function BEFORE sending a message to ensure the signature is set.
		//
		// NOTE: Unfortunately Gmail API does not expose the signature reply/forward settings,
		// so Drumkit defaults to always adding the signature to the email body.
		GetSignature(ctx context.Context, aliasEmail string) (string, error)

		// Returns the user's alias emails (e.g. <EMAIL>) from API.
		// At minimum, the user's primary email is returned.
		GetAliases(ctx context.Context) ([]string, error)

		ForwardMessage(
			ctx context.Context,
			originalMsgID string,
			genEmail *models.GeneratedEmail,
			useSenderSignature bool,
			addFwdPrefix bool,
		) (*gmail.Message, error)

		AddLabels(
			ctx context.Context,
			msgID string,
			labels []string,
		) error

		ArchiveMessage(
			ctx context.Context,
			msgID string,
		) error
	}

	// Service is a concrete gmail implementation of the Client interface
	Service struct {
		// Owner's account email address
		User models.UserAccessor

		// The normal Gmail SDK
		service *gmail.Service

		// HACK: Only used for response logging; email creds are stored in user table
		integration models.Integration

		// HACK: A clone of the Gmail SDK with better support for batch message get
		batchService *gmailbatch.Service
	}
)

func New[T models.UserAccessor](
	ctx context.Context,
	clientID,
	clientSecret string,
	user T,
	opts ...oauth.Option,
) (Client, error) {

	options := &oauth.Options{
		EncryptionKey: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	var oauthConfig = oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Endpoint:     google.Endpoint,
	}

	ts, err := oauth.NewCachingTokenSource(ctx, user, &oauthConfig, oauth.WithEncryptionKey(options.EncryptionKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create caching token source: %w", err)
	}

	service, err := gmail.NewService(ctx, option.WithHTTPClient(oauth.NewTracingClient(ctx, ts)))
	if err != nil {
		return nil, fmt.Errorf("gmail.NewService failed: %w", err)
	}

	batchService, err := gmailbatch.NewService(ctx, option.WithHTTPClient(oauth.NewTracingClient(ctx, ts)))
	if err != nil {
		return nil, fmt.Errorf("batch.NewService failed: %w", err)
	}

	return &Service{
		User:         user,
		service:      service,
		batchService: batchService,
		integration: models.Integration{
			Model: gorm.Model{ID: user.GetID()},
			Type:  models.EmailType,
			Name:  models.Gmail,
		}}, nil
}

// If HTTP error due to non-2xx response, logs the response code.
func (s *Service) logGoogleError(ctx context.Context, err error) {
	if err == nil {
		return
	}

	var httpErr *googleapi.Error
	if errors.As(err, &httpErr) {
		httplog.LogHTTPResponseCode(ctx, s.integration, httpErr.Code)
	} else {
		httplog.LogHTTPRequestFailed(ctx, s.integration, err)
	}
}

func (s *Service) GetAuthenticatedUser() models.UserAccessor {
	return s.User
}

func (s *Service) BatchGetMessages(ctx context.Context, ids []string) ([]*gmail.Message, error) {
	log.Info(ctx, "gmailclient.BatchGetMessages", zap.Strings("msgIds", ids))
	res, err := s.batchService.Batch.Email.Get(me, ids).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, http.StatusOK)
	}

	return res, err
}

func (s *Service) GetMessage(ctx context.Context, id string) (*gmail.Message, error) {
	log.Info(ctx, "gmailclient.GetMessage", zap.String("msgId", id))
	res, err := s.service.Users.Messages.Get(me, id).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)
	}

	return res, err
}

func (s *Service) ListHistory(ctx context.Context, lastHistoryID uint64) (*gmail.ListHistoryResponse, error) {
	log.Info(ctx, "gmailclient.ListHistory", zap.Uint64("lastHistoryId", lastHistoryID))
	res, err := s.service.Users.History.List(me).StartHistoryId(lastHistoryID).MaxResults(500).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)
	}
	return res, err
}

func (s *Service) WatchInbox(ctx context.Context, req *gmail.WatchRequest) (*gmail.WatchResponse, error) {
	log.Info(ctx, "gmailclient.WatchInbox", zap.Any("request", req))
	res, err := s.service.Users.Watch(me, req).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)
	}

	return res, err
}

func (s *Service) StopWatchingInbox(ctx context.Context) error {
	log.Info(ctx, "gmailclient.StopWatchingInbox")
	err := s.service.Users.Stop(me).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, http.StatusOK)
	}

	return err
}

// SendMessage sends a generated email, including attachments if any specified.
// The generated email must be populated with the following fields:
//   - To
//   - Subject
//   - Body (HTML only)
//   - From (optional)
//
// If "From" is not specified, then the email is sent from the user authenticated in this instance of Service.
// If "From" is specified, then the Gmail API tries to send from the corresponding alias and throws an error if
// such an alias does not exist.
//
// Upon success, the generated email is updated with the following fields:
// - ExternalID
// - ThreadID (the same as genEmail if a reply to an existing thread)
// - SentAt
// - Status
//
// TODO: Handle large attachments using resumable uploads
// https://developers.google.com/workspace/gmail/api/guides/uploads#multipart
func (s *Service) SendMessage(
	ctx context.Context,
	genEmail *models.GeneratedEmail,
	signatureHTML string,
	newAttachments []NewAttachment,
) (*gmail.Message, error) {

	var msgBuffer bytes.Buffer
	mpWriter := multipart.NewWriter(&msgBuffer)

	header := map[string]string{
		"From":         genEmail.Sender,
		"To":           strings.Join(genEmail.Recipients, ", "),
		"Subject":      genEmail.Subject,
		"MIME-Version": "1.0",
		"Content-Type": fmt.Sprintf(`multipart/mixed; boundary="%s"`, mpWriter.Boundary()),
	}

	if len(genEmail.CC) > 0 {
		header["Cc"] = strings.Join(genEmail.CC, ", ")
	}

	// // Add threading headers if this is a reply
	// if genEmail.ThreadReferences != "" {
	// 	header["References"] = genEmail.ThreadReferences
	// 	if len(genEmail.InReplyTo) > 0 {
	// 		header["In-Reply-To"] = genEmail.InReplyTo[0] // Use the first message ID in the array
	// 	}
	// }

	for k, v := range header {
		fmt.Fprintf(&msgBuffer, "%s: %s\r\n", k, v)
	}
	msgBuffer.WriteString("\r\n") // End of headers

	bodyPart, err := mpWriter.CreatePart(textproto.MIMEHeader{
		"Content-Type":              []string{"text/html; charset=UTF-8"},
		"Content-Transfer-Encoding": []string{"quoted-printable"},
	})
	if err != nil {
		return nil, fmt.Errorf("error creating body part: %w", err)
	}
	qpWriter := quotedprintable.NewWriter(bodyPart)
	_, err = qpWriter.Write([]byte(genEmail.Body + "<br><br>" + signatureHTML))
	if err != nil {
		return nil, fmt.Errorf("error writing body: %w", err)
	}
	qpWriter.Close()

	for _, att := range genEmail.Attachments {
		partHeader := textproto.MIMEHeader{
			"Content-Type":              []string{att.MimeType},
			"Content-Disposition":       []string{fmt.Sprintf(`attachment; filename="%s"`, att.OriginalFileName)},
			"Content-Transfer-Encoding": []string{"base64"},
		}

		part, err := mpWriter.CreatePart(partHeader)
		if err != nil {
			return nil, err
		}

		encoder := base64.NewEncoder(base64.StdEncoding, part)
		if getErr := s.getAttachment(ctx, att, encoder); getErr != nil {
			_ = encoder.Close()
			return nil, fmt.Errorf(
				"processing existing attachment '%s': %w",
				att.OriginalFileName,
				getErr,
			)
		}

		if closeErr := encoder.Close(); closeErr != nil {
			return nil, fmt.Errorf(
				"failed to finalize existing attachment part for '%s': %w",
				att.OriginalFileName,
				closeErr,
			)
		}
	}

	// Process new attachments
	for _, newAtt := range newAttachments {
		if err := s.addNewAttachment(ctx, mpWriter, newAtt); err != nil {
			return nil, err
		}
	}

	mpWriter.Close()

	raw := base64.URLEncoding.EncodeToString(msgBuffer.Bytes())

	msg := &gmail.Message{Raw: raw}
	res, err := s.service.Users.Messages.Send("me", msg).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)

		genEmail.ExternalID = res.Id
		genEmail.ThreadID = res.ThreadId
		genEmail.Status = models.SentStatus
		genEmail.SentAt = models.ToValidNullTime(helpers.Or(time.UnixMilli(res.InternalDate), time.Now()))

		// Re-fetch message object to get RFC ID
		msg, err := s.GetMessage(ctx, res.Id)
		if err != nil {
			log.Warn(ctx, "error re-fetching newly sent message", zap.Error(err))
			return res, nil
		}
		genEmail.RFCMessageID = getHeaderValue(msg.Payload.Headers, "Message-ID")
		genEmail.SentAt = models.ToValidNullTime(helpers.Or(time.UnixMilli(res.InternalDate), time.Now()))
	}

	return res, err
}

// getAttachment streams an attachment from Gmail API into an io.Writer
func (s *Service) getAttachment(ctx context.Context, att models.Attachment, writer io.Writer) error {
	res, err := s.service.Users.Messages.Attachments.Get("me", att.MessageExternalID, att.ExternalID).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("failed to download attachment: %w", err)
	}

	data, err := base64.URLEncoding.DecodeString(res.Data)
	if err != nil {
		return fmt.Errorf("failed to decode attachment data: %w", err)
	}
	log.Info(ctx, "downloaded attachment",
		zap.String("attachmentID", att.ExternalID),
		zap.String("name", att.OriginalFileName),
		zap.Int("sizeBytes", int(res.Size)),
	)

	if _, err := writer.Write(data); err != nil {
		return fmt.Errorf("failed to write attachment data: %w", err)
	}

	return nil
}

// Adds a single new attachment to the multipart writer
func (s *Service) addNewAttachment(ctx context.Context, mpWriter *multipart.Writer, newAtt NewAttachment) error {
	partHeader := textproto.MIMEHeader{
		"Content-Type":              []string{newAtt.MimeType},
		"Content-Disposition":       []string{fmt.Sprintf(`attachment; filename="%s"`, newAtt.FileName)},
		"Content-Transfer-Encoding": []string{"base64"}, // The data will be written to encoder which handles base64
	}

	part, err := mpWriter.CreatePart(partHeader)
	if err != nil {
		return fmt.Errorf("error creating MIME part for new attachment '%s': %w", newAtt.FileName, err)
	}

	// Some base64 data from frontend might contain a prefix like "data:application/pdf;base64,"
	// Remove it if present
	base64Data := newAtt.Data
	if idx := strings.Index(base64Data, ";base64,"); idx > 0 {
		base64Data = base64Data[idx+8:]
		log.Info(ctx, "removed base64 prefix", zap.String("fileName", newAtt.FileName))
	}

	decodedData, decErr := base64.StdEncoding.DecodeString(base64Data)
	if decErr != nil {
		// Try URLEncoding as fallback
		decodedData, decErr = base64.URLEncoding.DecodeString(base64Data)
		if decErr != nil {
			return fmt.Errorf("failed to decode Base64Data for new attachment '%s': %w", newAtt.FileName, decErr)
		}
		log.Info(ctx, "used URL encoding for base64 decode", zap.String("fileName", newAtt.FileName))
	}

	if len(decodedData) == 0 {
		return fmt.Errorf("decoded to empty data for attachment '%s'", newAtt.FileName)
	}

	encoder := base64.NewEncoder(base64.StdEncoding, part)
	defer encoder.Close()

	bytesWritten, writeErr := encoder.Write(decodedData)
	if writeErr != nil {
		return fmt.Errorf("failed to write data for new attachment '%s': %w", newAtt.FileName, writeErr)
	}

	log.Info(
		ctx,
		"processed new attachment from Base64Data",
		zap.String("fileName", newAtt.FileName),
		zap.Int("decodedSizeBytes", len(decodedData)),
		zap.Int("bytesWritten", bytesWritten),
	)

	return nil
}

// Returns list of message IDs from inbox since a certain date (inclusive)
func (s *Service) ListMessagesAfterDate(ctx context.Context, startDate time.Time) (msgIDs []string, err error) {
	return s.listMessages(ctx, startDate, nil)
}

func (s *Service) ListMessagesBetweenDates(
	ctx context.Context,
	startDate time.Time,
	endDate time.Time,
) (msgIDs []string, err error) {
	return s.listMessages(ctx, startDate, &endDate)
}

func (s *Service) listMessages(
	ctx context.Context,
	startDate time.Time,
	endDate *time.Time,
) (msgIDs []string, err error) {
	logFields := []zap.Field{zap.String("startDate", startDate.String())}
	queryParts := []string{fmt.Sprintf("after:%d", startDate.Unix())}

	if endDate != nil {
		logFields = append(logFields, zap.String("endDate", endDate.String()))
		queryParts = append(queryParts, fmt.Sprintf("before:%d", endDate.Unix()))
	}

	log.Info(
		ctx,
		"gmailclient.listMessages",
		logFields...,
	)

	var pageToken string
	var page uint

	for {
		req := s.service.Users.Messages.List(me).MaxResults(500).
			Q(strings.Join(queryParts, " "))

		if pageToken != "" {
			req.PageToken(pageToken)
		}

		resp, err := req.Context(ctx).Do()
		if err != nil {
			s.logGoogleError(ctx, err)
			return nil, fmt.Errorf("error getting page %d (token = %s): %w", page, pageToken, err)
		}
		httplog.LogHTTPResponseCode(ctx, s.integration, http.StatusOK)

		for _, msg := range resp.Messages {
			msgIDs = append(msgIDs, msg.Id)
		}

		if resp.NextPageToken == "" {
			break
		}

		pageToken = resp.NextPageToken
		page++

	}

	return msgIDs, nil
}

func (s *Service) GetAttachment(ctx context.Context, msgID, id string) (*gmail.MessagePartBody, error) {
	res, err := s.service.Users.Messages.Attachments.Get(me, msgID, id).Context(ctx).Do()

	if err != nil {
		s.logGoogleError(ctx, err)
	} else {
		httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)
	}

	return res, err
}

// GetSignature returns the alias's signature from API if it exists; alias can simply be the user's email address,
// which Gmail considers the primary email address.
// If there's a transient error, it returns the signature from the DB, if it exists.
// Users should call this function BEFORE sending a message to ensure the signature is set.
//
// NOTE: Outlook client often strips images from incoming Gmail messages, even when email is constructed in web app.
func (s *Service) GetSignature(ctx context.Context, aliasEmail string) (string, error) {
	user := s.GetAuthenticatedUser()
	var signature string

	settings, err := s.service.Users.Settings.SendAs.Get(me, aliasEmail).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)

		if aliasEmail != user.GetEmailAddress() {
			return "", err
		}

		// If alias is the user's primary email, fall back to the signature in DB
		signature = user.GetEmailSignature()
		if signature != "" {
			log.Info(ctx, "falling back to DB signature")

			return signature, nil
		}

		return "", err
	}

	// NOTE: Any image in Gmail signature is already public so we don't need to host in S3 ourselves, unlike Outlook
	signature = settings.Signature
	// If signature is not set in DB, set it to the fetched signature
	if signature != user.GetEmailSignature() && aliasEmail == user.GetEmailAddress() {
		log.Info(ctx, "DB signature out of date, updating signature in DB")

		user.SetEmailSignature(signature)
		if err := useraccessor.Update(ctx, user); err != nil {
			log.WarnNoSentry(ctx, "error updating user signature in DB", zap.Error(err))
		}
	}

	return signature, nil
}

func (s *Service) GetAliases(ctx context.Context) ([]string, error) {
	aliases, err := s.service.Users.Settings.SendAs.List(me).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)

		return nil, err
	}

	var emails []string
	for _, alias := range aliases.SendAs {
		emails = append(emails, alias.SendAsEmail)
	}
	return emails, nil
}

// ForwardMessage forwards an existing email with proper RFC headers and MIME structure.
// It preserves the original email's attachments and adds a custom message if provided.
//
// NOTE: addFwdPrefix should be true only the first time a message in a thread is being forwarded.
// Subsequent forwards for that same threadshould set addFwdPrefix to false, per Gmail web client behavior.
//
// Example 1: New message subject "Hello World"
// Outcome: Forwarding recipient sees: "Fwd: Hello World"
//
// Example 2: Forwarding a message previously forwarded outside of Drumkit, subject is "Fwd: Hello World"
// Outcome: Forwarding recipient sees: "Fwd: Fwd: Hello World"
//
// Example 3: Forwarding a reply in a thread that was previously forwarded by Drumkit,
// subject for thread is currently "Fwd: Hello World"
// Outcome: Forwarding recipient still sees: "Fwd: Hello World", *not* "Fwd: Fwd: Hello World"
// (e.g. models.EmailForwardingRule.ForwardSubsequentEmailsInThread = true)
func (s *Service) ForwardMessage(
	ctx context.Context,
	originalMsgID string,
	genEmail *models.GeneratedEmail,
	useSenderSignature bool,
	addFwdPrefix bool,
) (*gmail.Message, error) {
	signatureHTML := ""
	var err error

	ctx, metaSpan := otel.StartSpan(ctx, "gmailclient.ForwardMessage", nil)
	defer func() {
		metaSpan.End(err)
	}()

	if useSenderSignature {
		signatureHTML, err = s.GetSignature(ctx, s.GetAuthenticatedUser().GetEmailAddress())
		if err != nil {
			log.Warn(ctx, "error getting signature, proceeding without it", zap.Error(err))
		}
	}

	// Get the original message
	originalMsg, err := s.GetMessage(ctx, originalMsgID)
	if err != nil {
		return nil, fmt.Errorf("error getting original message: %w", err)
	}

	originalHeadersMap := headersMap(originalMsg.Payload)
	originalSubject := getHeaderValueMap(originalHeadersMap, "Subject")
	originalRFC := getHeaderValueMap(originalHeadersMap, "Message-ID")
	originalFrom := getHeaderValueMap(originalHeadersMap, "From")

	// Recursively extract the HTML and text body, and collect inline images
	htmlBody, textBody, cidToPart := extractBodyAndInlineImages(originalMsg.Payload.Parts)
	if htmlBody == "" && textBody != "" {
		htmlBody = "<pre>" + html.EscapeString(textBody) + "</pre>"
	}
	if htmlBody == "" {
		log.WarnNoSentry(ctx, "originalBody is empty")
	}

	forwardedBlock := buildForwardedMessageBlock(originalMsg, originalMsg.Payload.Headers, htmlBody)

	// --- MIME Structure ---
	// Outer: multipart/related
	//   - Part 1: multipart/alternative
	//       - Part 1.1: text/plain
	//       - Part 1.2: text/html
	//   - Part 2+: inline images

	var msgBuffer bytes.Buffer
	mixedWriter := multipart.NewWriter(&msgBuffer)

	// NOTE: Must use lowercase keys to match result from headersMap()
	newMsgHeaders := map[string]string{}
	newMsgHeaders["from"] = s.GetAuthenticatedUser().GetEmailAddress()
	newMsgHeaders["to"] = strings.Join(genEmail.Recipients, ", ")
	newMsgHeaders["subject"] = helpers.Ternary(addFwdPrefix, "Fwd: "+originalSubject, originalSubject)
	newMsgHeaders["mime-version"] = "1.0"
	newMsgHeaders["content-type"] = fmt.Sprintf(`multipart/related; boundary=\"%s\"`, mixedWriter.Boundary())
	newMsgHeaders["bcc"] = ""
	if len(genEmail.CC) > 0 {
		newMsgHeaders["cc"] = strings.Join(genEmail.CC, ", ")
	}
	// Build References chain: original References + original Message-ID
	newMsgHeaders["references"] += originalRFC

	if replyTo := getHeaderValueMap(originalHeadersMap, "reply-to"); replyTo != "" {
		newMsgHeaders["reply-to"] = replyTo
	} else {
		newMsgHeaders["reply-to"] = originalFrom
	}

	log.Info(ctx, "newMsgHeaders", zap.Any("newMsgHeaders", newMsgHeaders))

	for k, v := range newMsgHeaders {
		fmt.Fprintf(&msgBuffer, "%s: %s\r\n", k, v)
	}
	msgBuffer.WriteString("\r\n") // End of headers

	// --- multipart/alternative for HTML + text ---
	var altBuffer bytes.Buffer
	altWriter := multipart.NewWriter(&altBuffer)

	textHeader := textproto.MIMEHeader{
		"Content-Type":              []string{"text/plain; charset=\"UTF-8\""},
		"Content-Transfer-Encoding": []string{"quoted-printable"},
	}
	textPart, err := altWriter.CreatePart(textHeader)
	if err != nil {
		return nil, fmt.Errorf("error creating text part: %w", err)
	}

	plainTextContent := buildPlainTextContent(
		genEmail.Body,
		signatureHTML,
		originalMsg.Payload.Headers,
		textBody,
		useSenderSignature,
	)

	textWriter := quotedprintable.NewWriter(textPart)
	if _, err := textWriter.Write([]byte(plainTextContent)); err != nil {
		// Fail-open; plaintext version is non-critical
		log.Warn(ctx, "error writing text part", zap.Error(err))
	}
	textWriter.Close()

	htmlHeader := textproto.MIMEHeader{
		"Content-Type":              []string{"text/html; charset=\"UTF-8\""},
		"Content-Transfer-Encoding": []string{"quoted-printable"},
	}
	htmlPart, err := altWriter.CreatePart(htmlHeader)
	if err != nil {
		return nil, fmt.Errorf("error creating html part: %w", err)
	}
	htmlWriter := quotedprintable.NewWriter(htmlPart)

	fullHTMLBody := ""
	if strings.TrimSpace(genEmail.Body) != "" {
		fullHTMLBody += genEmail.Body + "<br><br>"
	}
	if strings.TrimSpace(forwardedBlock) != "" {
		fullHTMLBody += forwardedBlock
	}
	if strings.TrimSpace(signatureHTML) != "" {
		fullHTMLBody += signatureHTML + "<br><br>"
	}
	_, err = htmlWriter.Write([]byte(fullHTMLBody))
	if err != nil {
		return nil, fmt.Errorf("error writing HTML part: %w", err)
	}
	htmlWriter.Close()

	altWriter.Close()

	// Add the multipart/alternative as the second part of multipart/related
	relatedAltHeader := textproto.MIMEHeader{
		"Content-Type": []string{fmt.Sprintf("multipart/alternative; boundary=\"%s\"", altWriter.Boundary())},
	}
	relatedAltPart, err := mixedWriter.CreatePart(relatedAltHeader)
	if err != nil {
		return nil, fmt.Errorf("error creating alternative part in related: %w", err)
	}
	if _, err := relatedAltPart.Write(altBuffer.Bytes()); err != nil {
		return nil, fmt.Errorf("error writing alternative part to related: %w", err)
	}

	// Add inline images as parts of related with attachment disposition
	for cid, part := range cidToPart {
		var imageData []byte
		if part.Body.Data != "" {
			imageData, err = base64.URLEncoding.DecodeString(part.Body.Data)
			if err != nil {
				return nil, fmt.Errorf("error decoding inline image data: %w", err)
			}
		} else if part.Body.AttachmentId != "" {
			attBody, attErr := s.GetAttachment(ctx, originalMsgID, part.Body.AttachmentId)
			if attErr != nil {
				return nil, fmt.Errorf("error fetching inline image attachment: %w", attErr)
			}
			imageData, err = base64.URLEncoding.DecodeString(attBody.Data)
			if err != nil {
				return nil, fmt.Errorf("error decoding fetched inline image data: %w", err)
			}
		}

		// Use attachment disposition with filename like Gmail does
		filename := part.Filename
		if filename == "" {
			filename = fmt.Sprintf("image%s.png", cid) // fallback filename
		}

		imageHeader := textproto.MIMEHeader{
			"Content-Type":              []string{fmt.Sprintf("%s; name=\"%s\"", part.MimeType, filename)},
			"Content-Disposition":       []string{fmt.Sprintf("attachment; filename=\"%s\"", filename)},
			"Content-Transfer-Encoding": []string{"base64"},
			"X-Attachment-Id":           []string{cid},
			"Content-ID":                []string{"<" + cid + ">"},
		}
		imagePart, err := mixedWriter.CreatePart(imageHeader)
		if err != nil {
			return nil, fmt.Errorf("error creating inline image part: %w", err)
		}
		encoder := base64.NewEncoder(base64.StdEncoding, imagePart)
		if _, err := encoder.Write(imageData); err != nil {
			return nil, fmt.Errorf("error writing inline image: %w", err)
		}
		encoder.Close()
	}

	// Add non-inline attachments (recursively collected) to multipart/related
	var attachmentParts []*gmail.MessagePart
	collectNonInlineAttachments(originalMsg.Payload.Parts, &attachmentParts)
	for _, part := range attachmentParts {
		var attachmentData []byte
		if part.Body.AttachmentId != "" {
			attBody, err := s.GetAttachment(ctx, originalMsgID, part.Body.AttachmentId)
			if err != nil {
				return nil, fmt.Errorf("error getting attachment: %w", err)
			}
			attachmentData, err = base64.URLEncoding.DecodeString(attBody.Data)
			if err != nil {
				return nil, fmt.Errorf("error decoding attachment data: %w", err)
			}
		} else if part.Body.Data != "" {
			var err error
			attachmentData, err = base64.URLEncoding.DecodeString(part.Body.Data)
			if err != nil {
				return nil, fmt.Errorf("error decoding attachment inline data: %w", err)
			}
		}

		attachmentHeader := textproto.MIMEHeader{
			"Content-Type":              []string{fmt.Sprintf("%s; name=\"%s\"", part.MimeType, part.Filename)},
			"Content-Disposition":       []string{fmt.Sprintf(`attachment; filename="%s"`, part.Filename)},
			"Content-Transfer-Encoding": []string{"base64"},
		}
		attachmentPart, err := mixedWriter.CreatePart(attachmentHeader)
		if err != nil {
			return nil, fmt.Errorf("error creating attachment part: %w", err)
		}
		encoder := base64.NewEncoder(base64.StdEncoding, attachmentPart)
		if _, err := encoder.Write(attachmentData); err != nil {
			return nil, fmt.Errorf("error writing attachment: %w", err)
		}
		encoder.Close()
	}

	mixedWriter.Close()

	// Base64 encode the entire message
	rawMsg := base64.URLEncoding.EncodeToString(msgBuffer.Bytes())

	msg := &gmail.Message{Raw: rawMsg}
	res, err := s.service.Users.Messages.Send("me", msg).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)
		return res, err
	}
	httplog.LogHTTPResponseCode(ctx, s.integration, res.HTTPStatusCode)

	genEmail.ExternalID = res.Id
	genEmail.ThreadID = res.ThreadId
	genEmail.Status = models.SentStatus
	genEmail.Body = fullHTMLBody
	genEmail.SentAt = models.ToValidNullTime(time.Now())
	genEmail.Subject = getHeaderValueMap(newMsgHeaders, "subject")
	genEmail.ThreadReferences = getHeaderValueMap(newMsgHeaders, "references")
	genEmail.InReplyTo = []string{getHeaderValueMap(newMsgHeaders, "in-reply-to")}
	genEmail.Sender = getHeaderValueMap(newMsgHeaders, "from")
	genEmail.Recipients = strings.Split(getHeaderValueMap(newMsgHeaders, "to"), ",")
	genEmail.CC = strings.Split(getHeaderValueMap(newMsgHeaders, "cc"), ",")

	// Re-fetch message object to get final headers & values
	resMsgObj, err := s.GetMessage(ctx, res.Id)
	if err != nil {
		log.Warn(ctx, "error re-fetching newly sent message", zap.Error(err))
		return res, nil
	}

	finalHeaders := headersMap(resMsgObj.Payload)
	genEmail.Subject = getHeaderValueMap(finalHeaders, "subject")
	genEmail.ThreadReferences = getHeaderValueMap(finalHeaders, "references")
	genEmail.InReplyTo = []string{getHeaderValueMap(finalHeaders, "in-reply-to")}
	genEmail.Sender = getHeaderValueMap(finalHeaders, "from")
	genEmail.Recipients = []string{getHeaderValueMap(finalHeaders, "to")}
	genEmail.CC = []string{getHeaderValueMap(finalHeaders, "cc")}
	genEmail.RFCMessageID = getHeaderValue(resMsgObj.Payload.Headers, "message-id")
	genEmail.SentAt = models.ToValidNullTime(helpers.Or(time.UnixMilli(resMsgObj.InternalDate), time.Now()))

	return res, err
}

func (s *Service) AddLabels(
	ctx context.Context,
	msgID string,
	labels []string,
) error {
	log.Info(
		ctx,
		"gmailclient.AddLabels",
		zap.String("msgID", msgID),
		zap.Strings("labels", labels),
	)

	resp, err := s.service.Users.Labels.List("me").Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)
		return err
	}

	labelMap := make(map[string]string)
	for _, label := range resp.Labels {
		labelMap[label.Name] = label.Id
	}

	labelIDs := []string{}
	missingLabels := []string{}
	for _, label := range labels {
		if id, ok := labelMap[label]; ok {
			labelIDs = append(labelIDs, id)
		} else {
			missingLabels = append(missingLabels, label)
		}
	}

	// Create missing labels
	for _, missing := range missingLabels {
		log.WarnNoSentry(ctx, "label does not exist, creating", zap.String("label", missing))
		created, createErr := s.service.Users.Labels.Create(me, &gmail.Label{Name: missing}).Context(ctx).Do()
		if createErr != nil {
			log.WarnNoSentry(ctx, "error creating label", zap.String("label", missing), zap.Error(createErr))
			s.logGoogleError(ctx, createErr)

			continue
		}
		labelIDs = append(labelIDs, created.Id)
		labelMap[missing] = created.Id
	}

	_, err = s.service.Users.Messages.Modify(
		me,
		msgID,
		&gmail.ModifyMessageRequest{AddLabelIds: labelIDs},
	).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)
		return err
	}

	return nil
}

// ArchiveMessage removes the INBOX label from a message, effectively archiving it in Gmail.
func (s *Service) ArchiveMessage(ctx context.Context, msgID string) error {
	log.Info(
		ctx,
		"gmailclient.ArchiveMessage",
		zap.String("msgID", msgID),
	)
	_, err := s.service.Users.Messages.Modify(
		me,
		msgID,
		&gmail.ModifyMessageRequest{RemoveLabelIds: []string{"INBOX"}},
	).Context(ctx).Do()
	if err != nil {
		s.logGoogleError(ctx, err)
		return err
	}
	return nil
}

// isInlineImagePart checks if a part is an inline image (has Content-ID and disposition inline or empty)
func isInlineImagePart(part *gmail.MessagePart) bool {
	var cid string
	var disp string
	for _, h := range part.Headers {
		if strings.EqualFold(h.Name, "Content-ID") {
			cid = strings.Trim(h.Value, "<>")
		}
		if strings.EqualFold(h.Name, "Content-Disposition") {
			disp = h.Value
		}
	}
	return cid != "" && (disp == "" || strings.Contains(strings.ToLower(disp), "inline"))
}

// buildForwardedMessageBlock builds the standard forwarded message block for inline forwarding
func buildForwardedMessageBlock(m *gmail.Message, headers []*gmail.MessagePartHeader, originalBody string) string {
	get := func(name string) string {
		for _, h := range headers {
			if strings.EqualFold(h.Name, name) {
				return h.Value
			}
		}
		return ""
	}
	from := get("From")
	date := time.UnixMilli(m.InternalDate).Format("Mon, Jan 2, 2006 at 3:04 PM MST")
	subject := get("Subject")
	to := get("To")
	cc := get("Cc")

	var b strings.Builder
	b.WriteString(`<div>---------- Forwarded message ---------<br>`)
	if from != "" {
		b.WriteString(fmt.Sprintf("From: %s<br>", html.EscapeString(from)))
	}
	if date != "" {
		b.WriteString(fmt.Sprintf("Date: %s<br>", html.EscapeString(date)))
	}
	if subject != "" {
		b.WriteString(fmt.Sprintf("Subject: %s<br>", html.EscapeString(subject)))
	}
	if to != "" {
		b.WriteString(fmt.Sprintf("To: %s<br>", html.EscapeString(to)))
	}
	if cc != "" {
		b.WriteString(fmt.Sprintf("Cc: %s<br>", html.EscapeString(cc)))
	}
	b.WriteString(`<br>`)
	b.WriteString(originalBody)
	b.WriteString(`</div>`)
	return b.String()
}

// buildForwardedMessageBlockPlainText creates a plain text version of the forwarded message block
func buildForwardedMessageBlockPlainText(headers []*gmail.MessagePartHeader, originalBodyPlaintext string) string {
	get := func(name string) string {
		for _, h := range headers {
			if strings.EqualFold(h.Name, name) {
				return h.Value
			}
		}
		return ""
	}
	from := get("From")
	date := get("Date")
	subject := get("Subject")
	to := get("To")
	cc := get("Cc")

	var b strings.Builder
	b.WriteString("---------- Forwarded message ---------\n")
	if from != "" {
		b.WriteString(fmt.Sprintf("From: %s\n", from))
	}
	if date != "" {
		b.WriteString(fmt.Sprintf("Date: %s\n", date))
	}
	if subject != "" {
		b.WriteString(fmt.Sprintf("Subject: %s\n", subject))
	}
	if to != "" {
		b.WriteString(fmt.Sprintf("To: %s\n", to))
	}
	if cc != "" {
		b.WriteString(fmt.Sprintf("Cc: %s\n", cc))
	}
	b.WriteString("\n\n")
	b.WriteString(originalBodyPlaintext)
	return b.String()
}

// buildPlainTextContent constructs the complete plain text content for the forwarded email
func buildPlainTextContent(
	customMessage string,
	signatureHTML string,
	forwardedHeaders []*gmail.MessagePartHeader,
	originalTextBody string,
	useSenderSignature bool,
) string {
	var parts []string

	// Add custom message if provided
	if customMessage != "" {
		messageText := html2text.HTML2Text(customMessage)
		if messageText != "" {
			parts = append(parts, messageText)
		}
	}

	forwardedBlock := ""
	forwardedBlock = buildForwardedMessageBlockPlainText(forwardedHeaders, originalTextBody)
	parts = append(parts, forwardedBlock)

	if useSenderSignature && signatureHTML != "" {
		signatureText := html2text.HTML2Text(signatureHTML)
		if signatureText != "" {
			parts = append(parts, signatureText)
		}
	}

	return strings.Join(parts, "\n\n")
}

// Helper to build a map of header name to value for a MessagePart
func headersMap(p *gmail.MessagePart) map[string]string {
	hm := make(map[string]string)
	for _, h := range p.Headers {
		hm[strings.ToLower(h.Name)] = h.Value
	}
	return hm
}

// extractBodyAndInlineImages recursively extracts the HTML body (with fallback to plain text)
// and collects inline images by CID
func extractBodyAndInlineImages(
	parts []*gmail.MessagePart,
) (htmlBody, textBody string, cidToPart map[string]*gmail.MessagePart) {
	cidToPart = make(map[string]*gmail.MessagePart)
	for _, part := range parts {
		switch {
		case part.MimeType == "text/html":
			data, err := base64.URLEncoding.DecodeString(part.Body.Data)
			if err == nil {
				htmlBody += string(data)
			}
		case part.MimeType == "text/plain":
			data, err := base64.URLEncoding.DecodeString(part.Body.Data)
			if err == nil {
				textBody += string(data)
			}
		case strings.HasPrefix(part.MimeType, "multipart/"):
			h, t, c := extractBodyAndInlineImages(part.Parts)
			htmlBody += h
			textBody += t
			for k, v := range c {
				cidToPart[k] = v
			}
		default:
			// Check for inline images (Content-ID header and disposition inline)
			var cid string
			var disp string
			for _, h := range part.Headers {
				if strings.EqualFold(h.Name, "Content-ID") {
					cid = strings.Trim(h.Value, "<>")
				}
				if strings.EqualFold(h.Name, "Content-Disposition") {
					disp = h.Value
				}
			}
			if cid != "" { // Only consider parts with a CID
				// Treat as inline when disposition explicitly contains "inline" or is empty (Gmail omits it sometimes)
				if disp == "" || strings.Contains(strings.ToLower(disp), "inline") {
					cidToPart[cid] = part
				}
			}
		}
	}
	return
}

// collectNonInlineAttachments recursively gathers attachment parts that should be forwarded (excluding inline images)
func collectNonInlineAttachments(parts []*gmail.MessagePart, out *[]*gmail.MessagePart) {
	for _, p := range parts {
		if strings.HasPrefix(p.MimeType, "multipart/") {
			collectNonInlineAttachments(p.Parts, out)
			continue
		}

		if isInlineImagePart(p) {
			continue
		}

		disp := headersMap(p)["content-disposition"]
		if p.Filename != "" || strings.Contains(strings.ToLower(disp), "attachment") {
			*out = append(*out, p)
		}
	}
}

// getHeaderValue extracts the value of a header from Gmail message headers
func getHeaderValue(headers []*gmail.MessagePartHeader, name string) string {
	for _, header := range headers {
		if strings.EqualFold(header.Name, name) {
			return header.Value
		}
	}
	return ""
}

func getHeaderValueMap(headers map[string]string, name string) string {
	for k, v := range headers {
		if strings.EqualFold(k, name) {
			return v
		}
	}
	return ""
}
