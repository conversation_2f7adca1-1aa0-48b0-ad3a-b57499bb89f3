// Source: https://github.com/googleapis/google-api-go-client/issues/179#issuecomment-1641490906
package gmailbatch

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"net/url"

	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/option"
	htransport "google.golang.org/api/transport/http"
)

const (
	basePath = "https://gmail.googleapis.com/"
)

func NewService(ctx context.Context, opts ...option.ClientOption) (*Service, error) {
	client, endpoint, err := htransport.NewClient(ctx, opts...)
	if err != nil {
		return nil, err
	}
	s, err := New(client)
	if err != nil {
		return nil, err
	}

	if endpoint != "" {
		s.BasePath = endpoint
	}
	return s, nil
}

func New(client *http.Client) (*Service, error) {
	if client == nil {
		return nil, errors.New("client is nil")
	}
	s := &Service{client: client, BasePath: basePath}
	s.Batch = NewBatchservice(s)
	return s, nil
}

type Service struct {
	client    *http.Client
	BasePath  string
	UserAgent string

	Batch *BatchService
}

func (s *Service) userAgent() string {
	if s.UserAgent != "" {
		return googleapi.UserAgent
	}
	return googleapi.UserAgent + " " + s.UserAgent
}

type BatchService struct {
	s *Service

	Email *BatchEmailService
}

type BatchEmailService struct {
	s *Service
}

func NewBatchservice(s *Service) *BatchService {
	rs := &BatchService{s: s, Email: NewBatchEmailService(s)}
	return rs
}

func NewBatchEmailService(s *Service) *BatchEmailService {
	rs := &BatchEmailService{s: s}
	return rs
}

type BatchGetEmailsRequest struct {
	// A list of UsersMessagesGetCall requests to send to the batch api
	IDs []string `json:"-"`
}

//nolint:containedctx
type BatchEmailsCall struct {
	s                     *Service
	userID                string
	batchgetemailsrequest *BatchGetEmailsRequest
	urlParams             url.Values
	ctx                   context.Context
	header                http.Header
}

func (r *BatchEmailService) Get(userID string, ids []string) *BatchEmailsCall {
	c := &BatchEmailsCall{
		s:                     r.s,
		urlParams:             make(url.Values),
		batchgetemailsrequest: &BatchGetEmailsRequest{IDs: ids},
		ctx:                   context.Background(),
	}
	c.userID = userID
	return c
}

// "full" "raw" "metadata"
func (c *BatchEmailsCall) Format(format string) *BatchEmailsCall {
	c.urlParams.Set("format", format)
	return c
}

func (c *BatchEmailsCall) Fields(s ...googleapi.Field) *BatchEmailsCall {
	c.urlParams.Set("fields", googleapi.CombineFields(s))
	return c
}

func (c *BatchEmailsCall) Context(ctx context.Context) *BatchEmailsCall {
	c.ctx = ctx
	return c
}

func (c *BatchEmailsCall) Header() http.Header {
	if c.header == nil {
		c.header = make(http.Header)
	}
	return c.header
}

func (c *BatchEmailsCall) doRequest() (*http.Response, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	defer writer.Close()

	for _, id := range c.batchgetemailsrequest.IDs {
		part, err := writer.CreatePart(textproto.MIMEHeader{
			"Content-Type": {"application/http"},
			"Content-ID":   {id},
		})
		if err != nil {
			return nil, err
		}

		/* --- making single messages get request --- */
		reqHeaders := make(http.Header)
		for k, v := range c.header {
			reqHeaders[k] = v
		}
		reqHeaders.Set("User-Agent", c.s.userAgent())

		urls := googleapi.ResolveRelative(c.s.BasePath, "gmail/v1/users/{userID}/messages/{id}")
		urls += "?" + c.urlParams.Encode()
		req, err := http.NewRequestWithContext(
			c.ctx,
			http.MethodGet,
			urls,
			nil,
		)
		if err != nil {
			return nil, err
		}
		req.Header = reqHeaders
		googleapi.Expand(req.URL, map[string]string{
			"userID": c.userID,
			"id":     id,
		})
		err = req.Write(part)
		if err != nil {
			return nil, err
		}
	}

	writer.Close()

	batchURL := googleapi.ResolveRelative(c.s.BasePath, "batch/gmail/v1")
	req, err := http.NewRequestWithContext(c.ctx, http.MethodPost, batchURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", fmt.Sprintf("multipart/mixed; boundary=%s", writer.Boundary()))

	return c.s.client.Do(req.WithContext(c.ctx))
}

func (c *BatchEmailsCall) Do() ([]*gmail.Message, error) {
	res, err := c.doRequest()
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, err
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, err
	}

	_, params, err := mime.ParseMediaType(res.Header.Get("Content-Type"))
	if err != nil {
		return nil, err
	}

	mpr := multipart.NewReader(res.Body, params["boundary"])

	ret := make([]*gmail.Message, 0)

	for part, err := mpr.NextPart(); !errors.Is(err, io.EOF); part, err = mpr.NextPart() {
		if msg := getMessage(part); msg != nil {
			ret = append(ret, msg)
		}
	}

	return ret, nil
}

func getMessage(part *multipart.Part) *gmail.Message {
	buf := bufio.NewReader(part)
	resp, err := http.ReadResponse(buf, nil)
	if err != nil {
		log.Printf("error reading response from part: %v", err)
		return nil
	}
	defer resp.Body.Close()

	var message gmail.Message
	if err := json.NewDecoder(resp.Body).Decode(&message); err != nil {
		log.Printf("error reading gmail message from part response: %v", err)
		return nil
	}

	return &message
}
