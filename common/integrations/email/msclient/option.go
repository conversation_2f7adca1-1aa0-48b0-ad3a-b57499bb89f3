package msclient

type (
	EmailContentType string

	Options struct {
		ContentType         EmailContentType
		PreventInfiniteLoop bool
	}

	Option func(*Options)
)

const (
	HTMLContentType      EmailContentType = "html"
	PlaintextContentType EmailContentType = "text"
)

// WithContentType sets the email content type preference we want to receive from Outlook.
func WithContentType(contentType EmailContentType) Option {
	return func(o *Options) {
		o.ContentType = contentType
	}
}

// WithPreventInfiniteLoop prevents an infinite loop when calling a function recursively.
// The initial use case here is to prevent infinite loops when calling GetMessageByID recursively.
func WithPreventInfiniteLoop(b bool) Option {
	return func(o *Options) {
		o.PreventInfiniteLoop = b
	}
}
