package msclient

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/microcosm-cc/bluemonday"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
)

type MockMessage struct {
	Body struct {
		ContentType string
		Content     string
	}
}

type MockOptions struct {
	ContentType string
}

type MockOption func(*MockOptions)

func MockWithContentType(contentType string) MockOption {
	return func(opts *MockOptions) {
		opts.ContentType = contentType
	}
}

const (
	MockHTMLContentType      = "html"
	MockPlaintextContentType = "text"
)

type MockService struct {
	doFunc func(ctx context.Context, method, url string, body, response any, headers http.Header) error
}

func (s *MockService) MockDo(
	ctx context.Context,
	method,
	url string,
	body,
	response any,
	headers http.Header,
) error {

	return s.doFunc(ctx, method, url, body, response, headers)
}

func (s *MockService) MockGetMessageByID(
	ctx context.Context,
	id string,
	opts ...MockOption,
) (msg MockMessage, err error) {

	options := &MockOptions{
		ContentType: MockHTMLContentType,
	}

	for _, opt := range opts {
		opt(options)
	}

	headers := http.Header{"Prefer": []string{`IdType="ImmutableId"`}}

	if options.ContentType == MockPlaintextContentType {
		headers["Prefer"] = append(headers["Prefer"], `outlook.body-content-type="text"`)
	}

	err = s.doFunc(ctx, http.MethodGet, fmt.Sprintf("me/messages/%s", url.PathEscape(id)), nil, &msg, headers)
	if err != nil {
		return msg, fmt.Errorf("client.ByMessageId.Get(%s) failed: %w", id, err)
	}

	switch options.ContentType {
	case MockPlaintextContentType:
		return msg, nil
	case MockHTMLContentType:
		p := bluemonday.UGCPolicy()
		p.AllowElements("body")
		cleanMockHTML := p.Sanitize(msg.Body.Content)

		if !strings.Contains(cleanMockHTML, "<table") {
			return s.MockGetMessageByID(ctx, id, MockWithContentType(MockPlaintextContentType))
		}
		msg.Body.Content = cleanMockHTML
		return msg, nil
	default:
		return msg, fmt.Errorf("unsupported content type: %s", options.ContentType)
	}
}

func containsString(slice []string, s string) bool {
	for _, item := range slice {
		if item == s {
			return true
		}
	}
	return false
}

// TestService_GetMessageByID_MockPlaintext tests when ContentType is MockPlaintextContentType
func TestService_GetMessageByID_MockPlaintext(t *testing.T) {
	s := &MockService{}

	s.doFunc = func(_ context.Context, _, _ string, _, response any, headers http.Header) error {
		preferHeader := headers["Prefer"]
		if !containsString(preferHeader, `IdType="ImmutableId"`) {
			t.Error("Prefer header missing IdType=\"ImmutableId\"")
		}
		if !containsString(preferHeader, `outlook.body-content-type="text"`) {
			t.Error("Prefer header missing outlook.body-content-type=\"text\"")
		}

		msg := response.(*MockMessage)
		msg.Body.ContentType = MockPlaintextContentType
		msg.Body.Content = "This is plaintext content."
		return nil
	}

	msg, err := s.MockGetMessageByID(context.Background(), "12345", MockWithContentType(MockPlaintextContentType))
	if err != nil {
		t.Fatalf("GetMessageByID failed: %v", err)
	}

	expectedContent := "This is plaintext content."
	if msg.Body.Content != expectedContent {
		t.Errorf("Expected content %q, got %q", expectedContent, msg.Body.Content)
	}
}

// TestService_GetMessageByID_MockHTMLWithTable tests when ContentType is MockHTMLContentType
// and the sanitized MockHTML contains a table
func TestService_GetMessageByID_MockHTMLWithTable(t *testing.T) {
	s := &MockService{}

	s.doFunc = func(_ context.Context, _, _ string, _, response any, headers http.Header) error {
		preferHeader := headers["Prefer"]
		if !containsString(preferHeader, `IdType="ImmutableId"`) {
			t.Error("Prefer header missing IdType=\"ImmutableId\"")
		}
		if containsString(preferHeader, `outlook.body-content-type="text"`) {
			t.Error("Prefer header should not contain outlook.body-content-type=\"text\"")
		}

		msg := response.(*MockMessage)
		msg.Body.ContentType = MockHTMLContentType
		msg.Body.Content = `<html><body><table><tr><td>Data</td></tr></table></body></html>`
		return nil
	}

	msg, err := s.MockGetMessageByID(context.Background(), "12345", MockWithContentType(MockHTMLContentType))
	if err != nil {
		t.Fatalf("GetMessageByID failed: %v", err)
	}

	expectedContent := `<body><table><tr><td>Data</td></tr></table></body>`
	if msg.Body.Content != expectedContent {
		t.Errorf("Expected sanitized content %q, got %q", expectedContent, msg.Body.Content)
	}
}

// TestService_GetMessageByID_MockHTMLWithoutTable tests when ContentType is MockHTMLContentType
// but sanitized MockHTML does not contain a table
func TestService_GetMessageByID_MockHTMLWithoutTable(t *testing.T) {
	s := &MockService{}
	callCount := 0

	s.doFunc = func(_ context.Context, _, _ string, _, response any, headers http.Header) error {
		callCount++
		msg := response.(*MockMessage)

		switch callCount {
		case 1:
			preferHeader := headers["Prefer"]
			if !containsString(preferHeader, `IdType="ImmutableId"`) {
				t.Error("First call: Prefer header missing IdType=\"ImmutableId\"")
			}
			if containsString(preferHeader, `outlook.body-content-type="text"`) {
				t.Error("First call: Prefer header should not contain outlook.body-content-type=\"text\"")
			}

			msg.Body.ContentType = MockHTMLContentType
			msg.Body.Content = `<html><body><p>No table here</p></body></html>`
		case 2:
			preferHeader := headers["Prefer"]
			if !containsString(preferHeader, `IdType="ImmutableId"`) {
				t.Error("Second call: Prefer header missing IdType=\"ImmutableId\"")
			}
			if !containsString(preferHeader, `outlook.body-content-type="text"`) {
				t.Error("Second call: Prefer header missing outlook.body-content-type=\"text\"")
			}

			msg.Body.ContentType = MockPlaintextContentType
			msg.Body.Content = "This is plaintext content."
		default:
			t.Fatal("doFunc called more than twice")
		}

		return nil
	}

	msg, err := s.MockGetMessageByID(context.Background(), "12345", MockWithContentType(MockHTMLContentType))
	if err != nil {
		t.Fatalf("GetMessageByID failed: %v", err)
	}

	expectedContent := "This is plaintext content."
	if msg.Body.Content != expectedContent {
		t.Errorf("Expected content %q, got %q", expectedContent, msg.Body.Content)
	}
}

// TestService_GetMessageByID_Error tests when s.doFunc returns an error
func TestService_GetMessageByID_Error(t *testing.T) {
	s := &MockService{}

	s.doFunc = func(_ context.Context, _, _ string, _, _ any, _ http.Header) error {
		return errors.New("network error")
	}

	_, err := s.MockGetMessageByID(context.Background(), "12345", MockWithContentType(MockPlaintextContentType))
	if err == nil {
		t.Fatal("Expected error, got nil")
	}

	expectedErrorMsg := "client.ByMessageId.Get(12345) failed: network error"
	if err.Error() != expectedErrorMsg {
		t.Errorf("Expected error message %q, got %q", expectedErrorMsg, err.Error())
	}
}

// To run this test, ensure that '<EMAIL>' exists in your test DB with a valid refresh token.
// To onboard a new Outlook user, see ./README.md
// More info on tokens: https://learn.microsoft.com/en-us/entra/identity-platform/refresh-tokens#token-lifetime
func TestLiveConcurrentClients(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping TestLiveConcurrentClients: run with LIVE_TEST=true to enable")
		return
	}
	ctx := context.Background()

	if err := godotenv.Load(); err != nil {
		t.Log("info: no .env file found")
	}

	if os.Getenv("DB_HOST") == "" {
		t.Skip("missing DB vars, skipping test")
	}

	err := rds.OpenDirect(ctx,
		rds.WithDBHost(os.Getenv("DB_HOST")),
		rds.WithDBName(os.Getenv("DB_NAME")),
		rds.WithDBCredentials(os.Getenv("DB_USER"), os.Getenv("DB_PASSWORD")),
		rds.WithSSLMode("disable"),
		rds.WithGormConfig(&gorm.Config{PrepareStmt: true}))
	require.NoError(t, err)

	user, err := userDB.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err)

	var calls int
	var prevToken string
	var prevExpiry time.Time
	oauth.UpdateUserFunc = func(
		_ context.Context,
		updatedUser models.User,
	) error {

		calls++

		assert.NotEqual(t, prevToken, updatedUser.EncryptedAccessToken)
		prevToken = updatedUser.EncryptedAccessToken

		assert.False(t, updatedUser.TokenExpiry.Equal(prevExpiry))
		prevExpiry = updatedUser.TokenExpiry

		return nil
	}

	// Force expired token
	user.TokenExpiry = time.Now().Add(-10 * time.Minute)

	clientMap := make(map[int]Client, 3)

	for i := 0; i < 3; i++ {
		msClient, err := New(
			ctx,
			os.Getenv("MICROSOFT_CLIENT_ID"),
			os.Getenv("MICROSOFT_CLIENT_SECRET"),
			// User's encrypted tokens should've been generated with dummy value since env is dev.
			// If not, change it to actual AES key
			&user,
		)
		require.NoError(t, err)
		clientMap[i] = msClient

		// Force token refresh by calling client
		_, err = msClient.GetMessageByID(ctx, "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0AclAAA")
		assert.NoError(t, err)
	}

	// Verify that first client isn't invalidated by new ones
	// If test is throwing an error, verify this message wasn't deleted
	_, err = clientMap[0].GetMessageByID(ctx, "AAkALgAAAAAAHYQDEapmEc2byACqAC-EWg0AZvA5vWk2tUSpWyPAvF3g7QAAF0AclAAA")
	require.NoError(t, err)
	assert.Equal(t, 3, calls)
}
