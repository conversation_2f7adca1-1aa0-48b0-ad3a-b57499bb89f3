package redis

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
)

var RDB *redis.Client

func Init(ctx context.Context, redisURL string) error {
	options, err := redis.ParseURL(redisURL)
	if err != nil {
		return fmt.Errorf("failed to parse redisURL '%s': %w", redisURL, err)
	}

	RDB = redis.NewClient(options)

	log.Infof(ctx, "initialized redis client: %s", redisURL)

	return nil
}

func GetKey[T any](ctx context.Context, key string) (T, bool, error) {
	var result T
	if RDB == nil {
		return result, false, nil
	}

	val, err := RDB.Get(ctx, key).Result()

	if err != nil {
		if errors.Is(err, redis.Nil) {
			log.Debug(ctx, "No Redis key found for", zap.String("key", key))
		} else {
			log.Info(ctx, "Error getting key from Red<PERSON>", zap.String("key", key))
		}
		return result, false, err
	}

	err = json.Unmarshal([]byte(val), &result)
	if err != nil {
		log.Info(ctx, "Couldn't unmarshal data from Redis for", zap.String("key", key), zap.String("val", val))
		RDB.Del(ctx, key)
		return result, false, err
	}

	return result, true, nil
}

func SetKey[T any](ctx context.Context, key string, data T, expiration time.Duration) error {
	if RDB == nil {
		return nil
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		err = fmt.Errorf("failed to marshal bytes for redis key %s: %w", key, err)
		sentry.GetHubFromContext(ctx).CaptureException(err)
		return err
	}

	err = RDB.Set(ctx, key, jsonData, expiration).Err()
	if err != nil {
		err = fmt.Errorf("failed to set redis key %s: %w", key, err)
		sentry.GetHubFromContext(ctx).CaptureException(err)
		return err
	}

	return nil
}

// SetKeyWithRetries sets a key in Redis with exponential backoff.
// It retries up to 3 times with increasing delays (200ms, 400ms, 800ms).
func SetKeyWithRetries[T any](ctx context.Context, key string, data T, expiration time.Duration) (err error) {
	for i := 0; i < 3; i++ {
		err = SetKey(ctx, key, data, expiration)
		if err == nil {
			return nil
		}

		// Exponential backoff starting at 200ms
		time.Sleep(time.Duration(200*(1<<i)) * time.Millisecond)

		log.WarnNoSentry(
			ctx,
			"failed to set redis key",
			zap.String("key", key),
			zap.Error(err),
			zap.Int("attempt", i+1),
		)
	}

	return fmt.Errorf("failed to set redis key after 3 attempts: %w", err)
}

func DeleteKey(ctx context.Context, key string) error {
	if RDB == nil {
		return nil
	}

	cmd := RDB.Del(ctx, key)
	if err := cmd.Err(); err != nil {
		wrappedErr := fmt.Errorf("failed to execute delete command for redis key %s: %w", key, err)
		log.Error(ctx, "failed to execute delete command for redis key", zap.Error(wrappedErr))
		return wrappedErr
	}

	if cmd.Val() == 0 { // Check if any keys were actually deleted
		log.Info(ctx, "Redis key not found for deletion", zap.String("key", key))
		return NilEntry // Return redis.Nil if key was not found
	}

	return nil
}

var NilEntry = redis.Nil

// IncrementKey increments a key and sets expiry on first increment
func IncrementKey(ctx context.Context, key string, expiration time.Duration) (int64, error) {
	if RDB == nil {
		return 0, errors.New("redis client not initialized")
	}

	incrementedCount, err := RDB.Incr(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to increment key %s: %w", key, err)
	}

	if incrementedCount == 1 {
		err = RDB.Expire(ctx, key, expiration).Err()
		if err != nil {
			return 0, fmt.Errorf("failed to set expiry for key %s: %w", key, err)
		}
	}

	return incrementedCount, nil
}

// GetKeyExpiry returns the remaining TTL for a key in seconds
func GetKeyExpiry(ctx context.Context, key string) (time.Duration, error) {
	if RDB == nil {
		return 0, errors.New("redis client not initialized")
	}

	ttl, err := RDB.TTL(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get TTL for key %s: %w", key, err)
	}

	return ttl, nil
}

// SetIfNotExists sets a key only if it does not already exist as an atomic operation
// Returns true if the key was set, false if it already existed
func SetIfNotExists(
	ctx context.Context,
	key string,
	value string,
	expiration time.Duration,
) (bool, error) {
	if RDB == nil {
		return false, errors.New("redis client not initialized")
	}

	acquired, err := RDB.SetNX(ctx, key, value, expiration).Result()
	if err != nil {
		return false, fmt.Errorf("failed to set key %s with SetNX: %w", key, err)
	}

	return acquired, nil
}

// HashGet retrieves a field from a Redis hash with automatic JSON unmarshaling
func HashGet[T any](ctx context.Context, key string, field string) (T, bool, error) {
	var result T
	if RDB == nil {
		return result, false, nil
	}

	val, err := RDB.HGet(ctx, key, field).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			log.Debug(ctx, "No Redis hash field found",
				zap.String("key", key),
				zap.String("field", field))
		} else {
			log.Info(ctx, "Error getting hash field from Redis",
				zap.String("key", key),
				zap.String("field", field),
				zap.Error(err))
		}
		return result, false, err
	}

	err = json.Unmarshal([]byte(val), &result)
	if err != nil {
		log.Info(ctx, "Couldn't unmarshal hash field data from Redis",
			zap.String("key", key),
			zap.String("field", field),
			zap.String("val", val),
			zap.Error(err))
		return result, false, err
	}

	return result, true, nil
}

// HashSet stores a field in a Redis hash with automatic JSON marshaling
func HashSet[T any](ctx context.Context, key string, field string, data T) error {
	if RDB == nil {
		return nil
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		err = fmt.Errorf("failed to marshal bytes for redis hash %s field %s: %w", key, field, err)
		sentry.GetHubFromContext(ctx).CaptureException(err)
		return err
	}

	err = RDB.HSet(ctx, key, field, jsonData).Err()
	if err != nil {
		err = fmt.Errorf("failed to set redis hash %s field %s: %w", key, field, err)
		sentry.GetHubFromContext(ctx).CaptureException(err)
		return err
	}

	return nil
}

// HashGetString retrieves a string field from a Redis hash (no JSON unmarshaling)
func HashGetString(ctx context.Context, key string, field string) (string, bool, error) {
	if RDB == nil {
		return "", false, nil
	}

	val, err := RDB.HGet(ctx, key, field).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			log.Debug(ctx, "No Redis hash field found",
				zap.String("key", key),
				zap.String("field", field))
			return "", false, nil
		}
		log.Info(ctx, "Error getting hash field from Redis",
			zap.String("key", key),
			zap.String("field", field),
			zap.Error(err))
		return "", false, err
	}

	return val, true, nil
}

// HashSetString stores a string field in a Redis hash (no JSON marshaling)
func HashSetString(ctx context.Context, key string, field string, value string) error {
	if RDB == nil {
		return nil
	}

	err := RDB.HSet(ctx, key, field, value).Err()
	if err != nil {
		err = fmt.Errorf("failed to set redis hash %s field %s: %w", key, field, err)
		log.Error(ctx, "failed to set redis hash field",
			zap.String("key", key),
			zap.String("field", field),
			zap.Error(err))
		return err
	}

	return nil
}

// HashExists checks if a field exists in a Redis hash
func HashExists(ctx context.Context, key string, field string) (bool, error) {
	if RDB == nil {
		return false, nil
	}

	exists, err := RDB.HExists(ctx, key, field).Result()
	if err != nil {
		log.Info(ctx, "Error checking hash field existence",
			zap.String("key", key),
			zap.String("field", field),
			zap.Error(err))
		return false, err
	}

	return exists, nil
}

// KeyExists checks if a key exists in Redis
func KeyExists(ctx context.Context, key string) (bool, error) {
	if RDB == nil {
		return false, nil
	}

	count, err := RDB.Exists(ctx, key).Result()
	if err != nil {
		log.Info(ctx, "Error checking key existence",
			zap.String("key", key),
			zap.Error(err))
		return false, err
	}

	return count > 0, nil
}
