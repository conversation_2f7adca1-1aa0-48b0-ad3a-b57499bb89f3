package redis

import (
	"context"
	"fmt"
	"testing"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

func TestGetIntegrationState(t *testing.T) {
	ctx := context.Background()
	integrationID := uint(123)
	jobType := "test-job"
	key := fmt.Sprintf("integration-id-%d-%s", integrationID, jobType)

	// Create a mock Redis client
	mockRDB, mock := redismock.NewClientMock()
	originalRDB := RDB
	RDB = mockRDB
	defer func() {
		RDB = originalRDB
		_ = mockRDB.Close()
	}()

	tests := []struct {
		name           string
		setupMock      func(mock redismock.ClientMock)
		expectedTime   string
		expectedCursor string
		expectedErr    error
	}{
		{
			name: "happy path - both values present",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(1)
				mock.ExpectGet(key).SetVal("2024-01-01;cursor123")
			},
			expectedTime:   "2024-01-01",
			expectedCursor: "cursor123",
			expectedErr:    nil,
		},
		{
			name: "key doesn't exist",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(0)
			},
			expectedTime:   "",
			expectedCursor: "",
			expectedErr:    nil,
		},
		{
			name: "only updatedAt present",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(1)
				mock.ExpectGet(key).SetVal("2024-01-01;")
			},
			expectedTime:   "2024-01-01",
			expectedCursor: "",
			expectedErr:    nil,
		},
		{
			name: "only cursor present",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(1)
				mock.ExpectGet(key).SetVal(";cursor123")
			},
			expectedTime:   "",
			expectedCursor: "cursor123",
			expectedErr:    nil,
		},
		{
			name: "invalid state - empty string",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(1)
				mock.ExpectGet(key).SetVal("")
			},
			expectedTime:   "",
			expectedCursor: "",
			expectedErr:    nil,
		},
		{
			name: "invalid state - multiple separators",
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectExists(key).SetVal(1)
				mock.ExpectGet(key).SetVal("time;cursor;extra")
			},
			expectedTime:   "",
			expectedCursor: "",
			expectedErr:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.setupMock(mock)

			// Execute
			updatedAt, cursor, err := GetIntegrationState(ctx, integrationID, jobType)

			// Assert
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedTime, updatedAt)
			assert.Equal(t, tt.expectedCursor, cursor)

			// Verify all redis expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestSetIntegrationState(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		integrationID uint
		jobType       string
		updatedAt     string
		cursor        string
		setupMock     func(mock redismock.ClientMock)
		expectedError bool
	}{
		{
			name:          "successful state update",
			integrationID: 123,
			jobType:       "sync",
			updatedAt:     "2024-03-20T10:00:00Z",
			cursor:        "next_page_token",
			setupMock: func(mock redismock.ClientMock) {
				key := fmt.Sprintf("integration-id-%d-%s", 123, "sync")
				value := "2024-03-20T10:00:00Z" + QueryDataSeparator + "next_page_token"
				mock.ExpectTxPipeline()
				mock.ExpectDel(key).SetVal(1)
				mock.ExpectSet(key, value, 0).SetVal("OK")
				mock.ExpectTxPipelineExec()
			},
			expectedError: false,
		},
		{
			name:          "success - no cursor",
			integrationID: 123,
			jobType:       "sync",
			updatedAt:     "2024-03-20T10:00:00Z",
			cursor:        "",
			setupMock: func(mock redismock.ClientMock) {
				key := fmt.Sprintf("integration-id-%d-%s", 123, "sync")
				value := "2024-03-20T10:00:00Z;"
				mock.ExpectTxPipeline()
				mock.ExpectDel(key).SetVal(1)
				mock.ExpectSet(key, value, 0).SetVal("OK")
				mock.ExpectTxPipelineExec()
			},
			expectedError: false,
		},
		{
			name:          "error on delete",
			integrationID: 456,
			jobType:       "sync",
			updatedAt:     "2024-03-20T10:00:00Z",
			cursor:        "next_page_token",
			setupMock: func(mock redismock.ClientMock) {
				key := fmt.Sprintf("integration-id-%d-%s", 456, "sync")
				mock.ExpectTxPipeline()
				mock.ExpectDel(key).SetErr(redis.ErrClosed)
			},
			expectedError: true,
		},
		{
			name:          "error on set",
			integrationID: 789,
			jobType:       "sync",
			updatedAt:     "2024-03-20T10:00:00Z",
			cursor:        "next_page_token",
			setupMock: func(mock redismock.ClientMock) {
				key := fmt.Sprintf("integration-id-%d-%s", 789, "sync")
				value := "2024-03-20T10:00:00Z" + QueryDataSeparator + "next_page_token"
				mock.ExpectTxPipeline()
				mock.ExpectDel(key).SetVal(1)
				mock.ExpectSet(key, value, 0).SetErr(redis.ErrClosed)
			},
			expectedError: true,
		},
		{
			name:          "error on pipeline exec",
			integrationID: 999,
			jobType:       "sync",
			updatedAt:     "2024-03-20T10:00:00Z",
			cursor:        "next_page_token",
			setupMock: func(mock redismock.ClientMock) {
				key := fmt.Sprintf("integration-id-%d-%s", 999, "sync")
				value := "2024-03-20T10:00:00Z" + QueryDataSeparator + "next_page_token"
				mock.ExpectTxPipeline()
				mock.ExpectDel(key).SetVal(1)
				mock.ExpectSet(key, value, 0).SetVal("OK")
				mock.ExpectTxPipelineExec().SetErr(redis.ErrClosed)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock Redis client
			mockRDB, mock := redismock.NewClientMock()
			originalRDB := RDB
			RDB = mockRDB
			defer func() {
				RDB = originalRDB
				_ = mockRDB.Close()
			}()

			// Setup mock expectations
			tt.setupMock(mock)

			// Test the function
			err := SetIntegrationState(ctx, tt.integrationID, tt.jobType, tt.updatedAt, tt.cursor)

			// Assert results
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all Redis expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
