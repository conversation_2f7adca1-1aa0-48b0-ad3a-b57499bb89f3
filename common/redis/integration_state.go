package redis

import (
	"context"
	"errors"
	"fmt"
	"strings"
)

const (
	LocationJob        = "locations"
	CustomerJob        = "customers"
	QueryDataSeparator = ";"
)

// SetIntegrationState saves the current state of an integration job to Redis
// The state consists of an updatedAt timestamp and a pagination cursor
func SetIntegrationState(ctx context.Context, integrationID uint, jobType string, updatedAt, cursor string) error {
	if RDB == nil {
		return errors.New("redis client not initialized")
	}

	key := fmt.Sprintf("integration-id-%d-%s", integrationID, jobType)

	// Start a transaction
	pipe := RDB.TxPipeline()

	// Queue the delete operation
	pipe.Del(ctx, key)

	// Queue the set operation
	pipe.Set(
		ctx,
		key,
		fmt.Sprintf("%s%s%s", updatedAt, QueryDataSeparator, cursor),
		0,
	)

	// Execute both commands atomically
	if _, err := pipe.Exec(ctx); err != nil {
		return fmt.Errorf("failed to execute transaction: %w", err)
	}

	return nil
}

// GetIntegrationState retrieves the current state of an integration job from Redis
// Returns the updatedAt timestamp and cursor. If no state exists or if the state is invalid,
// returns empty strings and nil error.
func GetIntegrationState(
	ctx context.Context,
	integrationID uint,
	jobType string,
) (updatedAt, cursor string, err error) {
	if RDB == nil {
		return "", "", errors.New("redis client not initialized")
	}

	key := fmt.Sprintf("integration-id-%d-%s", integrationID, jobType)

	exists, err := RDB.Exists(ctx, key).Result()
	if err != nil {
		return "", "", fmt.Errorf("failed to check state existence: %w", err)
	}

	if exists == 0 {
		return "", "", nil
	}

	stateString, err := RDB.Get(ctx, key).Result()
	if err != nil {
		// Clean up invalid state
		RDB.Del(ctx, key)
		return "", "", fmt.Errorf("failed to get state: %w", err)
	}

	// QueryDataSeparator will always be present so we can safely assume the split will return 2 elements
	// Input: "abc;def" -> []string{"abc", "def"}
	// Input: ";def"    -> []string{"", "def"}
	// Input: "abc;"    -> []string{"abc", ""}
	// Input: ";"       -> []string{"", ""}
	parsedState := strings.Split(stateString, QueryDataSeparator)
	if len(parsedState) != 2 {
		// Clean up invalid state
		RDB.Del(ctx, key)
		return "", "", nil
	}

	return parsedState[0], parsedState[1], nil
}
