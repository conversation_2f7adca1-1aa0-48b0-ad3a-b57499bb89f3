package redis

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

const (
	// E2openCachePrefix is the Redis key prefix for E2open 3rd party caching
	E2openCachePrefix = "e2open:3rd_party:user"
)

// E2openCompanyInfo represents cached company information
type E2openCompanyInfo struct {
	Name string `json:"name"`
	ID   string `json:"id"`
}

// E2openOperationInfo represents cached operation information
type E2openOperationInfo struct {
	Name string `json:"name"`
	ID   string `json:"id"`
}

// E2openFormFields represents cached form fields
type E2openFormFields struct {
	ProIDFields []E2openProIDField `json:"proIdFields"`
	DateFields  []E2openDateField  `json:"dateFields"`
}

// E2openProIDField represents a PRO ID field option
type E2openProIDField struct {
	Label     string `json:"label"`
	FieldName string `json:"fieldName"`
}

// E2openDateField represents a date field option
type E2openDateField struct {
	Label     string `json:"label"`
	FieldName string `json:"fieldName"`
	Exists    bool   `json:"exists"`
}

// buildE2openCacheKey builds the Redis key for E2open 3rd party user cache
func buildE2openCacheKey(integrationID uint) string {
	return fmt.Sprintf("%s:%d", E2openCachePrefix, integrationID)
}

// GetE2openCachedCompanies retrieves cached companies for a user
func GetE2openCachedCompanies(
	ctx context.Context,
	integrationID uint,
) ([]E2openCompanyInfo, bool, error) {
	if RDB == nil {
		return nil, false, nil
	}

	key := buildE2openCacheKey(integrationID)

	// Check if companies data exists
	hasCompanies, err := HashExists(ctx, key, "has_companies")
	if err != nil {
		log.Debug(ctx, "error checking companies cache", zap.Error(err))
		return nil, false, err
	}

	if !hasCompanies {
		return nil, false, nil
	}

	// Get companies with automatic JSON unmarshaling
	companies, found, err := HashGet[[]E2openCompanyInfo](ctx, key, "companies")
	if err != nil || !found {
		return nil, false, err
	}

	log.Info(
		ctx,
		"retrieved companies from cache",
		zap.Uint("integrationID", integrationID),
		zap.Int("count", len(companies)),
	)

	return companies, true, nil
}

// SetE2openCachedCompanies stores companies in cache
func SetE2openCachedCompanies(
	ctx context.Context,
	integrationID uint,
	companies []E2openCompanyInfo,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)

	// Set boolean flag using HashSetString
	if err := HashSetString(ctx, key, "has_companies", "true"); err != nil {
		return err
	}

	// Set companies data using HashSet (with JSON marshaling)
	if err := HashSet(ctx, key, "companies", companies); err != nil {
		return err
	}

	log.Info(
		ctx,
		"cached companies",
		zap.Uint("integrationID", integrationID),
		zap.Int("count", len(companies)),
	)

	return nil
}

// SetE2openDirectAccess marks that user has direct form access (no companies)
func SetE2openDirectAccess(ctx context.Context, integrationID uint) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)

	// Use HashSetString for simple string values (not JSON)
	if err := HashSetString(ctx, key, "direct_access", "true"); err != nil {
		return err
	}
	if err := HashSetString(ctx, key, "has_companies", "false"); err != nil {
		return err
	}

	log.Info(ctx, "cached direct access", zap.Uint("integrationID", integrationID))

	return nil
}

// GetE2openDirectAccess checks if user has direct form access
func GetE2openDirectAccess(ctx context.Context, integrationID uint) (bool, error) {
	if RDB == nil {
		return false, nil
	}

	key := buildE2openCacheKey(integrationID)

	result, found, err := HashGetString(ctx, key, "direct_access")
	if err != nil {
		return false, err
	}

	if !found {
		return false, nil
	}

	return result == "true", nil
}

// GetE2openCachedOperations retrieves cached operations for a company
func GetE2openCachedOperations(
	ctx context.Context,
	integrationID uint,
	companyID string,
) ([]E2openOperationInfo, bool, error) {
	if RDB == nil {
		return nil, false, nil
	}

	key := buildE2openCacheKey(integrationID)
	hasField := fmt.Sprintf("company:%s:has_operations", companyID)
	operationsField := fmt.Sprintf("company:%s:operations", companyID)

	// Check if operations data exists for this company
	hasOps, err := HashExists(ctx, key, hasField)
	if err != nil {
		return nil, false, err
	}

	if !hasOps {
		return nil, false, nil
	}

	// Get operations with automatic JSON unmarshaling
	operations, found, err := HashGet[[]E2openOperationInfo](ctx, key, operationsField)
	if err != nil || !found {
		return nil, false, err
	}

	log.Info(
		ctx,
		"retrieved operations from cache",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
		zap.Int("count", len(operations)),
	)

	return operations, true, nil
}

// SetE2openCachedOperations stores operations for a company in cache
func SetE2openCachedOperations(
	ctx context.Context,
	integrationID uint,
	companyID string,
	operations []E2openOperationInfo,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)
	hasField := fmt.Sprintf("company:%s:has_operations", companyID)
	operationsField := fmt.Sprintf("company:%s:operations", companyID)

	// Set boolean flag using HashSetString
	if err := HashSetString(ctx, key, hasField, "true"); err != nil {
		return err
	}

	// Set operations data using HashSet (with JSON marshaling)
	if err := HashSet(ctx, key, operationsField, operations); err != nil {
		return err
	}

	log.Info(
		ctx,
		"cached operations",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
		zap.Int("count", len(operations)),
	)

	return nil
}

// SetE2openNoOperations marks that a company has no operations
func SetE2openNoOperations(
	ctx context.Context,
	integrationID uint,
	companyID string,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)
	hasField := fmt.Sprintf("company:%s:has_operations", companyID)
	operationsField := fmt.Sprintf("company:%s:operations", companyID)

	// Set boolean flag using HashSetString
	if err := HashSetString(ctx, key, hasField, "false"); err != nil {
		return err
	}

	// Set empty operations array using HashSet
	if err := HashSet(ctx, key, operationsField, []E2openOperationInfo{}); err != nil {
		return err
	}

	log.Info(
		ctx,
		"cached no operations for company",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
	)

	return nil
}

// GetE2openCachedFormFields retrieves cached form fields for a company (with optional operation and stopType)
func GetE2openCachedFormFields(
	ctx context.Context,
	integrationID uint,
	companyID string,
	operationID string,
	stopType string,
) (*E2openFormFields, bool, error) {
	if RDB == nil {
		return nil, false, nil
	}

	key := buildE2openCacheKey(integrationID)

	var hasField, formFieldsField string
	if operationID != "" {
		hasField = fmt.Sprintf("company:%s:operation:%s:%s:has_form_fields", companyID, operationID, stopType)
		formFieldsField = fmt.Sprintf("company:%s:operation:%s:%s:form_fields", companyID, operationID, stopType)
	} else {
		hasField = fmt.Sprintf("company:%s:%s:has_form_fields", companyID, stopType)
		formFieldsField = fmt.Sprintf("company:%s:%s:form_fields", companyID, stopType)
	}

	// Check if form fields exist
	hasFields, err := HashExists(ctx, key, hasField)
	if err != nil {
		return nil, false, err
	}

	if !hasFields {
		return nil, false, nil
	}

	// Get form fields with automatic JSON unmarshaling
	formFields, found, err := HashGet[E2openFormFields](ctx, key, formFieldsField)
	if err != nil || !found {
		return nil, false, err
	}

	log.Info(
		ctx,
		"retrieved form fields from cache",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
		zap.String("operationID", operationID),
		zap.String("stopType", stopType),
	)

	return &formFields, true, nil
}

// SetE2openCachedFormFields stores form fields in cache
func SetE2openCachedFormFields(
	ctx context.Context,
	integrationID uint,
	companyID string,
	operationID string,
	stopType string,
	formFields E2openFormFields,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)

	var hasField, formFieldsField string
	if operationID != "" {
		hasField = fmt.Sprintf("company:%s:operation:%s:%s:has_form_fields", companyID, operationID, stopType)
		formFieldsField = fmt.Sprintf("company:%s:operation:%s:%s:form_fields", companyID, operationID, stopType)
	} else {
		hasField = fmt.Sprintf("company:%s:%s:has_form_fields", companyID, stopType)
		formFieldsField = fmt.Sprintf("company:%s:%s:form_fields", companyID, stopType)
	}

	// Set boolean flag using HashSetString
	if err := HashSetString(ctx, key, hasField, "true"); err != nil {
		return err
	}

	// Set form fields using HashSet (with JSON marshaling)
	if err := HashSet(ctx, key, formFieldsField, formFields); err != nil {
		return err
	}

	log.Info(
		ctx, "cached form fields",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
		zap.String("operationID", operationID),
		zap.String("stopType", stopType),
	)

	return nil
}

// GetE2openDirectFormFields retrieves cached form fields for direct access (no company/operation)
func GetE2openDirectFormFields(
	ctx context.Context,
	integrationID uint,
	stopType string,
) (*E2openFormFields, bool, error) {
	if RDB == nil {
		return nil, false, nil
	}

	key := buildE2openCacheKey(integrationID)
	formFieldsField := fmt.Sprintf("direct:%s:form_fields", stopType)

	// Check if direct form fields exist for this stopType
	hasFields, err := HashExists(ctx, key, formFieldsField)
	if err != nil {
		return nil, false, err
	}

	if !hasFields {
		return nil, false, nil
	}

	// Get form fields with automatic JSON unmarshaling
	formFields, found, err := HashGet[E2openFormFields](ctx, key, formFieldsField)
	if err != nil || !found {
		return nil, false, err
	}

	log.Info(
		ctx,
		"retrieved direct form fields from cache",
		zap.Uint("integrationID", integrationID),
		zap.String("stopType", stopType),
	)

	return &formFields, true, nil
}

// SetE2openDirectFormFields stores form fields for direct access
func SetE2openDirectFormFields(
	ctx context.Context,
	integrationID uint,
	stopType string,
	formFields E2openFormFields,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)
	formFieldsField := fmt.Sprintf("direct:%s:form_fields", stopType)

	// Set boolean flag using HashSetString (direct access still applies)
	if err := HashSetString(ctx, key, "direct_access", "true"); err != nil {
		return err
	}

	// Set form fields using HashSet (with JSON marshaling)
	if err := HashSet(ctx, key, formFieldsField, formFields); err != nil {
		return err
	}

	log.Info(
		ctx,
		"cached direct form fields",
		zap.Uint("integrationID", integrationID),
		zap.String("stopType", stopType),
	)

	return nil
}

// SetE2openStopTypeNotSupported caches that a specific stopType is not supported
func SetE2openStopTypeNotSupported(
	ctx context.Context,
	integrationID uint,
	companyID string,
	operationID string,
	stopType string,
) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)

	// Build field name based on what's available
	field := fmt.Sprintf("direct:%s:not_supported", stopType)
	if companyID != "" {
		if operationID != "" {
			field = fmt.Sprintf("company:%s:operation:%s:%s:not_supported", companyID, operationID, stopType)
		} else {
			field = fmt.Sprintf("company:%s:%s:not_supported", companyID, stopType)
		}
	}

	// Set flag indicating this stopType is not supported
	if err := HashSetString(ctx, key, field, "true"); err != nil {
		return err
	}

	log.Info(
		ctx,
		"cached stop type not supported",
		zap.Uint("integrationID", integrationID),
		zap.String("companyID", companyID),
		zap.String("operationID", operationID),
		zap.String("stopType", stopType),
	)

	return nil
}

// IsE2openStopTypeNotSupported checks if a stopType is cached as not supported
func IsE2openStopTypeNotSupported(
	ctx context.Context,
	integrationID uint,
	companyID string,
	operationID string,
	stopType string,
) (bool, error) {
	key := buildE2openCacheKey(integrationID)

	// Build field name based on what's available
	field := fmt.Sprintf("direct:%s:not_supported", stopType)
	if companyID != "" {
		if operationID != "" {
			field = fmt.Sprintf("company:%s:operation:%s:%s:not_supported", companyID, operationID, stopType)
		} else {
			field = fmt.Sprintf("company:%s:%s:not_supported", companyID, stopType)
		}
	}

	result, found, err := HashGetString(ctx, key, field)
	if err != nil {
		return false, err
	}
	if !found {
		return false, nil
	}

	return result == "true", nil
}

// ClearE2openCache clears all cached data for a user (for Phase 2 refresh functionality)
func ClearE2openCache(ctx context.Context, integrationID uint) error {
	if RDB == nil {
		return nil
	}

	key := buildE2openCacheKey(integrationID)

	if err := DeleteKey(ctx, key); err != nil {
		log.Error(ctx, "failed to clear E2open cache", zap.Error(err))
		return fmt.Errorf("failed to clear E2open cache: %w", err)
	}

	log.Info(ctx, "cleared E2open cache", zap.Uint("integrationID", integrationID))

	return nil
}
