package emails

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	prose "github.com/jdkato/prose/v2" // pkg has been archived but works for our needs
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/prompts"
	rdsEmail "github.com/drumkitai/drumkit/common/rds/email"
	"github.com/drumkitai/drumkit/common/redis"
)

var (
	greetPattern = regexp.MustCompile(`(?i)^(Hi|Hello|Hey|Good day|Good afternoon),?[ \t]*([a-zA-Z]*)?,?\s*\n*`)
	// e.g.
	// From: <EMAIL> <<EMAIL>>
	// Date: Thursday, March 20, 2025 at 7:03 PM
	outlookReplyPattern = regexp.MustCompile(
		`(?m)\s*(?:\*\*)?(?:From:|De:)(?:\*\*)?.+?<.*?>[\s\S]*$`,
	)

	gmailReplyPattern = regexp.MustCompile(
		`(?m)\s*(?:\*\*)?(On|El|Le)(?:\*\*)?.*?(?:\n.*?)*?(wrote|escribió|a écrit)(?:\*\*)?:[\s\S]*`,
	)

	//nolint:lll
	sigPattern          = regexp.MustCompile(`(?i)(--\s*\n[\s\S]+)?$|((Thanks|Regards|Best regards|Best|Cheers|Sincerely|Please let me know|If you have any questions|Thank you),\s*[\s\S]{0,300})?$`)
	externalLinkPattern = regexp.MustCompile(`<(https?://|www\.)[^>]+>`)
	newlinePattern      = regexp.MustCompile(`\n(\s*)\n`)

	getEmailSignatureFunc = rdsEmail.GetSignatures

	EmailSignatureTTL = 24 * time.Hour * 7 // One week
)

func CleanEmail(ctx context.Context, email string, subject string) string {
	email = trimNewlines(email)

	// Find first occurrence of "re:" or "fwd?:" case-insensitive
	var replyIndex, fwdIndex = -1, -1
	replyIndexTuple := regexp.MustCompile(`(?i)re:`).FindStringIndex(subject)
	if replyIndexTuple != nil {
		replyIndex = replyIndexTuple[0]
	}

	fwdIndexTuple := regexp.MustCompile(`(?i)fwd?:`).FindStringIndex(subject)
	if fwdIndexTuple != nil {
		fwdIndex = fwdIndexTuple[0]
	}

	// Do not remove replies for the first forwarded email.
	// replyIndex < fwdIndex  ensures we don't remove inline thread when a reply chain is forwarded,
	// (e.g. FW: [EXTERNAL] RE: Delivery Appointment)
	// but we do for subsequent replies (e.g. RE: FW: [EXTERNAL] RE: Delivery Appointment)
	// Edge case: User manually removes RE/FW prefixes from the Gmail/Outlook-generated formats
	// so we don't remove replies
	if (fwdIndex == -1 && replyIndex != -1) || // Remove replies if it's a reply and not forwarded
		(replyIndex < fwdIndex && replyIndex != -1) { // Remove replies if it's a reply to a forwarded email
		email = removeReplies(email)
		log.Info(ctx, "removed replies")
		log.Debug(ctx, "removed replies", zap.String("bodyNoReplies", email))
	}

	email = removeGreeting(email)
	// TODO: We may want to analyze external links in the future specifically for Track & Trace.
	email = removeExternalLinks(email)

	// TODO: this removes the entire email in some cases and so has been temporarily disabled
	// email = removeSigWithNLP(email)

	return strings.TrimSpace(email)
}

type EmailSignature struct {
	EmailSignature string `json:"email_signature" jsonschema_description:"The signature of the email."`
}

// SegmentEmail segments an email into a body and signature using OpenAI.
func SegmentEmail(
	ctx context.Context,
	email models.Email,
) (bodyWithoutSignature string, signature string, err error) {

	signatureFound := false

	// check redis for signature first
	cachedSignature, found, err := redis.GetKey[string](ctx, "email_signature:"+email.Sender)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.WarnNoSentry(ctx, "error getting signature from redis", zap.Error(err))
	}
	if found {
		signatureFound = true
		signature = cachedSignature
	}

	if !signatureFound {
		// call to rds to get all the distinct signatures from a pool of the latest 50 emails from same sender_address
		signatures, err := getEmailSignatureFunc(ctx, email.Sender, 50)
		if err != nil || len(signatures) == 0 {
			log.WarnNoSentry(
				ctx,
				"no rds signatures found for current email, falling back to LLM signature extraction",
				zap.String("sender", email.Sender),
				zap.Error(err),
			)
		} else {
			for _, sig := range signatures {
				if strings.Contains(email.Body, sig) {
					signatureFound = true
					signature = sig
					// Cache the signature in Redis when found from RDS
					if err := redis.SetKey(ctx, "email_signature:"+email.Sender, sig, EmailSignatureTTL); err != nil {
						log.WarnNoSentry(ctx, "failed to cache signature in redis", zap.Error(err))
					}
					break
				}
			}
		}
	}

	// fallback to LLM if signature not found in RDS
	if !signatureFound {
		openaiService, err := openai.NewService(ctx)
		if err != nil {
			return "", "", fmt.Errorf("failed to create openai service: %w", err)
		}

		segResp, err := openaiService.GetResponse(
			ctx,
			email,
			models.Attachment{},
			braintrustsdk.CreateProjectDetails(braintrustsdk.EmailSegmentation, false),
			openai.ResponseOptions{
				DeveloperPrompt: prompts.SegmentPrompt,
				UserPrompt:      email.Body,
				Schema:          extractor.GenerateSchema[EmailSignature](),
			},
		)
		if err != nil {
			return "", "", fmt.Errorf("failed to segment email: %w", err)
		}

		emailSig, err := extractor.StructExtractor[EmailSignature](segResp.Content)
		if err != nil {
			return "", "", fmt.Errorf("failed to extract email signature: %w", err)
		}

		signature = emailSig.EmailSignature

		// Cache the signature in Redis when found via LLM
		if signature != "" {
			if err := redis.SetKey(ctx, "email_signature:"+email.Sender, signature, EmailSignatureTTL); err != nil {
				log.WarnNoSentry(ctx, "failed to cache signature in redis", zap.Error(err))
			}
		}
	}

	// Replace all occurrences of the signature in the body
	if signature == "" {
		bodyWithoutSignature = email.Body
	} else {
		bodyWithoutSignature = strings.ReplaceAll(email.Body, signature, "")
	}

	bodyWithoutSignature = strings.TrimSpace(bodyWithoutSignature)

	return bodyWithoutSignature, signature, nil
}

// Remove email signatures using Prose
// TODO: see if we can speed this up
func removeSigWithNLP(email string) string {
	doc, err := prose.NewDocument(email, prose.WithSegmentation(true)) // ensures sentence segmentation
	if err != nil {
		return email
	}

	sectionsToKeep := []string{}

	for _, sent := range doc.Sentences() {
		sentenceDoc, err := prose.NewDocument(sent.Text)
		if err != nil {
			continue
		}

		verbCount := 0
		for _, tok := range sentenceDoc.Tokens() {
			if tok.Tag == "VB" || tok.Tag == "VBD" || tok.Tag == "VBG" || tok.Tag == "VBN" ||
				tok.Tag == "VBP" || tok.Tag == "VBZ" {

				verbCount++
			}
		}

		// If the proportion of verbs is above the threshold, then consider the sentence
		// as part of the main email body.
		if float64(verbCount)/float64(len(sentenceDoc.Tokens())) > 0.1 {
			sectionsToKeep = append(sectionsToKeep, sent.Text)
		}
	}

	return strings.Join(sectionsToKeep, " ")
}

// Remove older email replies in thread
func removeReplies(email string) string {
	latestMessage := outlookReplyPattern.ReplaceAllString(email, "")
	latestMessage = gmailReplyPattern.ReplaceAllString(latestMessage, "")

	return strings.TrimSpace(latestMessage)
}

// Remove email greeting
func removeGreeting(email string) string {
	return greetPattern.ReplaceAllString(email, "")
}

// Remove email signatures
func removeSigWithRegex(email string) string {
	return sigPattern.ReplaceAllString(email, "")
}

// Remove external website links
func removeExternalLinks(email string) string {
	return externalLinkPattern.ReplaceAllString(email, "")
}

// Replace carriage returns with newlines and replace any sequence of 2+ newlines with just 2
func trimNewlines(email string) string {
	email = strings.ReplaceAll(email, "\r", "\n")
	return newlinePattern.ReplaceAllString(email, "\n\n")
}
