package emails

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

func TestCleanEmail(t *testing.T) {
	t.<PERSON>()

	tests := []struct {
		testName string
		subject  string
		input    string
		output   string
	}{
		{
			testName: "New simple message--no inline should be removed",
			subject:  "Checking in on this load",
			input:    "Hi <PERSON>,\nChecking in on this shipment -- where is it?",
			output:   "Checking in on this shipment -- where is it?",
		},
		{
			testName: "Simple reply--inline should be removed",
			subject:  "RE: Checking in on this load",
			//nolint:lll
			input:  "Hi <PERSON>, it's in Chicago rn\n\nOn Jul 25, <PERSON> wrote:\n\n>Checking in on this shipment -- where is it?!\n\n--\nJane",
			output: "it's in Chicago rn",
		},
		{
			testName: "1 forward--inline should not be removed",
			subject:  "Fwd: Quote request",
			input: `From: <PERSON>t: Friday, March 14, 2025 11:42 AM

			To: 'Jane <PERSON>' <<EMAIL>>

			Subject: RE: Box truck quote - NC to WV

			Hi Jane, can you requote with a liftgate please?

			HEDGESVILLE, WV 25427`,
			output: `From: John Smith

			Sent: Friday, March 14, 2025 11:42 AM

			To: 'Jane Smith' <<EMAIL>>

			Subject: RE: Box truck quote - NC to WV

			Hi Jane, can you requote with a liftgate please?

			HEDGESVILLE, WV 25427`,
		},
		{
			testName: "Multiple forwards, no replies--inline should not be removed",
			subject:  "FW: Test forward chain",
			input: `Adding context to this forward

			From: John Smith <<EMAIL>>

			Date: Monday, April 28, 2025 at 8:30PM

			To: Jane Smith <<EMAIL>>

			Subject: Fwd: Test forward chain

			\-\-\-\-\-\-\-\-\-\- Forwarded message ---------

			From: Bob Wilson <<EMAIL>>

			Date: Mon, Apr 28, 2025 at 8:28PM

			Subject: Test forward chain

			Just testing forwarding functionality

			Best,

			Bob Wilson
			Support Specialist
			Company A
			Phone: ************`,
			output: `Adding context to this forward

			From: John Smith <<EMAIL>>

			Date: Monday, April 28, 2025 at 8:30PM

			To: Jane Smith <<EMAIL>>

			Subject: Fwd: Test forward chain

			\-\-\-\-\-\-\-\-\-\- Forwarded message ---------

			From: Bob Wilson <<EMAIL>>

			Date: Mon, Apr 28, 2025 at 8:28PM

			Subject: Test forward chain

			Just testing forwarding functionality

			Best,

			Bob Wilson
			Support Specialist
			Company A
			Phone: ************`},
		{
			testName: "Forwarded reply",
			subject:  "FW: RE: Quote request",
			input: `Following up on quote request

			John Smith
			Logistics Analyst
			Company A
			Phone: ************
			<EMAIL>

			From: John Smith
			Sent: Friday, March 14, 2025 11:42 AM
			To: 'Jane Smith' <<EMAIL>>
			Subject: RE: Quote request

			Please provide updated quote`,
			output: `Following up on quote request

			John Smith
			Logistics Analyst
			Company A
			Phone: ************
			<EMAIL>

			From: John Smith
			Sent: Friday, March 14, 2025 11:42 AM
			To: 'Jane Smith' <<EMAIL>>
			Subject: RE: Quote request

			Please provide updated quote`,
		},
		{
			testName: "Forwarded chain has replies--inline should be removed",
			subject:  "Re: FW: Quote Request",
			input: `Available next Tuesday

			Thanks!

			John Smith
			Company A
			Phone: ************

			On Tue, Apr 1, 2025 at 5:12 PM John <<EMAIL>> wrote:
			> Checking availability
			>
			> John Smith
			> Company A
			> Phone: ************
			>
			> On Tue, Apr 1, 2025 at 2:11 PM Jane <<EMAIL>> wrote:
			>> When can we schedule this?
			>>
			>> Jane Smith
			>> Company B
			>> Phone: ************`,
			output: `Available next Tuesday

			Thanks!

			John Smith
			Company A
			Phone: ************`,
		},
	}

	for _, test := range tests {
		t.Run(test.testName, func(t *testing.T) {
			assert.Equal(t, test.output, CleanEmail(context.Background(), test.input, test.subject))
		})
	}
}

func TestRemoveReplies(t *testing.T) {
	t.Parallel()

	tests := []struct {
		subject  string
		input    string
		expected string
	}{
		{
			input:    "How are you?\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
			expected: "How are you?",
		},
		{
			input:    "How are you?\n\nEl mar, 29 ago 2023 a las 19:25, Alice escribió:\n\n> Hey!\n\nThanks,\nBob",
			expected: "How are you?",
		},
		{
			input:    "How are you?\n\nLe mar. 29 août 2023, à 19 h 30, Alice a écrit:\n\n> Hey!\n\nThanks,\nBob",
			expected: "How are you?",
		},
		{
			subject:  "FW: Forwarded with a new content",
			input:    "How are you?\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
			expected: "How are you?\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
		},
		{
			subject:  "FW: Forwarded but no new content",
			input:    "\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
			expected: "\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
		},
		{
			subject:  "Forwarded but no FW: prefix and no new content",
			input:    "\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
			expected: "\n\nOn Jul 25, Alice wrote:\n\n> Hey!\n\nThanks,\nBob",
		},
		{
			subject:  "Quote Request containing From: in body",
			input:    "Quote a Dry Van: 28,************\n\nRogarde,\n\nGreg",
			expected: "Quote a Dry Van: 28,************\n\nRogarde,\n\nGreg",
		},
		{
			subject: "Gmail replies 1",
			input: `Was this load delivered in Santa Fe Springs, CA. today?

			Thanks,
			
			On Tue, Apr 15, 2025 at 5:51 AM John Smith <
			
			<EMAIL>> wrote:
			
			> Hi Team,
			
			>
			
			> Please confirm if the driver has already arrived at the receiver (SANTA FE
			
			> SPRINGS, CA)
			
			>
			
			> Thank you!
			
			>
			
			>`,
			expected: "Was this load delivered in Santa Fe Springs, CA. today?\n\n\t\t\tThanks,",
		},
		{
			subject: "Gmail replies 2",
			input: `Adding 7-11 VA

			On Fri, Apr 11, 2025 at 12:40 PM <EMAIL> <
			
			<EMAIL>> wrote:
			
			> please release
			
			> [image: Sent from Front]
			
			-- 
			
			*John Smith*
			
			Operations Support Specialist
			
			Company A - Chino CA
			
			24hr Shipment Support: ************
			
			http://www.companyc.com  | NFI Terms and Conditions`,

			expected: `Adding 7-11 VA`,
		},
		{
			subject: "Gmail replies 3",
			input: `On Fri, Apr 11, 2025 at 12:40 PM <EMAIL> <
			
			<EMAIL>> wrote:
			
			> please release
			
			> [image: Sent from Front]
			
			-- 
			
			*John Smith*
			
			Operations Support Specialist
			
			Company A - Chino CA
			
			24hr Shipment Support: ************
			
			http://www.companyc.com  | NFI Terms and Conditions`,

			expected: ``,
		},
		{
			subject: "Gmail replies 4",
			//nolint:lll
			input: `Loaded and rolling

			--------------------------------------------------------------------------------T. SmithTransportation CoordinatorOffice-************"Team Work Makes A Dream Work"  
			
				On Wednesday, April 23, 2025 at 01:05:48 PM PDT, John Smith <<EMAIL>> wrote:  
			
			 Got it thank you`,
			//nolint:lll
			expected: "Loaded and rolling\n\n\t\t\t--------------------------------------------------------------------------------T. SmithTransportation CoordinatorOffice-************\"Team Work Makes A Dream Work\"",
		},
		{
			subject: "Gmail replies 5",
			input: `not loaded yet

			Sincerely, 
			
			ROAD EXPRESS 
			
			PH#************

			<EMAIL>
			
				On Monday, April 21, 2025 at 12:54:56 PM CDT, John Smith <<EMAIL>> wrote:  
			
			 Road express team any update??`,
			expected: `not loaded yet

			Sincerely, 
			
			ROAD EXPRESS 
			
			PH#************

			<EMAIL>`,
		},
		{
			subject: "Outlook reply 1",
			//nolint:lll
			input: `Good morning mr.

			our in ETA is in the next hour.
			
			thanks
			
			John Smith 
			
			GOLD TRUCKING LLC
			
			PH ************
			
			FX  ************

			________________________________
			
			From: John Smith <<EMAIL>>
			
			Sent: Thursday, November 14, 2024 8:18
			
			To: John Smith  <<EMAIL>>
			
			Cc: Jane Smith <<EMAIL>>; John Smith <<EMAIL>>
			
			Subject: Re: HANOVER PA TO MOBILE AL
			
			Good Morning John,
			
			Do you know what time the 2 VAR trucks will deliver today?
			
			[Company B]     John Smith
			
			Senior Director of Sales
			
			o: +1 (************
			
			w: companyb.com
			
			[facebook] [twitter]   [LinkedIn]   [instagram]   [Inc. 5000]  [Smartway]
			
			Cargo Liability Disclaimer:
			
			Unless otherwise specified by Customer in writing, Company B assumes that the cargo value of each load is equal to or less than $100,000.00.`,
			expected: `Good morning mr.

			our in ETA is in the next hour.
			
			thanks
			
			John Smith 
			
			GOLD TRUCKING LLC
			
			PH ************
			
			FX  ************

			________________________________`,
		},
		{
			subject: "Outlook replies 2",
			//nolint:lll
			input: `Please do, thank you

			[Company B]
			
			JP
			
			Capacity Sales Representative
			
			o: +1 (************
			
			w: companybtransport.com
			
			[facebook] [twitter]   [LinkedIn]   [instagram]   [Inc. 5000]  [Smartway]
			
			Cargo Liability Disclaimer:
			
			Unless otherwise specified by Customer in writing, Company B assumes that the cargo value of each load is equal to or less than $100,000.00.
			
			From: John Smith <<EMAIL>>
			
			Sent: Thursday, November 14, 2024 4:41 PM
			
			To: JP <<EMAIL>>
			
			Cc: PL-support <<EMAIL>>; John Smith <<EMAIL>>
			
			Subject: Re: RE:
			
			I don't have anybody in that are a, but I will keep you posted if I have a truck over there
			
			Best regards.
			
																			   John Smith
			
			MC 772268                                          Keep Trucking LLC
			
																			   Phone: ************ ext 203
			
																			   Direct : ************
			
																			   Fax: ************
			
																			   Email: <EMAIL>
			
			________________________________
			
			From: JP <<EMAIL>>
			
			Sent: Thursday, November 14, 2024 3:37 PM
			
			To: John Smith <<EMAIL>>
			
			Cc: PL-support <<EMAIL>>; John Smith <<EMAIL>>
			
			Subject: RE:
			
			Albany, OR to San Diego, CA
			
			[Image removed by sender. Company B]
			
			JP
			
			Capacity Sales Representative
			
			o: +1 (************
			
			w: companybtransport.com
			
			[Image removed by sender. facebook] [Image removed by sender. twitter]   [Image removed by sender. LinkedIn]   [Image removed by sender. instagram]   [Image removed by sender. Inc. 5000]  [Image removed by sender. Smartway]
			
			Cargo Liability Disclaimer:
			
			Unless otherwise specified by Customer in writing, Company B assumes that the cargo value of each load is equal to or less than $100,000.00.
			
			`,
			//nolint:lll
			expected: `Please do, thank you

			[Company B]
			
			JP
			
			Capacity Sales Representative
			
			o: +1 (************
			
			w: companybtransport.com
			
			[facebook] [twitter]   [LinkedIn]   [instagram]   [Inc. 5000]  [Smartway]
			
			Cargo Liability Disclaimer:
			
			Unless otherwise specified by Customer in writing, Company B assumes that the cargo value of each load is equal to or less than $100,000.00.`,
		},
	}

	for i, tt := range tests {
		t.Run(helpers.Or(tt.subject, fmt.Sprintf("test %d", i)), func(t *testing.T) {
			result := tt.input
			if !helpers.IsArrayElmInString(tt.subject, []string{"Fwd:", "Fw:", "FW:"}) {
				result = removeReplies(tt.input)
			}
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestRemoveGreeting(t *testing.T) {
	t.Parallel()

	tests := []struct {
		input  string
		output string
	}{
		{
			input:  "Hi Alice,\n\nHow are you?",
			output: "How are you?",
		},
		{
			input:  "Good Afternoon,\n\nCan we pick up the following:\n\n10/19 @ 1400",
			output: "Can we pick up the following:\n\n10/19 @ 1400",
		},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.output, removeGreeting(tt.input))
	}
}

func TestRemoveExternalLinks(t *testing.T) {
	t.Parallel()

	tests := []struct {
		input  string
		output string
	}{
		{
			input:  "Check out this link: <https://example.com>",
			output: "Check out this link: ",
		},
		{
			input:  "Multiple links: <https://example.com> and <www.example.org>",
			output: "Multiple links:  and ",
		},
		{
			input:  "Link with text: <https://example.com|Click here>",
			output: "Link with text: ",
		},
		{
			input:  "No links in this text",
			output: "No links in this text",
		},
		{
			input:  "Email address: <<EMAIL>>",
			output: "Email address: <<EMAIL>>",
		},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.output, removeExternalLinks(tt.input))
	}
}

func TestRemoveSigWithRegex(t *testing.T) {
	t.Parallel()

	tests := []struct {
		input  string
		output string
	}{
		{
			input:  "How are you?\n\n--\nBob",
			output: "How are you?\n\n",
		},
		// NFI example
		{
			input: `Please confirm pickup appt\n\nBest Regards,
			Sophie Edouard
			A to Z Logistics - Chino CA
			Direct: ************ | <EMAIL>
			24hr Shipment Support: ************
			www.logistics.com | NFI Terms and Conditions`,
			output: `Please confirm pickup appt\n\n`,
		},
		// Wicker Park example
		{
			input: `You can call me later on this evening to make sure we are good to go.
			The driver is based out of Renton, WA and is good to check in at 10pm.
			The driver's name is Dave. Best call back is ************.\n\nThank you,\nBoogs Bunny\n
			Operations Manager
			Office: ************ EXT# 104
			Direct: ************
			101 E. Lake St
			Bloomingdale, IL 60108
			To Learn More - Visit Us HERE`,
			output: `You can call me later on this evening to make sure we are good to go.
			The driver is based out of Renton, WA and is good to check in at 10pm.
			The driver's name is Dave. Best call back is ************.\n\n`,
		},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.output, removeSigWithRegex(tt.input))
	}
}

func TestRemoveSigWithNLP(t *testing.T) {
	t.Parallel()

	tests := []struct {
		input  string
		output string
	}{
		{
			//nolint:lll
			input:  "How are you?\r \r Thank you team\r \r \r Foo Bar Inc. A Superb Solution to Your Trucking Needs\r 12739 Fifth St.\r Boston, MA 83293",
			output: "How are you? Thank you team\n\n Foo Bar Inc.",
		},
	}

	for _, tt := range tests {
		result := trimNewlines(tt.input)
		result = removeSigWithNLP(result)
		assert.Equal(t, tt.output, result)
	}
}

func TestSegmentEmail(t *testing.T) {
	t.Parallel()

	// Mock email for testing
	email := models.Email{
		Sender: "<EMAIL>",
		Body:   "Hello, this is the email body.\n\nBest regards,\nJohn Smith\nCompany A\nPhone: ************",
	}

	// Test case 1: Redis cache hit
	t.Run("redis_cache_hit", func(t *testing.T) {
		ctx := context.Background()

		// Create mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Pre-populate Redis cache with JSON-encoded data (escape newlines)
		jsonSignature := "\"Best regards,\\nJohn Smith\\nCompany A\\nPhone: ************\""
		mock.ExpectGet("email_signature:" + email.Sender).SetVal(jsonSignature)

		body, signature, err := SegmentEmail(ctx, email)

		assert.NoError(t, err)
		assert.Equal(t, "Best regards,\nJohn Smith\nCompany A\nPhone: ************", signature)
		assert.NotContains(t, body, signature)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	// Test case 2: Redis miss, RDS hit
	t.Run("redis_miss_rds_hit", func(t *testing.T) {
		ctx := context.Background()

		// Create mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Redis miss
		mock.ExpectGet("email_signature:" + email.Sender).RedisNil()

		// Mock RDS to return a signature that matches the email body
		testSignature := "Best regards,\nJohn Smith\nCompany A\nPhone: ************"
		jsonSignature := "\"Best regards,\\nJohn Smith\\nCompany A\\nPhone: ************\""
		mock.ExpectSet("email_signature:"+email.Sender, []byte(jsonSignature), 24*time.Hour*7).SetVal("OK")

		// Mock the RDS GetSignatures function
		originalGetSignatures := getEmailSignatureFunc
		getEmailSignatureFunc = func(_ context.Context, _ string, _ int) ([]string, error) {
			return []string{testSignature}, nil
		}
		defer func() {
			getEmailSignatureFunc = originalGetSignatures
		}()

		body, signature, err := SegmentEmail(ctx, email)

		assert.NoError(t, err)
		assert.Equal(t, testSignature, signature)
		assert.NotContains(t, body, signature)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	// Test case 3: Redis error handling
	t.Run("redis_error", func(t *testing.T) {
		ctx := context.Background()

		// Create mock Redis client
		mockRDB, mock := redismock.NewClientMock()
		originalRDB := redis.RDB
		redis.RDB = mockRDB
		defer func() {
			redis.RDB = originalRDB
			_ = mockRDB.Close()
		}()

		// Redis error
		mock.ExpectGet("email_signature:" + email.Sender).SetErr(errors.New("redis connection failed"))

		// Mock RDS to return a signature that matches the email body
		originalGetSignatures := getEmailSignatureFunc
		getEmailSignatureFunc = func(_ context.Context, _ string, _ int) ([]string, error) {
			return []string{"Best regards,\nJohn Smith\nCompany A\nPhone: ************"}, nil
		}
		defer func() {
			getEmailSignatureFunc = originalGetSignatures
		}()

		// Mock RDS result being cached
		jsonSignature := "\"Best regards,\\nJohn Smith\\nCompany A\\nPhone: ************\""
		mock.ExpectSet("email_signature:"+email.Sender, []byte(jsonSignature), 24*time.Hour*7).SetVal("OK")

		body, signature, err := SegmentEmail(ctx, email)

		assert.NoError(t, err)
		assert.Equal(t, "Best regards,\nJohn Smith\nCompany A\nPhone: ************", signature)
		assert.NotContains(t, body, signature)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
