package emails

import (
	"context"
	"os"
	"testing"
	"time"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

var (
	// Semaphore to limit concurrency to 4 tests at a time
	semaphore = make(chan struct{}, 3)
)

// TestFunc represents a test function that takes a testing.T, context, and OpenAI client
type TestFunc func(ctx context.Context, t *testing.T)

//
// Test Live Parents
// 1. You will need to comment out the skip in the test to run it.
// 2. You will also need to modify openai.NewService to this:
/**
func NewService(ctx context.Context) (Service, error) {

	clientOnce.Do(func() {

		apiKey := "[INSERT_API_KEY_HERE]"
		if apiKey == "" {
			clientErr = errors.New("OpenAI API key is not set")
			return
		}

		openaiClient = openaiSDK.NewClient(
			openaiOption.WithAPIKey(apiKey),
		)
	})

	return &service{client: openaiClient}, clientErr
}
**/

func TestLiveCategories(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM labeling tests: run with LIVE_TEST=true to enable")
		return
	}

	tests := []TestFunc{
		LiveTestTrackAndTraceCategory,
		LiveTestSchedulingCategory,
		LiveTestQuotingCategory,
		LiveTestCapacityManagementCategory,
		LiveTestLoadBuildingCategory,
	}

	for _, test := range tests {
		testFunc := test // Create local copy of test function to use in closure
		t.Run(getFunctionName(testFunc), func(t *testing.T) {
			t.Parallel()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			timeoutCtx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
			defer cancel()

			testFunc(timeoutCtx, t)
		})
	}
}

func TestLiveLabels1(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM labeling tests: run with LIVE_TEST=true to enable")
		return
	}

	tests := []TestFunc{
		LiveTestDeliveryConfirmationLabel,
		LiveTestDriverInfoLabel,
		LiveTestTrackingETALabel,
		LiveTestAppointmentSchedulingLabel,
		LiveTestPickupConfirmationLabel,
		LiveTestAppointmentConfirmedLabel,
	}

	for _, test := range tests {
		testFunc := test // Create local copy of test function to use in closure
		t.Run(getFunctionName(testFunc), func(t *testing.T) {
			t.Parallel()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			timeoutCtx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
			defer cancel()

			testFunc(timeoutCtx, t)
		})
	}
}

func TestLiveLabels2(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.Skip("skipping live LLM labeling tests: run with LIVE_TEST=true to enable")
		return
	}

	tests := []TestFunc{
		LiveTestCheckCallLabel,
		LiveTestQuoteLabel,
		LiveTestQuoteRequestLabel,
		LiveTestCarrierInfoLabel,
		LiveTestTruckListLabel,
		LiveTestLoadBuildingLabel,
	}

	for _, test := range tests {
		testFunc := test // Create local copy of test function to use in closure
		t.Run(getFunctionName(testFunc), func(t *testing.T) {
			t.Parallel()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			timeoutCtx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
			defer cancel()

			testFunc(timeoutCtx, t)
		})
	}
}

//
// Category tests
//

//nolint:lll
func LiveTestTrackAndTraceCategory(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Delivery update for Load #FTL4567",
			Body:    "Hello, This is a delivery update for your shipment. The truck is currently in transit on I-95 and expected to arrive tomorrow at 2 PM. The driver, John Smith (************), has confirmed all is on schedule. Please let us know if you have any questions.",
		},
		{
			Subject: "DELIVERED: Load #789012 at XYZ Warehouse",
			Body:    "This is to confirm that load #789012 has been successfully delivered to XYZ Warehouse. Delivery was completed at 14:30 EST. POD has been signed by the receiver and is attached to this email. All items were delivered in good condition with no exceptions noted.",
		},
		{
			Subject: "Driver Information for Tomorrow's Pickup - Load #ABC123",
			Body:    "Please find the driver information for tomorrow's pickup:\nDriver Name: Michael Rodriguez\nPhone: ************\nTruck #: T-567\nTrailer #: 98765\nThe driver will arrive between 9:00-10:00 AM. Please have all paperwork ready.",
		},
	}

	for i, email := range testEmails {

		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach instead of falling back to regex
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if any of the returned labels correspond to track and trace category
		foundCategory := false
		trackAndTraceLabels := []string{
			string(DeliveryConfirmationLabel),
			string(DriverInfoLabel),
			string(TrackingETALabel),
			string(PickupConfirmationLabel),
			string(CheckCallLabel),
			string(CarrierInfoLabel),
		}

		for _, label := range labels {
			for _, ttLabel := range trackAndTraceLabels {
				if label == ttLabel {
					foundCategory = true
					break
				}
			}
			if foundCategory {
				break
			}
		}

		assert.True(t, foundCategory, "Test email %d should be classified as track and trace category", i+1)
	}
}

//nolint:lll
func LiveTestSchedulingCategory(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Appointment Request - Load #12345",
			Body:    "We need to schedule a delivery appointment for the following shipment:\nPO #: 56789\nShipper: ABC Manufacturing\nConsignee: XYZ Distribution Center\nPreferred delivery dates: July 18, 2023 (8AM-2PM), July 19, 2023 (8AM-2PM), or July 20, 2023 (8AM-2PM). Please confirm which date/time works best.",
		},
		{
			Subject: "Appointment Confirmation - Load #54321",
			Body:    "Your delivery appointment has been confirmed:\nLoad #: 54321\nAppointment Date: July 22, 2023\nAppointment Time: 10:00 AM EST\nFacility: Central Distribution Center\nDock Door: #14\nPlease ensure the driver arrives 15 minutes before the appointment time.",
		},
		{
			Subject: "RE: Scheduling Pickup - Order #ABC789",
			Body:    "We would like to schedule a pickup for this order for next week. Our facility is open Monday-Friday 8AM-5PM. Please let us know what day and time would work best for your driver to pickup this order consisting of 6 pallets.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if any returned labels correspond to scheduling category
		foundCategory := false
		schedulingLabels := []string{
			string(AppointmentSchedulingLabel),
			string(AppointmentConfirmedLabel),
		}

		for _, label := range labels {
			for _, sLabel := range schedulingLabels {
				if label == sLabel {
					foundCategory = true
					break
				}
			}
			if foundCategory {
				break
			}
		}

		assert.True(t, foundCategory, "Test email %d should be classified as scheduling category", i+1)
	}
}

//nolint:lll
func LiveTestQuotingCategory(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Rate Quote for Chicago to Memphis FTL",
			Body:    "Based on your request, here is our quote for the Chicago to Memphis lane:\nQuote #: Q-78945\nEquipment: 53' Dry Van\nPickup: Chicago, IL\nDelivery: Memphis, TN\nRate: $1,850.00 USD\nFuel Surcharge: $350.00 USD\nTotal Rate: $2,200.00 USD\nValidity: 7 days",
		},
		{
			Subject: "Request Rate for Orlando to Miami shipment",
			Body:    "We need a competitive rate for the following shipment:\nOrigin: Orlando, FL\nDestination: Miami, FL\nPickup Date: July 25, 2023\nDelivery Date: July 26, 2023\nCommodity: Electronics (palletized)\nWeight: 5,000 lbs\nDimensions: 4 pallets, 48\"x40\"x50\" each\nCan you please provide your best rate for this lane?",
		},
		{
			Subject: "Urgent: Price for Denver to Salt Lake City",
			Body:    "Looking for immediate pricing on the following lane:\nDenver, CO to Salt Lake City, UT\nDry van needed\nOne pickup, one drop\nWeight: 35,000 lbs\nReady Date: 7/27/2023\nPlease quote your best rate as soon as possible. Need to book this today.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if any returned labels correspond to quoting category
		foundCategory := false
		quotingLabels := []string{
			string(CarrierQuoteResponseLabel),
			string(QuoteRequestLabel),
		}

		for _, label := range labels {
			for _, qLabel := range quotingLabels {
				if label == qLabel {
					foundCategory = true
					break
				}
			}
			if foundCategory {
				break
			}
		}

		assert.True(t, foundCategory, "Test email %d should be classified as quoting category", i+1)
	}
}

//nolint:lll
func LiveTestCapacityManagementCategory(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Available trucks for week of 07/24 - Northeast Region",
			Body:    "Here is our list of available trucks for the week of 07/24/2023:\nMonday, 07/24:\n- 3x 53' Dry Vans in Boston, MA\n- 2x Reefers in Albany, NY\n- 1x Flatbed in Trenton, NJ\nTuesday, 07/25:\n- 5x 53' Dry Vans in Philadelphia, PA\n- 3x Reefers in Hartford, CT\nAll equipment is available for long-haul or regional routes.",
		},
		{
			Subject: "Capacity Update - Midwest Region",
			Body:    "Current available capacity for Midwest region:\n- Chicago, IL: 5 dry vans, 3 reefers\n- Detroit, MI: 2 dry vans, 1 flatbed\n- Indianapolis, IN: 4 dry vans\n- St. Louis, MO: 3 dry vans, 2 reefers\nAll trucks available starting 7/26. Contact dispatch to secure equipment.",
		},
		{
			Subject: "Truck Availability - Los Angeles",
			Body:    "We have the following trucks available in the Los Angeles area for next week:\n- Monday: 7 dry vans, 3 flatbeds\n- Tuesday: 5 dry vans, 2 reefers\n- Wednesday: 4 dry vans, 1 specialized heavy haul\nLooking for freight to move east. Competitive rates available.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if any returned labels correspond to capacity management category
		foundCategory := false
		capacityLabels := []string{
			string(TruckListLabel),
		}

		for _, label := range labels {
			for _, cLabel := range capacityLabels {
				if label == cLabel {
					foundCategory = true
					break
				}
			}
			if foundCategory {
				break
			}
		}

		assert.True(t, foundCategory, "Test email %d should be classified as capacity management category", i+1)
	}
}

//nolint:lll
func LiveTestLoadBuildingCategory(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "New shipment details - please create order",
			Body:    "Please create a new load with the following details:\nCustomer: Acme Tools Inc.\nCustomer PO#: PO-78901\nBOL#: BOL123456\nOrigin: Acme Manufacturing, 123 Factory Lane, Detroit, MI 48201\nContact: John Doe (313-555-1234)\nPickup Date: 07/28/2023, 08:00-12:00\nDestination: Acme Distribution Center, 456 Warehouse Blvd, Cleveland, OH 44115\nFreight Details: 10 pallets of machinery parts, Total weight: 12,500 lbs",
		},
		{
			Subject: "Load details for booking - Atlanta to Dallas",
			Body:    "Need to create a load for the following shipment:\nShipper: Global Electronics\nShipper Location: Atlanta, GA\nConsignee: Tech Warehouse\nConsignee Location: Dallas, TX\nProduct: Electronics components\nQuantity: 8 pallets\nWeight: 6,400 lbs\nReady Date: 07/30/2023\nDelivery Date: 08/02/2023\nEquipment: Dry Van\nSpecial Instructions: Liftgate required at delivery",
		},
		{
			Subject: "PO #45678 - Create load for shipping",
			Body:    "Please build a load in the system for this order:\nPO Number: 45678\nShipper: Westside Manufacturing\nOrigin Address: 789 Industrial Pkwy, Chicago, IL 60607\nConsignee: Eastcoast Retail\nDestination: 321 Commerce Ave, Boston, MA 02210\nCommodity: Retail merchandise\nPallets: 12\nWeight: 9,800 lbs\nDimensions: Standard 48x40 pallets\nPickup Window: 08/04/2023 between 9am-3pm\nDelivery Requested: 08/09/2023",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if any returned labels correspond to load building category
		foundCategory := false
		loadBuildingLabels := []string{
			string(LoadBuildingLabel),
		}

		for _, label := range labels {
			for _, lbLabel := range loadBuildingLabels {
				if label == lbLabel {
					foundCategory = true
					break
				}
			}
			if foundCategory {
				break
			}
		}

		assert.True(t, foundCategory, "Test email %d should be classified as load building category", i+1)
	}
}

//
// Label tests
//

//nolint:lll
func LiveTestDeliveryConfirmationLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "DELIVERED: Load #FTL22478 - Smith Manufacturing",
			Body:    "This is to confirm that load #FTL22478 for Smith Manufacturing has been delivered. Delivery completed on: July 12, 2023 at 14:35 EST. POD Status: Signed by receiver. Receiver Name: Alice Johnson. All items delivered in good condition. POD has been attached to this email.",
		},
		{
			Subject: "Delivery Confirmation - PO #12345",
			Body:    "We are pleased to confirm that your order (PO #12345) has been successfully delivered to your facility today at 10:15 AM. The delivery was received and signed for by Tom Wilson. All 8 pallets were delivered in good condition with no exceptions noted. Please let us know if you have any questions.",
		},
		{
			Subject: "Shipment ID #ABC789 - Delivery Complete",
			Body:    "This message confirms the successful delivery of Shipment ID #ABC789 to Dallas Distribution Center on July 18th at 3:45 PM CDT. The shipment was accepted by the receiving department and signed off by Manager James Rodriguez. All items accounted for. Thank you for your business.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the delivery confirmation label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(DeliveryConfirmationLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have delivery confirmation label", i+1)
	}
}

//nolint:lll
func LiveTestDriverInfoLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Driver information for tomorrow's pickup - Load #BK4501",
			Body:    "Hello, For tomorrow's pickup at your facility (Load #BK4501), our driver's information is as follows: Driver Name: Michael Rodriguez, Cell Phone: ************, CDL #: *********. The driver is expected to arrive between 9:00-10:00 AM. Please have the shipping documents ready.",
		},
		{
			Subject: "Driver and truck details for Load #TL9876",
			Body:    "For your upcoming shipment (Load #TL9876), here are the driver details: Name: John Smith, Phone: ************, Truck #: 789, Trailer #: 456, License Plate: ABC123. The driver has been instructed to check in at the security gate upon arrival. ETA 07/29/2023 at approximately 2:00 PM.",
		},
		{
			Subject: "DRIVER INFO: Order #XYZ-5678",
			Body:    "Please be advised that the following driver will be picking up Order #XYZ-5678 tomorrow: Driver: Sarah Johnson, Cell: ************, Truck: Freightliner Cascadia, Trailer #: 98765, License: FL-45678. Please notify your shipping department. The driver will call 1 hour before arrival.",
		},
		{
			Subject: "RE: Load #DEF789 - Driver Assignment",
			Body:    "In response to your request, we have assigned a driver for Load #DEF789. Driver Name: David Wilson, Contact Number: ************, CDL: **********, Truck Number: T-123, Trailer: 53' Dry Van #TR789. Driver will arrive at scheduled appointment time of 10:00 AM on July 30th.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the driver info label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(DriverInfoLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have driver info label", i+1)
	}
}

//nolint:lll
func LiveTestTrackingETALabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Tracking update for shipment #CS78923",
			Body:    "Here is the tracking update for your shipment #CS78923: Current Status: In Transit, Current Location: Columbus, OH, Tracking Number: 1ZTFE456789102, Last Update: July 14, 2023 - 16:25 EST, ETA at destination: July 15, 2023 between 13:00-15:00 EST. The driver has reported good weather conditions and no delays expected.",
		},
		{
			Subject: "ETA Update - Load #ABC123",
			Body:    "This is an update for your shipment (Load #ABC123). The truck is currently on I-75 near Cincinnati, OH. Based on current traffic and weather conditions, the estimated time of arrival at your facility is tomorrow, July 28th, at approximately 10:30 AM EDT. The driver will call dispatch if there are any changes to this ETA.",
		},
		{
			Subject: "Tracking Information - Order #XYZ789",
			Body:    "Your order #XYZ789 is now in transit. Current location: Denver, CO. The shipment is moving as scheduled and is expected to arrive at the destination on July 30th between 2:00-4:00 PM MDT. Tracking number: TR-789456123. You can use this tracking number on our website to get real-time updates.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the tracking/eta label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(TrackingETALabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have tracking/eta label", i+1)
	}
}

//nolint:lll
func LiveTestAppointmentSchedulingLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Request for delivery appointment - PO #45621",
			Body:    "We need to schedule a delivery appointment for the following shipment: PO #: 45621, Shipper: ABC Manufacturing, Consignee: XYZ Distribution Center, Freight: 8 pallets, 6,500 lbs. Preferred delivery dates: July 18, 2023 (8:00 AM - 2:00 PM), July 19, 2023 (8:00 AM - 2:00 PM), or July 20, 2023 (8:00 AM - 2:00 PM). Please confirm which date and time works best for your receiving department.",
		},
		{
			Subject: "Need to schedule pickup - Order #567890",
			Body:    "We would like to arrange a pickup for Order #567890. The shipment will be ready on July 28th, 2023. Our warehouse is open from 7:00 AM to 6:00 PM Monday through Friday. The load consists of 5 pallets of automotive parts weighing approximately 4,000 lbs total. Please let us know what time works best for your driver on the 28th.",
		},
		{
			Subject: "Delivery Appointment Request - Shipment #LTL12345",
			Body:    "We need to schedule a delivery appointment for Shipment #LTL12345: Consignee: Midwest Distribution, Address: 123 Warehouse Road, Indianapolis, IN 46202. The shipment is currently at our terminal and available for final delivery. Please provide available delivery windows for next week (Aug 1-5). This is a standard LTL shipment requiring a dock door.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the appointment scheduling label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(AppointmentSchedulingLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have appointment scheduling label", i+1)
	}
}

//nolint:lll
func LiveTestPickupConfirmationLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Pickup Confirmation - Order #ORD-98765",
			Body:    "This email confirms that your shipment (Order #ORD-98765) has been successfully picked up. Pickup completed on: July 11, 2023 at 11:20 CST. Location: Your Dallas Warehouse. Bill of Lading #: BOL-123456. Items: 4 pallets (3,200 lbs). All items in good condition. The shipment is now in transit to the destination.",
		},
		{
			Subject: "PICKED UP: Load #FTL-45678",
			Body:    "This is to confirm that Load #FTL-45678 has been successfully picked up from ABC Manufacturing on July 25, 2023 at 9:45 AM EDT. The driver has reported that all 8 pallets were loaded without issue. BOL has been signed by both parties. The shipment is now en route to the destination with an ETA of July 27th.",
		},
		{
			Subject: "Pickup Complete: Shipment #LTL-12345",
			Body:    "We are pleased to confirm that your Shipment #LTL-12345 has been picked up today at 2:30 PM PST from your facility in Portland. The driver has verified the piece count (12 boxes) and BOL #789123 has been signed. The freight is now in transit and scheduled for delivery as planned.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the pickup confirmation label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(PickupConfirmationLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have pickup confirmation label", i+1)
	}
}

//nolint:lll
func LiveTestAppointmentConfirmedLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Appointment Confirmed: Delivery on 07/22/2023",
			Body:    "Your delivery appointment has been confirmed: Load #: LTL-87654, Appointment Date: 07/22/2023, Appointment Time: 10:00 AM EST, Facility: Central Distribution Center, Address: 1234 Warehouse Ave, Chicago, IL 60007, Reference #: PO-56789, Dock Door: #14. Please ensure the driver arrives 15 minutes before the appointment time and has all necessary paperwork.",
		},
		{
			Subject: "CONFIRMED: Pickup Appointment for 08/02/2023",
			Body:    "This email confirms your pickup appointment has been scheduled for: Date: August 2, 2023, Time: 1:30 PM - 3:30 PM CST, Location: Phoenix Warehouse, 789 Industrial Blvd, Phoenix, AZ 85001. Reference Number: PU-12345. Please have all paperwork ready and freight properly packaged for pickup.",
		},
		{
			Subject: "Delivery Appointment Confirmation - PO #AB12345",
			Body:    "We have confirmed your delivery appointment for PO #AB12345: Date: August 5, 2023, Time: 9:00 AM EDT, Facility: Northeast Distribution Center, Dock #7. Contact person: James Smith (************). Please reference appointment #DEL-98765 upon arrival. All drivers must check in at security 15 minutes prior to the appointment time.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the appointment confirmed label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(AppointmentConfirmedLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have appointment confirmed label", i+1)
	}
}

//nolint:lll
func LiveTestCheckCallLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Check Call Update - Shipment #TR-56789",
			Body:    "Our driver has provided the following check call update for shipment #TR-56789: Current Status: Loaded, in transit. Current Location: 20 miles east of Denver, CO. Weather conditions: Clear. Traffic: Light. ETA to next stop: 2 hours (estimated arrival at 15:30 MST). Driver reports no issues with the freight or vehicle.",
		},
		{
			Subject: "Status Update: Load #FTL-12345 - Arrived at Shipper",
			Body:    "This is a check call notification for Load #FTL-12345. The driver has arrived at the shipper location (ABC Manufacturing, Chicago, IL) at 08:15 CST. Currently waiting to be loaded. Estimated loading time: 1.5 hours. Will provide another update once loading is complete. No issues reported at this time.",
		},
		{
			Subject: "Check Call: Load #LTL-45678 - Loaded and Departed",
			Body:    "Check call update for Load #LTL-45678: Status: Successfully loaded and departed from origin. Time of departure: 11:30 AM EDT. All freight secured properly. Current location: I-75 South, near Knoxville, TN. Weather and road conditions are good. ETA to destination: Tomorrow at approximately A2:00 PM EDT.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the check call label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(CheckCallLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have check call label", i+1)
	}
}

//nolint:lll
func LiveTestQuoteLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Rate Quote for Chicago to Memphis FTL",
			Body:    "Based on your request, here is our quote for the Chicago to Memphis lane: Quote #: Q-78945, Equipment: 53' Dry Van, Pickup: Chicago, IL, Delivery: Memphis, TN, Rate: $1,850.00 USD, Fuel Surcharge: $350.00 USD, Total Rate: $2,200.00 USD, Validity: 7 days. This rate includes: 2 hours free detention at both origin and destination, Standard liability coverage.",
		},
		{
			Subject: "QUOTE: Boston to New York City - LTL Service",
			Body:    "Please see our rate quote below for your LTL shipment: Origin: Boston, MA, Destination: New York City, NY, Commodity: Office Supplies, Weight: 2,500 lbs, Class: 70, Base Rate: $450.00, Fuel Surcharge (15%): $67.50, Accessorial Charges: Liftgate Service: $85.00, Total Quote: $602.50. Rate valid through 08/15/2023.",
		},
		{
			Subject: "FW: Rate Confirmation - Seattle to Portland",
			Body:    "Please find our quote for the Seattle to Portland lane as requested: Quote Reference: QT-123456, Service: Dedicated Truckload, Equipment: 53' Dry Van, Rate: $950.00, Fuel Surcharge: $175.00, Total: $1,125.00. Additional services available: After-hours delivery (+$150), Inside delivery (+$200). This quote is valid for 10 days from today.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the quote label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(CarrierQuoteResponseLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have carrier quote label", i+1)
	}
}

//nolint:lll
func LiveTestQuoteRequestLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Need rate for Orlando to Miami shipment ASAP",
			Body:    "We are looking for a competitive rate for the following shipment: Origin: Orlando, FL, Destination: Miami, FL, Pickup Date: July 25, 2023, Delivery Date: July 26, 2023, Commodity: Electronics (palletized), Weight: 5,000 lbs, Dimensions: 4 pallets, 48\"x40\"x50\" each, Special Requirements: Liftgate needed at delivery. Can you please provide your best rate for this lane? We need to book this shipment by tomorrow.",
		},
		{
			Subject: "Quote Request - Chicago to Detroit",
			Body:    "Please provide a rate quote for the following lane: From: Chicago, IL To: Detroit, MI Commodity: Auto parts (non-hazardous) Weight: 8,500 lbs Dimensions: 10 pallets (48\"x40\"x48\") Pickup: August 5th, 2023 Delivery: August 6th, 2023 Equipment Needed: Dry Van We'll need liftgate service at delivery. Please send your best rate as soon as possible.",
		},
		{
			Subject: "Requesting Pricing - TX to CA",
			Body:    "We're looking for competitive pricing on the following shipment: Origin: Dallas, TX Destination: Los Angeles, CA Freight: Consumer electronics Equipment: 53' dry van Pieces: 18 pallets Weight: 15,000 lbs Pickup window: Aug 10-12 Delivery required by: Aug 15 Please let me know what your best rate would be for this lane. We have similar volume weekly if rates are competitive.",
		},
		{
			Subject: "Request for quote",
			Body: `Vineland NJ 08360 USA 
					**Request for Quote** 245822
					**RFQ #:**
					**Date:**
					**PICK UP LOCATION**
					**SHIP TO LOCATION**
					**Pick Up Date:**
					4/7/2025
					**Delivery Date:**
					4/8/2025
					Danvers, MA 01923 USA
					**Orleans International, Inc.**
					30600 Northwestern Highway, Suite 300
					Farmington Hills, MI 48334 USA
					Phone:
					************
					Email:
					<EMAIL>
					271
					Devesa - Argentina: Sirloin CC Halal gn vp -
					Chilled (012825-2SM 0767)
					**Units:**
					**Product:**
					Vineland, NJ
					**Pallet Config.**
					12,376.74
					**Wt:**
					_,_
					**Estimated Total Units:** 271
					**Estimated Net Wt:** 12,376.74 **Estimated Total $:** $61,883.70`,
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the quote request label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(QuoteRequestLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have quote request label", i+1)
	}
}

//nolint:lll
func LiveTestCarrierInfoLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Carrier information for load #LTL53792",
			Body:    "Hello, Please find the carrier information for your load #LTL53792: Carrier Name: FastFreight Transport LLC, MC#: 123456, DOT#: 7891011, Insurance Certificate: Attached, Truck #: T-789, Trailer #: TR-456, Equipment Type: 53' dry van. Driver Name: John Smith, Driver Phone: ************, Driver License #: **********. Please let me know if you need any additional information.",
		},
		{
			Subject: "Carrier Details - Shipment #ABC-12345",
			Body:    "For your upcoming shipment #ABC-12345, here are the carrier details: Carrier: Reliable Trucking Inc., MC Number: 654321, DOT: 9876543, SCAC: RELT, Insurance: $1M Cargo, $2M Liability (certs attached), Equipment Type: Flatbed with Tarps, Contact: Dispatch Office (************, <EMAIL>)",
		},
		{
			Subject: "FW: Carrier Assignment for PO #56789",
			Body:    "We've assigned the following carrier for your PO #56789: Carrier Name: Midwest Express Logistics, USDOT: 1234567, MC#: 876543, Insurance Information: All certificates current and on file, Equipment: 53' Temperature Controlled, Truck Number: MEL-789, Trailer: R-456, Dispatcher Contact: Jane Smith (************)",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the carrier info label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(CarrierInfoLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have carrier info label", i+1)
	}
}

//nolint:lll
func LiveTestTruckListLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "Available trucks for week of 07/24 - Northeast Region",
			Body:    "Hello partners, Here is our list of available trucks for the week of 07/24/2023: Monday, 07/24: 3x 53' Dry Vans in Boston, MA, 2x Reefers in Albany, NY, 1x Flatbed in Trenton, NJ. Tuesday, 07/25: 5x 53' Dry Vans in Philadelphia, PA, 3x Reefers in Hartford, CT, 2x Flatbeds in Pittsburgh, PA. All equipment is available for long-haul or regional routes. Please contact our dispatch team to secure capacity.",
		},
		{
			Subject: "Truck Availability - West Coast",
			Body:    "Available trucks for next week (Aug 7-11): Los Angeles, CA: 7 dry vans, 3 reefers, 2 flatbeds; Portland, OR: 4 dry vans, 2 reefers; Seattle, WA: 5 dry vans, 1 specialized. All equipment is in good condition with professional drivers. Looking for freight heading east or within the region. Contact dispatch to secure these trucks before they're gone.",
		},
		{
			Subject: "CAPACITY ALERT: Available Equipment in Midwest",
			Body:    "Attention customers: We have the following equipment available in the Midwest region: Chicago: 5 dry vans, 2 reefers, 1 flatbed; Detroit: 3 dry vans; Indianapolis: 4 dry vans, 2 reefers; St. Louis: 3 dry vans, 1 step deck. All equipment available starting Monday, August 14th. Looking for outbound freight. Call ************ to book.",
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the truck list label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(TruckListLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have truck list label", i+1)
	}
}

//nolint:lll
func LiveTestLoadBuildingLabel(ctx context.Context, t *testing.T) {
	testEmails := []models.IngestedEmail{
		{
			Subject: "New shipment details - please create order",
			Body:    "Please create a new load with the following details: Customer: Acme Tools Inc., Customer PO#: PO-78901, BOL#: BOL123456, Origin: Acme Manufacturing, 123 Factory Lane, Detroit, MI 48201, Contact: John Doe (313-555-1234), Pickup Date: 07/28/2023, 08:00-12:00, Destination: Acme Distribution Center, 456 Warehouse Blvd, Cleveland, OH 44115, Contact: Jane Smith (216-555-5678), Freight Details: 10 pallets of machinery parts, Total weight: 12,500 lbs",
		},
		{
			Subject: "New Load Request - PO #AB12345",
			Body:    "Please build a load for the following shipment: PO Number: AB12345, Shipper: Global Electronics, 789 Industry Way, Chicago, IL 60607, Receiver: Tech Warehouse, 456 Distribution Ave, Atlanta, GA 30301, Commodity: Computer components (non-hazardous), Packaging: 8 pallets, 48x40x60 each, Total Weight: 6,400 lbs, Ready Date: 08/10/2023, Requested Delivery: 08/12/2023, Equipment: 53' Dry Van, Service Level: Standard",
		},
		{
			Subject: "Create Shipment - Order #XYZ-789",
			Body:    "Need to create a shipment for the following order: Order #: XYZ-789, Customer: Johnson Manufacturing, Origin: Johnson Plant, 123 Production Dr, Dallas, TX 75201, Destination: Regional DC, 456 Logistics Pkwy, Memphis, TN 38101, Product: Industrial supplies, Quantity: 15 pallets, Weight: 11,250 lbs, Dimensions: Standard 48x40 pallets, Pickup window: 08/15/2023 (8am-5pm), Delivery requirement: 08/17/2023 (by end of day), Notes: Dock-to-dock service",
		},
		{
			Subject: "One Release - A van for pickup tomorrow",
			Body: `
				Warehouse Code:
				Address:
				115-0010
				Ship To:
				5019494
				08/21/2024
				Shipping Order
				Shipping Order No.:
				Order Date:
				Customer PO:
				Ship From:
				Kronospan PB, LLC
				Kronospan PB, LLC
				1 Kronospan Way
				Eastaboga, AL United States
				MJB Anniston
				1608 Frank Akers Road
				Anniston, AL 36207
				1585 High Meadows Way
				Cedar Hill, TX 75104
				www.mjbwood.com
				************
				Page 1 of 1
				Phone No.:
				Phone No.:
				Salesperson
				Ship Mode
				Freight Terms
				VAN
				MILL
				Due Date
				09/30/2024
				Order Qty.
				Balance
				Received
				Cust. Part No.
				Description
				Footage
				Est. Weight
				Line Item Ref. No.
				Qty. / Pallet
				585
				45
				585 PC
				500544 - 5/8" x 49" x 97" PBD Raw
				Kronospan-Eastaboga PBD(95086)
				0.00 SF
				41,842.73 lbs
				Location:
				Shipping Notes:
				Total Footage:
				Total Estimated Weight:
				Carrier Name:
				Only the products that are identified as such on this document are FSC® certified.
				Unique Item Count:
				Total Qty.:
				1
				585 PC
				Carrier is to deliver to specified "Ship To" address only. If a change of delivery address is requested, please contact your
				MJB Wood Group representative.
				EPA TSCA Title VI & CARB 93120 Ph2 Compliant, for applicable products.
				Load must be 100% tarped - no exposed material. Load must be completely tarped before leaving the loading facility -
				do NOT untarp until instructed to do so by the receiver.
				0.00 SF
				41,842.73 lbs
				Total Pallets:
				13
			`,
		},
	}

	for i, email := range testEmails {
		labels, approach, _, _ := Classify(ctx, email)

		// Verify we used LLM approach
		assert.Equal(t, models.LLMApproach, approach, "Test email %d should be classified with LLM", i+1)

		// Check if the load building label is present
		foundLabel := false
		for _, label := range labels {
			if label == string(LoadBuildingLabel) {
				foundLabel = true
				break
			}
		}

		assert.True(t, foundLabel, "Test email %d should have load building label", i+1)
	}
}

// Helper function to get the name of a function
func getFunctionName(f TestFunc) string {
	// A better approach that doesn't use direct comparisons
	// Use a map to associate functions with their names
	functionNames := map[uintptr]string{
		getFunctionPointer(LiveTestTrackAndTraceCategory):      "LiveTestTrackAndTraceCategory",
		getFunctionPointer(LiveTestSchedulingCategory):         "LiveTestSchedulingCategory",
		getFunctionPointer(LiveTestQuotingCategory):            "LiveTestQuotingCategory",
		getFunctionPointer(LiveTestCapacityManagementCategory): "LiveTestCapacityManagementCategory",
		getFunctionPointer(LiveTestLoadBuildingCategory):       "LiveTestLoadBuildingCategory",
		getFunctionPointer(LiveTestDeliveryConfirmationLabel):  "LiveTestDeliveryConfirmationLabel",
		getFunctionPointer(LiveTestDriverInfoLabel):            "LiveTestDriverInfoLabel",
		getFunctionPointer(LiveTestTrackingETALabel):           "LiveTestTrackingETALabel",
		getFunctionPointer(LiveTestAppointmentSchedulingLabel): "LiveTestAppointmentSchedulingLabel",
		getFunctionPointer(LiveTestPickupConfirmationLabel):    "LiveTestPickupConfirmationLabel",
		getFunctionPointer(LiveTestAppointmentConfirmedLabel):  "LiveTestAppointmentConfirmedLabel",
		getFunctionPointer(LiveTestCheckCallLabel):             "LiveTestCheckCallLabel",
		getFunctionPointer(LiveTestQuoteLabel):                 "LiveTestQuoteLabel",
		getFunctionPointer(LiveTestQuoteRequestLabel):          "LiveTestQuoteRequestLabel",
		getFunctionPointer(LiveTestCarrierInfoLabel):           "LiveTestCarrierInfoLabel",
		getFunctionPointer(LiveTestTruckListLabel):             "LiveTestTruckListLabel",
		getFunctionPointer(LiveTestLoadBuildingLabel):          "LiveTestLoadBuildingLabel",
	}

	ptr := getFunctionPointer(f)
	if name, ok := functionNames[ptr]; ok {
		return name
	}
	return "UnknownTest"
}

// Helper function to get a pointer to a function
func getFunctionPointer(f TestFunc) uintptr {
	// This is a trick to get a unique identifier for a function
	// It's not 100% portable but works for our use case
	return *(*uintptr)(unsafe.Pointer(&f))
}
