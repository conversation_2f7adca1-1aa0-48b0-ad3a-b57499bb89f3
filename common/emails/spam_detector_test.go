package emails

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestSpamDetector_OutOfOfficeReplies(t *testing.T) {
	detector := NewSpamDetector()

	tests := []struct {
		name       string
		email      models.IngestedEmail
		expectSpam bool
		reason     SpamDetectionReason
	}{
		{
			name: "out of office auto-reply",
			email: models.IngestedEmail{
				Subject: "Out of Office Auto Reply",
				Body:    "I am out of the office and will return on Monday.",
			},
			expectSpam: true,
			reason:     ReasonOutOfOffice,
		},
		{
			name: "automatic reply",
			email: models.IngestedEmail{
				Subject: "Automatic Reply",
				Body:    "This is an automated response. I will get back to you.",
			},
			expectSpam: true,
			reason:     ReasonOutOfOffice,
		},
		{
			name: "away message",
			email: models.IngestedEmail{
				Subject: "I am away",
				Body:    "Currently away from my desk.",
			},
			expectSpam: true,
			reason:     ReasonOutOfOffice,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isSpam, reason := detector.IsSpam(tt.email)
			assert.Equal(t, tt.expectSpam, isSpam, "spam detection mismatch")
			if tt.expectSpam {
				assert.Equal(t, tt.reason, reason, "spam reason mismatch")
			}
		})
	}
}

func TestSpamDetector_BounceEmails(t *testing.T) {
	detector := NewSpamDetector()

	tests := []struct {
		name       string
		subject    string
		expectSpam bool
	}{
		{
			name:       "delivery failed",
			subject:    "Delivery Failed",
			expectSpam: true,
		},
		{
			name:       "non-delivery report",
			subject:    "Non-Delivery Report",
			expectSpam: true,
		},
		{
			name:       "returned mail",
			subject:    "Returned Mail",
			expectSpam: true,
		},
		{
			name:       "legitimate subject",
			subject:    "Load Confirmation",
			expectSpam: false,
		},
		{
			name:       "forwarded failure - Fw: Failure to deliver",
			subject:    "Fw: Failure to deliver",
			expectSpam: true,
		},
		{
			name:       "forwarded failure - Fwd: Undeliverable",
			subject:    "Fwd: Undeliverable",
			expectSpam: true,
		},
		{
			name:       "reply failure - Re: Delivery Failed",
			subject:    "Re: Delivery Failed",
			expectSpam: true,
		},
		{
			name:       "mail system error",
			subject:    "Mail system error occurred",
			expectSpam: true,
		},
		{
			name:       "permanent failure",
			subject:    "Permanent failure for recipient",
			expectSpam: true,
		},
		{
			name:       "message delivery failed",
			subject:    "Message delivery failed",
			expectSpam: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			email := models.IngestedEmail{
				Subject: tt.subject,
				Body:    "Test body",
			}
			isSpam, _ := detector.IsSpam(email)
			assert.Equal(t, tt.expectSpam, isSpam, "bounce detection mismatch")
		})
	}
}

func TestSpamDetector_FalsePositives(t *testing.T) {
	detector := NewSpamDetector()

	// These should NOT be detected as spam
	tests := []struct {
		name  string
		email models.IngestedEmail
	}{
		{
			name: "legitimate business email with pickup info",
			email: models.IngestedEmail{
				Subject: "New Load - Pickup from Chicago",
				Body: `Hi,

Please see the attached shipping order. 

Pickup: 123 Logistics Way, Chicago, IL
Delivery: 456 Distribution Center, Miami, FL
Load: 12 pallets, 8000 lbs
Pickup time: Monday 8:00 AM
Appointment required

Thanks`,
			},
		},
		{
			name: "invoice with rates",
			email: models.IngestedEmail{
				Subject: "Invoice #12345",
				Body: `Invoice Details:
Shipment: ORD-2024-123
Weight: 5000 lbs
Rate: $2500
Origin: Houston, TX
Destination: Los Angeles, CA`,
			},
		},
		{
			name: "quote response",
			email: models.IngestedEmail{
				Subject: "Quote Response for Load",
				Body: `Rate Quote:
Origin: Atlanta
Destination: New York
Distance: 850 miles
Weight: 10 pallets
Rate: $3500
Pickup: Next Friday`,
			},
		},
		{
			name: "carrier availability - dock high unit (actual reported case)",
			email: models.IngestedEmail{
				Subject: "KY-CA, 26ft DOCK HIGH LG/PJ ETA 1H45MINS RATE $3890",
				Body: `True dock high unit with LG&PJ and air ride

2 rows of E-Tracks and straps to secure the freight

Empty and ready

--

Tom M (224)335-7451
Tracking 24/7 847-600-9795
MC# 1279576
1030 S La Grange Rd, suite 29, La Grange IL 60525`,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isSpam, reason := detector.IsSpam(tt.email)
			assert.False(t, isSpam, "legitimate email incorrectly marked as spam: %s", reason)
		})
	}
}

func TestSpamDetector_SubjectBlacklist(t *testing.T) {
	detector := NewSpamDetector()

	tests := []struct {
		name       string
		email      models.IngestedEmail
		expectSpam bool
		reason     SpamDetectionReason
	}{
		{
			name: "exact match - Microsoft 365 security",
			email: models.IngestedEmail{
				Subject: "Microsoft 365 security: You have messages in quarantine",
				Body:    "Check your quarantine.",
			},
			expectSpam: true,
			reason:     ReasonSubjectBlacklist,
		},
		{
			name: "exact match - Coro Notification",
			email: models.IngestedEmail{
				Subject: "Coro Notification: Spam",
				Body:    "This is spam.",
			},
			expectSpam: true,
			reason:     ReasonSubjectBlacklist,
		},
		{
			name: "substring match - DAT broker newsletter",
			email: models.IngestedEmail{
				Subject: "Weekly DAT broker newsletter: Rate Updates",
				Body:    "Market rates have changed.",
			},
			expectSpam: true,
			reason:     ReasonSubjectBlacklist,
		},
		{
			name: "no match - similar but different",
			email: models.IngestedEmail{
				Subject: "Microsoft security: Please update your password",
				Body:    "Your account needs attention.",
			},
			expectSpam: false,
			reason:     "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isSpam, reason := detector.IsSpam(tt.email)
			assert.Equal(t, tt.expectSpam, isSpam, "subject blacklist detection mismatch")
			if tt.expectSpam {
				assert.Equal(t, tt.reason, reason, "spam reason mismatch")
			}
		})
	}
}

func TestSpamDetector_ExactVsSubstringMatching(t *testing.T) {
	detector := NewSpamDetector()

	tests := []struct {
		name       string
		email      models.IngestedEmail
		expectSpam bool
		reason     SpamDetectionReason
	}{
		{
			name: "exact NDR match - bounce",
			email: models.IngestedEmail{
				Subject: "bounce",
				Body:    "Test body",
			},
			expectSpam: true,
			reason:     ReasonBounceOrNDR,
		},
		{
			name: "no match - bounce in middle of word",
			email: models.IngestedEmail{
				Subject: "Bounced back with great energy",
				Body:    "Test body",
			},
			expectSpam: false,
			reason:     "",
		},
		{
			name: "substring match - delivery failed",
			email: models.IngestedEmail{
				Subject: "Your message: delivery failed urgently",
				Body:    "Test body",
			},
			expectSpam: true,
			reason:     ReasonBounceOrNDR,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isSpam, reason := detector.IsSpam(tt.email)
			assert.Equal(t, tt.expectSpam, isSpam, "matching mode detection mismatch")
			if tt.expectSpam {
				assert.Equal(t, tt.reason, reason, "spam reason mismatch")
			}
		})
	}
}

func TestSpamDetector_SingletonPatternInitialization(t *testing.T) {
	// Create multiple detectors to ensure singleton patterns are reused
	detector1 := NewSpamDetector()
	detector2 := NewSpamDetector()

	// Both detectors should identify the same NDR email as spam
	email := models.IngestedEmail{
		Subject: "Delivery Failed",
		Body:    "Message could not be delivered",
	}

	isSpam1, reason1 := detector1.IsSpam(email)
	isSpam2, reason2 := detector2.IsSpam(email)

	assert.True(t, isSpam1, "first detector should identify NDR email as spam")
	assert.Equal(t, ReasonBounceOrNDR, reason1, "first detector should return correct reason")

	assert.True(t, isSpam2, "second detector should identify NDR email as spam")
	assert.Equal(t, ReasonBounceOrNDR, reason2, "second detector should return correct reason")

	// Both should get the same result (patterns are shared via singleton)
	assert.Equal(t, isSpam1, isSpam2, "both detectors should return same result")
	assert.Equal(t, reason1, reason2, "both detectors should return same reason")
}

// BenchmarkSpamDetector_IsSpam measures spam detection performance
func BenchmarkSpamDetector_IsSpam(b *testing.B) {
	detector := NewSpamDetector()

	benchmarks := []struct {
		name  string
		email models.IngestedEmail
	}{
		{
			name: "legitimate email",
			email: models.IngestedEmail{
				Subject: "New Load Available - Chicago to Miami",
				Body: `Hi,

We have an available load with the following details:

Origin: Chicago, IL 60601
Destination: Miami, FL 33101
Weight: 8,000 lbs
Equipment: 53ft Dry Van
Rate: $2,500
Pickup Date: Tomorrow at 8:00 AM
Delivery: 3 days
Contact: (312) 555-0123

Best regards,
John Smith`,
			},
		},
		{
			name: "bounce/NDR email",
			email: models.IngestedEmail{
				Subject: "Delivery Failed: Message Could Not Be Sent",
				Body:    "The message you sent could not be delivered to the recipient.",
			},
		},
		{
			name: "out of office email",
			email: models.IngestedEmail{
				Subject: "Out of Office Auto Reply",
				Body:    "I am out of the office and will return on Monday.",
			},
		},
		{
			name: "subject blacklist match",
			email: models.IngestedEmail{
				Subject: "Microsoft 365 security: You have messages in quarantine",
				Body:    "You have messages in quarantine.",
			},
		},
		{
			name: "substring pattern match",
			email: models.IngestedEmail{
				Subject: "Weekly Update: DAT broker newsletter: Current Market Rates",
				Body:    "Here are this week's market rates...",
			},
		},
		{
			name: "long legitimate email",
			email: models.IngestedEmail{
				Subject: "Complex Load Details and Special Requirements",
				Body: `Dear Carrier,

This is a detailed load offer with multiple requirements and special instructions.

Load Information:
- Origin: New York, NY 10001
- Destination: Los Angeles, CA 90001
- Weight: 15,000 lbs
- Equipment: 53ft Reefer Van
- Rate: $4,200
- Pickup: Monday 6:00 AM
- Delivery Window: Friday 5:00 PM

Special Requirements:
1. Keep temperature between 35-38 degrees F
2. No detention time allowed
3. Notify before delivery
4. Require BOL signature
5. Proof of delivery required

Contact Information:
- Dispatch: (212) 555-0100
- Emergency: (212) 555-0101
- DOT: 1234567

Thank you for your attention to this load. Please confirm availability.

Best regards,
Sarah Johnson
Load Manager`,
			},
		},
	}

	for _, bm := range benchmarks {
		b.Run(bm.name, func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				detector.IsSpam(bm.email)
			}
		})
	}
}

// BenchmarkSpamDetector_IsSpam_Parallel measures spam detection performance under parallel load
func BenchmarkSpamDetector_IsSpam_Parallel(b *testing.B) {
	detector := NewSpamDetector()

	email := models.IngestedEmail{
		Subject: "New Load Available - Chicago to Miami",
		Body: `Hi,

We have an available load with the following details:

Origin: Chicago, IL 60601
Destination: Miami, FL 33101
Weight: 8,000 lbs
Equipment: 53ft Dry Van
Rate: $2,500
Pickup Date: Tomorrow at 8:00 AM
Delivery: 3 days
Contact: (312) 555-0123

Best regards,
John Smith`,
	}

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			detector.IsSpam(email)
		}
	})
}
