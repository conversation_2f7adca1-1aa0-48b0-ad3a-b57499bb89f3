package emails

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	rulesDB "github.com/drumkitai/drumkit/common/rds/rules"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

var ForwardedMessageTTL = 72 * time.Hour

// GetRedisKeyForwardedThread returns the Redis key for a thread that has forwarded at least one message.
func GetRedisKeyForwardedThread(ruleID uint, threadID string) string {
	return fmt.Sprintf("forwarded-rule-%d-thread-%s", ruleID, threadID)
}

// GetRedisKeyForwardedMessage returns the Redis key for a message that is either
// in the process of being forwarded or has been forwarded to prevent duplicate
// forwarding.
func GetRedisKeyForwardedMessage(ruleID uint, msgExternalID string) string {
	return fmt.Sprintf("forwarded-rule-%d-external-id-%s", ruleID, msgExternalID)
}

func GetRedisKeyForwardedRFC(ruleID uint, genEmailRFC string) string {
	return fmt.Sprintf("forwarded-rule-%d-generated-rfc-id-%s", ruleID, genEmailRFC)
}

// ProcessForwardingRules checks if an email should be forwarded based on its labels and categories
// and forwards it to the appropriate recipients if needed.
func ProcessForwardingRules(
	ctx context.Context,
	email models.Email,
	service *models.Service,
	sqsClient sqsclient.API,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "ProcessForwardingRules", nil)
	defer func() { metaSpan.End(nil) }()

	if service == nil || !service.IsEmailForwardingEnabled {
		return nil
	}

	rules, err := rulesDB.GetForwardingRulesByUserAndService(ctx, email.UserID, service.ID)
	if err != nil {
		return fmt.Errorf("error getting email forwarding rules: %w", err)
	}

	if len(rules) == 0 {
		log.Info(ctx, "no forwarding rules found for service", zap.Uint("serviceID", service.ID))
		return nil
	}

	var emailLabels []string
	if email.Labels != "" {
		emailLabels = strings.Split(email.Labels, ",")
	}

	// Check each rule
	for _, rule := range rules {
		ctx = log.With(ctx, zap.Uint("ruleID", rule.ID), zap.String("ruleDescription", rule.Description))

		shouldForward := false

		if rule.UserID != nil && *rule.UserID != email.UserID {
			log.Info(ctx, "rule is not applicable to this user", zap.Uint("ruleID", rule.ID))
			continue
		}

		// Check if thread has been forwarded by this rule before
		wasThreadForwarded, err := wasThreadForwarded(ctx, email.ThreadID, rule.ID)
		if err != nil {
			log.Error(ctx, "error checking if thread was forwarded", zap.Error(err))
		} else if wasThreadForwarded && rule.ForwardSubsequentEmailsInThread {
			log.Info(ctx, "thread was previously forwarded, forwarding email", zap.String("threadID", email.ThreadID))

			err := forwardEmail(ctx, email, &rule, wasThreadForwarded, sqsClient)
			if err != nil {
				log.Error(
					ctx,
					"critical: error forwarding subsequent email in thread",
					zap.Error(err),
					zap.String("threadToForward", email.ThreadID),
				)
			}

			continue
		}

		// Categories take precedence over labels
		if len(rule.EmailCategories) > 0 {
			for _, ruleCategory := range rule.EmailCategories {
				if ruleCategory == "all" {
					shouldForward = true
					break
				}
				categoryLabels := GetAllLabelsForCategory(EmailCategory(ruleCategory))
				if categoryLabels == nil {
					log.Warn(ctx, "no labels found for category in rule", zap.String("ruleCategory", ruleCategory))
					continue
				}

				// Check if any of the email's labels match the category's labels
				for _, label := range emailLabels {
					if slices.Contains(categoryLabels, EmailLabel(label)) {
						shouldForward = true
						break
					}
				}
			}
		} else if len(rule.EmailLabels) > 0 {
			// Check if any of the email's labels match the rule's labels
			for _, label := range emailLabels {
				if label == "all" {
					shouldForward = true
					break
				}
				if slices.Contains(rule.EmailLabels, label) {
					shouldForward = true
					break
				}
			}
		}

		if shouldForward {
			err := forwardEmail(ctx, email, &rule, wasThreadForwarded, sqsClient)
			if err != nil {
				log.Error(ctx, "error forwarding email", zap.Error(err))
				continue // Continue with other rules even if one fails
			}

		}
	}

	return nil
}

// forwardEmail checks if an email should be forwarded and forwards it if needed to the specified recipients.
func forwardEmail(
	ctx context.Context,
	email models.Email,
	rule *models.EmailForwardingRule,
	wasThreadForwardedBefore bool,
	sqsClient sqsclient.API,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "forwardEmail", nil)
	defer func() { metaSpan.End(err) }()

	skip, err := SkipSelfForward(ctx, email.UserID, email.RFCMessageID, rule.ID)
	if err != nil {
		log.Error(ctx, "error checking if message was self-forwarded, could lead to duplicates", zap.Error(err))
	} else if skip {
		log.Info(ctx, "message was self-forwarded, skipping")
		return nil
	}

	log.Info(ctx, "forwarding email based on rule")

	payload := sqsclient.SendEmailSQSEventBody{
		Email:                    &email,
		Rule:                     rule,
		WasThreadForwardedBefore: wasThreadForwardedBefore,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("error marshalling forwardingpayload: %w", err)
	}

	log.Info(ctx, "sending email to SQS", zap.String("queueURL", env.Vars.SendEmailSQSURL))
	_, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:    aws.String(env.Vars.SendEmailSQSURL),
		MessageBody: aws.String(string(payloadBytes)),
	}, func(o *sqs.Options) {
		o.RetryMaxAttempts = 3
	})
	if err != nil {
		return fmt.Errorf("error sending email to SQS: %w", err)
	}

	return nil
}

// skipDuplicateForwarding checks if a message has been forwarded before
// by first checking Redis and then RDS as a fallback
func SkipDuplicateForward(ctx context.Context, msgExternalID string, ruleID uint) (bool, error) {
	_, found, redisErr := redis.GetKey[string](ctx, GetRedisKeyForwardedMessage(ruleID, msgExternalID))
	if redisErr == nil && found {
		return true, nil
	}

	if !errors.Is(redisErr, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for forwarded message, falling back to RDS", zap.Error(redisErr))
	}

	// Fallback to RDS
	genEmail, err := genEmailDB.GetByForwardedMessageID(ctx, msgExternalID, ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("error checking RDS for forwarded message: %w", err)
	}

	return genEmail != nil && genEmail.ID != 0, nil
}

// skipSelfForward checks if the email is one the user already forwarded to him/herself
// and therefore should not duplicate forwarding. This is particularly helpful for when processing outgoing emails
// is fully supported in Drumkit pipeline.
func SkipSelfForward(ctx context.Context, userID uint, msgRFCID string, ruleID uint) (bool, error) {
	_, found, redisErr := redis.GetKey[string](ctx, GetRedisKeyForwardedRFC(ruleID, msgRFCID))
	if redisErr == nil && found {
		return true, nil
	}

	if !errors.Is(redisErr, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for self-forwarded message, falling back to RDS", zap.Error(redisErr))
	}

	// Fallback to RDS
	_, err := genEmailDB.GetForwardByGeneratedRFCID(ctx, userID, msgRFCID, ruleID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, fmt.Errorf("error checking if message was self-forwarded: %w", err)
	}

	return true, nil
}

// wasThreadForwarded checks if a thread has been forwarded before by this rule.
// If yes and rule.ForwardSubsequentEmailsInThread = true, then email is forwarded regardless of labeling
func wasThreadForwarded(ctx context.Context, threadID string, ruleID uint) (bool, error) {
	if threadID == "" {
		return false, errors.New("unexpected empty threadID")
	}

	// Check Redis first
	_, found, err := redis.GetKey[string](ctx, GetRedisKeyForwardedThread(ruleID, threadID))
	if err == nil && found {
		return true, nil
	}
	if !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error checking redis for forwarded thread", zap.Error(err))
	}

	// Fallback to RDS
	emails, err := genEmailDB.GetByForwardedThreadID(ctx, threadID, ruleID)
	if err != nil {
		return false, fmt.Errorf("error checking RDS for forwarded thread: %w", err)
	}

	return len(emails) > 0, nil
}
