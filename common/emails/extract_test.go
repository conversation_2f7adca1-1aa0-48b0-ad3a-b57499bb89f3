package emails

import (
	"context"
	"encoding/base64"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
)

func TestGmailMessageWithTextPlainPart(t *testing.T) {
	ctx := context.Background()
	messageContent := "Hello, world!"

	encodedContent := base64.URLEncoding.EncodeToString([]byte(messageContent))

	message := &gmail.MessagePart{
		MimeType: "text/plain",
		Body: &gmail.MessagePartBody{
			Data: encodedContent,
		},
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, messageContent, result)
}

func TestGmailMessageWithTextHtmlPart(t *testing.T) {
	ctx := context.Background()
	htmlContent := "<p>Hello, <strong>world!</strong></p>"
	expectedMarkdown := "Hello, **world!**"

	encodedContent := base64.URLEncoding.EncodeToString([]byte(htmlContent))

	message := &gmail.MessagePart{
		MimeType: "text/html",
		Body: &gmail.MessagePartBody{
			Data: encodedContent,
		},
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, expectedMarkdown, strings.TrimSpace(result))
}

func TestGmailMessageWithBothHtmlAndPlainText(t *testing.T) {
	ctx := context.Background()
	htmlContent := "<p>Hello, <strong>world!</strong></p>"
	plainTextContent := "Hello, world!"
	expectedMarkdown := "Hello, **world!**"

	encodedHTMLContent := base64.URLEncoding.EncodeToString([]byte(htmlContent))
	encodedPlainTextContent := base64.URLEncoding.EncodeToString([]byte(plainTextContent))

	message := &gmail.MessagePart{
		MimeType: "multipart/alternative",
		Parts: []*gmail.MessagePart{
			{
				MimeType: "text/plain",
				Body: &gmail.MessagePartBody{
					Data: encodedPlainTextContent,
				},
			},
			{
				MimeType: "text/html",
				Body: &gmail.MessagePartBody{
					Data: encodedHTMLContent,
				},
			},
		},
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, expectedMarkdown, strings.TrimSpace(result))
}

func TestGmailMessageWithNoTextPart(t *testing.T) {
	ctx := context.Background()

	message := &gmail.MessagePart{
		MimeType: "multipart/mixed",
		Parts:    []*gmail.MessagePart{},
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, "", result)
}

func TestMsclientBodyWithTextPlain(t *testing.T) {
	ctx := context.Background()
	bodyContent := "Hello, world!"

	message := &msclient.Body{
		ContentType: "text",
		Content:     bodyContent,
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, bodyContent, result)
}

func TestMsclientBodyWithTextHtml(t *testing.T) {
	ctx := context.Background()
	htmlContent := "<p>Hello, <strong>world!</strong></p>"
	expectedMarkdown := "Hello, **world!**"

	message := &msclient.Body{
		ContentType: "html",
		Content:     htmlContent,
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, expectedMarkdown, strings.TrimSpace(result))
}

func TestMsclientBodyWithUnsupportedContentType(t *testing.T) {
	ctx := context.Background()

	message := &msclient.Body{
		ContentType: "application/json",
		Content:     `{"key": "value"}`,
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.NoError(t, err)
	assert.Equal(t, "", result)
}

func TestUnsupportedMessageType(t *testing.T) {
	ctx := context.Background()

	message := 12345 // Unsupported type

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.Error(t, err)
	assert.Equal(t, "unsupported message type: 12345", err.Error())
	assert.Equal(t, "", result)
}

func TestGetPartBodyBase64DecodeError(t *testing.T) {
	ctx := context.Background()

	invalidBase64Data := "!!!invalid base64!!!"

	message := &gmail.MessagePart{
		MimeType: "text/plain",
		Body: &gmail.MessagePartBody{
			Data: invalidBase64Data,
		},
	}

	result, _, err := plaintextOrMarkdown(ctx, message)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "base64 decoding of text/plain part failed")
	assert.Equal(t, "", result)
}
