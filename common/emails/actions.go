package emails

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"sync"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/customer/redwood"
	"github.com/drumkitai/drumkit/common/integrations/llm"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	batchQuoteDB "github.com/drumkitai/drumkit/common/rds/batch_quote"
	carrier_quoteDB "github.com/drumkitai/drumkit/common/rds/carrier_quote"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	suggestionDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
	"github.com/drumkitai/drumkit/common/rds/vector"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

const (
	// list of 3rd party quote submission urls
	petroChoice = "https://petrochoice.mercurygate.net/MercuryGate/transport/quoteReply.jsp"
)

// Based on email labels, run corresponding actions like appointment extraction, quote extraction, etc.
// Fails-open on errors and sends to Sentry.
func RunActions(ctx context.Context, email models.Email, opts ...Option) {
	ctx, metaSpan := otel.StartSpan(ctx, "RunActions", nil)
	defer func() { metaSpan.End(nil) }()

	options := &Options{
		Service:        nil,
		EmailReprocess: false,
		SQSClient:      nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	// Process forwarding rules asynchronously
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		sentry.WithHub(ctx, func(ctx context.Context) {
			if err := ProcessForwardingRules(ctx, email, options.Service, options.SQSClient); err != nil {
				log.Error(ctx, "error processing forwarding rules", zap.Error(err))
			}
		})
	}()

	isQuoteRequestSuggestionEnabled := options.Service != nil &&
		(options.Service.IsCarrierNetworkQuotingEnabled || options.Service.IsQuickQuoteEnabled)
	isLoadBuildingEnabled := options.Service != nil && options.Service.IsLoadBuildingEnabled

	labels := strings.Split(email.Labels, ",")

	// Use goroutines to process labels concurrently
	var mu sync.Mutex
	var errors []error

	for _, label := range labels {
		wg.Add(1)
		go func(label string) {
			defer wg.Done()
			var err error

			switch label {
			case string(AppointmentConfirmedLabel), string(AppointmentSchedulingLabel):
				err = runPickupApptConfirmation(ctx, email, WithService(options.Service))

			case string(CarrierQuoteResponseLabel):
				err = runCarrierQuoteResponseExtract(ctx, &email, WithService(options.Service))

			case string(CarrierInfoLabel), string(DriverInfoLabel):
				err = runCarrierInfoExtract(
					ctx,
					email,
					WithService(options.Service),
					WithEmailReprocess(options.EmailReprocess),
				)

			case string(CheckCallLabel):
				err = runCheckCallExtract(ctx, email, WithService(options.Service))

			case string(TruckListLabel):
				err = runTruckListExtract(ctx, email, WithService(options.Service))

			case string(QuoteRequestLabel):
				if isQuoteRequestSuggestionEnabled {
					err = runQuoteRequestExtract(
						ctx,
						email,
						WithService(options.Service),
						WithEmailReprocess(options.EmailReprocess),
					)
				} else {
					log.WarnNoSentry(
						ctx,
						"email labelled as quote request but quote request suggestion feature is disabled",
					)
				}

			case string(BatchQuoteRequestLabel):
				if isQuoteRequestSuggestionEnabled {
					err = runBatchQuoteRequestExtract(
						ctx,
						email,
						WithService(options.Service),
						WithEmailReprocess(options.EmailReprocess),
					)
				} else {
					log.WarnNoSentry(
						ctx,
						"email labelled as batch quote request but quote request suggestion feature is disabled",
					)
				}

			case string(LoadBuildingLabel):
				if isLoadBuildingEnabled {
					err = runNewLoadExtract(
						ctx,
						email,
						WithService(options.Service),
						WithEmailReprocess(options.EmailReprocess),
					)
				} else {
					log.WarnNoSentry(ctx, "email labelled as load building but load building feature is disabled")
				}

			default:
				createEmailDraft(email)
			}

			if err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("%s action failed: %w", label, err))
				mu.Unlock()
			}
		}(label)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// Log all collected errors
	for _, err := range errors {
		log.Warn(ctx, "pipeline error", zap.Error(err), zap.Uint("emailID", email.ID))
	}

}

func createEmailDraft(_ models.Email) {}

func runPickupApptConfirmation(ctx context.Context, email models.Email, opts ...Option) error {
	options := &Options{
		Service: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.Service.IsAppointmentSuggestionsEnabled {
		log.Debug(ctx, "appointment suggestions not enabled, skipping sending to OpenAI")

		return nil
	}

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	suggestions, err := llm.ExtractApptConfirmationSuggestions(ctx, email, openaiService, llm.StandardRDS{})
	if err != nil {
		return fmt.Errorf("llm.ExtractPickupApptConfirmation error: %w", err)
	}

	for _, suggestion := range suggestions {

		err := suggestionDB.Create(ctx, &suggestion)
		if err != nil {
			// Fail open; Gorm will send to Sentry
			log.ErrorNoSentry(ctx, "suggestionDB.Create error: %w", zap.Error(err))
		}
	}

	return nil
}

func runCarrierInfoExtract(ctx context.Context, email models.Email, opts ...Option) error {
	options := &Options{
		Service:        nil,
		EmailReprocess: false,
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.Service.IsCarrierInfoSuggestionsEnabled {
		log.Debug(ctx, "carrier info suggestions not enabled, skipping sending to OpenAI")

		return nil
	}

	if !options.EmailReprocess {
		// Determine if we've already processed this email's carrier info, to avoid sending duplicates to OpenAI
		existingSuggestions, err := suggestionDB.
			GetSuggestionsByThreadIDAndCategory(
				ctx,
				email.ThreadID,
				string(models.CarrierInfo),
			)

		if err != nil {
			return fmt.Errorf("GetSuggestionsByThreadIDAndCategory error: %w", err)
		}

		// if we already have existing carrier info suggestions for this email thread, skip sending to OpenAI
		if len(existingSuggestions) != 0 {
			log.Info(
				ctx,
				"already have suggested carrier changes for this email thread, skipping sending to OpenAI",
				zap.String("threadID", email.ThreadID),
			)

			return nil
		}
	}

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	newSuggestions, err := llm.ExtractCarrierInfoSuggestion(ctx, email, openaiService, llm.StandardRDS{})
	if err != nil {
		return fmt.Errorf("llm.ExtractCarrierInfo error: %w", err)
	}

	// Technically we'll only get 1 suggestion here, but returns a list for easy checking of no suggestion
	for _, suggestion := range newSuggestions {

		err := suggestionDB.Create(ctx, &suggestion)
		if err != nil {
			// Fail open; Gorm will send to Sentry
			log.ErrorNoSentry(ctx, "suggestionDB.Create error: %w", zap.Error(err))
		}
	}

	return nil
}

var (
	dbFuncGetQuoteRequestByEmailID             = quoteRequestDB.GetRequestByEmailID
	dbFuncCreateQuoteRequest                   = quoteRequestDB.CreateQuoteRequest
	llmFuncExtractQuoteRequestSuggestions      = llm.ExtractQuoteRequestSuggestions
	llmFuncExtractBatchQuoteRequestSuggestions = llm.ExtractBatchQuoteRequestSuggestions
	dbFuncGetTMSListByServiceID                = integrationDB.GetTMSListByServiceID
	dbFuncCreateBatchQuote                     = batchQuoteDB.CreateBatchQuote
	dbFuncAssociateQuoteRequestsToBatch        = batchQuoteDB.AssociateQuoteRequestsToBatch
	dbFuncUpdateBatchQuote                     = batchQuoteDB.UpdateBatchQuote
	openaiServiceCreator                       = openai.NewService
)

func runQuoteRequestExtract(ctx context.Context, email models.Email, opts ...Option) error {
	options := parseOptions(opts...)

	if !options.Service.IsCarrierNetworkQuotingEnabled && !options.Service.IsQuickQuoteEnabled {
		log.Debug(ctx, "neither carrier network quoting or quick quote is enabled, skipping sending to OpenAI")
		return nil
	}

	// We allow multiple quote requests per thread but we don't want to re-process emails
	if shouldSkipQuoteExtraction(ctx, email, options) {
		return nil
	}

	// Get third-party quote submission URLs
	quoteURLs := parseThirdPartyQuoteURLs(email, options)

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}
	textractClient := textract.GetTextractClient(ctx)

	// get service's quick quote config
	config, err := getQuoteConfig(ctx, options.Service.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no quote config found for service")
		} else {
			log.Warn(ctx, "error getting quoting config, failing open", zap.Error(err))
		}
	}

	var tmsParam models.Integration
	tmses, err := dbFuncGetTMSListByServiceID(ctx, options.Service.ID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting TMS list", zap.Error(err))
	}
	if len(tmses) > 0 {
		tmsParam = tmses[0]
	}

	results, err := llmFuncExtractQuoteRequestSuggestions(
		ctx,
		email,
		openaiService,
		textractClient,
		llm.StandardRDS{},
		llm.WithConfig(config),
	)

	if err != nil {
		return fmt.Errorf("quote request extraction error: %w", err)
	}

	if len(results) == 0 {
		log.Info(ctx, "no quote request data found in email")
		// email.Labels = strings.ReplaceAll(email.Labels, string(QuoteRequestLabel), "")
		// if err := emailDB.Update(ctx, &email); err != nil {
		// 	log.WarnNoSentry(ctx, "error removing quote request label from email", zap.Error(err))
		// }
		return nil
	} else if len(results) > 1 {
		// This log is useful and can mean multiple things:
		// 1. Email IMPROPERLY labeled as "quote request" when there are actually multiple requests for quotes, it
		//    should be labeled "batch quote request"
		//    - resolved by updating quote labelling prompt (IsBatchQuote prompt)
		// 2. Email is PROPERLY labeled as "quote request" and improperly extracts duplicate suggestions. We
		//    interpret/expect to only be 1 suggestion
		//    - resolved by updating quote request prompt (singular qr extraction)
		// 2a. Email is PROPERLY labeled as "quote request" Rather than duplicate suggestions it is a improper
		//     parsing of a multi-stop quote request
		//    - resolved by updating quote request prompt (multi-stop qr extraction)
		log.Info(
			ctx,
			"multiple quote requests extracted from email labelled as quote request (singular)",
			zap.Int("count", len(results)),
		)
	}

	dedupedResults := deduplicateSuggestions(ctx, results)
	if len(dedupedResults) != len(results) {
		log.Info(
			ctx,
			"deduplicated quote request suggestions",
			zap.Int("originalCount", len(results)),
			zap.Int("dedupedCount", len(dedupedResults)),
		)
	}

	// Save quote requests and vector embeddings to RDS
	// - can potentially create batch quote if extraction results in >1 quote request extracted
	if err := createAndSaveQuoteRequests(ctx, email, dedupedResults, quoteURLs, tmsParam); err != nil {
		return fmt.Errorf("error creating quote requests and batch: %w", err)
	}

	return nil
}

func runBatchQuoteRequestExtract(ctx context.Context, email models.Email, opts ...Option) error {
	options := parseOptions(opts...)

	if !options.Service.IsCarrierNetworkQuotingEnabled && !options.Service.IsQuickQuoteEnabled {
		log.Debug(ctx, "neither carrier network quoting or quick quote is enabled, skipping sending to OpenAI")
		return nil
	}

	// We allow multiple quote requests per thread but we don't want to re-process emails
	if shouldSkipQuoteExtraction(ctx, email, options) {
		return nil
	}

	// Get third-party quote submission URLs
	quoteURLs := parseThirdPartyQuoteURLs(email, options)

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}
	textractClient := textract.GetTextractClient(ctx)

	// get service's quick quote config
	config, err := getQuoteConfig(ctx, options.Service.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no quote config found for service")
		} else {
			log.Warn(ctx, "error getting quoting config, failing open", zap.Error(err))
		}
	}

	var tmsParam models.Integration
	tmses, err := dbFuncGetTMSListByServiceID(ctx, options.Service.ID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting TMS list", zap.Error(err))
	}
	if len(tmses) > 0 {
		tmsParam = tmses[0]
	}

	results, err := llmFuncExtractBatchQuoteRequestSuggestions(
		ctx,
		email,
		openaiService,
		textractClient,
		llm.StandardRDS{},
		llm.WithConfig(config),
	)

	if err != nil {
		return fmt.Errorf("batch quote request extraction error: %w", err)
	}

	if len(results) == 0 {
		log.Info(ctx, "no quote request data found in email")
		// email.Labels = strings.ReplaceAll(email.Labels, string(QuoteRequestLabel), "")
		// if err := emailDB.Update(ctx, &email); err != nil {
		// 	log.WarnNoSentry(ctx, "error removing quote request label from email", zap.Error(err))
		// }
		return nil
	} else if len(results) == 1 {
		log.Info(
			ctx,
			"single quote request extracted from email labeled as batch quote request",
		)
	}

	dedupedResults := deduplicateSuggestions(ctx, results)
	if len(dedupedResults) != len(results) {
		log.Info(ctx, "deduplicated quote request suggestions", zap.Int("originalCount", len(results)),
			zap.Int("dedupedCount", len(dedupedResults)))
	}

	// Save quote requests, batch quote, and vector embeddings to RDS
	if err := createAndSaveQuoteRequests(ctx, email, dedupedResults, quoteURLs, tmsParam); err != nil {
		return fmt.Errorf("error creating quote requests and batch: %w", err)
	}

	return nil
}

// --- Helper functions for runQuoteRequestExtract and runBatchQuoteRequestExtract --- //
func parseOptions(opts ...Option) *Options {
	options := &Options{
		Service:        nil,
		EmailReprocess: false,
	}

	for _, opt := range opts {
		opt(options)
	}

	return options
}

// We allow multiple quote requests per thread but we don't want to re-process emails
func shouldSkipQuoteExtraction(ctx context.Context, email models.Email, options *Options) bool {
	if !options.EmailReprocess {
		dbQuoteReq, err := dbFuncGetQuoteRequestByEmailID(ctx, email.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			// Fail-open if transient DB error; occasional duplicate processing is fine
			log.WarnNoSentry(ctx, "error getting quote request from DB", zap.Error(err))
		} else if len(dbQuoteReq) > 0 && env.Vars.SkipDuplicateSuggestions {
			log.Info(
				ctx,
				"quote requests already exist for email, skipping LLM extraction",
				zap.Uint("first quoteRequestID from email", dbQuoteReq[0].ID),
			)

			return true
		}
	}

	return false
}

var (
	dbFuncGetEmailAndQuoteRequestByThreadID = genEmailDB.GetEmailAndQuoteRequestByThreadID
)

func runCarrierQuoteResponseExtract(ctx context.Context, email *models.Email, opts ...Option) error {
	options := &Options{
		Service: &models.Service{},
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.Service.IsCarrierNetworkQuotingEnabled {
		log.Debug(ctx, "carrier network quoting not enabled, skipping sending to OpenAI")

		return nil
	}

	carrierEmail, err := dbFuncGetEmailAndQuoteRequestByThreadID(ctx, email.ThreadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "email not associated with carrier network flow, skipping openAI")

			return nil
		}

		return fmt.Errorf("error getting associated quote request thread from DB: %w", err)
	}

	// Based on DB query requiring Drumkit-generated emails, this should always be == 1 by this point
	if len(carrierEmail.QuoteRequests) == 0 {
		log.Info(ctx, "no quote requests associated with email, skipping openAI")

		return nil
	}

	quoteReq := carrierEmail.QuoteRequests[0]

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	quote, err := llm.ExtractCarrierQuoteResponse(ctx, *email, openaiService)
	if err != nil {
		return fmt.Errorf("OpenAI error: %w", err)
	}
	quote.QuoteRequestID = quoteReq.ID

	err = carrier_quoteDB.Update(ctx, quoteReq.ID, &quote)
	if err != nil {
		return fmt.Errorf("error upserting quote: %w", err)
	}

	// Get TMS integrations for the service
	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, options.Service.ID)
	if err != nil {
		log.Warn(ctx, "error getting TMS integrations", zap.Error(err))
		return nil
	}

	// Check if any of the TMS integrations is Turvo
	for _, tmsIntegration := range tmsIntegrations {
		if tmsIntegration.Name == models.Turvo {
			// Create Turvo client
			turvoClient := turvo.New(ctx, tmsIntegration)
			if turvoClient == nil {
				log.Error(ctx, "error creating Turvo client")
				continue
			}

			// Submit quote to Turvo
			err = turvoClient.CreateOffer(ctx, quote)
			if err != nil {
				log.Error(ctx, "error submitting quote to Turvo", zap.Error(err))
				continue
			}

			log.Info(ctx, "successfully submitted quote to Turvo")
			break
		}
	}

	return nil
}

func runCheckCallExtract(ctx context.Context, email models.Email, opts ...Option) error {
	options := &Options{
		Service: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.Service.IsCheckCallSuggestionsEnabled {
		log.Debug(ctx, "check call suggestions not enabled, skipping sending to OpenAI")

		return nil
	}

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	suggestions, err := llm.ExtractCheckCallSuggestions(ctx, email, openaiService, llm.StandardRDS{})
	if err != nil {
		return fmt.Errorf("llm.ExtractCheckCallSuggestions error: %w", err)
	}

	for _, suggestion := range suggestions {

		err := suggestionDB.Create(ctx, &suggestion)
		if err != nil {
			// Fail open; Gorm will send to Sentry
			log.ErrorNoSentry(ctx, "suggestionDB.Create error: %w", zap.Error(err))
		}
	}

	return nil
}

func runTruckListExtract(ctx context.Context, email models.Email, opts ...Option) error {
	options := &Options{
		Service: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.Service.IsTruckListEnabled {
		log.Info(ctx, "truck list parsing not enabled, skipping sending to OpenAI")

		return nil
	}

	openaiService, err := openaiServiceCreator(ctx)
	if err != nil {
		return fmt.Errorf("error initializing OpenAI service: %w", err)
	}

	trucklist, err := llm.ExtractTruckListSuggestions(ctx, email, openaiService)
	if err != nil {
		return fmt.Errorf("llm.ExtractTruckList error: %w", err)
	}

	if len(trucklist.Trucks) > 0 {
		err = truckDB.CreateTruckList(ctx, &trucklist)
		if err != nil {
			err = fmt.Errorf("truckDB.CreateTruckList error: %w", err)
			return err
		}

		// Get TMS integrations for the service
		tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, options.Service.ID)
		if err != nil {
			log.Warn(ctx, "error getting TMS integrations", zap.Error(err))
			return nil
		}

		// Submit to each TMS integration
		for _, integration := range tmsIntegrations {
			switch integration.Name {
			case models.Turvo:
				turvoClient := turvo.New(ctx, integration)
				if turvoClient == nil {
					log.Error(ctx, "error creating Turvo client")
					continue
				}

				_, err = turvoClient.CreateRoutingGuide(ctx, &trucklist)
				if err != nil {
					log.Error(ctx, "error submitting truck list to Turvo", zap.Error(err))
					continue
				}
				log.Info(ctx, "successfully submitted truck list to Turvo")

			case models.Redwood:
				if strings.ToLower(options.Service.Name) == "redwood" {
					client, err := redwood.New(ctx, integration)
					if err != nil {
						log.Error(ctx, "error creating Redwood client", zap.Error(err))
						continue
					}

					_, err = client.SubmitTruckList(ctx, true, trucklist)
					if err != nil {
						log.Error(ctx, "error submitting truck list to Redwood", zap.Error(err))
						continue
					}
					log.Info(ctx, "successfully submitted truck list to Redwood")
				}
			}
		}
	}

	return nil
}

var (
	dbFuncGetLoadBuildingSuggestionsByEmail = suggestionDB.GetLoadBuildingSuggestionsByEmail
	llmFuncExtractNewLoadSuggestions        = llm.ExtractNewLoadSuggestions
	dbFuncCreateLoadSuggestion              = suggestionDB.Create
	dbFuncGetQuickQuoteConfigByServiceID    = quoteRequestDB.GetConfigByServiceID
)

// runNewLoadExtract parses available load info from the email body/PDFs (customer, pickup, dropoff, etc)
// and generates a quote request & load building suggestion for each potential load.
// Generating linked suggestions accomplishes a few things:
// 1. We capture when a quote is won and converted to a load
// 2. We capture customer for both quotes and new loads
// 3. The user sees just 1 "New Load" suggestion in sidebar instead of 2, which eliminates redundancy and confusion.
// 4. Metrics can still differentiate between quotes and load building suggestions.
// View "Consolidating Load Building & Quick Quote Suggestions ADR" on Notion for more details.
//
// NOTE: ExtractNewLoadSuggestions handles:
// 1) emails with 1+ PDF(s), and 2) emails with no PDFs and just 1 quote request/load in the body.
// Emails with no PDFs & multiple quote requests in the email body are handled by runQuoteRequestExtract().
func runNewLoadExtract(ctx context.Context, email models.Email, opts ...Option) (err error) {
	options := &Options{
		Service:        &models.Service{},
		EmailReprocess: false,
	}

	for _, opt := range opts {
		opt(options)
	}

	if !options.EmailReprocess {
		// Check if load suggestions already in the DB to reduce false positives
		loadBuildingSuggestions, err := dbFuncGetLoadBuildingSuggestionsByEmail(ctx, email.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			// Fail-open if transient DB error; occasional duplicate processing is fine
			log.WarnNoSentry(ctx, "error getting load suggestion from DB", zap.Error(err))
		} else if loadBuildingSuggestions.ID != 0 && env.Vars.SkipDuplicateSuggestions {
			log.Info(ctx, "loadBuildingSuggestions already exist for email, skipping LLM extraction",
				zap.Uint("suggestionID", loadBuildingSuggestions.ID))

			return nil
		}
	}

	// Get excluded special equipment and other config from service's Quick Quote config
	config, err := getQuoteConfig(ctx, options.Service.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Info(ctx, "no quote config found for service")
		} else {
			log.Warn(ctx, "error getting quoting config, failing open", zap.Error(err))
		}
	}

	var tmsParam models.Integration
	tmses, err := integrationDB.GetTMSListByServiceID(ctx, options.Service.ID)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting TMS list", zap.Error(err))
	}
	if len(tmses) > 0 {
		tmsParam = tmses[0]
	}

	var results []models.SuggestedLoadChange
	results, err = llmFuncExtractNewLoadSuggestions(
		ctx,
		email,
		tmsParam,
		extractor.WithConfig(config),
		extractor.WithService(*options.Service),
	)
	if err != nil {
		return fmt.Errorf("llm.ExtractNewLoadSuggestions error: %w", err)
	}

	if len(results) == 0 {
		log.Info(ctx, "no loads/quote requests found in email")

		// email.Labels = strings.ReplaceAll(email.Labels, string(LoadBuildingLabel), "")
		// email.Labels = strings.ReplaceAll(email.Labels, string(QuoteRequestLabel), "")

		// if err := emailDB.Update(ctx, &email); err != nil {
		// 	log.WarnNoSentry(ctx, "error removing load building/quote request labels from email", zap.Error(err))
		// }

		return nil
	}

	log.Infof(ctx, "new load len(results): %d", len(results))

	// Associate vector embeddings with created load building suggestions
	vectorRepo := rds.GetVectorRepository(ctx)

	for i := range results {
		err = dbFuncCreateLoadSuggestion(ctx, &results[i])
		if err != nil {
			log.WarnNoSentry(ctx, "error creating load building suggestion", zap.Error(err), zap.Int("index", i))
			continue
		}

		if vectorRepo != nil {
			// Update associations and get the model with vectors preloaded to avoid read replica race conditions
			sugWithVectors, err := vectorRepo.UpdateLoadBuildingAssociations(ctx, email, results, i)
			if err != nil {
				log.WarnNoSentry(ctx, "error updating vector associations and preloading vectors", zap.Error(err))
			} else {
				enhancer := vector.NewSuggestionEnhancer(vectorRepo)
				err = enhancer.Enhance(ctx, tmsParam.ID, sugWithVectors)
				if err != nil {
					log.WarnNoSentry(ctx, "error enhancing load building suggestion", zap.Error(err))
				}
			}
		}

	}

	return nil
}

// parseThirdPartyQuoteURLs parses the URLs where the broker (our user) may submit their quote from the email body
// 12/02/2024: Currently, we only support one quote URL object per email, regardless of the number of quote requests
func parseThirdPartyQuoteURLs(email models.Email, options *Options) models.ThirdPartyQuoteURLs {
	var quoteURLs models.ThirdPartyQuoteURLs
	if email.BodyType == models.MarkdownEmailBodyType && options.Service.IsQuoteSubmissionViaURLEnabled {
		regex := regexp.MustCompile(`\[[^\]]*?\]\((.*?)\)`)

		match := regex.FindStringSubmatch(email.Body)

		if len(match) > 1 {
			// If the regex succeeds, we try to match the url (match[1]) to one of our allowed 3rd party quote
			// submission urls. Most of these emails contain a submit and a decline link, so we only check for
			// the submit link (hence the "Decline" check).
			if strings.Contains(match[1], "petrochoice.mercurygate") && !strings.Contains(match[1], "Decline") {
				quoteURLs.FormURL = match[1]
				quoteURLs.SubmissionURL = petroChoice
			}
			// Future: add support for other services and their respective customer URLs
		}
	}

	return quoteURLs
}

// createAndSaveQuoteRequests saves quote requests to the database and creates a batch quote if needed (w/associations)
func createAndSaveQuoteRequests(
	ctx context.Context,
	email models.Email,
	quoteRequests []models.QuoteRequest,
	quoteURLs models.ThirdPartyQuoteURLs,
	tmsParam models.Integration,
) error {

	var batchQuote *models.BatchQuote

	// Create batch quote if we have multiple quote requests
	if len(quoteRequests) > 1 {
		var braintrustLogIDs models.LogRecordList
		var braintrustSpanID string
		if len(quoteRequests) > 0 {
			braintrustLogIDs = quoteRequests[0].BraintrustLogIDs
			braintrustSpanID = quoteRequests[0].BraintrustSpanID
		}

		batchQuote = &models.BatchQuote{
			UserID:           email.UserID,
			ServiceID:        email.ServiceID,
			EmailID:          email.ID,
			ThreadID:         email.ThreadID,
			SourceCategory:   models.EmailSourceCategory,
			SourceExternalID: email.ExternalID,
			Status:           models.BatchQuoteStatusProcessed,
			BraintrustLogIDs: braintrustLogIDs,
			BraintrustSpanID: braintrustSpanID,
		}

		if err := dbFuncCreateBatchQuote(ctx, batchQuote); err != nil {
			return fmt.Errorf("failed to create batch quote: %w", err)
		}

		for i := range quoteRequests {
			quoteRequests[i].BatchQuoteID = &batchQuote.ID
		}

		log.Info(
			ctx,
			"created batch quote for multiple quote requests",
			zap.Uint("batchQuoteID", batchQuote.ID),
			zap.Int("quoteRequestCount", len(quoteRequests)),
		)
	}

	// Get vector repository for updating embedding associations
	vectorRepo := rds.GetVectorRepository(ctx)

	// Save all quote requests
	for i := range quoteRequests {
		quoteRequests[i].ThirdPartyQuoteURLs = quoteURLs

		if err := dbFuncCreateQuoteRequest(ctx, &quoteRequests[i]); err != nil {
			return fmt.Errorf("error creating quote request: %w", err)
		}

		if vectorRepo != nil {
			// Update associations and get the model with vectors preloaded to avoid read replica race conditions
			sugWithVectors, err := vectorRepo.UpdateQuoteRequestAssociations(ctx, email, quoteRequests, i)
			if err != nil {
				log.WarnNoSentry(ctx, "error updating vector associations and preloading vectors", zap.Error(err))
			} else {
				enhancer := vector.NewSuggestionEnhancer(vectorRepo)
				err = enhancer.Enhance(ctx, tmsParam.ID, sugWithVectors)
				if err != nil {
					log.WarnNoSentry(ctx, "error enhancing quote request suggestion", zap.Error(err))
				}
			}
		}
	}

	// If we created a batch quote, create many-to-many associations and update summary
	if batchQuote != nil {
		// Collect all quote request IDs for batch association
		quoteRequestIDs := make([]uint, len(quoteRequests))
		for i, qr := range quoteRequests {
			quoteRequestIDs[i] = qr.ID
		}

		// Create many-to-many associations in a single batch operation
		if err := dbFuncAssociateQuoteRequestsToBatch(ctx, batchQuote.ID, quoteRequestIDs); err != nil {
			log.Warn(
				ctx,
				"failed to batch associate quote requests with batch",
				zap.Error(err),
				zap.Uint("batchQuoteID", batchQuote.ID),
				zap.Int("quoteRequestCount", len(quoteRequestIDs)))
		}

		batchQuote.QuoteRequests = quoteRequests
		batchQuote.UpdateSummaryInfo()
		if err := dbFuncUpdateBatchQuote(ctx, batchQuote); err != nil {
			log.Warn(ctx, "failed to update batch quote summary", zap.Error(err))
		}
	}

	return nil
}

// Deduplicate quote request suggestions. Helpful for if user is sent multiple vans/orders in one email
func deduplicateSuggestions(ctx context.Context, results []models.QuoteRequest) []models.QuoteRequest {
	normalizeTimestamps := func(qr *models.QuoteRequest) {

		if qr.SuggestedRequest.PickupDate.Valid {
			qr.SuggestedRequest.PickupDate.Time = qr.SuggestedRequest.PickupDate.Time.UTC()
		}
		if qr.SuggestedRequest.DeliveryDate.Valid {
			qr.SuggestedRequest.DeliveryDate.Time = qr.SuggestedRequest.DeliveryDate.Time.UTC()
		}
	}

	// We must serialize to JSON because structs with nested slices, maps, and functions are not Comparable
	// and thus cannot be used as map keys.
	set := make(map[string]models.QuoteRequest, len(results))
	for i := range results {
		// JSON marshaling preserves original timezone (see test) so normalize all timestamps to UTC
		// to avoid false negatives. (Timestamps are already parsed in their correct timezones so this is ok to do)
		normalizeTimestamps(&results[i])

		jsonBytes, err := json.Marshal(results[i])
		if err != nil {
			// Fail-open; occasional duplicate suggestions is fine
			log.WarnNoSentry(ctx, "error marshaling suggested load change", zap.Error(err))
			continue
		}
		set[string(jsonBytes)] = results[i]
	}

	dedupedResults := make([]models.QuoteRequest, 0, len(set))
	for _, result := range set {
		dedupedResults = append(dedupedResults, result)
	}

	return dedupedResults
}

func getQuoteConfig(ctx context.Context, serviceID uint) (models.QuickQuoteConfig, error) {
	config, err := dbFuncGetQuickQuoteConfigByServiceID(ctx, serviceID)

	return config, err
}
