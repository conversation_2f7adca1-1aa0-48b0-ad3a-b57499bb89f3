package emails

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

//
// Quote Test Cases
//

func TestQuoteRequestLabeling(t *testing.T) {

	opts := &Options{
		Service: &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuickQuoteEnabled:            true,
				IsCarrierNetworkQuotingEnabled: true,
				IsLoadBuildingEnabled:          true,
			},
		},
	}

	t.Run("Carrier info email", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",

			Subject: "Testing Email Labels",
			Body:    "Hey,\nthe truck is 123 \n the driver: Rylie \n the trailer: 2224 \nPickup time is now 10/7/23 at 10 am. ID is 31450-77153. And another one is 31433-54712. \nDelivery time is 10/8/23 at 3 pm", //nolint

			S3URL: "s3://bucket/key.json",
		}

		expected := []string{"carrier info", "load building", "truck list", "quote request", "carrier quote response"}
		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expected, result)
	})

	t.Run("OK 1", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "New shipment",
			// axle@wickerparklogistis account, thread https://shorturl.at/ejW27
			Body: `Can you please provide us with a rate for the following\r\n\r\n\r\n\r\n
			From Amherst NY 14228 To ASHLAND VA 23005\r\n\r\n1 pallet: 180 x 60 x 90 - 8,200 lbs.
			\r\n\r\n1 pallet: 84 x 60 x 72 - 1,500 lbs.\r\n\r\n5 pallets: 48 x 48 x 48 - 380 lbs.
			\r\n\r\n\r\n\r\n*  FIREPUMPS *  The pick up date requested 3/18/24\r\n\r\n\r\n\r\n
			Thank you very much,\r\nMarie\r\n\r\n\r\n`,
		}

		expectedLabels := []string{"appointment scheduling", "quote request", "load building", "carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	t.Run("OK 2", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Shipment Request",
			Body: `Hey,\r\nI've got an 850-lb flatbed shipment going from 02116 on 3/18 to 73301 on 3/20.
			How much would that cost?\r\n\r\n\r\nBest,\r\nSophie\r\nFast Shipper Inc\r\n\r\n`}

		expectedLabels := []string{"quote request", "load building", "truck list", "carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	t.Run("OK 3", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Shipment PL124561",
			//nolint:lll
			Body: `Hope all is well with you both.\r\n\r\nWe have 2 crates we are trying to ship out of our warehouse to our customer, please see below.
			This shipment is for Canada.  Pick-up would be Friday (3-15-24).\r\n\r\n\r\n\r\nShip to:\r\n\r\nSome Company Solutions,
			\r\n\r\n50 Washington Blvd,\r\n\r\nBrantford, ON (Canada)\r\n\r\n\r\n\r\nPick up address:\r\n\r\nSome Corporation\r\n
			101 Elm Ave\r\nSuite B\r\nRoselle, IL  60172\r\n\r\nPick-up hours 1:00 to 4:00 PM\r\n\r\n\r\n1st Crate\r\n\r\n4290 LBS.
			\r\n\r\n68\"x68\"x30\"\r\n\r\n\r\n2nd Crate\r\n\r\n4290 LBS.\r\n\r\n68\"x68\"x30\"\r\n\r\n\r\nTotal weight 8,580 lbs.
			\r\n\r\n\r\nClass 50\r\n\r\nDeclared Value is $8,000.00 each\r\n\r\nTotal declared value is $16,000.00\r\n\r\n\r\n
			Would you be able to send us quotes to ship this.Also, we would need a BOL from the truck line sent to us.\r\n
			Thank you so much.
			\r\n\r\nThank you so much.\r\n\r\n`,
		}

		expectedLabels := []string{
			"carrier quote response", "appointment scheduling",
			"quote request", "load building", "truck list",
		}

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	t.Run("No question", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "HOTSHOT & FUEL SURCHARGE",
			Body: `Quote - 48'/53' FLATBED - MUST BE 102\" WIDE - 8 CHAINS.
			HRS: 8:00am-6:00pm ET. Deliver hours M-F 7:30-2:30pm CT sharp.\n\n
			12 REELS CONDUIT  @ 96"X 48"X 96" EACH\n\n35,000 LBS TOTAL\n\n
			PU REF\n\nPICK UP LOCATION:SANDERSVILLE, GA 31082\n\n478-412-9733 Donnie Robertson\n\n
			DELIVERY LOCATION:\n\nALABASTER, AL 35007\n\n`,
		}

		expectedLabels := []string{
			"quote request", "appointment scheduling",
			"load building", "truck list", "carrier quote response",
		}
		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	t.Run("No question, just locations and transport type", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Spartanburg, SC 29307",
			Body: `HOT SHOT & FUEL SURCHARGE

			**FULLY TARPED W/ 8' LUMBER TARPS**

			**FROM**

			Denver, NC  28037

			**TO**

			Spartanburg, SC 29307

			Thank You,`,
		}
		expectedLabels := []string{"quote request", "carrier quote response"}
		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})
}

func TestCarrierQuoteResponseLabeling(t *testing.T) {
	// Feature flag must be enabled for label to be added
	opts := &Options{
		Service: &models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuickQuoteEnabled:            true,
				IsCarrierNetworkQuotingEnabled: true,
				IsLoadBuildingEnabled:          true,
			},
		},
	}

	t.Run("Dollar sign before number", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nHey we can do this for $3100 + FSC\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Dollar sign after number", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nHey we can do this for 3100 $ + FSC\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Quote and quote request", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nHey we could do this for 3100 $ + FSC\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"} // "could" causes "quote request label"

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	t.Run("USD before", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nHey we can do this for USD   3100 + FSC\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("USD after", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nHey we can do this for 3100      USD`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Dollars plural", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey,\r\nWe can do this for 3100 dollars\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Dollar singular", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey,\r\nWe can do this for 3100 dollar\r\n`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("No currency marker", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Budget estimate",
			Body:    `We can do it for 3200.`,
		}

		expectedLabels := []string{"carrier quote response", "quote request"} // "quote request" label is later removed

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	// Constraint to values >100 is an optimization to avoid false positive on dates and other numbers
	t.Run("Below 100 should not match", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Discounted price",
			Body:    `This special offer is just $99.99!`,
		}

		expectedLabels := []string(nil)

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Thousands separator", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote breakdown",
			Body:    `The final amount is 10,500 dollars.`,
		}

		expectedLabels := []string{"carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Decimal value", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote for Boston to Austin",
			Body:    `Hey\r\nIt'd be USD 840.50\r\n`,
		}

		expectedLabels := []string{"carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Thousands separator and decimal value", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Quote breakdown",
			Body:    `The final amount is 10,500.50 dollars.`,
		}

		expectedLabels := []string{"carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Multiple quotes in body", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Pricing details",
			Body:    `Option 1: USD 1,250.00\r\nOption 2: 1,500 dollars`,
		}

		expectedLabels := []string{"carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("OK 2", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Shipment Request",
			Body: `Hey,\r\nI've got an 850-lb flatbed shipment going from 02116 on 3/18 to 73301 on 3/20.
			How much would that cost?\r\n\r\n\r\nBest,\r\nSophie\r\nFast Shipper Inc\r\n\r\n`}

		// categorizeByRegex() removes "carrier quote response" label from quote requests that are not explicitly
		// part of a carrier quote thread
		expectedLabels := []string{"quote request", "load building", "truck list", "carrier quote response"}

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

	// False positive but processor/categorizeByRegex() has a feedback mechanism to remove "carrier quote response"
	// label from quote requests that are not explicitly part of a carrier quote thread
	t.Run("False positive", func(t *testing.T) {
		email := models.IngestedEmail{
			Account: "<EMAIL>",
			Subject: "Shipment PL124561",
			//nolint:lll
			Body: `Hope all is well with you both.\r\n\r\nWe have 2 crates we are trying to ship out of our warehouse to our customer, please see below.
			This shipment is for Canada.  Pick-up would be Friday (3-15-24).\r\n\r\n\r\n\r\nShip to:\r\n\r\nSome Company Solutions,
			\r\n\r\n50 Washington Blvd,\r\n\r\nBrantford, ON (Canada)\r\n\r\n\r\n\r\nPick up address:\r\n\r\nSome Corporation\r\n
			101 Elm Ave\r\nSuite B\r\nRoselle, IL  60172\r\n\r\nPick-up hours 1:00 to 4:00 PM\r\n\r\n\r\n1st Crate\r\n\r\n4290 LBS.
			\r\n\r\n68\"x68\"x30\"\r\n\r\n\r\n2nd Crate\r\n\r\n4290 LBS.\r\n\r\n68\"x68\"x30\"\r\n\r\n\r\nTotal weight 8,580 lbs.
			\r\n\r\n\r\nClass 50\r\n\r\nDeclared Value is $8,000.00 each\r\n\r\nTotal declared value is $16,000.00\r\n\r\n\r\n
			Would you be able to send us quotes to ship this.Also, we would need a BOL from the truck line sent to us.\r\n
			Thank you so much.
			\r\n\r\nThank you so much.\r\n\r\n`,
		}

		expectedLabels := []string{
			"carrier quote response", "appointment scheduling",
			"quote request", "load building", "truck list",
		}

		result := labelEmail(context.Background(), email, opts)
		assert.ElementsMatch(t, expectedLabels, result)
	})

}

//
// Carrier Test Cases
//

var opts = &Options{
	Service: &models.Service{
		FeatureFlags: models.FeatureFlags{
			IsLoadBuildingEnabled:          true,
			IsQuickQuoteEnabled:            true,
			IsCarrierNetworkQuotingEnabled: true,
		},
	},
}

func TestCarrierLabel(t *testing.T) {
	now := time.Now()

	emailInput := &models.IngestedEmail{
		Account:   "<EMAIL>",
		UserID:    1,
		ServiceID: 1,

		RFCMessageID: "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>",
		ExternalID:   "msg-f:1775132995676831833",
		ThreadID:     "thread-f:1775120948217100423",
		SentAt:       time.UnixMilli(now.UnixMilli()),

		ThreadReferences: "<drumkitai/drumkit/pull/<EMAIL>>",
		InReplyTo:        "<drumkitai/parthenon/pull/<EMAIL>>",

		Sender:     "<EMAIL>",
		Recipients: "<EMAIL>",
		CC:         "<EMAIL>",

		Subject: "Testing Email Labels",
		Body:    "Hey,\nDriver name: Danielle. truck Number: 12345. trailer Number: ********. \nPickup time is now 10/7/23 at 10 am. ID is 31450-77153. And another one is 31433-54712. \nDelivery time is 10/8/23 at 3 pm", //nolint

		S3URL: "s3://bucket/key.json",
	}

	expectedLabels := []string{
		"carrier info", "driver info", "load building",
		"truck list", "quote request", "carrier quote response",
	}

	result := labelEmail(context.Background(), *emailInput, opts)

	assert.ElementsMatch(t, expectedLabels, result)

}

func TestCarrierLabelArbitraryOrder(t *testing.T) {
	now := time.Now()
	emailInput := &models.IngestedEmail{
		Account:   "<EMAIL>",
		UserID:    1,
		ServiceID: 1,

		RFCMessageID: "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>",
		ExternalID:   "msg-f:1775132995676831833",
		ThreadID:     "thread-f:1775120948217100423",
		SentAt:       time.UnixMilli(now.UnixMilli()),

		ThreadReferences: "<drumkitai/drumkit/pull/<EMAIL>>",
		InReplyTo:        "<drumkitai/parthenon/pull/<EMAIL>>",

		Sender:     "<EMAIL>",
		Recipients: "<EMAIL>",
		CC:         "<EMAIL>",

		Subject: "Testing Email Labels",
		Body:    "Hey,\nthe truck is 123 \n the driver: Rylie \n the trailer: 2224 \nPickup time is now 10/7/23 at 10 am. ID is 31450-77153. And another one is 31433-54712. \nDelivery time is 10/8/23 at 3 pm", //nolint

		S3URL: "s3://bucket/key.json",
	}

	expectedLabels := []string{"carrier info", "load building", "truck list", "quote request", "carrier quote response"}

	result := labelEmail(context.Background(), *emailInput, opts)

	assert.ElementsMatch(t, expectedLabels, result)

}

func TestNoLabeling(t *testing.T) {
	now := time.Now()
	emailInput := &models.IngestedEmail{
		Account:   "<EMAIL>",
		UserID:    1,
		ServiceID: 1,

		RFCMessageID: "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>",
		ExternalID:   "msg-f:1775132995676831833",
		ThreadID:     "thread-f:1775120948217100423",
		SentAt:       time.UnixMilli(now.UnixMilli()),

		ThreadReferences: "<drumkitai/drumkit/pull/<EMAIL>>",
		InReplyTo:        "<drumkitai/parthenon/pull/<EMAIL>>",

		Sender:     "<EMAIL>",
		Recipients: "<EMAIL>",
		CC:         "<EMAIL>",

		Subject: "Testing Email Labels",
		Body:    "Hey\nThis email should have no labels applied to it.",

		S3URL: "s3://bucket/key.json",
	}

	expectedLabels := []string{}

	result := labelEmail(context.Background(), *emailInput, opts)

	assert.ElementsMatch(t, expectedLabels, result)

}

//
// Check Call Test Cases
//

// FIXME: Improve labeling logic, shouldn't be load building when both features enabled
func TestCheckCallLabelEmail(t *testing.T) {
	ctx := context.Background()

	tests := map[string]struct {
		EmailContent       string
		ExpectedRegexMatch []string
		ExpectedLabel      []string
	}{
		"Loaded": {
			EmailContent:       "The truck is loaded.",
			ExpectedRegexMatch: []string{"load(ed|ing)"},
			ExpectedLabel:      []string{"check call", "truck list"},
		},
		"Unloaded": {
			EmailContent:       "The truck has been unloaded.",
			ExpectedRegexMatch: []string{"unload(ed|ing)"},
			ExpectedLabel:      []string{"check call", "truck list"},
		},
		"At Pickup": {
			EmailContent:       "The delivery is at the pickup location.",
			ExpectedRegexMatch: []string{"at pickup"},
			ExpectedLabel:      []string{"check call", "load building"},
		},
		"At Drop-off": {
			EmailContent:       "The driver is at the drop-off point.",
			ExpectedRegexMatch: []string{"at drop(?:-|/|\\s+)?off"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Picked up": {
			EmailContent:       "The package has been picked up.",
			ExpectedRegexMatch: []string{"pick(?:ed|ing) up"},
			ExpectedLabel:      []string{"check call"},
		},
		"Delivered": {
			EmailContent:       "The package is being delivered.",
			ExpectedRegexMatch: []string{"deliver(?:ed|ing)"},
			ExpectedLabel:      []string{"check call"},
		},
		"Checked In": {
			EmailContent:       "The driver has checked in.",
			ExpectedRegexMatch: []string{"check(?:ed|ing)?(?:-|\\s+)?in"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Checked Out": {
			EmailContent:       "The driver has checked out.",
			ExpectedRegexMatch: []string{"check(?:ed|ing)?(?:-|\\s+)?out"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Checking In": {
			EmailContent:       "The driver is checking in.",
			ExpectedRegexMatch: []string{"check(?:ed|ing)?(?:-|\\s+)?in"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Checking Out": {
			EmailContent:       "The driver is checking out.",
			ExpectedRegexMatch: []string{"check(?:ed|ing)?(?:-|\\s+)?out"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Loaded and Unloaded": {
			EmailContent:       "The truck was loaded and then unloaded.",
			ExpectedRegexMatch: []string{"load(ed|ing)", "unload(ed|ing)"},
			ExpectedLabel:      []string{"check call", "truck list"},
		},
		// False positive
		"At Pickup with Extra Words": {
			EmailContent:       "The delivery is scheduled to be picked up at the warehouse.",
			ExpectedRegexMatch: []string{"at pickup"},
			ExpectedLabel:      []string{"check call", "load building"},
		},
		"Picked-up with Hyphen": {
			EmailContent:       "The package was picked-up at the warehouse.",
			ExpectedRegexMatch: []string{"pick(?:ed|ing) up"},
			ExpectedLabel:      []string{"check call"},
		},
		"Check In with Extra Words": {
			EmailContent:       "The driver has checked in at the warehouse.",
			ExpectedRegexMatch: []string{"check(?:ed|ing)?(?:-|\\s+)?in"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"At Shipper": {
			EmailContent:       "The delivery is currently at the shipper's location.",
			ExpectedRegexMatch: []string{"at shipper"},
			ExpectedLabel:      []string{"check call", "load building"},
		},
		"At Consignee": {
			EmailContent:       "The shipment is at the consignee's address.",
			ExpectedRegexMatch: []string{"at consignee"},
			ExpectedLabel:      []string{"check call", "load building"},
		},
		"At Warehouse": {
			EmailContent:       "The driver at the warehouse.",
			ExpectedRegexMatch: []string{"at warehouse"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Current Location": {
			EmailContent:       "The driver's current location is Reading,MA.",
			ExpectedRegexMatch: []string{"current location"},
			ExpectedLabel:      []string{"check call", "load building", "truck list"},
		},
		"Current location question": {
			EmailContent:  "What is the driver's current location?",
			ExpectedLabel: []string{"load building", "truck list"},
		},
		"In/Out": {
			EmailContent:       "Pickup:\nIn:5:20p\nOut:7:30p",
			ExpectedRegexMatch: []string{`(in|out)\s*:`},
			ExpectedLabel:      []string{"check call", "load building"},
		},
	}

	for name, tc := range tests {
		t.Run(name, func(t *testing.T) {
			labels := labelEmail(
				ctx,
				models.IngestedEmail{
					Body:    tc.EmailContent,
					Account: "<EMAIL>"},
				&Options{
					Service: &models.Service{
						FeatureFlags: models.FeatureFlags{
							IsQuickQuoteEnabled:   true,
							IsLoadBuildingEnabled: true,
						},
					},
				},
			)

			require.ElementsMatch(t, tc.ExpectedLabel, labels)
		})
	}
}

//
// Truck List Test Cases
//

func TestTruckListLabeling(t *testing.T) {
	t.Run("Markdown formatted truck list with multiple weekdays", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Truck list",
			Body: `NDL Open Dry Vans
Thursday 3/7
  - El Dorado KS
Friday 3/8
  - Grand Prairie TX
  - Lithonia GA
  - Statesville NC
Monday 3/11
  - Winter Haven FL
  - Lithonia GA (2 trucks)`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Table formatted truck list with weekdays", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Truck Availability",
			Body: `NDL Open Dry Vans
Thursday 3/7 El Dorado KS
Friday 3/8   Grand Prairie TX
             Lithonia GA
             Statesville NC
Monday 3/11  Winter Haven FL
             Lithonia GA (2 trucks)`,
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Table formatted truck list with short weekdays and case insensitive", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Truck Availability",
			Body: `NDL Open Dry Vans
thurs 3/7 El Dorado KS
fri 3/8   Grand Prairie TX
             Lithonia GA
             Statesville NC
mon 3/11  Winter Haven FL
             Lithonia GA (2 trucks)`,
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Table formatted truck list with weekdays AND years", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Truck Availability",
			Body: `NDL Open Dry Vans
Thursday 3/7/24 El Dorado KS
Friday 3/8/24   Grand Prairie TX
             Lithonia GA
             Statesville NC
Monday 3/11/24  Winter Haven FL
             Lithonia GA (2 trucks)`,
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Multiple dates on new lines", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Available trucks",
			Body: `Available trucks:
7/11 - Chicago
8/11 - Detroit
9/11 - Milwaukee`,
		}

		expectedLabels := []string{"truck list"}
		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Multiple weekdays on new lines", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Weekly availability",
			Body: `Truck schedule:
Monday 7/11
Tuesday 8/11
Wednesday 9/11`,
		}

		expectedLabels := []string{"truck list"}
		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Not a truck list - single date", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `Good morning:
7/11 - Chicago`,
		}

		var expectedLabels []string
		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Not a truck list - single weekday", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `Good morning:
Monday 7/11`,
		}

		var expectedLabels []string
		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("HTML table format should be labeled as truck list", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Good morning",
			Body: `
<table>
<tr><th>Date</th><th>Location</th></tr>
<tr><td>7/11</td><td>Chicago, IL</td></tr>
</table>`,
		}

		expectedLabels := []string{"truck list"}
		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email with 1 city, state pair", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `Locations:
- Houston, TX`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string(nil)

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email with multiple city, state pairs", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `Locations:
- Houston, TX
- Atlanta, GA
- Chicago, IL
- Denver, CO`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email with multiple date formats in mm/dd style", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `
Available Dates:
- 7/11
- 07-12
- 8.13`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email with month names and weekdays", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `Today:
- Monday 3/11
- March 12 Wednesday
- Apr 5 Fri
- Saturday May 6`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email with HTML table content", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Subject to Test Regex",
			Body: `<table>
<tr><td>City</td><td>State</td><td>Date</td></tr>
<tr><td>Houston</td><td>TX</td><td>3/11</td></tr>
<tr><td>Denver</td><td>CO</td><td>3/12</td></tr>
</table>`,
			S3URL: "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Backhaul", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Email",
			Body:       `Backhaul trucks available`,
			S3URL:      "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Back-haul", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Email",
			Body:       `Backhaul trucks available`,
			S3URL:      "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Back haul", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Email",
			Body:       `Backhaul trucks available`,
			S3URL:      "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Back - haul", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Email",
			Body:       `Backhaul trucks available`,
			S3URL:      "s3://bucket/key.json",
		}

		expectedLabels := []string{"truck list"}

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})

	t.Run("Email without matching patterns", func(t *testing.T) {
		email := models.IngestedEmail{
			Account:    "<EMAIL>",
			Recipients: "<EMAIL>",
			Subject:    "Random Email",
			Body:       `This is a random email with no details.`,
			S3URL:      "s3://bucket/key.json",
		}

		expectedLabels := []string(nil)

		result := labelEmail(context.Background(), email, &Options{})
		assert.Equal(t, expectedLabels, result)
	})
}
