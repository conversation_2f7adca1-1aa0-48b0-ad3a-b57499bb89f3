package emails

import (
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/models"
)

type Options struct {
	AppEnv    string
	AppTmpDir string
	// Loads associated with the email (always empty during on-prem labeling);
	// use with `OnPrem` if checking if len(loads) == 0
	EmailLoads           []models.Load
	S3Attachments        []models.Attachment
	HasPDFs              bool
	EmailAddress         string
	ExternalEmailID      uint
	ExternalUserID       uint
	LabelsToReprocess    []EmailLabel
	LLMEndpoint          string
	OnPrem               bool
	S3URL                string
	Service              *models.Service
	ServiceIDFromDrumkit uint
	User                 *models.User
	EmailReprocess       bool
	SQSClient            sqsclient.API
}

type Option func(*Options)

// WithAppEnv sets the app environment.
func WithAppEnv(env string) Option {
	return func(o *Options) {
		o.AppEnv = env
	}
}

// WithAppTmpDir sets the app's temporary directory.
func WithAppTmpDir(dir string) Option {
	return func(o *Options) {
		o.AppTmpDir = dir
	}
}

// WithAttachments sets the S3 attachment urls, which point to attachments included from the email.
func WithAttachments(attachments []models.Attachment) Option {
	return func(o *Options) {
		o.S3Attachments = attachments
	}
}

// WithHasPDFs sets the flag to indicate if the email attachments includes PDFs.
func WithHasPDFs(hasPDFs bool) Option {
	return func(o *Options) {
		o.HasPDFs = hasPDFs
	}
}

// WithEmailAddress sets the email address of the user stored by the customer.
func WithEmailAddress(email string) Option {
	return func(o *Options) {
		o.EmailAddress = email
	}
}

// WithExternalEmailID sets the email id stored by the customer.
func WithExternalEmailID(id uint) Option {
	return func(o *Options) {
		o.ExternalEmailID = id
	}
}

// WithExternalUserID sets the user id stored by the customer.
func WithExternalUserID(id uint) Option {
	return func(o *Options) {
		o.ExternalUserID = id
	}
}

// WithLLMEndpoint sets the llm endpoint, which can be used to classify emails.
func WithLLMEndpoint(endpoint string) Option {
	return func(o *Options) {
		o.LLMEndpoint = endpoint
	}
}

// WithOnPrem sets whether the customer hosts the environment on-premises.
func WithOnPrem(isOnPrem bool) Option {
	return func(o *Options) {
		o.OnPrem = isOnPrem
	}
}

// WithS3URL sets the S3 url, which is usually used to point to the location of a stored raw email.
func WithS3URL(url string) Option {
	return func(o *Options) {
		o.S3URL = url
	}
}

// WithService sets the service when we want to use feature flags. We don't set it when the customer self-hosts since
// the service model doesn't exist in their database.
func WithService(service *models.Service) Option {
	return func(o *Options) {
		o.Service = service
	}
}

// WithServiceIDFromDrumkit sets the service id of the customer stored by Drumkit.
func WithServiceIDFromDrumkit(id uint) Option {
	return func(o *Options) {
		o.ServiceIDFromDrumkit = id
	}
}

// WithUser sets the user.
func WithUser(user *models.User) Option {
	return func(o *Options) {
		o.User = user
	}
}

// WithLoads sets the loads associated with the email (not available in on-prem labeling).
func WithLoads(loads []models.Load) Option {
	return func(o *Options) {
		o.EmailLoads = loads
	}
}

// WithEmailReprocess sets the flag to override existing logic that prevents emails reprocessing.
func WithEmailReprocess(isEmailReprocess bool) Option {
	return func(o *Options) {
		o.EmailReprocess = isEmailReprocess
	}
}

// WithSQSClient sets the SQS client.
func WithSQSClient(sqsClient sqsclient.API) Option {
	return func(o *Options) {
		o.SQSClient = sqsClient
	}
}
