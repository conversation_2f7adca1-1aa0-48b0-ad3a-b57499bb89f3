package emails

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/llm"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/integrations/llm/textract"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/processor/env"
)

// MockQuoteDB is a mock for quoteDB
type MockQuoteDB struct {
	mock.Mock
}

func (m *MockQuoteDB) GetRequestByEmailID(ctx context.Context, emailID uint) ([]models.QuoteRequest, error) {
	args := m.Called(ctx, emailID)
	return args.Get(0).([]models.QuoteRequest), args.Error(1)
}

func (m *MockQuoteDB) GetRequestByThreadID(ctx context.Context, threadID string) ([]models.QuoteRequest, error) {
	args := m.Called(ctx, threadID)
	return args.Get(0).([]models.QuoteRequest), args.Error(1)
}

func (m *MockQuoteDB) CreateQuoteRequest(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	args := m.Called(ctx, quoteRequest)
	return args.Error(0)
}

// MockIntegrationDB is a mock for integrationDB
type MockIntegrationDB struct {
	mock.Mock
}

func (m *MockIntegrationDB) GetTMSListByServiceID(ctx context.Context, serviceID uint) ([]models.Integration, error) {
	args := m.Called(ctx, serviceID)
	return args.Get(0).([]models.Integration), args.Error(1)
}

// MockLLM is a mock for llm.StandardOpenAI
type MockLLM struct {
	mock.Mock
}

func (m *MockLLM) ExtractQuoteRequestSuggestionsDetails(
	ctx context.Context,
	email models.Email,
	openaiService openai.Service,
	textract textract.Client,
	rds llm.RDSInterface,
	_ ...llm.Option,
) ([]models.QuoteRequest, error) {

	args := m.Called(ctx, email, openaiService, textract, rds)
	return args.Get(0).([]models.QuoteRequest), args.Error(1)
}

// MockOpenAIService is a mock for openai.Service
type MockOpenAIService struct {
	mock.Mock
}

func (m *MockOpenAIService) GetResponse(
	ctx context.Context,
	_ models.Email,
	_ models.Attachment,
	_ braintrustsdk.ProjectDetails,
	options ...openai.ResponseOptions,
) (openai.GetResponseOutput, error) {
	args := m.Called(ctx, options)
	return args.Get(0).(openai.GetResponseOutput), args.Error(1)
}

func (m *MockOpenAIService) GetEmbedding(
	ctx context.Context,
	input string,
) ([]float64, error) {
	args := m.Called(ctx, input)
	return args.Get(0).([]float64), args.Error(1)
}

func setup(t *testing.T) (*MockQuoteDB, *MockLLM, *MockIntegrationDB) {
	env.Vars.SkipDuplicateSuggestions = true
	mockQuoteDB := new(MockQuoteDB)
	mockLLM := new(MockLLM)
	mockIntegrationDB := new(MockIntegrationDB)
	mockOpenAIServiceInstance := new(MockOpenAIService)

	// Save original function pointers
	origGetQuoteRequestByEmailID := dbFuncGetQuoteRequestByEmailID
	origExtractQuoteRequestSuggestions := llmFuncExtractQuoteRequestSuggestions
	origCreateQuoteRequest := dbFuncCreateQuoteRequest
	origGetTMSListByServiceID := dbFuncGetTMSListByServiceID
	origOpenaiServiceCreator := openaiServiceCreator

	// Set up function overrides
	dbFuncGetQuoteRequestByEmailID = mockQuoteDB.GetRequestByEmailID
	llmFuncExtractQuoteRequestSuggestions = mockLLM.ExtractQuoteRequestSuggestionsDetails
	dbFuncCreateQuoteRequest = mockQuoteDB.CreateQuoteRequest
	dbFuncGetTMSListByServiceID = mockIntegrationDB.GetTMSListByServiceID
	openaiServiceCreator = func(_ context.Context) (openai.Service, error) {
		return mockOpenAIServiceInstance, nil
	}

	t.Cleanup(func() {
		dbFuncGetQuoteRequestByEmailID = origGetQuoteRequestByEmailID
		llmFuncExtractQuoteRequestSuggestions = origExtractQuoteRequestSuggestions
		dbFuncCreateQuoteRequest = origCreateQuoteRequest
		dbFuncGetTMSListByServiceID = origGetTMSListByServiceID
		openaiServiceCreator = origOpenaiServiceCreator
	})

	return mockQuoteDB, mockLLM, mockIntegrationDB
}

func TestRunQuoteRequestExtract(t *testing.T) {
	ctx := context.Background()

	email := models.Email{
		Model: gorm.Model{
			ID: 1,
		},
	}
	dbFuncGetQuickQuoteConfigByServiceID = func(context.Context, uint) (models.QuickQuoteConfig, error) {
		return models.QuickQuoteConfig{}, nil
	}

	service := models.Service{
		FeatureFlags: models.FeatureFlags{
			IsCarrierNetworkQuotingEnabled: true,
			IsQuickQuoteEnabled:            true,
		},
		QuickQuoteConfig: &models.QuickQuoteConfig{
			SpecialEquipment: []string{"tanker"},
		},
	}

	t.Run("Feature not enabled for service", func(t *testing.T) {
		_, _, _ = setup(t)
		service := models.Service{
			FeatureFlags: models.FeatureFlags{
				IsQuickQuoteEnabled: false,
			},
		}

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.NoError(t, err)
	})

	t.Run("Quote request already exists", func(t *testing.T) {
		mockQuoteDB, _, _ := setup(t)

		mockQuoteDB.On("GetRequestByEmailID",
			ctx,
			email.ID,
		).Return([]models.QuoteRequest{
			{
				ID: 1,
			},
		}, nil)

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.NoError(t, err)
		mockQuoteDB.AssertExpectations(t)
	})

	t.Run("DB transient error", func(t *testing.T) {
		mockQuoteDB, mockLLM, mockIntegrationDB := setup(t)

		mockQuoteDB.On("GetRequestByEmailID",
			ctx,
			email.ID,
		).Return([]models.QuoteRequest{}, errors.New("transient error"))

		mockLLM.On("ExtractQuoteRequestSuggestionsDetails",
			ctx,
			email,
			mock.Anything, // openaiClient
			mock.Anything, // textractClient
			mock.Anything, // llm.StandardRDS{}
			mock.Anything, // llm.WithSpecialEquipment option
		).Return([]models.QuoteRequest{
			{ID: 2, ThreadID: "abc123"},
		}, nil)

		mockIntegrationDB.On("GetTMSListByServiceID", ctx, service.ID).Return([]models.Integration{}, nil)

		mockQuoteDB.On("CreateQuoteRequest", mock.Anything, mock.Anything).Return(nil)

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.NoError(t, err)
		mockQuoteDB.AssertExpectations(t)
		mockLLM.AssertExpectations(t)
		mockIntegrationDB.AssertExpectations(t)
	})

	t.Run("No existing quote request and OpenAI succeeds", func(t *testing.T) {
		mockQuoteDB, mockLLM, mockIntegrationDB := setup(t)

		mockQuoteDB.On("GetRequestByEmailID",
			ctx,
			email.ID,
		).Return([]models.QuoteRequest{}, gorm.ErrRecordNotFound)

		mockLLM.On("ExtractQuoteRequestSuggestionsDetails",
			ctx,
			email,
			mock.Anything, // openaiClient
			mock.Anything, // textractClient
			mock.Anything, // llm.StandardRDS{}
			mock.Anything, // llm.WithSpecialEquipment option
		).Return([]models.QuoteRequest{
			{
				ID:       2,
				ThreadID: "abc123",
			},
		}, nil)

		mockIntegrationDB.On("GetTMSListByServiceID", ctx, service.ID).Return([]models.Integration{}, nil)

		mockQuoteDB.On("CreateQuoteRequest",
			mock.Anything,
			mock.Anything,
		).Return(nil)

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.NoError(t, err)
		mockQuoteDB.AssertExpectations(t)
		mockLLM.AssertExpectations(t)
		mockIntegrationDB.AssertExpectations(t)
	})

	t.Run("OpenAI error", func(t *testing.T) {
		mockQuoteDB, mockLLM, mockIntegrationDB := setup(t)

		mockQuoteDB.On("GetRequestByEmailID",
			ctx,
			email.ID,
		).Return([]models.QuoteRequest{}, gorm.ErrRecordNotFound)

		mockLLM.On("ExtractQuoteRequestSuggestionsDetails",
			ctx,
			email,
			mock.Anything, // openaiClient
			mock.Anything, // textractClient
			mock.Anything, // llm.StandardRDS{}
			mock.Anything, // llm.WithSpecialEquipment option
		).Return([]models.QuoteRequest{}, errors.New("OpenAI error"))

		mockIntegrationDB.On("GetTMSListByServiceID", ctx, service.ID).Return([]models.Integration{}, nil)

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.Error(t, err)
		assert.Contains(t, err.Error(), "OpenAI error")
		mockQuoteDB.AssertExpectations(t)
		mockLLM.AssertExpectations(t)
		mockIntegrationDB.AssertExpectations(t)
	})

	t.Run("DB upsert error", func(t *testing.T) {
		mockQuoteDB, mockLLM, mockIntegrationDB := setup(t)

		mockQuoteDB.On("GetRequestByEmailID",
			ctx,
			email.ID,
		).Return([]models.QuoteRequest{}, gorm.ErrRecordNotFound)

		mockLLM.On("ExtractQuoteRequestSuggestionsDetails",
			ctx,
			email,
			mock.Anything, // openaiClient
			mock.Anything, // textractClient
			mock.Anything, // llm.StandardRDS{}
			mock.Anything, // llm.WithSpecialEquipment option
		).Return([]models.QuoteRequest{
			{
				ID:       2,
				ThreadID: "abc123",
			},
		}, nil)

		mockIntegrationDB.On("GetTMSListByServiceID", ctx, service.ID).Return([]models.Integration{}, nil)

		mockQuoteDB.On("CreateQuoteRequest", mock.Anything, mock.Anything).Return(errors.New("DB upsert error"))

		err := runQuoteRequestExtract(ctx, email, WithService(&service))

		require.Error(t, err)
		assert.Contains(t, err.Error(), "error creating quote request")
		mockQuoteDB.AssertExpectations(t)
		mockLLM.AssertExpectations(t)
		mockIntegrationDB.AssertExpectations(t)
	})
}

func TestDeduplicateSuggestions(t *testing.T) {
	ctx := context.Background()

	nyLoc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	now := time.Now().UTC()

	// Init QR1
	dupeQR1 := models.QuoteRequest{
		UserID:    1,
		EmailID:   100,
		ServiceID: 200,
		ThreadID:  "thread-1",
		Status:    models.SuggestionStatus(models.PendingStatus),
		SuggestedRequest: models.QuoteLoadInfo{
			TransportType: models.TransportType("truck"),
			Commodity:     "electronics",
			WeightLbs:     5000,
			Distance:      300,
			PickupDate:    models.ToValidNullTime(now),
		},
	}
	initializeTimestampsRecursive(&dupeQR1.SuggestedRequest, now)

	dupeQRJSON, err := json.Marshal(dupeQR1)
	require.NoError(t, err)
	log.Infof(ctx, "dupeQR1 in UTC (JSON): %s", string(dupeQRJSON))

	// Init DupeQR2, which is same as DupeQR1 but with NY loc
	dupeQR2 := dupeQR1
	initializeTimestampsRecursive(&dupeQR2.SuggestedRequest, now.In(nyLoc))
	dupeQR2JSON, err := json.Marshal(dupeQR2)
	require.NoError(t, err)
	log.Infof(ctx, "dupeQR2 in NY loc (JSON): %s", string(dupeQR2JSON))

	otherQR := models.QuoteRequest{
		UserID:    1,
		EmailID:   100,
		ServiceID: 200,
		ThreadID:  "thread-1",
		Status:    models.Pending,
		SuggestedRequest: models.QuoteLoadInfo{
			TransportType: models.TransportType("van"),
			Commodity:     "furniture",
			WeightLbs:     8000,
			Distance:      1000,
		},
	}

	t.Run("Handle duplicates", func(t *testing.T) {
		mockSuggestions := []models.QuoteRequest{dupeQR1, dupeQR1, otherQR}

		deduped := deduplicateSuggestions(ctx, mockSuggestions)

		require.Len(t, deduped, 2)
		assert.ElementsMatch(t, []models.QuoteRequest{dupeQR1, otherQR}, deduped)
	})

	// If test is failing, likely because additional timestamp fields were added to model that are not
	// being normalized in the deduplicateSuggestions function. Update the func to handle
	t.Run("Handle duplicates diff timezone format", func(t *testing.T) {
		mockSuggestions := []models.QuoteRequest{dupeQR1, dupeQR2, otherQR}

		deduped := deduplicateSuggestions(ctx, mockSuggestions)

		require.Len(t, deduped, 2)
		assert.ElementsMatch(t, []models.QuoteRequest{dupeQR1, otherQR}, deduped)
	})

	t.Run("Handle single entry", func(t *testing.T) {
		mockSuggestions := []models.QuoteRequest{otherQR}
		deduped := deduplicateSuggestions(ctx, mockSuggestions)

		require.Len(t, deduped, 1)
		assert.ElementsMatch(t, mockSuggestions, deduped)
	})

	t.Run("Handle no duplicates", func(t *testing.T) {
		mockSuggestions := []models.QuoteRequest{dupeQR1, otherQR}
		deduped := deduplicateSuggestions(ctx, mockSuggestions)

		require.Len(t, deduped, 2)
		assert.ElementsMatch(t, mockSuggestions, deduped)
	})

	t.Run("Handle empty input", func(t *testing.T) {
		deduped := deduplicateSuggestions(ctx, []models.QuoteRequest{})

		assert.Empty(t, deduped)
	})
}

// Recursively initialize timestamps in structs to the given time to catch any bugs in the deduplication logic
func initializeTimestampsRecursive(value any, t time.Time) {
	v := reflect.ValueOf(value)

	// Only process structs and pointers to structs
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return
	}

	if v.Type().Name() == "TMSCustomer" {
		return
	}
	// Iterate over the struct fields
	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)

		// Handle pointer fields
		if field.Kind() == reflect.Ptr && !field.IsNil() {
			initializeTimestampsRecursive(field.Interface(), t)
		}

		// Handle nested structs
		if field.Kind() == reflect.Struct {
			initializeTimestampsRecursive(field.Addr().Interface(), t)
		}

		// Set time.Time fields
		if field.Type() == reflect.TypeOf(time.Time{}) && field.CanSet() {
			field.Set(reflect.ValueOf(t))
		}

		// Set NullTime fields
		if field.Type() == reflect.TypeOf(models.NullTime{}) && field.CanSet() {
			nullTime := models.NullTime{Time: t, Valid: true}
			field.Set(reflect.ValueOf(nullTime))
		}
	}
}
