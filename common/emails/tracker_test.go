package emails

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/models"
)

func TestGetMcleodIDsFromEmail(t *testing.T) {
	tmsIntegration := models.Integration{Name: models.McleodEnterprise}

	t.Run("Extract load IDs correctly", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "0792351 for your request",
			Body:    "Please refer to 0792351 for details.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: mcleodenterprise.LoadIDType, ID: "0792351"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "0792351"},
		}

		require.Len(t, result, 2)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID})
		assert.Equal(t, tmsIntegration, result[0].integration)
		assert.Equal(t, tmsIntegration, result[1].integration)
	})

	t.Run("Remove URLs for RefNumberIDType", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "Random email",
			Body:    "Visit https://example.com/REF-12345 for details.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 0)
	})

	t.Run("Skip IDs without numbers for RefNumberIDType", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "REFERENCE",
			Body:    "The ID is REFERENCE.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 0)
	})

	t.Run("Handle mixed ID types", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "LOAD-54321 and Movement 654321",
			Body:    "Additional info: REF-09876 and https://example.com/URLREF-3532.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: mcleodenterprise.RefNumberIDType, ID: "LOAD-54321"},
			{IDType: mcleodenterprise.MovementIDType, ID: "654321"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "654321"},
			{IDType: mcleodenterprise.RefNumberIDType, ID: "REF-09876"},
		}

		require.Len(t, result, 4)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID,
			result[2].FreightID, result[3].FreightID})
	})

	t.Run("Deduplicate IDs", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "LOAD-11111 and LOAD-11111",
			Body:    "Repeated IDs: LOAD-11111.",
		}

		result := getMcleodIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 1)
		assert.Equal(t, "LOAD-11111", result[0].FreightID.ID)
	})
}

func TestGetRevenovaIDsFromEmail(t *testing.T) {
	tmsIntegration := models.Integration{Name: models.Revenova}

	t.Run("Extract Salesforce 18-char IDs correctly", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "Load Update for a0i0x0000014APkAAM",
			Body:    "Please refer to a0i0x0000014APkAAM for details. Also check a0i0x0000014APkBBM.",
		}

		result := getRevenovaIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: revenova.LoadIDType, ID: "a0i0x0000014APkAAM"},
			{IDType: revenova.LoadIDType, ID: "a0i0x0000014APkBBM"},
		}

		require.Len(t, result, 2)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID})
		assert.Equal(t, tmsIntegration, result[0].integration)
		assert.Equal(t, tmsIntegration, result[1].integration)
	})

	t.Run("Extract Load-XXXXXX format IDs correctly", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "Load Update for Load-307453",
			Body:    "Please refer to Load-307453 for details. Also check Load-123456.",
		}

		result := getRevenovaIDsFromEmail(email, tmsIntegration)

		expected := []tmsFreightID{
			{IDType: revenova.LoadIDType, ID: "Load-307453"},
			{IDType: revenova.LoadIDType, ID: "Load-123456"},
		}

		require.Len(t, result, 2)
		assert.ElementsMatch(t, expected, []tmsFreightID{result[0].FreightID, result[1].FreightID})
		assert.Equal(t, tmsIntegration, result[0].integration)
		assert.Equal(t, tmsIntegration, result[1].integration)
	})

	t.Run("Extract various ID formats", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "Multiple formats: Load-307453, REF-12345, and a0i0x0000014APkAAM",
			Body:    "Also check COOL-789 and ABC-123456.",
		}

		result := getRevenovaIDsFromEmail(email, tmsIntegration)

		// Should find all the various formats
		require.Len(t, result, 5)

		// Check that we found the expected IDs
		foundIDs := make([]string, len(result))
		for i, r := range result {
			foundIDs[i] = r.FreightID.ID
		}

		expectedIDs := []string{"Load-307453", "REF-12345", "a0i0x0000014APkAAM", "COOL-789", "ABC-123456"}
		for _, expectedID := range expectedIDs {
			assert.Contains(t, foundIDs, expectedID)
		}
	})

	t.Run("Handle empty email", func(t *testing.T) {
		email := &models.IngestedEmail{
			Subject: "No load IDs here",
			Body:    "Just regular text without any load references.",
		}

		result := getRevenovaIDsFromEmail(email, tmsIntegration)

		require.Len(t, result, 0)
	})
}
