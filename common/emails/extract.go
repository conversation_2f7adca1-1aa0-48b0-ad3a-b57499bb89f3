package emails

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/mail"
	"strings"

	md "github.com/<PERSON><PERSON>/html-to-markdown"
	plugins "github.com/<PERSON><PERSON><PERSON><PERSON>/html-to-markdown/plugin"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// Convert a MessageInterface, which holds either a Gmail or Outlook email into the *models.IngestedEmail object
// we will send to the Processor Lambda. This prepares the object accordingly for either case, sending it internally
// or receiving it from a customer.
// TODO: S3 backup should happen before body is converted to markdown
func PrepareEmailPayload[T MessageInterface](
	ctx context.Context,
	msg T,
	opts ...Option,
) (_ *models.IngestedEmail, err error) {

	options := &Options{
		OnPrem:          false,
		S3URL:           "",
		User:            nil,
		ExternalUserID:  0,
		ExternalEmailID: 0,
		HasPDFs:         false,
	}

	for _, opt := range opts {
		opt(options)
	}

	ctx, metaSpan := otel.StartSpan(ctx, "PrepareEmailPayload", MessageAttrs(options.User, msg))
	defer func() { metaSpan.End(nil) }()

	body, bodyType, isTruncated := msg.GetBody(options.S3URL != "")

	if isTruncated {
		// The max SQS message size is 262144 bytes: truncate the body to 200KB or less
		const maxBodyLength = 200000

		log.WarnNoSentry(
			ctx,
			"truncating email body",
			zap.String("msgId", msg.GetID()),
			zap.Int("originalLength", len(body)),
			zap.Int("truncatedLength", maxBodyLength),
		)
	}

	email := &models.IngestedEmail{
		Account:          options.EmailAddress,
		ExternalUserID:   options.ExternalUserID,
		ExternalEmailID:  options.ExternalEmailID,
		RFCMessageID:     msg.GetRFCMessageID(),
		ExternalID:       msg.GetID(),
		ThreadID:         msg.GetThreadID(),
		SentAt:           msg.GetInternalDate(),
		ThreadReferences: msg.GetThreadReferences(),
		InReplyTo:        msg.GetInReplyTo(),
		Sender:           msg.GetSender(),
		Recipients:       msg.GetRecipients(),
		CC:               msg.GetCC(),
		Subject:          msg.GetSubject(),
		Body:             body,
		BodyType:         bodyType,
		Truncated:        isTruncated,
		Attachments:      options.S3Attachments,
		S3URL:            options.S3URL,
		HasPDFs:          options.HasPDFs,
	}

	if options.User != nil {
		email.UserID = options.User.ID
		email.ServiceID = options.User.ServiceID
	}

	if options.OnPrem {
		email.ExternalUserID = options.ExternalUserID
		email.ExternalEmailID = options.ExternalEmailID
	}

	return email, nil
}

// plaintextOrMarkdown extracts the body of an email and converts it to markdown if it's HTML.
// Otherwise, it returns the body as plaintext.
func plaintextOrMarkdown(
	ctx context.Context,
	message any,
) (content string, contentType models.EmailBodyType, err error) {

	switch msg := message.(type) {
	case *gmail.MessagePart:
		// First try to get HTML content and convert to markdown
		if htmlPart := FindMimeType(msg, "text/html"); htmlPart != nil {
			body, err := GetPartBody(htmlPart)
			if err != nil {
				return "", models.PlaintextEmailBodyType, err
			}

			log.Info(ctx, "converting HTML to Markdown using html-to-markdown")

			converter := md.NewConverter("", true, nil)
			// this plugin allows tables to be converted to markdown
			converter.Use(plugins.GitHubFlavored())

			if markdown, err := converter.ConvertString(body); err == nil {
				return markdown, models.MarkdownEmailBodyType, nil
			}

			log.Warn(ctx, "failed to convert HTML email body to Markdown, falling back to plaintext", zap.Error(err))
		}

		// Fallback to plaintext if no HTML content available or conversion failed
		if textPart := FindMimeType(msg, "text/plain"); textPart != nil {
			body, err := GetPartBody(textPart)
			if err != nil {
				return "", models.PlaintextEmailBodyType, err
			}

			return body, models.PlaintextEmailBodyType, nil
		}

		log.WarnNoSentry(ctx, "no text/html nor text/plain sections found")
		return "", models.PlaintextEmailBodyType, nil

	case *msclient.Body:
		if msg == nil {
			return "", models.PlaintextEmailBodyType, nil
		}

		bodyContent := msg.Content
		contentType := strings.ToLower(msg.ContentType)

		// First try HTML content and convert to markdown
		if contentType == "html" || contentType == "text/html" {
			log.Info(ctx, "Outlook: Converting HTML to Markdown using html-to-markdown")

			converter := md.NewConverter("", true, nil)
			// this plugin allows tables to be converted to markdown
			converter.Use(plugins.GitHubFlavored())

			markdown, err := converter.ConvertString(bodyContent)
			if err != nil {
				log.Error(ctx, "Failed to convert Outlook HTML body to Markdown", zap.Error(err))

				return "",
					models.PlaintextEmailBodyType,
					fmt.Errorf("failed to convert Outlook HTML body to Markdown: %w", err)
			}

			return markdown, models.MarkdownEmailBodyType, nil
		}

		// Fallback to plaintext if no HTML content available
		if contentType == "text" || contentType == "text/plain" {
			return bodyContent, models.PlaintextEmailBodyType, nil
		}

		log.WarnNoSentry(ctx, "Outlook: No text/html or text/plain content type found")
		return "", models.PlaintextEmailBodyType, nil

	default:
		return "", models.PlaintextEmailBodyType, fmt.Errorf("unsupported message type: %v", msg)
	}
}

// Find the first part of the email message with a specific MIME type (e.g. "text/plain")
//
// Returns nil if no such part was found.
func FindMimeType(part *gmail.MessagePart, mimeType string) *gmail.MessagePart {
	if part.MimeType == mimeType {
		return part
	}

	// recursively check child parts
	for _, child := range part.Parts {
		if result := FindMimeType(child, mimeType); result != nil {
			return result
		}
	}

	// no match found
	return nil
}

// Return base64-decoded part body
func GetPartBody(part *gmail.MessagePart) (string, error) {
	if part.Body == nil {
		return "", fmt.Errorf("%s part has a nil body", part.MimeType)
	}

	body, err := base64.URLEncoding.DecodeString(part.Body.Data)
	if err != nil {
		return "", fmt.Errorf("base64 decoding of %s part failed: %w", part.MimeType, err)
	}

	return string(body), nil
}

// Convert a list of headers into a map for easier lookup by lowercase name
func headerMap(msg *gmail.Message) map[string][]string {
	result := make(map[string][]string, len(msg.Payload.Headers))

	for _, header := range msg.Payload.Headers {
		// There can be more than one header with the same name
		key := strings.ToLower(header.Name)
		result[key] = append(result[key], header.Value)
	}

	return result
}

// Return a comma-delimited list of emails in the from/to/cc headers
func addresses(ctx context.Context, headers map[string][]string, name string) string {
	v := getSingleHeader(ctx, headers, name)
	if v == "" {
		return ""
	}

	// Headers might contain both the name and the email, e.g. "From: Axle API <<EMAIL>>"
	// We care only about the email address: use the net/mail library to parse these headers
	addrs, err := mail.ParseAddressList(v)
	if err != nil {
		log.WarnNoSentry(ctx, "can't parse header", zap.String("headerName", name), zap.Error(err))
		return v
	}

	emails := make([]string, len(addrs))
	for i, addr := range addrs {
		emails[i] = addr.Address
	}

	return strings.Join(emails, ", ")
}

// Get the value for a single header, printing an error if there was more than one
func getSingleHeader(ctx context.Context, headers map[string][]string, name string) string {
	vals := headers[name]
	switch len(vals) {
	case 0:
		return ""
	case 1:
		return vals[0]
	default:
		log.WarnNoSentry(ctx, "expected at most 1 header, found multiple", zap.String("headerName", name))
		return vals[0]
	}
}
