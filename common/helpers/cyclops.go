package helpers

import (
	"fmt"
	"os"
)

const (
	EnvDevelopment = "dev"
	EnvStaging     = "staging"
	EnvProduction  = "prod"

	envAppEnvironment = "APP_ENV"
	envCyclopsURL     = "CYCLOPS_URL"

	cyclopsDevURL = "http://localhost:8000"
)

type ConfigError struct {
	Env     string
	Missing string
}

func (e *ConfigError) Error() string {
	return fmt.Sprintf("missing %s configuration for environment: %s", e.Missing, e.Env)
}

// GetCyclopsURL returns the appropriate Cyclops URL based on the environment.
// For the development environment, it returns the default dev URL if none is specified.
// For other environments, it requires the URL to be explicitly configured.
func GetCyclopsURL() (string, error) {
	appEnv := os.Getenv(envAppEnvironment)
	if appEnv == "" {
		appEnv = EnvDevelopment
	}

	cyclopsURL := os.Getenv(envCyclopsURL)

	switch appEnv {
	case EnvDevelopment:
		if cyclopsURL == "" {
			return cyclopsDevURL, nil
		}
		return cyclopsURL, nil

	case EnvStaging, EnvProduction:
		if cyclopsURL == "" {
			return "", &ConfigError{
				Env:     appEnv,
				Missing: "CYCLOPS_URL",
			}
		}
		return cyclopsURL, nil

	default:
		return "", fmt.Errorf("invalid environment: %s", appEnv)
	}
}
