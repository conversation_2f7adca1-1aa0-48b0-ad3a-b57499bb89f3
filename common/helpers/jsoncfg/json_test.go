package jsoncfg

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type testInput struct {
	Headers map[string]any `json:"headers"`
	ID      string         `json:"id"`
}

var testMsg = &testInput{
	Headers: map[string]any{
		"from": "Drumkit",
	},
	ID: "<<EMAIL>>",
}

func TestJSONMarshalDefault(t *testing.T) {
	t.<PERSON>()

	// HTML escape + no indent (should be the same as the stdlib)
	result, err := DefaultConfig.Marshal(testMsg)
	require.NoError(t, err)

	expected := `{"headers":{"from":"Drumkit"},"id":"\<EMAIL>\u003e"}`
	assert.Equal(t, expected, string(result))

	stdlib, err := json.Marshal(testMsg)
	require.NoError(t, err)
	assert.Equal(t, string(stdlib), string(result))
}

func TestJSONMarshalHumanReadable(t *testing.T) {
	t.<PERSON>()

	result, err := HumanReadableConfig.Marshal(testMsg)
	require.NoError(t, err)

	expected := `{
    "headers": {
        "from": "Drumkit"
    },
    "id": "<<EMAIL>>"
}`
	assert.Equal(t, expected, string(result))
}

func TestJSONMarshalSpaceEfficient(t *testing.T) {
	t.Parallel()

	result, err := SpaceEfficientConfig.Marshal(testMsg)
	require.NoError(t, err)

	expected := `{"headers":{"from":"Drumkit"},"id":"<<EMAIL>>"}`
	assert.Equal(t, expected, string(result))
}
