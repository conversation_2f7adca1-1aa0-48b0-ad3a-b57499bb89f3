// Package jsoncfg provides customizable JSON marshaling.
package jsoncfg

import (
	"bytes"
	"encoding/json"
)

// DefaultConfig is the same as the defaults in the stdlib: "&", "<", and ">" will be escaped for safety
var DefaultConfig = JSONConfig{EscapeHTML: true}

// SpaceEfficientConfig does not escape HTML nor add any indent
var SpaceEfficientConfig = JSONConfig{EscapeHTML: false}

// HumanReadableConfig adds 4 spaces of indent
var HumanReadableConfig = JSONConfig{EscapeHTML: false, Indent: "    "}

type JSONConfig struct {
	// If true, json.Marshal escapes "&", "<", and ">" as "\u0026", "\u003c", and "\u003e"
	// https://pkg.go.dev/encoding/json#Encoder.SetEscapeHTML
	EscapeHTML bool

	// To make JSON output more human-readable, set Indent to "  " or "    "
	Indent string
}

// Marshal is a customizable version of json.Marshal
func (cfg JSONConfig) Marshal(input any) ([]byte, error) {
	var buf bytes.Buffer
	enc := json.NewEncoder(&buf)
	enc.SetEscapeHTML(cfg.EscapeHTML)
	enc.SetIndent("", cfg.Indent)

	if err := enc.Encode(input); err != nil {
		return nil, err
	}

	result := buf.Bytes()

	// Strip trailing newline added by .Encode()
	return result[:len(result)-1], nil
}
