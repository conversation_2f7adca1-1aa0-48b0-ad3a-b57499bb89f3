package similarity

import (
	"testing"
)

func TestCalculateSimilarity_ExactMatch(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "exact match case insensitive",
			s1:       "ABC Corp",
			s2:       "ABC Corp",
			config:   DefaultConfig(),
			expected: 1.0,
		},
		{
			name:     "exact match case sensitive",
			s1:       "ABC Corp",
			s2:       "abc corp",
			config:   Config{CaseSensitive: true, NormalizeWhitespace: true},
			expected: 0.5, // Levenshtein similarity when case doesn't match
		},
		{
			name:     "exact match with whitespace normalization",
			s1:       "ABC Corp",
			s2:       "  ABC   Corp  ",
			config:   DefaultConfig(),
			expected: 1.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.<PERSON>("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_PrefixMatch(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "prefix match",
			s1:       "ABC",
			s2:       "ABC Corporation",
			config:   DefaultConfig(),
			expected: 0.9,
		},
		{
			name:     "prefix match case insensitive",
			s1:       "abc",
			s2:       "ABC Corporation",
			config:   DefaultConfig(),
			expected: 0.9,
		},
		{
			name:     "prefix match with whitespace",
			s1:       "ABC",
			s2:       "  ABC Corporation  ",
			config:   DefaultConfig(),
			expected: 0.9,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_ContainsMatch(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "contains match",
			s1:       "Corp",
			s2:       "ABC Corporation",
			config:   DefaultConfig(),
			expected: 0.7,
		},
		{
			name:     "contains match case insensitive",
			s1:       "corp",
			s2:       "ABC Corporation",
			config:   DefaultConfig(),
			expected: 0.7,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_LevenshteinDistance(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "small typo",
			s1:       "ABC Corp",
			s2:       "ABC Corpp",
			config:   DefaultConfig(),
			expected: 0.9, // Prefix match takes precedence
		},
		{
			name:     "multiple typos",
			s1:       "ABC Corp",
			s2:       "ABX Corq",
			config:   DefaultConfig(),
			expected: 0.75, // 1 - (2/8) = 0.75
		},
		{
			name:     "too many typos - below threshold",
			s1:       "ABC Corp",
			s2:       "XYZ Company",
			config:   DefaultConfig(),
			expected: 0.0, // Below 0.5 threshold
		},
		{
			name:     "custom threshold",
			s1:       "ABC Corp",
			s2:       "XYZ Company",
			config:   Config{MinSimilarityThreshold: 0.3},
			expected: 0.36363636363636365, // Actual Levenshtein similarity
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_JaroWinklerAlgorithm(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "jaro-winkler exact match",
			s1:       "IBM",
			s2:       "IBM",
			config:   Config{Algorithm: AlgorithmJaroWinkler},
			expected: 1.0,
		},
		{
			name:     "jaro-winkler abbreviation match",
			s1:       "IBM",
			s2:       "International Business Machines",
			config:   Config{Algorithm: AlgorithmJaroWinkler, MinSimilarityThreshold: 0.1},
			expected: 0.6193548387096774, // Actual Jaro-Winkler similarity
		},
		{
			name:     "jaro-winkler transposition",
			s1:       "Smith",
			s2:       "Smtih",
			config:   Config{Algorithm: AlgorithmJaroWinkler, MinSimilarityThreshold: 0.1},
			expected: 0.9466666666666665, // Actual Jaro-Winkler similarity
		},
		{
			name:     "jaro-winkler prefix bonus",
			s1:       "ABC",
			s2:       "ABCDEF",
			config:   Config{Algorithm: AlgorithmJaroWinkler, MinSimilarityThreshold: 0.1},
			expected: 0.8833333333333334, // Actual Jaro-Winkler similarity
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_HybridAlgorithm(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "hybrid algorithm - simple wins",
			s1:       "ABC",
			s2:       "ABC Corporation",
			config:   Config{Algorithm: AlgorithmHybrid},
			expected: 0.9, // Simple algorithm should win with prefix match
		},
		{
			name:     "hybrid algorithm - jaro-winkler wins",
			s1:       "Smith",
			s2:       "Smtih",
			config:   Config{Algorithm: AlgorithmHybrid, MinSimilarityThreshold: 0.1},
			expected: 0.9466666666666665, // Jaro-Winkler should win with transposition
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		expected float64
	}{
		{
			name:     "empty strings",
			s1:       "",
			s2:       "",
			config:   DefaultConfig(),
			expected: 1.0,
		},
		{
			name:     "one empty string",
			s1:       "ABC",
			s2:       "",
			config:   DefaultConfig(),
			expected: 0.0,
		},
		{
			name:     "whitespace only",
			s1:       "   ",
			s2:       "   ",
			config:   DefaultConfig(),
			expected: 1.0,
		},
		{
			name:     "single character",
			s1:       "A",
			s2:       "A",
			config:   DefaultConfig(),
			expected: 1.0,
		},
		{
			name:     "single character different",
			s1:       "A",
			s2:       "B",
			config:   DefaultConfig(),
			expected: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result != tt.expected {
				t.Errorf("CalculateSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateSimilarity_RealWorldExamples(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		config   Config
		minScore float64 // Minimum expected score
		maxScore float64 // Maximum expected score
	}{
		{
			name:     "company name variations",
			s1:       "Walmart",
			s2:       "Walmart Store",
			config:   NameMatchingConfig(),
			minScore: 0.8,
			maxScore: 1.0,
		},
		{
			name:     "abbreviation vs full name",
			s1:       "FedEx",
			s2:       "Federal Express",
			config:   NameMatchingConfig(),
			minScore: 0.0,
			maxScore: 0.9, // Jaro-Winkler gives higher similarity than expected
		},
		{
			name:     "distribution center variations",
			s1:       "ABC Distribution Center",
			s2:       "ABC Dist Ctr",
			config:   NameMatchingConfig(),
			minScore: 0.6,
			maxScore: 1.0,
		},
		{
			name:     "typo in company name",
			s1:       "Microsoft",
			s2:       "Microsft",
			config:   NameMatchingConfig(),
			minScore: 0.7,
			maxScore: 1.0,
		},
		{
			name:     "completely different names",
			s1:       "Apple Inc",
			s2:       "Microsoft Corp",
			config:   NameMatchingConfig(),
			minScore: 0.0,
			maxScore: 0.3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateSimilarity(tt.s1, tt.s2, tt.config)
			if result < tt.minScore || result > tt.maxScore {
				t.Errorf("CalculateSimilarity() = %v, want score between %v and %v", result, tt.minScore, tt.maxScore)
			}
		})
	}
}

func TestConfigFunctions(t *testing.T) {
	t.Run("DefaultConfig", func(t *testing.T) {
		config := DefaultConfig()
		if config.CaseSensitive {
			t.Errorf("DefaultConfig().CaseSensitive = %v, want false", config.CaseSensitive)
		}
		if !config.NormalizeWhitespace {
			t.Errorf("DefaultConfig().NormalizeWhitespace = %v, want true", config.NormalizeWhitespace)
		}
		if config.MinSimilarityThreshold != 0.5 {
			t.Errorf("DefaultConfig().MinSimilarityThreshold = %v, want 0.5", config.MinSimilarityThreshold)
		}
		if config.Algorithm != AlgorithmSimple {
			t.Errorf("DefaultConfig().Algorithm = %v, want AlgorithmSimple", config.Algorithm)
		}
	})

	t.Run("NameMatchingConfig", func(t *testing.T) {
		config := NameMatchingConfig()
		if config.CaseSensitive {
			t.Errorf("NameMatchingConfig().CaseSensitive = %v, want false", config.CaseSensitive)
		}
		if !config.NormalizeWhitespace {
			t.Errorf("NameMatchingConfig().NormalizeWhitespace = %v, want true", config.NormalizeWhitespace)
		}
		if config.MinSimilarityThreshold != 0.6 {
			t.Errorf("NameMatchingConfig().MinSimilarityThreshold = %v, want 0.6", config.MinSimilarityThreshold)
		}
		if config.Algorithm != AlgorithmJaroWinkler {
			t.Errorf("NameMatchingConfig().Algorithm = %v, want AlgorithmJaroWinkler", config.Algorithm)
		}
	})
}

func TestLevenshteinDistance(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		expected int
	}{
		{"identical strings", "abc", "abc", 0},
		{"one empty string", "abc", "", 3},
		{"both empty strings", "", "", 0},
		{"single character difference", "abc", "abd", 1},
		{"multiple differences", "abc", "xyz", 3},
		{"insertion", "abc", "abcd", 1},
		{"deletion", "abcd", "abc", 1},
		{"substitution", "abc", "axc", 1},
		{"transposition", "abc", "acb", 2},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := levenshteinDistance(tt.s1, tt.s2)
			if result != tt.expected {
				t.Errorf("levenshteinDistance() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestJaroSimilarity(t *testing.T) {
	tests := []struct {
		name     string
		s1       string
		s2       string
		expected float64
	}{
		{"identical strings", "abc", "abc", 1.0},
		{"one empty string", "abc", "", 0.0},
		{"both empty strings", "", "", 1.0},
		{"transposition", "MARTHA", "MARHTA", 0.9444444444444445},
		{"different strings", "abc", "xyz", 0.0},
		{"partial match", "DIXON", "DICKSONX", 0.7666666666666666},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := jaroSimilarity(tt.s1, tt.s2)
			if result != tt.expected {
				t.Errorf("jaroSimilarity() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// Benchmark tests to ensure performance is reasonable
func BenchmarkCalculateSimilarity_Simple(b *testing.B) {
	config := DefaultConfig()
	s1 := "ABC Corporation"
	s2 := "ABC Corp"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateSimilarity(s1, s2, config)
	}
}

func BenchmarkCalculateSimilarity_JaroWinkler(b *testing.B) {
	config := NameMatchingConfig()
	s1 := "International Business Machines"
	s2 := "IBM"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateSimilarity(s1, s2, config)
	}
}

func BenchmarkCalculateSimilarity_Hybrid(b *testing.B) {
	config := Config{Algorithm: AlgorithmHybrid}
	s1 := "ABC Corporation"
	s2 := "ABC Corp"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateSimilarity(s1, s2, config)
	}
}
