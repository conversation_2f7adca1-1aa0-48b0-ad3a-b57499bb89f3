package similarity

import "strings"

// Algorithm defines the algorithm to use for similarity calculation
type Algorithm int

const (
	// AlgorithmSimple uses exact/prefix/contains + Levenshtein distance
	AlgorithmSimple Algorithm = iota
	// AlgorithmJaroWinkler uses Jaro-Winkler similarity (better for abbreviations)
	AlgorithmJaroWinkler
	// AlgorithmHybrid uses both algorithms and returns the best score
	AlgorithmHybrid
)

// Config holds configuration options for string similarity calculations
type Config struct {
	// Algorithm determines which similarity algorithm to use
	Algorithm Algorithm
	// CaseSensitive determines if comparison should be case-sensitive
	CaseSensitive bool
	// NormalizeWhitespace removes extra whitespace before comparison
	NormalizeWhitespace bool
	// MinSimilarityThreshold sets the minimum similarity score to return (0.0-1.0)
	MinSimilarityThreshold float64
}

// DefaultConfig returns a sensible default configuration for most use cases
func DefaultConfig() Config {
	return Config{
		Algorithm:              AlgorithmSimple,
		CaseSensitive:          false,
		NormalizeWhitespace:    true,
		MinSimilarityThreshold: 0.5,
	}
}

// NameMatchingConfig returns a configuration optimized for name matching
func NameMatchingConfig() Config {
	return Config{
		Algorithm:              AlgorithmJaroWinkler,
		CaseSensitive:          false,
		NormalizeWhitespace:    true,
		MinSimilarityThreshold: 0.6,
	}
}

// CalculateSimilarity calculates similarity between two strings using the configured algorithm
// Returns a score between 0.0 (no similarity) and 1.0 (exact match)
func CalculateSimilarity(s1, s2 string, config Config) float64 {
	if s1 == s2 {
		return 1.0
	}

	// Normalize strings based on config
	normalized1 := normalizeString(s1, config)
	normalized2 := normalizeString(s2, config)

	if normalized1 == normalized2 {
		return 1.0
	}

	var score float64

	switch config.Algorithm {
	case AlgorithmSimple:
		score = calculateSimpleSimilarity(normalized1, normalized2)
	case AlgorithmJaroWinkler:
		score = calculateJaroWinklerSimilarity(normalized1, normalized2)
	case AlgorithmHybrid:
		simpleScore := calculateSimpleSimilarity(normalized1, normalized2)
		jaroScore := calculateJaroWinklerSimilarity(normalized1, normalized2)
		score = max(simpleScore, jaroScore)
	default:
		score = calculateSimpleSimilarity(normalized1, normalized2)
	}

	// Only return score if it meets the minimum threshold
	if score >= config.MinSimilarityThreshold {
		return score
	}

	return 0.0
}

// normalizeString applies normalization based on the configuration
func normalizeString(s string, config Config) string {
	if !config.CaseSensitive {
		s = strings.ToLower(s)
	}

	if config.NormalizeWhitespace {
		s = strings.TrimSpace(s)
		// Replace multiple spaces with single space
		for strings.Contains(s, "  ") {
			s = strings.ReplaceAll(s, "  ", " ")
		}
	}

	return s
}

// calculateSimpleSimilarity handles exact, prefix, contains, and Levenshtein distance
func calculateSimpleSimilarity(s1, s2 string) float64 {
	// Exact match gets highest priority
	if s1 == s2 {
		return 1.0
	}

	// Prefix match gets high score
	if strings.HasPrefix(s2, s1) {
		return 0.9
	}

	// Contains match gets medium score
	if strings.Contains(s2, s1) {
		return 0.7
	}

	// Fall back to Levenshtein distance for typos
	return calculateLevenshteinSimilarity(s1, s2)
}

// calculateJaroWinklerSimilarity calculates Jaro-Winkler similarity (excellent for abbreviations)
// See: https://en.wikipedia.org/wiki/Jaro%E2%80%93Winkler_distance
func calculateJaroWinklerSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}

	jaro := jaroSimilarity(s1, s2)

	// Winkler modification: give bonus for common prefix
	// Convert to runes for Unicode-safe character comparison
	r1 := []rune(s1)
	r2 := []rune(s2)

	prefixLen := 0
	maxPrefix := min(len(r1), len(r2), 4)
	for i := 0; i < maxPrefix; i++ {
		if r1[i] == r2[i] {
			prefixLen++
		} else {
			break
		}
	}

	return jaro + float64(prefixLen)*0.1*(1.0-jaro)
}

// jaroSimilarity calculates the Jaro similarity score between two strings.
//
// This is a robust, Unicode-safe implementation designed to handle multi-byte
// characters (runes) correctly, which is critical for names and location data.
// Time Complexity: O(N^2) where N is the length of the longer string.
// Space Complexity: O(N) auxiliary space for match tracking.
// See: https://en.wikipedia.org/wiki/Jaro%E2%80%93Winkler_distance
func jaroSimilarity(s1, s2 string) float64 {
	// 1. Convert to runes for correct Unicode character comparison.
	r1 := []rune(s1)
	r2 := []rune(s2)

	if s1 == s2 {
		return 1.0
	}

	len1, len2 := len(r1), len(r2)
	if len1 == 0 || len2 == 0 {
		return 0.0
	}

	// Calculate the match window size: floor(max(len1, len2) / 2) - 1
	matchWindow := max(len1, len2)/2 - 1
	if matchWindow < 0 {
		matchWindow = 0
	}

	r1Matches := make([]bool, len1)
	r2Matches := make([]bool, len2)

	matches := 0

	// 2. Find matches
	for i := 0; i < len1; i++ {
		// Define the search window in r2
		start := max(0, i-matchWindow)
		end := min(i+matchWindow+1, len2)

		for j := start; j < end; j++ {
			// Check if r2[j] is already matched OR characters don't match
			if r2Matches[j] || r1[i] != r2[j] {
				continue
			}

			// Record the match
			r1Matches[i] = true
			r2Matches[j] = true
			matches++
			break
		}
	}

	if matches == 0 {
		return 0.0
	}

	// 3. Count transpositions
	transpositions := 0
	k := 0 // Pointer for r2's matches

	for i := 0; i < len1; i++ {
		if !r1Matches[i] {
			continue
		}

		// Advance k to the next *matched* character in r2
		for !r2Matches[k] {
			k++
		}

		// If the sequence of matched characters differs, it's a transposition
		if r1[i] != r2[k] {
			transpositions++
		}
		k++
	}

	// Jaro similarity formula: J = 1/3 * (m/|r1| + m/|r2| + (m - t/2)/m)
	// Transpositions count should be divided by 2.
	transpositions /= 2

	return (float64(matches)/float64(len1) +
		float64(matches)/float64(len2) +
		(float64(matches)-float64(transpositions))/float64(matches)) / 3.0
}

// calculateLevenshteinSimilarity calculates similarity based on edit distance
// See: https://en.wikipedia.org/wiki/Levenshtein_distance
func calculateLevenshteinSimilarity(s1, s2 string) float64 {
	distance := levenshteinDistance(s1, s2)
	// Use rune length for Unicode-safe character counting
	r1 := []rune(s1)
	r2 := []rune(s2)
	maxLen := max(len(r1), len(r2))
	if maxLen == 0 {
		return 1.0
	}

	return 1.0 - float64(distance)/float64(maxLen)
}

// levenshteinDistance calculates the minimum edit distance between two strings
// using an optimized version of the Wagner-Fischer algorithm.
//
// Key Optimizations:
// - Unicode-Safe: Converts strings to runes to handle all multi-byte characters correctly.
// - Space-Efficient: Uses O(min(m, n)) space complexity, adapting the calculation to the shorter string.
// - Memory Efficient: Avoids repeated array allocation by swapping the 'prev' and 'curr' rows.
// See: https://en.wikipedia.org/wiki/Wagner%E2%80%93Fischer_algorithm
func levenshteinDistance(s1, s2 string) int {

	if s1 == s2 {
		return 0
	}

	r1 := []rune(s1)
	r2 := []rune(s2)

	if len(r1) == 0 {
		return len(r2)
	}
	if len(r2) == 0 {
		return len(r1)
	}

	// Ensure we always use the shorter string for the array dimension to achieve O(min(m,n)) space.
	// After swapping, r1 is the shorter string, r2 is the longer string.
	if len(r1) > len(r2) {
		r1, r2 = r2, r1
	}

	// Array size based on shorter string (r1) length + 1
	prev := make([]int, len(r1)+1)
	curr := make([]int, len(r1)+1)

	// Initialize first row with distances from empty string to r1 prefixes
	for j := 0; j <= len(r1); j++ {
		prev[j] = j
	}

	// Outer loop: iterate over longer string (r2)
	for i := 1; i <= len(r2); i++ {
		curr[0] = i // Distance of r2 prefix (length i) to empty string is i deletions

		// Inner loop: iterate over shorter string (r1)
		for j := 1; j <= len(r1); j++ {
			cost := 0
			if r2[i-1] != r1[j-1] {
				cost = 1 // Substitution cost
			}

			// Wagner-Fischer recurrence: min of (Deletion, Insertion, Substitution/Match)
			curr[j] = min(prev[j]+1, curr[j-1]+1, prev[j-1]+cost)
		}
		// Swap arrays for the next iteration (prev becomes the current row)
		prev, curr = curr, prev
	}

	// The result is in the last cell of the final 'prev' row.
	return prev[len(r1)]
}
