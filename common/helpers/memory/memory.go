package memory

import (
	"context"
	"errors"
	"fmt"
	"os"
	"runtime/debug"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// SetGoMemoryLimit reads GOMEMLIMIT and applies a Go runtime soft memory limit using runtime/debug.
// Supports values like "750MiB", "1GiB", numeric bytes (e.g. "134217728")
func SetGoMemoryLimit(ctx context.Context) {
	goMemLimit := os.Getenv("GOMEMLIMIT")
	if goMemLimit == "" {
		log.WarnNoSentry(ctx, "GOMEMLIMIT env var not set; Go will use default memory limit")
		return
	}

	limitStr := strings.TrimSpace(goMemLimit)

	limitBytes, err := parseGOMEMLIMITToBytes(limitStr)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to parse GOMEMLIMIT; keeping runtime default",
			zap.String("gomemlimit", goMemLimit),
			zap.Error(err),
		)
		return
	}

	prev := debug.SetMemoryLimit(limitBytes)
	log.Info(
		ctx,
		"applied Go memory soft limit",
		zap.String("gomemlimit", limitStr),
		zap.Int64("limit_bytes", limitBytes),
		zap.String("limit_human", bytesToHumanReadable(limitBytes)),
		zap.Int64("previous_limit_bytes", prev),
	)
}

// parseGOMEMLIMITToBytes parses a GOMEMLIMIT-style size string into bytes.
// Supports plain bytes (e.g. "134217728"), binary suffixes (KiB, MiB, GiB, TiB),
// and common shorthand variants (k, kb, kib, m, mb, mib, g, gb, gib, t, tb, tib).
func parseGOMEMLIMITToBytes(input string) (int64, error) {
	s := strings.TrimSpace(strings.ToLower(input))
	if s == "" {
		return 0, errors.New("empty memory limit")
	}

	// If numeric only, interpret as bytes
	isDigitOrDot := func(r byte) bool { return (r >= '0' && r <= '9') || r == '.' }
	i := 0
	for i < len(s) && isDigitOrDot(s[i]) {
		i++
	}

	numStr := strings.TrimSpace(s[:i])
	unit := strings.TrimSpace(s[i:])
	if numStr == "" {
		return 0, fmt.Errorf("invalid memory limit: %q", input)
	}

	// Allow trailing 'b'
	if unit == "b" {
		unit = ""
	}

	// Normalize unit
	switch unit {
	case "", "b":
		// bytes
	case "k", "kb", "kib":
		unit = "kib"
	case "m", "mb", "mib":
		unit = "mib"
	case "g", "gb", "gib":
		unit = "gib"
	case "t", "tb", "tib":
		unit = "tib"
	default:
		// Also handle forms like "gi", "mi", etc.
		if strings.HasSuffix(unit, "i") && len(unit) == 2 {
			unit += "b"
		}
		switch unit {
		case "ki", "kib":
			unit = "kib"
		case "mi", "mib":
			unit = "mib"
		case "gi", "gib":
			unit = "gib"
		case "ti", "tib":
			unit = "tib"
		default:
			return 0, fmt.Errorf("unsupported unit in memory limit: %q", input)
		}
	}

	// Parse number (allow decimals for convenience like 1.5GiB)
	val, err := strconv.ParseFloat(numStr, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid numeric value in memory limit %q: %w", input, err)
	}

	const (
		kib = 1024
		mib = 1024 * kib
		gib = 1024 * mib
		tib = 1024 * gib
	)

	var multiplier float64 = 1
	switch unit {
	case "":
		multiplier = 1
	case "kib":
		multiplier = kib
	case "mib":
		multiplier = mib
	case "gib":
		multiplier = gib
	case "tib":
		multiplier = tib
	}

	// Round to nearest byte
	bytes := int64(val*multiplier + 0.5)
	if bytes < 0 {
		return 0, fmt.Errorf("memory limit must be non-negative: %q", input)
	}
	return bytes, nil
}

// bytesToHumanReadable converts bytes to a human readable string.
func bytesToHumanReadable(b int64) string {
	const (
		kib = 1024
		mib = 1024 * kib
		gib = 1024 * mib
		tib = 1024 * gib
	)

	switch {
	case b >= tib:
		v := float64(b) / tib
		return trimTrailingZeros(fmt.Sprintf("%.2f TiB", v))
	case b >= gib:
		v := float64(b) / gib
		return trimTrailingZeros(fmt.Sprintf("%.2f GiB", v))
	case b >= mib:
		v := float64(b) / mib
		return trimTrailingZeros(fmt.Sprintf("%.2f MiB", v))
	case b >= kib:
		v := float64(b) / kib
		return trimTrailingZeros(fmt.Sprintf("%.2f KiB", v))
	default:
		return fmt.Sprintf("%d B", b)
	}
}

func trimTrailingZeros(s string) string {
	if strings.Contains(s, ".") {
		s = strings.TrimRight(s, "0")
		s = strings.TrimRight(s, ".")
	}
	return s
}
