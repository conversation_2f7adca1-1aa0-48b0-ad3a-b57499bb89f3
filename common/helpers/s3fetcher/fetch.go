package s3fetcher

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

// Fetcher retrieves objects from an S3 bucket
type Fetcher interface {
	FetchObjectFromS3(ctx context.Context, s3URL string) ([]byte, error)
	DownloadObjectFromS3ToDirectory(ctx context.Context, s3URL string, dirPath string) (string, error)
}

type S3API interface {
	GetObject(ctx context.Context, params *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error)
}

// S3Client satisfies the Fetcher interface
type S3Client struct {
	manager       S3API
	localCacheDir string
}

func New(ctx context.Context) (Fetcher, error) {
	// Check for LocalStack endpoint
	endpointURL := os.Getenv("AWS_ENDPOINT_URL")

	var cfg aws.Config
	var err error

	if endpointURL != "" && os.Getenv("APP_ENV") == "dev" {
		log.Info(ctx, "using LocalStack S3 configuration", zap.String("endpoint", endpointURL))
		cfg, err = config.LoadDefaultConfig(ctx,
			config.WithRegion("us-east-1"),
			config.WithRetryMaxAttempts(7),
		)
		if err != nil {
			return nil, err
		}

		s3Client := s3.NewFromConfig(cfg, func(o *s3.Options) {
			o.UsePathStyle = true
			o.BaseEndpoint = &endpointURL
		})

		return &S3Client{manager: s3Client, localCacheDir: ""}, nil
	}

	// Use default AWS configuration
	cfg, err = config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"), config.WithRetryMaxAttempts(7))
	if err != nil {
		return nil, err
	}

	return &S3Client{manager: s3.NewFromConfig(cfg), localCacheDir: ""}, nil

}

// NewWithLocalCache creates a new S3Client with local caching enabled
func NewWithLocalCache(ctx context.Context, cacheDir string) (Fetcher, error) {
	client, err := New(ctx)
	if err != nil {
		return nil, err
	}

	// It's safe to cast here because we know `New` returns an `*S3Client`.
	s3Client := client.(*S3Client)
	s3Client.localCacheDir = cacheDir

	return s3Client, nil
}

func (client *S3Client) FetchObjectFromS3(ctx context.Context, s3URL string) ([]byte, error) {
	ctx, span := otel.StartSpan(ctx, "s3fetcher.FetchObjectFromS3", nil)
	var err error
	defer span.End(err)

	var bucketName, key string
	bucketName, key, err = ParseS3URL(s3URL)
	if err != nil {
		err = fmt.Errorf("error parsing S3 URL: %w", err)
		return nil, err
	}

	// If a local cache directory is configured, check for the file there first.
	if client.localCacheDir != "" {
		filename := filepath.Base(key)
		filePath := filepath.Join(client.localCacheDir, filename)
		if _, err := os.Stat(filePath); err == nil {
			log.Info(
				ctx,
				"file already exists on disk, reading from disk",
				zap.String("objectKey", key),
				zap.String("filePath", filePath),
			)
			return os.ReadFile(filePath)
		}
	}

	input := &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &key,
	}

	result, err := client.manager.GetObject(ctx, input)
	if err != nil {
		err = fmt.Errorf("failed to download s3://%s/%s: %w", bucketName, key, err)
		return nil, err
	}

	if result.Body == nil {
		err = fmt.Errorf("S3 object body is nil for s3://%s/%s", bucketName, key)
		return nil, err
	}

	defer result.Body.Close()

	body, err := io.ReadAll(result.Body)
	if err != nil {
		err = fmt.Errorf("failed to read S3 object body: %w", err)
		return nil, err
	}

	if len(body) == 0 {
		err = errors.New("received empty file from S3")
		return nil, err
	}

	// If a local cache directory is configured, save the file for next time.
	if client.localCacheDir != "" {
		filename := filepath.Base(key)
		filePath := filepath.Join(client.localCacheDir, filename)
		// Create the directory if it doesn't exist.
		if err := os.MkdirAll(client.localCacheDir, 0755); err != nil {
			log.Error(
				ctx,
				"failed to create directory for caching",
				zap.String("path", client.localCacheDir),
				zap.Error(err),
			)
		} else {
			if err := os.WriteFile(filePath, body, 0600); err != nil {
				log.Error(ctx, "failed to cache file to disk", zap.String("path", filePath), zap.Error(err))
			} else {
				log.Info(ctx, "cached file to disk", zap.String("path", filePath))
			}
		}
	}

	fileDataSizeInBytes := len(body)
	log.Info(
		ctx,
		"fetched file from S3",
		zap.Float64("fileSizeKb", float64(fileDataSizeInBytes)/1024),
		zap.String("objectKey", key),
	)

	return body, nil
}

func (client *S3Client) DownloadObjectFromS3ToDirectory(
	ctx context.Context,
	s3URL string,
	dirPath string,
) (string, error) {

	ctx, span := otel.StartSpan(ctx, "s3fetcher.DownloadObjectFromS3ToDirectory", nil)
	var err error
	defer span.End(err)

	var bucketName, key string
	bucketName, key, err = ParseS3URL(s3URL)
	if err != nil {
		err = fmt.Errorf("error parsing S3 URL: %w", err)
		return "", err
	}

	filename := filepath.Base(key)
	filePath := filepath.Join(dirPath, filename)

	// Check if the file already exists on disk.
	if _, err := os.Stat(filePath); err == nil {
		log.Info(
			ctx,
			"file already exists on disk, skipping S3 download",
			zap.String("objectKey", key),
			zap.String("filePath", filePath),
		)
		return filePath, nil
	}

	// Check if directory exists, create it if it doesn't
	if _, err = os.Stat(dirPath); os.IsNotExist(err) {
		err = os.MkdirAll(dirPath, 0755)
		if err != nil {
			err = fmt.Errorf("failed to create directory %s: %w", dirPath, err)
			return "", err
		}
	}

	input := &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &key,
	}

	result, err := client.manager.GetObject(ctx, input)
	if err != nil {
		err = fmt.Errorf("failed to download s3://%s/%s: %w", bucketName, key, err)
		return "", err
	}

	if result.Body == nil {
		err = fmt.Errorf("S3 object body is nil for s3://%s/%s", bucketName, key)
		return "", err
	}

	defer result.Body.Close()

	file, err := os.Create(filePath)
	if err != nil {
		err = fmt.Errorf("failed to create file %s: %w", filePath, err)
		return "", err
	}

	var copyErr error
	defer func() {
		file.Close()
		if copyErr != nil {
			os.Remove(filePath)
		}
	}()

	var bytesWritten int64
	bytesWritten, copyErr = io.Copy(file, result.Body)
	if copyErr != nil {
		err = fmt.Errorf("failed to write S3 object to file: %w", copyErr)
		return "", err
	}

	if bytesWritten == 0 {
		err = errors.New("received empty file from S3")
		return "", err
	}

	log.Info(
		ctx,
		"downloaded file from S3 to directory",
		zap.Float64("fileSizeKb", float64(bytesWritten)/1024),
		zap.String("objectKey", key),
		zap.String("filePath", filePath),
	)

	return filePath, nil
}
