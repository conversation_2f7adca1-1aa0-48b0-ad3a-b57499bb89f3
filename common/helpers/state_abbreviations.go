package helpers

import (
	"context"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// Source maps for states and provinces. These are the single source of truth.
var (
	usaStateAbbreviations = map[string]string{
		"AL": "Alabama", "AK": "Alaska", "AZ": "Arizona", "AR": "Arkansas", "CA": "California",
		"CO": "Colorado", "CT": "Connecticut", "DE": "Delaware", "DC": "District of Columbia", "FL": "Florida",
		"GA": "Georgia", "HI": "Hawaii", "ID": "Idaho", "IL": "Illinois", "IN": "Indiana", "IA": "Iowa",
		"KS": "Kansas", "KY": "Kentucky", "LA": "Louisiana", "ME": "Maine", "MD": "Maryland",
		"MA": "Massachusetts", "MI": "Michigan", "MN": "Minnesota", "MS": "Mississippi", "MO": "Missouri",
		"MT": "Montana", "NE": "Nebraska", "NV": "Nevada", "NH": "New Hampshire", "NJ": "New Jersey",
		"NM": "New Mexico", "NY": "New York", "NC": "North Carolina", "ND": "North Dakota", "OH": "Ohio",
		"OK": "Oklahoma", "OR": "Oregon", "PA": "Pennsylvania", "RI": "Rhode Island", "SC": "South Carolina",
		"SD": "South Dakota", "TN": "Tennessee", "TX": "Texas", "UT": "Utah", "VT": "Vermont",
		"VA": "Virginia", "WA": "Washington", "WV": "West Virginia", "WI": "Wisconsin", "WY": "Wyoming",
		// US Territories
		"AS": "American Samoa", "GU": "Guam", "MP": "Northern Mariana Islands", "PR": "Puerto Rico",
		"VI": "U.S. Virgin Islands",
	}

	canadaProvinceAbbreviations = map[string]string{
		"AB": "Alberta", "BC": "British Columbia", "MB": "Manitoba", "NB": "New Brunswick",
		"NL": "Newfoundland and Labrador", "NT": "Northwest Territories", "NS": "Nova Scotia",
		"NU": "Nunavut", "ON": "Ontario", "PE": "Prince Edward Island", "QC": "Quebec",
		"SK": "Saskatchewan", "YT": "Yukon",
	}
)

var (
	abbreviationsToFullNames = make(map[string]string)
	fullNamesToAbbreviations = make(map[string]string)
)

func init() {
	for abbr, name := range usaStateAbbreviations {
		abbreviationsToFullNames[abbr] = name
		fullNamesToAbbreviations[strings.ToLower(name)] = abbr
	}
	for abbr, name := range canadaProvinceAbbreviations {
		abbreviationsToFullNames[abbr] = name
		fullNamesToAbbreviations[strings.ToLower(name)] = abbr
	}
}

// GetStateFullName maps a state/province abbreviation to its full name. It supports both USA and Canada.
func GetStateFullName(ctx context.Context, abbreviation string) string {
	abbreviation = strings.ToUpper(abbreviation)
	if fullName, exists := abbreviationsToFullNames[abbreviation]; exists {
		return fullName
	}
	log.Warn(ctx, "Unknown state abbreviation", zap.String("abbreviation", abbreviation))
	return "Unknown State"
}

// GetStateAbbreviation maps a full state/province name to its abbreviation. It supports both USA and Canada.
func GetStateAbbreviation(ctx context.Context, fullName string) string {
	fullName = strings.ToLower(fullName)
	if abbreviation, exists := fullNamesToAbbreviations[fullName]; exists {
		return abbreviation
	}

	log.Warn(ctx, "Unknown state name", zap.String("fullName", fullName))
	return "Unknown State"
}
