package helpers

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	goredis "github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/redis"
)

// TestCheckRateLimit covers the main scenarios for helpers.CheckRateLimit using a mock Redis client.
func TestCheckRateLimit(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		config        RateLimitConfig
		setupMock     func(redismock.ClientMock)
		envOverride   string // if non-empty, set DISABLE_RATE_LIMIT to this value
		expectedError string
	}{
		{
			name: "successful rate limit check",
			config: RateLimitConfig{
				MaxRequests:    5,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "tenant",
			},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectIncr("test-tenant-rate-limiter").SetVal(1)
				mock.ExpectExpire("test-tenant-rate-limiter", 1*time.Minute).SetVal(true)
			},
		},
		{
			name: "rate limit exceeded",
			config: RateLimitConfig{
				MaxRequests:    2,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "tenant",
			},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectIncr("test-tenant-rate-limiter").SetVal(3)
				mock.ExpectTTL("test-tenant-rate-limiter").SetVal(30 * time.Second)
			},
			expectedError: "test rate limit exceeded (max 2 requests per 1m0s, retry after 30s)",
		},
		{
			name: "redis connection failure",
			config: RateLimitConfig{
				MaxRequests:    10,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "tenant",
			},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectIncr("test-tenant-rate-limiter").SetErr(errors.New("connection failed"))
			},
			expectedError: "failed to increment key test-tenant-rate-limiter: connection failed",
		},
		{
			name: "redis not initialized",
			config: RateLimitConfig{
				MaxRequests:    10,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "tenant",
			},
			setupMock:     func(_ redismock.ClientMock) {},
			expectedError: "failed to connect to Redis",
		},
		{
			name: "rate limiting disabled via environment",
			config: RateLimitConfig{
				MaxRequests:    10,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "tenant",
			},
			setupMock:   func(_ redismock.ClientMock) {},
			envOverride: "true",
		},
		{
			name: "empty tenant",
			config: RateLimitConfig{
				MaxRequests:    5,
				TimeWindow:     1 * time.Minute,
				RedisKeyPrefix: "test",
				Tenant:         "",
			},
			setupMock: func(mock redismock.ClientMock) {
				mock.ExpectIncr("test--rate-limiter").SetVal(1)
				mock.ExpectExpire("test--rate-limiter", 1*time.Minute).SetVal(true)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Store and restore original Redis client
			originalRDB := redis.RDB
			defer func() { redis.RDB = originalRDB }()

			// Handle environment variable
			originalEnv := os.Getenv("DISABLE_RATE_LIMIT")
			defer func() { os.Setenv("DISABLE_RATE_LIMIT", originalEnv) }()

			if tt.envOverride != "" {
				os.Setenv("DISABLE_RATE_LIMIT", tt.envOverride)
			} else {
				// Explicitly clear the environment variable for tests that expect Redis calls
				os.Unsetenv("DISABLE_RATE_LIMIT")
			}

			var mock redismock.ClientMock
			var mockRDB *goredis.Client

			// Set up mock for all cases except "redis not initialized" and when rate limiting is disabled
			if tt.name != "redis not initialized" && tt.envOverride == "" {
				mockRDB, mock = redismock.NewClientMock()
				redis.RDB = mockRDB
				// Set up mock expectations
				tt.setupMock(mock)
			} else {
				// For cases where rate limiting is disabled, we don't need a mock
				redis.RDB = nil
			}

			err := CheckRateLimit(ctx, tt.config)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			// Only check mock expectations if we set up a mock
			if tt.name != "redis not initialized" && tt.envOverride == "" {
				assert.NoError(t, mock.ExpectationsWereMet())
			}
		})
	}
}

// TestCheckRateLimitEnvironmentVariable tests the environment variable handling specifically
func TestCheckRateLimitEnvironmentVariable(t *testing.T) {
	ctx := context.Background()

	// Store and restore original Redis client and environment
	originalRDB := redis.RDB
	defer func() { redis.RDB = originalRDB }()

	originalEnv := os.Getenv("DISABLE_RATE_LIMIT")
	defer func() { os.Setenv("DISABLE_RATE_LIMIT", originalEnv) }()

	config := RateLimitConfig{
		MaxRequests:    5,
		TimeWindow:     1 * time.Minute,
		RedisKeyPrefix: "test",
		Tenant:         "tenant",
	}

	// Test with DISABLE_RATE_LIMIT=true
	os.Setenv("DISABLE_RATE_LIMIT", "true")
	err := CheckRateLimit(ctx, config)
	assert.NoError(t, err, "Should return nil when rate limiting is disabled")

	// Test with DISABLE_RATE_LIMIT unset
	os.Unsetenv("DISABLE_RATE_LIMIT")

	// Set up mock for Redis calls
	mockRDB, mock := redismock.NewClientMock()
	redis.RDB = mockRDB
	mock.ExpectIncr("test-tenant-rate-limiter").SetVal(1)
	mock.ExpectExpire("test-tenant-rate-limiter", 1*time.Minute).SetVal(true)

	err = CheckRateLimit(ctx, config)
	assert.NoError(t, err, "Should work when rate limiting is enabled")
	assert.NoError(t, mock.ExpectationsWereMet(), "Should call Redis functions when rate limiting is enabled")
}
