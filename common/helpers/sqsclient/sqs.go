package sqsclient

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
)

// Interface for sending messages to SQS
type API interface {
	SendMessage(context.Context, *sqs.SendMessageInput, ...func(*sqs.Options)) (*sqs.SendMessageOutput, error)
}

func New(env string) (API, error) {
	if env == "dev" {
		return &LocalSQSClient{}, nil
	}

	if env == "test" || env == "mock" {
		return &MockSQSClient{}, nil
	}

	cfg, err := config.LoadDefaultConfig(context.Background(), config.WithRegion("us-east-1"))
	if err != nil {
		return nil, err
	}

	return sqs.NewFromConfig(cfg), nil
}

// LocalSQSClient is a client for sending messages to a local service instead of SQS via HTTP
type LocalSQSClient struct{}

func (c *LocalSQSClient) SendMessage(
	ctx context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	// POST to service running locally on a different port
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		*input.QueueUrl,
		strings.NewReader(aws.ToString(input.MessageBody)),
	)
	if err != nil {
		return nil, fmt.Errorf("unable to create local POST request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// For Outlook Ingestion -> Processor, Microsoft Graph will retry the webhook request if it doesn't receive a
	// 2XX response within 10 seconds. To avoid spam, particularly for emails that take longer to process like load
	// building & truck lists, we asynchronously send the request to processor. https://tinyurl.com/mrxapsma
	go sentry.WithHub(ctx, func(ctx context.Context) {
		asyncCtx := log.InheritContext(ctx, context.Background())
		asyncCtx, cancel := context.WithTimeout(asyncCtx, 5*time.Minute)
		defer cancel()

		resp, err := otel.TracingHTTPClient().Do(req)
		if err != nil {
			log.Error(asyncCtx, "failed to send POST request to "+req.URL.String(), zap.Error(err))
			return
		}
		defer resp.Body.Close()

		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			log.WarnNoSentry(asyncCtx, "failed to read response body", zap.Error(err))
		}

		if resp.StatusCode >= http.StatusBadRequest {
			log.Errorf(
				asyncCtx,
				"POST %s returned %d",
				req.URL.String(),
				resp.StatusCode,
				zap.ByteString("responseBody", respBody),
			)
		}
	})

	return &sqs.SendMessageOutput{}, nil
}

// MockSQSClient is a mock implementation of the SQSAPI interface for unit testing
type MockSQSClient struct {
	SentMessages []string
}

func (c *MockSQSClient) SendMessage(
	_ context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	c.SentMessages = append(c.SentMessages, aws.ToString(input.MessageBody))
	return &sqs.SendMessageOutput{}, nil
}
