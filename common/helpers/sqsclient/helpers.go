package sqsclient

import (
	"context"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// CaptureSQSMetadata captures metadata from an SQS record for analytics and logging
func CaptureSQSMetadata(
	ctx context.Context,
	start time.Time,
	record events.SQSMessage,
	msg *models.IngestedEmail,
) context.Context {
	// Add metadata to context for logging
	ctx = log.With(ctx,
		zap.String("sqsEventId", record.MessageId),
		zap.String("sqsApproximateReceiveCount", record.Attributes["ApproximateReceiveCount"]),
	)

	if msg == nil {
		return ctx
	}

	msg.SQSEventID = record.MessageId
	msg.ProcessingStartTime = start

	if lc, ok := lambdacontext.FromContext(ctx); ok {
		msg.LambdaRequestID = lc.AwsRequestID
	}

	if ts, ok := record.Attributes["ApproximateFirstReceiveTimestamp"]; ok {
		if unixMs, err := strconv.ParseInt(ts, 10, 64); err == nil {
			timestamp := time.Unix(0, unixMs*int64(time.Millisecond))
			msg.SQSApproximateFirstReceiveTimestamp = timestamp
			ctx = log.With(ctx, zap.Time("sqsApproximateFirstReceiveTimestamp", timestamp))
		}
	}

	if ts, ok := record.Attributes["SentTimestamp"]; ok {
		if unixMs, err := strconv.ParseInt(ts, 10, 64); err == nil {
			timestamp := time.Unix(0, unixMs*int64(time.Millisecond))
			msg.SQSSentTimestamp = timestamp
		}
	}

	if count, ok := record.Attributes["ApproximateReceiveCount"]; ok {
		if receiveCount, err := strconv.Atoi(count); err == nil {
			msg.SQSApproximateReceiveCount = receiveCount
		}
	}

	return ctx
}

// This is usually just one record, but in case of a batch, it's a slice
func SanitizeSQSRecords(event *events.SQSEvent) []map[string]any {
	sanitizedRecords := make([]map[string]any, 0, len(event.Records))

	for _, record := range event.Records {
		sanitizedRecord := map[string]any{
			"messageId":              record.MessageId,
			"receiptHandle":          record.ReceiptHandle,
			"md5OfBody":              record.Md5OfBody,
			"md5OfMessageAttributes": record.Md5OfMessageAttributes,
			"attributes":             record.Attributes,
			"messageAttributes":      record.MessageAttributes,
			"eventSourceARN":         record.EventSourceARN,
			"eventSource":            record.EventSource,
			"awsRegion":              record.AWSRegion,
		}

		sanitizedRecords = append(sanitizedRecords, sanitizedRecord)
	}

	return sanitizedRecords
}
