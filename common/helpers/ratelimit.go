package helpers

import (
	"context"
	"errors"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

type RateLimitConfig struct {
	MaxRequests    int           // Maximum requests allowed during time window
	TimeWindow     time.Duration // Duration of the time window
	RedisKeyPrefix string        // Prefix used to identify integration in Redis
	Tenant         string        // Optional; Tenant identifier for integration
}

func CheckRateLimit(ctx context.Context, config RateLimitConfig) error {
	// Check if rate limiting is disabled via environment variable
	if disableRateLimit := os.Getenv("DISABLE_RATE_LIMIT"); disableRateLimit == "true" {
		return nil
	}

	return checkRateLimit(ctx, config)
}

func checkRateLimit(ctx context.Context, config RateLimitConfig) error {
	if redis.RDB == nil {
		return errors.New("failed to connect to Redis")
	}

	key := fmt.Sprintf("%s-%s-rate-limiter", config.RedisKeyPrefix, config.Tenant)

	// Increment counter sets expiry if, and only if, it's the first increment
	currentRequestCount, err := redis.IncrementKey(ctx, key, config.TimeWindow)
	if err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to increment rate limiter in Redis",
			zap.String("prefix", config.RedisKeyPrefix),
			zap.String("tenant", config.Tenant),
			zap.Error(err),
		)

		return err
	}

	if currentRequestCount > int64(config.MaxRequests) {

		remainingTime, err := redis.GetKeyExpiry(ctx, key)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"failed to get key expiry in Redis",
				zap.String("prefix", config.RedisKeyPrefix),
				zap.String("tenant", config.Tenant),
				zap.Error(err),
			)
		}

		return fmt.Errorf(
			"%s rate limit exceeded (max %d requests per %v, retry after %v)",
			config.RedisKeyPrefix,
			config.MaxRequests,
			config.TimeWindow,
			remainingTime,
		)
	}

	return nil
}
