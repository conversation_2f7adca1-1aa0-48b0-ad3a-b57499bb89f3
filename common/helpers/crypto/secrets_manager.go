package crypto

import (
	"context"
	"errors"
	"os"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

const (
// TODO: placeholder; update with aws addresses below and add logic to determine which key to sign with
// prodEmailSecretArn        = ""
// prodIntegrationsSecretArn = ""
)

// AES encryption key lazy-loaded from secrets manager
var AESKey []byte

// Dummy value for dev environment
var defaultAESKey []byte

func getKeyFromSecretsManager(ctx context.Context) (*[]byte, error) {
	if len(defaultAESKey) > 0 {
		return &defaultAESKey, nil
	}

	env := os.Getenv("APP_ENV")
	forceAESKey := os.Getenv("FORCE_GEN_AES_KEY")

	if env == "dev" || forceAESKey == "true" {
		// hardcode test key for local development
		log.Info(ctx, "using hardcoded AES key for environment", zap.String("APP_ENV", env))

		defaultAESKey = []byte("abcdefghijklmnopqrstuvwxyz123456")
		return &defaultAESKey, nil
	}

	if AESKey == nil {
		log.Error(ctx, "AES key is empty")
		return &[]byte{}, errors.New("AES key is empty")
	}

	return &AESKey, nil
}
