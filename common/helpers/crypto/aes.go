package crypto

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
)

func EncryptTokens(
	ctx context.Context,
	accessToken,
	refreshToken string,
	key []byte,
) (encryptedAccessToken string, encryptedRefreshToken string, err error) {

	encryptedAccessToken, err = EncryptAESGCM(ctx, accessToken, &key)
	if err != nil {
		err = fmt.Errorf("failed to encrypt access token: %w", err)
		return
	}

	encryptedRefreshToken, err = EncryptAESGCM(ctx, refreshToken, &key)

	return
}

// EncryptAESGCM uses AES-256 in Galois/Counter Mode (GCM), which provides both confidentiality and integrity.
//
// The encryption key must be 32 bytes. The resulting ciphertext includes a random nonce and will be base64-encoded.
func EncryptAESGCM(ctx context.Context, plaintext string, key *[]byte) (string, error) {
	gcm, err := newGCM(ctx, key)
	if err != nil {
		return "", err
	}

	// Generate a random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("error reading from crypto/rand: %w", err)
	}

	cipherBytes := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// Base64 encoding is more space-efficient than hex encoding
	return base64.StdEncoding.EncodeToString(cipherBytes), nil
}

// DecryptAESGCM decrypts base64-encoded ciphertext from EncryptAESGCM.
func DecryptAESGCM(ctx context.Context, ciphertext string, key *[]byte) (string, error) {
	gcm, err := newGCM(ctx, key)
	if err != nil {
		return "", err
	}

	cipherBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("base64 decoding failed: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(cipherBytes) <= nonceSize {
		return "", fmt.Errorf("ciphertext is too short (%d bytes)", len(cipherBytes))
	}

	plaintext, err := gcm.Open(nil, cipherBytes[:nonceSize], cipherBytes[nonceSize:], nil)
	if err != nil {
		return "", fmt.Errorf("decryption failed: %w", err)
	}

	return string(plaintext), nil
}

func newGCM(ctx context.Context, key *[]byte) (cipher.AEAD, error) {
	// Load key from AWS secrets manager if necessary
	if key == nil || len(*key) == 0 {
		var err error
		if key, err = getKeyFromSecretsManager(ctx); err != nil {
			return nil, err
		}
	}

	// 32-byte key = AES-256: don't allow weaker algorithms
	if len(*key) != 32 {
		return nil, fmt.Errorf("key must be 32 bytes, found %d", len(*key))
	}

	block, err := aes.NewCipher(*key)
	if err != nil {
		return nil, fmt.Errorf("aes.NewCipher failed: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("cipher.NewGCM failed: %w", err)
	}

	return gcm, nil
}
