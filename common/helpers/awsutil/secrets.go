package awsutil

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// ReadSecretJSON reads the given secret from AWS secrets manager and j<PERSON> unmarshals into out
func ReadSecretJSON(ctx context.Context, secretID string, out any) error {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return err
	}

	client := secretsmanager.NewFromConfig(cfg)
	result, err := client.GetSecretValue(ctx, &secretsmanager.GetSecretValueInput{SecretId: &secretID})
	if err != nil {
		return fmt.Errorf("failed to get secret %s: %w", secretID, err)
	}

	if err := json.Unmarshal([]byte(aws.ToString(result.SecretString)), out); err != nil {
		return fmt.Errorf("JSON unmarshal failed for secret %s: %w", secretID, err)
	}

	log.Info(ctx, "loaded secrets", zap.String("secretId", secretID))
	return nil
}
