[{"city": "7911429", "state": "VA", "country": ""}, {"city": "Abbotsford", "state": "BC", "country": ""}, {"city": "Aberdeen", "state": "WA", "country": ""}, {"city": "Abingdon", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "AB", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "AB", "country": "CANADA"}, {"city": "<PERSON><PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Adairsville", "state": "GA", "country": ""}, {"city": "Advance", "state": "NC", "country": ""}, {"city": "Agawam", "state": "MA", "country": ""}, {"city": "Aguascalientes", "state": "AG", "country": ""}, {"city": "<PERSON><PERSON>", "state": "AB", "country": ""}, {"city": "Airdrie", "state": "AB", "country": ""}, {"city": "Airdrie", "state": "AB", "country": "CANADA"}, {"city": "Ajax", "state": "ON", "country": ""}, {"city": "Akron", "state": "OH", "country": "United States"}, {"city": "Albany", "state": "OR", "country": ""}, {"city": "Albany(Colonie)", "state": "NY", "country": ""}, {"city": "Albertville", "state": "MN", "country": ""}, {"city": "Albion", "state": "IN", "country": "United States"}, {"city": "Albion", "state": "NY", "country": ""}, {"city": "Albuquerque", "state": "NM", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Alexandria", "state": "LA", "country": ""}, {"city": "Allentown", "state": "PA", "country": ""}, {"city": "Allentown,", "state": "PA", "country": ""}, {"city": "Allentown, Pa", "state": "PA", "country": ""}, {"city": "Altamont", "state": "NY", "country": ""}, {"city": "Altoona", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": ""}, {"city": "Amarillo", "state": "TX", "country": ""}, {"city": "Amarillo", "state": "TX", "country": "United States"}, {"city": "American Canyon", "state": "CA", "country": ""}, {"city": "American Falls", "state": "ID", "country": ""}, {"city": "American Fork", "state": "UT", "country": ""}, {"city": "Americus", "state": "GA", "country": ""}, {"city": "Ames", "state": "IA", "country": ""}, {"city": "Amherst", "state": "NH", "country": ""}, {"city": "Amherst", "state": "NS", "country": ""}, {"city": "Amherst", "state": "NY", "country": ""}, {"city": "Amityville", "state": "NY", "country": ""}, {"city": "Amsterdam", "state": "NY", "country": ""}, {"city": "Anaheim", "state": "CA", "country": ""}, {"city": "Anchorage", "state": "AK", "country": ""}, {"city": "Andover", "state": "NJ", "country": ""}, {"city": "Anjou", "state": "QC", "country": ""}, {"city": "Anken<PERSON>", "state": "IA", "country": ""}, {"city": "Antioch", "state": "TN", "country": ""}, {"city": "Apodaca", "state": "NL", "country": "Mexico"}, {"city": "(Apodaca)", "state": "NL", "country": ""}, {"city": "(Apodaca Nuevo Leon)", "state": "NL", "country": "Mexico"}, {"city": "Apodaca Nuevo Leon", "state": "NL", "country": "Mexico"}, {"city": "<PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Apple Valley", "state": "CA", "country": ""}, {"city": "Arden Hills", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "OK", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "OK", "country": "United States"}, {"city": "Arlington", "state": "OR", "country": ""}, {"city": "Arlington", "state": "TX", "country": "United States"}, {"city": "Arlington", "state": "WA", "country": ""}, {"city": "Arlington Heights", "state": "IL", "country": ""}, {"city": "Arnprior", "state": "ON", "country": ""}, {"city": "<PERSON>", "state": "IL", "country": "United States"}, {"city": "Arvada", "state": "CO", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "VA", "country": ""}, {"city": "Ashland", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "IN", "country": "United States"}, {"city": "Aspen", "state": "CO", "country": ""}, {"city": "Athens", "state": "GA", "country": ""}, {"city": "Athens", "state": "TN", "country": ""}, {"city": "Atlanta", "state": "GA", "country": ""}, {"city": "Atwater", "state": "CA", "country": ""}, {"city": "Auburn", "state": "NY", "country": ""}, {"city": "Auburn", "state": "WA", "country": ""}, {"city": "Auburndale", "state": "FL", "country": ""}, {"city": "Auburn Hills", "state": "MI", "country": ""}, {"city": "Augusta", "state": "ME", "country": ""}, {"city": "Aurora", "state": "CO", "country": ""}, {"city": "Aurora", "state": "CO", "country": "United States"}, {"city": "Aurora", "state": "IL", "country": ""}, {"city": "Aurora", "state": "ON", "country": ""}, {"city": "Austell", "state": "GA", "country": ""}, {"city": "Austin", "state": "TX", "country": ""}, {"city": "Austin", "state": "TX", "country": "United States"}, {"city": "Ayer", "state": "MA", "country": ""}, {"city": "Ayr", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Baie<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Bakersfield", "state": "CA", "country": ""}, {"city": "Baltimore", "state": "MD", "country": ""}, {"city": "Baltimore", "state": "MD", "country": "United States"}, {"city": "Bangor", "state": "ME", "country": ""}, {"city": "Banner Elk", "state": "NC", "country": ""}, {"city": "Banning", "state": "CA", "country": ""}, {"city": "Baraboo", "state": "WI", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OH", "country": ""}, {"city": "Batesville", "state": "IN", "country": ""}, {"city": "Baton Rouge", "state": "LA", "country": ""}, {"city": "Battleboro", "state": "NC", "country": ""}, {"city": "Battle Creek", "state": "MI", "country": "United States"}, {"city": "Bayonne", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "NL", "country": ""}, {"city": "Baytown", "state": "TX", "country": ""}, {"city": "Baytown", "state": "TX", "country": "United States"}, {"city": "Bear", "state": "DE", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Beaumont", "state": "CA", "country": ""}, {"city": "Beauport", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OR", "country": ""}, {"city": "Bedford", "state": "NH", "country": ""}, {"city": "Beecave", "state": "TX", "country": ""}, {"city": "Beech Island", "state": "SC", "country": ""}, {"city": "Belcamp", "state": "MD", "country": ""}, {"city": "Belfast", "state": "ME", "country": ""}, {"city": "Bell", "state": "CA", "country": ""}, {"city": "Belleville", "state": "MI", "country": ""}, {"city": "Belleville", "state": "ON", "country": ""}, {"city": "Bellevue", "state": "OH", "country": ""}, {"city": "Bellevue", "state": "WA", "country": ""}, {"city": "Bellingham", "state": "MA", "country": ""}, {"city": "Bellingham", "state": "WA", "country": ""}, {"city": "Belmont", "state": "NY", "country": ""}, {"city": "Beloit", "state": "WI", "country": ""}, {"city": "Beltsville", "state": "MD", "country": ""}, {"city": "Belvidere", "state": "IL", "country": ""}, {"city": "Bennington", "state": "VT", "country": ""}, {"city": "Bensalem", "state": "PA", "country": ""}, {"city": "Bensenville", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "Bessemer", "state": "AL", "country": ""}, {"city": "Bethany Beach", "state": "DE", "country": ""}, {"city": "Bethel", "state": "CT", "country": ""}, {"city": "Bethel", "state": "PA", "country": ""}, {"city": "Bethlehem", "state": "PA", "country": ""}, {"city": "Beverly Hills", "state": "CA", "country": ""}, {"city": "Billerica", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MT", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "Birmingham", "state": "AL", "country": ""}, {"city": "Blackwood", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "WA", "country": ""}, {"city": "Blainville", "state": "QC", "country": ""}, {"city": "Bloomfield", "state": "CT", "country": ""}, {"city": "Bloomingdale", "state": "GA", "country": ""}, {"city": "Bloomington", "state": "MN", "country": ""}, {"city": "Bluefield", "state": "VA", "country": ""}, {"city": "Blue Ridge Summit", "state": "PA", "country": "UNITED STATES"}, {"city": "Boardman", "state": "OH", "country": ""}, {"city": "Boisbriand", "state": "QC", "country": ""}, {"city": "Boise", "state": "ID", "country": ""}, {"city": "<PERSON>ling<PERSON>", "state": "IL", "country": ""}, {"city": "Bolton", "state": "ON", "country": ""}, {"city": "Bonney Lake", "state": "WA", "country": ""}, {"city": "Bossier City", "state": "LA", "country": ""}, {"city": "Boston", "state": "GA", "country": ""}, {"city": "Boston", "state": "MA", "country": ""}, {"city": "Boston", "state": "MA", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Boucherville", "state": "QC", "country": ""}, {"city": "Boulder", "state": "CO", "country": ""}, {"city": "Boulder", "state": "CO", "country": "United States"}, {"city": "Bowling Green", "state": "KY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "FL", "country": "United States"}, {"city": "Brampton", "state": "ON", "country": ""}, {"city": "Brampton", "state": "ON", "country": "CANADA"}, {"city": "Branchburg", "state": "NJ", "country": ""}, {"city": "Branford", "state": "CT", "country": ""}, {"city": "Brantford", "state": "ON", "country": ""}, {"city": "Brantford", "state": "ON", "country": "Canada"}, {"city": "Brattleboro", "state": "VT", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Breinigsville", "state": "PA", "country": ""}, {"city": "Bremerton", "state": "WA", "country": ""}, {"city": "Brentwood", "state": "TN", "country": ""}, {"city": "<PERSON>", "state": "NY", "country": ""}, {"city": "Bridgeport", "state": "CT", "country": ""}, {"city": "Bridgeport", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MO", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "Bridgeview", "state": "IL", "country": ""}, {"city": "Bridgewater", "state": "NS", "country": ""}, {"city": "Brighton", "state": "CO", "country": ""}, {"city": "Brighton", "state": "CO", "country": "United States"}, {"city": "Brighton", "state": "MA", "country": ""}, {"city": "Bristol", "state": "CT", "country": ""}, {"city": "Bristol", "state": "PA", "country": ""}, {"city": "Bristol", "state": "RI", "country": ""}, {"city": "Brockport", "state": "NY", "country": ""}, {"city": "Brockton", "state": "MA", "country": ""}, {"city": "Broken Arrow", "state": "OK", "country": "United States"}, {"city": "Brookfield", "state": "IL", "country": ""}, {"city": "Brookings", "state": "OR", "country": ""}, {"city": "Brooklyn", "state": "NY", "country": ""}, {"city": "Brookshire", "state": "TX", "country": ""}, {"city": "Brookshire", "state": "TX", "country": "United States"}, {"city": "Broomfield", "state": "CO", "country": ""}, {"city": "Broussard", "state": "LA", "country": ""}, {"city": "Brownsburg", "state": "IN", "country": ""}, {"city": "Brownsville", "state": "TN", "country": "United States"}, {"city": "Brownsville", "state": "TX", "country": ""}, {"city": "Brownsville", "state": "TX", "country": "United States"}, {"city": "Brownwood", "state": "TX", "country": ""}, {"city": "Brunswick", "state": "ME", "country": ""}, {"city": "Brunswick", "state": "OH", "country": "United States"}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WV", "country": ""}, {"city": "Budd Lake", "state": "NJ", "country": ""}, {"city": "Buena Park", "state": "CA", "country": ""}, {"city": "Buena Vista", "state": "VA", "country": ""}, {"city": "Buffalo", "state": "NY", "country": ""}, {"city": "Buffalo", "state": "NY", "country": "UNITED STATES"}, {"city": "<PERSON> (Amherst)", "state": "NY", "country": ""}, {"city": "Buffalo (Williamsvil", "state": "NY", "country": ""}, {"city": "Burbank", "state": "CA", "country": ""}, {"city": "Burbank", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Burlington", "state": "IA", "country": ""}, {"city": "Burlington", "state": "NJ", "country": ""}, {"city": "Burlington", "state": "ON", "country": ""}, {"city": "Burlington", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "BC", "country": ""}, {"city": "<PERSON>", "state": "PA", "country": ""}, {"city": "Byesville", "state": "OH", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "MS", "country": ""}, {"city": "Byron Center", "state": "MI", "country": ""}, {"city": "Cabazon", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "ID", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Calera", "state": "AL", "country": ""}, {"city": "Calexico", "state": "CA", "country": ""}, {"city": "Calgary", "state": "AB", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Camas", "state": "WA", "country": ""}, {"city": "Cambridge", "state": "MN", "country": ""}, {"city": "Cambridge", "state": "OH", "country": ""}, {"city": "Cambridge", "state": "ON", "country": ""}, {"city": "Cambridge", "state": "ON", "country": "Canada"}, {"city": "Camden", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NB", "country": ""}, {"city": "Camp Hill", "state": "PA", "country": ""}, {"city": "Canmore", "state": "AB", "country": ""}, {"city": "Canmore", "state": "AL", "country": ""}, {"city": "Canton", "state": "MI", "country": ""}, {"city": "Canton", "state": "NY", "country": ""}, {"city": "Canton", "state": "OH", "country": ""}, {"city": "Canton", "state": "PA", "country": ""}, {"city": "Cantua Creek", "state": "CA", "country": ""}, {"city": "Cape Canaveral", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NB", "country": ""}, {"city": "Carldstadt", "state": "NJ", "country": ""}, {"city": "Carlisle", "state": "PA", "country": ""}, {"city": "Carmel", "state": "IN", "country": ""}, {"city": "Carmel", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "Cartersville", "state": "GA", "country": ""}, {"city": "Carthage", "state": "MO", "country": ""}, {"city": "<PERSON>", "state": "MN", "country": ""}, {"city": "<PERSON>", "state": "WY", "country": ""}, {"city": "Castle Rock", "state": "CO", "country": ""}, {"city": "Castleton On Hudson", "state": "NY", "country": ""}, {"city": "Castroville", "state": "CA", "country": ""}, {"city": "Catskill", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "SC", "country": ""}, {"city": "Cedar Falls", "state": "IA", "country": ""}, {"city": "Cedar Grove", "state": "NJ", "country": ""}, {"city": "Cedar Park", "state": "TX", "country": ""}, {"city": "Cedar Rapids", "state": "IA", "country": ""}, {"city": "Centerville", "state": "UT", "country": ""}, {"city": "Centralia", "state": "WA", "country": ""}, {"city": "Central Islip", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Chagrin Falls", "state": "OH", "country": ""}, {"city": "Chambersburg", "state": "PA", "country": ""}, {"city": "<PERSON>", "state": "AZ", "country": ""}, {"city": "<PERSON>", "state": "MN", "country": ""}, {"city": "Chariton", "state": "IA", "country": ""}, {"city": "Charleston", "state": "SC", "country": ""}, {"city": "Charlestown", "state": "MA", "country": ""}, {"city": "Charlotte", "state": "MI", "country": "United States"}, {"city": "Charlotte", "state": "NC", "country": ""}, {"city": "Charlotte", "state": "NC", "country": "United States"}, {"city": "Chatham", "state": "MA", "country": ""}, {"city": "Chatsworth", "state": "CA", "country": ""}, {"city": "Chattanooga", "state": "TN", "country": ""}, {"city": "Cheektowaga", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Chelmsford", "state": "MA", "country": ""}, {"city": "Cherry Hill", "state": "NJ", "country": ""}, {"city": "Chesapeake", "state": "VA", "country": ""}, {"city": "Cheshire", "state": "CT", "country": ""}, {"city": "Chester", "state": "PA", "country": ""}, {"city": "Chester", "state": "SC", "country": ""}, {"city": "Chester", "state": "VA", "country": ""}, {"city": "Chesterfield", "state": "MO", "country": ""}, {"city": "Cheyenne", "state": "WY", "country": ""}, {"city": "Cheyenne", "state": "WY", "country": "United States"}, {"city": "Chicago", "state": "IL", "country": ""}, {"city": "Chicago", "state": "IL", "country": "United States"}, {"city": "Chicopee", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Chino", "state": "CA", "country": ""}, {"city": "Christiansburg", "state": "VA", "country": ""}, {"city": "Cicero", "state": "IL", "country": "United States"}, {"city": "Cincinnati", "state": "OH", "country": ""}, {"city": "Cincinnati", "state": "OH", "country": "United States"}, {"city": "Cincinnati", "state": "OH", "country": "UNITED STATES"}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "Circleville", "state": "OH", "country": ""}, {"city": "Circleville", "state": "OH", "country": "United States"}, {"city": "City Of Commerce", "state": "CA", "country": ""}, {"city": "City Of Industr", "state": "CA", "country": ""}, {"city": "City Of Industry", "state": "CA", "country": ""}, {"city": "Clackamas", "state": "OR", "country": ""}, {"city": "Clackamas", "state": "OR", "country": "USA"}, {"city": "Clarksville", "state": "AR", "country": ""}, {"city": "Claysburg", "state": "PA", "country": ""}, {"city": "<PERSON>", "state": "NC", "country": ""}, {"city": "Clearfield", "state": "UT", "country": ""}, {"city": "Clearwater", "state": "FL", "country": ""}, {"city": "Clearwater Beach", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": "United States"}, {"city": "Cleveland", "state": "OH", "country": ""}, {"city": "Cleveland", "state": "TN", "country": ""}, {"city": "<PERSON>", "state": "UT", "country": ""}, {"city": "Clintonville", "state": "WI", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "NM", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Cochranton", "state": "PA", "country": ""}, {"city": "Coeur <PERSON>", "state": "ID", "country": ""}, {"city": "Colchester", "state": "CT", "country": ""}, {"city": "Coldwater", "state": "MI", "country": ""}, {"city": "Coldwater", "state": "MI", "country": "United States"}, {"city": "College Park", "state": "GA", "country": ""}, {"city": "College Place", "state": "WA", "country": ""}, {"city": "Collierville", "state": "TN", "country": "United States"}, {"city": "Colonial Height", "state": "VA", "country": ""}, {"city": "Colorado Springs", "state": "CO", "country": ""}, {"city": "Colorado Springs", "state": "CO", "country": "United States"}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "Columbia", "state": "MD", "country": ""}, {"city": "Columbia", "state": "MO", "country": ""}, {"city": "Columbia", "state": "SC", "country": ""}, {"city": "Columbia", "state": "TN", "country": ""}, {"city": "Columbus", "state": "GA", "country": ""}, {"city": "Columbus", "state": "OH", "country": ""}, {"city": "Columbus", "state": "OH", "country": "United States"}, {"city": "Commerce", "state": "CA", "country": ""}, {"city": "Commerce", "state": "CA", "country": "United States"}, {"city": "Commerce City", "state": "CO", "country": ""}, {"city": "Commerce City", "state": "CO", "country": "United States"}, {"city": "Commercial Point", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "Concord", "state": "NC", "country": ""}, {"city": "Concord", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Converse", "state": "TX", "country": "United States"}, {"city": "Cookeville", "state": "TN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": "United States"}, {"city": "Corinth", "state": "MS", "country": ""}, {"city": "Cornwall", "state": "ON", "country": ""}, {"city": "Corona", "state": "CA", "country": ""}, {"city": "Corpus Christi", "state": "TX", "country": ""}, {"city": "Costa Mesa", "state": "CA", "country": ""}, {"city": "Council Bluffs", "state": "IA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Covington", "state": "KY", "country": ""}, {"city": "Covington", "state": "WA", "country": ""}, {"city": "Cowansville", "state": "QC", "country": ""}, {"city": "Cranbury", "state": "NJ", "country": ""}, {"city": "Crandall", "state": "GA", "country": ""}, {"city": "Crewe", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Crossville", "state": "TN", "country": ""}, {"city": "Crown Point", "state": "IN", "country": ""}, {"city": "Crystal Springs", "state": "MS", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Cuyahoga Falls", "state": "OH", "country": ""}, {"city": "Cypress", "state": "CA", "country": ""}, {"city": "Cypress", "state": "TX", "country": ""}, {"city": "Dallas", "state": "TX", "country": ""}, {"city": "Dallas", "state": "TX", "country": "United States"}, {"city": "Dana Point", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "Danville", "state": "IL", "country": ""}, {"city": "Danville", "state": "WV", "country": ""}, {"city": "Dartmouth", "state": "NS", "country": ""}, {"city": "<PERSON>", "state": "FL", "country": ""}, {"city": "<PERSON>", "state": "IA", "country": "United States"}, {"city": "<PERSON>", "state": "OK", "country": ""}, {"city": "Dayton", "state": "MN", "country": ""}, {"city": "Dayton", "state": "NJ", "country": ""}, {"city": "Dayton", "state": "NY", "country": ""}, {"city": "Dayton", "state": "OH", "country": ""}, {"city": "Dayton", "state": "OH", "country": "United States"}, {"city": "Dayton", "state": "TX", "country": "United States"}, {"city": "Daytona Beach", "state": "FL", "country": ""}, {"city": "Dayville", "state": "CT", "country": ""}, {"city": "Decatur", "state": "AL", "country": "United States"}, {"city": "Decatur", "state": "GA", "country": ""}, {"city": "Decatur", "state": "IL", "country": ""}, {"city": "Dedham", "state": "MA", "country": ""}, {"city": "Deep River", "state": "CT", "country": ""}, {"city": "Deerfield Beach", "state": "FL", "country": ""}, {"city": "Dekalb", "state": "IL", "country": ""}, {"city": "Dekalb", "state": "IL", "country": "United States"}, {"city": "<PERSON>", "state": "IN", "country": "United States"}, {"city": "Delano", "state": "CA", "country": ""}, {"city": "Delta", "state": "BC", "country": ""}, {"city": "Delta", "state": "BC", "country": "CANADA"}, {"city": "Delta", "state": "CO", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Dennis Port", "state": "MA", "country": ""}, {"city": "Denver", "state": "CO", "country": ""}, {"city": "Denver", "state": "CO", "country": "United States"}, {"city": "Denver", "state": "PA", "country": ""}, {"city": "Des Moines", "state": "IA", "country": ""}, {"city": "Des Moines", "state": "IA", "country": "United States"}, {"city": "Des Moines", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "Detroit", "state": "MI", "country": "UNITED STATES"}, {"city": "<PERSON>", "state": "ND", "country": ""}, {"city": "Din<PERSON>", "state": "CA", "country": ""}, {"city": "Disposal", "state": "CA", "country": ""}, {"city": "Dorcester", "state": "MA", "country": ""}, {"city": "Douglassville", "state": "PA", "country": ""}, {"city": "Downers Grove", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ID", "country": ""}, {"city": "Dublin", "state": "GA", "country": ""}, {"city": "Dublin", "state": "OH", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IN", "country": ""}, {"city": "Duncansville", "state": "PA", "country": ""}, {"city": "Duncanville", "state": "TX", "country": ""}, {"city": "Dundee", "state": "FL", "country": ""}, {"city": "<PERSON>", "state": "NC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": "United States"}, {"city": "Durham", "state": "NC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MN", "country": ""}, {"city": "Earth City", "state": "MO", "country": ""}, {"city": "East Bernstadt", "state": "KY", "country": ""}, {"city": "East Falmouth", "state": "MA", "country": ""}, {"city": "East Flat Rock", "state": "NC", "country": ""}, {"city": "Eastford", "state": "CT", "country": ""}, {"city": "East Hanover", "state": "NJ", "country": ""}, {"city": "East Liberty", "state": "OH", "country": "United States"}, {"city": "Easton", "state": "PA", "country": ""}, {"city": "East Providence", "state": "RI", "country": ""}, {"city": "East Rutherford", "state": "NJ", "country": ""}, {"city": "East Spencer", "state": "NC", "country": ""}, {"city": "East St. Louis", "state": "IL", "country": "United States"}, {"city": "Eastvale", "state": "CA", "country": ""}, {"city": "East Wenatchee", "state": "WA", "country": ""}, {"city": "East York", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WI", "country": "United States"}, {"city": "Edgerton", "state": "KS", "country": ""}, {"city": "Edgewood", "state": "MD", "country": ""}, {"city": "Edison", "state": "CA", "country": ""}, {"city": "Edison", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "OK", "country": ""}, {"city": "<PERSON>", "state": "OK", "country": "United States"}, {"city": "Edmonton", "state": "AB", "country": ""}, {"city": "Edmonton", "state": "AB", "country": "Canada"}, {"city": "Edwardsville", "state": "IL", "country": ""}, {"city": "El Centro", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "NJ", "country": ""}, {"city": "Elizabethtown", "state": "PA", "country": ""}, {"city": "Elk Grove", "state": "IL", "country": ""}, {"city": "Elk Grove Villa", "state": "IL", "country": ""}, {"city": "Elkland", "state": "PA", "country": ""}, {"city": "Elkridge", "state": "MD", "country": ""}, {"city": "Elkton", "state": "FL", "country": ""}, {"city": "Elkton", "state": "MD", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Elm Grove", "state": "WI", "country": ""}, {"city": "Elmhurst", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NY", "country": "UNITED STATES"}, {"city": "Elmwood Park", "state": "NJ", "country": ""}, {"city": "El Paso", "state": "TX", "country": ""}, {"city": "El Reno", "state": "OK", "country": "United States"}, {"city": "El Segundo", "state": "CA", "country": ""}, {"city": "Elwood", "state": "IL", "country": ""}, {"city": "Elwood", "state": "IL", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Englewood", "state": "CO", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Erie", "state": "CO", "country": ""}, {"city": "Erie", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "KY", "country": ""}, {"city": "Essex", "state": "MD", "country": ""}, {"city": "Etobicoke", "state": "ON", "country": ""}, {"city": "Etobicoke", "state": "ON", "country": "Canada"}, {"city": "Eugene", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "Evansville", "state": "IN", "country": ""}, {"city": "<PERSON>", "state": "WA", "country": ""}, {"city": "Exeter", "state": "NH", "country": ""}, {"city": "(<PERSON><PERSON><PERSON>)", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Fairfield", "state": "NJ", "country": ""}, {"city": "Fairmont", "state": "WV", "country": ""}, {"city": "Fairport", "state": "NY", "country": ""}, {"city": "Fairview", "state": "TN", "country": ""}, {"city": "Fall City", "state": "WA", "country": ""}, {"city": "Fall River", "state": "MA", "country": ""}, {"city": "Falmouth", "state": "ME", "country": ""}, {"city": "Fargo", "state": "ND", "country": ""}, {"city": "Farmers Branch", "state": "TX", "country": ""}, {"city": "Farmington", "state": "NY", "country": ""}, {"city": "Farr West", "state": "UT", "country": ""}, {"city": "Fayetteville", "state": "NC", "country": ""}, {"city": "Fayetteville", "state": "NC", "country": "United States"}, {"city": "Federalsburg", "state": "MD", "country": ""}, {"city": "Federal Way", "state": "WA", "country": ""}, {"city": "Feeding Hills", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MO", "country": ""}, {"city": "Fernandina Beach", "state": "FL", "country": ""}, {"city": "Fife", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OH", "country": ""}, {"city": "Firebaugh", "state": "CA", "country": ""}, {"city": "Firestone", "state": "CO", "country": ""}, {"city": "<PERSON>s", "state": "IN", "country": ""}, {"city": "Flanders", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "NC", "country": ""}, {"city": "Flint", "state": "MI", "country": "United States"}, {"city": "Florence", "state": "KY", "country": ""}, {"city": "Florence", "state": "KY", "country": "United States"}, {"city": "Florence", "state": "NJ", "country": ""}, {"city": "Florence", "state": "OR", "country": ""}, {"city": "Florence", "state": "SC", "country": ""}, {"city": "Florence,", "state": "SC", "country": ""}, {"city": "Florenceville-Bristo", "state": "NB", "country": ""}, {"city": "Flower Mound", "state": "TX", "country": ""}, {"city": "<PERSON>ond <PERSON>", "state": "WI", "country": ""}, {"city": "Fontana", "state": "CA", "country": ""}, {"city": "Forest Grove", "state": "OR", "country": ""}, {"city": "Forest Park", "state": "GA", "country": ""}, {"city": "Fort Collins", "state": "CO", "country": ""}, {"city": "Fort Erie", "state": "ON", "country": ""}, {"city": "Fort Lauderdale", "state": "FL", "country": ""}, {"city": "Fort Lawn", "state": "SC", "country": ""}, {"city": "Fort Loudon", "state": "PA", "country": ""}, {"city": "Fort Madison", "state": "IA", "country": ""}, {"city": "Fort Myers", "state": "FL", "country": ""}, {"city": "Fort Wayne", "state": "IN", "country": ""}, {"city": "Fort Worth", "state": "TX", "country": ""}, {"city": "Fort Worth", "state": "TX", "country": "United States"}, {"city": "Fountain", "state": "CO", "country": ""}, {"city": "Fountain Hills", "state": "AZ", "country": ""}, {"city": "Four Oaks", "state": "NC", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "Frankfort", "state": "IN", "country": ""}, {"city": "Frankfort", "state": "KY", "country": ""}, {"city": "<PERSON>", "state": "IN", "country": ""}, {"city": "<PERSON>", "state": "PA", "country": ""}, {"city": "<PERSON>", "state": "WI", "country": ""}, {"city": "Franklinville", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "MD", "country": ""}, {"city": "Fredericksburg", "state": "VA", "country": ""}, {"city": "Fredericton", "state": "NB", "country": ""}, {"city": "Fredonia", "state": "NY", "country": ""}, {"city": "Freeport", "state": "ME", "country": ""}, {"city": "Freeport", "state": "NY", "country": ""}, {"city": "Fremont", "state": "CA", "country": ""}, {"city": "Frenchtown Charter Township", "state": "MI", "country": "United States"}, {"city": "Fresno", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Frostproof", "state": "FL", "country": ""}, {"city": "Ft Mitchell", "state": "KY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Gaffney", "state": "SC", "country": ""}, {"city": "Gainesville", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "TN", "country": "UNITED STATES"}, {"city": "Galveston", "state": "TX", "country": ""}, {"city": "Gansevoort", "state": "NY", "country": ""}, {"city": "Gap", "state": "PA", "country": ""}, {"city": "Gardena", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": "United States"}, {"city": "Garysburg", "state": "NC", "country": ""}, {"city": "Gastonia", "state": "NC", "country": ""}, {"city": "Gatineau", "state": "QC", "country": ""}, {"city": "Geneva", "state": "AL", "country": ""}, {"city": "Geneva", "state": "IL", "country": ""}, {"city": "Geneva", "state": "NY", "country": ""}, {"city": "Geneva,", "state": "IL", "country": ""}, {"city": "Geneva, Il", "state": "IL", "country": ""}, {"city": "Georgetown", "state": "KY", "country": ""}, {"city": "Georgetown", "state": "ON", "country": ""}, {"city": "Georgetown", "state": "TX", "country": ""}, {"city": "Germantown", "state": "TN", "country": "United States"}, {"city": "Gibson City", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "Gig Harbor", "state": "WA", "country": ""}, {"city": "<PERSON>", "state": "AZ", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Glendale", "state": "CO", "country": ""}, {"city": "Glendale Heights", "state": "IL", "country": ""}, {"city": "Glendora", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "KS", "country": ""}, {"city": "Golden", "state": "CO", "country": ""}, {"city": "Golden Valley", "state": "AZ", "country": ""}, {"city": "Golden Valley", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Goodyear", "state": "AZ", "country": ""}, {"city": "Gorham", "state": "ME", "country": ""}, {"city": "Goshen", "state": "NY", "country": ""}, {"city": "Granbury", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Grand Forks Afb", "state": "ND", "country": ""}, {"city": "Grand Junction", "state": "CO", "country": ""}, {"city": "Grand Prairie", "state": "TX", "country": ""}, {"city": "Grand Prairie", "state": "TX", "country": "United States"}, {"city": "Grand Prairie, Dallas", "state": "TX", "country": "United States"}, {"city": "Grand Rapids", "state": "MI", "country": ""}, {"city": "Grandview", "state": "MO", "country": ""}, {"city": "Grandville", "state": "MI", "country": ""}, {"city": "Granite City", "state": "IL", "country": ""}, {"city": "Grants Pass", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Great Bend", "state": "KS", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CO", "country": ""}, {"city": "Green Bay", "state": "WI", "country": ""}, {"city": "Greencastle", "state": "PA", "country": ""}, {"city": "Greendale", "state": "IN", "country": ""}, {"city": "Greenfield", "state": "IN", "country": ""}, {"city": "Greensboro", "state": "NC", "country": ""}, {"city": "Greenville", "state": "SC", "country": ""}, {"city": "Greenwich", "state": "CT", "country": ""}, {"city": "Greenwich", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "IN", "country": ""}, {"city": "Greenwood Village", "state": "CO", "country": ""}, {"city": "G<PERSON>", "state": "SC", "country": ""}, {"city": "Grove City", "state": "OH", "country": ""}, {"city": "Grove City", "state": "OH", "country": "United States"}, {"city": "Groveport", "state": "OH", "country": ""}, {"city": "Guadalupe", "state": "CA", "country": ""}, {"city": "Guelph", "state": "ON", "country": ""}, {"city": "Guilford", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Gypsum", "state": "CO", "country": ""}, {"city": "Hacienda Height", "state": "CA", "country": ""}, {"city": "Hagerstown", "state": "MD", "country": ""}, {"city": "Halfmoon", "state": "NY", "country": ""}, {"city": "Halifax", "state": "NS", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WV", "country": ""}, {"city": "Hamburg", "state": "NY", "country": ""}, {"city": "Hamburg", "state": "PA", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WV", "country": ""}, {"city": "<PERSON>", "state": "LA", "country": ""}, {"city": "Hamtramck", "state": "MI", "country": ""}, {"city": "Hanover", "state": "PA", "country": ""}, {"city": "Happy Valley", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON>", "state": "LA", "country": ""}, {"city": "Harker Heights", "state": "TX", "country": ""}, {"city": "Harmony", "state": "PA", "country": ""}, {"city": "Harrisburg", "state": "NC", "country": ""}, {"city": "Harrisburg", "state": "PA", "country": ""}, {"city": "Hartford", "state": "MI", "country": ""}, {"city": "<PERSON>", "state": "LA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Ha<PERSON>pa<PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Hawthorne", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": "UNITED STATES"}, {"city": "<PERSON>", "state": "CA", "country": "USA"}, {"city": "<PERSON>", "state": "IL", "country": ""}, {"city": "Hazel Park", "state": "MI", "country": "United States"}, {"city": "Hazel Township", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MO", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Hazle Township", "state": "PA", "country": ""}, {"city": "Healdsburg", "state": "CA", "country": ""}, {"city": "Hebron", "state": "KY", "country": ""}, {"city": "Hebron", "state": "OH", "country": ""}, {"city": "Helena", "state": "MT", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": "United States"}, {"city": "<PERSON>", "state": "KY", "country": ""}, {"city": "<PERSON>", "state": "NV", "country": ""}, {"city": "Henrico", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "MS", "country": ""}, {"city": "Hialeah", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NC", "country": "United States"}, {"city": "Hicksville", "state": "NY", "country": ""}, {"city": "Highland", "state": "NY", "country": ""}, {"city": "Highlands Ranch", "state": "CO", "country": ""}, {"city": "High Point", "state": "NC", "country": ""}, {"city": "Hillsboro", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "HI", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OH", "country": ""}, {"city": "Hobart", "state": "IN", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "KS", "country": ""}, {"city": "Holland", "state": "MI", "country": ""}, {"city": "Hollywood", "state": "FL", "country": ""}, {"city": "Holtville", "state": "CA", "country": ""}, {"city": "Honesdale", "state": "PA", "country": ""}, {"city": "Honolulu", "state": "HI", "country": ""}, {"city": "Hood River", "state": "OR", "country": ""}, {"city": "Hope", "state": "AR", "country": ""}, {"city": "Hopewell Junction", "state": "NY", "country": ""}, {"city": "<PERSON>", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "Horn Lake", "state": "MS", "country": ""}, {"city": "Horn Lake", "state": "MS", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Houston", "state": "TX", "country": ""}, {"city": "Houston", "state": "TX", "country": "United States"}, {"city": "<PERSON>", "state": "NY", "country": ""}, {"city": "Huntersville", "state": "NC", "country": ""}, {"city": "Huntsville", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON> ", "state": "KS", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Independence", "state": "KY", "country": ""}, {"city": "Independence", "state": "MO", "country": ""}, {"city": "Independence", "state": "MO", "country": "United States"}, {"city": "Indianapolis", "state": "IN", "country": ""}, {"city": "Indianapolis", "state": "IN", "country": "United States"}, {"city": "Ingersoll", "state": "ON", "country": ""}, {"city": "Inola", "state": "OK", "country": ""}, {"city": "Inola", "state": "OK", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "CA", "country": "UNITED STATES"}, {"city": "Ipswich", "state": "MA", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": "United States"}, {"city": "Irwindale", "state": "CA", "country": ""}, {"city": "<PERSON>sa<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Ithaca", "state": "NY", "country": ""}, {"city": "<PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON>", "state": "MI", "country": ""}, {"city": "<PERSON>", "state": "MS", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "TN", "country": ""}, {"city": "<PERSON>", "state": "TN", "country": "United States"}, {"city": "<PERSON>", "state": "WI", "country": ""}, {"city": "Jacksonville", "state": "FL", "country": ""}, {"city": "Jacksonville", "state": "TX", "country": ""}, {"city": "Jamaica", "state": "NY", "country": ""}, {"city": "Jamesburg", "state": "NJ", "country": ""}, {"city": "Jamestown", "state": "NY", "country": ""}, {"city": "Janesville", "state": "WI", "country": ""}, {"city": "<PERSON>", "state": "MO", "country": ""}, {"city": "<PERSON>", "state": "NV", "country": ""}, {"city": "Jeffersonville", "state": "GA", "country": ""}, {"city": "Jericho", "state": "VT", "country": ""}, {"city": "Jersey City", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MD", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "Jonesboro", "state": "AR", "country": ""}, {"city": "Jonesboro", "state": "GA", "country": ""}, {"city": "Jonestown", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "MO", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "Junction City", "state": "KS", "country": ""}, {"city": "Jupiter", "state": "FL", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "HI", "country": ""}, {"city": "Kalamazoo", "state": "MI", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Kankakee", "state": "IL", "country": "United States"}, {"city": "Kansas City", "state": "KS", "country": ""}, {"city": "Kansas City", "state": "MO", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": "United States"}, {"city": "Kearneysville", "state": "WV", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "BC", "country": ""}, {"city": "Kendallville", "state": "IN", "country": "United States"}, {"city": "Kenmore", "state": "WA", "country": ""}, {"city": "Kennebunk", "state": "ME", "country": ""}, {"city": "Kennesaw", "state": "GA", "country": ""}, {"city": "Kennett Square", "state": "PA", "country": ""}, {"city": "Kennewick", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Kent", "state": "WA", "country": ""}, {"city": "Kent", "state": "WA", "country": "United States"}, {"city": "Kent", "state": "WA", "country": "UNITED STATES"}, {"city": "<PERSON><PERSON><PERSON>", "state": "HI", "country": ""}, {"city": "<PERSON><PERSON>", "state": "KY", "country": ""}, {"city": "King Of Prussia", "state": "PA", "country": ""}, {"city": "Kingston", "state": "MA", "country": ""}, {"city": "Kingston", "state": "OK", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "<PERSON>er", "state": "ON", "country": ""}, {"city": "<PERSON>er", "state": "ON", "country": "CANADA"}, {"city": "Kleinburg", "state": "ON", "country": ""}, {"city": "Knightdale", "state": "NC", "country": ""}, {"city": "Knoxville", "state": "TN", "country": ""}, {"city": "Kutztown", "state": "PA", "country": ""}, {"city": "<PERSON>", "state": "WA", "country": ""}, {"city": "Lachine", "state": "QC", "country": ""}, {"city": "Laconia (Gilford)", "state": "NH", "country": ""}, {"city": "La Crosse", "state": "WI", "country": ""}, {"city": "Lafayette", "state": "CO", "country": ""}, {"city": "Lafayette", "state": "LA", "country": ""}, {"city": "La Grange", "state": "GA", "country": "United States"}, {"city": "La Habra", "state": "CA", "country": ""}, {"city": "Lake Buena Vista", "state": "FL", "country": ""}, {"city": "Lake Charles", "state": "LA", "country": ""}, {"city": "Lake City", "state": "FL", "country": ""}, {"city": "Lake Elmo", "state": "MN", "country": ""}, {"city": "<PERSON> Jackson", "state": "TX", "country": ""}, {"city": "Lake Katrine", "state": "NY", "country": ""}, {"city": "Lakeland", "state": "FL", "country": ""}, {"city": "Lakeside", "state": "NS", "country": ""}, {"city": "Lake St. Louis", "state": "MO", "country": ""}, {"city": "Lakeville", "state": "MN", "country": ""}, {"city": "Lake Wales", "state": "FL", "country": ""}, {"city": "Lakewood", "state": "CO", "country": ""}, {"city": "Lakewood", "state": "NJ", "country": ""}, {"city": "Lakewood", "state": "WA", "country": ""}, {"city": "La Mirada", "state": "CA", "country": ""}, {"city": "Lancaster", "state": "PA", "country": ""}, {"city": "Lancaster", "state": "SC", "country": ""}, {"city": "Langley", "state": "BC", "country": ""}, {"city": "Lansing", "state": "MI", "country": ""}, {"city": "La Pocatiere", "state": "QC", "country": ""}, {"city": "La Porte", "state": "IN", "country": ""}, {"city": "La Porte", "state": "TX", "country": ""}, {"city": "La Porte", "state": "TX", "country": "United States"}, {"city": "Laredo", "state": "TX", "country": ""}, {"city": "Laredo", "state": "TX", "country": "United States"}, {"city": "L'Assomption", "state": "QC", "country": ""}, {"city": "Las Vegas", "state": "NV", "country": ""}, {"city": "<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "QC", "country": "Canada"}, {"city": "<PERSON><PERSON>", "state": "QC", "country": "CANADA"}, {"city": "La Vergne", "state": "TN", "country": ""}, {"city": "Lavista", "state": "NE", "country": ""}, {"city": "La Vista", "state": "NE", "country": ""}, {"city": "<PERSON>", "state": "KS", "country": ""}, {"city": "<PERSON>", "state": "MA", "country": ""}, {"city": "Lawrenceburg", "state": "IN", "country": ""}, {"city": "Lawrenceville", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": "United States"}, {"city": "<PERSON>", "state": "UT", "country": ""}, {"city": "<PERSON><PERSON>", "state": "KS", "country": ""}, {"city": "Lebanon", "state": "CT", "country": ""}, {"city": "Lebanon", "state": "IN", "country": "United States"}, {"city": "Lebanon", "state": "OR", "country": ""}, {"city": "Lebec", "state": "CA", "country": ""}, {"city": "Le Center", "state": "MN", "country": ""}, {"city": "<PERSON>", "state": "FL", "country": ""}, {"city": "Leesport", "state": "PA", "country": ""}, {"city": "Le Mars", "state": "IA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IL", "country": "UNITED STATES"}, {"city": "<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ID", "country": ""}, {"city": "Lewisville", "state": "TX", "country": ""}, {"city": "Lexington", "state": "KY", "country": ""}, {"city": "Liberty", "state": "MO", "country": "United States"}, {"city": "Liberty", "state": "SC", "country": ""}, {"city": "Liberty Lake", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Lima", "state": "OH", "country": ""}, {"city": "Lincoln", "state": "IL", "country": ""}, {"city": "Lincoln", "state": "NE", "country": ""}, {"city": "Lincoln", "state": "RI", "country": ""}, {"city": "Lincolnton", "state": "NC", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "UT", "country": ""}, {"city": "Litchfield Park", "state": "AZ", "country": ""}, {"city": "Lithia Springs", "state": "GA", "country": ""}, {"city": "Little Rock", "state": "AR", "country": ""}, {"city": "Littleton", "state": "CO", "country": ""}, {"city": "Littleton", "state": "NH", "country": ""}, {"city": "Livermore", "state": "CA", "country": ""}, {"city": "Liverpool", "state": "NY", "country": ""}, {"city": "Livingston", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "UT", "country": ""}, {"city": "Logan Township", "state": "NJ", "country": ""}, {"city": "Logan Twp", "state": "NJ", "country": ""}, {"city": "Lombard", "state": "IL", "country": ""}, {"city": "London", "state": "KY", "country": ""}, {"city": "London", "state": "ON", "country": ""}, {"city": "Londonderry", "state": "NH", "country": ""}, {"city": "Lone Tree", "state": "CO", "country": ""}, {"city": "Long Beach", "state": "CA", "country": ""}, {"city": "Long Island City", "state": "NY", "country": ""}, {"city": "Longmont", "state": "CO", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "QC", "country": "CANADA"}, {"city": "Longview", "state": "TX", "country": ""}, {"city": "Longview", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OH", "country": ""}, {"city": "Lo<PERSON>", "state": "VA", "country": ""}, {"city": "Los Angeles", "state": "CA", "country": ""}, {"city": "Los Lunas", "state": "NM", "country": ""}, {"city": "Lost Hills", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>(Chichester)", "state": "NH", "country": ""}, {"city": "Louisburg", "state": "NC", "country": ""}, {"city": "Louisville", "state": "CO", "country": ""}, {"city": "Louisville", "state": "KY", "country": ""}, {"city": "Loveland", "state": "CO", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": "United States"}, {"city": "Ludlow", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Lyons", "state": "GA", "country": ""}, {"city": "Lyons", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Madeira Beach", "state": "FL", "country": ""}, {"city": "Madison", "state": "CT", "country": ""}, {"city": "<PERSON>mi <PERSON>", "state": "FL", "country": ""}, {"city": "Malden", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Manasquan", "state": "NJ", "country": ""}, {"city": "Manassas", "state": "VA", "country": ""}, {"city": "Manchester", "state": "CT", "country": ""}, {"city": "Manchester", "state": "PA", "country": ""}, {"city": "Mancos", "state": "CO", "country": ""}, {"city": "Manheim", "state": "PA", "country": ""}, {"city": "Mankato", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Mansfield", "state": "MA", "country": ""}, {"city": "Mansfield", "state": "PA", "country": ""}, {"city": "Mansfield", "state": "TX", "country": ""}, {"city": "Manteca", "state": "CA", "country": ""}, {"city": "Manteno", "state": "IL", "country": ""}, {"city": "Marco Island", "state": "FL", "country": ""}, {"city": "Marina", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "IN", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "Markham", "state": "ON", "country": ""}, {"city": "Marshville", "state": "NC", "country": ""}, {"city": "Martinsville", "state": "VA", "country": ""}, {"city": "<PERSON>", "state": "FL", "country": ""}, {"city": "Marysville", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "Mazomanie", "state": "WI", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Mcclellan Park", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Mcminnville", "state": "OR", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "Mebane", "state": "NC", "country": ""}, {"city": "Mechancsville", "state": "VA", "country": ""}, {"city": "Mechanicsburg", "state": "PA", "country": ""}, {"city": "Medicine Lodge", "state": "KS", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "Medway", "state": "MA", "country": ""}, {"city": "Melbourne", "state": "FL", "country": ""}, {"city": "Memphis", "state": "TN", "country": ""}, {"city": "Memphis", "state": "TN", "country": "UNITED STATES"}, {"city": "Menomonie", "state": "WI", "country": ""}, {"city": "Merced", "state": "CA", "country": ""}, {"city": "Meridian", "state": "ID", "country": ""}, {"city": "Merrillville", "state": "IN", "country": ""}, {"city": "Me<PERSON>mack", "state": "NH", "country": ""}, {"city": "Mesa", "state": "AZ", "country": ""}, {"city": "Miami", "state": "FL", "country": ""}, {"city": "Miami Beach", "state": "FL", "country": ""}, {"city": "Michigan City", "state": "IN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "Middleburg", "state": "FL", "country": ""}, {"city": "Middlebury", "state": "IN", "country": ""}, {"city": "Middle River", "state": "MD", "country": ""}, {"city": "Middletown", "state": "DE", "country": ""}, {"city": "Middletown", "state": "NY", "country": ""}, {"city": "Middletown", "state": "PA", "country": "United States"}, {"city": "Middletown Dc", "state": "PA", "country": "United States"}, {"city": "Midland", "state": "GA", "country": ""}, {"city": "Midlothian", "state": "TX", "country": ""}, {"city": "Mill Creek", "state": "WA", "country": ""}, {"city": "Mill Hall", "state": "PA", "country": ""}, {"city": "Millville", "state": "NJ", "country": ""}, {"city": "Milpitas", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "ON", "country": ""}, {"city": "<PERSON>", "state": "ON", "country": "Canada"}, {"city": "<PERSON>", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "OR", "country": ""}, {"city": "Milwaukee", "state": "WI", "country": ""}, {"city": "Milwaukie", "state": "OR", "country": ""}, {"city": "Minersville", "state": "PA", "country": ""}, {"city": "Minneapolis", "state": "MN", "country": ""}, {"city": "Minnetonka", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ND", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MI", "country": ""}, {"city": "<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Mira <PERSON>", "state": "CA", "country": ""}, {"city": "Miramar Beach", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NB", "country": ""}, {"city": "Mission", "state": "TX", "country": ""}, {"city": "Mississauga", "state": "ON", "country": ""}, {"city": "Missouri City", "state": "TX", "country": "United States"}, {"city": "Modesto", "state": "CA", "country": ""}, {"city": "Monaca", "state": "PA", "country": ""}, {"city": "Moncton", "state": "NB", "country": ""}, {"city": "<PERSON>", "state": "GA", "country": ""}, {"city": "<PERSON>", "state": "LA", "country": ""}, {"city": "<PERSON>", "state": "MI", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "WA", "country": ""}, {"city": "Monroe Township", "state": "NJ", "country": ""}, {"city": "Monrovia", "state": "MD", "country": ""}, {"city": "Monterrey", "state": "NL", "country": "Mexico"}, {"city": "<PERSON>", "state": "AL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "Montreal", "state": "QC", "country": ""}, {"city": "Montreal", "state": "QC", "country": "Canada"}, {"city": "Montreal", "state": "QC", "country": "CANADA"}, {"city": "Montreal-Est", "state": "QC", "country": ""}, {"city": "Montreal-Nord", "state": "QC", "country": ""}, {"city": "Mont-Royal", "state": "QC", "country": ""}, {"city": "<PERSON>", "state": "OK", "country": "United States"}, {"city": "Moorestown", "state": "NJ", "country": ""}, {"city": "Mooresville", "state": "IN", "country": ""}, {"city": "Mooresville", "state": "NC", "country": ""}, {"city": "Moreno Valley", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "Morristown", "state": "NJ", "country": ""}, {"city": "Morristown", "state": "TN", "country": ""}, {"city": "Morrisville", "state": "NC", "country": ""}, {"city": "<PERSON>", "state": "MS", "country": ""}, {"city": "Moses Lake", "state": "WA", "country": ""}, {"city": "Mound", "state": "MN", "country": "United States"}, {"city": "Mount Crawford", "state": "VA", "country": ""}, {"city": "Mount Holly", "state": "NJ", "country": ""}, {"city": "Mount Hope", "state": "ON", "country": ""}, {"city": "Mount Juliet", "state": "TN", "country": ""}, {"city": "Mount Olive", "state": "NJ", "country": ""}, {"city": "Mount Pearl", "state": "NL", "country": ""}, {"city": "Mount Pleasant", "state": "PA", "country": ""}, {"city": "Mount Pocono", "state": "PA", "country": ""}, {"city": "Mount Sterling", "state": "IL", "country": ""}, {"city": "Mount Vernon", "state": "IL", "country": ""}, {"city": "Mount Vernon", "state": "TX", "country": ""}, {"city": "Mount Vernon", "state": "WA", "country": ""}, {"city": "Mt Vernon", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>sboro", "state": "TN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "IA", "country": "United States"}, {"city": "Muskegon", "state": "MI", "country": ""}, {"city": "Muskegon", "state": "MI", "country": "United States"}, {"city": "Myerstown", "state": "PA", "country": ""}, {"city": "Mystic", "state": "CT", "country": ""}, {"city": "Naperville", "state": "IL", "country": ""}, {"city": "Naples", "state": "FL", "country": ""}, {"city": "Naples", "state": "FL", "country": "United States"}, {"city": "Nashua", "state": "NH", "country": ""}, {"city": "Nashville", "state": "IL", "country": ""}, {"city": "Nashville", "state": "TN", "country": ""}, {"city": "Natchitoches", "state": "LA", "country": ""}, {"city": "National City", "state": "CA", "country": ""}, {"city": "National Stock Yards", "state": "IL", "country": ""}, {"city": "Nazareth", "state": "PA", "country": ""}, {"city": "Nepean", "state": "ON", "country": ""}, {"city": "New Albany", "state": "OH", "country": ""}, {"city": "Newark", "state": "DE", "country": ""}, {"city": "Newark", "state": "NJ", "country": ""}, {"city": "New Braunfels", "state": "TX", "country": ""}, {"city": "New Braunfels", "state": "TX", "country": "United States"}, {"city": "Newburyport", "state": "MA", "country": ""}, {"city": "New Caney", "state": "TX", "country": ""}, {"city": "New Caney", "state": "TX", "country": "United States"}, {"city": "New Castle", "state": "DE", "country": ""}, {"city": "New Concord", "state": "OH", "country": ""}, {"city": "New Galilee", "state": "PA", "country": ""}, {"city": "Newington", "state": "NH", "country": ""}, {"city": "New Kensington", "state": "PA", "country": ""}, {"city": "New London", "state": "WI", "country": ""}, {"city": "New Milford", "state": "CT", "country": ""}, {"city": "Newnan", "state": "GA", "country": ""}, {"city": "Newnan", "state": "GA", "country": "United States"}, {"city": "New Orleans", "state": "LA", "country": ""}, {"city": "New Philadelphia", "state": "OH", "country": ""}, {"city": "Newport", "state": "MN", "country": ""}, {"city": "Newport", "state": "RI", "country": ""}, {"city": "New Smyrna Beach", "state": "FL", "country": ""}, {"city": "<PERSON>", "state": "NC", "country": ""}, {"city": "Newville", "state": "PA", "country": ""}, {"city": "New York", "state": "NY", "country": ""}, {"city": "Niagara Falls", "state": "NY", "country": ""}, {"city": "Niagara On The Lake", "state": "ON", "country": ""}, {"city": "<PERSON>", "state": "NY", "country": ""}, {"city": "Niles", "state": "IL", "country": ""}, {"city": "N Las Vegas", "state": "NV", "country": ""}, {"city": "N Little Rock", "state": "AR", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "AZ", "country": ""}, {"city": "Norcross", "state": "GA", "country": ""}, {"city": "Norfolk", "state": "VA", "country": ""}, {"city": "<PERSON>", "state": "OK", "country": ""}, {"city": "Norristown", "state": "PA", "country": ""}, {"city": "Northampton", "state": "PA", "country": ""}, {"city": "North Billerica", "state": "MA", "country": ""}, {"city": "Northborough", "state": "MA", "country": ""}, {"city": "Northbrook", "state": "IL", "country": ""}, {"city": "North Canton", "state": "OH", "country": ""}, {"city": "Northglenn", "state": "CO", "country": ""}, {"city": "North Hollywood", "state": "CA", "country": ""}, {"city": "Northlake", "state": "IL", "country": ""}, {"city": "Northlake", "state": "IL", "country": "United States"}, {"city": "North Las Vegas", "state": "NV", "country": ""}, {"city": "North Salem", "state": "NY", "country": ""}, {"city": "North Salt Lake City", "state": "UT", "country": ""}, {"city": "North Smithfield", "state": "RI", "country": ""}, {"city": "North Vernon", "state": "IN", "country": ""}, {"city": "North York", "state": "ON", "country": ""}, {"city": "<PERSON>", "state": "MA", "country": ""}, {"city": "Norwalk", "state": "CA", "country": ""}, {"city": "Norwalk", "state": "CT", "country": ""}, {"city": "Novi", "state": "MI", "country": ""}, {"city": "Oak Creek", "state": "WI", "country": ""}, {"city": "Oak Harbor", "state": "WA", "country": ""}, {"city": "Oakland", "state": "CA", "country": ""}, {"city": "Oakland", "state": "IA", "country": ""}, {"city": "Oakville", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "OH", "country": ""}, {"city": "Ocean City", "state": "NJ", "country": ""}, {"city": "Oceanside", "state": "CA", "country": ""}, {"city": "O<PERSON>ata", "state": "OK", "country": "United States"}, {"city": "Ocoee", "state": "FL", "country": ""}, {"city": "Oconomowoc", "state": "WI", "country": ""}, {"city": "Odessa", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "UT", "country": ""}, {"city": "Ogdensburg", "state": "NY", "country": ""}, {"city": "Oglesby", "state": "IL", "country": ""}, {"city": "Okeechobee", "state": "FL", "country": ""}, {"city": "Oklahoma City", "state": "OK", "country": ""}, {"city": "Oklahoma City", "state": "OK", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "KS", "country": ""}, {"city": "Old Hickory", "state": "TN", "country": ""}, {"city": "Olive Branch", "state": "MS", "country": ""}, {"city": "Olive Branch", "state": "MS", "country": "United States"}, {"city": "Olympia", "state": "WA", "country": ""}, {"city": "Omaha", "state": "NE", "country": ""}, {"city": "Omaha", "state": "NE", "country": "United States"}, {"city": "Omak", "state": "WA", "country": ""}, {"city": "Oneonta", "state": "NY", "country": ""}, {"city": "Ontario", "state": "CA", "country": ""}, {"city": "Ontario", "state": "OR", "country": ""}, {"city": "Opelika", "state": "AL", "country": ""}, {"city": "Orange", "state": "CA", "country": ""}, {"city": "Orange", "state": "MA", "country": ""}, {"city": "Orange Beach", "state": "AL", "country": ""}, {"city": "Orchard Park", "state": "NY", "country": ""}, {"city": "Oregon City", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON>", "state": "UT", "country": ""}, {"city": "Orestes", "state": "IN", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Orlando", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Oshkosh", "state": "WI", "country": ""}, {"city": "Osterville", "state": "MA", "country": ""}, {"city": "Oswego", "state": "IL", "country": ""}, {"city": "Oswego", "state": "NY", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Ottawa", "state": "IL", "country": ""}, {"city": "Ottawa", "state": "KS", "country": ""}, {"city": "Overbrook", "state": "KS", "country": ""}, {"city": "Overland Park", "state": "KS", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": "United States"}, {"city": "Pacific", "state": "MO", "country": ""}, {"city": "Paducah", "state": "KY", "country": ""}, {"city": "Pagosa Springs", "state": "CO", "country": ""}, {"city": "Palm Desert", "state": "CA", "country": ""}, {"city": "Palmetto", "state": "FL", "country": ""}, {"city": "Palm Springs", "state": "CA", "country": ""}, {"city": "Palmyra", "state": "ME", "country": ""}, {"city": "Panama City Beach", "state": "FL", "country": ""}, {"city": "Paramount", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "Paris", "state": "KY", "country": "United States"}, {"city": "Park City", "state": "UT", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "Parkersburg", "state": "WV", "country": ""}, {"city": "Park Forest", "state": "IL", "country": ""}, {"city": "Parsippany", "state": "NJ", "country": ""}, {"city": "Pasadena", "state": "TX", "country": ""}, {"city": "Pasadena", "state": "TX", "country": "United States"}, {"city": "Pasco", "state": "WA", "country": ""}, {"city": "Paso Robles", "state": "CA", "country": ""}, {"city": "Patchogue (East)", "state": "NY", "country": ""}, {"city": "Paterson", "state": "WA", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "Pauls Valley", "state": "OK", "country": ""}, {"city": "Pauls Valley", "state": "OK", "country": "United States"}, {"city": "Pawtucket", "state": "RI", "country": ""}, {"city": "<PERSON>", "state": "MA", "country": ""}, {"city": "Pearland", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": "MS", "country": ""}, {"city": "Pendergrass", "state": "GA", "country": ""}, {"city": "Pennsauken", "state": "NJ", "country": ""}, {"city": "Penn Yan", "state": "NY", "country": ""}, {"city": "Pensacola", "state": "FL", "country": ""}, {"city": "Pensacola Beach", "state": "FL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Perth Amboy", "state": "NJ", "country": ""}, {"city": "Petaluma", "state": "CA", "country": ""}, {"city": "Petersburg", "state": "VA", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "Pflugerville", "state": "TX", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Philadelphia", "state": "PA", "country": ""}, {"city": "Philapelphia", "state": "PA", "country": ""}, {"city": "Phillipsburg", "state": "NJ", "country": ""}, {"city": "Phoenix", "state": "AZ", "country": ""}, {"city": "Pickering", "state": "ON", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IN", "country": ""}, {"city": "Pierrefonds", "state": "QC", "country": ""}, {"city": "Pitt Meadows", "state": "BC", "country": ""}, {"city": "Pittsburg", "state": "KS", "country": "United States"}, {"city": "Pittsburgh", "state": "PA", "country": ""}, {"city": "<PERSON>ston", "state": "PA", "country": ""}, {"city": "<PERSON>nti<PERSON>", "state": "CA", "country": ""}, {"city": "Plainfield", "state": "CT", "country": ""}, {"city": "Plainfield", "state": "IL", "country": "United States"}, {"city": "Plainfield", "state": "IN", "country": ""}, {"city": "Plainville", "state": "MA", "country": ""}, {"city": "Plano", "state": "TX", "country": ""}, {"city": "Plano", "state": "TX", "country": "United States"}, {"city": "Plant City", "state": "FL", "country": ""}, {"city": "Plattsburgh", "state": "NY", "country": ""}, {"city": "Pleasant Grove", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "WI", "country": ""}, {"city": "Pleasant Prairie", "state": "WI", "country": ""}, {"city": "Plymouth", "state": "MA", "country": ""}, {"city": "Plymouth", "state": "MI", "country": ""}, {"city": "Plymouth", "state": "MN", "country": ""}, {"city": "Plymouth", "state": "NH", "country": ""}, {"city": "Plymouth", "state": "NS", "country": ""}, {"city": "<PERSON>ly<PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "Poca", "state": "WV", "country": ""}, {"city": "Point Comfort", "state": "TX", "country": "United States"}, {"city": "Pointe<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Pomona", "state": "CA", "country": ""}, {"city": "Pompano Beach", "state": "FL", "country": ""}, {"city": "Ponca City", "state": "OK", "country": ""}, {"city": "Ponca City", "state": "OK", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "ID", "country": ""}, {"city": "Port Coquitlam", "state": "BC", "country": ""}, {"city": "Porterville", "state": "CA", "country": ""}, {"city": "Portland", "state": "ME", "country": ""}, {"city": "Portland", "state": "OR", "country": ""}, {"city": "Portland", "state": "OR", "country": "USA"}, {"city": "Portland", "state": "TN", "country": ""}, {"city": "Port Orange", "state": "FL", "country": ""}, {"city": "Port Orchard", "state": "WA", "country": ""}, {"city": "Port Reading", "state": "NJ", "country": ""}, {"city": "Portsmouth", "state": "OH", "country": ""}, {"city": "Portsmouth", "state": "RI", "country": ""}, {"city": "Portsmouth", "state": "VA", "country": ""}, {"city": "Port Washington", "state": "NY", "country": ""}, {"city": "Port Wentworth", "state": "GA", "country": ""}, {"city": "Post Falls", "state": "ID", "country": ""}, {"city": "Poteau", "state": "OK", "country": "United States"}, {"city": "Potsdam", "state": "NY", "country": ""}, {"city": "Pottstown", "state": "PA", "country": ""}, {"city": "Pottsville", "state": "PA", "country": ""}, {"city": "Poulsbo", "state": "WA", "country": ""}, {"city": "Poway", "state": "CA", "country": ""}, {"city": "Presque Isle", "state": "ME", "country": ""}, {"city": "Princeton", "state": "IL", "country": ""}, {"city": "Princeton", "state": "MN", "country": ""}, {"city": "Proctor", "state": "WV", "country": ""}, {"city": "Prosperity", "state": "SC", "country": ""}, {"city": "Providence", "state": "RI", "country": ""}, {"city": "Provo", "state": "UT", "country": ""}, {"city": "<PERSON>", "state": "CT", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Quakertown", "state": "PA", "country": ""}, {"city": "Quapaw", "state": "OK", "country": ""}, {"city": "Quebec", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": ""}, {"city": "Raleigh", "state": "NC", "country": ""}, {"city": "<PERSON>", "state": "CU", "country": "Mexico"}, {"city": "Rancho Cordova", "state": "CA", "country": ""}, {"city": "Rancho Cucamong", "state": "CA", "country": ""}, {"city": "Rancho Cucamonga", "state": "CA", "country": ""}, {"city": "Rancho Dominguez", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "NJ", "country": ""}, {"city": "Rapid City", "state": "SD", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "Reading", "state": "PA", "country": ""}, {"city": "Reading(Sinking Spg)", "state": "PA", "country": ""}, {"city": "Redlands", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Redondo Beach", "state": "CA", "country": ""}, {"city": "Red Wing", "state": "MN", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Regina", "state": "SK", "country": ""}, {"city": "Reidsville", "state": "NC", "country": ""}, {"city": "Reno", "state": "NV", "country": ""}, {"city": "Ren<PERSON>", "state": "WA", "country": ""}, {"city": "Repentigny", "state": "QC", "country": ""}, {"city": "Revere", "state": "MA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Richburg", "state": "SC", "country": ""}, {"city": "Richibucto", "state": "NB", "country": ""}, {"city": "Richland", "state": "WA", "country": ""}, {"city": "Richmond", "state": "BC", "country": ""}, {"city": "Richmond", "state": "BC", "country": "Canada"}, {"city": "Richmond", "state": "CA", "country": ""}, {"city": "Richmond", "state": "IN", "country": ""}, {"city": "Richmond", "state": "TX", "country": ""}, {"city": "Richmond", "state": "VA", "country": ""}, {"city": "Richmond Hill", "state": "ON", "country": ""}, {"city": "Ridgefield", "state": "NJ", "country": "United States"}, {"city": "Ridgefield", "state": "WA", "country": "United States"}, {"city": "Ridgeland", "state": "MS", "country": ""}, {"city": "Rincon", "state": "GA", "country": ""}, {"city": "Rio Rancho", "state": "NM", "country": ""}, {"city": "Rio Rico", "state": "AZ", "country": ""}, {"city": "Riverdale", "state": "UT", "country": ""}, {"city": "River Grove", "state": "IL", "country": ""}, {"city": "Riverhead", "state": "NY", "country": ""}, {"city": "Riverside", "state": "CA", "country": ""}, {"city": "Riverside", "state": "CA", "country": "United States"}, {"city": "Riverton", "state": "UT", "country": ""}, {"city": "Riviera Beach", "state": "FL", "country": ""}, {"city": "Roanoke", "state": "IN", "country": "United States"}, {"city": "Roanoke", "state": "TX", "country": "United States"}, {"city": "Roanoke", "state": "VA", "country": ""}, {"city": "Robbinsville", "state": "NJ", "country": ""}, {"city": "Rochester", "state": "NY", "country": ""}, {"city": "Rochester (Greece)", "state": "NY", "country": ""}, {"city": "Rockford", "state": "OH", "country": ""}, {"city": "Rock Hill", "state": "CT", "country": ""}, {"city": "Rock Hill", "state": "SC", "country": ""}, {"city": "Rockingham", "state": "VA", "country": ""}, {"city": "Rockmart", "state": "GA", "country": ""}, {"city": "Rockwall", "state": "TX", "country": ""}, {"city": "Rocky Hill", "state": "CT", "country": ""}, {"city": "Rockyview", "state": "AB", "country": ""}, {"city": "Rocky View", "state": "AB", "country": ""}, {"city": "Rockyview Count", "state": "AB", "country": ""}, {"city": "<PERSON>", "state": "AR", "country": ""}, {"city": "<PERSON>", "state": "AR", "country": "United States"}, {"city": "<PERSON>", "state": "MN", "country": ""}, {"city": "Rome", "state": "GA", "country": ""}, {"city": "Rome", "state": "NY", "country": ""}, {"city": "Romeoville", "state": "IL", "country": ""}, {"city": "Romulus", "state": "MI", "country": ""}, {"city": "Rosemont", "state": "IL", "country": ""}, {"city": "Roseville", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Rowland Heights", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Roxbury", "state": "MA", "country": "United States"}, {"city": "Royal Oaks", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NM", "country": ""}, {"city": "<PERSON><PERSON><PERSON>(E.Prov)", "state": "RI", "country": ""}, {"city": "Russellville", "state": "AR", "country": ""}, {"city": "Russellville", "state": "KY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "VA", "country": "United States"}, {"city": "Sacramento", "state": "CA", "country": ""}, {"city": "Saint-Bruno", "state": "QC", "country": "Canada"}, {"city": "<PERSON>", "state": "MO", "country": ""}, {"city": "Saint Clair Shores", "state": "MI", "country": ""}, {"city": "Saint <PERSON>sville", "state": "OH", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Saint-Jean-Sur-Riche", "state": "QC", "country": ""}, {"city": "Saint-Lambert-D", "state": "QC", "country": ""}, {"city": "<PERSON>", "state": "QC", "country": ""}, {"city": "Saint<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Saint<PERSON><PERSON>", "state": "QC", "country": "Canada"}, {"city": "<PERSON>", "state": "QC", "country": ""}, {"city": "Saint<PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "Saint Louis", "state": "MO", "country": ""}, {"city": "Saint Paul", "state": "MN", "country": ""}, {"city": "Saint Paul", "state": "OR", "country": ""}, {"city": "Saint Petersburg", "state": "FL", "country": ""}, {"city": "Saint-Roch-De-Lachig", "state": "QC", "country": ""}, {"city": "Saint Simons Island", "state": "GA", "country": ""}, {"city": "Salem", "state": "CT", "country": ""}, {"city": "Salem", "state": "MA", "country": ""}, {"city": "Salem", "state": "NC", "country": ""}, {"city": "Salem", "state": "NH", "country": ""}, {"city": "Salem", "state": "NJ", "country": ""}, {"city": "Salem", "state": "OR", "country": ""}, {"city": "Salem", "state": "VA", "country": ""}, {"city": "Salinas", "state": "CA", "country": ""}, {"city": "Salisbury", "state": "NC", "country": ""}, {"city": "Salt Lake City", "state": "UT", "country": ""}, {"city": "San Angelo", "state": "TX", "country": ""}, {"city": "San Antonio", "state": "TX", "country": ""}, {"city": "San Antonio", "state": "TX", "country": "United States"}, {"city": "San Bernardino", "state": "CA", "country": ""}, {"city": "San Diego", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "UT", "country": ""}, {"city": "San Fernando", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "ME", "country": ""}, {"city": "San Francisco", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TX", "country": "United States"}, {"city": "San Jose", "state": "CA", "country": ""}, {"city": "San Juan Bautis", "state": "CA", "country": ""}, {"city": "San Leandro", "state": "CA", "country": ""}, {"city": "San Lorenzo", "state": "CA", "country": ""}, {"city": "San Luis Obispo", "state": "CA", "country": ""}, {"city": "San Luis Potosi", "state": "SL", "country": ""}, {"city": "San Marcos", "state": "TX", "country": ""}, {"city": "Santa Ana", "state": "CA", "country": ""}, {"city": "Santa Ana", "state": "CA", "country": "United States"}, {"city": "Santa Barbara", "state": "CA", "country": ""}, {"city": "Santa Clara", "state": "CA", "country": ""}, {"city": "Santa Clara", "state": "CA", "country": "United States"}, {"city": "Santa Fe", "state": "NM", "country": ""}, {"city": "Santa Fe Spring", "state": "CA", "country": ""}, {"city": "Santa Fe Springs", "state": "CA", "country": ""}, {"city": "Santa Maria", "state": "CA", "country": ""}, {"city": "Santa Monica", "state": "CA", "country": ""}, {"city": "Santa Monica", "state": "CA", "country": "United States"}, {"city": "Sapulpa", "state": "OK", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "FL", "country": ""}, {"city": "Sarasota Springs", "state": "UT", "country": ""}, {"city": "Saskatoon", "state": "SK", "country": ""}, {"city": "Saugus", "state": "MA", "country": ""}, {"city": "Savannah", "state": "GA", "country": ""}, {"city": "Scarborough", "state": "ON", "country": ""}, {"city": "Schaumburg", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "TX", "country": ""}, {"city": "Sc<PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Schulenburg", "state": "TX", "country": ""}, {"city": "Sc<PERSON>ylerville", "state": "NY", "country": ""}, {"city": "Scotia(Glenville)", "state": "NY", "country": ""}, {"city": "Scranton", "state": "PA", "country": ""}, {"city": "Seabrook", "state": "NH", "country": ""}, {"city": "Seabrook", "state": "TX", "country": ""}, {"city": "<PERSON>ford", "state": "DE", "country": ""}, {"city": "Seattle", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "NC", "country": ""}, {"city": "<PERSON><PERSON>", "state": "TN", "country": ""}, {"city": "Senatobia", "state": "MS", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Severn", "state": "MD", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OK", "country": "United States"}, {"city": "Sheboygan", "state": "WI", "country": ""}, {"city": "<PERSON>", "state": "MI", "country": ""}, {"city": "<PERSON>", "state": "OR", "country": ""}, {"city": "Shippensburg", "state": "PA", "country": ""}, {"city": "Shoemakersville", "state": "PA", "country": ""}, {"city": "Shoemakersville", "state": "VA", "country": ""}, {"city": "<PERSON>wood", "state": "IL", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "Silverdale", "state": "WA", "country": ""}, {"city": "Simpsonville", "state": "SC", "country": ""}, {"city": "Sioux City", "state": "IA", "country": ""}, {"city": "Sioux Falls", "state": "SD", "country": ""}, {"city": "Smiths Grove", "state": "KY", "country": ""}, {"city": "Snohomish", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Somerset", "state": "NJ", "country": ""}, {"city": "Somerset", "state": "PA", "country": ""}, {"city": "Somerset(Branchburg)", "state": "NJ", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NH", "country": ""}, {"city": "Somerville", "state": "MA", "country": ""}, {"city": "Somerville", "state": "NJ", "country": ""}, {"city": "Sonoita", "state": "AZ", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "QC", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Southaven", "state": "MS", "country": "United States"}, {"city": "South Bay", "state": "FL", "country": ""}, {"city": "South Bend", "state": "IN", "country": ""}, {"city": "South Boston", "state": "VA", "country": ""}, {"city": "Southbridge", "state": "MA", "country": ""}, {"city": "South Brunswick", "state": "NJ", "country": ""}, {"city": "South Burlington", "state": "VT", "country": ""}, {"city": "South Chesterfi", "state": "VA", "country": ""}, {"city": "South Dennis", "state": "MA", "country": ""}, {"city": "South El Monte", "state": "CA", "country": ""}, {"city": "Southhampton", "state": "ON", "country": ""}, {"city": "South Plainfiel", "state": "NJ", "country": ""}, {"city": "South San Franc", "state": "CA", "country": ""}, {"city": "Spanaway", "state": "WA", "country": ""}, {"city": "Spanish Fork", "state": "UT", "country": ""}, {"city": "Sparks", "state": "NV", "country": ""}, {"city": "Spartanburg", "state": "SC", "country": ""}, {"city": "<PERSON>", "state": "WV", "country": ""}, {"city": "Spokane", "state": "WA", "country": ""}, {"city": "Spokane Valley", "state": "WA", "country": ""}, {"city": "Spring", "state": "TX", "country": ""}, {"city": "Springfield", "state": "MA", "country": ""}, {"city": "Springfield", "state": "OH", "country": ""}, {"city": "Springville", "state": "NY", "country": ""}, {"city": "Springville", "state": "UT", "country": ""}, {"city": "Stafford", "state": "VA", "country": ""}, {"city": "St Alsip", "state": "IL", "country": ""}, {"city": "Staten Island", "state": "NY", "country": ""}, {"city": "Statesville", "state": "NC", "country": ""}, {"city": "St Augustine", "state": "FL", "country": ""}, {"city": "Staunton", "state": "VA", "country": ""}, {"city": "St Bruno", "state": "QC", "country": ""}, {"city": "St Charles", "state": "IL", "country": ""}, {"city": "Ste Foy", "state": "QC", "country": ""}, {"city": "Stephanville", "state": "NL", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "<PERSON>", "state": "KS", "country": "United States"}, {"city": "<PERSON>", "state": "VA", "country": ""}, {"city": "Steubenville", "state": "OH", "country": ""}, {"city": "St Eustache", "state": "QC", "country": ""}, {"city": "Stillwater", "state": "OK", "country": "United States"}, {"city": "St Jerome", "state": "QC", "country": ""}, {"city": "St Johns", "state": "NL", "country": ""}, {"city": "St Laurent", "state": "QC", "country": ""}, {"city": "St Louis", "state": "MO", "country": ""}, {"city": "St. Louis", "state": "MO", "country": ""}, {"city": "St Marys", "state": "ON", "country": ""}, {"city": "Stockton", "state": "CA", "country": ""}, {"city": "Stockton", "state": "CA", "country": "United States"}, {"city": "Stoney Creek", "state": "ON", "country": ""}, {"city": "Stoney Creek", "state": "ON", "country": "Canada"}, {"city": "Stratford", "state": "ON", "country": ""}, {"city": "Streetsboro", "state": "OH", "country": ""}, {"city": "Streetsboro", "state": "OH", "country": "United States"}, {"city": "Sturtevant", "state": "WI", "country": ""}, {"city": "Suffolk", "state": "VA", "country": ""}, {"city": "Sugar City", "state": "ID", "country": ""}, {"city": "Sugar Land", "state": "TX", "country": ""}, {"city": "Sulphur Springs", "state": "TX", "country": ""}, {"city": "Summerside", "state": "PE", "country": ""}, {"city": "Summerville", "state": "SC", "country": ""}, {"city": "Summit", "state": "IL", "country": ""}, {"city": "<PERSON>", "state": "WA", "country": ""}, {"city": "Sunbury", "state": "OH", "country": ""}, {"city": "Sunderland", "state": "MA", "country": ""}, {"city": "Sunnyside", "state": "WA", "country": ""}, {"city": "Surrey", "state": "BC", "country": ""}, {"city": "Surrey", "state": "BC", "country": "CANADA"}, {"city": "Sussex", "state": "NB", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "GA", "country": ""}, {"city": "Swedesboro", "state": "NJ", "country": ""}, {"city": "Sweetwater", "state": "FL", "country": ""}, {"city": "Syracuse", "state": "NY", "country": ""}, {"city": "Tacoma", "state": "WA", "country": ""}, {"city": "Tallahassee", "state": "FL", "country": ""}, {"city": "Tamarac", "state": "FL", "country": ""}, {"city": "Tampa", "state": "FL", "country": ""}, {"city": "Tampa", "state": "FL", "country": "United States"}, {"city": "Tannersville", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "MA", "country": ""}, {"city": "Tecumseh", "state": "ON", "country": ""}, {"city": "Telford", "state": "PA", "country": ""}, {"city": "Temple", "state": "TX", "country": ""}, {"city": "Temple", "state": "TX", "country": "United States"}, {"city": "Temple Terrace", "state": "FL", "country": ""}, {"city": "Terminal Island", "state": "CA", "country": ""}, {"city": "Terrebonne", "state": "QC", "country": ""}, {"city": "Terre Haute", "state": "IN", "country": "United States"}, {"city": "Texarkana", "state": "AR", "country": ""}, {"city": "Texarkana", "state": "TX", "country": ""}, {"city": "Thermal", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ME", "country": ""}, {"city": "Thomasville", "state": "GA", "country": ""}, {"city": "<PERSON>", "state": "CO", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Thunder Bay", "state": "ON", "country": ""}, {"city": "Tift<PERSON>", "state": "GA", "country": ""}, {"city": "Tigard", "state": "OR", "country": ""}, {"city": "Tillsonburg", "state": "ON", "country": ""}, {"city": "Tinker Afb", "state": "OK", "country": "United States"}, {"city": "Tipp City", "state": "OH", "country": ""}, {"city": "<PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "AZ", "country": ""}, {"city": "Tompkinsville", "state": "KY", "country": ""}, {"city": "<PERSON><PERSON>", "state": "UT", "country": ""}, {"city": "<PERSON><PERSON>", "state": "PA", "country": ""}, {"city": "Toronto", "state": "ON", "country": ""}, {"city": "Toronto", "state": "ON", "country": "Canada"}, {"city": "Torrance", "state": "CA", "country": ""}, {"city": "Torrington", "state": "CT", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "Tremont", "state": "PA", "country": ""}, {"city": "Trenton", "state": "ON", "country": ""}, {"city": "Trois-Rivieres", "state": "QC", "country": ""}, {"city": "Troutdale", "state": "OR", "country": ""}, {"city": "Troy", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CT", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "OR", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "OR", "country": "United States"}, {"city": "<PERSON><PERSON><PERSON>", "state": "OR", "country": "UNITED STATES"}, {"city": "<PERSON>", "state": "GA", "country": ""}, {"city": "Tucson", "state": "AZ", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Tulsa", "state": "OK", "country": ""}, {"city": "Tulsa", "state": "OK", "country": "United States"}, {"city": "Tultit<PERSON>", "state": "EM", "country": ""}, {"city": "Tumwater", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Twin Falls", "state": "ID", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": "United States"}, {"city": "<PERSON>", "state": "IA", "country": ""}, {"city": "Union", "state": "MO", "country": ""}, {"city": "Union Gap", "state": "WA", "country": ""}, {"city": "University Park", "state": "IL", "country": ""}, {"city": "Utica", "state": "NY", "country": ""}, {"city": "Uxbridge", "state": "MA", "country": ""}, {"city": "Valcourt", "state": "QC", "country": ""}, {"city": "Valdese", "state": "NC", "country": ""}, {"city": "Valdosta", "state": "GA", "country": ""}, {"city": "Valencia", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Van <PERSON> Township", "state": "MI", "country": ""}, {"city": "Vancouver", "state": "BC", "country": ""}, {"city": "Vancouver", "state": "WA", "country": ""}, {"city": "Vandalia", "state": "OH", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": ""}, {"city": "Varennes", "state": "QC", "country": ""}, {"city": "Varennes", "state": "QC", "country": "Canada"}, {"city": "Vaudreuil-Dorion", "state": "QC", "country": ""}, {"city": "<PERSON>", "state": "ON", "country": ""}, {"city": "<PERSON>", "state": "ON", "country": ""}, {"city": "Venice", "state": "CA", "country": ""}, {"city": "Ventura", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "BC", "country": ""}, {"city": "<PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "CT", "country": ""}, {"city": "Vero Beach", "state": "FL", "country": ""}, {"city": "Versailles", "state": "KY", "country": ""}, {"city": "Vestal", "state": "NY", "country": ""}, {"city": "Victoria", "state": "TX", "country": ""}, {"city": "Vineland", "state": "NJ", "country": ""}, {"city": "Virginia Beach", "state": "VA", "country": ""}, {"city": "Visalia", "state": "CA", "country": ""}, {"city": "Vista", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "AL", "country": ""}, {"city": "Wadsworth", "state": "OH", "country": ""}, {"city": "Waite Park", "state": "MN", "country": ""}, {"city": "Walkerville", "state": "MI", "country": ""}, {"city": "Walnut", "state": "CA", "country": ""}, {"city": "Wareham", "state": "MA", "country": ""}, {"city": "Warners", "state": "NY", "country": ""}, {"city": "<PERSON>", "state": "OH", "country": "United States"}, {"city": "Warrensville He", "state": "OH", "country": ""}, {"city": "Warrensville Hts", "state": "OH", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OR", "country": ""}, {"city": "Warsaw", "state": "NY", "country": ""}, {"city": "Warwick", "state": "RI", "country": ""}, {"city": "Washington", "state": "DC", "country": ""}, {"city": "Washington", "state": "IN", "country": ""}, {"city": "Washington", "state": "PA", "country": ""}, {"city": "Washington", "state": "WV", "country": ""}, {"city": "Watertown", "state": "NY", "country": ""}, {"city": "Watsonville", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "WI", "country": ""}, {"city": "Waverly", "state": "NY", "country": ""}, {"city": "Waverly", "state": "PA", "country": ""}, {"city": "Waxahachie", "state": "TX", "country": ""}, {"city": "Wayland", "state": "MA", "country": ""}, {"city": "<PERSON>", "state": "NJ", "country": ""}, {"city": "<PERSON>", "state": "NY", "country": ""}, {"city": "<PERSON>", "state": "TX", "country": ""}, {"city": "Webster City", "state": "IA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "ON", "country": ""}, {"city": "Wellford", "state": "SC", "country": ""}, {"city": "<PERSON>", "state": "ME", "country": ""}, {"city": "<PERSON>", "state": "NV", "country": ""}, {"city": "Wentzville", "state": "MO", "country": ""}, {"city": "Westbrook", "state": "CT", "country": ""}, {"city": "Westbury", "state": "NY", "country": ""}, {"city": "West Chester", "state": "OH", "country": "United States"}, {"city": "West Chicago", "state": "IL", "country": ""}, {"city": "West Deptford", "state": "NJ", "country": ""}, {"city": "Westfield", "state": "MA", "country": ""}, {"city": "West Hollywood", "state": "CA", "country": ""}, {"city": "West Jefferson", "state": "OH", "country": ""}, {"city": "West Jordan", "state": "UT", "country": ""}, {"city": "<PERSON><PERSON>", "state": "LA", "country": "UNITED STATES"}, {"city": "Westminster", "state": "CO", "country": ""}, {"city": "Westmoreland", "state": "NH", "country": ""}, {"city": "Weston", "state": "WV", "country": ""}, {"city": "West Palm Beach", "state": "FL", "country": ""}, {"city": "Westport", "state": "CT", "country": ""}, {"city": "West Sacramento", "state": "CA", "country": ""}, {"city": "West Schulte Road", "state": "CA", "country": ""}, {"city": "West Valley", "state": "UT", "country": ""}, {"city": "West Valley City", "state": "UT", "country": ""}, {"city": "West Warwick", "state": "RI", "country": ""}, {"city": "Wetumpka", "state": "AL", "country": ""}, {"city": "Weymouth", "state": "MA", "country": ""}, {"city": "Wheatley", "state": "ON", "country": ""}, {"city": "Whippany", "state": "NJ", "country": ""}, {"city": "W<PERSON>by", "state": "ON", "country": ""}, {"city": "White", "state": "GA", "country": ""}, {"city": "Whitefish", "state": "MT", "country": ""}, {"city": "Whitehall", "state": "OH", "country": ""}, {"city": "White Hall", "state": "AR", "country": ""}, {"city": "Whiteland", "state": "IN", "country": ""}, {"city": "White Salmon", "state": "WA", "country": ""}, {"city": "Whitestown", "state": "IN", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "<PERSON>", "state": "PA", "country": ""}, {"city": "Williamsburg", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "SC", "country": ""}, {"city": "Wilmington", "state": "IL", "country": ""}, {"city": "Wilmington", "state": "IL", "country": "United States"}, {"city": "Wilmington", "state": "MA", "country": ""}, {"city": "Wilmington", "state": "NC", "country": ""}, {"city": "Wilsonville", "state": "OR", "country": ""}, {"city": "Winchester", "state": "KY", "country": ""}, {"city": "Winchester", "state": "MA", "country": ""}, {"city": "Winchester", "state": "VA", "country": ""}, {"city": "Windsor", "state": "CO", "country": "United States"}, {"city": "Windsor", "state": "ON", "country": ""}, {"city": "Windsor", "state": "ON", "country": "Canada"}, {"city": "Windsor", "state": "WI", "country": ""}, {"city": "<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "Winnebago", "state": "IL", "country": ""}, {"city": "Winnipeg", "state": "MB", "country": ""}, {"city": "Winnipeg", "state": "MB", "country": "CANADA"}, {"city": "Winston Salem", "state": "NC", "country": ""}, {"city": "Winston-Salem", "state": "NC", "country": ""}, {"city": "Witlon", "state": "NY", "country": ""}, {"city": "Woburn", "state": "MA", "country": ""}, {"city": "Woodbridge", "state": "ON", "country": ""}, {"city": "Woodbridge", "state": "VA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "OR", "country": ""}, {"city": "<PERSON>", "state": "IL", "country": ""}, {"city": "Woodhaven", "state": "MI", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "CA", "country": ""}, {"city": "Woodland", "state": "CA", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IL", "country": ""}, {"city": "<PERSON><PERSON>", "state": "IL", "country": "United States"}, {"city": "Woodstock", "state": "ON", "country": ""}, {"city": "Woodsville", "state": "NH", "country": ""}, {"city": "Wood Village", "state": "OR", "country": ""}, {"city": "Woonsocket", "state": "RI", "country": ""}, {"city": "Worcester", "state": "MA", "country": ""}, {"city": "W Palm Beach", "state": "FL", "country": ""}, {"city": "Ya<PERSON><PERSON>", "state": "WA", "country": ""}, {"city": "<PERSON><PERSON><PERSON>", "state": "NY", "country": ""}, {"city": "York", "state": "PA", "country": ""}, {"city": "Yukon", "state": "OK", "country": ""}, {"city": "Yukon", "state": "OK", "country": "United States"}, {"city": "<PERSON><PERSON>", "state": "AZ", "country": ""}, {"city": "Zanesville", "state": "OH", "country": ""}, {"city": "Zephyrhills", "state": "FL", "country": ""}]