package timezone

import (
	"context"
	"encoding/json"
	"os"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetTimezone(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		city          string
		state         string
		expectedTZ    string
		expectedError bool
	}{
		{"New York", "New York", "NY", "America/New_York", false},
		{"New York City", "New York City", "NY", "America/New_York", false},
		{"Los Angeles", "Los Angeles", "CA", "America/Los_Angeles", false},
		{"Chicago", "Chicago", "IL", "America/Chicago", false},
		{"Case Insensitive", "NEW YORK CITY", "ny", "America/New_York", false},
		{"Not Found", "NonExistent", "ZZ", "", true},
		{"Chantilly", "Chantilly", "VA", "America/New_York", false},
		{"Fairfax", "Fairfax", "VA", "America/New_York", false},
		{"Boston", "Boston", "MA", "America/New_York", false},
		{"Camden", "Camden", "NJ", "America/New_York", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tz, err := GetTimezone(ctx, tt.city, tt.state, "United States")

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTZ, tz)
			}
		})
	}
}

func TestLoadCityTimezones(t *testing.T) {
	ctx := context.Background()

	expectedCities := []struct {
		city  string
		state string
		tz    string
	}{
		{"New York City", "NY", "America/New_York"},
		{"Los Angeles", "CA", "America/Los_Angeles"},
		{"Chicago", "IL", "America/Chicago"},
	}

	for _, city := range expectedCities {
		tz, err := GetTimezone(ctx, city.city, city.state, "United States")
		assert.NoError(t, err)
		assert.Equal(t, city.tz, tz)
	}
}

func TestGetTimezoneThreadSafety(t *testing.T) {
	ctx := context.Background()

	concurrency := 100
	done := make(chan bool)

	for i := 0; i < concurrency; i++ {
		go func() {
			tz, err := GetTimezone(ctx, "New York City", "NY", "United States")
			assert.NoError(t, err)
			assert.Equal(t, "America/New_York", tz)
			done <- true
		}()
	}

	for i := 0; i < concurrency; i++ {
		<-done
	}
}

func TestConvertToTimezone(t *testing.T) {
	laLoc, err := time.LoadLocation("America/Los_Angeles")
	require.NoError(t, err)

	nyLoc, err := time.LoadLocation("America/New_York")
	require.NoError(t, err)

	tests := []struct {
		name        string
		dt          time.Time
		timezone    string
		expectedRes time.Time
		expectErr   bool
	}{
		{
			name:        "UTC - Expect No Change",
			dt:          time.Date(2024, time.June, 30, 12, 0, 0, 0, time.UTC),
			timezone:    "UTC",
			expectedRes: time.Date(2024, time.June, 30, 12, 0, 0, 0, time.UTC),
			expectErr:   false,
		},
		{
			name:        "Valid Timezone PST",
			dt:          time.Date(2024, time.June, 30, 12, 0, 0, 0, time.UTC),
			timezone:    "America/Los_Angeles",
			expectedRes: time.Date(2024, time.June, 30, 12, 0, 0, 0, laLoc),
			expectErr:   false,
		},
		{
			name:        "Valid Timezone EST",
			dt:          time.Date(2024, time.June, 30, 12, 0, 0, 0, time.UTC),
			timezone:    "America/New_York",
			expectedRes: time.Date(2024, time.June, 30, 12, 0, 0, 0, nyLoc),
			expectErr:   false,
		},
		{
			name:        "Invalid Timezone",
			dt:          time.Date(2024, time.June, 30, 12, 0, 0, 0, time.UTC),
			timezone:    "Invalid/Timezone",
			expectedRes: time.Time{},
			expectErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			res, err := DenormalizeUTC(tt.dt, tt.timezone)
			if tt.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
			assert.True(t, res.Equal(tt.expectedRes))
		})
	}
}

type CityState struct {
	City    string `json:"city"`
	State   string `json:"state"`
	Country string `json:"country"`
}

func TestAdditionalCities(t *testing.T) {
	ctx := context.Background()

	warehouses, err := os.ReadFile("warehouses.json")
	assert.NoError(t, err)

	var cityStates []CityState
	err = json.Unmarshal(warehouses, &cityStates)
	assert.NoError(t, err)

	var failureCount int32

	for _, cs := range cityStates {
		t.Run(cs.City+", "+cs.State, func(t *testing.T) {
			tz, err := GetTimezone(ctx, cs.City, cs.State, cs.Country)
			if err != nil || tz == "" {
				atomic.AddInt32(&failureCount, 1)
				t.Errorf("Failed to get timezone for %s, %s %s. Error: %v",
					cs.City, cs.State, cs.Country, err)
			}
		})
	}

	t.Logf("Total failures: %d out of %d city-state pairs", failureCount, len(cityStates))

	if failureCount > 0 {
		t.Errorf("Failed to get timezones for %d city-state pairs", failureCount)
	}
}
