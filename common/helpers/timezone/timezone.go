package timezone

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
)

type CityInfo struct {
	City, State, Timezone string
}

var (
	stateCapitals, mexicoTimezones map[string]string
	canadaTimezones                map[string][]string
)

func init() {
	stateCapitals = map[string]string{
		"al": "America/Chicago",              // Montgomery, Alabama
		"ak": "America/Anchorage",            // Juneau, Alaska
		"az": "America/Phoenix",              // Phoenix, Arizona
		"ar": "America/Chicago",              // Little Rock, Arkansas
		"ca": "America/Los_Angeles",          // Sacramento, California
		"co": "America/Denver",               // Denver, Colorado
		"ct": "America/New_York",             // Hartford, Connecticut
		"de": "America/New_York",             // Dover, Delaware
		"fl": "America/New_York",             // Tallahassee, Florida
		"ga": "America/New_York",             // Atlanta, Georgia
		"hi": "Pacific/Honolulu",             // Honolulu, Hawaii
		"id": "America/Boise",                // Boise, Idaho
		"il": "America/Chicago",              // Springfield, Illinois
		"in": "America/Indiana/Indianapolis", // Indianapolis, Indiana
		"ia": "America/Chicago",              // Des Moines, Iowa
		"ks": "America/Chicago",              // Topeka, Kansas
		"ky": "America/New_York",             // Frankfort, Kentucky
		"la": "America/Chicago",              // Baton Rouge, Louisiana
		"me": "America/New_York",             // Augusta, Maine
		"md": "America/New_York",             // Annapolis, Maryland
		"ma": "America/New_York",             // Boston, Massachusetts
		"mi": "America/Detroit",              // Lansing, Michigan
		"mn": "America/Chicago",              // St. Paul, Minnesota
		"ms": "America/Chicago",              // Jackson, Mississippi
		"mo": "America/Chicago",              // Jefferson City, Missouri
		"mt": "America/Denver",               // Helena, Montana
		"ne": "America/Chicago",              // Lincoln, Nebraska
		"nv": "America/Los_Angeles",          // Carson City, Nevada
		"nh": "America/New_York",             // Concord, New Hampshire
		"nj": "America/New_York",             // Trenton, New Jersey
		"nm": "America/Denver",               // Santa Fe, New Mexico
		"ny": "America/New_York",             // Albany, New York
		"nc": "America/New_York",             // Raleigh, North Carolina
		"nd": "America/Chicago",              // Bismarck, North Dakota
		"oh": "America/New_York",             // Columbus, Ohio
		"ok": "America/Chicago",              // Oklahoma City, Oklahoma
		"or": "America/Los_Angeles",          // Salem, Oregon
		"pa": "America/New_York",             // Harrisburg, Pennsylvania
		"ri": "America/New_York",             // Providence, Rhode Island
		"sc": "America/New_York",             // Columbia, South Carolina
		"sd": "America/Chicago",              // Pierre, South Dakota
		"tn": "America/Chicago",              // Nashville, Tennessee
		"tx": "America/Chicago",              // Austin, Texas
		"ut": "America/Denver",               // Salt Lake City, Utah
		"vt": "America/New_York",             // Montpelier, Vermont
		"va": "America/New_York",             // Richmond, Virginia
		"wa": "America/Los_Angeles",          // Olympia, Washington
		"wv": "America/New_York",             // Charleston, West Virginia
		"wi": "America/Chicago",              // Madison, Wisconsin
		"wy": "America/Denver",               // Cheyenne, Wyoming
	}

	canadaTimezones = map[string][]string{
		"ab": {"America/Edmonton"},
		"bc": {"America/Vancouver", "America/Dawson_Creek", "America/Fort_Nelson", "America/Creston"},
		"mb": {"America/Winnipeg"},
		"nb": {"America/Moncton"},
		"nl": {"America/St_Johns", "America/Goose_Bay"},
		"ns": {"America/Halifax"},
		"nt": {"America/Yellowknife", "America/Inuvik"},
		"nu": {"America/Iqaluit", "America/Rankin_Inlet", "America/Cambridge_Bay"},
		"on": {"America/Toronto", "America/Nipigon", "America/Rainy_River", "America/Atikokan"},
		"pe": {"America/Halifax"},
		"qc": {"America/Montreal", "America/Blanc-Sablon"},
		"sk": {"America/Regina", "America/Swift_Current"},
		"yt": {"America/Whitehorse", "America/Dawson"},
	}

	mexicoTimezones = map[string]string{
		"ag": "America/Mexico_City", // Aguascalientes
		"bc": "America/Tijuana",     // Baja California
		"bs": "America/Mazatlan",    // Baja California Sur
		"cm": "America/Merida",      // Campeche
		"cs": "America/Mexico_City", // Chiapas
		"ch": "America/Chihuahua",   // Chihuahua
		"cx": "America/Mexico_City", // Mexico City
		"co": "America/Matamoros",   // Coahuila
		"cl": "America/Mexico_City", // Colima
		"dg": "America/Monterrey",   // Durango
		"gt": "America/Mexico_City", // Guanajuato
		"gr": "America/Mexico_City", // Guerrero
		"hg": "America/Mexico_City", // Hidalgo
		"ja": "America/Mexico_City", // Jalisco
		"em": "America/Mexico_City", // México
		"mi": "America/Mexico_City", // Michoacán
		"mo": "America/Mexico_City", // Morelos
		"na": "America/Mazatlan",    // Nayarit
		"nl": "America/Monterrey",   // Nuevo León
		"oa": "America/Mexico_City", // Oaxaca
		"pu": "America/Mexico_City", // Puebla
		"qe": "America/Mexico_City", // Querétaro
		"qr": "America/Cancun",      // Quintana Roo
		"sl": "America/Mexico_City", // San Luis Potosí
		"si": "America/Mazatlan",    // Sinaloa
		"so": "America/Hermosillo",  // Sonora
		"tb": "America/Mexico_City", // Tabasco
		"tm": "America/Matamoros",   // Tamaulipas
		"tl": "America/Mexico_City", // Tlaxcala
		"ve": "America/Mexico_City", // Veracruz
		"yu": "America/Merida",      // Yucatán
		"za": "America/Mexico_City", // Zacatecas
	}
}

func getCanadaTimezone(province string) (string, error) {
	timezones, ok := canadaTimezones[province]
	if !ok {
		return "", fmt.Errorf("unknown province: %s", province)
	}

	return timezones[0], nil
}

// GetTimezone returns IANA timezone (e.g. America/New_York) for a given city, state, and (optionally) country.
func GetTimezone(ctx context.Context, city, state, country string) (string, error) {
	city, state, country = strings.TrimSpace(strings.ToLower(city)), strings.TrimSpace(strings.ToLower(state)),
		strings.TrimSpace(strings.ToLower(country))

	switch country {
	case "mexico":
		if tz, ok := mexicoTimezones[state]; ok {
			return tz, nil
		}

		return "America/Mexico_City", nil

	case "canada":
		if tz, err := getCanadaTimezone(state); err == nil {
			return tz, nil
		}

		return "America/Toronto", nil

	case "", "us", "usa", "united states", "united states of america":
		key := fmt.Sprintf("%s, %s", city, state)

		// try city-state pair lookup
		if tz, ok := usCityTimezones[key]; ok {
			return tz, nil
		}

		// fallback on capital's timezone
		if tz, ok := stateCapitals[state]; ok {
			log.Infof(ctx, "Using capital timezone for %s, %s", city, state, zap.String("timezone", tz))
			return tz, nil
		}

		// special case
		if key == "washington, dc," {
			return "America/New_York", nil
		}

		if country == "" {
			if tz, err := getCanadaTimezone(state); err == nil {
				return tz, nil
			}

			if tz, ok := mexicoTimezones[state]; ok {
				return tz, nil
			}
		}
	}

	return "", fmt.Errorf("timezone not found for %s, %s, %s", city, state, country)
}

// GetTimezoneByZipOrCity returns IANA timezone (e.g. America/New_York) for a given zipcode AND/OR city, state.
// If zipcode is provided, it takes precedence over city and state.
func GetTimezoneByZipOrCity(ctx context.Context, zip, city, state, country string) (string, error) {
	if len(strings.TrimSpace(zip)) >= 5 {
		return helpers.AwsGetTimezoneFromZip(ctx, zip, "zip")
	}

	return GetTimezone(ctx, city, state, country)
}

// GetLocationByCity converts city, state and (optionally) country to a *time.Location object.
func GetLocationByCity(ctx context.Context, city, state, country string) (*time.Location, error) {
	tzName, err := GetTimezone(ctx, city, state, country)
	if err != nil {
		return nil, err
	}

	return time.LoadLocation(tzName)
}

// Converts a 3-4 letter abbreviation (e.g. "MST" or "UTC") to a time.Location object.
//
// This is only necessary for timezone formats that include a tz abbreviation instead of a UTC offset.
func GetLocationByAbbrv(zone string) (*time.Location, error) {
	name := zone

	switch zone {
	case "HST":
		name = "Pacific/Honolulu"
	case "AKST", "AKDT":
		name = "America/Anchorage"
	case "PST", "PDT":
		name = "America/Los_Angeles"
	case "MST", "MDT":
		name = "America/Denver"
	case "CST", "CDT":
		name = "America/Chicago"
	case "EST", "EDT":
		name = "America/New_York"
	}

	// NOTE: "UTC" is natively understood by time.LoadLocation
	return time.LoadLocation(name)
}

// Convert timezone-agnostic timestamps to the warehouse's locale,
// where `timezone` param is the IANA name (e.g. America/New_York).
// EX: load.Pickup.ApptStartTime in Aljex is originally 2024-07-01T10:00:00Z and the location is Boston.
// Conversion means that pickupApptTime is actually 2024-07-01T10:00:0-04:00 (EDT).
func DenormalizeUTC(dt time.Time, timezone string) (time.Time, error) {
	dt = dt.In(time.UTC)

	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return time.Time{}, fmt.Errorf("error loading location %s: %w", timezone, err)
	}

	res := time.Date(dt.Year(), dt.Month(), dt.Day(), dt.Hour(), dt.Minute(), dt.Second(), dt.Nanosecond(), loc)

	return res, nil
}

// Converts a timezone-specific timestamp to timezone agnostic.
// EX: load.Pickup.ApptStartTime is originally 2024-07-01T10:00:0-04:00 (EDT).
// This function will convert it to 2024-07-01T10:00:00Z.
// Helpful for if a data source provides timestamps in RFC3339 standard ways, but another source expects a string
// already in local time.
//
// If IANA `origTimezone` param is provided, then function first converts `dt.In(origTimezone)` then normalizes to UTC.
// If empty, then `dt` is normalized directly.
func NormalizeToUTC(dt time.Time, origTimezone string) (time.Time, error) {
	if origTimezone != "" {
		loc, err := time.LoadLocation(origTimezone)
		if err != nil {
			return time.Time{}, fmt.Errorf("error loading location %s: %w", origTimezone, err)
		}

		dt = dt.In(loc)
	}

	res := time.Date(dt.Year(), dt.Month(), dt.Day(), dt.Hour(), dt.Minute(), dt.Second(), dt.Nanosecond(), time.UTC)

	return res, nil
}
