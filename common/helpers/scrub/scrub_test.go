package scrub

import (
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestScrubbedURLString(t *testing.T) {
	scrubbed := URL(&url.URL{
		Scheme:   "https",
		Host:     "example.com",
		RawQuery: "name=axle&apiKey=secret-key&username=root&password=secret-pass",
	})

	expected := url.Values{
		"name":     []string{"axle"},
		"apiKey":   []string{"redacted-10"},
		"username": []string{"root"},
		"password": []string{"redacted-11"},
	}
	assert.Equal(t, expected, scrubbed.Query())
}
