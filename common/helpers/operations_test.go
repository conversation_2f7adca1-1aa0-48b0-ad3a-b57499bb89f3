package helpers

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestOr(t *testing.T) {
	t.Run("Or with strings", func(t *testing.T) {
		result := Or("", "default")
		assert.Equal(t, "default", result)

		result = Or("non-empty", "default")
		assert.Equal(t, "non-empty", result)
	})

	t.Run("Or with integers", func(t *testing.T) {
		result := Or(0, 10)
		assert.Equal(t, 10, result)

		result = Or(5, 10)
		assert.Equal(t, 5, result)
	})

	t.Run("Or with booleans", func(t *testing.T) {
		result := Or(false, true)
		assert.Equal(t, true, result)

		result = Or(true, false)
		assert.Equal(t, true, result)
	})

	t.Run("Or with floats", func(t *testing.T) {
		result := Or(0.0, 3.14)
		assert.Equal(t, 3.14, result)

		result = Or(2.71, 3.14)
		assert.Equal(t, 2.71, result)
	})
}

func TestOrNullTime(t *testing.T) {
	someTime1 := time.Date(2025, time.February, 14, 8, 0, 0, 0, time.UTC)
	someTime2 := time.Date(2025, time.January, 8, 13, 0, 0, 0, time.UTC)
	zeroTime := time.Time{}

	t.Run("First value is valid", func(t *testing.T) {
		a := models.NullTime{Valid: true, Time: someTime1}
		b := models.NullTime{Valid: true, Time: someTime2}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, a.Time, result.Time)
	})

	t.Run("Second value is valid", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: true, Time: someTime2}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, b.Time, result.Time)
	})

	t.Run("Both values are not valid and zero", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: false, Time: zeroTime}

		result := OrNullTime(a, b)

		require.False(t, result.Valid)
		assert.Equal(t, zeroTime, result.Time)
	})

	t.Run("Both values are not valid but non-zero", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: someTime1}
		b := models.NullTime{Valid: false, Time: someTime2}

		result := OrNullTime(a, b)

		require.False(t, result.Valid)
		assert.Equal(t, someTime2, result.Time)
	})

	t.Run("First value is not valid, second has zero-time but valid", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: true, Time: zeroTime}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, zeroTime, result.Time)
	})
}

func TestDeduplicate(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected any
	}{
		// Integer tests
		{
			name:     "empty int slice",
			input:    []int{},
			expected: []int{},
		},
		{
			name:     "single int element",
			input:    []int{1},
			expected: []int{1},
		},
		{
			name:     "no int duplicates",
			input:    []int{1, 2, 3, 4, 5},
			expected: []int{1, 2, 3, 4, 5},
		},
		{
			name:     "all int duplicates",
			input:    []int{1, 1, 1, 1, 1},
			expected: []int{1},
		},
		{
			name:     "int duplicates at beginning",
			input:    []int{1, 1, 2, 3, 4},
			expected: []int{1, 2, 3, 4},
		},
		{
			name:     "int duplicates at end",
			input:    []int{1, 2, 3, 4, 4},
			expected: []int{1, 2, 3, 4},
		},
		{
			name:     "int duplicates in middle",
			input:    []int{1, 2, 2, 3, 4},
			expected: []int{1, 2, 3, 4},
		},
		{
			name:     "scattered int duplicates",
			input:    []int{1, 2, 1, 3, 2, 4, 1, 5},
			expected: []int{1, 2, 3, 4, 5},
		},
		{
			name:     "int with zeros",
			input:    []int{0, 1, 0, 2, 1, 0},
			expected: []int{0, 1, 2},
		},
		{
			name:     "negative int numbers",
			input:    []int{-1, 0, 1, -1, 2, 0, -2},
			expected: []int{-1, 0, 1, 2, -2},
		},
		// String tests
		{
			name:     "empty string slice",
			input:    []string{},
			expected: []string{},
		},
		{
			name:     "empty strings",
			input:    []string{"", "", "a", "b", ""},
			expected: []string{"", "a", "b"},
		},
		{
			name:     "case sensitive strings",
			input:    []string{"Hello", "hello", "HELLO", "World"},
			expected: []string{"Hello", "hello", "HELLO", "World"},
		},
		{
			name:     "mixed string content",
			input:    []string{"apple", "banana", "apple", "cherry", "banana", "date"},
			expected: []string{"apple", "banana", "cherry", "date"},
		},
		// Order preservation test
		{
			name:     "order preservation",
			input:    []int{3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5},
			expected: []int{3, 1, 4, 5, 9, 2, 6},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			switch input := tt.input.(type) {
			case []int:
				expected := tt.expected.([]int)
				result := Deduplicate(input)
				assert.Equal(t, expected, result)

				// For order preservation test, verify first occurrence is preserved
				if tt.name == "order preservation" {
					for _, val := range result {
						firstOccurrence := -1
						for j, inputVal := range input {
							if inputVal == val {
								firstOccurrence = j
								break
							}
						}
						require.NotEqual(t, -1, firstOccurrence, "Element %d not found in input", val)

						// Check that no earlier occurrence exists
						for j := 0; j < firstOccurrence; j++ {
							assert.NotEqual(t, val, input[j], "Element %d appears earlier in input at index %d", val, j)
						}
					}
				}
			case []string:
				expected := tt.expected.([]string)
				result := Deduplicate(input)
				assert.Equal(t, expected, result)
			}
		})
	}
}

func TestNormalizeZipCode(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "9-digit ZIP with dash",
			input:    "44904-9327",
			expected: "44904",
		},
		{
			name:     "9-digit ZIP with space",
			input:    "44904 9327",
			expected: "44904",
		},
		{
			name:     "5-digit ZIP",
			input:    "44904",
			expected: "44904",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "ZIP with leading/trailing spaces",
			input:    "  44904-9327  ",
			expected: "44904",
		},
		{
			name:     "ZIP with multiple delimiters",
			input:    "44904-9327-1234",
			expected: "44904",
		},
		{
			name:     "9-digit ZIP without delimiter",
			input:    "449049327",
			expected: "44904",
		},
		{
			name:     "Short ZIP (less than 5 digits)",
			input:    "1234",
			expected: "1234",
		},
		{
			name:     "Canadian postal code format",
			input:    "M5H 2N2",
			expected: "M5H",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizeZipCode(tt.input)
			if result != tt.expected {
				t.Errorf("NormalizeZipCode(%q) = %q; want %q", tt.input, result, tt.expected)
			}
		})
	}
}
