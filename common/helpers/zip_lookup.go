package helpers

import (
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
)

type (
	CityStateLookupRequest struct {
		UserID  string `xml:"USERID,attr"`
		ZipCode struct {
			Zip5 string `xml:"Zip5"`
		} `xml:"ZipCode"`
	}

	CityStateLookupResponse struct {
		ZipCode struct {
			Zip5  string        `xml:"Zip5"`
			City  string        `xml:"City"`
			State string        `xml:"State"`
			Error *ErrorDetails `xml:"Error"`
		} `xml:"ZipCode"`
	}

	ZipcodeLookupRequest struct {
		City  string `json:"city"`
		State string `json:"state"`
	}

	ZipcodeLookupResponse struct {
		ResultStatus string    `json:"resultStatus"`
		City         string    `json:"city"`
		State        string    `json:"state"`
		ZipList      []Ziplist `json:"zipList"`
	}

	Ziplist struct {
		Zip5 string `json:"zip5"`
	}

	ErrorDetails struct {
		Description string `xml:"Description"`
	}
)

// const uspsURL = "https://tools.usps.com/tools/app/ziplookup/zipByCityState"

// LookupZipByCityState retrieves the ZIP code for a given city and state
// This used to implement the USPS API, but now we use AWS Location Service due to failing USPS lookups
func LookupZipCodeByCityState(ctx context.Context, city, state string) (zipcode string, err error) {

	awsLocation, err := AwsLocationLookup(ctx, city, state, "")
	if err != nil {
		return "", fmt.Errorf("error looking up location: %w", err)
	}

	locationPlace := *awsLocation.Results[0].Place

	if locationPlace.PostalCode == nil {
		return "", fmt.Errorf("no zip code found for city/state: %s, %s", city, state)
	}

	return *locationPlace.PostalCode, nil
}

// Lookup city and state by zipcode via USPS API
// https://www.usps.com/business/web-tools-apis/address-information-api.htm#_Toc110511824
func LookupCityStateByZipcode(
	ctx context.Context,
	zipCode string,
	userID string,
) (res CityStateLookupResponse, err error) {

	request := CityStateLookupRequest{
		UserID: userID,
		ZipCode: struct {
			Zip5 string `xml:"Zip5"`
		}{
			Zip5: zipCode,
		},
	}

	requestBody, err := xml.Marshal(request)
	if err != nil {
		err = fmt.Errorf("error marshaling request: %w", err)

		return res, err
	}

	const apiURL = "https://secure.shippingapis.com/ShippingAPI.dll"
	const apiAction = "CityStateLookup"

	queryParams := url.Values{}
	queryParams.Set("API", apiAction)
	queryParams.Set("XML", string(requestBody))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, apiURL, strings.NewReader(queryParams.Encode()))
	if err != nil {
		return res, fmt.Errorf("error building POST request: %w", err)
	}
	req.Header.Set("Accept", "application/xml")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return res, fmt.Errorf("error sending POST request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return res, fmt.Errorf("error reading response: %w", err)
	}
	respBodyStr := string(respBody)

	if resp.StatusCode >= http.StatusBadRequest {
		return res, fmt.Errorf("state lookup returned %d: %s", resp.StatusCode, respBodyStr)
	}

	if err := xml.Unmarshal(respBody, &res); err != nil {
		log.Debug(ctx, "resp body", zap.String("body", respBodyStr))
		return res, fmt.Errorf("error unmarshaling response: %w", err)
	}

	if res.ZipCode.Error != nil {
		return res, fmt.Errorf(
			"city-state lookup error: %s, zip input: %s",
			res.ZipCode.Error.Description,
			zipCode,
		)
	}

	return res, nil
}
