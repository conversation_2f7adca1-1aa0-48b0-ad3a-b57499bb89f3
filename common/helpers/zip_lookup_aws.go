package helpers

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

// AwsGetTimezoneFromZip uses AWS to look up city/state by a zip code and
// returns the IANA timezone name from the response (e.g. America/New_York).
func AwsGetTimezoneFromZip(ctx context.Context, zip, label string) (string, error) {
	location, err := AwsLocationLookup(ctx, "", "", zip)
	if err != nil || location == nil {
		log.Error(
			ctx,
			fmt.Sprintf("error looking up %s zipcode with AWS Location", label),
			zap.String("zip", zip),
			zap.Error(err),
		)

		return "", err
	}

	if len(location.Results) == 0 {
		return "", fmt.Errorf("no results found for %s zip code %q", label, zip)
	}

	return *location.Results[0].Place.TimeZone.Name, nil
}

// GetZipOrLookup returns the given zip code if non-empty; otherwise,
// it tries to look up a zip code by (city, state) from free USPS API.
func GetZipOrLookup(ctx context.Context, zip, city, state string) (string, error) {
	if zip != "" {
		return zip, nil
	}

	zip, err := LookupZipCodeByCityState(ctx, city, state)
	if err != nil {
		return "", err
	}
	if len(zip) == 0 {
		return "", fmt.Errorf("could not find any zip code for city=%q, state=%q", city, state)
	}

	return zip, nil
}
