package helpers

import (
	"context"
	"strings"
	"testing"
)

func TestStateAbbreviationConversion(t *testing.T) {
	ctx := context.Background()

	t.Run("GetStateFullName", func(t *testing.T) {
		// Combine all known abbreviations into one map for thorough testing.
		allAbbreviations := make(map[string]string)
		for k, v := range usaStateAbbreviations {
			allAbbreviations[k] = v
		}
		for k, v := range canadaProvinceAbbreviations {
			allAbbreviations[k] = v
		}

		// Test every known abbreviation.
		for abbr, expectedName := range allAbbreviations {
			// Test uppercase (standard).
			if got := GetStateFullName(ctx, abbr); got != expectedName {
				t.Errorf("GetStateFullName(%q) = %q; want %q", abbr, got, expectedName)
			}
			// Test lowercase to ensure case-insensitivity.
			if got := GetStateFullName(ctx, strings.ToLower(abbr)); got != expectedName {
				t.Errorf("GetStateFullName(%q) = %q; want %q", strings.To<PERSON><PERSON><PERSON>(abbr), got, expectedName)
			}
		}

		// Explicitly test the problematic case.
		if got := GetStateFullName(ctx, "DC"); got != "District of Columbia" {
			t.Errorf("GetStateFullName(\"DC\") = %q; want \"District of Columbia\"", got)
		}

		// Test an unknown abbreviation.
		if got := GetStateFullName(ctx, "XX"); got != "Unknown State" {
			t.Errorf("GetStateFullName(\"XX\") = %q; want \"Unknown State\"", got)
		}

		// Test an empty abbreviation.
		if got := GetStateFullName(ctx, ""); got != "Unknown State" {
			t.Errorf("GetStateFullName(\"\") = %q; want \"Unknown State\"", got)
		}
	})

	t.Run("GetStateAbbreviation", func(t *testing.T) {
		// Invert the maps for reverse lookup testing.
		allFullNames := make(map[string]string)
		for k, v := range usaStateAbbreviations {
			allFullNames[v] = k
		}
		for k, v := range canadaProvinceAbbreviations {
			allFullNames[v] = k
		}

		// Test every known full name.
		for name, expectedAbbr := range allFullNames {
			// Test with original casing.
			if got := GetStateAbbreviation(ctx, name); got != expectedAbbr {
				t.Errorf("GetStateAbbreviation(%q) = %q; want %q", name, got, expectedAbbr)
			}
			// Test with all lowercase to ensure case-insensitivity.
			if got := GetStateAbbreviation(ctx, strings.ToLower(name)); got != expectedAbbr {
				t.Errorf("GetStateAbbreviation(%q) = %q; want %q", strings.ToLower(name), got, expectedAbbr)
			}
			// Test with all uppercase to ensure case-insensitivity.
			if got := GetStateAbbreviation(ctx, strings.ToUpper(name)); got != expectedAbbr {
				t.Errorf("GetStateAbbreviation(%q) = %q; want %q", strings.ToUpper(name), got, expectedAbbr)
			}
		}

		// Explicitly test the problematic case.
		if got := GetStateAbbreviation(ctx, "District of Columbia"); got != "DC" {
			t.Errorf("GetStateAbbreviation(\"District of Columbia\") = %q; want \"DC\"", got)
		}

		// Test an unknown name.
		if got := GetStateAbbreviation(ctx, "Atlantis"); got != "Unknown State" {
			t.Errorf("GetStateAbbreviation(\"Atlantis\") = %q; want \"Unknown State\"", got)
		}

		// Test an empty name.
		if got := GetStateAbbreviation(ctx, ""); got != "Unknown State" {
			t.Errorf("GetStateAbbreviation(\"\") = %q; want \"Unknown State\"", got)
		}
	})
}
