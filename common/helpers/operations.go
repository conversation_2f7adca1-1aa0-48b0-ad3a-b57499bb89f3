package helpers

import (
	"strings"

	"golang.org/x/exp/constraints"

	"github.com/drumkitai/drumkit/common/models"
)

func Min[T constraints.Ordered](a, b T) T {
	if a < b {
		return a
	}
	return b
}

// Max returns the maximum of two values of an ordered type.
func Max[T constraints.Ordered](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// Or returns the first non-zero value out of two provided values, similar to JS's ?? operator
func Or[T comparable](a, b T) T {
	var zero T
	if a != zero {
		return a
	}
	return b
}

// OrNullTime returns the first non-zero value out of two provided values, similar to JS's ?? operator.
// Defined separately from Or() because cases like `models.NullTime{Time: time.Time{}.In(someLoc), Valid: false}`
// where the struct was technically not entirely empty, `Or` would select that non-valid NullTime obj instead of
// the second, actually valid datetime object.
func OrNullTime(a, b models.NullTime) models.NullTime {
	if a.Valid {
		return a
	}
	return b
}

func Ternary[T any](cond bool, a, b T) T {
	if cond {
		return a
	}
	return b
}

func Deduplicate[T comparable](sliceList []T) []T {
	allKeys := make(map[T]bool)
	list := []T{}

	for _, item := range sliceList {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

func IsBlank(s string) bool {
	return strings.TrimSpace(s) == ""
}

// NormalizeZipCode extracts only the 5-digit ZIP code from a ZIP+4 format.
// Examples: "12345-6789" -> "12345", "12345 6789" -> "12345", "12345" -> "12345"
func NormalizeZipCode(zipCode string) string {
	if zipCode == "" {
		return ""
	}

	// Trim whitespace
	zipCode = strings.TrimSpace(zipCode)

	// Split on common delimiters (dash, space)
	// and return only the first part (5-digit ZIP)
	for _, delimiter := range []string{"-", " "} {
		if idx := strings.Index(zipCode, delimiter); idx > 0 {
			return zipCode[:idx]
		}
	}

	// If already 5 digits or less, return as-is
	if len(zipCode) <= 5 {
		return zipCode
	}

	// If more than 5 digits without delimiter, take first 5
	return zipCode[:5]
}
