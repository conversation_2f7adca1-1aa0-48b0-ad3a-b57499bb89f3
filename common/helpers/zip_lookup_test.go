package helpers

import (
	"context"
	"os"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLookupCityStateByZipcode(t *testing.T) {
	if os.Getenv("LIVE_TEST") != "true" {
		t.<PERSON><PERSON>("skipping service TestLookupCityStateByZipcode: run with LIVE_TEST=true to enable")
		return
	}

	ctx := context.Background()

	if err := godotenv.Load(); err != nil {
		t.Log("info: no .env file found")
	}
	userID := os.Getenv("USPS_USER_ID")

	t.Run("Boston", func(t *testing.T) {
		resp, err := LookupCityStateByZipcode(ctx, "02116", userID)
		require.NoError(t, err)
		assert.Equal(t, "BOSTON", resp.ZipCode.City)
		assert.Equal(t, "MA", resp.ZipCode.State)
		assert.Equal(t, "02116", resp.ZipCode.Zip5)

	})

	t.Run("Empty zip", func(t *testing.T) {
		resp, err := LookupCityStateByZipcode(ctx, "", userID)
		require.Error(t, err)
		assert.Empty(t, resp)
	})

	t.Run("Canada", func(t *testing.T) {
		resp, err := LookupCityStateByZipcode(ctx, "H1A 0A0", userID)
		require.Error(t, err)
		assert.Empty(t, resp)
	})

}
