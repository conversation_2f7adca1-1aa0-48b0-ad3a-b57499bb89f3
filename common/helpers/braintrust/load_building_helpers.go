package braintrustutil

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// This file contains helper functions for normalizing load building suggestion changes into extractor models
// that are used for submitting braintrust logs. This is allows us to compare suggested and applied changes,
// score the initial LLM response, and submit that feedback to Braintrust.

func SubmitBraintrustLoadBuildingFeedback(ctx context.Context, suggestion models.SuggestedLoadChange) error {
	if len(suggestion.BraintrustLogIDs) == 0 {
		return errors.New("no braintrust log records found for load building suggestion")
	}

	var err error
	var expected, suggested any

	for _, logID := range suggestion.BraintrustLogIDs {

		switch logID.ProjectStepName {
		case string(braintrustsdk.LBGetBasicInfo):
			expected, suggested = NormalizeLoadBasicInfo(suggestion)

		case string(braintrustsdk.LBGetCustomer):
			expected, suggested = NormalizeLoadCustomer(suggestion)

		case string(braintrustsdk.LBGetPickup):
			expected, suggested = NormalizeLoadPickup(suggestion)

		case string(braintrustsdk.LBGetConsignee):
			expected, suggested = NormalizeLoadConsignee(suggestion)

		case string(braintrustsdk.LBGetRateData):
			expected, suggested = NormalizeLoadRateData(suggestion)

		case string(braintrustsdk.LBGetSpecifications):
			expected, suggested = NormalizeLoadSpecifications(suggestion)

		case string(braintrustsdk.LBGetCommoditiesAndRefs):
			expected, suggested = NormalizeLoadCommoditiesAndRefs(suggestion)

		default:
			log.Warn(
				ctx,
				"unknown braintrust log ID, you likely need to add it to the switch statement",
				zap.String("logID", logID.ProjectStepName),
				zap.Uint("suggestionID", suggestion.ID),
			)
		}

		err = braintrustsdk.SubmitFeedback(
			ctx,
			braintrustsdk.LoadBuildingProjectID,
			braintrustsdk.BuildFeedbackRequestBody(ctx, logID.ID, expected, suggested),
		)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}
	}

	return nil
}

// UpdateAppliedSuggestionWithLoad updates the applied changes in a suggestion with a load's real data,
// enabling us to compare suggested and applied changes.
// Suggestion is a pointer so we can modify the applied changes in place
func UpdateAppliedSuggestionWithLoad(load models.Load, suggestion *models.SuggestedLoadChange) {
	suggestion.Applied = models.SuggestedChanges{
		LoadChanges: &models.LoadChanges{
			ExternalTMSID:     load.ExternalTMSID,
			FreightTrackingID: load.FreightTrackingID,
			Mode:              load.Mode,
			MoreThanTwoStops:  load.MoreThanTwoStops,
			Customer:          load.Customer,
			RateData:          load.RateData,
			Pickup:            load.Pickup,
			Consignee:         load.Consignee,
			Commodities:       load.Commodities,
			Specifications: models.SuggestedSpecifications{
				ServiceType:         load.Specifications.ServiceType,
				TransportType:       load.Specifications.TransportType,
				TransportSize:       load.Specifications.TransportSize,
				TotalInPalletCount:  json.Number(strconv.Itoa(load.Specifications.TotalInPalletCount)),
				TotalOutPalletCount: json.Number(strconv.Itoa(load.Specifications.TotalOutPalletCount)),
				TotalPieces:         load.Specifications.TotalPieces,
				Commodities:         load.Specifications.Commodities,
				NumCommodities:      load.Specifications.NumCommodities,
				TotalWeight:         load.Specifications.TotalWeight,
				NetWeight:           load.Specifications.NetWeight,
				BillableWeight:      load.Specifications.BillableWeight,
				TotalDistance:       load.Specifications.TotalDistance,
				MinTempFahrenheit:   float64(load.Specifications.MinTempFahrenheit),
				MaxTempFahrenheit:   float64(load.Specifications.MaxTempFahrenheit),
			},
		},
	}
}

// ----- Helpers that transform `models.LoadChanges` substructs to `extractor` models for Braintrust evaluation -----

// Load Basic Info helpers
func normalizeLoadBasicInfo(obj models.LoadChanges) extractor.BasicInfo {
	return extractor.BasicInfo{
		MoreThanTwoStops: obj.MoreThanTwoStops,
		RefNumber:        obj.Customer.RefNumber,
	}
}

func NormalizeLoadBasicInfo(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.BasicInfo, expected extractor.BasicInfo) {
	return normalizeLoadBasicInfo(*suggestion.Suggested.LoadChanges),
		normalizeLoadBasicInfo(*suggestion.Applied.LoadChanges)
}

// Load Customer helpers
func normalizeLoadCustomer(obj models.LoadChanges) extractor.CustomerInfo {
	return extractor.CustomerInfo{
		ShipperName:         obj.Customer.Name,
		OriginalSenderEmail: obj.Customer.Email,
	}
}

func NormalizeLoadCustomer(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.CustomerInfo, expected extractor.CustomerInfo) {
	return normalizeLoadCustomer(*suggestion.Suggested.LoadChanges),
		normalizeLoadCustomer(*suggestion.Applied.LoadChanges)
}

// Load Pickup helpers
func normalizeLoadPickup(obj models.LoadChanges) extractor.PickupInfo {
	return extractor.PickupInfo{
		Pickup: models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{
				AddressLine1:  obj.Pickup.AddressLine1,
				AddressLine2:  obj.Pickup.AddressLine2,
				City:          obj.Pickup.City,
				Contact:       obj.Pickup.Contact,
				Country:       obj.Pickup.Country,
				Email:         obj.Pickup.Email,
				ExternalTMSID: obj.Pickup.ExternalTMSID,
				Name:          obj.Pickup.Name,
				Phone:         obj.Pickup.Phone,
				State:         obj.Pickup.State,
				Zipcode:       obj.Pickup.Zipcode,
			},
			ApptEndTime: models.NullTime{
				Time:  obj.Pickup.ApptEndTime.Time,
				Valid: true,
			},
			ApptNote:     obj.Pickup.ApptNote,
			ApptRequired: obj.Pickup.ApptRequired,
			ApptStartTime: models.NullTime{
				Time:  obj.Pickup.ApptStartTime.Time,
				Valid: true,
			},
			ApptConfirmed:     obj.Pickup.ApptConfirmed,
			ApptType:          obj.Pickup.ApptType,
			BusinessHours:     obj.Pickup.BusinessHours,
			ExternalTMSStopID: obj.Pickup.ExternalTMSStopID,
			ReadyTime: models.NullTime{
				Time:  obj.Pickup.ReadyTime.Time,
				Valid: true,
			},
			RefNumber: obj.Pickup.RefNumber,
			Timezone:  obj.Pickup.Timezone,
		},
	}
}

func NormalizeLoadPickup(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.PickupInfo, expected extractor.PickupInfo) {
	return normalizeLoadPickup(*suggestion.Suggested.LoadChanges),
		normalizeLoadPickup(*suggestion.Applied.LoadChanges)
}

// Load Consignee helpers
func normalizeLoadConsignee(obj models.LoadChanges) extractor.ConsigneeInfo {
	return extractor.ConsigneeInfo{
		Consignee: models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{
				AddressLine1:  obj.Consignee.AddressLine1,
				AddressLine2:  obj.Consignee.AddressLine2,
				City:          obj.Consignee.City,
				Contact:       obj.Consignee.Contact,
				Country:       obj.Consignee.Country,
				Email:         obj.Consignee.Email,
				ExternalTMSID: obj.Consignee.ExternalTMSID,
				Name:          obj.Consignee.Name,
				Phone:         obj.Consignee.Phone,
				State:         obj.Consignee.State,
				Zipcode:       obj.Consignee.Zipcode,
			},
			ApptEndTime: models.NullTime{
				Time:  obj.Consignee.ApptEndTime.Time,
				Valid: true,
			},
			ApptNote:     obj.Consignee.ApptNote,
			ApptRequired: obj.Consignee.ApptRequired,
			ApptStartTime: models.NullTime{
				Time:  obj.Consignee.ApptStartTime.Time,
				Valid: true,
			},
			ApptType:          obj.Consignee.ApptType,
			BusinessHours:     obj.Consignee.BusinessHours,
			ExternalTMSStopID: obj.Consignee.ExternalTMSStopID,
			MustDeliver: models.NullTime{
				Time:  obj.Consignee.MustDeliver.Time,
				Valid: true,
			},
			RefNumber: obj.Consignee.RefNumber,
			Timezone:  obj.Consignee.Timezone,
		},
	}
}

func NormalizeLoadConsignee(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.ConsigneeInfo, expected extractor.ConsigneeInfo) {
	return normalizeLoadConsignee(*suggestion.Suggested.LoadChanges),
		normalizeLoadConsignee(*suggestion.Applied.LoadChanges)
}

// Load Rate Data helpers
func normalizeLoadRateData(obj models.LoadChanges) extractor.RateDataInfo {
	return extractor.RateDataInfo{
		RateData: models.RateData{
			CollectionMethod: obj.RateData.CollectionMethod,
			CustomerLineHaulCharge: models.ValueUnit{
				Val:  obj.RateData.CustomerLineHaulCharge.Val,
				Unit: obj.RateData.CustomerLineHaulCharge.Unit,
			},
			CustomerLineHaulRate: obj.RateData.CustomerLineHaulRate,
			CustomerRateNumUnits: obj.RateData.CustomerRateNumUnits,
			CustomerRateType:     obj.RateData.CustomerRateType,
			FSCFlatRate:          obj.RateData.FSCFlatRate,
			FSCPerMile:           obj.RateData.FSCPerMile,
			FSCPercent:           obj.RateData.FSCPercent,
			NetProfitUSD:         obj.RateData.NetProfitUSD,
			ProfitPercent:        obj.RateData.ProfitPercent,
			RevenueCode:          obj.RateData.RevenueCode,
		},
	}
}

func NormalizeLoadRateData(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.RateDataInfo, expected extractor.RateDataInfo) {
	return normalizeLoadRateData(*suggestion.Suggested.LoadChanges),
		normalizeLoadRateData(*suggestion.Applied.LoadChanges)
}

// Load Specifications helpers
func normalizeLoadSpecifications(obj models.LoadChanges) extractor.SpecificationsInfo {
	return extractor.SpecificationsInfo{
		Specifications: models.SuggestedSpecifications{
			ServiceType:         obj.Specifications.ServiceType,
			TransportType:       obj.Specifications.TransportType,
			TransportSize:       obj.Specifications.TransportSize,
			TotalInPalletCount:  obj.Specifications.TotalInPalletCount,
			TotalOutPalletCount: obj.Specifications.TotalOutPalletCount,
			TotalPieces:         obj.Specifications.TotalPieces,
			Commodities:         obj.Specifications.Commodities,
			NumCommodities:      obj.Specifications.NumCommodities,
			TotalWeight:         obj.Specifications.TotalWeight,
			NetWeight:           obj.Specifications.NetWeight,
			BillableWeight:      obj.Specifications.BillableWeight,
			TotalDistance:       obj.Specifications.TotalDistance,
			MinTempFahrenheit:   obj.Specifications.MinTempFahrenheit,
			MaxTempFahrenheit:   obj.Specifications.MaxTempFahrenheit,
		},
	}
}

func NormalizeLoadSpecifications(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.SpecificationsInfo, expected extractor.SpecificationsInfo) {
	return normalizeLoadSpecifications(*suggestion.Suggested.LoadChanges),
		normalizeLoadSpecifications(*suggestion.Applied.LoadChanges)
}

func NormalizeLoadCommoditiesAndRefs(
	suggestion models.SuggestedLoadChange,
) (suggested extractor.CommoditiesAndRefsInfo, expected extractor.CommoditiesAndRefsInfo) {

	return normalizeLoadCommoditiesAndRefs(*suggestion.Suggested.LoadChanges),
		normalizeLoadCommoditiesAndRefs(*suggestion.Applied.LoadChanges)
}

func normalizeLoadCommoditiesAndRefs(obj models.LoadChanges) extractor.CommoditiesAndRefsInfo {
	return extractor.CommoditiesAndRefsInfo{
		Commodities:          obj.Commodities,
		AdditionalReferences: obj.AdditionalReferences,
	}
}
