package braintrustutil

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// This file contains helper functions for normalizing quote request suggestion changes into llm QR models
// that are used for submitting braintrust logs. This is allows us to compare suggested and applied changes,
// score the initial LLM response, and submit that feedback to Braintrust.

// Quote Begin Conversation
func normalizeQuoteBeginConversation(obj models.QuoteLoadInfo) llm.QuoteRequestOutput {
	stops := []llm.Stop{}

	// Add pickup stop if present
	if obj.PickupLocation != (models.Address{}) {
		stops = append(stops, llm.Stop{
			Location: llm.Address{
				City:  obj.PickupLocation.City,
				State: obj.PickupLocation.State,
				Zip:   obj.PickupLocation.Zip,
			},
			DateTime: parseDateForExport(obj.PickupDate.Time),
			Type:     "pickup",
		})
	}

	// Add any intermediate stops if present (optional, not in original logic)
	// If you have obj.Stops with type == "stop", add them here
	for _, s := range obj.Stops {
		if s.StopType == "stop" {
			stops = append(stops, llm.Stop{
				Location: llm.Address{
					City:  s.Address.City,
					State: s.Address.State,
					Zip:   s.Address.Zip,
				},
				DateTime: parseDateForExport(s.MustDeliver.Time),
				Type:     "stop",
			})
		}
	}

	// Add dropoff stop if present
	if obj.DeliveryLocation != (models.Address{}) {
		stops = append(stops, llm.Stop{
			Location: llm.Address{
				City:  obj.DeliveryLocation.City,
				State: obj.DeliveryLocation.State,
				Zip:   obj.DeliveryLocation.Zip,
			},
			DateTime: parseDateForExport(obj.DeliveryDate.Time),
			Type:     "dropoff",
		})
	}

	return llm.QuoteRequestOutput{
		QuoteRequests: []llm.QuoteRequest{
			{
				TruckType: string(obj.TransportType),
				Stops:     stops,
			},
		},
	}
}

func NormalizeQuoteBeginConversation(
	suggestion models.QuoteRequest,
) (suggested llm.QuoteRequestOutput, expected llm.QuoteRequestOutput) {
	return normalizeQuoteBeginConversation(suggestion.SuggestedRequest),
		normalizeQuoteBeginConversation(suggestion.AppliedRequest)
}

// Quote Customer
func normalizeQuoteCustomer(obj models.QuoteLoadInfo) llm.CustomerInfo {
	return llm.CustomerInfo{
		ShipperName:         obj.Customer.Name,
		OriginalSenderEmail: obj.Customer.Email,
	}
}

func NormalizeQuoteCustomer(
	suggestion models.QuoteRequest,
) (suggested llm.CustomerInfo, expected llm.CustomerInfo) {
	return normalizeQuoteCustomer(suggestion.SuggestedRequest),
		normalizeQuoteCustomer(suggestion.AppliedRequest)
}

func SubmitBraintrustQuoteRequestFeedback(ctx context.Context, suggestion models.QuoteRequest) error {
	if len(suggestion.BraintrustLogIDs) == 0 {
		return errors.New("no braintrust log records found for quote request suggestion")
	}

	var err error
	var expected, suggested any

	for _, logID := range suggestion.BraintrustLogIDs {

		switch logID.ProjectStepName {
		case string(braintrustsdk.QRBeginConversation):
			expected, suggested = NormalizeQuoteBeginConversation(suggestion)

		case string(braintrustsdk.QRGetCustomer):
			expected, suggested = NormalizeQuoteCustomer(suggestion)
		}

		err = braintrustsdk.SubmitFeedback(
			ctx,
			braintrustsdk.QuoteRequestProjectID,
			braintrustsdk.BuildFeedbackRequestBody(ctx, logID.ID, expected, suggested),
		)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}
	}

	return nil
}

// ParseDateForExport formats a time.Time and address into the string format expected by LLM stops export.
// If the time is zero, returns an empty string.
func parseDateForExport(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	if t.Hour() == 0 && t.Minute() == 0 && t.Second() == 0 {
		return t.Format("01/02/2006")
	}

	return t.Format("01/02/2006, 03:04PM")
}
