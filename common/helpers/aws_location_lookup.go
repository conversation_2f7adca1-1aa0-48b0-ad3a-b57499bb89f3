package helpers

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"regexp"
	"strings"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/location"
	"github.com/aws/aws-sdk-go-v2/service/location/types"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

func redisLocationLookupKey(ctx context.Context, city string, state string, zipcode string) string {
	if len(state) > 2 {
		state = GetStateAbbreviation(ctx, state)
	}

	// Preventing mismatches such as "Opa Locka" and "Opa-Locka". This works because this function is
	// used both for getting keys to check with as well as getting the keys being set.
	city = strings.ReplaceAll(city, " ", "-")

	if zipcode == "" {
		return fmt.Sprintf("location-lookup-%s-%s", strings.ToLower(city), strings.ToLower(state))
	}

	return fmt.Sprintf(
		"location-lookup-%s-%s-%s",
		strings.ToLower(city),
		strings.ToLower(state),
		strings.ToLower(zipcode),
	)
}

// This function allows both lookups by city and state or zipcode, caching results in Redis through
// multiple keys so future lookups with limited information can always access it.
// https://docs.aws.amazon.com/location/latest/developerguide/search-place-index-geocoding.html
func AwsLocationLookup(
	ctx context.Context,
	city string,
	state string,
	zipcode string,
) (res *location.SearchPlaceIndexForTextOutput, err error) {
	// Check if AWS Location is disabled for local development
	if os.Getenv("DISABLE_AWS_LOCATION") == "true" {
		log.Info(ctx, "AWS Location disabled for local development, returning mock data")
		return createMockLocationResponse(city, state, zipcode), nil
	}

	// Only used for response logging for internal metrics
	integration := models.Integration{Type: "utility", Name: "aws-location"}

	redisKey := redisLocationLookupKey(ctx, city, state, zipcode)

	cachedLocation, found, err := redis.GetKey[location.SearchPlaceIndexForTextOutput](ctx, redisKey)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get AWS Location from Redis", zap.Error(err))
		return nil, err
	}
	if found {

		log.Info(
			ctx,
			"found location in Redis",
			zap.String("redisKey", redisKey),
			zap.String("city", city),
			zap.String("state", state),
			zap.String("zipcode", zipcode),
			zap.Any("cachedLocation", cachedLocation),
		)

		shouldReturnCachedLocation := true
		cachedPlace := cachedLocation.Results[0].Place

		if cachedPlace.PostalCode == nil {
			log.WarnNoSentry(
				ctx,
				"no postal code found for cached location, deleting cache since zipcode lookup will fail",
				zap.Any("cachedLocation", cachedLocation),
			)

			shouldReturnCachedLocation = false
		}

		if shouldReturnCachedLocation {
			if cachedPlace.PostalCode != nil && cachedPlace.Country != nil && *cachedPlace.Country == "USA" {
				formattedZip := formatUSZipcode(*cachedPlace.PostalCode)
				cachedLocation.Results[0].Place.PostalCode = &formattedZip
			}

			return &cachedLocation, nil
		}
	}

	cleanupRedisLocationLookupKey(ctx, redisKey)

	log.Info(
		ctx,
		"location not found on redis, proceeding to lookup with AWS",
		zap.Any("redisKey", redisKey),
	)

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return nil, err
	}

	locationClient := location.NewFromConfig(cfg)

	locationPlaceIndex := "drumkit.place.here"
	locationLanguage := "en"

	// We can rely on AWS to match results even when city/state or zipcode is not provided.
	var lookupString string
	if zipcode == "" {
		lookupString = fmt.Sprintf("%s, %s", city, state)
	} else {
		if len(zipcode) == 6 {
			// put a space between the third and fourth digit since this is a Canadian zipcode
			lookupString = fmt.Sprintf("%s %s", zipcode[:3], zipcode[3:])
		} else {
			lookupString = zipcode
		}
	}

	log.Debug(ctx, "AWS Locations lookup string", zap.String("lookupString", lookupString))

	locationQuery := location.SearchPlaceIndexForTextInput{
		IndexName:       &locationPlaceIndex,
		Text:            &lookupString,
		FilterCountries: []string{"USA", "CAN"},
		Language:        &locationLanguage,
	}

	locationResult, err := locationClient.SearchPlaceIndexForText(ctx, &locationQuery)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, integration, err)
		return nil, err
	}

	if locationResult == nil || len(locationResult.Results) == 0 || locationResult.Results[0].PlaceId == nil {
		err404 := errtypes.EntityNotFoundError(integration, lookupString, "locationQuery")

		httplog.LogHTTPRequestFailed(ctx, integration, err404)

		return nil, errors.New("no results were found")
	}

	locationZip := locationResult.Results[0].Place.PostalCode
	locationCountry := locationResult.Results[0].Place.Country
	if locationZip != nil && locationCountry != nil && *locationCountry == "USA" {
		formattedZip := formatUSZipcode(*locationZip)
		locationResult.Results[0].Place.PostalCode = &formattedZip
	}

	httplog.LogHTTPResponseCode(ctx, integration, http.StatusOK)
	log.Info(
		ctx,
		"location lookup executed through AWS client",
		zap.Any("location summary", locationResult.Summary),
		zap.Any("location results", locationResult.Results),
	)

	// We cache the location through different keys so lookups with only zipcode,
	// city/state or both always have a match.
	// We're allowed to store AWS Location results since our place index has an explicit intended use of "storage".
	locationPlace := locationResult.Results[0].Place

	postalCode := ""
	if locationPlace.PostalCode != nil {
		postalCode = *locationPlace.PostalCode
	} else {
		log.WarnNoSentry(
			ctx,
			"no postal code found for location, this should not happen",
			zap.Any("locationPlace", locationPlace),
		)
	}

	municipality := ""
	if locationPlace.Municipality != nil {
		municipality = *locationPlace.Municipality
	} else {
		log.WarnNoSentry(
			ctx,
			"no municipality (city) found for location, this should not happen",
			zap.Any("locationPlace", locationPlace),
		)
	}

	region := ""
	if locationPlace.Region != nil {
		region = *locationPlace.Region
	} else {
		log.WarnNoSentry(
			ctx,
			"no region (state) found for location, this should not happen",
			zap.Any("locationPlace", locationPlace),
		)
	}

	zipcodeKey := redisLocationLookupKey(ctx, "", "", postalCode)
	cityStateKey := redisLocationLookupKey(ctx, municipality, region, "")
	fullAddressKey := redisLocationLookupKey(ctx, municipality, region, postalCode)

	// Cache with no expiration (0 duration)
	if err := redis.SetKey(ctx, zipcodeKey, *locationResult, 0); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", zipcodeKey),
		)
	} else {
		log.Info(ctx, "location stored successfully on redis", zap.Any("redisKey", zipcodeKey))
	}

	if err := redis.SetKey(ctx, cityStateKey, *locationResult, 0); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", cityStateKey),
		)
	} else {
		log.Info(ctx, "location stored successfully on redis", zap.Any("redisKey", cityStateKey))
	}

	if err := redis.SetKey(ctx, fullAddressKey, *locationResult, 0); err != nil {
		log.WarnNoSentry(
			ctx,
			"error setting AWS Location lookup in redis",
			zap.Error(err),
			zap.Any("key", fullAddressKey),
		)
	} else {
		log.Info(ctx, "location lookup stored successfully on redis", zap.Any("redisKey", fullAddressKey))
	}

	return locationResult, nil
}

// formatUSZipcode takes a zipcode string and returns a formatted 5-digit US zipcode if valid
// Returns the original string if it's not a valid US zipcode format
func formatUSZipcode(zipcode string) string {
	// Regular expression for US zipcode format xxxxx-xxxx
	usZipRegex := regexp.MustCompile(`^\d{5}-\d{4}$`)

	if usZipRegex.MatchString(zipcode) {
		// Extract just the first 5 digits
		return zipcode[:5]
	}

	return zipcode
}

func cleanupRedisLocationLookupKey(ctx context.Context, key string) {
	if delErr := redis.DeleteKey(ctx, key); delErr != nil && !errors.Is(delErr, redis.NilEntry) {
		log.Error(
			ctx,
			"failed to clean up empty/bugged redis key",
			zap.String("redisKey", key),
			zap.Error(delErr),
		)
	}
}

// createMockLocationResponse creates a mock response for local development
func createMockLocationResponse(city string, state string, zipcode string) *location.SearchPlaceIndexForTextOutput {
	// Use provided values or defaults
	mockCity := city
	if mockCity == "" {
		mockCity = "Mock City"
	}

	mockState := state
	if mockState == "" {
		mockState = "CA"
	}

	mockZip := zipcode
	if mockZip == "" {
		mockZip = "90210"
	}

	// Ensure state is abbreviated
	if len(mockState) > 2 {
		mockState = "CA" // Default to CA for long state names
	}

	mockPlaceID := "mock-place-id-123"
	mockCountry := "USA"
	mockTimeZoneName := "America/New_York"
	mockTimeZone := types.TimeZone{
		Name: &mockTimeZoneName,
	}
	mockLabel := fmt.Sprintf("%s, %s %s, %s", mockCity, mockState, mockZip, mockCountry)
	mockRelevance := 1.0

	// Create mock coordinates (Los Angeles area as default)
	mockGeometry := types.PlaceGeometry{
		Point: []float64{-118.2437, 34.0522},
	}

	mockPlace := types.Place{
		Label:        &mockLabel,
		Municipality: &mockCity,
		Region:       &mockState,
		PostalCode:   &mockZip,
		Country:      &mockCountry,
		Geometry:     &mockGeometry,
		TimeZone:     &mockTimeZone,
	}

	mockResult := types.SearchForTextResult{
		Place:     &mockPlace,
		PlaceId:   &mockPlaceID,
		Relevance: &mockRelevance,
	}

	mockDataSource := "Mock"
	mockText := fmt.Sprintf("%s, %s %s", mockCity, mockState, mockZip)

	mockSummary := types.SearchPlaceIndexForTextSummary{
		DataSource: &mockDataSource,
		Text:       &mockText,
	}

	return &location.SearchPlaceIndexForTextOutput{
		Results: []types.SearchForTextResult{mockResult},
		Summary: &mockSummary,
	}
}
