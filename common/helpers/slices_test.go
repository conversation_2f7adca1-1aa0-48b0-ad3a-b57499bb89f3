package helpers

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestIsStringInArray(t *testing.T) {
	tests := []struct {
		name     string
		array    []string
		item     string
		expected bool
	}{
		{
			name:     "Match full word",
			array:    []string{"this is a test", "argo shipment", "hello world"},
			item:     "argo",
			expected: true,
		},
		{
			name:     "No match when part of a word",
			array:    []string{"this is a test", "cargo shipment", "hello world"},
			item:     "argo",
			expected: false,
		},
		{
			name:     "Case insensitive match",
			array:    []string{"This is a Test", "ARGO shipment", "hello World"},
			item:     "argo",
			expected: true,
		},
		{
			name:     "Empty array",
			array:    []string{},
			item:     "argo",
			expected: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := IsStringInArray(tc.array, tc.item)
			require.Equal(t, tc.expected, result, "unexpected result for case: %s", tc.name)
		})
	}
}
