package s3backup

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestS(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Allowed characters only",
			input:    "Release123_!.-*'()",
			expected: "Release123_!.-*'()",
		},
		{
			name:     "Replace hash symbol",
			input:    "Release #2935",
			expected: "Release--2935",
		},
		{
			name:     "Replace spaces and symbols",
			input:    "Invoice $ Amount: 100%",
			expected: "Invoice---Amount--100-",
		},
		{
			name:     "No replacements needed",
			input:    "Valid_File-Name123!",
			expected: "Valid_File-Name123!",
		},
		{
			name:     "Replace multiple invalid characters",
			input:    "File@Name^With&Invalid%Chars",
			expected: "File-Name-With-Invalid-Chars",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			actual := SanitizeFileName(tc.input)

			assert.Equal(t, tc.expected, actual, "Expected sanitized file name to match")
		})
	}
}
