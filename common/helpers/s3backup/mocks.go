package s3backup

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// MockArchiver offers an in-memory implementation which satisfies Archiver interface
type MockArchiver struct {
	// set of email IDs
	GmailMessages      map[string]bool
	OutlookMessages    map[string]bool
	OutlookAttachments map[string]bool
}

func NewMock() *MockArchiver {
	return &MockArchiver{
		GmailMessages:      make(map[string]bool),
		OutlookMessages:    make(map[string]bool),
		OutlookAttachments: make(map[string]bool),
	}
}

func (c *MockArchiver) Gmail(_ context.Context, msg *models.IngestedEmail, account string) (string, error) {
	c.GmailMessages[msg.ExternalID] = true

	return fmt.Sprintf("s3://gmail/%s/%s.test", account, msg.ExternalID), nil
}

func (c *<PERSON>ckArchiver) Outlook(_ context.Context, msg *models.IngestedEmail, account string) (string, error) {
	c.OutlookMessages[msg.ExternalID] = true

	return fmt.Sprintf("s3://outlook/%s/%s.test", account, msg.ExternalID), nil
}

func (c *MockArchiver) Attachment(
	_ context.Context, _ models.IntegrationName, account, msgID, attachmentName string, _ []byte,
) (string, error) {
	c.OutlookAttachments[msgID] = true

	return fmt.Sprintf("s3://outlook/%s/%s/%s.test", account, msgID, attachmentName), nil
}

func (c *MockArchiver) LaneRatePrediction(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {

	return fmt.Sprintf("s3://service-%d/greenscreens/lane-rate-prediction/status-%d/%s-%s.test",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) NetworkLaneRatePrediction(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {

	return fmt.Sprintf("s3://service-%d/greenscreens/network-lane-rate-prediction/status-%d/%s-%s.test",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) QuickQuote(
	_ context.Context,
	account,
	laneRatePredictionID string,
	_ helpers.APIResponse,
) (string, error) {

	return fmt.Sprintf("s3://greenscreens/%s/quick-quote-%s.test", account, laneRatePredictionID), nil
}

func (c *MockArchiver) PostedRate(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {
	return fmt.Sprintf("service-%d/truckstop/posted-rate/status-%d/%s-%s.json",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) PostedTrendline(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {
	return fmt.Sprintf("service-%d/truckstop/posted-trendline/status-%d/%s-%s.json",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) BookedRate(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {
	return fmt.Sprintf("service-%d/truckstop/booked-rate/status-%d/%s-%s.json",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) BookedTrendline(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {
	return fmt.Sprintf("service-%d/truckstop/booked-trendline/status-%d/%s-%s.json",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) BookedHistory(
	_ context.Context,
	serviceID uint,
	userEmail string,
	apiResp helpers.APIResponse,
) (string, error) {
	return fmt.Sprintf("service-%d/truckstop/booked-history/status-%d/%s-%s.json",
		serviceID, apiResp.Status, time.Now().Format(time.RFC3339), userEmail), nil
}

func (c *MockArchiver) TMSResponse(
	_ context.Context,
	tms models.Integration,
	dataType DataType,
	apiResponse helpers.APIResponse,
) (string, error) {
	timestamp := time.Now().Format(time.RFC3339Nano)

	return fmt.Sprintf("service-%d/%s-%d/%s/status-%d/%s.txt",
		tms.ServiceID, tms.Name, tms.ID, dataType, apiResponse.Status, timestamp), nil
}

func (c *MockArchiver) PublicUserAsset(
	_ context.Context, user models.User, env, subfolder, fileExtension string, _ []byte) (string, error) {
	return fmt.Sprintf("s3://public-user-assets/%s/%s/%s/%s.%s",
		env, user.EmailAddress, subfolder, uuid.New().String(), fileExtension), nil
}
