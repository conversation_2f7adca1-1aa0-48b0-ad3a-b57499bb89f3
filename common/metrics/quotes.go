package metrics

import (
	"context"
	"math"
	"sort"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	quoteDB "github.com/drumkitai/drumkit/common/rds/quick_quote"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

// QuoteSummaryData represents the high-level summary metrics for quote requests
type QuoteSummaryData struct {
	TotalOpportunities int64 `json:"totalOpportunities"`
	TotalQuotes        int64 `json:"totalQuotes"`
	BrokerResponses    int64 `json:"brokerResponses"`
	LoadsWon           int64 `json:"loadsWon"`
}

// MetricData represents a named metric value for charting
type MetricData struct {
	Name  string `json:"name"`
	Value int64  `json:"value"`
}

// metricAccumulator helps track metric totals and counts for averaging
type metricAccumulator struct {
	Total float64
	Count int
}

// CalculateQuoteSummaryMetrics calculates the high-level summary metrics for quote requests
func CalculateQuoteSummaryMetrics(ctx context.Context, quoteRequests []models.QuoteRequest) QuoteSummaryData {
	if len(quoteRequests) == 0 {
		return QuoteSummaryData{}
	}

	// Get total opportunities
	totalOpportunities := int64(len(quoteRequests))

	// Get total quotes looked up via drumkit
	totalQuotes := calculateTotalQuotes(ctx, quoteRequests)

	// Get broker responses
	brokerResponses := countBrokerResponses(quoteRequests)

	// Get loads won
	loadsWon := countLoadsWon(quoteRequests)

	return QuoteSummaryData{
		TotalOpportunities: totalOpportunities,
		TotalQuotes:        totalQuotes,
		BrokerResponses:    brokerResponses,
		LoadsWon:           loadsWon,
	}
}

// CalculateTopFiveCustomersByRequestCount calculates the top 5 customers by number of quote requests
func CalculateTopFiveCustomersByRequestCount(ctx context.Context, quoteRequests []models.QuoteRequest) []MetricData {
	if len(quoteRequests) == 0 {
		return []MetricData{}
	}

	customerCounts := make(map[uint]int64)

	for _, qr := range quoteRequests {
		customerID := qr.AppliedRequest.CustomerID
		if customerID == 0 {
			customerID = qr.SuggestedRequest.CustomerID
		}
		if customerID != 0 {
			customerCounts[customerID]++
		}
	}

	if len(customerCounts) == 0 {
		return []MetricData{}
	}

	// Get top 5 customers by converting to slice and sorting
	type idCount struct {
		id    uint
		count int64
	}
	countsList := make([]idCount, 0, len(customerCounts))
	for id, count := range customerCounts {
		countsList = append(countsList, idCount{id, count})
	}

	sort.Slice(countsList, func(i, j int) bool {
		return countsList[i].count > countsList[j].count
	})

	if len(countsList) > 5 {
		countsList = countsList[:5]
	}

	customerIDs := make([]uint, len(countsList))
	for i, item := range countsList {
		customerIDs[i] = item.id
	}

	customerNames, err := tmsCustomerDB.GetCustomerNamesByIDs(ctx, customerIDs)
	if err != nil {
		log.ErrorNoSentry(ctx, "failed to get customer names", zap.Error(err))
	}

	result := make([]MetricData, 0, len(countsList))
	for _, item := range countsList {
		if n, exists := customerNames[item.id]; exists {
			result = append(result, MetricData{Name: n, Value: item.count})
		}
	}

	return result
}

// CalculateQuotePlatformUsage calculates the usage of different quoting platforms
func CalculateQuotePlatformUsage(ctx context.Context, quoteRequests []models.QuoteRequest) []MetricData {
	if len(quoteRequests) == 0 {
		return []MetricData{}
	}

	// Map to track quote platform usage
	platformUsage := make(map[string]int64)

	// Get all quote request IDs for accepted quote requests
	acceptedRequestIDs := make([]uint, 0)
	for _, quoteRequest := range quoteRequests {
		if quoteRequest.Status == models.Accepted {
			acceptedRequestIDs = append(acceptedRequestIDs, quoteRequest.ID)
		}
	}

	// Batch fetch quotes for all accepted quote requests
	allQuotes, err := quoteDB.BatchGetByQuoteRequestIDs(ctx, acceptedRequestIDs)
	if err != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to batch get quotes for quote requests",
			zap.Error(err),
		)
	}

	// Process each quote request with its associated quotes
	for _, quoteRequest := range quoteRequests {
		// Only count accepted quote requests
		if quoteRequest.Status != models.Accepted {
			continue
		}

		quotes, exists := allQuotes[quoteRequest.ID]
		if !exists {
			continue
		}

		foundMatch := false
		for _, quote := range quotes {
			if quoteRequest.FinalCarrierCost == int(math.Round(quote.TotalCost)) {
				platformUsage[string(quote.Source)]++
				foundMatch = true
				break
			}
		}

		if !foundMatch {
			platformUsage[string(models.BrokerDeterminedSource)]++
		}
	}

	// Check if any of the service's quote platforms aren't in the platformUsage
	if len(quoteRequests) > 0 {
		quotingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, quoteRequests[0].ServiceID)
		if err != nil {
			log.ErrorNoSentry(
				ctx,
				"failed to get quote integrations for service id",
				zap.Uint("serviceID", quoteRequests[0].ServiceID),
			)
		}

		for _, integration := range quotingIntegrations {
			found := false
			for existingName := range platformUsage {
				if strings.EqualFold(existingName, string(integration.Name)) {
					found = true
					break
				}
			}

			if !found {
				platformUsage[string(integration.Name)] = 0
			}
		}

		// Make sure the broker determined source is in the map
		if _, ok := platformUsage[string(models.BrokerDeterminedSource)]; !ok {
			platformUsage[string(models.BrokerDeterminedSource)] = 0
		}
	}

	// Create metric data from the map
	platformData := make([]MetricData, 0, len(platformUsage))
	for source, count := range platformUsage {
		platformData = append(platformData, MetricData{
			Name:  formatSourceName(source),
			Value: count,
		})
	}

	// Sort by value in descending order
	sortMetricsByValueDesc(platformData)

	return platformData
}

// CalculateTransportTypeDistribution calculates the distribution of transport types
func CalculateTransportTypeDistribution(quoteRequests []models.QuoteRequest) []MetricData {
	if len(quoteRequests) == 0 {
		return []MetricData{}
	}

	// Count occurrences of each transport type
	counts := make(map[string]int64)
	for _, quoteRequest := range quoteRequests {
		if quoteRequest.AppliedRequest.TransportType != "" {
			counts[string(quoteRequest.AppliedRequest.TransportType)]++
		} else if quoteRequest.SuggestedRequest.TransportType != "" {
			counts[string(quoteRequest.SuggestedRequest.TransportType)]++
		}
	}

	// Convert map to slice of MetricData
	transportTypes := make([]MetricData, 0, len(counts))
	for name, value := range counts {
		transportTypes = append(transportTypes, MetricData{
			Name:  name,
			Value: value,
		})
	}

	// Sort by value in descending order
	sortMetricsByValueDesc(transportTypes)

	return transportTypes
}

// CalculateAverageMetrics calculates average margin and rate metrics
func CalculateAverageMetrics(quoteRequests []models.QuoteRequest) []MetricData {
	if len(quoteRequests) == 0 {
		return []MetricData{}
	}

	// Create metric accumulators for each metric type
	metrics := map[string]*metricAccumulator{
		"Percent Margin": {Total: 0, Count: 0},
		"Dollar Margin":  {Total: 0, Count: 0},
		"Rate Per Mile":  {Total: 0, Count: 0},
	}

	// Process each quote request
	for _, quoteRequest := range quoteRequests {
		// Skip if no final quote price (needed for margin calculation)
		if quoteRequest.FinalQuotePrice <= 0 {
			continue
		}

		finalQuotePrice := float64(quoteRequest.FinalQuotePrice)
		finalMargin := float64(quoteRequest.FinalMargin)

		// Calculate and accumulate metrics based on margin type
		switch quoteRequest.MarginType {
		case string(models.Percentage):
			// For percent margins
			percentValue := finalMargin
			dollarValue := (percentValue / 100.0) * finalQuotePrice

			metrics["Percent Margin"].Total += percentValue
			metrics["Percent Margin"].Count++

			metrics["Dollar Margin"].Total += dollarValue
			metrics["Dollar Margin"].Count++

		case string(models.Amount):
			// For dollar margins
			dollarValue := finalMargin

			metrics["Dollar Margin"].Total += dollarValue
			metrics["Dollar Margin"].Count++

			// Avoid division by zero
			if finalQuotePrice > 0 {
				percentValue := (dollarValue / finalQuotePrice) * 100.0
				metrics["Percent Margin"].Total += percentValue
				metrics["Percent Margin"].Count++
			}
		}

		// Process rate per mile
		if quoteRequest.CarrierCostType == string(models.PerMile) {
			metrics["Rate Per Mile"].Total += float64(quoteRequest.FinalCarrierCost)
			metrics["Rate Per Mile"].Count++
		}
	}

	// Calculate averages and build result
	result := make([]MetricData, 0, len(metrics))
	for name, accumulator := range metrics {
		avgValue := float64(0)
		if accumulator.Count > 0 {
			avgValue = accumulator.Total / float64(accumulator.Count)
		}

		result = append(result, MetricData{
			Name:  name,
			Value: int64(math.Round(avgValue)),
		})
	}

	return result
}

// Helper functions

// calculateTotalQuotes counts the total number of quotes across all quote requests
func calculateTotalQuotes(ctx context.Context, quoteRequests []models.QuoteRequest) int64 {
	if len(quoteRequests) == 0 {
		return 0
	}

	// Get all quote request IDs
	quoteRequestIDs := make([]uint, 0, len(quoteRequests))
	for _, qr := range quoteRequests {
		quoteRequestIDs = append(quoteRequestIDs, qr.ID)
	}

	// Batch get quotes for all quote requests
	allQuotes, err := quoteDB.BatchGetByQuoteRequestIDs(ctx, quoteRequestIDs)
	if err != nil {
		log.ErrorNoSentry(
			ctx,
			"failed to batch get quotes for quote requests",
			zap.Error(err),
		)
		return 0
	}

	// Count total quotes
	totalQuotes := int64(0)
	for _, quotes := range allQuotes {
		totalQuotes += int64(len(quotes))
	}

	return totalQuotes
}

// countBrokerResponses counts the number of quote requests with broker responses
func countBrokerResponses(quoteRequests []models.QuoteRequest) int64 {
	brokerResponses := int64(0)
	for _, quoteRequest := range quoteRequests {
		// if a quote request is accepted, it means the broker did one of three things:
		// 1. Created a draft response
		// 2. Copied the target sell price
		// 3. Submitted a quote via url
		if quoteRequest.Status == models.Accepted {
			brokerResponses++
		}
	}

	return brokerResponses
}

// countLoadsWon counts the number of quote requests that led to won loads
func countLoadsWon(quoteRequests []models.QuoteRequest) int64 {
	loadsWon := int64(0)

	for _, quoteRequest := range quoteRequests {
		if quoteRequest.WonLoadID != nil {
			loadsWon++
		}
	}

	return loadsWon
}
