package metrics

import (
	"sort"
	"strings"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

// sortMetricsByValueDesc sorts MetricData slice by Value in descending order
func sortMetricsByValueDesc(data []MetricData) {
	sort.Slice(data, func(i, j int) bool {
		return data[i].Value > data[j].Value
	})
}

// formatSourceName converts a source string to a more readable/presentable format
func formatSourceName(source string) string {
	if strings.Contains(source, "_") {
		parts := strings.Split(source, "_")
		for i, part := range parts {
			parts[i] = cases.Title(language.English).String(part)
		}

		return strings.Join(parts, " ")
	}

	// special case for DAT as it needs to be all caps
	if strings.EqualFold(source, "dat") {
		return "DAT"
	}

	return cases.Title(language.English).String(source)
}
