package sentry

import (
	"context"
	"testing"

	"github.com/getsentry/sentry-go" //nolint:gomodguard // this pkg is allowed to import Sentry SDK directly
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWithHub(t *testing.T) {
	t.Parallel()

	hub := sentry.CurrentHub().Clone()
	hub.Scope().SetTag("base", "base-value")
	ctx := sentry.SetHubOnContext(context.Background(), hub)

	done := make(chan bool)

	go WithHub(ctx, func(ctx context.Context) {
		defer func() { done <- true }()

		// There should be a cloned hub available in the new context
		asyncHub := GetHubFromContext(ctx)
		require.NotNil(t, asyncHub)

		asyncHub.Scope().SetTag("async", "async-value")

		// Tags should include both this scope and the tags from the parent hub
		validateTags(t, asyncHub, map[string]string{"async": "async-value", "base": "base-value"})
	})

	// Wait for the goroutine to finish
	assert.True(t, <-done)

	// The scope in the original goroutine should not have changed
	validateTags(t, hub, map[string]string{"base": "base-value"})
}

func TestWithHubAsyncCaptured(t *testing.T) {
	t.Parallel()

	letters := []string{"a", "b", "c", "d", "e"}
	for i, s := range letters {
		done := make(chan bool)

		go func(index int, letter string) {
			defer func() { done <- true }()

			WithHub(context.Background(), func(_ context.Context) {
				assert.Equal(t, letters[index], letter)
			})
		}(i, s) // capture loop variables

		assert.True(t, <-done)
	}
}

// Each nested WithHub() invocation should clone the parent scope
func TestWithHubNested(t *testing.T) {
	t.Parallel()

	WithHub(context.Background(), func(ctx context.Context) {
		hub1 := GetHubFromContext(ctx)
		hub1.Scope().SetTag("a", "1")
		expectedTags1 := map[string]string{"a": "1"}

		// Test tags before and after the WithHub call - nested modifications should not affect the parent hub
		validateTags(t, hub1, expectedTags1)
		defer validateTags(t, hub1, expectedTags1)

		WithHub(ctx, func(ctx context.Context) {
			hub2 := GetHubFromContext(ctx)
			hub2.Scope().SetTag("b", "2")
			expectedTags2 := map[string]string{"a": "1", "b": "2"}

			validateTags(t, hub2, expectedTags2)
			defer validateTags(t, hub2, expectedTags2)

			WithHub(ctx, func(ctx context.Context) {
				hub3 := GetHubFromContext(ctx)
				hub3.Scope().SetTag("c", "3")
				expectedTags3 := map[string]string{"a": "1", "b": "2", "c": "3"}

				validateTags(t, hub3, expectedTags3)
			})
		})
	})

	validateTags(t, sentry.CurrentHub(), map[string]string{})
}

func validateTags(t *testing.T, hub *sentry.Hub, expected map[string]string) {
	event := sentry.NewEvent()
	hub.Scope().ApplyToEvent(event, nil)
	assert.Equal(t, expected, event.Tags)
}
