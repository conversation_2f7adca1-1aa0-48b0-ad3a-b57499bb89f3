package sentry

import (
	"context"
	"fmt"

	"github.com/getsentry/sentry-go" //nolint:gomodguard // this pkg is allowed to import Sentry SDK directly

	"github.com/drumkitai/drumkit/common/models"
)

// GetHubFromContext always returns a non-nil Sentry hub.
func GetHubFromContext(ctx context.Context) *sentry.Hub {
	if hub := sentry.GetHubFromContext(ctx); hub != nil {
		return hub
	}

	return sentry.CurrentHub()
}

// WithHub invokes the given function with a cloned Sentry hub passed via ctx.
//
// This should be used after the go async keyword for all Axle application code. Example:
//
//	go sentry.WithHub(ctx, func(ctx context.Context) {
//	    hub := sentry.GetHubFromContext(ctx)
//	    hub.Scope().SetTag("key", "value")
//
//	    if err := longPollJob(); err != nil {
//	        // Use the context-specific hub instead of the global sentry.CaptureException
//	        hub.CaptureException(err)
//	    }
//	})
//
// If you need to pass a variable to the goroutine (e.g. to safely capture a loop variable):
//
//	for _, s := range []string{"a", "b", "c"} {
//	    go func(x str) {
//	        sentry.WithHub(ctx, func(ctx context.Context) {
//	            fmt.Println(x)
//	        })
//	    }(s) // loop variable captured
//	}
//
// 'f' is the application code you want to run with a cloned Sentry hub.
// The context passed to f includes the cloned Sentry hub which you can access via sentry.GetHubFromContext(ctx).
func WithHub(ctx context.Context, f func(context.Context)) {
	// Every goroutine needs its own hub clone so the scope can be safely modified.
	// https://docs.sentry.io/platforms/go/concurrency/
	hub := GetHubFromContext(ctx).Clone()

	// Report panics
	defer func() {
		if err := recover(); err != nil {
			if eventID := hub.RecoverWithContext(ctx, err); eventID != nil {
				// Flush the event to Sentry before the app crashes
				hub.Flush(FlushTimeout)
			}

			// Re-panic to print the error details and crash the application like normal
			// (Or the panic can be recovered again by the application)
			panic(err)
		}
	}()

	// Run the application code
	f(sentry.SetHubOnContext(ctx, hub))
}

func SetUser(ctx context.Context, user models.UserAccessor, isOnPrem ...bool) {
	GetHubFromContext(ctx).ConfigureScope(func(scope *sentry.Scope) {
		sentryUser := sentry.User{
			Email: user.GetEmailAddress(),
			ID:    fmt.Sprint(user.GetID()),
			Name:  user.GetName(),
		}

		if len(isOnPrem) > 0 && !isOnPrem[0] {
			if s, ok := user.(models.ServiceIDAccessor); ok {
				sentryUser.Data = map[string]string{
					"ServiceID": fmt.Sprint(s.GetServiceID()),
				}
			}
		}

		scope.SetUser(sentryUser)
	})
}
