package sentry

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLogInsightsURL(t *testing.T) {
	// NOTE: this is a valid URL, you can pull up the logs
	expected := "https://us-east-1.console.aws.amazon.com/cloudwatch/home#logsV2:logs-insights" +
		"$3FqueryDetail$3D$257E$2528" +
		"end$257E$25272023-01-26T19*3A50*3A00.000Z$257E" +
		"start$257E$25272023-01-26T19*3A40*3A00.000Z$257E" +
		"timeType$257E$2527ABSOLUTE$257E" +
		"tz$257E$2527Local$257E" +
		"editorString$257E$2527fields*20*40message*0A*7C*20sort*20*40timestamp*20desc*0A*7C*20limit*20200$257E" +
		"source$257E$2528$257E$2527*2Faws*2Felasticbeanstalk*2Fmercury-staging*2Fvar*2Flog*2Fweb.stdout.log$2529$2529"

	input := logInsightsInput{
		Region:   "us-east-1",
		LogGroup: "/aws/elasticbeanstalk/mercury-staging/var/log/web.stdout.log",
		Start:    "2023-01-26T19:40:00.000Z",
		End:      "2023-01-26T19:50:00.000Z",
		TimeType: "ABSOLUTE",
		Timezone: "Local",
		Query:    "fields @message\n| sort @timestamp desc\n| limit 200",
	}
	assert.Equal(t, expected, input.LogInsightsURL())
}

func TestStringEncode(t *testing.T) {
	assert.Equal(t, "*2Faws*2Felasticbeanstalk*2Fmercury-staging",
		stringEncode("/aws/elasticbeanstalk/mercury-staging"))
	assert.Equal(t, "2023-01-26T19*3A50*3A00.000Z", stringEncode("2023-01-26T19:50:00.000Z"))
	assert.Equal(t, "fields*20*40message*0A*7C*20sort*20*40timestamp*20desc*0A*7C*20limit*20200",
		stringEncode("fields @message\n| sort @timestamp desc\n| limit 200"))

	// No special characters need to be encoded
	assert.Equal(t, "Local", stringEncode("Local"))
}

func TestDoubleEncode(t *testing.T) {
	assert.Equal(t, "$257E$2528", doubleEncode("~("))
	assert.Equal(t, "end$257E$2527", doubleEncode("end~'"))
	assert.Equal(t, "source$257E$2528$257E$2527", doubleEncode("source~(~'"))
}
