package sentry

import (
	"os"
	"time"

	"github.com/getsentry/sentry-go" //nolint:gomodguard // this pkg is allowed to import Sentry SDK directly
)

const FlushTimeout = 2 * time.Second

type (
	Level = sentry.Level
	Scope = sentry.Scope
)

const (
	LevelDebug   = sentry.LevelDebug
	LevelInfo    = sentry.LevelInfo
	LevelWarning = sentry.LevelWarning
	LevelError   = sentry.LevelError
	LevelFatal   = sentry.LevelFatal
)

func Initialize() (err error) {
	opts := sentry.ClientOptions{
		AttachStacktrace: true,
		Dsn:              os.Getenv("SENTRY_DSN"),
		Debug:            os.Getenv("DEBUG") == "true" || os.Getenv("DEBUG") == "1",
		Environment:      os.Getenv("APP_ENV"),
		ServerName:       os.Getenv("AWS_LAMBDA_FUNCTION_NAME"),
	}

	if opts.Dsn != "" {
		opts.BeforeSend = beforeSend()
	}

	return sentry.Init(opts)
}
