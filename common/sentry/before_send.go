package sentry

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/getsentry/sentry-go" //nolint:gomodguard // this pkg is allowed to import Sentry SDK directly
	"github.com/jellydator/ttlcache/v3"
	"go.opentelemetry.io/otel/trace"
)

const cacheTTL = 3 * time.Minute

// BeforeSend handler: add a link to the associated CloudWatch logs
func beforeSend() func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
	logGroup := os.Getenv("AWS_LAMBDA_LOG_GROUP_NAME")
	logStream := os.Getenv("AWS_LAMBDA_LOG_STREAM_NAME")
	axiomTraceDataset := os.Getenv("AXIOM_TRACE_DATASET")
	axiomOrgID := os.Getenv("AXIOM_ORG_ID")

	// Cache event fingerprints locally for a short period of time to avoid sending the same event too often.
	// TODO: distributed cache
	cache := ttlcache.New[string, bool]()

	return func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
		key := eventFingerprint(event)

		// we don't want a Get() operation to reset the TTL
		if cache.Get(key, ttlcache.WithDisableTouchOnHit[string, bool]()) != nil {
			summary := event.Message
			if len(event.Exception) > 0 {
				summary = event.Exception[0].Value
			}

			//nolint:forbidigo // can't use zap logger here, circular dependency
			fmt.Printf("WARNING: dropping Sentry event seen less than %s ago: %s\n", cacheTTL, summary)
			return nil
		}

		cache.Set(key, true, cacheTTL)

		// Add the stack of error types to the default fingerprint to merge less aggressively
		// (e.g. most errors might be captured at the same stack trace in the top-level Lambda handler)
		if event.Fingerprint == nil && len(event.Exception) > 0 {
			event.Fingerprint = []string{"{{ default }}"}

			for _, ex := range event.Exception {
				if ex.Type == "errtypes.HTTPResponseError" {
					//nolint:gocritic // Don't crash application if this regex fails for some reason
					re, err := regexp.Compile(`returned (\d{3}):`)
					if err != nil {
						//nolint:forbidigo // Circular dependency
						fmt.Println("WARN - error regex parsing HTTP status: ", err)
					}

					matches := re.FindStringSubmatch(ex.Value)
					if len(matches) > 1 {
						statusCode := matches[1]
						event.Fingerprint = append(event.Fingerprint, "HTTP Status: "+statusCode)
					}
				}
				event.Fingerprint = append(event.Fingerprint, ex.Type)

			}
		}

		// Add CloudWatch Insights link to issue context
		if logGroup != "" && logStream != "" {
			now := time.Now().UTC()

			event.Contexts["CloudWatch"] = map[string]any{
				// Link to the log stream starting 10 seconds prior to the event
				"LogInsightsURL": logInsightsInput{
					Region:   "us-east-1",
					LogGroup: logGroup,
					Start:    now.Add(-10 * time.Second).Format(time.RFC3339),
					// Logs can take a few seconds to buffer to CloudWatch
					End:      now.Add(2 * time.Second).Format(time.RFC3339),
					TimeType: "ABSOLUTE",
					Timezone: "Local",
					Query: strings.Join([]string{
						"fields @timestamp, @message",
						"| filter @logStream = \"" + logStream + "\"",
						"| sort @timestamp desc",
						"| limit 1000",
					}, "\n"),
				}.LogInsightsURL(),
			}
		}

		// Add link to trace ID
		if axiomTraceDataset != "" && axiomOrgID != "" {
			traceID := trace.SpanContextFromContext(hint.Context).TraceID()
			if traceID.IsValid() {
				u := url.URL{
					Scheme: "https",
					Host:   "app.axiom.co",
					Path:   fmt.Sprintf("/%s/query", axiomOrgID),
				}
				q := u.Query()
				q.Set("traceDataset", axiomTraceDataset)
				q.Set("traceId", traceID.String())
				q.Set("traceStart", time.Now().Add(-1*time.Hour).Format(time.RFC3339))
				q.Set("traceEnd", time.Now().Add(1*time.Hour).Format(time.RFC3339))
				u.RawQuery = q.Encode()

				event.Contexts["Axiom"] = map[string]any{
					"AxiomTraceURL": u.String(),
				}

			}
		}

		return event
	}
}

func eventFingerprint(event *sentry.Event) string {
	if len(event.Fingerprint) > 0 {
		// the event already has a custom fingerprint
		return strings.Join(event.Fingerprint, ";")
	}

	if len(event.Exception) > 0 {
		// sentry.CaptureException leads here
		return stacktraceFingerprint(event.Exception[0].Stacktrace)
	}

	if len(event.Threads) > 0 {
		// sentry.CaptureMessage leads here
		return stacktraceFingerprint(event.Threads[0].Stacktrace)
	}

	// unlikely to be here, but if so, fallback to Message (note: may be blank)
	return event.Message
}

// Generate a fingerprint of the stacktrace based on the module, function, and line number of each stack frame.
//
// This approximates Sentry's default fingerprint.
func stacktraceFingerprint(s *sentry.Stacktrace) string {
	if s == nil || len(s.Frames) == 0 {
		return ""
	}

	hasher := sha256.New()

	for _, f := range s.Frames {
		hasher.Write([]byte(f.Module))
		hasher.Write([]byte(f.Function))
		hasher.Write([]byte(strconv.Itoa(f.Lineno))) // disambiguate multiple Sentry events from the same function
	}

	return hex.EncodeToString(hasher.Sum(nil))
}
