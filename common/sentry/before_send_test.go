package sentry

import (
	"testing"

	"github.com/getsentry/sentry-go" //nolint:gomodguard // this pkg is allowed to import Sentry SDK directly
	"github.com/stretchr/testify/assert"
)

// Subsequent errors with the same custom fingerprint are dropped
func TestBeforeSendCustomFingerprint(t *testing.T) {
	t.Parallel()

	f := beforeSend()
	event := &sentry.Event{Fingerprint: []string{"abc", "def"}, Message: "<PERSON><PERSON><PERSON> loves tests"}

	// First error is sent
	assert.Equal(t, event, f(event, nil))

	// Subsequent errors are dropped
	assert.Nil(t, f(event, nil))
	assert.Nil(t, f(event, nil))
	assert.Nil(t, f(event, nil))
}

// Subsequent errors with the same stacktrace are dropped (even if the error msg differs)
func TestBeforeSendException(t *testing.T) {
	t.Parallel()

	f := beforeSend()

	// Based on real example:
	// https://sentry.io/organizations/axle-technologies/issues/3849462487/events/73f7430ec09b4012be3a2130079bd7c9
	ex := sentry.Exception{
		Type:   "*pgconn.PgError",
		Value:  "ERROR: syntax error",
		Module: "github.com/drumkitai/oracle/database",
		Stacktrace: &sentry.Stacktrace{
			Frames: []sentry.Frame{
				{
					Module:   "github.com/drumkitai/oracle/database",
					Function: "sentryDBHandler",
					Lineno:   170,
				},
				{
					Module:   "gorm.io/gorm",
					Function: "(*processor).Execute",
					Lineno:   130,
				},
			},
		},
	}
	event := &sentry.Event{Exception: []sentry.Exception{ex}}

	// First error is sent
	assert.Equal(t, event, f(event, nil))

	// Subsequent errors are dropped, even if we change the error value
	event.Fingerprint = nil
	assert.Nil(t, f(event, nil))

	ex.Value = "ERROR: db unreachable"
	event.Fingerprint = nil
	assert.Nil(t, f(&sentry.Event{Exception: []sentry.Exception{ex}}, nil))
}

// Subsequent messages with the same stacktrace are dropped (even if the msg differs)
func TestBeforeSendMessage(t *testing.T) {
	t.Parallel()

	f := beforeSend()

	// Based on real example
	// https://sentry.io/organizations/axle-technologies/issues/3832708537/events/d63e3f368f7149f29ea65e94a2f44d09
	event := &sentry.Event{
		Message: "warning: location.DateTime is stale (2023-01-06 ...)",
		Threads: []sentry.Thread{
			{
				Stacktrace: &sentry.Stacktrace{
					Frames: []sentry.Frame{
						{
							Module:   "mercury/routes/vehicle",
							Function: "GetVehicleLocation",
							Lineno:   79,
						},
					},
				},
			},
		},
	}

	// First error is sent
	assert.Equal(t, event, f(event, nil))

	// Subsequent errors are dropped, even if we change the message
	assert.Nil(t, f(event, nil))

	event.Message = "warning: location.DateTime is stale (0001-01-01 ...)"
	assert.Nil(t, f(event, nil))
}

// If no stacktrace is available, deduplicate only by the event message
func TestBeforeSendNoStacktrace(t *testing.T) {
	t.Parallel()

	f := beforeSend()

	event := &sentry.Event{
		Message: "warning: location.DateTime is stale (2023-01-06 ...)",
	}

	// First error is sent
	assert.Equal(t, event, f(event, nil))

	// Subsequent errors are dropped, unless we change the message
	assert.Nil(t, f(event, nil))

	event.Message = "warning: location.DateTime is stale (0001-01-01 ...)"
	assert.Equal(t, event, f(event, nil))
}

func TestStacktraceFingerprintNoFrames(t *testing.T) {
	t.Parallel()

	assert.Equal(t, "", stacktraceFingerprint(nil))
	assert.Equal(t, "", stacktraceFingerprint(&sentry.Stacktrace{}))
}

func TestStacktraceFingerprintSingleFrame(t *testing.T) {
	t.Parallel()

	stack := &sentry.Stacktrace{
		Frames: []sentry.Frame{
			{
				Module:   "mercury/middlewares",
				Function: "CheckToken",
			},
		},
	}

	const expected = "4b67f74f42c5c7a58f18be1973dbceb46d53eed8f721f0c3430e36a058ac60b3"
	assert.Equal(t, expected, stacktraceFingerprint(stack))

	// A different line number results in a different hash
	stack.Frames[0].Lineno = 1
	assert.NotEqual(t, expected, stacktraceFingerprint(stack))
}

func TestStacktraceFingerprintMultipleFrames(t *testing.T) {
	t.Parallel()

	stack := &sentry.Stacktrace{
		Frames: []sentry.Frame{
			{
				Module:   "mercury/middlewares",
				Function: "CheckToken",
				Lineno:   1,
			},
			{
				Module:   "github.com/gofiber/fiber/v2",
				Function: "(*Ctx).Next",
				Lineno:   2,
			},
			{
				Module:   "github.com/drumkitai/oracle/integrations/verizon",
				Function: "GetDrivers",
				Lineno:   3,
			},
		},
	}

	const expected = "a4b62962a44f973860e1c5bbc7a8557cd4734ff159eebfc1378326ebc65981e0"
	assert.Equal(t, expected, stacktraceFingerprint(stack))

	// Try hashing a real stack trace
	assert.NotEmpty(t, stacktraceFingerprint(sentry.NewStacktrace()))
}

func TestExceptionTypesFingerprint(t *testing.T) {
	t.Parallel()

	// exception types are added to the fingerprint
	f := beforeSend()

	event := &sentry.Event{
		Exception: []sentry.Exception{
			{Type: "*fmt.wrapError"},
			{Type: "*errors.errorString"},
		},
	}
	f(event, nil)

	assert.Equal(t, []string{"{{ default }}", "*fmt.wrapError", "*errors.errorString"}, event.Fingerprint)
}
