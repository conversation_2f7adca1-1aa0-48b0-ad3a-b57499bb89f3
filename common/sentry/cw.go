package sentry

import (
	"net/url"
	"strings"
)

type logInsightsInput struct {
	Region   string // e.g. "us-east-1"
	LogGroup string // e.g. "/aws/elasticbeanstalk/mercury-staging/var/log/web.stdout.log"
	Start    string // e.g. "2023-01-26T19:40:00.000Z"
	End      string // e.g. "2023-01-26T19:50:00.000Z
	TimeType string // "RELATIVE" or "ABSOLUTE"
	Timezone string // "Local" or omitted for UTC
	Query    string // e.g. "fields @message | filter @message LIKE "..." | sort @timestamp desc | limit 200"
}

//nolint:lll
// CloudWatch Insights URLs are essentially triple-encoded.
// To understand how, here is a breakdown of a Mercury staging query over a 10-minute absolute window:
//
// https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:logs-insights
// $3FqueryDetail$3D$257E$2528
//    -->     ($) ?queryDetail=%7E%28
//    ---->   (%) ?queryDetail=~(
// end$257E$25272023-01-26T19*3a50*3a00.000Z$257E
//    -->     ($) end%7E%272023-01-26T19*3a50*3a00.000Z%7E
//    ---->   (%) end~'2023-01-26T19*3a50*3a00.000Z~
//    ------> (*) end~'2023-01-26T19:50:00.000Z~
// start$257E$25272023-01-26T19*3a40*3a00.000Z$257E
//    -->     ($) start%7E%272023-01-26T19*3a40*3a00.000Z%7E
//    ---->   (%) start~'2023-01-26T19*3a40*3a00.000Z~
//    ------> (*) start~'2023-01-26T19:40:00.000Z~
// timeType$257E$2527ABSOLUTE$257E
//    -->     ($) timeType%7E%27ABSOLUTE%7E
//    ---->   (%) timeType~'ABSOLUTE~
// tz$257E$2527Local$257E
//    -->     ($) tz%7E%27Local%7E
//    ---->   (%) tz~'Local~
// editorString$257E$2527fields*20*40timestamp*2c*20*40message*0a*7c*20filter*20*40logStream*20*3d*20*22i-0d23f9b295e275033*22*0a*7c*20sort*20*40timestamp*20desc*0a*7c*20limit*20200$257E
//    -->     ($) editorString%7E%27fields*20*40timestamp*2c*20*40message*0a*7c*20filter*20*40logStream*20*3d*20*22i-0d23f9b295e275033*22*0a*7c*20sort*20*40timestamp*20desc*0a*7c*20limit*20200%7E
//    ---->   (%) editorString~'fields*20*40timestamp*2c*20*40message*0a*7c*20filter*20*40logStream*20*3d*20*22i-0d23f9b295e275033*22*0a*7c*20sort*20*40timestamp*20desc*0a*7c*20limit*20200~
//    ------> (*) editorString~'fields @timestamp, @message\n| filter @logStream = "i-0d23f9b295e275033"\n| sort @timestamp desc\n| limit 200~   (technically, *0a is LF)
// isLiveTail$257Efalse$257E
//    -->     ($) isLiveTail%7Efalse%7E
//    ---->   (%) isLiveTail~false~
// queryId$257E$252726e1325a-1747-4bce-b818-ecc6426817f6$257E
//    -->     ($) queryId%7E%2726e1325a-1747-4bce-b818-ecc6426817f6%7E
//    ---->   (%) queryId~'26e1325a-1747-4bce-b818-ecc6426817f6~
// source$257E$2528$257E$2527*2faws*2felasticbeanstalk*2fmercury-staging*2fvar*2flog*2fweb.stdout.log$2529$2529
//    -->     ($) source%7E%28%7E%27*2faws*2felasticbeanstalk*2fmercury-staging*2fvar*2flog*2fweb.stdout.log%29%29
//    ---->   (%) source~(~'*2faws*2felasticbeanstalk*2fmercury-staging*2fvar*2flog*2fweb.stdout.log))
//    ------> (*) source~(~'/aws/elasticbeanstalk/mercury-staging/var/log/web.stdout.log))

func (input logInsightsInput) LogInsightsURL() string {
	var sb strings.Builder

	sb.WriteString("https://")
	sb.WriteString(input.Region)
	sb.WriteString(".console.aws.amazon.com/cloudwatch/home#logsV2:logs-insights$3FqueryDetail$3D")

	sb.WriteString(doubleEncode("~(end~'"))
	sb.WriteString(stringEncode(input.End))

	sb.WriteString(doubleEncode("~start~'"))
	sb.WriteString(stringEncode(input.Start))

	sb.WriteString(doubleEncode("~timeType~'"))
	sb.WriteString(stringEncode(input.TimeType))

	if input.Timezone != "" {
		sb.WriteString(doubleEncode("~tz~'"))
		sb.WriteString(stringEncode(input.Timezone))
	}

	sb.WriteString(doubleEncode("~editorString~'"))
	sb.WriteString(stringEncode(input.Query))

	sb.WriteString(doubleEncode("~source~(~'"))
	sb.WriteString(stringEncode(input.LogGroup))

	sb.WriteString(doubleEncode("))"))

	return sb.String()
}

// String parameters are url-encoded and % is replaced with *
// Example: "/aws/elasticbeanstalk/mercury-staging" becomes "*2Faws*2Felasticbeanstalk*2Fmercury-staging"
//
// Note: in the AWS console, the string URL-encoding is lowercased, e.g. "*2f"
// Here, we return the "*2F" uppercase encoding, which also seems to work.
func stringEncode(s string) string {
	return strings.ReplaceAll(
		// Normal url-encoding uses + for a space, but we need *20
		strings.ReplaceAll(url.QueryEscape(s), "+", "*20"),
		"%", "*")
}

// For query detail outside the string values, replace ~ and URL-encode twice
func doubleEncode(s string) string {
	encoded := strings.ReplaceAll(url.QueryEscape(s), "~", "%7E")
	return strings.ReplaceAll(url.QueryEscape(encoded), "%", "$")
}
