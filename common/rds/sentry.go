package rds

import (
	"errors"
	"strings"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/sentry"
)

// Register gorm callbacks to report any DB errors to Sentry: https://gorm.io/docs/write_plugins.html
func sentryCallbacks(db *gorm.DB) error {
	if err := db.Callback().Create().After("*").Register("sentry:after_create", sentryDBHandler); err != nil {
		return err
	}

	if err := db.Callback().Query().After("*").Register("sentry:after_query", sentryDBHandler); err != nil {
		return err
	}

	if err := db.Callback().Update().After("*").Register("sentry:after_update", sentryDBHandler); err != nil {
		return err
	}

	return db.Callback().Delete().After("*").Register("sentry:after_delete", sentryDBHandler)
}

// Gorm callback handler: capture db.Error for Sentry
func sentryDBHandler(db *gorm.DB) {
	if db.Error == nil || errors.Is(db.Error, gorm.ErrRecordNotFound) || db.Statement == nil {
		// ignore 'not found' errors, which are expected in normal operation
		return
	}

	if strings.Contains(db.Error.Error(),
		"duplicate key value violates unique constraint \"idx_emails_external_id\"") {

		// ignore dup key errors for the emails table
		return
	}

	hub := sentry.GetHubFromContext(db.Statement.Context)
	hub.WithScope(func(scope *sentry.Scope) {
		// Show raw SQL query in the Sentry issue context
		if query := db.Explain(db.Statement.SQL.String(), db.Statement.Vars...); query != "" {
			scope.SetContext("SQL", map[string]any{"Query": query})
		}

		hub.CaptureException(db.Error)
	})
}
