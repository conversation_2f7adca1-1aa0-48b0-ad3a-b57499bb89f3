package onpremuser

import (
	"context"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, updatedUser models.OnPremUser) error {
	updatedUser.EmailAddress = strings.ToLower(updatedUser.EmailAddress)

	return rds.WithContext(ctx).Model(&updatedUser).Updates(updatedUser).Error
}

// Explicitly updates backfill start time because Gorm default behavior is to not update null/empty values
func UpdateBackfillLock(ctx context.Context, user *models.OnPremUser, startTime models.NullTime) error {
	return rds.WithContext(ctx).Model(user).Clauses(clause.Returning{}).
		Select("BackfillStartTime").
		Updates(models.OnPremUser{BackfillStartTime: startTime}).
		Error
}
