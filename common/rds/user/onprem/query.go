package onpremuser

import (
	"context"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByID(ctx context.Context, id uint) (user models.OnPremUser, err error) {
	return user, rds.WithContextReader(ctx).Where("id = ?", id).First(&user).Error
}

func GetByEmail(ctx context.Context, emailAddr string) (user models.OnPremUser, err error) {
	return user, rds.WithContextReader(ctx).
		Where("lower(email_address) = ?", strings.ToLower(emailAddr)).
		First(&user).Error
}

func GetByExternalID(ctx context.Context, userExternalID string) (user models.OnPremUser, err error) {
	return user, rds.WithContextReader(ctx).Where("mail_client_id = ?", userExternalID).First(&user).Error
}

func GetByOutlookIDs(ctx context.Context, userExternalID, clientState string) (user models.OnPremUser, err error) {
	return user, rds.WithContextReader(ctx).
		Where("mail_client_id = ? AND outlook_client_state = ?", userExternalID, clientState).
		First(&user).Error
}
