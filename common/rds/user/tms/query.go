package tmsuser

import (
	"context"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetUsersByTMSID(ctx context.Context, integID uint) (res []models.TMSUser, err error) {
	return res, rds.WithContextReader(ctx).Where("tms_id = ?", integID).Find(&res).Error
}

// Case-insensitive search
func GetByUsername(ctx context.Context, username string, integID uint) (res models.TMSUser, err error) {
	return res, rds.WithContextReader(ctx).
		Where("lower(username) = ? AND tms_id = ?", strings.ToLower(username), integID).
		First(&res).Error
}
