package tmsuser

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Refreshes list of TMS users/operators by deleting existing users from DB and adding current ones.
// This way, Drumkit captures not only new users in the TMS but also deleted ones.
func RefreshUsers(ctx context.Context, users []models.TMSUser) error {
	if len(users) == 0 {
		return errors.New("empty slice of TMSUsers")
	}

	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().
			Where("tms_id = ?", users[0].TMSID).Delete(&models.TMSUser{}).Error
		if err != nil {
			return fmt.Errorf("error deleting existing users for TMS %d: %w", users[0].TMSID, err)
		}

		return tx.CreateInBatches(&users, 1000).Error
	})
}
