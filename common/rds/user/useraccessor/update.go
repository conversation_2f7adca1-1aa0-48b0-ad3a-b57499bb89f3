package useraccessor

import (
	"context"
	"errors"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, user models.UserAccessor) error {
	user.SetEmailAddress(strings.ToL<PERSON>er(user.GetEmailAddress()))

	switch u := user.(type) {
	case *models.User:
		// Update the User model
		return rds.WithContext(ctx).Model(u).Updates(u).Error
	case *models.OnPremUser:
		// Update the OnPremUser model
		return rds.WithContext(ctx).Model(u).Updates(u).Error
	default:
		return errors.New("unsupported user type")
	}
}
