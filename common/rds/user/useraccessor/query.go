package useraccessor

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByEmail(
	ctx context.Context,
	emailAddr string,
	modelType models.UserType,
) (user models.UserAccessor, err error) {

	db := rds.WithContextReader(ctx).Where("lower(email_address) = ?", emailAddr)

	switch modelType {
	case models.InternalUserType:
		var user models.User
		return &user, db.First(&user).Error
	case models.OnPremUserType:
		var user models.OnPremUser
		return &user, db.First(&user).Error
	default:
		return nil, errors.New("unsupported model type")
	}
}
