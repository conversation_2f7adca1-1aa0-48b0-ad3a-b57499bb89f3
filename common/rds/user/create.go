package user

import (
	"context"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Create(ctx context.Context, user *models.User) error {
	user.EmailAddress = strings.ToL<PERSON>er(user.EmailAddress)

	switch user.EmailAddress {
	case "<EMAIL>", "<EMAIL>":
		user.DATEmailAddress = "<EMAIL>"
		user.HasGrantedDATPermissions = true
	case "<EMAIL>", "<EMAIL>":
		user.DATEmailAddress = "<EMAIL>"
		user.HasGrantedDATPermissions = true
	case "<EMAIL>", "jstan<PERSON><EMAIL>":
		user.DATEmailAddress = "<EMAIL>"
		user.HasGrantedDATPermissions = true
	case "<EMAIL>":
		user.DATEmailAddress = "<EMAIL>"
		user.HasGrantedDATPermissions = true
	}

	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
	).Create(user).Error
}
