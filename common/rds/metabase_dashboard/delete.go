package metabasedashboard

import (
	"context"
	"errors"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Delete deletes a Metabase dashboard by its ID
func Delete(ctx context.Context, id uint) error {
	result := rds.WithContext(ctx).Delete(&models.MetabaseDashboard{}, id)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("dashboard not found")
	}

	return nil
}
