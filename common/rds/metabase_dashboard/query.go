package metabasedashboard

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetByID retrieves a Metabase dashboard by its ID
func GetByID(ctx context.Context, id uint) (models.MetabaseDashboard, error) {
	var dashboard models.MetabaseDashboard
	return dashboard, rds.WithContextReader(ctx).Where("id = ?", id).First(&dashboard).Error
}

// GetByServiceID retrieves all Metabase dashboards for a specific service
func GetByServiceID(ctx context.Context, serviceID uint) ([]models.MetabaseDashboard, error) {
	var dashboards []models.MetabaseDashboard
	return dashboards, rds.WithContextReader(ctx).Where("service_id = ?", serviceID).Find(&dashboards).Error
}

// GetByUserID retrieves all Metabase dashboards for a specific user
func GetByUserID(ctx context.Context, userID uint) ([]models.MetabaseDashboard, error) {
	var dashboards []models.MetabaseDashboard
	return dashboards, rds.WithContextReader(ctx).Where("user_id = ?", userID).Find(&dashboards).Error
}

// GetByServiceAndUserID retrieves all Metabase dashboards for a specific service and user
func GetByServiceAndUserID(ctx context.Context, serviceID, userID uint) ([]models.MetabaseDashboard, error) {
	var dashboards []models.MetabaseDashboard

	err := rds.WithContextReader(ctx).
		Where("service_id = ? AND user_id = ?", serviceID, userID).
		Find(&dashboards).Error

	return dashboards, err
}
