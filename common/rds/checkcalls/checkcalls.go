package checkcalls

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func BatchUpsertCheckCalls(ctx context.Context, calls []models.CheckCall) error {
	if len(calls) == 0 {
		log.Debug(ctx, "skipping upsert for empty list of check calls")

		return nil
	}

	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			// TODO: combo unique index on freight ID and service/TMS, not just freight ID
			Columns: []clause.Column{{Name: "freight_tracking_id"},
				{Name: "date_time"}, {Name: "date_time_without_timezone"}},
			// NOTE: Gorm bug, When DoNothing: true, Gorm duplicates the objects that were actually inserted
			// in the returned list. We circumvent that by updating all the rows. Other option is to re-query.
			UpdateAll: true}).
		Create(&calls).Error
}
