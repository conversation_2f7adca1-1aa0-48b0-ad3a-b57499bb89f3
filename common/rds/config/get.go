package config

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetCarrierQuoteConfigByServiceID(ctx context.Context, serviceID uint) (*models.CarrierQuoteConfig, error) {
	var config models.CarrierQuoteConfig

	if err := rds.WithContextReader(ctx).Where("service_id = ?", serviceID).First(&config).Error; err != nil {
		return nil, err
	}

	return &config, nil
}
