package tmscustomerprompts

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	CustomerPromptKey = "tms_customer_prompt:%d:%d:%s:%s"
	TMSPromptKey      = "tms_prompt:%d:%s:%s"

	CustomerPromptTTL = 1 * time.Hour
	TMSPromptTTL      = 1 * time.Hour
)

// GetCustomerPrompt gets the active prompt for a specific customer, TMS, and extractor
func GetCustomerPrompt(
	ctx context.Context,
	tmsID uint,
	customerID uint,
	feature string,
	extractorName string,
) (*models.TMSCustomerPrompt, error) {
	// Try Redis first
	key := fmt.Sprintf(CustomerPromptKey, tmsID, customerID, feature, extractorName)
	prompt, found, err := redis.GetKey[string](ctx, key)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting tms_customer_prompt from redis", zap.Error(err))
	}

	if found {
		return &models.TMSCustomerPrompt{
			TMSIntegrationID: tmsID,
			TMSCustomerID:    &customerID,
			ExtractorName:    extractorName,
			PromptText:       prompt,
		}, nil
	}

	// Fall back to database
	var promptModel models.TMSCustomerPrompt
	err = rds.WithContextReader(ctx).
		Where(
			"tms_integration_id = ? AND tms_customer_id = ? AND scope = ? "+
				"AND feature = ? AND extractor_name = ? AND is_active = ?",
			tmsID,
			customerID,
			models.CustomerScope,
			feature,
			extractorName,
			true,
		).
		First(&promptModel).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// Cache the result in Redis
	if err := redis.SetKey(ctx, key, promptModel.PromptText, CustomerPromptTTL); err != nil {
		log.Warn(ctx, "error setting tms_customer_prompt in redis", zap.Error(err))
	}

	return &promptModel, nil
}

// GetTMSWidePrompt gets the active TMS-wide prompt for a specific extractor
func GetTMSWidePrompt(
	ctx context.Context,
	tmsID uint,
	feature string,
	extractorName string,
) (*models.TMSCustomerPrompt, error) {
	// Try Redis first
	key := fmt.Sprintf(TMSPromptKey, tmsID, feature, extractorName)
	prompt, found, err := redis.GetKey[string](ctx, key)
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "error getting tms_prompt from redis", zap.Error(err))
	}

	if found {
		return &models.TMSCustomerPrompt{
			TMSIntegrationID: tmsID,
			TMSCustomerID:    nil, // TMS-wide prompt has no customer ID
			ExtractorName:    extractorName,
			PromptText:       prompt,
		}, nil
	}

	// Fall back to database
	var promptModel models.TMSCustomerPrompt
	err = rds.WithContextReader(ctx).
		Where(
			"tms_integration_id = ? AND scope = ? AND feature = ? AND extractor_name = ? AND is_active = ?",
			tmsID,
			models.TMSScope,
			feature,
			extractorName,
			true,
		).
		First(&promptModel).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// Cache the result in Redis
	if err := redis.SetKey(ctx, key, promptModel.PromptText, TMSPromptTTL); err != nil {
		log.Warn(ctx, "error setting tms_prompt in redis", zap.Error(err))
	}

	return &promptModel, nil
}

// GetActiveCustomerPrompts gets all active prompts for a TMS customer with lazy Redis caching
func GetActiveCustomerPrompts(
	ctx context.Context,
	tmsID uint,
	customerID uint,
	feature string,
) ([]models.TMSCustomerPrompt, error) {

	var prompts []models.TMSCustomerPrompt
	var missingExtractors []models.PromptExtractorName

	for _, extractorName := range models.AllLoadBuildingExtractors {
		key := fmt.Sprintf(CustomerPromptKey, tmsID, customerID, feature, extractorName)
		if prompt, found, err := redis.GetKey[string](ctx, key); found && err == nil {
			prompts = append(prompts, models.TMSCustomerPrompt{
				TMSIntegrationID: tmsID,
				TMSCustomerID:    &customerID,
				ExtractorName:    string(extractorName),
				PromptText:       prompt,
			})
		} else {
			missingExtractors = append(missingExtractors, extractorName)
		}
	}

	// If any prompts missing, fetch all from DB and populate Redis only for existing prompts
	if len(missingExtractors) > 0 {
		var dbPrompts []models.TMSCustomerPrompt
		err := rds.WithContextReader(ctx).
			Where(
				"tms_integration_id = ? AND tms_customer_id = ? AND feature = ? AND is_active = ?",
				tmsID,
				customerID,
				feature,
				true,
			).
			Find(&dbPrompts).Error
		if err != nil {
			return nil, err
		}

		// Create map for easy lookup
		dbPromptMap := make(map[models.PromptExtractorName]models.TMSCustomerPrompt)
		for _, prompt := range dbPrompts {
			dbPromptMap[models.PromptExtractorName(prompt.ExtractorName)] = prompt
		}

		// Only cache prompts that actually exist
		for _, extractorName := range missingExtractors {
			if prompt, exists := dbPromptMap[extractorName]; exists {
				key := fmt.Sprintf(CustomerPromptKey, tmsID, customerID, feature, extractorName)
				if err := redis.SetKey(ctx, key, prompt.PromptText, CustomerPromptTTL); err != nil {
					log.Warn(ctx, "error setting tms_customer_prompt in redis", zap.Error(err))
				}
				prompts = append(prompts, prompt)
			}
			// NOTE: If we want to negative cache as this feature scales, we can do that here.
		}
	}

	return prompts, nil
}

// GetActiveTMSPrompts gets all active TMS-wide prompts with lazy Redis caching
func GetActiveTMSPrompts(ctx context.Context, tmsID uint, feature string) ([]models.TMSCustomerPrompt, error) {
	// Try to get each prompt from Redis individually
	var prompts []models.TMSCustomerPrompt
	var missingExtractors []models.PromptExtractorName

	for _, extractorName := range models.AllLoadBuildingExtractors {
		key := fmt.Sprintf(TMSPromptKey, tmsID, feature, extractorName)
		if prompt, found, err := redis.GetKey[string](ctx, key); found && err == nil {
			prompts = append(prompts, models.TMSCustomerPrompt{
				TMSIntegrationID: tmsID,
				TMSCustomerID:    nil, // TMS-wide prompt has no customer ID
				ExtractorName:    string(extractorName),
				PromptText:       prompt,
			})
		} else {
			missingExtractors = append(missingExtractors, extractorName)
		}
	}

	// If any prompts missing, fetch all from DB and populate Redis only for existing prompts
	if len(missingExtractors) > 0 {
		var dbPrompts []models.TMSCustomerPrompt
		err := rds.WithContextReader(ctx).
			Where(
				"tms_integration_id = ? AND scope = ? AND feature = ? AND is_active = ?",
				tmsID,
				models.TMSScope,
				feature,
				true,
			).
			Find(&dbPrompts).Error
		if err != nil {
			return nil, err
		}

		// Create map for easy lookup
		dbPromptMap := make(map[string]models.TMSCustomerPrompt)
		for _, prompt := range dbPrompts {
			dbPromptMap[prompt.ExtractorName] = prompt
		}

		// Only cache prompts that actually exist
		for _, extractorName := range missingExtractors {
			if prompt, exists := dbPromptMap[string(extractorName)]; exists {
				key := fmt.Sprintf(TMSPromptKey, tmsID, feature, extractorName)
				if err := redis.SetKey(ctx, key, prompt.PromptText, TMSPromptTTL); err != nil {
					log.Warn(ctx, "error setting tms_prompt in redis", zap.Error(err))
				}
				prompts = append(prompts, prompt)
			}
			// NOTE: If we want to negative cache as this feature scales, we can do that here.
		}
	}

	return prompts, nil
}
