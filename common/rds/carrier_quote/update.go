package carrierquote

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, quoteReqID uint, newQuote *models.CarrierQuote) error {
	txErr := rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var existingQuote models.CarrierQuote

		err := tx.
			Model(newQuote).
			Where("quote_request_id = ? AND thread_id = ?", quoteReqID, newQuote.ThreadID).
			First(&existingQuote).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		// Don't override the existing quote value if carrier sends additional emails e.g. broker asks
		// clarifying question and carrier responds without changing the original quote they provided.
		// If later in thread carrier rescinds offer, then we override SuggestedQuote.isAvailable=false
		// This logic assumes LLM correctly detects when carrier provides & rescinds quotes
		existingSug := existingQuote.SuggestedQuote
		newSug := newQuote.SuggestedQuote

		if existingSug != nil && newSug != nil && newSug.IsAvailable {
			newSug.IsAvailable = helpers.Or(newSug.IsAvailable, existingSug.IsAvailable)
			newSug.TotalCost = helpers.Or(newSug.TotalCost, existingSug.TotalCost)
		}

		//nolint:gocritic // This is intended
		newQuote.SuggestedQuoteHistory = append(existingQuote.SuggestedQuoteHistory, models.MiniCarrierQuote{
			EmailID:   newQuote.EmailID,
			Timestamp: time.Now(),
			Quote:     newQuote.SuggestedQuote,
		})

		return tx.Model(newQuote).Clauses(clause.Returning{}).
			Where("thread_id = ? AND quote_request_id = ?", newQuote.ThreadID, quoteReqID).
			Updates(newQuote).Error
	})

	return txErr
}
