package quoterequest

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	tmscustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
)

const (
	// Default threshold for considering a load won by field similarity;
	// downstream metrics can filter for higher thresholds if needed
	WonLoadScoreThreshold = 70
)

// 30 days, there's a lag between when shipper first requests a quote and when broker wins it and are ready to ship
var WonLoadLookupWindow = 30 * 24 * time.Hour

func GetRequestByID(ctx context.Context, id uint) (res models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("id = ?", id).
		First(&res).Error
}

func GetRequestByEmailID(ctx context.Context, emailID uint) (res []models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("email_id = ?", emailID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Vector associations are not preloaded by default clause.Associations as they are a many2many association.
func GetRequestByEmailIDWithVectors(ctx context.Context, emailID uint) (res []models.QuoteRequest, err error) {
	return res, rds.WithContext(ctx).
		Preload(clause.Associations).
		Preload("Vectors").
		Where("email_id = ?", emailID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Gets quote request by threadID and preloads associations
func GetRequestByThreadID(ctx context.Context, threadID string) (res []models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("thread_id = ? AND (status = 'pending' OR status = 'inFlight')", threadID).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}

// Gets quote request and preloads carrier quoting associations (emails + quotes).
// Carrier Quoting supports shared/delegated inboxes, so this function maps the user's thread ID to the shared inbox's
// thread ID so that User B can review a CQ initiated by User A. For details, see
// https://linear.app/drumkit/issue/ENG-3280/allow-other-users-to-view-cq-initiated-by-other-user
//
// Note that for the CarrierEmails associations, this function returns
// only the first message of each thread that Drumkit sent to the carriers on behalf of the user.
func GetRequestByThreadIDPreloadAssociation(
	ctx context.Context,
	userID uint,
	threadID string,
	service models.Service,
) (res models.QuoteRequest, err error) {

	if !service.IsDelegatedInboxEnabled {
		log.Info(ctx, "delegated inbox disabled, fetching quote request only by user")

		err = rds.WithContextReader(ctx).
			Preload(clause.Associations).
			// clause.Associations doesn't preload nested associations, so we need to do it manually
			Preload("CarrierQuotes.CarrierLocation").
			Preload("CarrierQuotes.CarrierLocation.TMSCarrier").
			First(&res, "thread_id = ?", threadID).Error

		return res, err
	}

	log.Info(ctx, "delegated inbox enabled, fetching quote request by user/service")
	// If service has delegated/shared inboxes, then User B should be able to see in-progress CQ initiated by User A.
	// https://linear.app/drumkit/issue/ENG-3280/allow-other-users-to-view-cq-initiated-by-other-user#comment-78da709e
	err = rds.WithContextReader(ctx).
		Raw(`
		-- Even though subqueries fetch pool of RFC IDs/threads in order to map User B's thread to User A's version,
		-- QR is associated with only 1 of those thread IDs.
		SELECT * FROM quote_requests qr
		WHERE qr.thread_id IN (
			-- Returns 1+ thread IDs from the user's service
			SELECT thread_id FROM emails
			WHERE service_id = ? AND rfc_message_id IN (
				-- A thread contains 1+ emails and thus 1+ RFC message IDs for the thread
				SELECT rfc_message_id FROM emails
				WHERE user_id = ? AND thread_id = ?
			)
		)
		-- But this filters to QRs that
		-- 1) has carrier emails associated with it and
		-- 2) QR is associated with thread belonging to user or the service
		AND EXISTS (
			SELECT 1 FROM quote_request_carrier_emails_1tomany ce WHERE ce.quote_request_id = qr.id
		)
		LIMIT 1
	`, service.ID, userID, threadID).
		Scan(&res).Error

	if err != nil {
		return res, err
	}

	if res.ID == 0 {
		log.Info(ctx, "quote request ID is 0, returning gorm.ErrRecordNotFound")
		return res, gorm.ErrRecordNotFound
	}

	// Re-fetch with preloaded associations
	err = rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Preload("CarrierQuotes.CarrierLocation").
		Preload("CarrierQuotes.CarrierLocation.TMSCarrier").
		First(&res, "id = ?", res.ID).Error

	return res, err
}

func GetQuoteRequestBySourceExternalID(
	ctx context.Context,
	serviceID uint,
	sourceExternalID string,
) (res models.QuoteRequest, err error) {
	return res, rds.WithContextReader(ctx).
		Where("service_id = ? AND source_external_id = ?", serviceID, sourceExternalID).
		First(&res).Error
}

func GetThirdPartyURLsByEmailID(ctx context.Context, emailID uint) (res models.ThirdPartyQuoteURLs, err error) {
	return res, rds.WithContextReader(ctx).
		Model(&models.QuoteRequest{}).
		Where("email_id = ?", emailID).
		Select("third_party_quote_urls").
		First(&res).Error
}

func GetConfigByServiceID(ctx context.Context, serviceID uint) (res models.QuickQuoteConfig, err error) {
	return res, rds.WithContextReader(ctx).
		Model(&models.QuickQuoteConfig{}).
		Where("service_id = ?", serviceID).
		First(&res).Error
}

//
// Metric queries
//

func GetQuoteRequests(ctx context.Context, serviceID uint, opts ...Option) (res []models.QuoteRequest, err error) {
	options := &queryOptions{
		dateRange: DateRangeOption{
			StartDate: nil,
			EndDate:   nil,
			NoLimit:   false,
		},
		userID:     nil,
		customerID: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	query := rds.WithContextReader(ctx).
		Model(&models.QuoteRequest{}).
		Where("service_id = ?", serviceID)

	if options.userID != nil {
		query = query.Where("user_id = ?", options.userID)
	}

	if options.customerID != nil {
		query = query.Where(
			"applied_customer_id = ? OR suggested_customer_id = ?",
			options.customerID,
			options.customerID,
		)
	}

	if !options.dateRange.NoLimit {
		if options.dateRange.StartDate != nil {
			query = query.Where("created_at >= ?", options.dateRange.StartDate)
		}
		if options.dateRange.EndDate != nil {
			query = query.Where("created_at <= ?", options.dateRange.EndDate)
		}
	}

	return res, query.Find(&res).Error
}

// PreloadQuoteRequestWithVectors preloads a quote request with its vectors
func PreloadQuoteRequestWithVectors(ctx context.Context, quoteRequestID uint) (*models.QuoteRequest, error) {
	var quoteRequest models.QuoteRequest
	err := rds.WithContextReader(ctx).Preload("Vectors").First(&quoteRequest, quoteRequestID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to reload quote request with vectors: %w", err)
	}
	return &quoteRequest, nil
}

// GetQuoteRequestByLoadSuggestionID returns a quote request by load suggestion ID.
func GetQuoteRequestByLoadSuggestionID(
	ctx context.Context,
	loadSuggestionID uint,
) (res models.QuoteRequest, err error) {

	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("load_suggestion_id = ?", loadSuggestionID).
		First(&res).Error
}

// Score and prioritize matches
type scoredRequest struct {
	request models.QuoteRequest
	score   int
}

// getQuoteRequestsByFieldSimilarity returns a quote request that matches the load by field similarity.
// It prioritizes matches by customer ID, date proximity, and location.
// This is used to power win rate calculations.
// TODO: Support multi-stop loads, use the new stops model here instead of the legacy fields.
// RDS query finds candidate quote requests, then Go code to score and prioritize them. The quote request(s)
// above a certain score are considered matches aka won loads.
func getQuoteRequestsByFieldSimilarity(
	ctx context.Context,
	newLoad models.Load,
) (res []scoredRequest, err error) {
	ctx = log.With(ctx, zap.Uint("loadID", newLoad.ID))

	matchedByAppliedFields := true
	// First try to match using applied fields
	quoteRequests, err := findCandidateQuoteRequestsMatchingLoad(ctx, newLoad, true)
	if err != nil {
		return nil, fmt.Errorf("error finding quote requests by applied fields: %w", err)
	}

	if len(quoteRequests) > 0 {
		log.Info(ctx, "found candidate quote requests by applied fields", zap.Any("count", len(quoteRequests)))
	} else {
		// Otherwise try matching using suggested fields
		quoteRequests, err = findCandidateQuoteRequestsMatchingLoad(ctx, newLoad, false)
		if err != nil {
			return nil, fmt.Errorf("error finding quote requests by suggested fields: %w", err)
		}

		if len(quoteRequests) > 0 {
			log.Info(ctx, "found candidate quote requests by suggested fields", zap.Any("count", len(quoteRequests)))
			matchedByAppliedFields = false
		}
	}

	if len(quoteRequests) == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	scoredSuggestedRequests := scoreCandidates(ctx, quoteRequests, newLoad, matchedByAppliedFields)

	sortedScoredRequests := sortAndFilterScoredRequests(scoredSuggestedRequests)

	bestMatch := sortedScoredRequests[0]

	if bestMatch.score < WonLoadScoreThreshold {
		return nil, fmt.Errorf("no quote requests with sufficient match confidence found: %w", gorm.ErrRecordNotFound)
	}

	// Suppose User A, B, C receive the same quote request with the same RFCMessageID, creating QRs A, B and C.
	// Only user A acts on their copy, setting applied fields on QR A but not QR B and C.
	// findCandidateQuoteRequestsMatchingLoad returns only QR A as candidate because we prefer records with
	// applied fields. But we should still mark QR B and C as won loads for data completeness.
	if matchedByAppliedFields {
		duplicateQRs, err := GetDuplicateSuggestedQuoteRequests(ctx, &bestMatch.request)
		if err != nil {
			// Fail-open; helpful but not critical
			log.WarnNoSentry(ctx, "error getting duplicate suggested quote requests", zap.Error(err))
		}

		for _, qr := range duplicateQRs {
			sortedScoredRequests = append(sortedScoredRequests, scoredRequest{
				request: qr,
				score:   bestMatch.score,
			})
		}
	}

	// If we have a good match(es), return it/them
	matchedIDs := make([]uint, 0, len(sortedScoredRequests))
	for _, request := range sortedScoredRequests {
		matchedIDs = append(matchedIDs, request.request.ID)
	}

	log.Info(
		ctx,
		"found quote request match(es) by field similarity",
		zap.Any("quoteRequestIDs", matchedIDs),
		zap.Uint("loadId", newLoad.ID),
		zap.Int("score", bestMatch.score),
	)

	return sortedScoredRequests, nil
}

// GetDuplicateSuggestedQuoteRequests returns duplicate suggested quote requests by RFCMessageID and SuggestedRequest.
// It's used to mark duplicate quote requests with its won load.
func GetDuplicateSuggestedQuoteRequests(ctx context.Context, qr *models.QuoteRequest) ([]models.QuoteRequest, error) {
	var duplicateQRs []models.QuoteRequest

	err := rds.WithContextReader(ctx).
		Where("rfc_message_id = ?", qr.RFCMessageID).
		Where("service_id = ?", qr.ServiceID).
		Where("id != ?", qr.ID).
		Where("suggested_request = ?", qr.SuggestedRequest).
		// Optimization: duplicates are created around the same time, so we can limit the query
		Where("created_at BETWEEN ? AND ?", qr.CreatedAt.Add(-48*time.Hour), qr.CreatedAt.Add(48*time.Hour)).
		Limit(100).
		Find(&duplicateQRs).Error

	return duplicateQRs, err
}

// findCandidateQuoteRequestsMatchingLoad performs the base query to find candidate quote requests
// that might match the load. Note that because Customer is optional on the QQ form, we don't use it to fetch
// potential candidates, but later we score them by customer match.
func findCandidateQuoteRequestsMatchingLoad(
	ctx context.Context,
	newLoad models.Load,
	useAppliedFields bool,
) ([]models.QuoteRequest, error) {
	log.Info(ctx, "finding quote requests matching load", zap.Any("useAppliedFields", useAppliedFields))

	newLoadCreatedAt := newLoad.CreatedAt
	if newLoadCreatedAt.IsZero() {
		newLoadCreatedAt = time.Now() // In case struct is not populated with CreatedAt yet
	}

	query := rds.WithContextReader(ctx).
		Where("service_id = ?", newLoad.ServiceID).
		Where("won_load_id IS NULL").
		Where("created_at BETWEEN ? AND ?", newLoadCreatedAt.Add(-WonLoadLookupWindow), newLoadCreatedAt)

	if useAppliedFields {
		switch {
		case newLoad.Specifications.TransportTypeEnum != nil:
			query = query.Where("applied_transport_type = trim(lower(?))", *newLoad.Specifications.TransportTypeEnum)

		case newLoad.Specifications.TransportType != "":
			log.Warn(
				ctx,
				"transport type enum is nil, using less performant legacy transport type",
				zap.Uint("loadID", newLoad.ID),
				zap.Uint("tmsID", newLoad.TMSID),
			)
			query = query.Where(
				//nolint:lll
				"(applied_transport_type ILIKE '%%' || trim(?) || '%%' OR trim(?) ILIKE '%%' || applied_transport_type || '%%')",
				newLoad.Specifications.TransportType,
				newLoad.Specifications.TransportType,
			)

		default:
			log.WarnNoSentry(ctx, "transport type is empty, skipping applied transport type filter",
				zap.Uint("loadID", newLoad.ID),
				zap.Uint("tmsID", newLoad.TMSID),
			)
		}

		// Use trim(lower(?) instead of `strings` fns to leverage table indexes
		query = query.Where(`
			(applied_pickup_state = trim(lower(?)) AND applied_pickup_city = trim(lower(?))) OR 
			(applied_pickup_zip = trim(lower(?)))`,
			newLoad.Pickup.State,
			newLoad.Pickup.City,
			newLoad.Pickup.Zipcode, // CAN zips are alphanumeric
		)

		query = query.Where(
			`(applied_dropoff_state = trim(lower(?)) AND applied_dropoff_city = trim(lower(?))) OR 
			(applied_dropoff_zip = trim(lower(?)))`,
			newLoad.Consignee.State,
			newLoad.Consignee.City,
			newLoad.Consignee.Zipcode, // CAN zips are alphanumeric
		)
	} else {
		switch {
		case newLoad.Specifications.TransportTypeEnum != nil:
			transportType := strings.TrimSpace(strings.ToLower(string(*newLoad.Specifications.TransportTypeEnum)))
			query = query.Where("suggested_transport_type = ?", transportType)

		case newLoad.Specifications.TransportType != "":
			log.Warn(
				ctx,
				"transport type enum is nil, using less performant legacy transport type",
				zap.Uint("loadID", newLoad.ID),
				zap.Uint("tmsID", newLoad.TMSID),
			)
			query = query.Where(
				//nolint:lll
				"(suggested_transport_type ILIKE '%%' || trim(?) || '%%' OR trim(?) ILIKE '%%' || suggested_transport_type || '%%')",
				newLoad.Specifications.TransportType,
				newLoad.Specifications.TransportType,
			)

		default:
			log.WarnNoSentry(ctx, "transport type is empty, skipping suggested transport type filter",
				zap.Uint("loadID", newLoad.ID),
				zap.Uint("tmsID", newLoad.TMSID),
			)
		}

		query = query.Where(`
			(suggested_pickup_state = trim(lower(?)) AND suggested_pickup_city = trim(lower(?))) OR 
			(suggested_pickup_zip = trim(lower(?)))`,
			newLoad.Pickup.State,
			newLoad.Pickup.City,
			newLoad.Pickup.Zipcode, // CAN zips are alphanumeric
		)
		query = query.Where(`
			(suggested_dropoff_state = trim(lower(?)) AND suggested_dropoff_city = trim(lower(?))) OR 
			(suggested_dropoff_zip = trim(lower(?)))`,
			newLoad.Consignee.State,
			newLoad.Consignee.City,
			newLoad.Consignee.Zipcode, // CAN zips are alphanumeric
		)
	}

	var quoteRequests []models.QuoteRequest
	err := query.Limit(100).Find(&quoteRequests).Error
	if err != nil {
		return nil, fmt.Errorf("error querying for matching quote requests: %w", err)
	}

	return quoteRequests, nil
}

// Scores for different field matches, when scoring by applied fields
// MaxScore =
//
//	BaseScore +
//	MatchScoreCustomer +
//	MatchScoreThread +
//	MatchScoreDate(for pickup) +
//	MatchScoreDate(for dropoff) =
//	50 + 14 + 16 + 10 + 10 = 100
//	Note: MatchScoreCustomerNameFuzzy (8) is used as fallback when exact customer match fails
const (
	// Base score for all candidates, as they've already matched transport type, pickup and dropoff location
	BaseScore = 50
	// Customers run a lot of loads so don't want to overindex on customer matching, though important
	MatchScoreCustomer = 14
	MatchScoreThread   = 16
	// Dates are optional on the QQ form, so we don't index on those too much
	MatchScoreDate = 10
	// Fuzzy name matching for if IDs unavailable (some services don't integrate TMSes yet so ID is empty)
	MatchScoreCustomerNameFuzzy = 10
)

// scoreCandidates scores quote requests based on applied field matching
// NOTE: By this point, the candidates match transport type, pickup and dropoff location based on the SQL query.
// At least one more field must match to be considered a match, with a lot of weight placed on customer and threadID
// since dates are optional on the QQ form.
func scoreCandidates(
	ctx context.Context,
	quoteRequests []models.QuoteRequest,
	newLoad models.Load,
	useAppliedFields bool,
) []scoredRequest {

	var scoredRequests []scoredRequest
	newLoadPickupDate := helpers.OrNullTime(newLoad.Pickup.ApptStartTime, newLoad.Pickup.ReadyTime)
	newLoadDropoffDate := helpers.OrNullTime(newLoad.Consignee.ApptStartTime, newLoad.Consignee.MustDeliver)

	for _, qr := range quoteRequests {
		var score int
		if useAppliedFields {
			score = scoreFn(ctx, newLoad, qr.ThreadID, qr.AppliedRequest, newLoadPickupDate, newLoadDropoffDate)
		} else {
			score = scoreFn(ctx, newLoad, qr.ThreadID, qr.SuggestedRequest, newLoadPickupDate, newLoadDropoffDate)
		}

		scoredRequests = append(scoredRequests, scoredRequest{
			request: qr,
			score:   score,
		})
	}

	sort.Slice(scoredRequests, func(i, j int) bool {
		return scoredRequests[i].score > scoredRequests[j].score
	})

	return scoredRequests
}

// Generic score function that can be used for both applied and suggested fields
func scoreFn(
	ctx context.Context,
	newLoad models.Load,
	qrThreadID string,
	qr models.QuoteLoadInfo, // Either AppliedRequest or SuggestedRequest
	newLoadPickupDate models.NullTime,
	newLoadDropoffDate models.NullTime,
) int {
	score := BaseScore

	score += scoreCustomerNameFn(ctx, &newLoad, &qr)

	// If the load came from the same thread, it's likely a bid the broker won
	// Check a max of first 5 emails to avoid performance issues
	if len(newLoad.Emails) > 0 {
		for _, email := range newLoad.Emails[:helpers.Min(len(newLoad.Emails), 5)] {
			if email.ThreadID == "" && email.ID != 0 {
				// Email object not populated, fetch it from the DB
				err := rds.GetByID(ctx, email.ID, email)
				if err != nil {
					log.WarnNoSentry(ctx, "error fetching email from DB", zap.Error(err))
					continue
				}
			}

			if email.ThreadID == qrThreadID {
				score += MatchScoreThread
			}
		}
	}

	// Date proximity is next most important; not a direct comparison because actual load date
	// may differ from initial QR due to changing circumstances.
	if newLoadPickupDate.Valid && qr.PickupDate.Valid {
		score += scoreDateFn(newLoadPickupDate, qr.PickupDate)
	}

	// Dropoff date
	if newLoadDropoffDate.Valid && qr.DeliveryDate.Valid {
		score += scoreDateFn(newLoadDropoffDate, qr.DeliveryDate)
	}

	return score
}

// Helper function to score date proximity
func scoreDateFn(loadDate models.NullTime, quoteDate models.NullTime) int {

	// If either date is invalid, no score
	if !loadDate.Valid || !quoteDate.Valid {
		return 0
	}

	loadTime := loadDate.Time
	quoteTime := quoteDate.Time

	// Check if dates are the same (ignoring time)
	if loadTime.Year() == quoteTime.Year() &&
		loadTime.Month() == quoteTime.Month() &&
		loadTime.Day() == quoteTime.Day() {
		return MatchScoreDate
	}

	// Calculate days difference
	diff := math.Abs((loadTime.Sub(quoteTime).Hours() / 24))
	// If within a week, still give some points, fewer as difference increases
	if diff < 7 {
		return int(MatchScoreDate - math.Round(diff))
	}

	// More than a week apart, no score
	return 0

}

const customerNameSimilarityThreshold = 0.7

// Helper function to score customer name match between new load and quote request, first based on IDs, then fuzzy match
func scoreCustomerNameFn(ctx context.Context, newLoad *models.Load, qr *models.QuoteLoadInfo) int {

	if newLoad.Customer.ExternalTMSID != "" && qr.Customer.ExternalTMSID != "" {
		if newLoad.Customer.ExternalTMSID == qr.Customer.ExternalTMSID {
			return MatchScoreCustomer
		}

		return 0

	}

	// Check if Drumkit IDs match, which isn't stored in models.Load so we have to look it up in RDS
	if qr.CustomerID != 0 {
		loadCustomer, err := tmscustomerDB.GetByExternalTMSID(ctx, newLoad.TMSID, newLoad.Customer.ExternalTMSID)
		if err == nil && loadCustomer.ID == qr.CustomerID {

			return MatchScoreCustomer
		}
	}

	// Fallback to fuzzy name matching
	if newLoad.Customer.Name != "" && qr.Customer.Name != "" {
		similarity, err := calculateNameSimilarity(ctx, newLoad.Customer.Name, qr.Customer.Name)

		if err == nil && similarity > customerNameSimilarityThreshold {
			return MatchScoreCustomerNameFuzzy
		}
	}

	return 0
}

// calculateNameSimilarity uses PostgreSQL's pg_trgm extension to calculate similarity between two names
func calculateNameSimilarity(ctx context.Context, name1, name2 string) (float64, error) {
	if name1 == "" || name2 == "" {
		return 0, nil
	}

	var similarity float64
	err := rds.WithContext(ctx).Raw(
		"SELECT similarity($1, $2)",
		name1, name2,
	).Scan(&similarity).Error

	if err != nil {
		return 0, fmt.Errorf("failed to calculate name similarity: %w", err)
	}

	return similarity, nil
}

// sortAndFilterScoredRequests sorts scored requests by score in descending order and keeps duplicate requests
// with the same RFCMessageID and score
func sortAndFilterScoredRequests(scoredRequests []scoredRequest) []scoredRequest {
	// Sort scored requests by score in descending order
	slices.SortFunc(scoredRequests, func(a, b scoredRequest) int {
		return b.score - a.score
	})

	bestMatch := scoredRequests[0]

	// Keep duplicate requests with the same RFCMessageID and score
	filteredRequests := slices.DeleteFunc(scoredRequests, func(a scoredRequest) bool {
		return a.request.RFCMessageID != bestMatch.request.RFCMessageID || a.score != bestMatch.score
	})

	return filteredRequests
}

type DateRangeOption struct {
	StartDate *time.Time
	EndDate   *time.Time
	NoLimit   bool // if true, ignore dates completely
}

type DateCondition struct {
	Query  string
	Params []any
}

func getDateCondition(opt *DateRangeOption) DateCondition {
	// Default to 7 day filter
	if opt == nil {
		return DateCondition{
			Query:  "AND qr.created_at >= NOW() - INTERVAL '7 days'",
			Params: nil,
		}
	}

	// No date filter
	if opt.NoLimit {
		return DateCondition{
			Query:  "",
			Params: nil,
		}
	}

	if opt.StartDate != nil && opt.EndDate != nil {
		// Add one day to end date to include the full last day
		endDate := opt.EndDate.Add(24 * time.Hour)
		return DateCondition{
			Query: "AND qr.created_at >= ? AND qr.created_at < ?",
			Params: []any{
				opt.StartDate.Format(time.RFC3339),
				endDate.Format(time.RFC3339),
			},
		}
	}

	if opt.StartDate != nil {
		return DateCondition{
			Query:  "AND qr.created_at >= ?",
			Params: []any{opt.StartDate.Format(time.RFC3339)},
		}
	}

	if opt.EndDate != nil {
		// Add one day to include the full end date
		endDate := opt.EndDate.Add(24 * time.Hour)
		return DateCondition{
			Query:  "AND qr.created_at < ?",
			Params: []any{endDate.Format(time.RFC3339)},
		}
	}

	return DateCondition{
		Query:  "AND qr.created_at >= NOW() - INTERVAL '7 days'",
		Params: nil,
	}
}

// GetValidRequestsWithQuotes retrieves valid quote requests with their associated Greenscreens quotes.
// It filters out false positives and deduplicates across accounts using RFC message IDs.
//
// A quote request is considered valid when it contains:
//   - Complete pickup location (either city+state OR zip)
//   - Complete dropoff location (either city+state OR zip)
//   - Valid pickup date
//   - Optional: Valid delivery date
//
// Invalid requests are included if they:
//   - Have an accepted Greenscreens quote
//   - Match the quote's location and dates exactly
//   - Were created by the same user
//
// The function performs several operations:
//  1. Filters valid quote requests excluding test users aka Drumkit accounts associated with the service
//  2. Matches requests with accepted carrier quotes based on location and dates
//  3. Deduplicates results by RFC message ID (global identifier across accounts)
//  4. Joins with related tables to include email, user, and service information
//
// Location matching is done either by:
//   - Exact city/state match (case insensitive)
//   - Exact ZIP code match (when available)
//
// Date Filtering:
// By default, returns data from the last 7 days. This can be customized using options:
//   - WithStartDate: Set minimum creation date
//   - WithEndDate: Set maximum creation date
//   - WithDateRange: Set both start and end dates
//   - WithNoDateLimit: Retrieve all historical data
//
// The results are ordered by creation date and include:
//   - Quote request details
//   - Email metadata (RFC ID, subject, sender, etc.)
//   - User information
//   - Service details
//   - Associated quote information (pricing, margins, status)
//
// Note: This endpoint is used by Trident to estimate request volume. LLM-generated
// false positives are filtered out by requiring complete location and date information.
//
// Parameters:
//   - ctx: Context for the database operation
//   - serviceID: ID of the service to fetch quotes for (only Trident for now)
//   - opts: Optional date filtering parameters (variadic Option functions)
//
// Returns:
//   - []models.QuoteRequest: Slice of valid quote requests with their associated data
//   - error: Any error encountered during the operation
//
// Examples:
//
//	// Get last 7 days (default)
//	GetValidRequestsWithQuotes(ctx, serviceID)
//
//	// Get specific date range
//	GetValidRequestsWithQuotes(ctx, serviceID, WithDateRange(startTime, endTime))
//
//	// Get all historical data
//	GetValidRequestsWithQuotes(ctx, serviceID, WithNoDateLimit())
//
//	// Get from specific start date
//	GetValidRequestsWithQuotes(ctx, serviceID, WithStartDate(startTime))
func GetValidRequestsWithQuotes(
	ctx context.Context,
	serviceID uint,
	opts ...Option,
) ([]models.QuoteRequestWithDetails, error) {

	options := &queryOptions{}
	for _, opt := range opts {
		opt(options)
	}

	var quoteRequests []models.QuoteRequestWithDetails

	const (
		// ValidQuoteRequestsCTE filters quote requests with valid location data
		ValidQuoteRequestsCTE = "valid_quote_requests"
		// MatchedQuotesCTE joins quote requests with their matching carrier quotes
		MatchedQuotesCTE = "matched_quotes"
		// FinalResultsCTE deduplicates results by RFC message ID
		FinalResultsCTE = "final_results"
	)

	dateCondition := getDateCondition(&options.dateRange)

	query := fmt.Sprintf(`
    WITH %s AS MATERIALIZED (
        -- First CTE: Filter valid quote requests
        SELECT * FROM quote_requests qr
        --
        WHERE service_id = ?
        %s -- Dynamic date condition
        --
        -- For each quote request, check if there EXISTS a user that:
        --   1. Has an ID matching this quote request's user_id.
        --   2. Is marked as a test user
        -- The NOT EXISTS means "only keep this quote request if we can't find a matching test user"
        -- If the user is a test user -> EXISTS becomes true -> NOT EXISTS becomes false -> exclude row
        -- If the user is NOT a test user -> EXISTS becomes false -> NOT EXISTS becomes true -> keep row
        --
        AND NOT EXISTS (
            SELECT 1 FROM users u
            WHERE u.id = qr.user_id
            AND u.is_test_user = true
        )
        AND (
            -- Valid requests (complete location + dates)
            (
                -- Ensure pickup location is valid (either city+state OR zip)
                (
                    (qr.suggested_pickup_city IS NOT NULL
                     AND qr.suggested_pickup_city != ''
                     AND qr.suggested_pickup_state IS NOT NULL
                     AND qr.suggested_pickup_state != '')
                    OR
                    (qr.suggested_pickup_zip IS NOT NULL
                     AND qr.suggested_pickup_zip != '')
                )
                -- Ensure dropoff location is valid (either city+state OR zip)
                AND (
                    (qr.suggested_dropoff_city IS NOT NULL
                     AND qr.suggested_dropoff_city != ''
                     AND qr.suggested_dropoff_state IS NOT NULL
                     AND qr.suggested_dropoff_state != '')
                    OR
                    (qr.suggested_dropoff_zip IS NOT NULL
                     AND qr.suggested_dropoff_zip != '')
                )
                -- Ensure pickup date is valid (delivery date is optional)
                AND qr.suggested_pickup_date IS NOT NULL
            )
            OR
            -- Include invalid requests that were actually applied (have matching accepted quotes)
            EXISTS (
                -- "Is there at least one quote that matches these conditions?"
                SELECT 1
                FROM quick_quotes q
                JOIN quote_request_quick_quotes_1tomany cnq ON cnq.quote_id = q.id
                WHERE cnq.quote_request_id = qr.id
                AND q.user_id = qr.user_id
                AND qr.status = 'accepted'
                AND (
                    -- Match by city/state
                    (LOWER(q.pickup_city) = LOWER(qr.suggested_pickup_city)
                    AND LOWER(q.pickup_state) = LOWER(qr.suggested_pickup_state)
                    AND LOWER(q.dropoff_city) = LOWER(qr.suggested_dropoff_city)
                    AND LOWER(q.dropoff_state) = LOWER(qr.suggested_dropoff_state))
                    OR
                    -- Match by ZIP if available
                    (q.pickup_zip = qr.suggested_pickup_zip
                    AND qr.suggested_pickup_zip != ''
                    AND q.dropoff_zip = qr.suggested_dropoff_zip
                    AND qr.suggested_dropoff_zip != '')
                )
                AND DATE(q.pickup_date) = DATE(qr.suggested_pickup_date)
                AND DATE(q.delivery_date) = DATE(qr.suggested_delivery_date)
            )
        )
    ),
    email_subset AS MATERIALIZED (
        -- Optimization: Filter emails before joining
        SELECT * FROM emails
        WHERE id IN (SELECT DISTINCT(email_id) FROM %s)
    ),
    %s AS (
        -- Second CTE: Match with accepted quotes
        SELECT vr.*, q.id as matched_quote_id
        FROM %s vr
        LEFT JOIN quick_quotes q ON (
            q.user_id = vr.user_id
            AND (
                -- Match by city/state
                (q.pickup_city ILIKE vr.suggested_pickup_city
                AND q.pickup_state ILIKE vr.suggested_pickup_state
                AND q.dropoff_city ILIKE vr.suggested_dropoff_city
                AND q.dropoff_state ILIKE vr.suggested_dropoff_state)
                OR
                -- Match by ZIP if available
                (q.pickup_zip = vr.suggested_pickup_zip
                AND vr.suggested_pickup_zip != ''
                AND q.dropoff_zip = vr.suggested_dropoff_zip
                AND vr.suggested_dropoff_zip != '')
            )
            -- Match dates exactly
            AND DATE(q.pickup_date) = DATE(vr.suggested_pickup_date)
            AND DATE(q.delivery_date) = DATE(vr.suggested_delivery_date)
        )
    ),
    %s AS (
        -- Third CTE: Deduplicate by RFC message ID
        -- When multiple requests exist for the same email (RFC ID):
        --   1. Keep the most recently created request
        --   2. If multiple quotes exist, keep the one with highest price
        SELECT DISTINCT ON (e.rfc_message_id)
            mq.*,
            e.rfc_message_id,
            e.subject,
            e.sent_at,
            e.sender,
            e.recipients,
            e.classification_method,
            e.thread_id,
            u.email_address as user_email,
            s.name as service_name,
            q.distance,
            q.currency,
            q.total_cost,
            q.final_quote_price,
            q.final_margin,
            q.final_carrier_cost,
            q.target_buy_rate,
            q.low_buy_rate,
            q.high_buy_rate,
            q.start_buy_rate,
            q.fuel_rate,
            q.confidence_level,
            q.min_markup,
            q.max_markup,
            q.total_cost
        FROM %s mq
        JOIN email_subset e ON e.id = mq.email_id
        JOIN users u ON u.id = mq.user_id
        JOIN services s ON s.id = mq.service_id
        LEFT JOIN quick_quotes q ON q.id = mq.matched_quote_id
        ORDER BY
            e.rfc_message_id,
            mq.created_at DESC,
            q.final_quote_price DESC
    )
    -- Final select: Get complete result set with all needed fields
    SELECT
        fr.*,
        cnq.quote_id,
        fr.status as quote_request_status,
        q.pipeline as quote_pipeline
    FROM %s fr
    LEFT JOIN quote_request_quick_quotes_1tomany cnq ON cnq.quote_request_id = fr.id
    LEFT JOIN quick_quotes q ON q.id = cnq.quote_id
    ORDER BY fr.created_at DESC`,
		ValidQuoteRequestsCTE,
		dateCondition.Query,
		ValidQuoteRequestsCTE,
		MatchedQuotesCTE,
		ValidQuoteRequestsCTE,
		FinalResultsCTE,
		MatchedQuotesCTE,
		FinalResultsCTE,
	)

	params := []any{serviceID}
	if dateCondition.Params != nil {
		params = append(params, dateCondition.Params...)
	}

	return quoteRequests, rds.WithContextReader(ctx).Raw(query, params...).Find(&quoteRequests).Error
}
