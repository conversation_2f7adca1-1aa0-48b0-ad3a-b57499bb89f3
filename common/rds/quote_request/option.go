package quoterequest

import "time"

type Option func(*queryOptions)

type queryOptions struct {
	dateRange  DateRangeOption
	userID     *uint
	customerID *uint
}

func WithUserID(userID uint) Option {
	return func(o *queryOptions) {
		o.userID = &userID
	}
}

func WithCustomerID(customerID uint) Option {
	return func(o *queryOptions) {
		o.customerID = &customerID
	}
}

// WithStartDate sets the start date for the query range
func WithStartDate(start time.Time) Option {
	return func(o *queryOptions) {
		o.dateRange.StartDate = &start
		o.dateRange.NoLimit = false
	}
}

// WithEndDate sets the end date for the query range
func WithEndDate(end time.Time) Option {
	return func(o *queryOptions) {
		o.dateRange.EndDate = &end
		o.dateRange.NoLimit = false
	}
}

// WithDateRange sets both start and end dates for the query range
func WithDateRange(start, end time.Time) Option {
	return func(o *queryOptions) {
		o.dateRange.StartDate = &start
		o.dateRange.EndDate = &end
		o.dateRange.NoLimit = false
	}
}

// WithNoDateLimit removes date filtering
func WithNoDateLimit() Option {
	return func(o *queryOptions) {
		o.dateRange.NoLimit = true
		o.dateRange.StartDate = nil
		o.dateRange.EndDate = nil
	}
}
