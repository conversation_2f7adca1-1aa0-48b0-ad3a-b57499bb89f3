package quoterequest

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateQuoteRequestStatus updates the status of a quote request.
// If the new status is inFlight, we save the applied fields.
// Passing nil for appliedFields will not overwrite the existing applied fields.
func UpdateQuoteRequestStatus(
	ctx context.Context,
	quoteRequestID uint,
	appliedFields *models.QuoteLoadInfo,
	user *models.User,
	status models.SuggestionStatus,
	finalQuoteData *models.FinalQuoteData,
	selectedQuickQuoteID *uint,
) error {

	if quoteRequestID == 0 {
		return errors.New("invalid quote request ID")
	}

	var existingRequest models.QuoteRequest
	if err := rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", quoteRequestID).
		First(&existingRequest).Error; err != nil {

		return fmt.Errorf("quote request not found: %w", err)
	}

	existingRequest.Status = status
	existingRequest.QuotedByUserID = &user.ID

	if status == models.InFlight && appliedFields != nil {
		existingRequest.AppliedRequest = *appliedFields
	}

	if status == models.Accepted && finalQuoteData != nil {
		existingRequest.FinalQuotePrice = finalQuoteData.FinalQuotePrice
		existingRequest.FinalMargin = finalQuoteData.FinalMargin
		existingRequest.MarginType = finalQuoteData.MarginType
		existingRequest.CarrierCostType = finalQuoteData.CarrierCostType
		existingRequest.FinalCarrierCost = finalQuoteData.FinalCarrierCost
		if finalQuoteData.CustomerID != 0 {
			existingRequest.AppliedRequest.CustomerID = finalQuoteData.CustomerID
		}

		existingRequest.FinalQuoteHistory = append(existingRequest.FinalQuoteHistory, models.QuoteHistoryLog{
			UserID:    user.ID,
			UserEmail: user.EmailAddress,
			Timestamp: time.Now(),
			Quote:     *finalQuoteData,
		})
	}

	existingRequest.SelectedQuickQuoteID = selectedQuickQuoteID

	// Single database update for all cases
	return rds.WithContext(ctx).
		Model(&existingRequest).
		Where("id = ?", existingRequest.ID).
		Updates(&existingRequest).Error
}

// AssociateLoadWithQuoteRequest associates a load with a quote request(s).
// A load may be associated with duplicate quote requests that have same load info & RFC message ID.
// If the load building suggestion is not nil, we look for a quote request with the same load suggestion ID.
// If the suggestion is nil, we look for a quote request with the same pickup/dropoff/date/transport type/customer.
//
// TODO: Quoting portals often include the customer ref #, so we should use that if available.
func AssociateLoadWithQuoteRequest(
	ctx context.Context,
	suggestion *models.SuggestedLoadChange,
	newLoad models.Load,
) error {

	if suggestion != nil && suggestion.ID != 0 {
		quoteRequest, err := GetQuoteRequestByLoadSuggestionID(ctx, suggestion.ID)
		if err != nil {
			log.WarnNoSentry(
				ctx,
				"error associating load with quote request by load suggestion ID, trying to match by field similarity",
				zap.Error(err),
			)
		} else {
			quoteRequest.WonLoadID = &newLoad.ID
			quoteRequest.MatchedToLoadBy = models.MatchedBySuggestion

			return rds.WithContext(ctx).Save(&quoteRequest).Error
		}
	}

	wonQuoteRequests, err := getQuoteRequestsByFieldSimilarity(ctx, newLoad)
	if err != nil {
		return err
	}

	finalWonQuoteRequests := make([]models.QuoteRequest, 0, len(wonQuoteRequests))
	for _, scoredRequest := range wonQuoteRequests {
		scoredRequest.request.WonLoadID = &newLoad.ID
		scoredRequest.request.MatchedToLoadBy = models.MatchedByFieldSimilarity
		scoredRequest.request.WonLoadScore = scoredRequest.score

		finalWonQuoteRequests = append(finalWonQuoteRequests, scoredRequest.request)
	}

	if err := rds.WithContext(ctx).Save(&finalWonQuoteRequests).Error; err != nil {
		return fmt.Errorf("error saving matched quote requests: %w", err)
	}

	return nil
}

// UpdateQuoteRequestBraintrustLogID updates the Braintrust log ID for a quote request.
func UpdateQuoteRequestBraintrustLogID(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).
		Model(&models.QuoteRequest{}).
		Where("id = ?", quoteRequest.ID).
		Update("braintrust_log_ids", quoteRequest.BraintrustLogIDs).Error
}

func UpdateSuggestedRequest(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).
		Model(quoteRequest).
		Where("id = ?", quoteRequest.ID).
		Clauses(clause.Returning{}).
		Update("suggested_request", quoteRequest.SuggestedRequest).Error
}

func UpdateAppliedRequest(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).
		Model(quoteRequest).
		Where("id = ?", quoteRequest.ID).
		Clauses(clause.Returning{}).
		Update("applied_request", quoteRequest.AppliedRequest).Error
}
