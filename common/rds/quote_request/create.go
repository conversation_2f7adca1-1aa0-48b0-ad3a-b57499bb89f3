package quoterequest

import (
	"context"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Creates or updates a quote request and its associated generated emails & carrier quotes.
func UpsertQuoteRequestAndAssociations(ctx context.Context, quoteReq *models.QuoteRequest) error {
	tx := rds.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer tx.Rollback()

	// For new records
	if quoteReq.ID == 0 {
		if err := tx.Session(&gorm.Session{FullSaveAssociations: true}).
			Create(quoteReq).Error; err != nil {

			return fmt.Errorf("creating quote request: %w", err)
		}
	} else {
		// For existing records
		if err := tx.Session(&gorm.Session{FullSaveAssociations: true}).
			Model(quoteReq).Updates(quoteReq).Error; err != nil {
			return fmt.Errorf("updating quote request: %w", err)
		}
	}

	// Upsert quotes with quote request IDs
	for i := range quoteReq.CarrierQuotes {
		quoteReq.CarrierQuotes[i].QuoteRequestID = quoteReq.ID
	}

	if err := tx.Save(&quoteReq.CarrierQuotes).Error; err != nil {
		return fmt.Errorf("error updating carrier quotes with quote request ID: %w", err)
	}

	return tx.Commit().Error
}

// we now allow multiple quote requests per email, so we need shouldn't be upserting quote requests based on emailID
// we should just be creating them.
func CreateQuoteRequest(ctx context.Context, quoteReq *models.QuoteRequest) error {
	return rds.WithContext(ctx).Clauses(clause.Returning{}).
		Create(quoteReq).Error
}
