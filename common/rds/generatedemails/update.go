package generatedemails

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, email *models.GeneratedEmail) error {
	return rds.WithContext(ctx).Updates(email).Error
}

func DeletePendingEmail(ctx context.Context, email *models.GeneratedEmail) error {
	email.Status = models.CanceledStatus

	return rds.WithContext(ctx).Updates(email).Error
}

func UndoDeletePendingEmail(ctx context.Context, email *models.GeneratedEmail) error {
	email.Status = models.PendingStatus

	return rds.WithContext(ctx).Updates(email).Error
}
