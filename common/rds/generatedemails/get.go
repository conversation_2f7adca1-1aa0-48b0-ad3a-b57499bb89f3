package generatedemails

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetEmail(ctx context.Context, id uint) (*models.GeneratedEmail, error) {
	var genEmail models.GeneratedEmail

	return &genEmail, rds.WithContextReader(ctx).Where("id = ?", id).First(&genEmail).Error
}

// NOTE: We only support pending carrier emails for now.
func GetEmailsByLoadID(ctx context.Context, serviceID, loadID uint) (*models.PendingOutboxEmails, error) {
	var genEmails []models.GeneratedEmail

	err := rds.WithContextReader(ctx).
		Joins("JOIN generated_email_loads m2m ON m2m.generated_email_id = generated_emails.id").
		Joins("JOIN loads ON loads.id = m2m.load_id").
		Where("loads.id = ? AND generated_emails.status = ? AND generated_emails.service_id = ?",
			loadID, models.PendingStatus, serviceID).
		Find(&genEmails).
		Error

	if err != nil {
		return nil, err
	}

	outboxEmails := &models.PendingOutboxEmails{
		CarrierEmails: models.PendingCarrierEmails{
			Pickup:    []models.PendingEmail{},
			Loaded:    []models.PendingEmail{},
			InTransit: []models.PendingEmail{},
			Dropoff:   []models.PendingEmail{},
			Unloaded:  []models.PendingEmail{},
		},
	}

	for _, genEmail := range genEmails {
		pendingEmail := models.PendingEmail{
			ID:        genEmail.ID,
			Recipient: genEmail.Recipients[0],
			Subject:   genEmail.Subject,
			Body:      genEmail.Body,
			Timestamp: genEmail.ScheduleSend.Time,
		}

		switch genEmail.Milestone {
		case models.PickupMilestone:
			outboxEmails.CarrierEmails.Pickup = append(outboxEmails.CarrierEmails.Pickup, pendingEmail)
		case models.LoadedMilestone:
			outboxEmails.CarrierEmails.Loaded = append(outboxEmails.CarrierEmails.Loaded, pendingEmail)
		case models.InTransitMilestone:
			outboxEmails.CarrierEmails.InTransit = append(outboxEmails.CarrierEmails.InTransit, pendingEmail)
		case models.DropoffMilestone:
			outboxEmails.CarrierEmails.Dropoff = append(outboxEmails.CarrierEmails.Dropoff, pendingEmail)
		case models.UnloadedMilestone:
			outboxEmails.CarrierEmails.Unloaded = append(outboxEmails.CarrierEmails.Unloaded, pendingEmail)
		}
	}

	return outboxEmails, nil
}

// Return the first email associated with the threadID and preloads its quote request, if any
func GetEmailAndQuoteRequestByThreadID(ctx context.Context, threadID string) (*models.GeneratedEmail, error) {
	var email models.GeneratedEmail

	err := rds.WithContextReader(ctx).
		Where("thread_id = ?", threadID).
		Order("sent_at ASC").
		Preload("QuoteRequests").
		First(&email).Error

	return &email, err
}

// GetByForwardedThreadID returns all generated emails that were forwarded from the given thread ID by a rule
func GetByForwardedThreadID(ctx context.Context, threadID string, ruleID uint) ([]*models.GeneratedEmail, error) {
	var emails []*models.GeneratedEmail

	err := rds.WithContextReader(ctx).
		Where("forwarded_message_thread_id = ? and triggered_by_rule_id = ?", threadID, ruleID).
		Find(&emails).Error

	return emails, err
}

// GetByForwardedMessageID returns the generated email that forwarded the given message ID by a rule
func GetByForwardedMessageID(ctx context.Context, msgExternalID string, ruleID uint) (*models.GeneratedEmail, error) {
	var email models.GeneratedEmail

	err := rds.WithContextReader(ctx).
		Where("forwarded_message_external_id = ? and triggered_by_rule_id = ?", msgExternalID, ruleID).
		First(&email).Error

	return &email, err
}

// GetForwardByGeneratedRFCID returns the generated email with the given RFC and rule ID
func GetForwardByGeneratedRFCID(
	ctx context.Context,
	userID uint,
	rfcID string,
	ruleID uint,
) (*models.GeneratedEmail, error) {
	var email models.GeneratedEmail

	err := rds.WithContextReader(ctx).
		Where("user_id = ? AND rfc_message_id = ? and triggered_by_rule_id = ? AND is_forward = true",
			userID, rfcID, ruleID).
		First(&email).Error

	return &email, err
}
