package generatedemails

import (
	"context"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func BatchCreateGeneratedEmails(ctx context.Context, emails []*models.GeneratedEmail) error {
	if len(emails) == 0 {
		log.Debug(ctx, "skipping batch create for empty list of generated emails")

		return nil
	}

	return rds.WithContext(ctx).Create(emails).Error
}
