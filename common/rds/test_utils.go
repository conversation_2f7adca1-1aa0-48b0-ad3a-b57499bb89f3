package rds

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
)

// --- Generic test helpers for RDS tests ---
// NOTE: NOT FOR PRODUCTION. Only for tests

// NOTE: If you need to debug test & view DB after test completion, *temporarily* remove the ClearTestDB call.
func SetupTestDB(t *testing.T) context.Context {
	t.Helper()

	ctx := context.Background()
	MustOpenTestDB(ctx, "drumkit_test_db")
	ClearTestDB(ctx, t)
	t.Cleanup(func() { ClearTestDB(ctx, t) })

	return ctx
}

// NOTE: Use only for debugging tests. Finalized tests should use SetupTestDB for idempotence.
func SetupTestDBForDebugging(t *testing.T) context.Context {
	t.Helper()

	ctx := context.Background()
	MustOpenTestDB(ctx, "drumkit_test_db")
	ClearTestDB(ctx, t)

	return ctx
}

func ClearTestDB(ctx context.Context, t *testing.T) {
	// Set both these columns in models.Service to null first to adhere to check constraints
	updateValues := map[string]any{
		"nickname":              nil,
		"quick_quote_config_id": nil,
	}

	err := WithContext(ctx).Session(&gorm.Session{AllowGlobalUpdate: true}).
		Model(&models.Service{}).Updates(updateValues).Error
	require.NoError(t, err)

	// Delete associations tables first to avoid foreign key constraint errors; add as needed
	err = WithContext(ctx).Exec("DELETE FROM email_loads").Error
	require.NoError(t, err)

	err = WithContext(ctx).Exec("DELETE FROM user_group_integrations").Error
	require.NoError(t, err)

	err = WithContext(ctx).Exec("DELETE FROM user_group_users").Error
	require.NoError(t, err)

	// Clear tables in reverse order to respect foreign key constraints
	for i := len(DefaultMigrationOrder) - 1; i >= 0; i-- {
		err = WithContext(ctx).Session(&gorm.Session{AllowGlobalUpdate: true}).
			Unscoped().Delete(DefaultMigrationOrder[i]).Error
		require.NoError(t, err)
	}
}

// CreateTestService creates a test service. If a service is provided, it will be used instead of the default.
func CreateTestService(ctx context.Context, t *testing.T, serviceParam ...models.Service) models.Service {
	t.Helper()

	service := models.Service{Name: "Test Service"}
	if len(serviceParam) > 0 {
		service = serviceParam[0]
	}

	err := WithContext(ctx).Create(&service).Error
	require.NoError(t, err)

	return service
}

// CreateTestUser creates a test user. If a user is provided, it will be used instead of the default.
func CreateTestUser(ctx context.Context, t *testing.T, serviceID uint, userParam ...models.User) models.User {
	t.Helper()

	user := models.User{EmailAddress: "<EMAIL>", ServiceID: serviceID}
	if len(userParam) > 0 {
		require.Equal(t, userParam[0].ServiceID, serviceID)
		user = userParam[0]
	}

	err := WithContext(ctx).Create(&user).Error
	require.NoError(t, err)

	return user
}

func CreateTestIntegration(
	ctx context.Context,
	t *testing.T,
	integration models.Integration,
) models.Integration {

	t.Helper()

	err := WithContext(ctx).Create(&integration).Error
	require.NoError(t, err)

	return integration
}

func CreateTestIntegrationByType(
	ctx context.Context,
	t *testing.T,
	serviceID uint,
	integrationType models.IntegrationType,
) models.Integration {

	t.Helper()

	integration := models.Integration{
		Name:      "Test Integration",
		Type:      integrationType,
		ServiceID: serviceID,
	}
	err := WithContext(ctx).Create(&integration).Error
	require.NoError(t, err)

	return integration
}

func CreateTestEmail(ctx context.Context, t *testing.T, serviceID, userID uint) models.Email {
	t.Helper()

	email := models.Email{
		ExternalID: "email1",
		ServiceID:  serviceID,
		UserID:     userID,
	}
	err := WithContext(ctx).Create(&email).Error
	require.NoError(t, err)

	return email
}

func CreateTestBatchQuote(
	ctx context.Context,
	t *testing.T,
	userID, serviceID, emailID uint,
	createBatchQuoteFunc func(context.Context, *models.BatchQuote) error,
) models.BatchQuote {

	t.Helper()

	batchQuote := models.BatchQuote{
		UserID:    userID,
		ServiceID: serviceID,
		EmailID:   emailID,
		ThreadID:  "thread1",
		Status:    models.BatchQuoteStatusProcessed,
	}
	err := createBatchQuoteFunc(ctx, &batchQuote)
	require.NoError(t, err)
	require.NotZero(t, batchQuote.ID)

	return batchQuote
}

// CreateTestQuoteRequests creates a specified number of quote requests in test DB and returns them.
func CreateTestQuoteRequests(
	ctx context.Context,
	t *testing.T,
	userID, serviceID, emailID uint,
	count int,
	batchUpsertFunc func(context.Context, []models.QuoteRequest) error,
) []models.QuoteRequest {
	t.Helper()

	quoteRequests := make([]models.QuoteRequest, count)
	for i := 0; i < count; i++ {
		quoteRequests[i] = models.QuoteRequest{
			UserID:           userID,
			ServiceID:        serviceID,
			EmailID:          emailID,
			ThreadID:         "thread1",
			SourceCategory:   models.EmailSourceCategory,
			Source:           "testsource",
			SourceExternalID: fmt.Sprintf("src%d", i+1),
			Status:           models.Pending,
		}
	}
	err := batchUpsertFunc(ctx, quoteRequests)
	require.NoError(t, err)

	var dbQuoteRequests []models.QuoteRequest
	err = WithContextReader(ctx).Where("service_id = ?", serviceID).Find(&dbQuoteRequests).Error
	require.NoError(t, err)
	require.Len(t, dbQuoteRequests, count)

	return dbQuoteRequests
}

// CreateTestCarrier creates a test carrier in the DB
func CreateTestCarrier(ctx context.Context, t *testing.T, serviceID uint, tmsIntegrationID uint) models.TMSCarrier {
	t.Helper()

	carrier := models.TMSCarrier{
		TMSIntegrationID: tmsIntegrationID,
		ServiceID:        serviceID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			Name: "Test Carrier",
		},
	}
	err := WithContext(ctx).Create(&carrier).Error
	require.NoError(t, err)

	return carrier
}
