package tmscustomerschedulingintegration

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetMostUsedIntegrationByTMSCustomer returns the most frequently used scheduling integration for a TMS customer
func GetMostUsedIntegrationByTMSCustomer(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID uint,
) (*models.TMSCustomerSchedulingIntegration, error) {

	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND tms_customer_id = ?
			 AND is_disabled = ? 
			 AND deleted_at IS NULL`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			tmsCustomerID,
			false,
		).
		Order("usage_count DESC").
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetAllIntegrationsForTMSCustomer returns all scheduling integrations associated with a TMS customer
func GetAllIntegrationsForTMSCustomer(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID uint,
) ([]models.TMSCustomerSchedulingIntegration, error) {

	var associations []models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND tms_customer_id = ? 
			 AND is_disabled = ? 
			 AND deleted_at IS NULL`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			tmsCustomerID,
			false,
		).
		Order("usage_count DESC").
		Preload("SchedulingIntegration").
		Find(&associations).Error

	return associations, err
}

// GetAssociationByTMSCustomerAndIntegration returns a specific association between TMS customer and integration
func GetAssociationByTMSCustomerAndIntegration(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID,
	schedulingIntegrationID uint,
) (*models.TMSCustomerSchedulingIntegration, error) {

	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND tms_customer_id = ?
			 AND scheduling_integration_id = ? 
			 AND is_disabled = ?
			 AND deleted_at IS NULL`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			tmsCustomerID,
			schedulingIntegrationID,
			false,
		).
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetRecommendedIntegrationByTMSCustomer returns the recommended integration choice for a TMS customer
// Priority: 1) Most used integration by TMS customer, 2) nil if no associations found
func GetRecommendedIntegrationByTMSCustomer(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID uint,
) (*models.Integration, error) {

	mostUsed, err := GetMostUsedIntegrationByTMSCustomer(
		ctx,
		serviceID,
		tmsIntegrationID,
		warehouseID,
		tmsCustomerID,
	)
	if err == nil && mostUsed != nil {
		return &mostUsed.SchedulingIntegration, nil
	}

	// Return nil if no associations found (not an error - customer just has no preferences)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return nil, err
}

// GetMostUsedIntegrationByCustomerName returns the most frequently used scheduling integration for a customer name
func GetMostUsedIntegrationByCustomerName(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID uint,
	customerName string,
) (*models.TMSCustomerSchedulingIntegration, error) {

	var association models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND customer_name = ? 
			 AND is_disabled = ? 
			 AND deleted_at IS NULL`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			customerName,
			false,
		).
		Order("usage_count DESC").
		Preload("SchedulingIntegration").
		First(&association).Error

	if err != nil {
		return nil, err
	}

	return &association, nil
}

// GetAllIntegrationsForCustomerName returns all scheduling integrations associated with a customer name
func GetAllIntegrationsForCustomerName(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID uint,
	customerName string,
) ([]models.TMSCustomerSchedulingIntegration, error) {

	var associations []models.TMSCustomerSchedulingIntegration

	err := rds.WithContextReader(ctx).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND customer_name = ? 
			 AND is_disabled = ? 
			 AND deleted_at IS NULL`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			customerName,
			false,
		).
		Order("usage_count DESC").
		Preload("SchedulingIntegration").
		Find(&associations).Error

	return associations, err
}

// GetRecommendedIntegrationByCustomerName returns the recommended integration choice for a customer name
// Priority: 1) Most used integration by customer name, 2) nil if no associations found
func GetRecommendedIntegrationByCustomerName(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID uint,
	customerName string,
) (*models.Integration, error) {

	mostUsed, err := GetMostUsedIntegrationByCustomerName(
		ctx,
		serviceID,
		tmsIntegrationID,
		warehouseID,
		customerName,
	)
	if err == nil && mostUsed != nil {
		return &mostUsed.SchedulingIntegration, nil
	}

	// Return nil if no associations found (not an error - customer just has no preferences)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return nil, err
}
