package tmscustomerschedulingintegration

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateOrUpdateAssociation creates a new association or updates an existing one.
// It handles two cases:
// - TMSCustomerID-based associations: uses tms_customer_id unique index
// - CustomerName-based associations: uses customer_name unique index
func CreateOrUpdateAssociation(
	ctx context.Context,
	association *models.TMSCustomerSchedulingIntegration,
	db *gorm.DB,
) error {

	if db == nil {
		db = rds.WithContext(ctx)
	}

	conflictCols := []clause.Column{
		{Name: "tms_integration_id"},
		{Name: "associated_warehouse_id"},
		{Name: "scheduling_integration_id"},
	}

	// Add conflict columns based on which customer identifier is set
	if association.TMSCustomerID != nil {
		conflictCols = append(conflictCols, clause.Column{Name: "tms_customer_id"})
	} else if association.CustomerName != nil {
		conflictCols = append(conflictCols, clause.Column{Name: "customer_name"})
	}

	return db.Clauses(
		clause.OnConflict{
			Columns: conflictCols,
			DoUpdates: clause.AssignmentColumns([]string{
				"usage_count",
				"notes",
				"is_disabled",
				"updated_at",
			}),
		},
	).Create(association).Error
}

// RecordUsageByTMSCustomerID records that an appointment was scheduled for a customer (by TMS customer ID) using a
// specific scheduling integration. This will create the association if it doesn't exist, or increment usage if it does
func RecordUsageByTMSCustomerID(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID,
	schedulingIntegrationID uint,
) error {

	// First try to increment existing association
	err := IncrementUsageByTMSCustomerID(
		ctx,
		serviceID,
		tmsIntegrationID,
		warehouseID,
		tmsCustomerID,
		schedulingIntegrationID,
	)
	if err == nil {
		return nil
	}

	// If no existing association, create a new one
	if errors.Is(err, gorm.ErrRecordNotFound) {
		association := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               serviceID,
			TMSIntegrationID:        tmsIntegrationID,
			AssociatedWarehouseID:   warehouseID,
			TMSCustomerID:           &tmsCustomerID,
			SchedulingIntegrationID: schedulingIntegrationID,
			UsageCount:              1,
			IsDisabled:              false,
		}
		return CreateOrUpdateAssociation(ctx, association, nil)
	}

	return err
}

// IncrementUsageByTMSCustomerID increments the usage count and updates last used time for an association
func IncrementUsageByTMSCustomerID(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID,
	tmsCustomerID,
	schedulingIntegrationID uint,
) error {

	result := rds.WithContext(ctx).Model(&models.TMSCustomerSchedulingIntegration{}).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND tms_customer_id = ? 
			 AND scheduling_integration_id = ?`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			tmsCustomerID,
			schedulingIntegrationID,
		).
		Updates(map[string]any{
			"usage_count": gorm.Expr("usage_count + 1"),
			"updated_at":  time.Now(),
		})

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

// RecordUsageByCustomerName records that an appointment was scheduled for a customer (by name) using a specific
// scheduling integration. This will create the association if it doesn't exist, or increment usage if it does
func RecordUsageByCustomerName(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID uint,
	customerName string,
	schedulingIntegrationID uint,
) error {

	// First try to increment existing association
	err := IncrementUsageByCustomerName(
		ctx,
		serviceID,
		tmsIntegrationID,
		warehouseID,
		customerName,
		schedulingIntegrationID,
	)
	if err == nil {
		return nil
	}

	// If no existing association, create a new one
	if errors.Is(err, gorm.ErrRecordNotFound) {
		association := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               serviceID,
			TMSIntegrationID:        tmsIntegrationID,
			AssociatedWarehouseID:   warehouseID,
			CustomerName:            &customerName,
			SchedulingIntegrationID: schedulingIntegrationID,
			UsageCount:              1,
			IsDisabled:              false,
		}
		return CreateOrUpdateAssociation(ctx, association, nil)
	}

	return err
}

// IncrementUsageByCustomerName increments the usage count and updates last used time for a customer name association
func IncrementUsageByCustomerName(
	ctx context.Context,
	serviceID,
	tmsIntegrationID,
	warehouseID uint,
	customerName string,
	schedulingIntegrationID uint,
) error {

	// Direct customer name query before trying fuzzy matching
	result := rds.WithContext(ctx).Model(&models.TMSCustomerSchedulingIntegration{}).
		Where(
			`service_id = ? 
			 AND tms_integration_id = ? 
			 AND associated_warehouse_id = ? 
			 AND customer_name = ? 
			 AND scheduling_integration_id = ? 
			 AND is_disabled = ?`,
			serviceID,
			tmsIntegrationID,
			warehouseID,
			customerName,
			schedulingIntegrationID,
			false,
		).
		Updates(map[string]any{
			"usage_count": gorm.Expr("usage_count + 1"),
			"updated_at":  time.Now(),
		})

	// TODO: Add relatively strict fuzzy matching for customer name - honestly this is not really needed?

	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}
