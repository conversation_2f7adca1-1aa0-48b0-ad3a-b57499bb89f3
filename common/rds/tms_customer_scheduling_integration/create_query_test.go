package tmscustomerschedulingintegration

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Run with:
// 1. EXPORT TEST_LIVE_DB=true
// 2. go test ./common/rds/tms_customer_scheduling_integration -run TestLiveTMSCustomerSchedulingIntegrationRDS
func TestLiveTMSCustomerSchedulingIntegrationRDS(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestLiveTMSCustomerSchedulingIntegrationRDS: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	warehouse := createTestWarehouse(ctx, t)
	tmsIntegration := createTestTMSIntegration(ctx, t, service.ID, models.Mcleod)
	tmsCustomer := createTestTMSCustomer(
		ctx,
		t,
		tmsIntegration,
		"Test TMS Customer",
		"TEST-CUSTOMER-001",
	)
	schedulingIntegration1 := createTestSchedulingIntegration(ctx, t, service.ID, models.YardView)
	schedulingIntegration2 := createTestSchedulingIntegration(ctx, t, service.ID, models.Opendock)

	t.Run("CreateOrUpdateAssociation", func(t *testing.T) {
		association := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               service.ID,
			TMSIntegrationID:        tmsIntegration.ID,
			AssociatedWarehouseID:   warehouse.ID,
			TMSCustomerID:           &tmsCustomer.ID,
			SchedulingIntegrationID: schedulingIntegration1.ID,
			UsageCount:              5,
			Notes:                   "Initial association",
			IsDisabled:              false,
		}

		err := CreateOrUpdateAssociation(ctx, association, nil)
		require.NoError(t, err)
		assert.NotZero(t, association.ID)

		// Update the same association
		association.UsageCount = 10
		association.Notes = "Updated association"
		err = CreateOrUpdateAssociation(ctx, association, nil)
		require.NoError(t, err)

		// Verify the update
		retrieved, err := GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)
		assert.Equal(t, 10, retrieved.UsageCount)
		assert.Equal(t, "Updated association", retrieved.Notes)
	})

	t.Run("RecordUsage - TMS Customer ID", func(t *testing.T) {
		// Record usage for a new association
		err := RecordUsageByTMSCustomerID(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration2.ID,
		)
		require.NoError(t, err)

		// Verify the association was created with usage_count = 1
		association, err := GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration2.ID,
		)
		require.NoError(t, err)
		assert.Equal(t, 1, association.UsageCount)

		// Record usage again to increment
		err = RecordUsageByTMSCustomerID(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration2.ID,
		)
		require.NoError(t, err)

		// Verify usage count incremented
		association, err = GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration2.ID,
		)
		require.NoError(t, err)
		assert.Equal(t, 2, association.UsageCount)
	})

	t.Run("IncrementUsage", func(t *testing.T) {
		// Get current usage count
		association, err := GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)
		initialCount := association.UsageCount

		// Increment usage
		err = IncrementUsageByTMSCustomerID(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)

		// Verify increment
		association, err = GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)
		assert.Equal(t, initialCount+1, association.UsageCount)

		// Try to increment non-existent association
		nonExistentCustomerID := uint(999999)
		err = IncrementUsageByTMSCustomerID(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			nonExistentCustomerID,
			schedulingIntegration1.ID,
		)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("RecordUsageByCustomerName", func(t *testing.T) {
		customerName := "ACME Corporation"

		// Record usage for a new customer name association
		err := RecordUsageByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)

		// Verify the association was created with usage_count = 1
		association, err := GetMostUsedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.Equal(t, 1, association.UsageCount)
		assert.Equal(t, customerName, *association.CustomerName)

		// Record usage again to increment
		err = RecordUsageByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)

		// Verify usage count incremented
		association, err = GetMostUsedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.Equal(t, 2, association.UsageCount)
	})

	t.Run("IncrementUsageByCustomerName", func(t *testing.T) {
		customerName := "Widget Industries"

		// Create initial association
		association := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               service.ID,
			TMSIntegrationID:        tmsIntegration.ID,
			AssociatedWarehouseID:   warehouse.ID,
			CustomerName:            &customerName,
			SchedulingIntegrationID: schedulingIntegration2.ID,
			UsageCount:              5,
			IsDisabled:              false,
		}
		err := CreateOrUpdateAssociation(ctx, association, nil)
		require.NoError(t, err)

		// Increment usage
		err = IncrementUsageByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
			schedulingIntegration2.ID,
		)
		require.NoError(t, err)

		// Verify increment
		retrieved, err := GetMostUsedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.Equal(t, 6, retrieved.UsageCount)

		// Try to increment non-existent customer name
		err = IncrementUsageByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			"NonExistentCustomer",
			schedulingIntegration1.ID,
		)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("GetMostUsedIntegrationByTMSCustomer", func(t *testing.T) {
		// Should return integration1 since it has higher usage count
		mostUsed, err := GetMostUsedIntegrationByTMSCustomer(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
		)
		require.NoError(t, err)
		assert.NotNil(t, mostUsed)
		assert.Equal(t, schedulingIntegration1.ID, mostUsed.SchedulingIntegrationID)
		assert.NotNil(t, mostUsed.SchedulingIntegration)

		// Non-existent customer should return error
		nonExistentCustomerID := uint(999999)
		_, err = GetMostUsedIntegrationByTMSCustomer(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			nonExistentCustomerID,
		)
		assert.Error(t, err)
	})

	t.Run("GetAllIntegrationsForTMSCustomer", func(t *testing.T) {
		associations, err := GetAllIntegrationsForTMSCustomer(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
		)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(associations), 2) // Should have at least integration1 and integration2

		// Verify ordering by usage_count DESC
		if len(associations) >= 2 {
			assert.GreaterOrEqual(
				t,
				associations[0].UsageCount,
				associations[1].UsageCount,
				"Should be ordered by usage_count DESC",
			)
		}

		// Verify preloading
		for _, assoc := range associations {
			assert.NotZero(t, assoc.SchedulingIntegration.ID)
		}
	})

	t.Run("GetAssociationByCustomerAndIntegration", func(t *testing.T) {
		association, err := GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			schedulingIntegration1.ID,
		)
		require.NoError(t, err)
		assert.NotNil(t, association)
		assert.Equal(t, tmsCustomer.ID, *association.TMSCustomerID)
		assert.Equal(t, schedulingIntegration1.ID, association.SchedulingIntegrationID)

		// Non-existent association should return error
		nonExistentIntegrationID := uint(999999)
		_, err = GetAssociationByTMSCustomerAndIntegration(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
			nonExistentIntegrationID,
		)
		assert.Error(t, err)
	})

	t.Run("GetRecommendedIntegrationByTMSCustomer", func(t *testing.T) {
		recommended, err := GetRecommendedIntegrationByTMSCustomer(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
		)
		require.NoError(t, err)
		assert.NotNil(t, recommended)
		assert.NotZero(t, recommended.ID)

		// Non-existent customer should return nil (not an error)
		nonExistentCustomerID := uint(999999)
		recommended, err = GetRecommendedIntegrationByTMSCustomer(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			nonExistentCustomerID,
		)
		require.NoError(t, err)
		assert.Nil(t, recommended)
	})

	t.Run("GetMostUsedIntegrationByCustomerName", func(t *testing.T) {
		customerName := "ACME Corporation"

		mostUsed, err := GetMostUsedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.NotNil(t, mostUsed)
		assert.Equal(t, customerName, *mostUsed.CustomerName)
		assert.NotNil(t, mostUsed.SchedulingIntegration)

		// Non-existent customer name should return error
		_, err = GetMostUsedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			"NonExistentCustomer",
		)
		assert.Error(t, err)
	})

	t.Run("GetAllIntegrationsForCustomerName", func(t *testing.T) {
		customerName := "Test Multiple Integrations"

		// Create multiple associations for this customer name
		for _, schedulingIntegration := range []models.Integration{schedulingIntegration1, schedulingIntegration2} {
			err := RecordUsageByCustomerName(
				ctx,
				service.ID,
				tmsIntegration.ID,
				warehouse.ID,
				customerName,
				schedulingIntegration.ID,
			)
			require.NoError(t, err)
		}

		associations, err := GetAllIntegrationsForCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(associations), 2)

		// Verify all associations have the correct customer name
		for _, assoc := range associations {
			assert.Equal(t, customerName, *assoc.CustomerName)
			assert.NotZero(t, assoc.SchedulingIntegration.ID)
		}
	})

	t.Run("GetRecommendedIntegrationByCustomerName", func(t *testing.T) {
		customerName := "ACME Corporation"

		recommended, err := GetRecommendedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			customerName,
		)
		require.NoError(t, err)
		assert.NotNil(t, recommended)
		assert.NotZero(t, recommended.ID)

		// Non-existent customer name should return nil (not an error)
		recommended, err = GetRecommendedIntegrationByCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			"NonExistentCustomer",
		)
		require.NoError(t, err)
		assert.Nil(t, recommended)
	})

	t.Run("Same TMS Customer different TMS integration", func(t *testing.T) {
		otherTMSIntegration := createTestTMSIntegration(ctx, t, service.ID, models.Turvo)
		schedulingIntegration3 := createTestSchedulingIntegration(ctx, t, service.ID, models.E2open)

		association := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               service.ID,
			TMSIntegrationID:        otherTMSIntegration.ID, // Can change this to tmsIntegration.ID to see error case
			AssociatedWarehouseID:   warehouse.ID,
			TMSCustomerID:           &tmsCustomer.ID,
			SchedulingIntegrationID: schedulingIntegration3.ID,
			UsageCount:              1, // Lower than the first/primary association using tmsIntegration
			Notes:                   "Association with different TMS integration",
			IsDisabled:              false,
		}
		err := CreateOrUpdateAssociation(ctx, association, nil)
		require.NoError(t, err)

		// Gets by highest usage count (so this being 1 which is less than primary we can verify filtering by tms id)
		recommended, err := GetRecommendedIntegrationByTMSCustomer(
			ctx,
			service.ID,
			otherTMSIntegration.ID,
			warehouse.ID,
			tmsCustomer.ID,
		)
		require.NoError(t, err)
		assert.NotNil(t, recommended)
		assert.NotZero(t, recommended.ID)
		assert.Equal(t, recommended.ID, schedulingIntegration3.ID)
	})

	t.Run("IsDisabled filtering", func(t *testing.T) {
		// Create a disabled association
		disabledCustomerName := "Disabled Customer"
		disabledAssociation := &models.TMSCustomerSchedulingIntegration{
			ServiceID:               service.ID,
			TMSIntegrationID:        tmsIntegration.ID,
			AssociatedWarehouseID:   warehouse.ID,
			CustomerName:            &disabledCustomerName,
			SchedulingIntegrationID: schedulingIntegration1.ID,
			UsageCount:              100,
			IsDisabled:              true,
		}
		err := CreateOrUpdateAssociation(ctx, disabledAssociation, nil)
		require.NoError(t, err)

		// Query functions should not return disabled associations
		_, err = GetMostUsedIntegrationByCustomerName(
			ctx, service.ID, tmsIntegration.ID, warehouse.ID, disabledCustomerName)
		assert.Error(t, err, "Should not find disabled association")

		associations, err := GetAllIntegrationsForCustomerName(
			ctx,
			service.ID,
			tmsIntegration.ID,
			warehouse.ID,
			disabledCustomerName,
		)
		require.NoError(t, err)
		assert.Empty(t, associations)
	})
}

// Helper functions to create test data

func createTestWarehouse(ctx context.Context, t *testing.T) models.Warehouse {
	t.Helper()

	warehouse := models.Warehouse{
		WarehouseID:           "TEST-WH-001",
		WarehouseName:         "Test Warehouse",
		WarehouseAddressLine1: "123 Test St",
		WarehouseTimezone:     "America/Los_Angeles",
		Source:                models.YardViewSource,
	}

	err := rds.WithContext(ctx).Create(&warehouse).Error
	require.NoError(t, err)
	require.NotZero(t, warehouse.ID)

	return warehouse
}

// Create a TMS integration
func createTestTMSIntegration(
	ctx context.Context,
	t *testing.T,
	serviceID uint,
	integrationName models.IntegrationName,
) models.Integration {

	t.Helper()

	integration := models.Integration{
		ServiceID: serviceID,
		Name:      integrationName,
		Type:      models.TMS,
	}

	err := rds.WithContext(ctx).Create(&integration).Error
	require.NoError(t, err)
	require.NotZero(t, integration.ID)

	return integration
}

// Create a TMS customer
func createTestTMSCustomer(
	ctx context.Context,
	t *testing.T,
	tmsIntegration models.Integration,
	customerName,
	externalTMSID string,
) models.TMSCustomer {

	t.Helper()

	customer := models.TMSCustomer{
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: externalTMSID,
			Name:          customerName,
			City:          "Test City",
			State:         "CA",
			Zipcode:       "90210",
			Country:       "USA",
		},
		TMSIntegrationID: tmsIntegration.ID,
	}

	err := rds.WithContext(ctx).Create(&customer).Error
	require.NoError(t, err)
	require.NotZero(t, customer.ID)

	return customer
}

func createTestSchedulingIntegration(
	ctx context.Context,
	t *testing.T,
	serviceID uint,
	integrationName models.IntegrationName,
) models.Integration {
	t.Helper()

	integration := models.Integration{
		ServiceID: serviceID,
		Name:      integrationName,
		Type:      models.Scheduling,
	}

	err := rds.WithContext(ctx).Create(&integration).Error
	require.NoError(t, err)
	require.NotZero(t, integration.ID)

	return integration
}
