package rds

import (
	"context"
	"strings"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
)

func GetUserByID(ctx context.Context, id uint) (user models.User, err error) {
	return user, WithContextReader(ctx).Where("id = ?", id).First(&user).Error
}

func GetUserByEmail(ctx context.Context, emailAddr string) (user models.User, err error) {
	return user, WithContextReader(ctx).
		Where("lower(email_address) = ?", strings.ToLower(emailAddr)).
		First(&user).Error
}

func GetUserByOutlookIDs(ctx context.Context, userExternalID, clientState string) (user models.User, err error) {
	return user, WithContextReader(ctx).
		Where("mail_client_id = ? AND outlook_client_state = ?", userExternalID, clientState).
		First(&user).Error
}

func GetUserByExternalID(ctx context.Context, userExternalID string) (user models.User, err error) {
	return user, WithContextReader(ctx).Where("mail_client_id = ?", userExternalID).First(&user).Error
}

func GetAllUsers(ctx context.Context) (users []models.User, err error) {
	return users, WithContextReader(ctx).Find(&users).Error
}

func UpdateUser(ctx context.Context, updatedUser models.User) error {
	updatedUser.EmailAddress = strings.ToLower(updatedUser.EmailAddress)

	return WithContext(ctx).Model(&updatedUser).Updates(updatedUser).Error
}

func RemoveUserDATCredentials(ctx context.Context, user *models.User) error {
	return WithContext(ctx).
		Model(user).
		Updates(models.User{
			DATEmailAddress:          "",
			HasGrantedDATPermissions: false,
		}).Error
}

// UpdateSignatureSettings updates signature settings for a user.
// NOTE: gorm.Updates() by default does not update to null/empty values, so we need to explicitly set them to nil/false
func UpdateSignatureSettings(ctx context.Context, updatedUser *models.User) error {
	return WithContext(ctx).Model(updatedUser).
		Select(
			"EmailSignature", "EmailSignatureQuillDelta",
			"UseSignatureOnNewEmails", "UseSignatureOnRepliesForwards").
		Updates(updatedUser).Error
}

func UpdateUserReplaceGroups(ctx context.Context, updatedUser models.User) error {
	return WithContext(ctx).
		Model(&updatedUser).
		Association("UserGroups").
		Replace(updatedUser.UserGroups)
}

// Explicitly updates backfill start time because Gorm default behavior is to not update null/empty values
func UpdateBackfillLock(ctx context.Context, user *models.User, startTime models.NullTime) error {
	return WithContext(ctx).Model(user).Clauses(clause.Returning{}).
		Select("BackfillStartTime").
		Updates(models.User{BackfillStartTime: startTime}).
		Error
}

func CreateUser(ctx context.Context, user *models.User) error {
	user.EmailAddress = strings.ToLower(user.EmailAddress)
	return WithContext(ctx).Create(user).Error
}
