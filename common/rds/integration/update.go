package integration

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, updatedIntegration *models.Integration) error {
	return rds.WithContext(ctx).Model(updatedIntegration).Updates(*updatedIntegration).Error
}

func Disable(ctx context.Context, integration *models.Integration) error {
	if integration.Disabled {
		return fmt.Errorf("error integration is already disabled: %s %s", integration.Name, integration.Username)
	}

	integration.Disabled = true

	return rds.WithContext(ctx).Updates(integration).Error
}
