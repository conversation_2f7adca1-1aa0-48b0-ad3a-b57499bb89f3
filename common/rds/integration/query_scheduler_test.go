package integration

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestLiveGetSchedulerByServiceUserIDsAndName(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestLiveGetSchedulerByServiceUserIDsAndName: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)

	// Create test services
	service1 := rds.CreateTestService(ctx, t, models.Service{Name: "Test Service 1"})

	service2 := rds.CreateTestService(ctx, t, models.Service{Name: "Test Service 2"})

	// Create test user for service 1 (not part of any user group)
	user := rds.CreateTestUser(ctx, t, service1.ID)

	// Create integrations for service 1 (2 service-wide integrations: OpenDock, Retalix)
	openDockService1 := models.Integration{
		Name:          models.Opendock,
		Type:          models.Scheduling,
		ServiceID:     service1.ID,
		IsServiceWide: true,
		Disabled:      false,
	}
	err := Create(ctx, &openDockService1)
	require.NoError(t, err)

	retalixService1 := models.Integration{
		Name:          models.Retalix,
		Type:          models.Scheduling,
		ServiceID:     service1.ID,
		IsServiceWide: true,
		Disabled:      false,
	}
	err = Create(ctx, &retalixService1)
	require.NoError(t, err)

	// Create integrations for service 2 (OpenDock and YardView to test isolation)
	openDockService2 := models.Integration{
		Name:          models.Opendock,
		Type:          models.Scheduling,
		ServiceID:     service2.ID,
		IsServiceWide: true,
		Disabled:      false,
	}
	err = Create(ctx, &openDockService2)
	require.NoError(t, err)

	yardViewService2 := models.Integration{
		Name:          models.YardView,
		Type:          models.Scheduling,
		ServiceID:     service2.ID,
		IsServiceWide: true,
		Disabled:      false,
	}
	err = Create(ctx, &yardViewService2)
	require.NoError(t, err)

	t.Run("returns service-wide OpenDock integration for user in service 1", func(t *testing.T) {
		integration, err := GetSchedulerByServiceUserIDsAndName(
			ctx,
			user.ID,
			service1.ID,
			string(models.Opendock),
			0,
		)

		require.NoError(t, err)
		assert.Equal(t, openDockService1.ID, integration.ID)
		assert.Equal(t, models.Opendock, integration.Name)
		assert.Equal(t, models.Scheduling, integration.Type)
		assert.Equal(t, service1.ID, integration.ServiceID)
		assert.True(t, integration.IsServiceWide)
		assert.False(t, integration.Disabled)
	})

	t.Run("returns ErrRecordNotFound when integration does not exist in service", func(t *testing.T) {
		_, err := GetSchedulerByServiceUserIDsAndName(
			ctx,
			user.ID,
			service1.ID,
			string(models.YardView), // YardView doesn't exist in service 1
			0,
		)

		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("does not return integration from different service", func(t *testing.T) {
		// User from service 1 should not get YardView from service 2
		_, err := GetSchedulerByServiceUserIDsAndName(
			ctx,
			user.ID,
			service1.ID,
			string(models.YardView), // YardView exists in service 2 but not service 1
			0,
		)

		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)

		// Double-check that YardView does exist in service 2
		integration, err := GetSchedulerByServiceUserIDsAndName(
			ctx,
			user.ID,
			service2.ID, // Query service 2 instead
			string(models.YardView),
			0,
		)
		require.NoError(t, err)
		assert.Equal(t, yardViewService2.ID, integration.ID)
		assert.Equal(t, service2.ID, integration.ServiceID)
	})

	// Test user group functionality
	t.Run("user group integration tests", func(t *testing.T) {
		// Create Team 1 and Team 2 user groups
		team1 := models.UserGroup{
			Name:      "Team 1",
			ServiceID: service1.ID,
		}
		err := rds.WithContext(ctx).Create(&team1).Error
		require.NoError(t, err)

		team2 := models.UserGroup{
			Name:      "Team 2",
			ServiceID: service1.ID,
		}
		err = rds.WithContext(ctx).Create(&team2).Error
		require.NoError(t, err)

		// Create group-specific integrations for Team 1 (OpenDock, Retalix)
		team1OpenDock := models.Integration{
			Name:          models.Opendock,
			Type:          models.Scheduling,
			ServiceID:     service1.ID,
			IsServiceWide: false, // Group-specific
			Disabled:      false,
		}
		err = Create(ctx, &team1OpenDock)
		require.NoError(t, err)
		//nolint:lll
		// TODO: ENG-4147 Gorm tag "default:true" takes precedence even when we use Select("*").Create, so manually set to false
		err = rds.WithContext(ctx).Debug().Model(&team1OpenDock).Updates(map[string]any{"is_service_wide": false}).Error
		require.NoError(t, err)

		team1Retalix := models.Integration{
			Name:          models.Retalix,
			Type:          models.Scheduling,
			ServiceID:     service1.ID,
			IsServiceWide: false, // Group-specific
			Disabled:      false,
		}
		err = Create(ctx, &team1Retalix)
		require.NoError(t, err)
		err = rds.WithContext(ctx).Model(&team1Retalix).Updates(map[string]any{"is_service_wide": false}).Error
		require.NoError(t, err)

		// Create group-specific integrations for Team 2 (OpenDock, Retalix)
		team2OpenDock := models.Integration{
			Name:          models.Opendock,
			Type:          models.Scheduling,
			ServiceID:     service1.ID,
			IsServiceWide: false, // Group-specific
			Disabled:      false,
		}
		err = Create(ctx, &team2OpenDock)
		require.NoError(t, err)
		err = rds.WithContext(ctx).Model(&team2OpenDock).Updates(map[string]any{"is_service_wide": false}).Error
		require.NoError(t, err)

		team2Retalix := models.Integration{
			Name:          models.Retalix,
			Type:          models.Scheduling,
			ServiceID:     service1.ID,
			IsServiceWide: false, // Group-specific
			Disabled:      false,
		}
		err = Create(ctx, &team2Retalix)
		require.NoError(t, err)
		err = rds.WithContext(ctx).Model(&team2Retalix).Updates(map[string]any{"is_service_wide": false}).Error
		require.NoError(t, err)

		// Assert that the integrations were created
		require.NotEmpty(t, team1OpenDock.ID)
		require.NotEmpty(t, team1Retalix.ID)
		require.NotEmpty(t, team2OpenDock.ID)
		require.NotEmpty(t, team2Retalix.ID)

		// Associate integrations with user groups through many-to-many relationship
		err = rds.WithContext(ctx).
			Model(&team1).
			Association("Integrations").
			Append(&team1OpenDock, &team1Retalix)
		require.NoError(t, err)

		err = rds.WithContext(ctx).
			Model(&team2).
			Association("Integrations").
			Append(&team2OpenDock, &team2Retalix)
		require.NoError(t, err)

		t.Run("user with no team association uses service-wide OpenDock", func(t *testing.T) {
			// User should get service-wide integration since they're not in any group
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				user.ID,
				service1.ID,
				string(models.Opendock),
				0,
			)

			require.NoError(t, err)
			// Should return the service-wide OpenDock, not any group-specific one
			assert.Equal(t, openDockService1.ID, integration.ID)
			assert.True(t, integration.IsServiceWide)
		})

		t.Run("user in Team 1 uses Team 1 integrations", func(t *testing.T) {
			// Add user to Team 1
			err := rds.WithContext(ctx).
				Model(&team1).
				Association("Users").
				Append(&user)
			require.NoError(t, err)

			// User should now get Team 1's OpenDock integration
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				user.ID,
				service1.ID,
				string(models.Opendock),
				0,
			)

			require.NoError(t, err)
			// Should return Team 1's OpenDock, not service-wide
			assert.Equal(t, team1OpenDock.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)

			// User should also get Team 1's Retalix integration
			integration, err = GetSchedulerByServiceUserIDsAndName(
				ctx,
				user.ID,
				service1.ID,
				string(models.Retalix),
				0,
			)

			require.NoError(t, err)
			assert.Equal(t, team1Retalix.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)

			// User should NOT get Team 2's integrations
			// Since user is only in Team 1, they shouldn't access Team 2's integrations
			// But since we don't have a way to directly test this without modifying the function,
			// we can at least verify the returned integration is from Team 1, not Team 2
			assert.NotEqual(t, team2OpenDock.ID, integration.ID)
			assert.NotEqual(t, team2Retalix.ID, integration.ID)
		})
	})

	// Test integrationID priority logic
	t.Run("integrationID priority tests", func(t *testing.T) {
		// Create Team 3 for these specific tests
		team3 := models.UserGroup{
			Name:      "Team 3",
			ServiceID: service1.ID,
		}
		err := rds.WithContext(ctx).Create(&team3).Error
		require.NoError(t, err)

		// Create a new user for these tests
		testUser := rds.CreateTestUser(ctx, t, service1.ID)

		// Create group-specific integration for Team 3
		team3OpenDock := models.Integration{
			Name:          models.Opendock,
			Type:          models.Scheduling,
			ServiceID:     service1.ID,
			IsServiceWide: false,
			Disabled:      false,
		}
		err = Create(ctx, &team3OpenDock)
		require.NoError(t, err)
		err = rds.WithContext(ctx).Model(&team3OpenDock).Updates(map[string]any{"is_service_wide": false}).Error
		require.NoError(t, err)

		// Associate integration with Team 3
		err = rds.WithContext(ctx).
			Model(&team3).
			Association("Integrations").
			Append(&team3OpenDock)
		require.NoError(t, err)

		// Add user to Team 3
		err = rds.WithContext(ctx).
			Model(&team3).
			Association("Users").
			Append(&testUser)
		require.NoError(t, err)

		t.Run("Priority 1: specific integrationID found (service-wide)", func(t *testing.T) {
			// Request specific service-wide integration by ID
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Opendock),
				openDockService1.ID, // Request service-wide by ID
			)

			require.NoError(t, err)
			assert.Equal(t, openDockService1.ID, integration.ID)
			assert.True(t, integration.IsServiceWide)
		})

		t.Run("Priority 1: specific integrationID found (group-specific)", func(t *testing.T) {
			// Request specific group integration by ID
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Opendock),
				team3OpenDock.ID, // Request group-specific by ID
			)

			require.NoError(t, err)
			assert.Equal(t, team3OpenDock.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)
		})

		t.Run("Priority 1: integrationID not found, falls back to Priority 2", func(t *testing.T) {
			// Request non-existent integration ID, should fall back to group-specific
			nonExistentID := uint(99999)
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Opendock),
				nonExistentID,
			)

			require.NoError(t, err)
			// Should fall back to group-specific integration (Priority 2)
			assert.Equal(t, team3OpenDock.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)
		})

		t.Run("Priority 1: integrationID from wrong service, falls back to Priority 2", func(t *testing.T) {
			// Request integration ID from different service, should fall back
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Opendock),
				openDockService2.ID, // ID from service2, but querying service1
			)

			require.NoError(t, err)
			// Should fall back to group-specific integration (Priority 2)
			assert.Equal(t, team3OpenDock.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)
		})

		t.Run("Priority 1: user has no access to group integration by ID, fallback to Priority 2", func(t *testing.T) {
			// Create another user not in Team 3
			otherUser := rds.CreateTestUser(ctx, t, service1.ID)

			// Request Team 3's integration by ID, but user is not in Team 3
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				otherUser.ID,
				service1.ID,
				string(models.Opendock),
				team3OpenDock.ID,
			)

			require.NoError(t, err)
			// Should fall back to service-wide integration (Priority 3, since user has no group)
			assert.Equal(t, openDockService1.ID, integration.ID)
			assert.True(t, integration.IsServiceWide)
		})

		t.Run("Priority 2: group-specific integration when no integrationID provided", func(t *testing.T) {
			// No integrationID provided, should get group-specific integration
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Opendock),
				0, // No specific ID
			)

			require.NoError(t, err)
			assert.Equal(t, team3OpenDock.ID, integration.ID)
			assert.False(t, integration.IsServiceWide)
		})

		t.Run("Priority 3: service-wide fallback when user has no group", func(t *testing.T) {
			// Create user not in any group
			ungroupedUser := rds.CreateTestUser(ctx, t, service1.ID)

			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				ungroupedUser.ID,
				service1.ID,
				string(models.Opendock),
				0,
			)

			require.NoError(t, err)
			assert.Equal(t, openDockService1.ID, integration.ID)
			assert.True(t, integration.IsServiceWide)
		})

		t.Run("Priority 3: service-wide fallback when group has no matching integration", func(t *testing.T) {
			// User is in Team 3, but Team 3 doesn't have Retalix integration
			// Should fall back to service-wide Retalix
			integration, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.Retalix), // Team 3 doesn't have Retalix
				0,
			)

			require.NoError(t, err)
			assert.Equal(t, retalixService1.ID, integration.ID)
			assert.True(t, integration.IsServiceWide)
		})

		t.Run("Error: no integration found at any priority level", func(t *testing.T) {
			// Request integration that doesn't exist anywhere
			_, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.YardView), // Doesn't exist in service1
				0,
			)

			assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
		})

		t.Run("Error: integrationID exists but wrong name", func(t *testing.T) {
			// Request specific ID but with wrong integration name
			_, err := GetSchedulerByServiceUserIDsAndName(
				ctx,
				testUser.ID,
				service1.ID,
				string(models.YardView), // Wrong name
				openDockService1.ID,     // OpenDock ID
			)

			assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
		})
	})
}
