package carriergroup

import (
	"context"
	"errors"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Create inserts a new carrier group in the database
func Create(ctx context.Context, carrierGroup *models.CarrierGroup) error {
	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(carrierGroup).Error
}

// CreateInBatch creates multiple carrier groups in a batch operation
func CreateInBatch(ctx context.Context, carrierGroups *[]models.CarrierGroup) error {
	if err := checkCarrierGroups(carrierGroups); err != nil {
		return err
	}

	return rds.WithContext(ctx).CreateInBatches(carrierGroups, 1000).Error
}

// Upsert updates existing carrier group or inserts new one if not exists
func Upsert(ctx context.Context, carrierGroup *models.CarrierGroup) error {
	return rds.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns:   []clause.Column{{Name: "name"}, {Name: "service_id"}},
				DoUpdates: clause.AssignmentColumns([]string{"name", "email", "service_id"}),
			},
			clause.Returning{},
		).
		Create(carrierGroup).
		Error
}

// UpsertInBatch updates existing carrier groups or inserts new ones in the database.
func UpsertInBatch(ctx context.Context, carrierGroups *[]models.CarrierGroup) error {
	if err := checkCarrierGroups(carrierGroups); err != nil {
		return err
	}

	return rds.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns:   []clause.Column{{Name: "name"}, {Name: "service_id"}},
				DoUpdates: clause.AssignmentColumns([]string{"name", "email", "service_id"}),
			},
			clause.Returning{},
		).
		CreateInBatches(carrierGroups, 1000).
		Error
}

// checkCarrierGroups checks if the carrier groups slice is valid
func checkCarrierGroups(carrierGroups *[]models.CarrierGroup) error {
	if carrierGroups == nil {
		return errors.New("nil CarrierGroups slice")
	}

	if len(*carrierGroups) == 0 {
		return errors.New("empty slice of CarrierGroups")
	}

	return nil
}
