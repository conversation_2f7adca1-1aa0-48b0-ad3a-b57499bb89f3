package carriergroup

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchCarrierGroupQuery struct {
	ServiceID uint   `json:"serviceID"`
	Name      string `json:"name"`
}

// Get retrieves a carrier group by ID
func Get(ctx context.Context, id uint) (*models.CarrierGroup, error) {
	var carrierGroup models.CarrierGroup

	err := rds.WithContextReader(ctx).Preload("Service").First(&carrierGroup, id).Error
	if err != nil {
		return nil, err
	}

	return &carrierGroup, nil
}

// GetByName retrieves a carrier group by name
func GetByName(ctx context.Context, name string) (*models.CarrierGroup, error) {
	var carrierGroup models.CarrierGroup

	err := rds.WithContextReader(ctx).Preload("Service").Where("name = ?", name).First(&carrierGroup).Error
	if err != nil {
		return nil, err
	}

	return &carrierGroup, nil
}

// GetByServiceID retrieves all carrier groups for a given service ID
func GetByServiceID(ctx context.Context, serviceID uint, limit int) ([]models.CarrierGroup, error) {
	var carrierGroups []models.CarrierGroup
	query := rds.WithContextReader(ctx).Preload("TMSCarriers").Where("service_id = ?", serviceID)

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&carrierGroups).Error
	if err != nil {
		return nil, err
	}

	return carrierGroups, nil
}

// List retrieves all carrier groups with optional pagination
func List(ctx context.Context, page, pageSize int) ([]models.CarrierGroup, error) {
	var carrierGroups []models.CarrierGroup
	query := rds.WithContextReader(ctx).Preload("Service")

	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(&carrierGroups).Error
	if err != nil {
		return nil, err
	}

	return carrierGroups, nil
}

// Count returns the total number of carrier groups
func Count(ctx context.Context) (int64, error) {
	var count int64

	err := rds.WithContextReader(ctx).Model(&models.CarrierGroup{}).Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}

// FuzzySearchByName performs a fuzzy search on carrier group names within a service.
func FuzzySearchByName(
	ctx context.Context,
	query SearchCarrierGroupQuery,
	limit int,
) ([]models.CarrierGroup, error) {
	if query.Name == "" {
		return nil, nil
	}

	var carrierGroups []models.CarrierGroup
	const similarityThreshold = 0.1

	db := rds.WithContextReader(ctx).
		Model(&models.CarrierGroup{}).
		Scopes(fuzzyMatchName(ctx, query.Name, query.ServiceID, similarityThreshold))

	if limit > 0 {
		db = db.Limit(limit)
	}

	err := db.Preload("TMSCarriers").Find(&carrierGroups).Error
	if err != nil {
		return nil, err
	}

	return carrierGroups, nil
}

// fuzzyMatchByField returns a scope function for fuzzy matching carrier group fields
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	serviceID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH carrier_group_matches AS (
					SELECT *,
						%s <-> ? AS trgm_dist,
						%s ILIKE '%%' || ? || '%%' AS is_match
					FROM carrier_groups
					WHERE service_id = ?
						AND (%s %% ? OR %s ILIKE '%%' || ? || '%%')
				)
				SELECT *
				FROM carrier_group_matches
				ORDER BY
					is_match DESC,
					trgm_dist ASC;
			`, columnName, columnName, columnName, columnName)

			return tx.Raw(query,
				searchTerm, searchTerm, // for <-> and ILIKE
				serviceID,              // for service_id
				searchTerm, searchTerm, // for % and ILIKE
			)
		})
	}
}

// fuzzyMatchName returns a scope function for fuzzy matching carrier group names.
func fuzzyMatchName(
	ctx context.Context,
	name string,
	serviceID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "name", name, serviceID, similarityThreshold)
}

// WithPreload returns a gorm DB preloaded with relations using the DB reader connection.
func WithPreload(ctx context.Context) *gorm.DB {
	return rds.WithContextReader(ctx).Model(&models.CarrierGroup{}).Preload("Service")
}
