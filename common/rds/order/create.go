package order

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// CreateOrderInDB creates a new order in the database or updates an existing one
func CreateOrderInDB(ctx context.Context, order *models.Order) error {
	db := rds.WithContext(ctx)

	// If OrderTrackingID is empty, just create a new order
	if order.OrderTrackingID == "" {
		return db.Create(order).Error
	}

	// Use OnConflict to handle the unique constraint on order_tracking_id
	return db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "order_tracking_id"}},
		UpdateAll: true,
	}).Create(order).Error
}
