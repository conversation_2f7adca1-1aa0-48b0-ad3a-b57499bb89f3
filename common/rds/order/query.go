package order

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetOrderFromDB retrieves an order by ID
func GetOrderFromDB(ctx context.Context, id uint) (*models.Order, error) {
	var order models.Order

	db := rds.WithContextReader(ctx)
	if err := db.First(&order, id).Error; err != nil {
		return nil, err
	}

	return &order, nil
}

// GetOrderByReference retrieves an order by any reference number
func GetOrderByReference(ctx context.Context, system string, value string) (*models.Order, error) {
	var order models.Order
	db := rds.WithContextReader(ctx)

	// Search in various order fields based on the system
	query := db.Model(&models.Order{})
	switch system {
	case "PO":
		query = query.Where("po_nums LIKE ?", "%"+value+"%")
	case "ID":
		query = query.Where("external_order_id = ? OR order_tracking_id = ?", value, value)
	case "TMS":
		query = query.Where("external_tms_id = ?", value)
	default:
		// If system is unknown, search in all relevant fields
		query = query.Where(
			"po_nums LIKE ? OR external_order_id = ? OR order_tracking_id = ? OR external_tms_id = ?",
			"%"+value+"%", value, value, value,
		)
	}

	if err := query.First(&order).Error; err != nil {
		return nil, err
	}

	return &order, nil
}

// GetOrderByPrimaryReference retrieves an order by its primary reference number for a given system
func GetOrderByPrimaryReference(ctx context.Context, system string, value string) (*models.Order, error) {
	// Since we don't have a concept of primary references anymore,
	// this function behaves the same as GetOrderByReference
	return GetOrderByReference(ctx, system, value)
}

// ListOrdersFromDB retrieves all orders with optional filtering
func ListOrdersFromDB(ctx context.Context, status, serviceID, fromDate, toDate string) ([]models.Order, error) {
	var orders []models.Order
	db := rds.WithContextReader(ctx)
	query := db.Model(&models.Order{})

	if status != "" {
		query = query.Where("status = ?", status)
	}
	if serviceID != "" {
		query = query.Where("service_id = ?", serviceID)
	}
	if fromDate != "" {
		query = query.Where("pickup_date >= ?", fromDate)
	}
	if toDate != "" {
		query = query.Where("pickup_date <= ?", toDate)
	}

	if err := query.Find(&orders).Error; err != nil {
		return nil, err
	}

	return orders, nil
}

// GetOrderByTrackingID retrieves an order by its tracking ID
func GetOrderByTrackingID(ctx context.Context, trackingID string) (*models.Order, error) {
	var order models.Order
	db := rds.WithContextReader(ctx)

	if err := db.Where("order_tracking_id = ?", trackingID).First(&order).Error; err != nil {
		return nil, err
	}

	return &order, nil
}
