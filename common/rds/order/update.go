package order

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateOrderInDB updates an existing order
func UpdateOrderInDB(ctx context.Context, order *models.Order) error {
	db := rds.WithContext(ctx)
	return db.Save(order).Error
}

// DeleteOrderFromDB deletes an order
func DeleteOrderFromDB(ctx context.Context, id uint) error {
	db := rds.WithContext(ctx)
	return db.Delete(&models.Order{}, id).Error
}

// AddLoadToOrderInDB adds a load to an order
func AddLoadToOrderInDB(ctx context.Context, orderID, loadID uint) error {
	db := rds.WithContext(ctx)

	// Update the load to reference the order
	order := &models.Order{}
	if err := db.First(order, orderID).Error; err != nil {
		return err
	}

	order.LoadID = loadID
	return db.Save(order).Error
}

// RemoveLoadFromOrderInDB removes a load from an order
func RemoveLoadFromOrderInDB(ctx context.Context, orderID uint) error {
	db := rds.WithContext(ctx)

	// Update the load to remove the order reference
	order := &models.Order{}
	if err := db.First(order, orderID).Error; err != nil {
		return err
	}

	order.LoadID = 0
	return db.Save(order).Error
}
