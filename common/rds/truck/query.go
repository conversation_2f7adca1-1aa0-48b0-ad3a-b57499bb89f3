package truck

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Gets truck list by associated email ID
func GetTruckListByEmail(
	ctx context.Context,
	emailID string,
) (res *models.TruckList, err error) {
	// ORDER BY created_at DESC: This was added to support backfilling. When we backfilled
	// drumkittest@redwoodlogistics emails on 10/24/2024, we noticed that there could be
	// multiple truck lists for the same email. We want to be fetching the most recent one
	// because it contains the most up-to-date information.
	return res, rds.
		WithContextReader(ctx).
		Preload("Trucks").
		Order("created_at DESC").
		First(&res, "email_id = ?", emailID).Error
}

// Gets trucks by associated email ID
func GetTrucksByEmail(
	ctx context.Context,
	emailID string,
) (res []*models.Truck, err error) {
	return res, rds.WithContextReader(ctx).Find(&res, "email_id = ?", emailID).Error
}

// Gets truck list by associated thread ID
func GetTruckListByThreadID(
	ctx context.Context,
	threadID string,
) (res *models.TruckList, err error) {
	return res, rds.
		WithContextReader(ctx).
		Preload("Trucks").
		Order("created_at DESC").
		First(&res, "thread_id = ?", threadID).Error
}

// Gets trucks by associated thread ID
func GetTrucksByThreadID(
	ctx context.Context,
	threadID string,
) (res []*models.Truck, err error) {
	return res, rds.WithContextReader(ctx).Find(&res, "thread_id = ?", threadID).Error
}
