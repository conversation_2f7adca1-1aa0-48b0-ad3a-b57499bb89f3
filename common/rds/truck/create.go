package truck

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func CreateTruckList(ctx context.Context, trucklist *models.TruckList) error {
	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(&trucklist).Error
}

func BatchUpsertTrucks(ctx context.Context, trucks []models.Truck) error {
	if len(trucks) == 0 {
		log.Debug(ctx, "skipping upsert for empty list of trucks")

		return nil
	}

	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(&trucks).Error
}

func GetTruckByID(ctx context.Context, id uint) (truck models.Truck, err error) {
	return truck, rds.WithContext(ctx).
		Where("id = ?", id).
		First(&truck).Error
}

func CreateTruck(ctx context.Context, truck *models.Truck) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{}).
		Create(&truck).
		First(truck).Error
}

func DeleteTruck(ctx context.Context, truck models.Truck) error {
	return rds.WithContext(ctx).Delete(&truck).Error
}
