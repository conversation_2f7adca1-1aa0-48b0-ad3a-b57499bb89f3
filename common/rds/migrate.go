package rds

import (
	"context"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// NOTE: Order matters for foreign keys; do not alphabetize
var DefaultMigrationOrder = []any{
	&models.Service{},
	&models.Integration{},
	&models.Warehouse{},
	&models.User{},
	&models.UserGroup{},
	&models.Email{},
	&models.Order{},
	&models.Load{},
	&models.Commodity{},
	&models.Appointment{},
	&models.CheckCall{},
	&models.Exception{},
	&models.SuggestedLoadChange{},
	&models.Stop{},
	&models.QuoteRequest{},
	&models.QuickQuote{},
	&models.QuickQuoteConfig{},
	&models.CarrierQuote{},
	&models.CarrierGroup{},
	&models.CarrierQuoteConfig{},
	&models.GeneratedEmail{},
	&models.EmailTemplate{},
	&models.Truck{},
	&models.TMSUser{},
	&models.TMSCustomer{},
	&models.TMSCarrier{},
	&models.TMSCustomerSchedulingIntegration{},
	&models.TMSLocation{},
	&models.TruckList{},
	&models.LoadViewEvent{},
	&models.Vector{},
	&models.MetabaseDashboard{},
	&models.EmailForwardingRule{},
	&models.WarehouseAddress{},
	&models.BatchQuoteQuoteRequest{},
	&models.BatchQuote{},
	&models.TMSCustomerPrompt{},
}

func AutoMigrate(ctx context.Context, opts ...Option) error {
	options := &Options{
		IsOnPrem:       false,
		MigrationOrder: DefaultMigrationOrder,
	}

	for _, opt := range opts {
		opt(options)
	}

	// Some extensions required before migrations
	if !options.IsOnPrem {
		err := setupDBExtensions(ctx)
		if err != nil {
			return err
		}
	}

	if err := WithContext(ctx).AutoMigrate(options.MigrationOrder...); err != nil {
		return fmt.Errorf("gorm AutoMigrate failed: %w", err)
	}

	// Indexes can be created only after migrations
	if !options.IsOnPrem {
		err := setupDBIndexes(ctx)
		if err != nil {
			return err
		}
	}

	log.Info(ctx, "rds.AutoMigrate finished successfully")

	return nil
}

func setupDBExtensions(ctx context.Context) error {
	// Setup pg_trgm for text search
	pgtrgmQuery := `CREATE EXTENSION IF NOT EXISTS pg_trgm;`
	if err := WithContext(ctx).Exec(pgtrgmQuery).Error; err != nil {
		return errors.New("failed to setup pg_trgm")
	}

	// Setup btree_gist for GIN indexes
	gistQuery := `CREATE EXTENSION IF NOT EXISTS btree_gist;`
	if err := WithContext(ctx).Exec(gistQuery).Error; err != nil {
		return errors.New("failed to setup btree_gist")
	}

	// Setup PostGIS for geospatial functionality
	postgisQuery := `CREATE EXTENSION IF NOT EXISTS postgis;`
	if err := WithContext(ctx).Exec(postgisQuery).Error; err != nil {
		return errors.New("failed to setup postgis")
	}

	// Setup pgvector for vector similarity search (embeddings)
	vectorQuery := `CREATE EXTENSION IF NOT EXISTS vector;`
	if err := WithContext(ctx).Exec(vectorQuery).Error; err != nil {
		return errors.New("failed to setup vector")
	}

	return nil
}

//nolint:lll
func setupDBIndexes(ctx context.Context) error {
	// models.Warehouse{} - Search Index - Address
	if !WithContext(ctx).Migrator().HasIndex(&models.Warehouse{}, "idx_warehouse_address") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_warehouse_address ON warehouses USING gin (warehouse_full_address gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create address search index on Warehouses")
		}
		log.Info(ctx, "created gin search index on Warehouses.FullAddress")
	}

	// models.Warehouse{} - Search Index - Identifier
	if !WithContext(ctx).Migrator().HasIndex(&models.Warehouse{}, "idx_warehouse_identifier") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_warehouse_identifier ON warehouses USING gin (warehouse_full_identifier gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create idenfitier search index on Warehouses")
		}
		log.Info(ctx, "created gin search index on Warehouses.FullIdentifier")
	}

	// models.TMSLocation{} - Unique Index
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_id_tms_location") {
		query := `
			CREATE UNIQUE INDEX IF NOT EXISTS idx_tms_id_tms_location ON tms_locations (tms_integration_id, external_tms_id);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite unique index on TMSLocation")
		}
		log.Info(ctx, "created composite unique index on TMSLocation")
	}

	// B-tree indexes for TMSLocation{}

	// For exact/prefix matching with TMS filtering (Location Search Stage 1 performance).
	// This is critical for exactMatchSearch() performance.
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_locations_tms_id_name_address") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_locations_tms_id_name_address ON tms_locations (tms_integration_id, name_address);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite index on TMSLocation")
		}
		log.Info(ctx, "created composite index on TMSLocation")
	}

	// For name searches with TMS filtering.
	// Supports FuzzySearchByName() and enhancedFuzzyMatchNameAddress() filtering
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_locations_tms_id_name") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_locations_tms_id_name ON tms_locations (tms_integration_id, name);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite index on TMSLocation")
		}
		log.Info(ctx, "created composite index on TMSLocation")
	}

	// For address searches with TMS filtering.
	// Supports FuzzySearchByStreetAddress() and enhancedFuzzyMatchNameAddress() filtering
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_locations_tms_id_address") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_locations_tms_id_address ON tms_locations (tms_integration_id, address_line1);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite index on TMSLocation")
		}
		log.Info(ctx, "created composite index on TMSLocation")
	}

	// GIN indexes for TMSLocation{}

	// Name
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_location_name") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_location_name ON tms_locations USING gin (name gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSLocation")
		}
		log.Info(ctx, "created gin search index on TMSLocation.Name")
	}

	// Address Line 1
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_location_address_line1") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_location_address_line1 ON tms_locations USING gin (address_line1 gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSLocation")
		}
		log.Info(ctx, "created gin search index on TMSLocation.address_line1")
	}

	// Full Address
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_tms_location_full_address") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_location_full_address ON tms_locations USING gin (full_address gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSLocation")
		}
		log.Info(ctx, "created gin search index on TMSLocation.full_address")
	}

	// Name + Full_Address
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "idx_name_address_trgm") {
		query := `
		CREATE INDEX IF NOT EXISTS idx_name_address_trgm ON tms_locations USING GIN (name_address gin_trgm_ops);
		`

		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSLocation")
		}
		log.Info(ctx, "created gin search index on TMSLocation.name_address")
	}

	// models.TMSCustomer{} - Search Index
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSCustomer{}, "idx_tms_customer_name") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_customer_name ON tms_customers USING gin (name gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSCustomer")
		}
		log.Info(ctx, "created gin search index on TMSCustomer.Name")
	}

	// models.TMSCustomer{} - Unique Index
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSCustomer{}, "idx_tms_id_tms_customer") {
		query := `
			CREATE UNIQUE INDEX IF NOT EXISTS idx_tms_id_tms_customer ON tms_customers (tms_integration_id, external_tms_id);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite unique index on TMSCustomer")
		}

		log.Info(ctx, "created composite unique index on TMSCustomer")
	}

	// models.TMSCarrier{} - Unique Index
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSCarrier{}, "idx_tms_id_tms_carrier") {
		query := `
			CREATE UNIQUE INDEX IF NOT EXISTS idx_tms_id_tms_carrier ON tms_carriers (tms_integration_id, external_tms_id);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create composite unique index on TMSCarrier")
		}

		log.Info(ctx, "created composite unique index on TMSCarrier")
	}

	// models.TMSCarrier{} - Search Index
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSCarrier{}, "idx_tms_carrier_name") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_tms_carrier_name ON tms_carriers USING gin (name gin_trgm_ops);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create GIN index on TMSCustomer")
		}
		log.Info(ctx, "created gin search index on TMSCarrier.Name")
	}

	// models.QuoteRequest{} - Location Indices -- Suggested Fields (see findQuoteRequestsMatchingLoad for reference)
	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_suggested_pickup_loc") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_suggested_pickup_loc
			ON quote_requests (trim(lower(suggested_pickup_state)), trim(lower(suggested_pickup_city)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create suggested pickup location index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest suggested pickup location")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_suggested_pickup_zip") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_suggested_pickup_zip
			ON quote_requests (trim(lower(suggested_pickup_zip)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create pickup zip index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest suggested pickup zip")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_suggested_dropoff_loc") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_suggested_dropoff_loc
			ON quote_requests (trim(lower(suggested_dropoff_state)), trim(lower(suggested_dropoff_city)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create suggested dropoff location index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest suggested dropoff location")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_suggested_dropoff_zip") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_suggested_dropoff_zip
			ON quote_requests (trim(lower(suggested_dropoff_zip)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create suggested dropoff zip index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest suggested dropoff zip")
	}

	// models.QuoteRequest{} - Location Indices -- Applied Fields (see findQuoteRequestsMatchingLoad for reference)
	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_applied_pickup_loc") {
		// State listed in index first because it's lower cardinality, higher selectivity than city
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_applied_pickup_loc
			ON quote_requests (trim(lower(applied_pickup_state)), trim(lower(applied_pickup_city)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create applied pickup location index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest applied pickup location")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_applied_pickup_zip") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_applied_pickup_zip
			ON quote_requests (trim(lower(applied_pickup_zip)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create applied pickup zip index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest pickup zip")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_applied_dropoff_loc") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_applied_dropoff_loc
			ON quote_requests (trim(lower(applied_dropoff_state)), trim(lower(applied_dropoff_city)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create applied dropoff location index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest applied dropoff location")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_qr_applied_dropoff_zip") {
		query := `
			CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_qr_applied_dropoff_zip
			ON quote_requests (trim(lower(applied_dropoff_zip)));
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create applied dropoff zip index on QuoteRequest")
		}
		log.Info(ctx, "created composite index on QuoteRequest applied dropoff zip")
	}

	// Create spatial GiST index for PostGIS point column
	if !WithContext(ctx).Migrator().HasIndex(&models.TMSLocation{}, "point_idx") {
		// "Building a spatial index is a computationally intensive exercise...and blocks write access to your table,
		// so on a production system you may want to do it in a slower, CONCURRENTLY-aware way"
		// https://postgis.net/docs/manual-3.3/using_postgis_dbmanagement.html#gist_indexes
		err := WithContext(ctx).Exec(
			"CREATE INDEX CONCURRENTLY IF NOT EXISTS point_idx ON tms_locations USING GIST ( point )").Error
		if err != nil {
			return fmt.Errorf("failed to create GIST index: %w", err)
		}
		log.Info(ctx, "created GiST spatial index on TMSLocation.point")
	}

	// models.Load{} - Lane History Index - 3-digit zip tier lookups
	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_lane_history_zip_prefix") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_lane_history_zip_prefix
			ON loads (
				service_id,
				pickup_date,
				pickup_zip_prefix,
				consignee_zip_prefix,
				specifications_transport_type
			)
			INCLUDE (
				carrier_name,
				ratedata_carrier_total_cost,
				specifications_total_distance
			)
			WHERE pickup_date IS NOT NULL;
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create zip code lane history index on Load")
		}
		log.Info(ctx, "created covering index on Load for zip prefix tier")
	}

	// models.Load{} - Lane History Index - City-to-city tier lookups
	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_lane_history_city") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_lane_history_city
			ON loads (
				service_id,
				pickup_date,
				pickup_city,
				pickup_state,
				consignee_city,
				consignee_state,
				specifications_transport_type
			)
			INCLUDE (
				carrier_name,
				ratedata_carrier_total_cost,
				specifications_total_distance
			)
			WHERE pickup_date IS NOT NULL;
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create city-to-city lane history index on Load")
		}
		log.Info(ctx, "created covering index on Load for city-to-city tier")
	}

	// models.Load{} - Lane History Index - State-to-state tier lookups
	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_lane_history_state") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_lane_history_state
			ON loads (
				service_id,
				pickup_date,
				pickup_state,
				consignee_state,
				specifications_transport_type
			)
			INCLUDE (
				carrier_name,
				ratedata_carrier_total_cost,
				specifications_total_distance
			)
			WHERE pickup_date IS NOT NULL;
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create state-to-state lane history index on Load")
		}
		log.Info(ctx, "created covering index on Load for state-to-state tier")
	}

	// models.Vector{} - Search Index
	// Create the index for vector similarity search
	// Using HNSW index for better search performance (speed-recall tradeoff)
	// Optimized parameters for 1536-dimension embeddings:
	// - m=16: number of connections per layer (default)
	// - ef_construction=128: higher value for better accuracy with high dimensions
	if !WithContext(ctx).Migrator().HasIndex(&models.Vector{}, "idx_vector_embedding") {
		indexSQL := `
		CREATE INDEX IF NOT EXISTS idx_vector_embedding ON vectors USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 128);
		`

		if err := WithContext(ctx).Exec(indexSQL).Error; err != nil {
			return errors.New("failed to create vector index on Vector")
		}
		log.Info(ctx, "created HNSW vector index on Vector")
	}

	// models.Load{} - Poller Index - Find Recently Updated Loads
	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_tms_id_updated_at") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_tms_id_updated_at
			ON loads (tms_id, updated_at DESC);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create tms_id & updated_at index on Load")
		}
		log.Info(ctx, "created index on Load for tms_id & updated_at")
	}

	// models.Load{} - Poller Index - Find Loads with Empty Carrier Cost
	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_tms_id_carrier_cost") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_tms_id_carrier_cost
			ON loads (tms_id, ratedata_carrier_cost)
			WHERE ratedata_carrier_cost IS NULL OR ratedata_carrier_cost = 0;
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create tms_id & carrier_cost partial index on Load")
		}
		log.Info(ctx, "created partial index on Load for tms_id & empty carrier_cost")
	}

	// models.Load{} - Warehouse Lookup Indexes now defined via GORM tags on model

	// models.SuggestedLoadChange{} - Status Index for Smart Suggestions
	if !WithContext(ctx).Migrator().HasIndex(&models.SuggestedLoadChange{}, "idx_suggested_load_changes_status_service") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_suggested_load_changes_status_service
			ON suggested_load_changes (status, service_id)
			WHERE status IN ('accepted', 'pending');
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create status & service_id index on SuggestedLoadChange")
		}
		log.Info(ctx, "created status & service_id index on SuggestedLoadChange")
	}

	// models.QuoteRequest{} - Status Index for Smart Suggestions
	if !WithContext(ctx).Migrator().HasIndex(&models.QuoteRequest{}, "idx_quote_requests_status_service") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_quote_requests_status_service
			ON quote_requests (status, service_id)
			WHERE status IN ('accepted', 'inFlight', 'pending');
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create status & service_id index on QuoteRequest")
		}
		log.Info(ctx, "created status & service_id index on QuoteRequest")
	}

	// models.Vector{} - Service and Content Type Index for Smart Suggestions
	if !WithContext(ctx).Migrator().HasIndex(&models.Vector{}, "idx_vectors_service_content_similarity") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_vectors_service_content_similarity
			ON vectors (service_id, content_type)
			WHERE service_id IS NOT NULL AND content_type IS NOT NULL;
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create service_id & content_type index on Vector")
		}
		log.Info(ctx, "created service_id & content_type index on Vector")
	}

	if !WithContext(ctx).Migrator().HasIndex(&models.Load{}, "idx_load_customer_name") {
		query := `
			CREATE INDEX IF NOT EXISTS idx_load_customer_name 
			ON loads (customer_name);
		`
		if err := WithContext(ctx).Exec(query).Error; err != nil {
			return errors.New("failed to create customer_name index on Load")
		}
		log.Info(ctx, "created index on Load for customer_name")
	}

	return nil
}
