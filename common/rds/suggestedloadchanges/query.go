package suggestedloadchanges

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetSuggestionByID(ctx context.Context, id uint) (*models.SuggestedLoadChange, error) {
	var suggestion models.SuggestedLoadChange

	if err := rds.WithContextReader(ctx).Where("id = ?", id).First(&suggestion).Error; err != nil {
		return nil, err
	}

	return &suggestion, nil
}

func GetSuggestionsByThreadIDAndLoadID(ctx context.Context,
	threadID string, loadID uint) ([]models.SuggestedLoadChange, error) {
	var suggestions []models.SuggestedLoadChange

	err := rds.WithContextReader(ctx).
		Where("thread_id = ? AND load_id = ?", threadID, loadID).
		Preload("Email").Limit(10).Find(&suggestions).Error
	if err != nil {
		return nil, err
	}

	return suggestions, nil
}

func GetSuggestionsByLoadID(ctx context.Context, loadID uint) ([]models.SuggestedLoadChange, error) {
	var suggestions []models.SuggestedLoadChange

	err := rds.WithContextReader(ctx).
		Where("load_id = ?", loadID).
		Preload("Email").Limit(10).
		Find(&suggestions).
		Error

	if err != nil {
		return nil, err
	}

	return suggestions, nil
}

func GetSuggestionsByThreadIDAndCategory(
	ctx context.Context,
	threadID string,
	category string,
) (suggestions []models.SuggestedLoadChange, err error) {

	subQuery := rds.WithContextReader(ctx).
		Model(&models.Email{}).
		Select("id").
		Where("thread_id = ?", threadID)

	err = rds.WithContextReader(ctx).
		Where("email_id IN (?)", subQuery).
		Where("category = ?", category).
		Limit(10).
		Find(&suggestions).Error

	return suggestions, err
}

func GetSuggestedLoadChangesByThreadID(
	ctx context.Context, threadID string,
) (res models.SuggestedLoadChange, err error) {
	return res, rds.WithContext(ctx).Preload(clause.Associations).First(&res, "thread_id = ?", threadID).Error
}

// NOTE: Do *not* use this in API to get suggestions as Drumkit sidebar only reliably has thread context,
// not the specific email the user is viewing.
func GetSuggestedLoadChangesByEmailIDPreload(
	ctx context.Context, emailID uint,
) (res []models.SuggestedLoadChange, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("email_id = ?", emailID).
		Find(&res).Error
}

// NOTE: Do *not* use this in API to get suggestions as Drumkit sidebar only reliably has thread context,
// not the specific email the user is viewing.
// Must have this separate function as we need to explicitly load vectors as clause.Associations
// doesn't handle many2many associations.
func GetSuggestedLoadChangesByEmailIDPreloadWithVectors(
	ctx context.Context, emailID uint,
) (res []models.SuggestedLoadChange, err error) {
	return res, rds.WithContext(ctx).
		Preload(clause.Associations).
		Preload("Vectors").
		Where("email_id = ?", emailID).
		Find(&res).Error
}

func GetLoadBuildingSuggestionsByEmail(
	ctx context.Context, emailID uint,
) (res models.SuggestedLoadChange, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		First(&res, "email_id = ? AND pipeline = ?", emailID, models.LoadBuildingPipeline).
		Error
}

func GetLoadBuildingSuggestionsByThreadID(
	ctx context.Context, threadID string,
) (res []models.SuggestedLoadChange, err error) {
	return res, rds.WithContextReader(ctx).
		Preload(clause.Associations).
		Where("thread_id = ?", threadID).
		Where("pipeline = ? AND status = 'pending'", models.LoadBuildingPipeline).
		Limit(10).
		Order("id DESC").
		Find(&res).Error
}
