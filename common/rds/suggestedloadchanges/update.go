package suggestedloadchanges

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// UpdateSuggestion updates a suggestion in the database.
func UpdateSuggestion(ctx context.Context, suggestion *models.SuggestedLoadChange) error {
	return rds.WithContext(ctx).
		Clauses(
			clause.Locking{
				Strength: "UPDATE",
				Options:  "NOWAIT",
			},
		).
		Save(suggestion).Error
}
