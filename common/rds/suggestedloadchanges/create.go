package suggestedloadchanges

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Create(ctx context.Context, suggestion *models.SuggestedLoadChange) error {
	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.Clauses(clause.Returning{}).Create(suggestion).Error
		if err != nil {
			return err
		}
		// Back-references load suggestion foreign key in quote request record
		if suggestion.QuoteRequestSuggestion != nil {
			suggestion.QuoteRequestSuggestion.LoadSuggestionID = &suggestion.ID
			err = tx.Clauses(
				clause.Locking{
					Strength: "UPDATE",
					Options:  "NOWAIT",
				}).
				Save(suggestion.QuoteRequestSuggestion).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
