package vector

// Unit tests for Quote Request Case-Based Reasoning (QR CBR) functionality.

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

// Helper function to create test QuoteRequest
func createTestQuoteRequest() *models.QuoteRequest {
	return &models.QuoteRequest{
		SuggestedRequest: models.QuoteLoadInfo{
			CustomerID:    1,
			TransportType: models.VanTransportType,
			Customer: models.CompanyCoreInfo{
				Name: "Test Customer",
			},
			PickupLocation: models.Address{
				City:  "Chicago",
				State: "IL",
				Zip:   "60601",
			},
			DeliveryLocation: models.Address{
				City:  "Detroit",
				State: "MI",
				Zip:   "48201",
			},
		},
		AppliedRequest: models.QuoteLoadInfo{
			CustomerID:    2,
			TransportType: models.FlatbedTransportType,
			Customer: models.CompanyCoreInfo{
				Name: "Applied Customer",
			},
			PickupLocation: models.Address{
				City:  "Milwaukee",
				State: "WI",
				Zip:   "53201",
			},
			DeliveryLocation: models.Address{
				City:  "Cleveland",
				State: "OH",
				Zip:   "44101",
			},
		},
	}
}

// Helper function to create similar quote requests for testing
func createSimilarQuoteRequests() []models.Suggestion {
	qr1 := &models.QuoteRequest{
		SuggestedRequest: models.QuoteLoadInfo{
			CustomerID:    1,
			TransportType: models.VanTransportType,
		},
		AppliedRequest: models.QuoteLoadInfo{
			CustomerID:    2,
			TransportType: models.FlatbedTransportType,
		},
	}

	qr2 := &models.QuoteRequest{
		SuggestedRequest: models.QuoteLoadInfo{
			CustomerID:    3,
			TransportType: models.VanTransportType,
		},
		AppliedRequest: models.QuoteLoadInfo{
			CustomerID:    2,
			TransportType: models.FlatbedTransportType,
		},
	}

	return []models.Suggestion{qr1, qr2}
}

func TestApplyQRCBR_Success(t *testing.T) {
	se := &SuggestionEnhancer{
		vectorRepo: &Repository{db: nil},
	}

	currentQR := createTestQuoteRequest()
	similarSuggestions := createSimilarQuoteRequests()

	err := se.applyQRCBR(context.Background(), currentQR, similarSuggestions)

	assert.NoError(t, err)
}

func TestApplyQRCBR_InvalidSuggestionType(t *testing.T) {
	se := &SuggestionEnhancer{
		vectorRepo: &Repository{db: nil},
	}

	// Use a non-QuoteRequest suggestion
	invalidSuggestion := &models.SuggestedLoadChange{}
	similarSuggestions := createSimilarQuoteRequests()

	err := se.applyQRCBR(context.Background(), invalidSuggestion, similarSuggestions)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "current suggestion is not a QuoteRequest")
}

func TestAnalyzeSimilarSuggestionEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	similarSuggestions := createSimilarQuoteRequests()

	analysis := se.analyzeSimilarSuggestionEdits(similarSuggestions)

	assert.NotNil(t, analysis)
	assert.Greater(t, len(analysis.CustomerEdits), 0)
	assert.Greater(t, len(analysis.TransportTypeEdits), 0)
}

func TestAnalyzeCustomerEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	qr := createTestQuoteRequest()
	se.analyzeCustomerEdits(qr, editCounts)

	assert.Equal(t, 1, len(editCounts))
	edit := editCounts["customerID_1_to_2"]
	assert.Equal(t, "customerID", edit.FieldName)
	assert.Equal(t, uint(1), edit.SuggestedValue)
	assert.Equal(t, uint(2), edit.AppliedValue)
	assert.Equal(t, 1, edit.Frequency)
}

func TestAnalyzeLocationEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	qr := createTestQuoteRequest()
	se.analyzePickupLocationEdits(qr, editCounts)

	// Should have 3 edits: city, state, and zip
	assert.Equal(t, 3, len(editCounts))
	assert.Contains(t, editCounts, "pickupCity_Chicago_to_Milwaukee")
	assert.Contains(t, editCounts, "pickupState_IL_to_WI")
	assert.Contains(t, editCounts, "pickupZip_60601_to_53201")
}

func TestAnalyzeLocationEdits_CaseInsensitive(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	qr := createTestQuoteRequest()
	// Make suggested values different case but same content
	qr.SuggestedRequest.PickupLocation.City = "chicago"
	qr.SuggestedRequest.PickupLocation.State = "il"
	qr.SuggestedRequest.PickupLocation.Zip = "60601"
	qr.AppliedRequest.PickupLocation.City = "CHICAGO"
	qr.AppliedRequest.PickupLocation.State = "IL"
	qr.AppliedRequest.PickupLocation.Zip = "60601"

	se.analyzePickupLocationEdits(qr, editCounts)

	// Should not create edits for case-only differences
	assert.Equal(t, 0, len(editCounts))
}

func TestAnalyzeLocationEdits_IgnoresEmptyAppliedValues(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	qr := createTestQuoteRequest()
	qr.AppliedRequest.PickupLocation = models.Address{} // All empty

	se.analyzePickupLocationEdits(qr, editCounts)

	// Should not create edits when applied values are empty
	assert.Equal(t, 0, len(editCounts))
}

func TestCalculateEditWeights(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := map[string]*FieldEdit{
		"edit1": {Frequency: 3},
		"edit2": {Frequency: 7},
	}

	se.calculateEditWeights(editCounts, 10)

	assert.Equal(t, 0.3, editCounts["edit1"].Weight)
	assert.Equal(t, 0.7, editCounts["edit2"].Weight)
}

func TestShouldApplyEdit(t *testing.T) {
	se := &SuggestionEnhancer{}

	tests := []struct {
		name     string
		edit     *FieldEdit
		expected bool
	}{
		{"meets thresholds", &FieldEdit{Weight: 0.4, Frequency: 3}, true},
		{"weight too low", &FieldEdit{Weight: 0.2, Frequency: 3}, false},
		{"frequency too low", &FieldEdit{Weight: 0.4, Frequency: 1}, false},
		{"at threshold", &FieldEdit{Weight: 0.3, Frequency: 2}, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := se.shouldApplyEdit(tt.edit)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestApplyCustomerEdit_InvalidAppliedValue(t *testing.T) {
	se := &SuggestionEnhancer{vectorRepo: &Repository{db: nil}}

	currentQR := createTestQuoteRequest()
	edit := &FieldEdit{
		FieldName:    "customerID",
		AppliedValue: "invalid", // Should be uint
	}

	err := se.applyCustomerEdit(context.Background(), currentQR, edit)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid applied customer ID")
}

func TestApplyCustomerEdit_NilVectorRepo(t *testing.T) {
	se := &SuggestionEnhancer{vectorRepo: nil}

	currentQR := createTestQuoteRequest()
	edit := &FieldEdit{
		FieldName:    "customerID",
		AppliedValue: uint(123),
	}

	err := se.applyCustomerEdit(context.Background(), currentQR, edit)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database connection not available")
}

func TestApplyLocationEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentQR := createTestQuoteRequest()

	// Test pickup location edit
	pickupEdit := &FieldEdit{
		FieldName:    "pickupCity",
		AppliedValue: "New York",
	}
	se.applyPickupLocationEdit(currentQR, pickupEdit)
	assert.Equal(t, "New York", currentQR.SuggestedRequest.PickupLocation.City)

	// Test dropoff location edit
	dropoffEdit := &FieldEdit{
		FieldName:    "dropoffState",
		AppliedValue: "CA",
	}
	se.applyDropoffLocationEdit(currentQR, dropoffEdit)
	assert.Equal(t, "CA", currentQR.SuggestedRequest.DeliveryLocation.State)
}

func TestApplyTransportTypeEdit(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentQR := createTestQuoteRequest()

	// Test with TransportType value
	edit := &FieldEdit{
		FieldName:    "transportType",
		AppliedValue: models.ReeferTransportType,
	}
	se.applyTransportTypeEdit(currentQR, edit)
	assert.Equal(t, models.ReeferTransportType, currentQR.SuggestedRequest.TransportType)

	// Test with string value
	edit2 := &FieldEdit{
		FieldName:    "transportType",
		AppliedValue: "FLATBED",
	}
	se.applyTransportTypeEdit(currentQR, edit2)
	assert.Equal(t, models.TransportType("FLATBED"), currentQR.SuggestedRequest.TransportType)
}

func TestAnalyzeSimilarSuggestionEdits_SkipsNonQuoteRequests(t *testing.T) {
	se := &SuggestionEnhancer{}

	// Mix of QuoteRequest and other suggestion types
	similarSuggestions := []models.Suggestion{
		createTestQuoteRequest(),
		&models.SuggestedLoadChange{}, // This should be skipped
		createTestQuoteRequest(),
	}

	analysis := se.analyzeSimilarSuggestionEdits(similarSuggestions)

	assert.NotNil(t, analysis)
	// Should process only the QuoteRequest suggestions
}
