package vector

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// QRCBRAnalysis contains the results of case-based reasoning analysis for quote requests
type QRCBRAnalysis struct {
	CustomerEdits        []*FieldEdit
	PickupLocationEdits  []*FieldEdit
	DropoffLocationEdits []*FieldEdit
	TransportTypeEdits   []*FieldEdit
}

// applyCaseBasedReasoning analyzes similar suggestions and applies consistent edits
// to the current working suggestion for quote requests only.
func (se *SuggestionEnhancer) applyQRCBR(
	ctx context.Context,
	currentSuggestion models.Suggestion,
	similarSuggestions []models.Suggestion,
) error {
	currentQR, ok := currentSuggestion.(*models.QuoteRequest)
	if !ok {
		return errors.New("current suggestion is not a QuoteRequest")
	}

	analysis := se.analyzeSimilarSuggestionEdits(similarSuggestions)

	editsApplied := 0

	for _, edit := range analysis.CustomerEdits {
		if se.shouldApplyEdit(edit) {
			err := se.applyCustomerEdit(ctx, currentQR, edit)
			if err != nil {
				log.Warn(ctx, "failed to apply customer edit",
					zap.String("fieldName", edit.FieldName),
					zap.Error(err))
			} else {
				editsApplied++
			}
		}
	}

	for _, edit := range analysis.PickupLocationEdits {
		if se.shouldApplyEdit(edit) {
			se.applyPickupLocationEdit(currentQR, edit)
			editsApplied++
		}
	}

	for _, edit := range analysis.DropoffLocationEdits {
		if se.shouldApplyEdit(edit) {
			se.applyDropoffLocationEdit(currentQR, edit)
			editsApplied++
		}
	}

	for _, edit := range analysis.TransportTypeEdits {
		if se.shouldApplyEdit(edit) {
			se.applyTransportTypeEdit(currentQR, edit)
			editsApplied++
		}
	}

	log.Info(ctx, "CBR analysis completed",
		zap.Int("editsApplied", editsApplied),
		zap.Int("customerEdits", len(analysis.CustomerEdits)),
		zap.Int("pickupLocationEdits", len(analysis.PickupLocationEdits)),
		zap.Int("dropoffLocationEdits", len(analysis.DropoffLocationEdits)),
		zap.Int("transportTypeEdits", len(analysis.TransportTypeEdits)))

	return nil
}

// analyzeSimilarSuggestionEdits analyzes edits across similar suggestions to identify patterns
func (se *SuggestionEnhancer) analyzeSimilarSuggestionEdits(
	similarSuggestions []models.Suggestion,
) *QRCBRAnalysis {

	customerEditCounts := make(map[string]*FieldEdit, 10)
	pickupLocationEditCounts := make(map[string]*FieldEdit, 30)
	dropoffLocationEditCounts := make(map[string]*FieldEdit, 30)
	transportTypeEditCounts := make(map[string]*FieldEdit, 10)

	totalSuggestions := len(similarSuggestions)

	for _, suggestion := range similarSuggestions {
		qr, ok := suggestion.(*models.QuoteRequest)
		if !ok {
			continue
		}

		if qr.AppliedRequest.CustomerID == 0 &&
			qr.AppliedRequest.PickupLocation.City == "" &&
			qr.AppliedRequest.DeliveryLocation.City == "" {
			continue
		}

		se.analyzeCustomerEdits(qr, customerEditCounts)

		se.analyzePickupLocationEdits(qr, pickupLocationEditCounts)

		se.analyzeDropoffLocationEdits(qr, dropoffLocationEditCounts)

		se.analyzeTransportTypeEdits(qr, transportTypeEditCounts)
	}

	se.calculateEditWeights(customerEditCounts, totalSuggestions)
	se.calculateEditWeights(pickupLocationEditCounts, totalSuggestions)
	se.calculateEditWeights(dropoffLocationEditCounts, totalSuggestions)
	se.calculateEditWeights(transportTypeEditCounts, totalSuggestions)

	analysis := &QRCBRAnalysis{
		CustomerEdits:        make([]*FieldEdit, 0, len(customerEditCounts)),
		PickupLocationEdits:  make([]*FieldEdit, 0, len(pickupLocationEditCounts)),
		DropoffLocationEdits: make([]*FieldEdit, 0, len(dropoffLocationEditCounts)),
		TransportTypeEdits:   make([]*FieldEdit, 0, len(transportTypeEditCounts)),
	}

	for _, edit := range customerEditCounts {
		analysis.CustomerEdits = append(analysis.CustomerEdits, edit)
	}
	for _, edit := range pickupLocationEditCounts {
		analysis.PickupLocationEdits = append(analysis.PickupLocationEdits, edit)
	}
	for _, edit := range dropoffLocationEditCounts {
		analysis.DropoffLocationEdits = append(analysis.DropoffLocationEdits, edit)
	}
	for _, edit := range transportTypeEditCounts {
		analysis.TransportTypeEdits = append(analysis.TransportTypeEdits, edit)
	}

	return analysis
}

// analyzeCustomerEdits compares suggested vs applied customer fields
func (se *SuggestionEnhancer) analyzeCustomerEdits(
	qr *models.QuoteRequest,
	editCounts map[string]*FieldEdit,
) {
	if qr.SuggestedRequest.CustomerID != qr.AppliedRequest.CustomerID && qr.AppliedRequest.CustomerID != 0 {
		editKey := fmt.Sprintf(
			"customerID_%d_to_%d",
			qr.SuggestedRequest.CustomerID,
			qr.AppliedRequest.CustomerID,
		)
		if edit, exists := editCounts[editKey]; exists {
			edit.Frequency++
		} else {
			editCounts[editKey] = &FieldEdit{
				FieldName:      "customerID",
				SuggestedValue: qr.SuggestedRequest.CustomerID,
				AppliedValue:   qr.AppliedRequest.CustomerID,
				Frequency:      1,
			}
		}
	}
}

// analyzePickupLocationEdits compares suggested vs applied pickup location fields
func (se *SuggestionEnhancer) analyzePickupLocationEdits(
	qr *models.QuoteRequest,
	editCounts map[string]*FieldEdit,
) {
	if !strings.EqualFold(
		qr.SuggestedRequest.PickupLocation.City,
		qr.AppliedRequest.PickupLocation.City,
	) && qr.AppliedRequest.PickupLocation.City != "" {

		editKey := fmt.Sprintf(
			"pickupCity_%s_to_%s",
			qr.SuggestedRequest.PickupLocation.City,
			qr.AppliedRequest.PickupLocation.City,
		)
		se.incrementEditCount(
			editCounts,
			editKey,
			"pickupCity",
			qr.SuggestedRequest.PickupLocation.City,
			qr.AppliedRequest.PickupLocation.City,
		)
	}

	if !strings.EqualFold(
		qr.SuggestedRequest.PickupLocation.State,
		qr.AppliedRequest.PickupLocation.State,
	) && qr.AppliedRequest.PickupLocation.State != "" {

		editKey := fmt.Sprintf(
			"pickupState_%s_to_%s",
			qr.SuggestedRequest.PickupLocation.State,
			qr.AppliedRequest.PickupLocation.State,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"pickupState",
			qr.SuggestedRequest.PickupLocation.State,
			qr.AppliedRequest.PickupLocation.State,
		)
	}

	if !strings.EqualFold(
		qr.SuggestedRequest.PickupLocation.Zip,
		qr.AppliedRequest.PickupLocation.Zip,
	) && qr.AppliedRequest.PickupLocation.Zip != "" {

		editKey := fmt.Sprintf(
			"pickupZip_%s_to_%s",
			qr.SuggestedRequest.PickupLocation.Zip,
			qr.AppliedRequest.PickupLocation.Zip,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"pickupZip",
			qr.SuggestedRequest.PickupLocation.Zip,
			qr.AppliedRequest.PickupLocation.Zip,
		)
	}
}

// analyzeDropoffLocationEdits compares suggested vs applied dropoff location fields
func (se *SuggestionEnhancer) analyzeDropoffLocationEdits(
	qr *models.QuoteRequest,
	editCounts map[string]*FieldEdit,
) {
	if !strings.EqualFold(
		qr.SuggestedRequest.DeliveryLocation.City,
		qr.AppliedRequest.DeliveryLocation.City,
	) && qr.AppliedRequest.DeliveryLocation.City != "" {

		editKey := fmt.Sprintf(
			"dropoffCity_%s_to_%s",
			qr.SuggestedRequest.DeliveryLocation.City,
			qr.AppliedRequest.DeliveryLocation.City,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"dropoffCity",
			qr.SuggestedRequest.DeliveryLocation.City,
			qr.AppliedRequest.DeliveryLocation.City,
		)
	}

	if !strings.EqualFold(
		qr.SuggestedRequest.DeliveryLocation.State,
		qr.AppliedRequest.DeliveryLocation.State,
	) && qr.AppliedRequest.DeliveryLocation.State != "" {

		editKey := fmt.Sprintf(
			"dropoffState_%s_to_%s",
			qr.SuggestedRequest.DeliveryLocation.State,
			qr.AppliedRequest.DeliveryLocation.State,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"dropoffState",
			qr.SuggestedRequest.DeliveryLocation.State,
			qr.AppliedRequest.DeliveryLocation.State,
		)
	}

	if !strings.EqualFold(
		qr.SuggestedRequest.DeliveryLocation.Zip,
		qr.AppliedRequest.DeliveryLocation.Zip,
	) && qr.AppliedRequest.DeliveryLocation.Zip != "" {

		editKey := fmt.Sprintf(
			"dropoffZip_%s_to_%s",
			qr.SuggestedRequest.DeliveryLocation.Zip,
			qr.AppliedRequest.DeliveryLocation.Zip,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"dropoffZip",
			qr.SuggestedRequest.DeliveryLocation.Zip,
			qr.AppliedRequest.DeliveryLocation.Zip,
		)
	}
}

// analyzeTransportTypeEdits compares suggested vs applied transport type fields
func (se *SuggestionEnhancer) analyzeTransportTypeEdits(
	qr *models.QuoteRequest,
	editCounts map[string]*FieldEdit,
) {
	if !strings.EqualFold(
		string(qr.SuggestedRequest.TransportType),
		string(qr.AppliedRequest.TransportType),
	) && qr.AppliedRequest.TransportType != "" {

		editKey := fmt.Sprintf(
			"transportType_%s_to_%s",
			qr.SuggestedRequest.TransportType,
			qr.AppliedRequest.TransportType,
		)
		se.incrementEditCount(
			editCounts,
			editKey,
			"transportType",
			qr.SuggestedRequest.TransportType,
			qr.AppliedRequest.TransportType,
		)
	}
}

// incrementEditCount is a helper to increment edit counts
func (se *SuggestionEnhancer) incrementEditCount(
	editCounts map[string]*FieldEdit,
	editKey, fieldName string,
	suggestedValue, appliedValue any,
) {
	if edit, exists := editCounts[editKey]; exists {
		edit.Frequency++
	} else {
		editCounts[editKey] = &FieldEdit{
			FieldName:      fieldName,
			SuggestedValue: suggestedValue,
			AppliedValue:   appliedValue,
			Frequency:      1,
		}
	}
}

// calculateEditWeights calculates weights based on frequency and total suggestions
func (se *SuggestionEnhancer) calculateEditWeights(
	editCounts map[string]*FieldEdit,
	totalSuggestions int,
) {
	for _, edit := range editCounts {
		// Weight is the frequency as a percentage of total suggestions
		edit.Weight = float64(edit.Frequency) / float64(totalSuggestions)
	}
}

// shouldApplyEdit determines if an edit should be applied based on frequency and weight thresholds
func (se *SuggestionEnhancer) shouldApplyEdit(edit *FieldEdit) bool {
	// Apply edit if it occurs in at least 30% of similar suggestions AND at least 2 times
	return edit.Weight >= 0.3 && edit.Frequency >= 2
}

// applyCustomerEdit applies customer-related edits to the current suggestion
func (se *SuggestionEnhancer) applyCustomerEdit(
	ctx context.Context,
	currentQR *models.QuoteRequest,
	edit *FieldEdit,
) error {
	if edit.FieldName == "customerID" {
		appliedCustomerID, ok := edit.AppliedValue.(uint)
		if !ok || appliedCustomerID == 0 {
			return errors.New("invalid applied customer ID")
		}

		if se.vectorRepo == nil || se.vectorRepo.db == nil {
			return errors.New("database connection not available")
		}

		// The full customer must be updated, not just the ID.
		var customer models.TMSCustomer
		err := se.vectorRepo.db.WithContext(ctx).
			Model(&models.TMSCustomer{}).
			Where("id = ?", appliedCustomerID).
			First(&customer).Error
		if err != nil {
			return fmt.Errorf("failed to fetch company core info: %w", err)
		}

		currentQR.SuggestedRequest.CustomerID = appliedCustomerID
		currentQR.SuggestedRequest.Customer = customer.CompanyCoreInfo

		log.Info(ctx, "applied customer edit",
			zap.Uint("customerID", appliedCustomerID),
			zap.Float64("weight", edit.Weight),
			zap.Int("frequency", edit.Frequency))
	}

	return nil
}

// applyPickupLocationEdit applies pickup location edits to the current suggestion
func (se *SuggestionEnhancer) applyPickupLocationEdit(currentQR *models.QuoteRequest, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return
	}

	switch edit.FieldName {
	case "pickupCity":
		currentQR.SuggestedRequest.PickupLocation.City = appliedValue
	case "pickupState":
		currentQR.SuggestedRequest.PickupLocation.State = appliedValue
	case "pickupZip":
		currentQR.SuggestedRequest.PickupLocation.Zip = appliedValue
	}
}

// applyDropoffLocationEdit applies dropoff location edits to the current suggestion
func (se *SuggestionEnhancer) applyDropoffLocationEdit(currentQR *models.QuoteRequest, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return
	}

	switch edit.FieldName {
	case "dropoffCity":
		currentQR.SuggestedRequest.DeliveryLocation.City = appliedValue
	case "dropoffState":
		currentQR.SuggestedRequest.DeliveryLocation.State = appliedValue
	case "dropoffZip":
		currentQR.SuggestedRequest.DeliveryLocation.Zip = appliedValue
	}
}

// applyTransportTypeEdit applies transport type edits to the current suggestion
func (se *SuggestionEnhancer) applyTransportTypeEdit(currentQR *models.QuoteRequest, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(models.TransportType)
	if !ok {
		appliedValueStr, strOk := edit.AppliedValue.(string)
		if !strOk || appliedValueStr == "" {
			return
		}
		appliedValue = models.TransportType(appliedValueStr)
	}

	if appliedValue == "" {
		return
	}

	currentQR.SuggestedRequest.TransportType = appliedValue
}
