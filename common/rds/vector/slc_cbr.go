package vector

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// SLCCBRAnalysis contains the results of case-based reasoning analysis for suggested load changes
type SLCCBRAnalysis struct {
	ModeEdits                   []*FieldEdit
	CustomerExternalTMSIDEdits  []*FieldEdit
	RateDataEdits               []*FieldEdit
	PickupExternalTMSIDEdits    []*FieldEdit
	ConsigneeExternalTMSIDEdits []*FieldEdit
	SpecificationEdits          []*FieldEdit
}

// applySLCCBR analyzes similar suggestions and applies consistent edits
// to the current working suggestion for suggested load changes.
func (se *SuggestionEnhancer) applySLCCBR(
	ctx context.Context,
	tmsID uint,
	currentSuggestion models.Suggestion,
	similarSuggestions []models.Suggestion,
) error {
	currentSLC, ok := currentSuggestion.(*models.SuggestedLoadChange)
	if !ok {
		return fmt.Errorf("current suggestion is not a SuggestedLoadChange: %T", currentSuggestion)
	}

	if currentSLC.Suggested.LoadChanges == nil {
		log.Info(ctx, "no load changes data found in suggested field, skipping SLC CBR")
		return nil
	}

	analysis := se.analyzeSimilarLoadChangeEdits(similarSuggestions)

	editsApplied := 0

	for _, edit := range analysis.ModeEdits {
		if se.shouldApplyEdit(edit) {
			se.applyModeEdit(currentSLC, edit)
			editsApplied++
		}
	}

	for _, edit := range analysis.CustomerExternalTMSIDEdits {
		if se.shouldApplyEdit(edit) {
			err := se.applyCustomerExternalTMSIDEdit(ctx, tmsID, currentSLC, edit)
			if err != nil {
				log.Warn(ctx, "failed to apply customer external TMS ID edit",
					zap.String("fieldName", edit.FieldName),
					zap.Error(err))
			} else {
				editsApplied++
			}
		}
	}

	for _, edit := range analysis.RateDataEdits {
		if se.shouldApplyEdit(edit) {
			se.applyRateDataEdit(currentSLC, edit)
			editsApplied++
		}
	}

	for _, edit := range analysis.PickupExternalTMSIDEdits {
		if se.shouldApplyEdit(edit) {
			err := se.applyPickupExternalTMSIDEdit(ctx, tmsID, currentSLC, edit)
			if err != nil {
				log.Warn(ctx, "failed to apply pickup external TMS ID edit",
					zap.String("fieldName", edit.FieldName),
					zap.Error(err))
			} else {
				editsApplied++
			}
		}
	}

	for _, edit := range analysis.ConsigneeExternalTMSIDEdits {
		if se.shouldApplyEdit(edit) {
			err := se.applyConsigneeExternalTMSIDEdit(ctx, tmsID, currentSLC, edit)
			if err != nil {
				log.Warn(ctx, "failed to apply consignee external TMS ID edit",
					zap.String("fieldName", edit.FieldName),
					zap.Error(err))
			} else {
				editsApplied++
			}
		}
	}

	for _, edit := range analysis.SpecificationEdits {
		if se.shouldApplyEdit(edit) {
			se.applySpecificationEdit(currentSLC, edit)
			editsApplied++
		}
	}

	log.Info(ctx, "SLC CBR analysis completed",
		zap.Int("editsApplied", editsApplied),
		zap.Int("modeEdits", len(analysis.ModeEdits)),
		zap.Int("customerExternalTMSIDEdits", len(analysis.CustomerExternalTMSIDEdits)),
		zap.Int("rateDataEdits", len(analysis.RateDataEdits)),
		zap.Int("pickupExternalTMSIDEdits", len(analysis.PickupExternalTMSIDEdits)),
		zap.Int("consigneeExternalTMSIDEdits", len(analysis.ConsigneeExternalTMSIDEdits)),
		zap.Int("specificationEdits", len(analysis.SpecificationEdits)))

	return nil
}

// analyzeSimilarLoadChangeEdits analyzes edits across similar load change suggestions to identify patterns
func (se *SuggestionEnhancer) analyzeSimilarLoadChangeEdits(
	similarSuggestions []models.Suggestion,
) *SLCCBRAnalysis {

	modeEditCounts := make(map[string]*FieldEdit, 10)
	customerExternalTMSIDEditCounts := make(map[string]*FieldEdit, 20)
	rateDataEditCounts := make(map[string]*FieldEdit, 20)
	pickupExternalTMSIDEditCounts := make(map[string]*FieldEdit, 20)
	consigneeExternalTMSIDEditCounts := make(map[string]*FieldEdit, 20)
	specificationEditCounts := make(map[string]*FieldEdit, 30)

	totalSuggestions := len(similarSuggestions)

	for _, suggestion := range similarSuggestions {
		slc, ok := suggestion.(*models.SuggestedLoadChange)
		if !ok {
			continue
		}

		if slc.Suggested.LoadChanges == nil || slc.Applied.LoadChanges == nil {
			continue
		}

		if se.isEmptyLoadChanges(slc.Applied.LoadChanges) {
			continue
		}

		se.analyzeModeEdits(slc, modeEditCounts)
		se.analyzeCustomerExternalTMSIDEdits(slc, customerExternalTMSIDEditCounts)
		se.analyzeRateDataEdits(slc, rateDataEditCounts)
		se.analyzePickupExternalTMSIDEdits(slc, pickupExternalTMSIDEditCounts)
		se.analyzeConsigneeExternalTMSIDEdits(slc, consigneeExternalTMSIDEditCounts)
		se.analyzeSpecificationEdits(slc, specificationEditCounts)
	}

	se.calculateEditWeights(modeEditCounts, totalSuggestions)
	se.calculateEditWeights(customerExternalTMSIDEditCounts, totalSuggestions)
	se.calculateEditWeights(rateDataEditCounts, totalSuggestions)
	se.calculateEditWeights(pickupExternalTMSIDEditCounts, totalSuggestions)
	se.calculateEditWeights(consigneeExternalTMSIDEditCounts, totalSuggestions)
	se.calculateEditWeights(specificationEditCounts, totalSuggestions)

	analysis := &SLCCBRAnalysis{
		ModeEdits:                   make([]*FieldEdit, 0, len(modeEditCounts)),
		CustomerExternalTMSIDEdits:  make([]*FieldEdit, 0, len(customerExternalTMSIDEditCounts)),
		RateDataEdits:               make([]*FieldEdit, 0, len(rateDataEditCounts)),
		PickupExternalTMSIDEdits:    make([]*FieldEdit, 0, len(pickupExternalTMSIDEditCounts)),
		ConsigneeExternalTMSIDEdits: make([]*FieldEdit, 0, len(consigneeExternalTMSIDEditCounts)),
		SpecificationEdits:          make([]*FieldEdit, 0, len(specificationEditCounts)),
	}

	for _, edit := range modeEditCounts {
		analysis.ModeEdits = append(analysis.ModeEdits, edit)
	}
	for _, edit := range customerExternalTMSIDEditCounts {
		analysis.CustomerExternalTMSIDEdits = append(analysis.CustomerExternalTMSIDEdits, edit)
	}
	for _, edit := range rateDataEditCounts {
		analysis.RateDataEdits = append(analysis.RateDataEdits, edit)
	}
	for _, edit := range pickupExternalTMSIDEditCounts {
		analysis.PickupExternalTMSIDEdits = append(analysis.PickupExternalTMSIDEdits, edit)
	}
	for _, edit := range consigneeExternalTMSIDEditCounts {
		analysis.ConsigneeExternalTMSIDEdits = append(analysis.ConsigneeExternalTMSIDEdits, edit)
	}
	for _, edit := range specificationEditCounts {
		analysis.SpecificationEdits = append(analysis.SpecificationEdits, edit)
	}

	return analysis
}

// isEmptyLoadChanges checks if LoadChanges has any meaningful data
func (se *SuggestionEnhancer) isEmptyLoadChanges(lc *models.LoadChanges) bool {
	return lc.Mode == "" &&
		lc.Customer.ExternalTMSID == "" &&
		lc.RateData.CollectionMethod == "" &&
		lc.RateData.CustomerRateType == "" &&
		lc.Pickup.ExternalTMSID == "" &&
		lc.Consignee.ExternalTMSID == "" &&
		lc.Specifications.ServiceType == "" &&
		lc.Specifications.TransportType == "" &&
		lc.Specifications.Commodities == ""
}

// analyzeModeEdits compares suggested vs applied mode fields
func (se *SuggestionEnhancer) analyzeModeEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if slc.Suggested.LoadChanges.Mode != slc.Applied.LoadChanges.Mode && slc.Applied.LoadChanges.Mode != "" {
		editKey := fmt.Sprintf(
			"mode_%s_to_%s",
			slc.Suggested.LoadChanges.Mode,
			slc.Applied.LoadChanges.Mode,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"mode",
			slc.Suggested.LoadChanges.Mode,
			slc.Applied.LoadChanges.Mode,
		)
	}
}

// analyzeCustomerExternalTMSIDEdits compares suggested vs applied customer external TMS ID fields
func (se *SuggestionEnhancer) analyzeCustomerExternalTMSIDEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if slc.Suggested.LoadChanges.Customer.ExternalTMSID != slc.Applied.LoadChanges.Customer.ExternalTMSID &&
		slc.Applied.LoadChanges.Customer.ExternalTMSID != "" {
		editKey := fmt.Sprintf(
			"customerExternalTMSID_%s_to_%s",
			slc.Suggested.LoadChanges.Customer.ExternalTMSID,
			slc.Applied.LoadChanges.Customer.ExternalTMSID,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"customerExternalTMSID",
			slc.Suggested.LoadChanges.Customer.ExternalTMSID,
			slc.Applied.LoadChanges.Customer.ExternalTMSID,
		)
	}
}

// analyzeRateDataEdits compares suggested vs applied rate data fields
func (se *SuggestionEnhancer) analyzeRateDataEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if !strings.EqualFold(
		slc.Suggested.LoadChanges.RateData.CollectionMethod,
		slc.Applied.LoadChanges.RateData.CollectionMethod,
	) && slc.Applied.LoadChanges.RateData.CollectionMethod != "" {

		editKey := fmt.Sprintf(
			"collectionMethod_%s_to_%s",
			slc.Suggested.LoadChanges.RateData.CollectionMethod,
			slc.Applied.LoadChanges.RateData.CollectionMethod,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"collectionMethod",
			slc.Suggested.LoadChanges.RateData.CollectionMethod,
			slc.Applied.LoadChanges.RateData.CollectionMethod,
		)
	}

	if !strings.EqualFold(
		slc.Suggested.LoadChanges.RateData.CustomerRateType,
		slc.Applied.LoadChanges.RateData.CustomerRateType,
	) && slc.Applied.LoadChanges.RateData.CustomerRateType != "" {

		editKey := fmt.Sprintf(
			"customerRateType_%s_to_%s",
			slc.Suggested.LoadChanges.RateData.CustomerRateType,
			slc.Applied.LoadChanges.RateData.CustomerRateType,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"customerRateType",
			slc.Suggested.LoadChanges.RateData.CustomerRateType,
			slc.Applied.LoadChanges.RateData.CustomerRateType,
		)
	}
}

// analyzePickupExternalTMSIDEdits compares suggested vs applied pickup external TMS ID fields
func (se *SuggestionEnhancer) analyzePickupExternalTMSIDEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if slc.Suggested.LoadChanges.Pickup.ExternalTMSID != slc.Applied.LoadChanges.Pickup.ExternalTMSID &&
		slc.Applied.LoadChanges.Pickup.ExternalTMSID != "" {
		editKey := fmt.Sprintf(
			"pickupExternalTMSID_%s_to_%s",
			slc.Suggested.LoadChanges.Pickup.ExternalTMSID,
			slc.Applied.LoadChanges.Pickup.ExternalTMSID,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"pickupExternalTMSID",
			slc.Suggested.LoadChanges.Pickup.ExternalTMSID,
			slc.Applied.LoadChanges.Pickup.ExternalTMSID,
		)
	}
}

// analyzeConsigneeExternalTMSIDEdits compares suggested vs applied consignee external TMS ID fields
func (se *SuggestionEnhancer) analyzeConsigneeExternalTMSIDEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if slc.Suggested.LoadChanges.Consignee.ExternalTMSID != slc.Applied.LoadChanges.Consignee.ExternalTMSID &&
		slc.Applied.LoadChanges.Consignee.ExternalTMSID != "" {
		editKey := fmt.Sprintf(
			"consigneeExternalTMSID_%s_to_%s",
			slc.Suggested.LoadChanges.Consignee.ExternalTMSID,
			slc.Applied.LoadChanges.Consignee.ExternalTMSID,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"consigneeExternalTMSID",
			slc.Suggested.LoadChanges.Consignee.ExternalTMSID,
			slc.Applied.LoadChanges.Consignee.ExternalTMSID,
		)
	}
}

// analyzeSpecificationEdits compares suggested vs applied specification fields
func (se *SuggestionEnhancer) analyzeSpecificationEdits(
	slc *models.SuggestedLoadChange,
	editCounts map[string]*FieldEdit,
) {
	if !strings.EqualFold(
		slc.Suggested.LoadChanges.Specifications.ServiceType,
		slc.Applied.LoadChanges.Specifications.ServiceType,
	) && slc.Applied.LoadChanges.Specifications.ServiceType != "" {

		editKey := fmt.Sprintf(
			"serviceType_%s_to_%s",
			slc.Suggested.LoadChanges.Specifications.ServiceType,
			slc.Applied.LoadChanges.Specifications.ServiceType,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"serviceType",
			slc.Suggested.LoadChanges.Specifications.ServiceType,
			slc.Applied.LoadChanges.Specifications.ServiceType,
		)
	}

	if !strings.EqualFold(
		slc.Suggested.LoadChanges.Specifications.TransportType,
		slc.Applied.LoadChanges.Specifications.TransportType,
	) && slc.Applied.LoadChanges.Specifications.TransportType != "" {

		editKey := fmt.Sprintf(
			"transportType_%s_to_%s",
			slc.Suggested.LoadChanges.Specifications.TransportType,
			slc.Applied.LoadChanges.Specifications.TransportType,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"transportType",
			slc.Suggested.LoadChanges.Specifications.TransportType,
			slc.Applied.LoadChanges.Specifications.TransportType,
		)
	}

	if !strings.EqualFold(
		slc.Suggested.LoadChanges.Specifications.Commodities,
		slc.Applied.LoadChanges.Specifications.Commodities,
	) && slc.Applied.LoadChanges.Specifications.Commodities != "" {

		editKey := fmt.Sprintf(
			"commodities_%s_to_%s",
			slc.Suggested.LoadChanges.Specifications.Commodities,
			slc.Applied.LoadChanges.Specifications.Commodities,
		)

		se.incrementEditCount(
			editCounts,
			editKey,
			"commodities",
			slc.Suggested.LoadChanges.Specifications.Commodities,
			slc.Applied.LoadChanges.Specifications.Commodities,
		)
	}
}

// applyModeEdit applies mode edits to the current suggestion
func (se *SuggestionEnhancer) applyModeEdit(currentSLC *models.SuggestedLoadChange, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(models.LoadMode)
	if !ok {
		appliedValueStr, strOk := edit.AppliedValue.(string)
		if !strOk || appliedValueStr == "" {
			return
		}
		appliedValue = models.LoadMode(appliedValueStr)
	}

	if appliedValue == "" {
		return
	}

	currentSLC.Suggested.LoadChanges.Mode = appliedValue
}

// applyCustomerExternalTMSIDEdit applies customer external TMS ID edits to the current suggestion
func (se *SuggestionEnhancer) applyCustomerExternalTMSIDEdit(
	ctx context.Context,
	tmsID uint,
	currentSLC *models.SuggestedLoadChange,
	edit *FieldEdit,
) error {

	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return errors.New("applied value is not a string")
	}

	if se.vectorRepo == nil || se.vectorRepo.db == nil {
		return errors.New("database connection not available")
	}

	var customer models.TMSCustomer
	query := se.vectorRepo.db.WithContext(ctx).Model(&models.TMSCustomer{})
	if tmsID != 0 {
		query = query.Where("tms_integration_id = ?", tmsID)
	}
	err := query.Where("external_tms_id = ?", appliedValue).First(&customer).Error
	if err != nil {
		return fmt.Errorf("failed to fetch company core info: %w", err)
	}

	currentSLC.Suggested.LoadChanges.Customer.ExternalTMSID = appliedValue
	currentSLC.Suggested.LoadChanges.Customer.CompanyCoreInfo = customer.CompanyCoreInfo

	return nil
}

// applyRateDataEdit applies rate data edits to the current suggestion
func (se *SuggestionEnhancer) applyRateDataEdit(currentSLC *models.SuggestedLoadChange, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return
	}

	switch edit.FieldName {
	case "collectionMethod":
		currentSLC.Suggested.LoadChanges.RateData.CollectionMethod = appliedValue
	case "customerRateType":
		currentSLC.Suggested.LoadChanges.RateData.CustomerRateType = appliedValue
	}
}

// applyPickupExternalTMSIDEdit applies pickup external TMS ID edits to the current suggestion
func (se *SuggestionEnhancer) applyPickupExternalTMSIDEdit(
	ctx context.Context,
	tmsID uint,
	currentSLC *models.SuggestedLoadChange,
	edit *FieldEdit,
) error {

	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return errors.New("applied value is not a string")
	}

	if se.vectorRepo == nil || se.vectorRepo.db == nil {
		return errors.New("database connection not available")
	}

	var pickup models.TMSLocation
	query := se.vectorRepo.db.WithContext(ctx).Model(&models.TMSLocation{})
	if tmsID != 0 {
		query = query.Where("tms_integration_id = ?", tmsID)
	}
	err := query.Where("external_tms_id = ?", appliedValue).First(&pickup).Error
	if err != nil {
		return fmt.Errorf("failed to fetch pickup: %w", err)
	}

	currentSLC.Suggested.LoadChanges.Pickup.ExternalTMSID = appliedValue
	currentSLC.Suggested.LoadChanges.Pickup.CompanyCoreInfo = pickup.CompanyCoreInfo

	return nil
}

// applyConsigneeExternalTMSIDEdit applies consignee external TMS ID edits to the current suggestion
func (se *SuggestionEnhancer) applyConsigneeExternalTMSIDEdit(
	ctx context.Context,
	tmsID uint,
	currentSLC *models.SuggestedLoadChange,
	edit *FieldEdit,
) error {

	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return errors.New("applied value is not a string")
	}

	if se.vectorRepo == nil || se.vectorRepo.db == nil {
		return errors.New("database connection not available")
	}

	var consignee models.TMSLocation
	query := se.vectorRepo.db.WithContext(ctx).Model(&models.TMSLocation{})
	if tmsID != 0 {
		query = query.Where("tms_integration_id = ?", tmsID)
	}
	err := query.Where("external_tms_id = ?", appliedValue).First(&consignee).Error
	if err != nil {
		return fmt.Errorf("failed to fetch consignee: %w", err)
	}

	currentSLC.Suggested.LoadChanges.Consignee.ExternalTMSID = appliedValue
	currentSLC.Suggested.LoadChanges.Consignee.CompanyCoreInfo = consignee.CompanyCoreInfo

	return nil
}

// applySpecificationEdit applies specification edits to the current suggestion
func (se *SuggestionEnhancer) applySpecificationEdit(currentSLC *models.SuggestedLoadChange, edit *FieldEdit) {
	appliedValue, ok := edit.AppliedValue.(string)
	if !ok || appliedValue == "" {
		return
	}

	switch edit.FieldName {
	case "serviceType":
		currentSLC.Suggested.LoadChanges.Specifications.ServiceType = appliedValue
	case "transportType":
		currentSLC.Suggested.LoadChanges.Specifications.TransportType = appliedValue
	case "commodities":
		currentSLC.Suggested.LoadChanges.Specifications.Commodities = appliedValue
	}
}
