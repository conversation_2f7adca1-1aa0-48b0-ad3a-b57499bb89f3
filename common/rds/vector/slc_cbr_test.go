package vector

// Unit tests for Suggested Load Change Case-Based Reasoning (SLCCBR) functionality.

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

// Helper function to create test SuggestedLoadChange
func createTestSuggestedLoadChange() *models.SuggestedLoadChange {
	return &models.SuggestedLoadChange{
		Suggested: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("FTL"),
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Test Customer",
						ExternalTMSID: "CUST001",
					},
				},
				RateData: models.RateData{
					CollectionMethod: "Prepaid",
					CustomerRateType: "Flat",
				},
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Test Pickup",
						ExternalTMSID: "PICKUP001",
					},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Test Consignee",
						ExternalTMSID: "CONSIGNEE001",
					},
				},
				Specifications: models.SuggestedSpecifications{
					ServiceType:   "Standard",
					TransportType: "Van",
					Commodities:   "Electronics",
				},
			},
		},
		Applied: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("LTL"),
				Customer: models.Customer{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Applied Customer",
						ExternalTMSID: "CUST002",
					},
				},
				RateData: models.RateData{
					CollectionMethod: "Collect",
					CustomerRateType: "PerMile",
				},
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Applied Pickup",
						ExternalTMSID: "PICKUP002",
					},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						Name:          "Applied Consignee",
						ExternalTMSID: "CONSIGNEE002",
					},
				},
				Specifications: models.SuggestedSpecifications{
					ServiceType:   "Express",
					TransportType: "Flatbed",
					Commodities:   "Machinery",
				},
			},
		},
	}
}

// Helper function to create similar suggested load changes for testing
func createSimilarSuggestedLoadChanges() []models.Suggestion {
	slc1 := &models.SuggestedLoadChange{
		Suggested: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("FTL"),
			},
		},
		Applied: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("LTL"),
			},
		},
	}

	slc2 := &models.SuggestedLoadChange{
		Suggested: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("FTL"),
			},
		},
		Applied: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{
				Mode: models.LoadMode("LTL"),
			},
		},
	}

	return []models.Suggestion{slc1, slc2}
}

func TestApplySLCCBR_Success(t *testing.T) {
	se := &SuggestionEnhancer{
		vectorRepo: &Repository{db: nil},
	}

	currentSLC := createTestSuggestedLoadChange()
	similarSuggestions := createSimilarSuggestedLoadChanges()

	err := se.applySLCCBR(context.Background(), 123, currentSLC, similarSuggestions)

	assert.NoError(t, err)
}

func TestApplySLCCBR_InvalidSuggestionType(t *testing.T) {
	se := &SuggestionEnhancer{
		vectorRepo: &Repository{db: nil},
	}

	// Use a non-SuggestedLoadChange suggestion
	invalidSuggestion := &models.QuoteRequest{}
	similarSuggestions := createSimilarSuggestedLoadChanges()

	err := se.applySLCCBR(context.Background(), 123, invalidSuggestion, similarSuggestions)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "current suggestion is not a SuggestedLoadChange")
}

func TestApplySLCCBR_NoLoadChangesData(t *testing.T) {
	se := &SuggestionEnhancer{
		vectorRepo: &Repository{db: nil},
	}

	// Create SLC without LoadChanges data
	currentSLC := &models.SuggestedLoadChange{
		Suggested: models.SuggestedChanges{
			LoadChanges: nil,
		},
	}
	similarSuggestions := createSimilarSuggestedLoadChanges()

	err := se.applySLCCBR(context.Background(), 123, currentSLC, similarSuggestions)

	assert.NoError(t, err) // Should return without error but skip processing
}

func TestAnalyzeSimilarLoadChangeEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	similarSuggestions := createSimilarSuggestedLoadChanges()

	analysis := se.analyzeSimilarLoadChangeEdits(similarSuggestions)

	assert.NotNil(t, analysis)
	assert.Greater(t, len(analysis.ModeEdits), 0)
}

func TestAnalyzeModeEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	slc := createTestSuggestedLoadChange()
	se.analyzeModeEdits(slc, editCounts)

	assert.Equal(t, 1, len(editCounts))
	edit := editCounts["mode_FTL_to_LTL"]
	assert.Equal(t, "mode", edit.FieldName)
	assert.Equal(t, models.LoadMode("FTL"), edit.SuggestedValue)
	assert.Equal(t, models.LoadMode("LTL"), edit.AppliedValue)
	assert.Equal(t, 1, edit.Frequency)
}

func TestAnalyzeCustomerExternalTMSIDEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	slc := createTestSuggestedLoadChange()
	se.analyzeCustomerExternalTMSIDEdits(slc, editCounts)

	assert.Equal(t, 1, len(editCounts))
	edit := editCounts["customerExternalTMSID_CUST001_to_CUST002"]
	assert.Equal(t, "customerExternalTMSID", edit.FieldName)
	assert.Equal(t, "CUST001", edit.SuggestedValue)
	assert.Equal(t, "CUST002", edit.AppliedValue)
}

func TestAnalyzeRateDataEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	slc := createTestSuggestedLoadChange()
	se.analyzeRateDataEdits(slc, editCounts)

	// Should have 2 edits: collection method and customer rate type
	assert.Equal(t, 2, len(editCounts))
	assert.Contains(t, editCounts, "collectionMethod_Prepaid_to_Collect")
	assert.Contains(t, editCounts, "customerRateType_Flat_to_PerMile")
}

func TestAnalyzeRateDataEdits_CaseInsensitive(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	slc := createTestSuggestedLoadChange()
	// Make suggested values different case but same content
	slc.Suggested.LoadChanges.RateData.CollectionMethod = "prepaid"
	slc.Applied.LoadChanges.RateData.CollectionMethod = "PREPAID"

	se.analyzeRateDataEdits(slc, editCounts)

	// Should not create edits for case-only differences (only customerRateType should be different)
	assert.Equal(t, 1, len(editCounts))
}

func TestAnalyzeSpecificationEdits(t *testing.T) {
	se := &SuggestionEnhancer{}
	editCounts := make(map[string]*FieldEdit)

	slc := createTestSuggestedLoadChange()
	se.analyzeSpecificationEdits(slc, editCounts)

	// Should have 3 edits: service type, transport type, and commodities
	assert.Equal(t, 3, len(editCounts))
	assert.Contains(t, editCounts, "serviceType_Standard_to_Express")
	assert.Contains(t, editCounts, "transportType_Van_to_Flatbed")
	assert.Contains(t, editCounts, "commodities_Electronics_to_Machinery")
}

func TestIsEmptyLoadChanges(t *testing.T) {
	se := &SuggestionEnhancer{}

	// Test empty load changes
	emptyLC := &models.LoadChanges{}
	assert.True(t, se.isEmptyLoadChanges(emptyLC))

	// Test load changes with data
	fullLC := createTestSuggestedLoadChange().Suggested.LoadChanges
	assert.False(t, se.isEmptyLoadChanges(fullLC))
}

func TestApplyModeEdit(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentSLC := createTestSuggestedLoadChange()

	// Test with LoadMode value
	edit := &FieldEdit{
		FieldName:    "mode",
		AppliedValue: models.LoadMode("Intermodal"),
	}
	se.applyModeEdit(currentSLC, edit)
	assert.Equal(t, models.LoadMode("Intermodal"), currentSLC.Suggested.LoadChanges.Mode)

	// Test with string value
	edit2 := &FieldEdit{
		FieldName:    "mode",
		AppliedValue: "Expedited",
	}
	se.applyModeEdit(currentSLC, edit2)
	assert.Equal(t, models.LoadMode("Expedited"), currentSLC.Suggested.LoadChanges.Mode)
}

func TestApplyModeEdit_InvalidValue(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentSLC := createTestSuggestedLoadChange()
	originalMode := currentSLC.Suggested.LoadChanges.Mode

	edit := &FieldEdit{
		FieldName:    "mode",
		AppliedValue: 123, // Should be LoadMode or string
	}
	se.applyModeEdit(currentSLC, edit)

	// Value should remain unchanged
	assert.Equal(t, originalMode, currentSLC.Suggested.LoadChanges.Mode)
}

func TestApplyCustomerExternalTMSIDEdit_InvalidValue(t *testing.T) {
	se := &SuggestionEnhancer{vectorRepo: &Repository{db: nil}}

	currentSLC := createTestSuggestedLoadChange()
	edit := &FieldEdit{
		FieldName:    "customerExternalTMSID",
		AppliedValue: 123, // Should be string
	}

	err := se.applyCustomerExternalTMSIDEdit(context.Background(), 123, currentSLC, edit)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "applied value is not a string")
}

func TestApplyCustomerExternalTMSIDEdit_NilVectorRepo(t *testing.T) {
	se := &SuggestionEnhancer{vectorRepo: nil}

	currentSLC := createTestSuggestedLoadChange()
	edit := &FieldEdit{
		FieldName:    "customerExternalTMSID",
		AppliedValue: "CUST123",
	}

	err := se.applyCustomerExternalTMSIDEdit(context.Background(), 123, currentSLC, edit)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database connection not available")
}

func TestApplyRateDataEdit(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentSLC := createTestSuggestedLoadChange()

	// Test collection method edit
	edit1 := &FieldEdit{
		FieldName:    "collectionMethod",
		AppliedValue: "Third Party",
	}
	se.applyRateDataEdit(currentSLC, edit1)
	assert.Equal(t, "Third Party", currentSLC.Suggested.LoadChanges.RateData.CollectionMethod)

	// Test customer rate type edit
	edit2 := &FieldEdit{
		FieldName:    "customerRateType",
		AppliedValue: "CWT",
	}
	se.applyRateDataEdit(currentSLC, edit2)
	assert.Equal(t, "CWT", currentSLC.Suggested.LoadChanges.RateData.CustomerRateType)
}

func TestApplySpecificationEdit(t *testing.T) {
	se := &SuggestionEnhancer{}
	currentSLC := createTestSuggestedLoadChange()

	tests := []struct {
		fieldName string
		value     string
		checkFunc func(*models.SuggestedLoadChange, string)
	}{
		{
			"serviceType",
			"Expedited",
			func(slc *models.SuggestedLoadChange, value string) {
				assert.Equal(t, value, slc.Suggested.LoadChanges.Specifications.ServiceType)
			},
		},
		{
			"transportType",
			"Reefer",
			func(slc *models.SuggestedLoadChange, value string) {
				assert.Equal(t, value, slc.Suggested.LoadChanges.Specifications.TransportType)
			},
		},
		{
			"commodities",
			"Food Products",
			func(slc *models.SuggestedLoadChange, value string) {
				assert.Equal(t, value, slc.Suggested.LoadChanges.Specifications.Commodities)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.fieldName, func(_ *testing.T) {
			edit := &FieldEdit{
				FieldName:    tt.fieldName,
				AppliedValue: tt.value,
			}
			se.applySpecificationEdit(currentSLC, edit)
			tt.checkFunc(currentSLC, tt.value)
		})
	}
}

func TestAnalyzeSimilarLoadChangeEdits_SkipsNonSuggestedLoadChanges(t *testing.T) {
	se := &SuggestionEnhancer{}

	// Mix of SuggestedLoadChange and other suggestion types
	similarSuggestions := []models.Suggestion{
		createTestSuggestedLoadChange(),
		&models.QuoteRequest{}, // This should be skipped
		createTestSuggestedLoadChange(),
	}

	analysis := se.analyzeSimilarLoadChangeEdits(similarSuggestions)

	assert.NotNil(t, analysis)
	// Should process only the SuggestedLoadChange suggestions
}

func TestAnalyzeSimilarLoadChangeEdits_SkipsEmptyLoadChanges(t *testing.T) {
	se := &SuggestionEnhancer{}

	// Create suggestions with empty applied load changes
	slcWithEmpty := &models.SuggestedLoadChange{
		Suggested: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{Mode: models.LoadMode("FTL")},
		},
		Applied: models.SuggestedChanges{
			LoadChanges: &models.LoadChanges{}, // Empty
		},
	}

	similarSuggestions := []models.Suggestion{slcWithEmpty}

	analysis := se.analyzeSimilarLoadChangeEdits(similarSuggestions)

	assert.NotNil(t, analysis)
	// Should not process suggestions with empty applied load changes
	assert.Equal(t, 0, len(analysis.ModeEdits))
}
