package vector

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type vectorEmail struct {
	VectorID uint `gorm:"primaryKey"`
	EmailID  uint `gorm:"primaryKey"`
}

type vectorLoadSuggestion struct {
	VectorID              uint `gorm:"primaryKey"`
	SuggestedLoadChangeID uint `gorm:"primaryKey"`
}

type vectorQuoteRequest struct {
	VectorID       uint `gorm:"primaryKey"`
	QuoteRequestID uint `gorm:"primaryKey"`
}

func (r *Repository) AssociateWithEmail(vector *models.Vector, email *models.Email) error {
	return r.db.Model(vector).Association("Emails").Append(email)
}

func (r *Repository) AssociateWithLoadSuggestion(vector *models.Vector, newSug *models.SuggestedLoadChange) error {
	return r.db.Model(vector).Association("SuggestedLoadChanges").Append(newSug)
}

func (r *Repository) AssociateWithQuoteRequest(vector *models.Vector, newSug *models.QuoteRequest) error {
	return r.db.Model(vector).Association("QuoteRequests").Append(newSug)
}

// BatchAssociateWithEmail creates associations between multiple
// vectors and an email in a single transaction
func (r *Repository) BatchAssociateWithEmail(
	ctx context.Context,
	vectors []models.Vector,
	email *models.Email,
) (err error) {
	ctx, span := otel.StartSpan(ctx, "BatchAssociateWithEmail", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "BatchAssociateWithEmail failed", zap.Error(err))
		}
		span.End(err)
	}()

	if len(vectors) == 0 {
		return nil
	}

	uniqueMap := make(map[uint]struct{})
	uniqueVectors := make([]models.Vector, 0)
	for _, vector := range vectors {
		if _, exists := uniqueMap[vector.ID]; !exists {
			uniqueMap[vector.ID] = struct{}{}
			uniqueVectors = append(uniqueVectors, vector)
		}
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		vectorEmailAssociations := make([]vectorEmail, len(uniqueVectors))
		for i, vector := range uniqueVectors {
			vectorEmailAssociations[i] = vectorEmail{
				VectorID: vector.ID,
				EmailID:  email.ID,
			}
		}

		return tx.Table("vector_emails").
			Clauses(clause.OnConflict{DoNothing: true}).
			Create(vectorEmailAssociations).Error
	})
}

// BatchAssociateWithLoadSuggestion creates associations between multiple vectors
// and a single load suggestion in a single transaction
func (r *Repository) BatchAssociateWithLoadSuggestion(
	ctx context.Context,
	vectors []models.Vector,
	suggestion *models.SuggestedLoadChange,
) (err error) {
	ctx, span := otel.StartSpan(ctx, "BatchAssociateWithLoadSuggestion", nil)
	defer func() {
		if err != nil {
			log.Error(ctx, "BatchAssociateWithLoadSuggestion failed", zap.Error(err))
		}
		span.End(err)
	}()

	if len(vectors) == 0 {
		return nil
	}

	err = r.db.WithContext(ctx).Transaction(
		func(tx *gorm.DB) error {
			associations := make([]vectorLoadSuggestion, len(vectors))
			for i, vector := range vectors {
				associations[i] = vectorLoadSuggestion{
					VectorID:              vector.ID,
					SuggestedLoadChangeID: suggestion.ID,
				}
			}

			return tx.Table("vector_suggested_load_changes").
				Clauses(clause.OnConflict{DoNothing: true}).
				Create(associations).Error
		})
	return err
}

// BatchAssociateWithQuoteRequest creates associations between multiple vectors
// and a single quote request in a single transaction
func (r *Repository) BatchAssociateWithQuoteRequest(
	ctx context.Context,
	vectors []models.Vector,
	quoteRequest *models.QuoteRequest,
) error {

	if len(vectors) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		associations := make([]vectorQuoteRequest, len(vectors))
		for i, vector := range vectors {
			associations[i] = vectorQuoteRequest{
				VectorID:       vector.ID,
				QuoteRequestID: quoteRequest.ID,
			}
		}

		return tx.Table("vector_quote_requests").
			Clauses(clause.OnConflict{DoNothing: true}).
			Create(associations).Error
	})
}

// UpdateLoadBuildingAssociations updates vector embeddings with associations to load building suggestions
// and possible quote request suggestions.
// If the attachmentExternalID is provided, it will be used to find the attachment vector embedding.
// Otherwise, the emailID will be used to find the email vector embedding.
// Returns the updated SuggestedLoadChange with vectors preloaded to avoid read replica race conditions.
func (r *Repository) UpdateLoadBuildingAssociations(
	ctx context.Context,
	email models.Email,
	results []models.SuggestedLoadChange,
	i int) (*models.SuggestedLoadChange, error) {

	if r == nil {
		return nil, errors.New("repository is nil")
	}

	// Start transaction to ensure consistency between association updates and vector preloading
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	committed := false
	defer func() {
		if !committed {
			tx.Rollback()
		}
	}()

	// Update associations within transaction
	var err error
	if results[i].S3Attachment.ExternalID != "" {
		err = r.updateAssociationsWithDB(
			ctx,
			tx,
			&email,
			results[i].S3Attachment.ExternalID,
			models.AttachmentContent,
			&results[i],
			nil,
		)
	} else if results[i].EmailID != 0 {
		err = r.updateAssociationsWithDB(
			ctx,
			tx,
			&email,
			"",
			models.EmailBodyContent,
			&results[i],
			nil,
		)
	}

	if err != nil {
		return nil, fmt.Errorf("error updating vector embedding associations: %w", err)
	}

	// Preload vectors within the same transaction
	var suggestionWithVectors models.SuggestedLoadChange
	err = tx.Preload("Vectors").First(&suggestionWithVectors, results[i].ID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to preload suggested load change with vectors: %w", err)
	}

	// Commit transaction
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}
	committed = true

	return &suggestionWithVectors, nil
}

// UpdateQuoteRequestAssociations updates vector embeddings with associations to quote requests.
// If the attachmentExternalID is provided, it will be used to find the attachment vector embedding.
// Otherwise, the emailID will be used to find the email vector embedding.
// Returns the updated QuoteRequest with vectors preloaded to avoid read replica race conditions.
func (r *Repository) UpdateQuoteRequestAssociations(
	ctx context.Context,
	email models.Email,
	dedupedResults []models.QuoteRequest,
	i int) (*models.QuoteRequest, error) {

	if r == nil {
		return nil, errors.New("repository is nil")
	}

	// Start transaction to ensure consistency between association updates and vector preloading
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	committed := false
	defer func() {
		if !committed {
			tx.Rollback()
		}
	}()

	// Update associations within transaction
	var err error
	if dedupedResults[i].Attachment.ExternalID != "" {
		err = r.updateAssociationsWithDB(
			ctx,
			tx,
			&email,
			dedupedResults[i].Attachment.ExternalID,
			models.AttachmentContent,
			nil,
			&dedupedResults[i],
		)
	} else if dedupedResults[i].EmailID != 0 {
		err = r.updateAssociationsWithDB(
			ctx,
			tx,
			&email,
			"",
			models.EmailBodyContent,
			nil,
			&dedupedResults[i],
		)
	}

	if err != nil {
		return nil, fmt.Errorf("error updating vector embedding associations: %w", err)
	}

	// Preload vectors within the same transaction
	var quoteRequestWithVectors models.QuoteRequest
	err = tx.Preload("Vectors").First(&quoteRequestWithVectors, dedupedResults[i].ID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to preload quote request with vectors: %w", err)
	}

	// Commit transaction
	if err = tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}
	committed = true

	return &quoteRequestWithVectors, nil
}

func (r *Repository) updateAssociationsWithDB(
	ctx context.Context,
	db *gorm.DB,
	email *models.Email,
	attachmentExternalID string,
	contentType models.ContentType,
	suggestion *models.SuggestedLoadChange,
	quoteRequest *models.QuoteRequest,
) error {

	var vector models.Vector

	query := db.Where("email_id = ? AND service_id = ? AND content_type = ?", email.ID, email.ServiceID, contentType)

	if attachmentExternalID != "" && contentType == models.AttachmentContent {
		query = query.Where("attachment_external_id = ?", attachmentExternalID)
	}

	result := query.First(&vector)

	if result.Error != nil {
		return fmt.Errorf("failed to find vector embedding: %w", result.Error)
	}

	if suggestion != nil {
		if err := db.Model(&vector).Association("SuggestedLoadChanges").Append(suggestion); err != nil {
			return fmt.Errorf("failed to associate vector with suggestion: %w", err)
		}

		// If there's a quote request suggestion, associate it as well.
		// Jun 11, 2025: This logic is temporarily deprecated since we do not auto create
		// quote request suggestions from load building suggestions anymore.
		if suggestion.QuoteRequestSuggestion != nil {
			if err := db.Model(&vector).
				Association("QuoteRequests").
				Append(suggestion.QuoteRequestSuggestion); err != nil {

				return fmt.Errorf("failed to associate vector with quote request: %w", err)
			}
		}
	}

	if quoteRequest != nil {
		if err := db.Model(&vector).Association("QuoteRequests").Append(quoteRequest); err != nil {
			return fmt.Errorf("failed to associate vector with quote request: %w", err)
		}
	}

	log.Debug(ctx, "Updated embedding associations",
		zap.Uint("emailID", email.ID),
		zap.String("attachmentExternalID", attachmentExternalID),
	)

	return nil
}
