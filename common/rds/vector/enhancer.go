package vector

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// FieldEdit represents a single field edit with frequency information
type FieldEdit struct {
	FieldName      string
	SuggestedValue any
	AppliedValue   any
	Frequency      int
	Weight         float64
}

type SuggestionEnhancer struct {
	vectorRepo *Repository
}

func NewSuggestionEnhancer(vectorRepo *Repository) *SuggestionEnhancer {
	service := &SuggestionEnhancer{
		vectorRepo: vectorRepo,
	}

	return service
}

func (se *SuggestionEnhancer) Enhance(
	ctx context.Context,
	tmsID uint,
	suggestion models.Suggestion,
) error {
	var err error
	attrs := otel.EnhanceSuggestionAttrs(suggestion)
	ctx, metaSpan := otel.StartSpan(ctx, "SuggestionEnhancer.Enhance", attrs)
	defer func() { metaSpan.End(err) }()

	suggestionType := suggestion.GetSuggestionType()

	log.Info(ctx, "starting suggestion enhancement",
		zap.String("suggestionType", string(suggestionType)))

	similarSuggestions, err := se.findSimilarSuggestions(ctx, suggestion)
	if err != nil {
		return fmt.Errorf("failed to find similar suggestions: %w", err)
	}

	if len(similarSuggestions) == 0 {
		log.Info(ctx, "no similar suggestions found, skipping enhancement")
		return nil
	}

	if suggestionType == models.QuickQuoteSuggestion {
		err = se.applyQRCBR(ctx, suggestion, similarSuggestions)
		if err != nil {
			return fmt.Errorf("failed to apply case-based reasoning: %w", err)
		}

		err = se.saveSuggestion(ctx, suggestion)
		if err != nil {
			return fmt.Errorf("failed to save enhanced suggestion: %w", err)
		}
	}

	if suggestionType == models.LoadBuilding {
		// need tmsID to speed up the lookup of the customer, pickup, and consignee
		err = se.applySLCCBR(ctx, tmsID, suggestion, similarSuggestions)
		if err != nil {
			return fmt.Errorf("failed to apply case-based reasoning: %w", err)
		}

		err = se.saveSuggestion(ctx, suggestion)
		if err != nil {
			return fmt.Errorf("failed to save enhanced suggestion: %w", err)
		}
	}

	log.Info(ctx, "suggestion enhancement completed")

	return nil
}

func (se *SuggestionEnhancer) findSimilarSuggestions(
	ctx context.Context,
	suggestion models.Suggestion,
) ([]models.Suggestion, error) {
	var err error
	attrs := otel.FindSimilarSuggestionsAttrs(suggestion)
	ctx, metaSpan := otel.StartSpan(ctx, "SuggestionEnhancer.findSimilarSuggestions", attrs)
	defer func() { metaSpan.End(err) }()

	suggestionVectors := suggestion.GetVectors()
	if len(suggestionVectors) == 0 {
		return nil, errors.New("no vectors found for suggestion")
	}

	suggestionVector := suggestionVectors[0]
	if len(suggestionVector.Embedding) == 0 {
		return nil, errors.New("no embedding found for suggestion")
	}

	similarityService := NewSimilarityService(se.vectorRepo.db)

	var results []models.Suggestion

	switch suggestion.GetSuggestionType() {
	case models.LoadBuilding:
		loadChanges, err := similarityService.FindSimilarLoadChanges(
			ctx,
			suggestion.GetServiceID(),
			suggestionVector.ContentType,
			suggestionVector.Embedding,
		)
		if err != nil {
			return nil, err
		}

		for i := range loadChanges {
			loadChangeCopy := loadChanges[i]
			results = append(results, &loadChangeCopy)
		}

	case models.QuickQuoteSuggestion:
		quoteRequests, err := similarityService.FindSimilarQuoteRequests(
			ctx,
			suggestion.GetServiceID(),
			suggestionVector.ContentType,
			suggestionVector.Embedding,
		)
		if err != nil {
			return nil, err
		}

		for i := range quoteRequests {
			quoteRequestCopy := quoteRequests[i]
			results = append(results, &quoteRequestCopy)
		}
	}

	return results, nil
}

// saveSuggestion saves the enhanced suggestion to the database
func (se *SuggestionEnhancer) saveSuggestion(ctx context.Context, suggestion models.Suggestion) error {
	switch s := suggestion.(type) {
	case *models.QuoteRequest:
		return se.vectorRepo.db.WithContext(ctx).
			Model(&models.QuoteRequest{}).
			Where("id = ?", s.ID).
			Updates(s).Error
	case *models.SuggestedLoadChange:
		return se.vectorRepo.db.WithContext(ctx).
			Model(&models.SuggestedLoadChange{}).
			Where("id = ?", s.ID).
			Updates(s).Error
	default:
		return fmt.Errorf("unsupported suggestion type: %T", suggestion)
	}
}
