// Package vector provides suggestion enhancement functionality with smart pattern detection
//
// Benchmarks (requires LIVE_TEST=true and DB environment variables):
//
//	LIVE_TEST=true go test -bench=. -v
//
// Main benchmark test:
//
//	LIVE_TEST=true go test -bench=BenchmarkFindSimilarSuggestions -v    # Core similarity search
//
// This benchmark tests the ability to find similar suggestions using vector embeddings
// and validates that vector similarity translates to actual business content similarity.
//
// Required environment variables for integration tests and benchmarks:
//
//	DB_HOST, DB_NAME, DB_USER, DB_PASSWORD
package vector

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

var testDB *gorm.DB

// TestSuggestionEnhancerCreation tests the creation of suggestion enhancer
func TestSuggestionEnhancerCreation(t *testing.T) {
	mockRepo := &Repository{db: nil}
	enhancer := NewSuggestionEnhancer(mockRepo)

	assert.NotNil(t, enhancer)
	assert.Equal(t, mockRepo, enhancer.vectorRepo)
}

// BenchmarkFindSimilarSuggestions benchmarks the core similarity search functionality
// This is the main benchmark that validates vector similarity translates to business content similarity
// To run: LIVE_TEST=true go test -bench=BenchmarkFindSimilarSuggestions -v
func BenchmarkFindSimilarSuggestions(b *testing.B) {
	enhancer := setupBenchmarkDB(b)
	ctx := context.Background()

	loadSuggestions := fetchTestLoadBuildingSuggestions(ctx, 2)
	quoteSuggestions := fetchTestQuickQuoteSuggestions(ctx, 2)

	allSuggestions := make([]models.Suggestion, 0, len(loadSuggestions)+len(quoteSuggestions))
	allSuggestions = append(allSuggestions, loadSuggestions...)
	allSuggestions = append(allSuggestions, quoteSuggestions...)

	if len(allSuggestions) == 0 {
		b.Skip("No test suggestions found for similarity search")
	}

	b.Logf("Running similarity search benchmark with %d suggestions", len(allSuggestions))

	var metrics SimilarityMetrics

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, suggestion := range allSuggestions {
			start := time.Now()
			similar, err := enhancer.findSimilarSuggestions(ctx, suggestion)
			duration := time.Since(start)

			if i == 0 { // Only collect detailed results on first iteration
				metrics.TotalDuration += duration
				if err == nil {
					metrics.TotalSimilar += len(similar)

					for _, similarSuggestion := range similar {
						contentSimilarity := calculateSuggestionContentSimilarity(suggestion, similarSuggestion)
						metrics.TotalContentSimilarity += contentSimilarity
						metrics.Comparisons++
					}
				}
			}
		}
	}
	b.StopTimer()

	logSimilarityResults(b, allSuggestions, metrics)
}

// SimilarityMetrics holds metrics for similarity search benchmarks
type SimilarityMetrics struct {
	TotalDuration          time.Duration
	TotalSimilar           int
	TotalContentSimilarity float64
	Comparisons            int
}

// logSimilarityResults logs detailed similarity search results
func logSimilarityResults(b *testing.B, suggestions []models.Suggestion, metrics SimilarityMetrics) {
	if len(suggestions) == 0 {
		return
	}

	avgDuration := metrics.TotalDuration / time.Duration(len(suggestions))
	avgSimilar := float64(metrics.TotalSimilar) / float64(len(suggestions))
	avgContentSimilarity := float64(0)
	if metrics.Comparisons > 0 {
		avgContentSimilarity = metrics.TotalContentSimilarity / float64(metrics.Comparisons)
	}

	b.Log("\n=== Similarity Search Results ===")
	b.Logf("Total suggestions tested: %d", len(suggestions))
	b.Logf("Average search duration: %v", avgDuration)
	b.Logf("Average similar suggestions found: %.1f", avgSimilar)
	b.Logf("Total similar suggestions: %d", metrics.TotalSimilar)
	b.Logf("Content similarity comparisons: %d", metrics.Comparisons)
	b.Logf("Average content similarity score: %.3f", avgContentSimilarity)

	if avgDuration > 100*time.Millisecond {
		b.Log("⚠ Warning: Average search duration exceeds 100ms")
	}
	if avgSimilar < 1.0 {
		b.Log("⚠ Warning: Low average similarity results, may indicate sparse data")
	}
	if avgContentSimilarity < 0.3 {
		b.Log("⚠ Warning: Low content similarity despite vector similarity - may need threshold adjustment")
	}
	if avgContentSimilarity > 0.7 {
		b.Log("✅ Excellent: High content similarity validates vector embeddings quality")
	}
	b.Log("=== End Similarity Search Results ===\n")
}

// fetchTestLoadBuildingSuggestions retrieves load building suggestions with vectors from database
func fetchTestLoadBuildingSuggestions(ctx context.Context, count int) []models.Suggestion {
	if testDB == nil {
		log.Error(ctx, "Test database not initialized")
		return []models.Suggestion{}
	}

	var loadChanges []models.SuggestedLoadChange

	//nolint:lll
	err := testDB.WithContext(ctx).
		Preload("Vectors").
		Joins("JOIN vector_suggested_load_changes ON suggested_load_changes.id = vector_suggested_load_changes.suggested_load_change_id").
		Joins("JOIN vectors ON vector_suggested_load_changes.vector_id = vectors.id").
		Where("vectors.embedding IS NOT NULL").
		Where("suggested_load_changes.pipeline = ?", models.LoadBuildingPipeline).
		Group("suggested_load_changes.id").
		Order("RANDOM()").
		Limit(count).
		Find(&loadChanges).Error

	if err != nil {
		log.Error(ctx, "Could not fetch load building suggestions with vectors", zap.Error(err))
		return []models.Suggestion{}
	}

	var suggestions []models.Suggestion
	for _, loadChange := range loadChanges {
		if len(loadChange.Vectors) > 0 {
			loadChangeCopy := loadChange
			suggestions = append(suggestions, &loadChangeCopy)
		}
	}

	log.Info(ctx, "Fetched load building suggestions for testing",
		zap.Int("count", len(suggestions)),
		zap.Int("total_vectors", countTotalVectors(suggestions)))
	return suggestions
}

// fetchTestQuickQuoteSuggestions retrieves quote request suggestions with vectors from database
func fetchTestQuickQuoteSuggestions(ctx context.Context, count int) []models.Suggestion {
	if testDB == nil {
		log.Error(ctx, "Test database not initialized")
		return []models.Suggestion{}
	}

	var quoteRequests []models.QuoteRequest

	err := testDB.WithContext(ctx).
		Preload("Vectors").
		Joins("JOIN vector_quote_requests ON quote_requests.id = vector_quote_requests.quote_request_id").
		Joins("JOIN vectors ON vector_quote_requests.vector_id = vectors.id").
		Where("vectors.embedding IS NOT NULL").
		Group("quote_requests.id").
		Order("RANDOM()").
		Limit(count).
		Find(&quoteRequests).Error

	if err != nil {
		log.Error(ctx, "Could not fetch quote request suggestions with vectors", zap.Error(err))
		return []models.Suggestion{}
	}

	var suggestions []models.Suggestion
	for _, quoteRequest := range quoteRequests {
		if len(quoteRequest.Vectors) > 0 {
			quoteRequestCopy := quoteRequest
			suggestions = append(suggestions, &quoteRequestCopy)
		}
	}

	log.Info(ctx, "Fetched quote request suggestions for testing",
		zap.Int("count", len(suggestions)),
		zap.Int("total_vectors", countTotalVectors(suggestions)))
	return suggestions
}

// countTotalVectors counts total vectors across all suggestions
func countTotalVectors(suggestions []models.Suggestion) int {
	total := 0
	for _, s := range suggestions {
		total += len(s.GetVectors())
	}
	return total
}

// setupBenchmarkDB initializes database connection and enhancer for benchmarks
func setupBenchmarkDB(b *testing.B) *SuggestionEnhancer {
	if testing.Short() {
		b.Skip("Skipping RDS benchmark in short mode")
	}

	if os.Getenv("LIVE_TEST") != "true" {
		b.Skip("skipping benchmark: run with LIVE_TEST=true to enable")
	}

	dbHost := os.Getenv("DB_HOST")
	dbName := os.Getenv("DB_NAME")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")

	if dbHost == "" || dbName == "" || dbUser == "" || dbPassword == "" {
		b.Skip("Required database environment variables not set")
	}

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=5432 sslmode=disable",
		dbHost, dbUser, dbPassword, dbName)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{PrepareStmt: true})
	if err != nil {
		b.Fatalf("Failed to connect to database: %v", err)
	}

	testDB = db
	vectorRepo := New(db)

	return NewSuggestionEnhancer(vectorRepo)
}

// calculateSuggestionContentSimilarity compares the actual business content of two suggestions
// Returns a score from 0.0 (completely different) to 1.0 (identical)
func calculateSuggestionContentSimilarity(suggestion1, suggestion2 models.Suggestion) float64 {
	if suggestion1.GetSuggestionType() != suggestion2.GetSuggestionType() {
		return 0.0 // Different types are considered completely different
	}

	switch suggestion1.GetSuggestionType() {
	case models.LoadBuilding:
		return compareLoadBuildingSuggestions(suggestion1, suggestion2)
	case models.QuickQuoteSuggestion:
		return compareQuoteRequestSuggestions(suggestion1, suggestion2)
	default:
		return 0.0
	}
}

// compareLoadBuildingSuggestions compares two load building suggestions
func compareLoadBuildingSuggestions(suggestion1, suggestion2 models.Suggestion) float64 {
	load1, ok1 := suggestion1.(*models.SuggestedLoadChange)
	load2, ok2 := suggestion2.(*models.SuggestedLoadChange)
	if !ok1 || !ok2 {
		return 0.0
	}

	return compareLoadChanges(load1, load2)
}

// compareQuoteRequestSuggestions compares two quote request suggestions
func compareQuoteRequestSuggestions(suggestion1, suggestion2 models.Suggestion) float64 {
	quote1, ok1 := suggestion1.(*models.QuoteRequest)
	quote2, ok2 := suggestion2.(*models.QuoteRequest)
	if !ok1 || !ok2 {
		return 0.0
	}

	return compareQuoteRequests(quote1, quote2)
}

// compareLoadChanges compares two SuggestedLoadChange objects based on business-critical fields
func compareLoadChanges(load1, load2 *models.SuggestedLoadChange) float64 {
	score := 0.0
	maxScore := 11.0 // Total possible points for all business-critical fields

	if load1.Suggested.LoadChanges == nil || load2.Suggested.LoadChanges == nil {
		return 0.0
	}

	if load1.Suggested.LoadChanges.Customer.ExternalTMSID != "" &&
		load2.Suggested.LoadChanges.Customer.ExternalTMSID != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Customer.ExternalTMSID,
			load2.Suggested.LoadChanges.Customer.ExternalTMSID,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Customer.Name != "" &&
		load2.Suggested.LoadChanges.Customer.Name != "" {

		if strings.EqualFold(load1.Suggested.LoadChanges.Customer.Name,
			load2.Suggested.LoadChanges.Customer.Name,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Pickup.ExternalTMSID != "" &&
		load2.Suggested.LoadChanges.Pickup.ExternalTMSID != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Pickup.ExternalTMSID,
			load2.Suggested.LoadChanges.Pickup.ExternalTMSID,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Pickup.Name != "" &&
		load2.Suggested.LoadChanges.Pickup.Name != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Pickup.Name,
			load2.Suggested.LoadChanges.Pickup.Name,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Consignee.ExternalTMSID != "" &&
		load2.Suggested.LoadChanges.Consignee.ExternalTMSID != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Consignee.ExternalTMSID,
			load2.Suggested.LoadChanges.Consignee.ExternalTMSID,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Consignee.Name != "" &&
		load2.Suggested.LoadChanges.Consignee.Name != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Consignee.Name,
			load2.Suggested.LoadChanges.Consignee.Name,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Specifications.TransportType != "" &&
		load2.Suggested.LoadChanges.Specifications.TransportType != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Specifications.TransportType,
			load2.Suggested.LoadChanges.Specifications.TransportType,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Specifications.Commodities != "" &&
		load2.Suggested.LoadChanges.Specifications.Commodities != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Specifications.Commodities,
			load2.Suggested.LoadChanges.Specifications.Commodities,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.Specifications.ServiceType != "" &&
		load2.Suggested.LoadChanges.Specifications.ServiceType != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.Specifications.ServiceType,
			load2.Suggested.LoadChanges.Specifications.ServiceType,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.RateData.CollectionMethod != "" &&
		load2.Suggested.LoadChanges.RateData.CollectionMethod != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.RateData.CollectionMethod,
			load2.Suggested.LoadChanges.RateData.CollectionMethod,
		) {
			score += 1.0
		}
	}

	if load1.Suggested.LoadChanges.RateData.CustomerRateType != "" &&
		load2.Suggested.LoadChanges.RateData.CustomerRateType != "" {

		if strings.EqualFold(
			load1.Suggested.LoadChanges.RateData.CustomerRateType,
			load2.Suggested.LoadChanges.RateData.CustomerRateType,
		) {
			score += 1.0
		}
	}

	return score / maxScore
}

// compareQuoteRequests compares two QuoteRequest objects based on UI form fields
func compareQuoteRequests(quote1, quote2 *models.QuoteRequest) float64 {
	score := 0.0
	maxScore := 7.0 // Total possible points for UI form fields

	if quote1.SuggestedRequest.Customer.ExternalTMSID != "" &&
		quote2.SuggestedRequest.Customer.ExternalTMSID != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.Customer.ExternalTMSID,
			quote2.SuggestedRequest.Customer.ExternalTMSID,
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.Customer.Name != "" &&
		quote2.SuggestedRequest.Customer.Name != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.Customer.Name,
			quote2.SuggestedRequest.Customer.Name,
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.TransportType != "" &&
		quote2.SuggestedRequest.TransportType != "" {

		if strings.EqualFold(
			string(quote1.SuggestedRequest.TransportType),
			string(quote2.SuggestedRequest.TransportType),
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.PickupLocation.City != "" &&
		quote2.SuggestedRequest.PickupLocation.City != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.PickupLocation.City,
			quote2.SuggestedRequest.PickupLocation.City,
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.PickupLocation.State != "" &&
		quote2.SuggestedRequest.PickupLocation.State != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.PickupLocation.State,
			quote2.SuggestedRequest.PickupLocation.State,
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.DeliveryLocation.City != "" &&
		quote2.SuggestedRequest.DeliveryLocation.City != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.DeliveryLocation.City,
			quote2.SuggestedRequest.DeliveryLocation.City,
		) {
			score += 1.0
		}
	}

	if quote1.SuggestedRequest.DeliveryLocation.State != "" &&
		quote2.SuggestedRequest.DeliveryLocation.State != "" {

		if strings.EqualFold(
			quote1.SuggestedRequest.DeliveryLocation.State,
			quote2.SuggestedRequest.DeliveryLocation.State,
		) {
			score += 1.0
		}
	}

	return score / maxScore
}
