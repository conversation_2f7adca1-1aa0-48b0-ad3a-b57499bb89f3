package vector

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type SimilarityService struct {
	db                        *gorm.DB
	queryTimeout              time.Duration
	hnswEfSearch              int
	cosineSimilarityThreshold float64
}

func NewSimilarityService(db *gorm.DB) *SimilarityService {
	return &SimilarityService{
		db:           db,
		queryTimeout: 5 * time.Second,
		// 100 is a good default for hnsw.ef_search since it is a good balance between speed and accuracy
		// 50 would be faster but less accurate and 200 would be slower but more accurate
		hnswEfSearch: 100,
		// cosine similarity is 1 - distance, so 0.15 is 85% similarity
		cosineSimilarityThreshold: 0.15,
	}
}

func (s *SimilarityService) FindSimilarLoadChanges(
	ctx context.Context,
	serviceID uint,
	contentType models.ContentType,
	embedding models.VectorEmbedding,
) ([]models.SuggestedLoadChange, error) {

	queryCtx, cancel := context.WithTimeout(ctx, s.queryTimeout)
	defer cancel()

	embeddingValue, err := embedding.Value()
	if err != nil {
		return nil, fmt.Errorf("failed to convert embedding to SQL value: %w", err)
	}

	embeddingStr, ok := embeddingValue.(string)
	if !ok {
		return nil, fmt.Errorf("unexpected embedding value type: %T", embeddingValue)
	}

	// We must run this query in a transaction to ensure that the hnsw.ef_search setting is set locally
	// for the duration of the query.
	tx := s.db.WithContext(queryCtx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	committed := false
	defer func() {
		if !committed {
			tx.Rollback()
		}
	}()

	// This is not SQL injection because we control the value of s.hnswEfSearch.
	err = tx.Exec(fmt.Sprintf("SET LOCAL hnsw.ef_search = %d", s.hnswEfSearch)).Error
	if err != nil {
		return nil, fmt.Errorf("failed to set hnsw.ef_search: %w", err)
	}

	// Originally I ordered by distance but in testing I realized we want to prioritize
	// newer suggestions instead so as our users make edits to our suggestions time after
	// time, we continuously update the suggestions with the latest version that users want.
	query := `
		WITH top_candidates AS (
			SELECT
				v.id AS vector_id,
				v.embedding <=> ? AS distance -- Calculate distance just once here!
			FROM
				vectors v
			WHERE
				v.service_id = ?
				AND v.content_type = ?
			ORDER BY
				distance -- Order by the *already calculated* distance
			LIMIT 50 -- Efficiently get the top 50 closest candidates using the vector index
		),
		filtered_candidates AS ( -- A new step to apply the distance threshold
			SELECT
				tc.vector_id,
				tc.distance
			FROM
				top_candidates tc
			WHERE
				tc.distance < ? -- Apply the threshold *after* we've found the top 50
		)
		SELECT
			slc.*,
			fc.distance,
			1 - fc.distance AS cosine_similarity
		FROM
			filtered_candidates fc
		JOIN
			vector_suggested_load_changes vslc
			ON vslc.vector_id = fc.vector_id
		JOIN
			suggested_load_changes slc
			ON slc.id = vslc.suggested_load_change_id
		WHERE
			slc.service_id = ?
			AND slc.status = 'accepted'
		ORDER BY
			slc.created_at DESC
		LIMIT 10;
	`

	var results []models.SuggestedLoadChange
	err = tx.Raw(
		query,
		embeddingStr,
		serviceID,
		contentType,
		s.cosineSimilarityThreshold,
		serviceID,
	).Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("similarity query failed: %w", err)
	}

	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}
	committed = true

	log.Debug(ctx, "load change similarity query completed",
		zap.Int("results", len(results)),
		zap.Float64("threshold", s.cosineSimilarityThreshold))

	return results, nil
}

func (s *SimilarityService) FindSimilarQuoteRequests(
	ctx context.Context,
	serviceID uint,
	contentType models.ContentType,
	embedding models.VectorEmbedding,
) ([]models.QuoteRequest, error) {

	queryCtx, cancel := context.WithTimeout(ctx, s.queryTimeout)
	defer cancel()

	embeddingValue, err := embedding.Value()
	if err != nil {
		return nil, fmt.Errorf("failed to convert embedding to SQL value: %w", err)
	}

	embeddingStr, ok := embeddingValue.(string)
	if !ok {
		return nil, fmt.Errorf("unexpected embedding value type: %T", embeddingValue)
	}

	// We must run this query in a transaction to ensure that the hnsw.ef_search setting is set locally
	// for the duration of the query.
	tx := s.db.WithContext(queryCtx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	committed := false
	defer func() {
		if !committed {
			tx.Rollback()
		}
	}()

	// This is not SQL injection because we control the value of s.hnswEfSearch.
	err = tx.Exec(fmt.Sprintf("SET LOCAL hnsw.ef_search = %d", s.hnswEfSearch)).Error
	if err != nil {
		return nil, fmt.Errorf("failed to set hnsw.ef_search: %w", err)
	}

	// Originally I ordered by distance but in testing I realized we want to prioritize
	// newer suggestions instead so as our users make edits to our suggestions time after
	// time, we continuously update the suggestions with the latest version that users want.
	query := `
		WITH top_candidates AS (
			SELECT
				v.id AS vector_id,
				v.embedding <=> ? AS distance -- Calculate distance once
			FROM
				vectors v
			WHERE
				v.service_id = ?
				AND v.content_type = ?
			ORDER BY
				distance
			LIMIT 50
		),
		filtered_candidates AS (
			SELECT
				tc.vector_id,
				tc.distance
			FROM
				top_candidates tc
			WHERE
				tc.distance < ?
		)
		SELECT
			qr.*,
			fc.distance,
			1 - fc.distance AS cosine_similarity
		FROM
			filtered_candidates fc
		JOIN
			vector_quote_requests vqr
			ON vqr.vector_id = fc.vector_id
		JOIN
			quote_requests qr
			ON qr.id = vqr.quote_request_id
		WHERE
			qr.service_id = ?
			AND qr.status IN ('accepted', 'inFlight')
		ORDER BY
			qr.created_at DESC
		LIMIT 10;
	`

	var results []models.QuoteRequest
	err = tx.Raw(
		query,
		embeddingStr,
		serviceID,
		contentType,
		s.cosineSimilarityThreshold,
		serviceID,
	).Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("similarity query failed: %w", err)
	}

	err = tx.Commit().Error
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}
	committed = true

	log.Debug(ctx, "quote request similarity query completed",
		zap.Int("results", len(results)),
		zap.Float64("threshold", s.cosineSimilarityThreshold))

	return results, nil
}
