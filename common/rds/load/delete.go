package load

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Delete stale placeholder loads. If they haven't been updated since creation, then it's because:
//
//  1. They actually don't exist in the TMS.
//  2. The placeholder load was a duplicate of another one. This would happen if we parsed two different ID types
//     that later resolve into the same load record.
//
// Expanding on #2: let's say an email contains Turvo customId 12345-67890 and poNumber 7654321, which actually
// refer to the same load. If there's a transient error during processing, we don't know if that poNumber belongs
// to the same load, so we create 2 placeholder loads with freightTrackingIDs 12345-67890 and 7654321.
// Later, the API will try to look up these loads again. Both will upsert on the load record where
// freightTrackingID = 12345-67890, but the one where freightTrackingID is 7654321 will remain untouched because
// there's no shipment with customId (aka freightTrackingID) equal to 7654321; it's a PO # for load
// 12345-67890. Thus we periodically clear out placeholder loads from DB.
func DeletePlaceholderLoads(ctx context.Context, serviceID uint) (count int, err error) {
	err = rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var loadsToDelete []models.Load
		err := tx.Where("created_at = updated_at AND updated_at < ? AND service_id = ? AND is_placeholder = true",
			time.Now().AddDate(0, 0, -7), serviceID).
			Find(&loadsToDelete).Error
		if err != nil {
			return fmt.Errorf("error finding loads: %w", err)
		}
		if len(loadsToDelete) == 0 {
			return nil
		}

		result := tx.Clauses(clause.Returning{}).Select("Emails").Delete(&loadsToDelete)
		count = int(result.RowsAffected)

		return result.Error
	})

	return count, err

}
