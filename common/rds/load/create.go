package load

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Create(ctx context.Context, load *models.Load) error {

	// Update dates before creation
	if err := load.UpdateDates(); err != nil {
		log.WarnNoSentry(
			ctx,
			"failed to update load dates during creation",
			zap.Error(err),
			zap.String("loadID", fmt.Sprintf("%d", load.ID)),
		)
	}

	if len(load.Pickup.Zipcode) >= 3 {
		load.Pickup.ZipPrefix = load.Pickup.Zipcode[:3]
	}

	if len(load.Consignee.Zipcode) >= 3 {
		load.Consignee.ZipPrefix = load.Consignee.Zipcode[:3]
	}

	matchLoadPickupToWarehouse(ctx, load)
	matchLoadDropoffToWarehouse(ctx, load)

	return rds.WithContext(ctx).Create(load).Error
}
