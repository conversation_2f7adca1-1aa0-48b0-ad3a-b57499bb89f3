package load

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	rediscommon "github.com/drumkitai/drumkit/common/redis"
)

func TestDeletePlaceholderLoads(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping service TestDeletePlaceholderLoads: run with TEST_LIVE_DB=true to enable")
		return
	}

	mockRDB, _ := redismock.NewClientMock()
	rediscommon.RDB = mockRDB
	t.Cleanup(func() {
		_ = mockRDB.Close()
		rediscommon.RDB = nil
	})

	ctx := context.Background()
	rds.MustOpenTestDB(ctx, "drumkit_test_db")

	rds.ClearTestDB(ctx, t)

	t.Cleanup(func() {
		rds.ClearTestDB(ctx, t)
	})

	service := models.Service{Name: "Test Service"}
	err := rds.CreateService(ctx, &service)
	require.NoError(t, err)

	aljex := models.Integration{
		Name:      models.Aljex,
		Type:      models.TMS,
		ServiceID: service.ID,
	}
	err = rds.WithContext(ctx).Create(&aljex).Error
	require.NoError(t, err)

	email := models.Email{
		ExternalID: "email1",
		ServiceID:  service.ID,
	}

	now := time.Now()
	L8D := now.AddDate(0, 0, -8)
	loads := []models.Load{
		{
			LoadCoreInfo: models.LoadCoreInfo{
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						AddressLine1: "123 Main St",
						City:         "Boston",
						State:        "MA",
						Zipcode:      "02118",
					},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						AddressLine1: "456 Elm St",
						City:         "Cambridge",
						State:        "MA",
						Zipcode:      "02139",
					},
				},
			},
			FreightTrackingID: "load1",
			ServiceID:         service.ID,
			TMSID:             aljex.ID,
		},
		{
			Model: gorm.Model{
				CreatedAt: L8D,
				UpdatedAt: L8D,
			},
			LoadCoreInfo: models.LoadCoreInfo{
				Pickup: models.Pickup{
					CompanyCoreInfo: models.CompanyCoreInfo{
						AddressLine1: "123 Main St",
						City:         "Boston",
						State:        "MA",
						Zipcode:      "02118",
					},
				},
				Consignee: models.Consignee{
					CompanyCoreInfo: models.CompanyCoreInfo{
						AddressLine1: "456 Elm St",
						City:         "Cambridge",
						State:        "MA",
						Zipcode:      "02139",
					},
				},
			},
			FreightTrackingID: "placeholderLoad",
			ServiceID:         service.ID,
			IsPlaceholder:     true,
			TMSID:             aljex.ID,
		},
	}
	email.Loads = loads

	for i := range email.Loads {
		err := UpsertLoad(ctx, &email.Loads[i], &service)
		require.NoError(t, err)
	}

	err = rds.WithContext(ctx).Debug().Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns:   []clause.Column{{Name: "external_id"}},
			UpdateAll: true}).
		Create(&email).Error

	require.NoError(t, err)

	// Override Gorm's automated time-tracking behavior to match delete conditions
	require.NoError(t, rds.WithContext(ctx).Model(&email.Loads[1]).UpdateColumn("updated_at", L8D).Error)

	t.Run("OK", func(t *testing.T) {
		rowsAffected, err := DeletePlaceholderLoads(ctx, service.ID)
		require.NoError(t, err)
		assert.Equal(t, 1, rowsAffected)

		// Verify association with placeholder load was deleted
		var dbEmail models.Email
		err = rds.WithContext(ctx).Debug().
			Where("external_id = ?", "email1").
			Preload("Loads").
			First(&dbEmail).Error
		require.NoError(t, err)

		require.Len(t, dbEmail.Loads, 1)
		require.False(t, dbEmail.Loads[0].IsPlaceholder)

		// Verify placeholder email was deleted
		var dbLoads []models.Load
		err = rds.WithContext(ctx).Unscoped().Where("is_placeholder = TRUE").Find(&dbLoads).Error
		require.NoError(t, err)
		assert.Len(t, dbLoads, 1)
		assert.NotEmpty(t, dbLoads[0].DeletedAt)
		assert.True(t, dbLoads[0].IsPlaceholder)

	},
	)
}
