package tmscarrier

import (
	"context"
	"database/sql"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchCarriersQuery struct {
	TMSID uint `json:"tmsID"`
	models.CompanyCoreInfo
	DOTNumber string `json:"dotNumber"`
}

// Get carriers by TMS ID, ordered by name in ascending order
func GetTMSCarriersByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSCarrier, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", query.TMSID).Order("name ASC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

// This function is used by the "Name" autocomplete search input on the FE
func FuzzySearchByName(ctx context.Context, query SearchCarriersQuery) (res []models.TMSCarrier, err error) {
	var carrierNameSearchThreshold = 0.1

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchByField(ctx, "name", query.Name, query.TMSID, carrierNameSearchThreshold)).
		Find(&res).Error

	return res, err
}

// This function is used by the "Address Line 1" autocomplete search input on the FE
func FuzzySearchByStreetAddress(ctx context.Context, query SearchCarriersQuery) (res []models.TMSCarrier, err error) {
	var carrierAddressSearchThreshold = 0.1

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchByField(ctx, "address_line1", query.AddressLine1, query.TMSID, carrierAddressSearchThreshold)).
		Find(&res).Error

	return res, err
}

func SearchByDOTNumber(ctx context.Context, query SearchCarriersQuery) (res []models.TMSCarrier, err error) {
	err = rds.WithContextReader(ctx).
		Where("dot_number LIKE  '%' || ? || '%'", query.DOTNumber).
		Find(&res).Error

	return res, err
}

// Helper function to fuzzy match a carrier by a column name
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH tms_carrier_distances AS (
					SELECT *,
						%[1]s <-> @searchTerm AS trgm_dist,
						%[1]s ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM tms_carriers
					WHERE tms_integration_id = @tmsID
					-- Exclude carriers whose similarity to searchTerm is lower than threshold
					AND (%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%')
				)
				SELECT *
				FROM tms_carrier_distances
				ORDER BY
					is_match DESC,
					trgm_dist;
			`, columnName)

			// NOTE: Scopes() and Raw/Exec() cannot be used to chain multiple fuzzy queries together
			// (like load advanced search) because they're executed immediately
			// but this usage is fine
			return tx.Raw(query, sql.Named("searchTerm", searchTerm), sql.Named("tmsID", tmsID))
		})
	}
}

// GetCarrierByExternalID retrieves a carrier by its external TMS ID and integration ID
func GetCarrierByExternalID(ctx context.Context, tmsID uint, externalTMSID string) (models.TMSCarrier, error) {
	var carrier models.TMSCarrier

	err := rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND external_tms_id = ?", tmsID, externalTMSID).
		First(&carrier).Error

	return carrier, err
}

// GetByServiceID retrieves a TMSCarrier by its service ID.
func GetByServiceID(ctx context.Context, serviceID uint) (*models.TMSCarrier, error) {
	var carrier models.TMSCarrier

	err := rds.WithContextReader(ctx).
		Where("service_id = ?", serviceID).
		First(&carrier).Error

	return &carrier, err
}

// GetByEmail retrieves a TMSCarrier by its primary email address and service ID.
// It returns the carrier and an error. If no carrier is found,
// gorm.ErrRecordNotFound will be returned.
func GetByEmail(ctx context.Context, serviceID uint, email string) (*models.TMSCarrier, error) {
	var carrier models.TMSCarrier

	err := rds.WithContextReader(ctx).
		Where("service_id = ? AND email = ?", serviceID, email).
		First(&carrier).Error
	if err != nil {
		return nil, err
	}

	return &carrier, nil
}
