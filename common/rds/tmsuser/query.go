package tmsuser

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

type TMSInfo struct {
	RevenueCode   string `json:"revenueCode"`
	ExternalTMSID string `json:"externalTMSID"`
}

type UserInfo struct {
	EmailAddress string
	Name         string
	ServiceID    uint
}

func GetUsersByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSUser, err error) {
	db := rds.WithContextReader(ctx).Where("tms_id = ?", query.TMSID)

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

func GetByDrumkitUserID(ctx context.Context, drumkitUserID uint, tmsID uint) (res *models.TMSUser, err error) {
	// get users email_address from the user table
	var emailAddress string
	err = rds.WithContextReader(ctx).Model(&models.User{}).
		Where("id = ?", drumkitUserID).
		Select("email_address").
		First(&emailAddress).Error
	if err != nil {
		return nil, err
	}

	// get tms_user by email_address
	err = rds.WithContextReader(ctx).Model(&models.TMSUser{}).
		Where("email_address = ? AND tms_id = ?", emailAddress, tmsID).
		First(&res).Error
	if err != nil {
		return nil, err
	}

	return res, nil
}

// Case-insensitive search
func GetByUsername(ctx context.Context, username string, tmsID uint) (res models.TMSUser, err error) {
	return res, rds.WithContextReader(ctx).
		Where("lower(username) = ? AND tms_id = ?", strings.ToLower(username), tmsID).
		First(&res).Error
}

// Case insensitive search
func GetByEmail(ctx context.Context, emailAddress string, tmsID uint) (res models.TMSUser, err error) {
	return res, rds.WithContextReader(ctx).
		Where("lower(email_address) = ? AND tms_id = ?", strings.ToLower(emailAddress), tmsID).
		First(&res).Error
}

func GetTMSInfo(ctx context.Context, userID uint) (resp *TMSInfo, err error) {
	// get users email_address, name, and service_id from the user table
	var userInfo UserInfo
	err = rds.WithContextReader(ctx).Model(&models.User{}).
		Where("id = ?", userID).
		Select("email_address, name, service_id").
		First(&userInfo).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// get TMS integration for the service
	tmsIntegrations, err := integrationDB.GetTMSListByServiceID(ctx, userInfo.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	if len(tmsIntegrations) == 0 {
		return nil, nil
	}

	// Use the first TMS integration (there's typically only one per service)
	tmsIntegrationID := tmsIntegrations[0].ID

	// Search tms_user table with preference order:
	// 1. Both name and email match
	// 2. Email match only
	// 3. Name match only
	resp = &TMSInfo{}

	err = rds.WithContextReader(ctx).Model(&models.TMSUser{}).
		Where("tms_id = ? AND lower(email_address) = ? AND lower(username) = ?",
			tmsIntegrationID, strings.ToLower(userInfo.EmailAddress), strings.ToLower(userInfo.Name)).
		Select("revenue_code, external_tms_id").
		First(&resp).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	err = rds.WithContextReader(ctx).Model(&models.TMSUser{}).
		Where("tms_id = ? AND lower(email_address) = ?",
			tmsIntegrationID, strings.ToLower(userInfo.EmailAddress)).
		Select("revenue_code, external_tms_id").
		First(&resp).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	err = rds.WithContextReader(ctx).Model(&models.TMSUser{}).
		Where("tms_id = ? AND lower(username) = ?",
			tmsIntegrationID, strings.ToLower(userInfo.Name)).
		Select("revenue_code, external_tms_id").
		First(&resp).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return resp, nil
}

func SearchByTMSID(ctx context.Context, query rds.GenericSearchQuery) (res []models.TMSUser, err error) {
	var userSearchThreshold = 0.3

	return res, rds.WithContextReader(ctx).
		Scopes(fuzzyMatch(ctx, query.Search, query.TMSID, userSearchThreshold)).
		Find(&res).Error
}

func fuzzyMatch(
	ctx context.Context,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		const columnName = "username"

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH tms_user_distances AS (
					SELECT *,
						%[1]s <-> @searchTerm AS trgm_dist,
						%[1]s ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM tms_users
					WHERE tms_id = @tmsID
					-- Exclude customers whose similarity to searchTerm is lower than threshold
					AND (%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%')

				)
				SELECT *
				FROM tms_user_distances
				ORDER BY
					is_match DESC,
					trgm_dist;
			`, columnName)

			// NOTE: Scopes() and Raw() cannot be used to chain multiple fuzzy queries together
			// (like load advanced search) because the queries are executed in a single transaction
			// but this usage is fine
			return tx.Raw(query, sql.Named("searchTerm", searchTerm), sql.Named("tmsID", tmsID))
		})
	}
}
