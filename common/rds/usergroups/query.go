package usergroups

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByID(ctx context.Context, groupID uint) (userGroup models.UserGroup, err error) {
	return userGroup, rds.WithContextReader(ctx).
		Where("id = ?", groupID).
		Where("deleted_at IS NULL").
		First(&userGroup).Error
}

func GetByServiceID(ctx context.Context, serviceID uint) (userGroups []models.UserGroup, err error) {
	return userGroups, rds.WithContextReader(ctx).
		Where("service_id = ?", serviceID).
		Where("deleted_at IS NULL").
		Find(&userGroups).Error
}

func GetByUserID(ctx context.Context, userID uint) (userGroup models.UserGroup, err error) {
	return userGroup, rds.WithContextReader(ctx).
		Raw(
			`SELECT * FROM user_groups WHERE id IN (
				SELECT user_group_id FROM user_group_users WHERE user_id = ?
		)`, userID).
		First(&userGroup).Error
}

func GetAllGroupIDsByUserID(ctx context.Context, userID uint) (userGroupIDs []uint, err error) {
	return userGroupIDs, rds.WithContextReader(ctx).
		Raw(
			`SELECT id FROM user_groups WHERE id IN (
				SELECT user_group_id FROM user_group_users WHERE user_id = ?
		)`, userID).
		Pluck("id", &userGroupIDs).Error
}
