package batchquote

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func CreateBatchQuote(ctx context.Context, batchQuote *models.BatchQuote) error {
	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(batchQuote).Error
}

func BatchUpsertQuoteRequests(ctx context.Context, quoteRequests []models.QuoteRequest) error {
	if len(quoteRequests) == 0 {
		log.Debug(ctx, "skipping upsert for empty list of quote requests")
		return nil
	}

	return rds.WithContext(ctx).Clauses(clause.Returning{}).Create(&quoteRequests).Error
}

func CreateQuoteRequestForBatch(ctx context.Context, quoteRequest *models.QuoteRequest) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{}).
		Create(quoteRequest).
		First(quoteRequest).Error
}

func CreateQuickQuoteForBatch(ctx context.Context, quickQuote *models.QuickQuote) error {
	return rds.WithContext(ctx).Clauses(
		clause.Returning{}).
		Create(quickQuote).
		First(quickQuote).Error
}
