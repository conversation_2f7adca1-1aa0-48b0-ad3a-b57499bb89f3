package batchquote

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type UpdateBatchQuoteOptions struct {
	QuoteRequestIDs     []uint // If provided, will replace associations
	AppendQuoteRequests bool   // If true, append instead of replace

	// Field updates
	Status             *models.BatchQuoteStatus
	AverageConfidence  *float64
	ProcessingErrors   *models.BatchQuoteErrors
	TotalQuoteRequests *int

	UpdateAssociations  bool // Whether to handle associations
	UpdateSummaryFields bool // Whether to update summary fields
}

// UpdateBatchQuoteWithOptions provides a flexible way to update batch quotes with various options
func UpdateBatchQuoteWithOptions(ctx context.Context, batchQuoteID uint, opts UpdateBatchQuoteOptions) error {
	return rds.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Handle associations if requested
		if opts.UpdateAssociations && len(opts.QuoteRequestIDs) > 0 {
			// Create slice of quote request models with IDs
			quoteRequests := make([]models.QuoteRequest, len(opts.QuoteRequestIDs))
			for i, id := range opts.QuoteRequestIDs {
				quoteRequests[i].ID = id
			}

			// Update the many-to-many association
			batchQuote := &models.BatchQuote{}
			batchQuote.ID = batchQuoteID
			association := tx.Model(batchQuote).Association("QuoteRequests")

			if opts.AppendQuoteRequests {
				if err := association.Append(quoteRequests); err != nil {
					return err
				}
			} else {
				if err := association.Replace(quoteRequests); err != nil {
					return err
				}
			}
		}

		// Handle field updates
		updates := make(map[string]any)

		if opts.Status != nil {
			updates["status"] = *opts.Status
		}
		if opts.AverageConfidence != nil {
			updates["average_confidence"] = *opts.AverageConfidence
		}
		if opts.ProcessingErrors != nil {
			updates["processing_errors"] = *opts.ProcessingErrors
		}
		if opts.TotalQuoteRequests != nil {
			updates["total_quote_requests"] = *opts.TotalQuoteRequests
		} else if opts.UpdateSummaryFields && len(opts.QuoteRequestIDs) > 0 {
			updates["total_quote_requests"] = len(opts.QuoteRequestIDs)
		}

		// Apply updates if any
		if len(updates) > 0 {
			return tx.Model(&models.BatchQuote{}).
				Where("id = ?", batchQuoteID).
				Updates(updates).Error
		}

		return nil
	})
}

// UpdateBatchQuote updates only the BatchQuote fields without touching associations.
func UpdateBatchQuote(ctx context.Context, batchQuote *models.BatchQuote) error {
	return rds.WithContext(ctx).
		Clauses(clause.Returning{}).
		Omit("QuoteRequests").
		Save(batchQuote).Error
}

// AssociateQuoteRequestsToBatch associates multiple quote requests with a batch quote in a single operation
func AssociateQuoteRequestsToBatch(ctx context.Context, batchQuoteID uint, quoteRequestIDs []uint) error {
	if len(quoteRequestIDs) == 0 {
		return nil
	}

	return UpdateBatchQuoteWithOptions(
		ctx,
		batchQuoteID,
		UpdateBatchQuoteOptions{
			QuoteRequestIDs:     quoteRequestIDs,
			AppendQuoteRequests: true,
			UpdateAssociations:  true,
		},
	)
}
