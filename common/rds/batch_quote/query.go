package batchquote

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetBatchQuoteByID(ctx context.Context, id uint) (res models.BatchQuote, err error) {
	return res, rds.WithContextReader(ctx).
		Preload("QuoteRequests").
		Where("id = ?", id).
		First(&res).Error
}

func GetBatchQuoteByEmailID(ctx context.Context, emailID uint) (res *models.BatchQuote, err error) {
	return res, rds.
		WithContextReader(ctx).
		Preload("QuoteRequests").
		Order("created_at DESC").
		First(&res, "email_id = ?", emailID).Error
}

func GetBatchQuoteByThreadID(ctx context.Context, threadID string) (res *models.BatchQuote, err error) {
	return res, rds.
		WithContextReader(ctx).
		Preload("QuoteRequests").
		Order("created_at DESC").
		First(&res, "thread_id = ?", threadID).Error
}

func GetBatchQuotesByServiceID(ctx context.Context, serviceID uint,
) (res []*models.BatchQuote, err error) {
	return res, rds.
		WithContextReader(ctx).
		Preload("QuoteRequests").
		Where("service_id = ?", serviceID).
		Order("created_at DESC").
		Find(&res).Error
}

func GetBatchQuotesByUserID(ctx context.Context, userID uint,
) (res []*models.BatchQuote, err error) {
	return res, rds.
		WithContextReader(ctx).
		Preload("QuoteRequests").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&res).Error
}

func GetBatchQuotesByQuoteRequestIDs(
	ctx context.Context,
	quoteRequestIDs []uint,
) (res []*models.BatchQuote, err error) {

	subQuery := rds.WithContextReader(ctx).
		Model(&models.BatchQuoteQuoteRequest{}).
		Select("DISTINCT batch_quote_id").
		Where("quote_request_id IN ?", quoteRequestIDs)

	return res, rds.
		WithContextReader(ctx).
		Preload("QuoteRequests").
		Where("id IN (?)", subQuery).
		Order("created_at DESC").
		Find(&res).Error
}
