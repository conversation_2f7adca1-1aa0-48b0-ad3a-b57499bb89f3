package batchquote

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestLiveBatchQuoteRDS(t *testing.T) {
	if os.Getenv("TEST_LIVE_DB") != "true" {
		t.Skip("skipping TestLiveBatchQuoteRDS: run with TEST_LIVE_DB=true to enable")
		return
	}

	ctx := rds.SetupTestDB(t)
	service := rds.CreateTestService(ctx, t)
	user := rds.CreateTestUser(ctx, t, service.ID)
	email := rds.CreateTestEmail(ctx, t, service.ID, user.ID)

	// Create batch quote with no associated quote requests
	batchQuote := rds.CreateTestBatchQuote(ctx, t, user.ID, service.ID, email.ID, CreateBatchQuote)

	// Create 3 quote requests
	quoteRequests := rds.CreateTestQuoteRequests(ctx, t, user.ID, service.ID, email.ID, 3, BatchUpsertQuoteRequests)

	// Associate quote requests 0 and 1 to batch quote
	err := AssociateQuoteRequestsToBatch(ctx, batchQuote.ID, []uint{quoteRequests[0].ID, quoteRequests[1].ID})
	require.NoError(t, err)
	batchQuote.QuoteRequests = quoteRequests

	// Update batch quote with options (replace quote requests 0 and 1 with 2)
	// Start: Batch quote has 2 quote requests (quoteRequests[0] and quoteRequests[1]) associated
	// End: Batch quote has 1 quote request (quoteRequests[2]) associated
	t.Run("Update batch quote with options (replace quote requests)", func(t *testing.T) {
		ids := []uint{quoteRequests[2].ID}
		opts := UpdateBatchQuoteOptions{
			QuoteRequestIDs:    ids,
			UpdateAssociations: true,
		}
		err := UpdateBatchQuoteWithOptions(ctx, batchQuote.ID, opts)
		require.NoError(t, err)
		updatedBatchQuote, err := GetBatchQuoteByID(ctx, batchQuote.ID)
		require.NoError(t, err)
		assert.Len(t, updatedBatchQuote.QuoteRequests, 1)
		assert.Equal(t, quoteRequests[2].ID, updatedBatchQuote.QuoteRequests[0].ID)
	})

	// Update batch quote with options (append quote requests 1)
	// Start: Batch quote has only 1 quote request (quoteRequests[2]) associated
	// End: Batch quote has 2 quote requests (quoteRequests[2] and quoteRequests[1]) associated
	t.Run("Update batch quote with options (append)", func(t *testing.T) {
		ids := []uint{quoteRequests[1].ID}
		opts := UpdateBatchQuoteOptions{
			QuoteRequestIDs:     ids,
			AppendQuoteRequests: true,
			UpdateAssociations:  true,
		}
		err := UpdateBatchQuoteWithOptions(ctx, batchQuote.ID, opts)
		require.NoError(t, err)

		updatedBatchQuote, err := GetBatchQuoteByID(ctx, batchQuote.ID)
		require.NoError(t, err)
		assert.Len(t, updatedBatchQuote.QuoteRequests, 2)
	})

	// Update batch quote fields only
	// Start: Batch quote status is Processed
	// End: Batch quote status is Submitted
	t.Run("Update batch quote fields only", func(t *testing.T) {
		batchQuote.Status = models.BatchQuoteStatusSubmitted
		err := UpdateBatchQuote(ctx, &batchQuote)
		require.NoError(t, err)

		updated, err := GetBatchQuoteByID(ctx, batchQuote.ID)
		require.NoError(t, err)
		assert.Equal(t, models.BatchQuoteStatusSubmitted, updated.Status)
	})

	// Associate quote requests 0 and 1 to batch quote
	// Note quote request 1 is already associated with batch quote from a previous test
	// Start: Batch quote has 2 quote requests (quoteRequests[2] and quoteRequests[1]) associated
	// End: Batch quote has 3 quote requests (quoteRequests[2], quoteRequests[1], and quoteRequests[0]) associated
	t.Run("Associate quote requests to batch", func(t *testing.T) {
		ids := []uint{quoteRequests[0].ID, quoteRequests[1].ID}
		err := AssociateQuoteRequestsToBatch(ctx, batchQuote.ID, ids)
		require.NoError(t, err)

		updated, err := GetBatchQuoteByID(ctx, batchQuote.ID)
		require.NoError(t, err)
		assert.Len(t, updated.QuoteRequests, 3)
	})

	// Get batch quotes using various methods
	t.Run("Get batch quote queries", func(t *testing.T) {
		tcs := []struct {
			name string
			run  func(t *testing.T)
		}{
			{
				name: "by ID",
				run: func(t *testing.T) {
					batchQuote, err := GetBatchQuoteByID(ctx, batchQuote.ID)
					require.NoError(t, err)
					assert.NotZero(t, batchQuote.ID)
				},
			},
			{
				name: "by EmailID",
				run: func(t *testing.T) {
					batchQuote, err := GetBatchQuoteByEmailID(ctx, email.ID)
					require.NoError(t, err)
					assert.NotNil(t, batchQuote)
					assert.NotZero(t, batchQuote.ID)
				},
			},
			{
				name: "by ThreadID",
				run: func(t *testing.T) {
					batchQuote, err := GetBatchQuoteByThreadID(ctx, batchQuote.ThreadID)
					require.NoError(t, err)
					assert.NotNil(t, batchQuote)
					assert.NotZero(t, batchQuote.ID)
				},
			},
			{
				name: "by ServiceID",
				run: func(t *testing.T) {
					batchQuotes, err := GetBatchQuotesByServiceID(ctx, service.ID)
					require.NoError(t, err)
					assert.NotEmpty(t, batchQuotes)
				},
			},
			{
				name: "by UserID",
				run: func(t *testing.T) {
					batchQuotes, err := GetBatchQuotesByUserID(ctx, user.ID)
					require.NoError(t, err)
					assert.NotEmpty(t, batchQuotes)
				},
			},
			{
				name: "by QuoteRequestIDs",
				run: func(t *testing.T) {
					ids := []uint{quoteRequests[0].ID, quoteRequests[1].ID}
					batchQuotes, err := GetBatchQuotesByQuoteRequestIDs(ctx, ids)
					require.NoError(t, err)
					assert.NotEmpty(t, batchQuotes)
				},
			},
		}
		for _, tc := range tcs {
			t.Run(tc.name, tc.run)
		}
	})
}
