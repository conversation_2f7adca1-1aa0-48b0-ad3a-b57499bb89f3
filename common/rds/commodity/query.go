package commodity

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByLoadID(ctx context.Context, loadID string) (commodities []models.Commodity, err error) {
	return commodities, rds.WithContextReader(ctx).Where("load_id = ?", loadID).Find(&commodities).Error

}

func Get(ctx context.Context, id uint) (commodity models.Commodity, err error) {
	return commodity, rds.WithContextReader(ctx).Where("id = ?", id).First(&commodity).Error
}
