package tmscustomer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/similarity"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

var ErrCustomerNotFound = errors.New("customer not found")

type SearchCustomersQuery struct {
	TMSID uint `json:"tmsID"`
	models.CompanyCoreInfo
}

// Get customers by TMS ID, ordered by name in ascending order
func GetTMSCustomersByTMSID(ctx context.Context, query rds.GenericGetQuery) (res []models.TMSCustomer, err error) {
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", query.TMSID).Order("name ASC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	return res, db.Find(&res).Error
}

// GetCustomerNamesByIDs gets multiple customer names by their IDs in a single query
func GetCustomerNamesByIDs(ctx context.Context, customerIDs []uint) (map[uint]string, error) {
	if len(customerIDs) == 0 {
		return map[uint]string{}, nil
	}

	// Define a struct to hold the query results
	type CustomerName struct {
		ID   uint   `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}

	var results []CustomerName

	// Execute a single query with the IN clause
	err := rds.WithContextReader(ctx).
		Model(&models.TMSCustomer{}).
		Where("id IN (?)", customerIDs).
		Select("id, name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Convert the results to a map
	customerNames := make(map[uint]string, len(results))
	for _, result := range results {
		customerNames[result.ID] = result.Name
	}

	return customerNames, nil
}

func GetCustomerByID(ctx context.Context, id uint) (res models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Where("id = ?", id).
		First(&res).Error

	return res, err
}

func GetByExternalTMSID(ctx context.Context, tmsID uint, externalTMSID string) (res models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND external_tms_id = ?", tmsID, externalTMSID).
		First(&res).Error

	return res, err
}

func GetCustomerEmailByID(ctx context.Context, id uint) (res string, err error) {
	var customer models.TMSCustomer

	err = rds.WithContextReader(ctx).Where("id = ?", id).First(&customer).Error
	if err != nil {
		return "", err
	}

	return customer.Email, nil
}

func GetCustomerNameByID(ctx context.Context, id uint) (res string, err error) {
	var customer models.TMSCustomer
	err = rds.WithContextReader(ctx).Where("id = ?", id).First(&customer).Error
	if err != nil {
		return "", err
	}

	return customer.Name, nil
}

// This function is used by the "Name" autocomplete search input on the FE
func FuzzySearchByName(ctx context.Context, query SearchCustomersQuery) (res []models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchName(ctx, query.Name, query.TMSID, 0.1)).
		Find(&res).Error

	log.Info(ctx, "found customers by name fuzzy match", zap.Int("count", len(res)))

	return res, err
}

// This function is used by the "Address Line 1" autocomplete search input on the FE
func FuzzySearchByStreetAddress(
	ctx context.Context,
	query SearchCustomersQuery,
) (res []models.TMSCustomer, err error) {

	var customerAddressSearchThreshold = 0.1
	if len(query.AddressLine1) < 3 {
		return res, nil
	}

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchStreetAddress(ctx, query.AddressLine1, query.TMSID, customerAddressSearchThreshold)).
		Find(&res).Error

	log.Info(ctx, "found customers by street fuzzy match", zap.Int("count", len(res)))

	return res, err
}

// This function is part of the LLM load building extraction process and uses a higher similarity threshold
// than search function to match customers by name
func GetCustomerByName(ctx context.Context, tmsID uint, name string) (res models.TMSCustomer, err error) {
	var customers []models.TMSCustomer
	var customerNameSearchThreshold = 0.4

	if len(name) < 3 {
		return res, nil
	}

	// Try ILIKE match first
	err = rds.WithContextReader(ctx).
		Where("tms_integration_id = ? AND name ILIKE '%' || ? || '%'", tmsID, name).
		Find(&customers).Error

	if err != nil {
		return res, err
	}

	// Handle simple cases first
	switch len(customers) {
	case 0:
		// No exact matches, try fuzzy matching
		return getCustomerByFuzzyMatch(ctx, tmsID, name, customerNameSearchThreshold)
	case 1:
		// Single exact match - return it
		return customers[0], nil
	}

	// Multiple exact matches - apply tiebreaker
	log.Info(ctx, "Multiple customers found, applying tiebreaker",
		zap.Int("count", len(customers)),
		zap.String("searchName", name))

	bestMatch := findBestCustomerMatch(ctx, name, customers)

	log.Info(ctx, "Tiebreaker selected customer",
		zap.String("searchName", name),
		zap.String("selectedName", bestMatch.Name))

	return bestMatch, nil
}

// getCustomerByFuzzyMatch performs fuzzy matching when no exact matches are found
func getCustomerByFuzzyMatch(
	ctx context.Context,
	tmsID uint,
	name string,
	threshold float64,
) (res models.TMSCustomer, err error) {
	var customers []models.TMSCustomer

	err = rds.WithContextReader(ctx).
		Scopes(fuzzyMatchName(ctx, name, tmsID, threshold)).
		Find(&customers).Error

	if err != nil {
		return res, err
	}

	// Handle fuzzy match results
	switch len(customers) {
	case 0:
		return res, ErrCustomerNotFound
	case 1:
		return customers[0], nil
	}

	// Multiple fuzzy matches - apply tiebreaker
	log.Info(ctx, "Multiple customers found by fuzzy match, applying tiebreaker",
		zap.Int("count", len(customers)),
		zap.String("searchName", name))

	bestMatch := findBestCustomerMatch(ctx, name, customers)

	log.Info(ctx, "Fuzzy match tiebreaker selected customer",
		zap.String("searchName", name),
		zap.String("selectedName", bestMatch.Name))

	return bestMatch, nil
}

// findBestCustomerMatch finds the best matching customer by name using the string similarity package
func findBestCustomerMatch(ctx context.Context, searchName string, candidates []models.TMSCustomer) models.TMSCustomer {
	if len(candidates) == 0 {
		return models.TMSCustomer{}
	}

	if len(candidates) == 1 || searchName == "" {
		return candidates[0]
	}

	// Use name matching configuration optimized for company names
	config := similarity.NameMatchingConfig()

	bestMatch := candidates[0]
	bestScore := 0.0

	normalizedSearchName := normalizeCustomerName(searchName)

	for _, candidate := range candidates {
		normalizedCandidateName := normalizeCustomerName(candidate.Name)
		score := similarity.CalculateSimilarity(normalizedSearchName, normalizedCandidateName, config)
		if score > bestScore {
			bestScore = score
			bestMatch = candidate
		}
	}

	// Log low confidence matches for monitoring
	if bestScore < 0.3 {
		log.WarnNoSentry(
			ctx,
			"Low confidence customer match found",
			zap.String("searchName", searchName),
			zap.String("matchedName", bestMatch.Name),
			zap.Float64("score", bestScore),
			zap.Int("candidates", len(candidates)),
		)
	}

	return bestMatch
}

// normalizeCustomerName performs normalization for customer/company names
// to improve similarity matching. This preserves legal suffixes (Inc, LLC, Corp)
// to allow differentiation between similar company names in tiebreakers.
func normalizeCustomerName(name string) string {
	if name == "" {
		return ""
	}

	// Convert to lowercase for case-insensitive matching
	normalized := strings.ToLower(name)

	// Remove common punctuation but keep the words intact
	// This helps match "ABC, Inc." with "ABC Inc" but preserves "Inc"
	normalized = regexp.MustCompile(`[.,;:!?'"()\-]`).ReplaceAllString(normalized, " ")

	// Convert & to "and" for better matching
	normalized = strings.ReplaceAll(normalized, "&", " and ")

	// Normalize whitespace - remove extra spaces and trim
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")
	normalized = strings.TrimSpace(normalized)

	return normalized
}

func fuzzyMatchName(
	ctx context.Context,
	name string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "name", name, tmsID, similarityThreshold)
}

func fuzzyMatchStreetAddress(
	ctx context.Context,
	addressLine1 string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {
	return fuzzyMatchByField(ctx, "address_line1", addressLine1, tmsID, similarityThreshold)
}

// Helper function to fuzzy match a customer by a column name
func fuzzyMatchByField(
	ctx context.Context,
	columnName,
	searchTerm string,
	tmsID uint,
	similarityThreshold float64,
) func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if searchTerm == "" {
			return db
		}

		return db.Scopes(func(tx *gorm.DB) *gorm.DB {
			if err := rds.SetSimilarityThreshold(ctx, tx, similarityThreshold); err != nil {
				log.Errorf(ctx, "failed to set similarity threshold: %w", err)
				return db
			}

			query := fmt.Sprintf(`
				WITH tms_customer_distances AS (
					SELECT *,
						%[1]s <-> @searchTerm AS trgm_dist,
						%[1]s ILIKE '%%' || @searchTerm || '%%' AS is_match
					FROM tms_customers
					WHERE tms_integration_id = @tmsID
					-- Exclude customers whose similarity to searchTerm is lower than threshold
					AND (%[1]s %% @searchTerm OR %[1]s ILIKE '%%' || @searchTerm || '%%')
				)
				SELECT *
				FROM tms_customer_distances
				ORDER BY
					is_match DESC,
					trgm_dist
				LIMIT 20;
			`, columnName)

			// NOTE: Scopes() and Raw() cannot be used to chain multiple fuzzy queries together
			// (like load advanced search) because the queries are executed in a single transaction
			// but this usage is fine
			return tx.Raw(query, sql.Named("searchTerm", searchTerm), sql.Named("tmsID", tmsID))
		})
	}
}

// GetByID returns a TMSCustomer by its ID
func GetByID(ctx context.Context, id uint) (res models.TMSCustomer, err error) {
	err = rds.WithContextReader(ctx).
		Where("id = ?", id).
		First(&res).Error
	return res, err
}
