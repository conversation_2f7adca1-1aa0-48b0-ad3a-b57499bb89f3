package email

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByID(ctx context.Context, id uint) (models.Email, error) {
	var email models.Email
	return email, rds.WithContextReader(ctx).First(&email, id).Error
}

func GetByIDPreload(ctx context.Context, id uint) (email models.Email, err error) {
	return email, rds.WithContextReader(ctx).Preload("Loads").First(&email, id).Error
}

func GetByIDPreloadWithLoadsAndVectors(ctx context.Context, id uint) (email models.Email, err error) {
	return email, rds.WithContext(ctx).Preload("Loads").Preload("Vectors").First(&email, id).Error
}

func GetEmailByExternalID(ctx context.Context, externalID string) (*models.Email, error) {
	var email models.Email

	err := rds.WithContextReader(ctx).
		Where("external_id = ?", externalID).
		Preload("Loads").
		First(&email).Error

	if err != nil {
		return nil, err
	}

	return &email, nil
}

func GetAttachmentsFromThreadID(ctx context.Context, id string) ([]models.Attachment, error) {
	var emails []models.Email
	var attachments []models.Attachment

	err := rds.WithContextReader(ctx).
		Select("attachments").
		Where("thread_id = ?", id).
		Order("created_at DESC").
		Limit(20).
		Find(&emails).Error
	if err != nil {
		return nil, err
	}

	if len(emails) == 0 {
		return nil, errors.New("no emails found")
	}

	for _, email := range emails {
		attachments = append(attachments, email.Attachments...)
	}

	if len(attachments) == 0 {
		return nil, errors.New("no attachments found for the given thread ID within the retrieved emails")
	}

	return attachments, err
}

func GetSignatures(ctx context.Context, sender string, pool int) ([]string, error) {
	var emails []models.Email
	var signatures []string

	err := rds.WithContextReader(ctx).
		Select("signature").
		Where("sender = ?", sender).
		Order("sent_at DESC").
		Limit(pool).
		Find(&emails).Error
	if err != nil {
		return nil, err
	}

	if len(emails) == 0 {
		return nil, errors.New("no emails found")
	}

	// Extract unique signatures from the fetched emails
	signatureMap := make(map[string]bool)
	for _, email := range emails {
		if email.Signature != "" {
			signatureMap[email.Signature] = true
		}
	}

	// Convert map keys to slice
	for sig := range signatureMap {
		signatures = append(signatures, sig)
	}

	if len(signatures) == 0 {
		return nil, errors.New("no signatures found")
	}

	return signatures, nil
}

func GetOnPremEmailByExternalID(ctx context.Context, externalID string) (*models.OnPremEmail, error) {
	var email models.OnPremEmail

	err := rds.WithContextReader(ctx).
		Where("external_id = ?", externalID).
		First(&email).Error

	return &email, err
}

// Return all onprem emails associated with the threadID
func GetAllOnPremEmailsByThreadID(ctx context.Context, threadID string) ([]models.OnPremEmail, error) {
	var emails []models.OnPremEmail
	return emails, rds.WithContextReader(ctx).Where("thread_id = ?", threadID).Find(&emails).Error
}

// Return the most recent email associated with the threadID
func GetEmailByThreadID(ctx context.Context, threadID string) (*models.Email, error) {
	var email models.Email

	err := rds.WithContextReader(ctx).
		Where("thread_id = ?", threadID).
		Order("sent_at DESC").
		Preload("Loads").
		First(&email).Error

	if err != nil {
		return nil, err
	}

	return &email, nil
}

func GetNumberOfEmailsByThreadIDAndUserID(ctx context.Context, threadID string, userID uint) (int, error) {
	var count int64
	err := rds.WithContextReader(ctx).
		Model(&models.Email{}).
		Where("thread_id = ?", threadID).
		Where("user_id = ?", userID).
		Count(&count).Error

	return int(count), err
}

func GetByRFCIDPreload(ctx context.Context, rfcID string, serviceID uint) (res *models.Email, err error) {
	res = &models.Email{}

	err = rds.WithContextReader(ctx).
		Preload(clause.Associations).
		// For some reason, rfc_message_id = ? doesn't always work, but LIKE (with no % wildcards) does ¯\_(ツ)_/¯
		Where("rfc_message_id LIKE ? AND service_id = ?", rfcID, serviceID).
		First(res).Error

	return res, err
}

// NOTE: RFCMessageID is the same for all recipients, so we must search by user ID *and* RFC ID to specify the email
// Drumkit generated on behalf of that user
// TODO: Move quick quote emails to GeneratedEmails table, deprecate
func GetBeaconGeneratedEmailByRFCID(ctx context.Context, userID uint, rfcID string) (email *models.Email, err error) {
	email = &models.Email{}

	return email, rds.WithContextReader(ctx).
		Where("user_id = ? AND rfc_message_id = ? AND beacon_generated = TRUE", userID, rfcID).
		First(email).Error
}

// Return the email Drumkit generated associated with the threadID
// TODO: Move quick quote emails to GeneratedEmails table, deprecate
func GetBeaconGeneratedEmailByThreadID(ctx context.Context, threadID string) (*models.Email, error) {
	var email models.Email

	err := rds.WithContextReader(ctx).
		Where("thread_id = ? AND beacon_generated = TRUE", threadID).
		Order("sent_at ASC").
		First(&email).Error

	return &email, err
}

// Return the latest email_id associated with the threadID
func GetLatestEmailIDForThread(ctx context.Context, threadID string) (uint, error) {
	var emailID uint

	err := rds.WithContextReader(ctx).
		Model(&models.Email{}).
		Select("id").
		Where("thread_id = ?", threadID).
		Order("sent_at DESC").
		First(&emailID).Error

	return emailID, err
}

// TransformSharedThread returns the transformed threadID for the user's copy of the email.
// If `threadID` already belongs to `userID`, then the same threadID is returned.
//
// Additional context: This enables delegated/shared inboxes, so User B can see thread from shared inbox A.
// However, Gmail (and Outlook?) generate unique message and threadIDs for each user's copy of the email,
// so we need to transform the threadID to match the user's copy using the cross-user, globally unique RFC ID.
// https://linear.app/drumkit/issue/ENG-3280/allow-other-users-to-view-cq-initiated-by-other-user#comment-78da709e
func TransformSharedThread(
	ctx context.Context,
	userID uint,
	threadID string,
	service *models.Service,
) (transformedThreadID string, err error) {

	if !service.IsDelegatedInboxEnabled {
		log.Warn(
			ctx,
			"service can't transform email to shared thread",
			zap.Bool("delegatedInboxEnabled", service.IsDelegatedInboxEnabled),
		)

		return threadID, nil
	}

	err = rds.WithContextReader(ctx).Raw(
		`-- Returns 1 unique thread ID from the user's emails
		SELECT DISTINCT thread_id
		FROM emails
		WHERE user_id = ? AND rfc_message_id IN (
			-- A thread contains 1+ emails and thus 1+ RFC IDs for the thread
			SELECT rfc_message_id
			FROM emails
			WHERE service_id = ? AND thread_id = ?
		)`, userID, service.ID, threadID).
		Scan(&transformedThreadID).Error

	// If thread's userID = requesting user's ID, then the original threadID is returned
	return transformedThreadID, err
}

// GetLastEmailsForUsers returns the most recent email for each user within the last 30 days
// This is a batch version of GetLastEmailTimeForUser to avoid N+1 queries
func GetLastEmailsForUsers(ctx context.Context, userIDs []uint) (map[uint]*time.Time, error) {
	if len(userIDs) == 0 {
		return make(map[uint]*time.Time), nil
	}

	// Use MAX aggregation with the composite index for efficient batch queries
	var results []struct {
		UserID     uint       `gorm:"column:user_id"`
		LastSentAt *time.Time `gorm:"column:last_sent_at"`
	}

	err := rds.WithContextReader(ctx).
		Model(&models.Email{}).
		Select("user_id, MAX(sent_at) as last_sent_at").
		Where("user_id IN ? AND sent_at > NOW() - INTERVAL '30 days'", userIDs).
		Group("user_id").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	// Convert to map for easy lookup
	result := make(map[uint]*time.Time)
	for _, row := range results {
		result[row.UserID] = row.LastSentAt
	}

	return result, nil
}
