package quote

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByID(ctx context.Context, id uint) (models.QuickQuote, error) {
	var quote models.QuickQuote
	return quote, rds.WithContextReader(ctx).First(&quote, id).Error
}

func GetAll(ctx context.Context, serviceID uint) ([]models.QuickQuote, error) {
	var quotes []models.QuickQuote
	return quotes, rds.WithContextReader(ctx).Where("service_id = ?", serviceID).Find(&quotes).Error
}

// BatchGetByQuoteRequestIDs gets quotes for multiple quote request IDs in batches
// to efficiently handle large numbers of IDs
func BatchGetByQuoteRequestIDs(ctx context.Context, quoteRequestIDs []uint) (map[uint][]models.QuickQuote, error) {
	if len(quoteRequestIDs) == 0 {
		return map[uint][]models.QuickQuote{}, nil
	}

	result := make(map[uint][]models.QuickQuote)

	// Process in chunks to avoid database limitations
	const chunkSize = 1000

	// Process the IDs in chunks
	for i := 0; i < len(quoteRequestIDs); i += chunkSize {
		end := min(i+chunkSize, len(quoteRequestIDs))

		chunk := quoteRequestIDs[i:end]

		// Execute a query for this chunk of IDs
		var quotes []models.QuickQuote
		err := rds.WithContextReader(ctx).
			Where("quote_request_id IN (?)", chunk).
			Find(&quotes).Error

		if err != nil {
			return nil, err
		}

		// Group quotes from this chunk by their quote request ID
		for _, quote := range quotes {
			result[quote.QuoteRequestID] = append(result[quote.QuoteRequestID], quote)
		}
	}

	return result, nil
}
