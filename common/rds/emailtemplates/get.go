package emailtemplates

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetEmailTemplateByTypeAndIDs(
	ctx context.Context,
	userID uint,
	userGroupID []uint,
	serviceID uint,
	templateTypes []models.EmailTemplateType,
) (templates []models.EmailTemplate, err error) {

	// First try to find a user-specific template
	err = rds.WithContextReader(ctx).
		Where("user_id = ?", userID).
		Where("template_type IN ?", templateTypes).
		Find(&templates).Error

	if err == nil && len(templates) > 0 {
		return templates, nil
	}

	// Then, if user groups are provided, try to find a user group template
	if len(userGroupID) > 0 {
		err = rds.WithContextReader(ctx).
			Where("user_group_id IN ?", userGroupID).
			Where("template_type IN ?", templateTypes).
			Find(&templates).Error

		if err == nil && len(templates) > 0 {
			return templates, nil
		}
	}

	// If no user or user group template found, fall back to service template
	err = rds.WithContextReader(ctx).
		Where("service_id = ?", serviceID).
		Where("template_type IN ?", templateTypes).
		Find(&templates).Error

	return templates, err
}

func GetAllAccessibleEmailTemplatesByTypeForUser(
	ctx context.Context,
	userID uint,
	userGroupIDs []uint,
	serviceID uint,
	templateType models.EmailTemplateType,
) (templates []models.EmailTemplate, err error) {

	err = rds.WithContextReader(ctx).
		Where("(user_id = ? OR user_group_id IN ? OR service_id = ?)", userID, userGroupIDs, serviceID).
		Where("template_type = ?", templateType).
		Find(&templates).Error

	return templates, err
}
