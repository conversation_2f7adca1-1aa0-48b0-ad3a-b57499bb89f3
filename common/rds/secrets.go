package rds

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/keyvault/azsecrets"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
)

func readAWSSecret(ctx context.Context, secretID, region string) (map[string]any, error) {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(region))
	if err != nil {
		return nil, err
	}

	client := secretsmanager.NewFromConfig(cfg)
	result, err := client.GetSecretValue(ctx, &secretsmanager.GetSecretValueInput{SecretId: &secretID})
	if err != nil {
		return nil, fmt.Errorf("failed to get secret %s: %w", secretID, err)
	}

	var creds map[string]any
	if err := json.Unmarshal([]byte(aws.ToString(result.SecretString)), &creds); err != nil {
		return nil, fmt.Errorf("JSON unmarshal failed for secret %s: %w", secretID, err)
	}

	log.Info(ctx, "loaded RDS credentials", zap.String("secretId", secretID))

	return creds, nil
}

func ReadAzureSecret(ctx context.Context, keyVaultName string, secretNames []string) (map[string]any, error) {
	vaultURL := fmt.Sprintf("https://%s.vault.azure.net/", keyVaultName)

	cred, err := azidentity.NewDefaultAzureCredential(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to obtain a credential: %w", err)
	}

	client, err := azsecrets.NewClient(vaultURL, cred, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create secrets client: %w", err)
	}

	secrets := make(map[string]any, len(secretNames))

	for _, secretName := range secretNames {
		resp, err := client.GetSecret(ctx, secretName, "", nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get secret %s: %w", secretName, err)
		}

		secrets[secretName] = *resp.Value
	}

	return secrets, nil
}
