# TMSLocation DB Operations

Last updated Feb 2025 by <PERSON>

### `common/rds/tms_location/query.go`

- makes use of [PostGIS](https://postgis.net/) (a PostgreSQL extension for storing, indexing, and querying geospatial data)

- In `SearchLocationsByRadius()` we use [`ST_DWithin()`](https://postgis.net/docs/ST_DWithin.html) to see if a location is
  within a given distance

- SRID 4326, WGS84 - World Geodetic System 1984, which is the SRS used in GPS.

### Links:

1. [<PERSON>'s Lunch and Learn on PostGIS](https://www.notion.so/drumkitai/Working-with-Geospatial-Data-4f0fc64c439f4194b6c9ce2ee886d274#16638062ea874142b21c2a571b580dda)
2. [Linear Ticket Introducing RadiusSearch and PostGIS functionality](https://linear.app/drumkit/issue/ENG-2993/geospatial-search-database-for-candidate-carriers-based-on-locations)
   - See <PERSON>'s comments with more links to code examples from Drumkit's older product (Axle API) that used PostGIS

# TMS Location Search Optimization

This package provides optimized search functionality for TMS locations with nearly a million records.

## Performance Features

### 1. Enhanced Fuzzy Search

- **Multi-stage search strategy**: Exact match → Fuzzy match → Fallback
- **Adaptive thresholds**: Automatically adjusts similarity thresholds based on search term characteristics
- **Better ranking algorithm**: Considers multiple factors including exact matches, word boundaries, and length similarity
- **Search term preprocessing**: Normalizes common abbreviations (St → Street, Ave → Avenue, etc.)

### 2. Database Optimization

#### PostgreSQL Extensions

Ensure the following extensions are enabled:

```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```

### 3. Search Strategy

The enhanced search follows this multi-stage approach:

1. **Stage 1: Exact/Prefix Match** (fastest)

   - Looks for exact matches and prefix matches using ILIKE
   - Returns immediately if good results found
   - Typical response time: < 50ms

2. **Stage 2: Adaptive Fuzzy Search**

   - Uses trigram similarity with adaptive thresholds
   - Threshold calculated based on search term length and word count
   - Typical response time: 50-200ms

3. **Stage 3: Fallback Search** (if needed)
   - Uses lower threshold for broader results on short terms
   - Only triggered if < 3 results found in Stage 2
   - Typical response time: 100-300ms

### 4. Search Term Processing

The system automatically:

- Normalizes common address abbreviations
- Handles case-insensitive matching
- Cleans up extra whitespace
- Applies word boundary matching for better precision

### 5. Performance Targets

- **Sub-200ms** response time for most queries
- **Sub-100ms** for exact/prefix matches
- **< 3 iterations** to find relevant results
- **20 result limit** to maintain consistent performance

## Future Improvements

Potential enhancements for even better performance:

1. **Caching layer** for common search terms
2. **Search result pre-computation** for popular queries
3. **Machine learning ranking** based on user interaction patterns
4. **Elasticsearch integration** for more advanced text search capabilities

-- Performance monitoring queries to run after index creation:

-- 1. Check index sizes
/_
SELECT
schemaname,
tablename,
indexname,
pg_size_pretty(pg_relation_size(indexrelid)) AS index_size
FROM pg_stat_user_indexes
WHERE tablename = 'tms_locations'
ORDER BY pg_relation_size(indexrelid) DESC;
_/

-- 2. Monitor index usage
/_
SELECT
schemaname,
tablename,
indexname,
idx_scan,
idx_tup_read,
idx_tup_fetch
FROM pg_stat_user_indexes
WHERE tablename = 'tms_locations'
ORDER BY idx_scan DESC;
_/

-- 3. Check for unused indexes (run after some time in production)
/_
SELECT
schemaname,
tablename,
indexname,
idx_scan
FROM pg_stat_user_indexes
WHERE tablename = 'tms_locations'
AND idx_scan = 0;
_/

-- Notes:
-- _ Stage 1 (exactMatchSearch): Uses idx_tms_locations_tms_integration_name_address for fast ILIKE matching
-- _ Stage 2 (enhancedFuzzyMatchNameAddress): Uses composite indexes + existing GIN indexes for filtered trigram search

-- Expected Performance Improvements:
-- Stage 1 (Exact/Prefix): < 50ms (composite B-tree indexes)
-- Stage 2 (Fuzzy): 50-200ms (composite B-tree + GIN trigram indexes)  
-- Stage 3 (Fallback): 100-300ms (composite B-tree + GIN trigram with lower thresholds)

-- Index Maintenance:
-- These B-tree indexes require minimal maintenance compared to GIN indexes
-- Monitor usage with the queries above and drop any unused indexes
