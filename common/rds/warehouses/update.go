package warehouse

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func Update(ctx context.Context, updatedWarehouse *models.Warehouse) error {
	return rds.WithContext(ctx).Model(updatedWarehouse).Updates(*updatedWarehouse).Error
}

func SoftDeleteByIDs(ctx context.Context, warehouseIDs []uint) error {
	if len(warehouseIDs) == 0 {
		return nil
	}

	return rds.WithContext(ctx).Delete(&models.Warehouse{}, warehouseIDs).Error
}
