package rules

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// GetForwardingRulesByUserAndService retrieves all email forwarding rules for a given service ID
// or user ID.
func GetForwardingRulesByUserAndService(
	ctx context.Context,
	userID uint,
	serviceID uint,
) ([]models.EmailForwardingRule, error) {

	var rules []models.EmailForwardingRule
	err := rds.WithContextReader(ctx).Where("service_id = ? AND user_id IS NULL", serviceID).Find(&rules).Error
	if err != nil {
		return nil, fmt.Errorf("error getting service's rules: %w", err)
	}

	if userID != 0 {
		temp := []models.EmailForwardingRule{}
		err = rds.WithContextReader(ctx).Where("service_id = ? AND user_id = ?", serviceID, userID).Find(&temp).Error
		if err != nil {
			return nil, fmt.Errorf("error getting user's rules: %w", err)
		}
		rules = append(rules, temp...)
	}

	return rules, nil
}
