package exceptions

import (
	"context"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func BatchUpsertExceptions(ctx context.Context, events []models.Exception) error {
	if len(events) == 0 {
		log.Debug(ctx, "skipping upsert for empty list of exceptions")

		return nil
	}

	return rds.WithContext(ctx).Clauses(
		clause.Returning{},
		clause.OnConflict{
			Columns: []clause.Column{{Name: "load_id"},
				{Name: "date_time"}, {Name: "date_time_without_timezone"}},
			// NOTE: Gorm bug, When DoNothing: true, Gorm duplicates the objects that were actually inserted
			// in the returned list. We circumvent that by updating all the rows. Other option is to re-query.
			UpdateAll: true}).
		Create(&events).Error
}
