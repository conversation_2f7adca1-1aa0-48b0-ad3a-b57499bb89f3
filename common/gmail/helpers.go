package gmailhelpers

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers"
	email_helpers "github.com/drumkitai/drumkit/common/helpers/emails"
	"github.com/drumkitai/drumkit/common/helpers/jsoncfg"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/sqsclient"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

// ProcessAndEnqueueGmailMessage encapsulates the common logic for processing a single Gmail message
// and enqueueing it for downstream processing. It handles, attachments, S3 archival, and SQS enqueueing.
func ProcessAndEnqueueGmailMessage(
	ctx context.Context,
	client gmailclient.Client,
	sqsClient sqsclient.API,
	s3Uploader s3backup.Archiver,
	user *models.User,
	service *models.Service,
	msg *gmail.Message,
	processorQueueURL string,
) (err error) {
	ctx, attrs := emails.LogWithMessageAttrs(ctx, user, &emails.GmailMessage{Message: msg})

	ctx, metaSpan := otel.StartSpan(ctx, "gmail.ProcessAndEnqueueGmailMessage", attrs)
	defer func() { metaSpan.End(err) }()

	if len(msg.Payload.Headers) == 0 {
		log.Warn(ctx, "email has no headers, skipping")
		return nil
	}

	var attachments []models.Attachment
	var hasPDFs bool
	if email_helpers.ShouldProcessAttachments(service) {
		attachments, hasPDFs = processAttachments(
			ctx, user, msg.Id, msg.Payload, client, s3Uploader,
		)
	}

	gmailMsg := emails.GmailMessage{Message: msg}

	payload, err := emails.PrepareEmailPayload(
		ctx,
		&gmailMsg,
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithUser(user),
		emails.WithS3URL("temp-s3-url"), // This will be replaced after S3 upload
		emails.WithAttachments(attachments),
		emails.WithHasPDFs(hasPDFs),
	)

	if err != nil {
		log.Error(ctx, "error creating email payload", zap.Error(err))
		return nil // Non-fatal, just log and continue
	}

	var s3URL string
	if s3Uploader != nil {
		if s3URL, err = s3Uploader.Gmail(ctx, payload, user.EmailAddress); err != nil {
			log.Error(ctx, "s3 archive failed", zap.String("msgId", msg.Id), zap.Error(err))
			// Non-fatal, continue processing
		}
	}
	payload.S3URL = s3URL

	payloadBytes, err := jsoncfg.SpaceEfficientConfig.Marshal(payload)
	if err != nil {
		log.Error(ctx, "error marshaling email payload", zap.Error(err))
		return nil // Non-fatal, just log and continue
	}

	log.Info(
		ctx,
		"sending SQS payload",
		zap.String("sqsUrl", processorQueueURL),
		zap.Any("payload", payload.Sanitize()),
	)

	_, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:    aws.String(processorQueueURL),
		MessageBody: aws.String(string(payloadBytes)),
	})

	if err != nil {
		// This is a potentially more significant issue.
		// For now, log and continue, but it might indicate a configuration problem.
		log.Error(ctx, "error enqueueing email", zap.Error(err))
		return nil
	}

	// Save ingestion status to redis
	if msg.ThreadId != "" {
		ingestionStatusKey := fmt.Sprintf("ingestion-status:%s", msg.ThreadId)
		// Set a comfortable buffer to account for email processing time.
		// Processor queue metrics show an average of ~60s, but processing time can spike higher under load.
		if err := redis.SetKey(ctx, ingestionStatusKey, string(models.InFlight), 10*time.Minute); err != nil {
			log.Error(ctx, "error saving gmail email ingestion status to redis", zap.Error(err))
		}
	} else {
		log.WarnNoSentry(ctx, "no thread ID found for message, could not save ingestion status", zap.Any("msg", msg))
	}

	log.Info(ctx, "successfully enqueued email for processing")
	return nil
}

// processAttachments recursively gets all attachments from a message, including inline/embedded images.
// Helpful: https://www.ehfeng.com/gmail-api-mime-types/
func processAttachments(
	ctx context.Context,
	user *models.User,
	msgID string,
	msgTopLevelPart *gmail.MessagePart,
	client gmailclient.Client,
	s3Uploader s3backup.Archiver,
) (res []models.Attachment, hasPDFs bool) {
	ctx, metaSpan := otel.StartSpan(
		ctx, "gmail.processAttachments",
		emails.MessageAttrs(user, &emails.GmailMessage{Message: &gmail.Message{Id: msgID, Payload: msgTopLevelPart}}),
	)
	defer func() { metaSpan.End(nil) }()

	for _, part := range msgTopLevelPart.Parts {
		ctx := log.With(ctx, zap.String("attachmentName", part.Filename), zap.String("attachmentType", part.MimeType))

		// Check if this is any type of multipart container, not just specific ones
		if strings.HasPrefix(part.MimeType, "multipart/") {
			log.Debug(ctx, "found multipart container, recursing", zap.String("mimeType", part.MimeType))
			nestedRes, nestedHasPDFs := processAttachments(ctx, user, msgID, part, client, s3Uploader)

			// Append nested attachments to the result
			res = append(res, nestedRes...)
			hasPDFs = hasPDFs || nestedHasPDFs

			continue
		}
		if part.Body.AttachmentId == "" {
			log.Debug(ctx, "attachmentId is empty, skipping")
			continue
		}

		// LLM Load Building and Quote Request pipelines currently only support PDFs for now
		isPDF := email_helpers.IsPDF(part.MimeType, part.Filename)

		// If *gmail.MessagePart.Body.AttachmentId is not empty, "Data" field is empty in message payload
		// so must fetch attachment separately
		attachment, err := client.GetAttachment(ctx, msgID, part.Body.AttachmentId)
		if err != nil {
			log.Error(ctx, "Failed to get attachment", zap.Error(err))
			continue
		}

		data, err := base64.URLEncoding.DecodeString(attachment.Data)
		if err != nil {
			log.Error(ctx, "Failed to decode attachment content", zap.Error(err))
			continue
		}

		// S3 Cost Optimization: LLM Load Building and Quote Request pipelines currently only support PDFs for now
		var s3URL string
		if isPDF {
			hasPDFs = true

			s3URL, err = s3Uploader.Attachment(ctx, models.Gmail, user.EmailAddress, msgID, part.Filename, data)
			if err != nil {
				log.Error(ctx, "Failed to upload PDF to S3", zap.Error(err))
			} else {
				log.Info(ctx, "Uploaded PDF to S3")
			}
		}

		res = append(res, models.Attachment{
			MessageExternalID: msgID,
			ExternalID:        part.Body.AttachmentId,
			OriginalFileName:  part.Filename,
			IsInline:          isInlineAttachment(part),
			MimeType:          helpers.Ternary(isPDF, "application/pdf", part.MimeType),
			// Gmail hosts images in signature so it's in <img src="...">, unlike Outlook where it's an attachment
			IsInSenderSignature: false,
			TransformedFileName: s3backup.SanitizeFileName(part.Filename),
			S3URL:               s3URL,
		})

	}

	log.Debugf(ctx, "returning attachments: %d, HasPDFs: %t", len(res), hasPDFs)
	return res, hasPDFs
}

// isInlineAttachment checks if a given MessagePart is an inline attachment.
func isInlineAttachment(part *gmail.MessagePart) bool {
	for _, header := range part.Headers {
		name := strings.ToLower(header.Name)
		value := strings.ToLower(header.Value)

		// Check if Content-Disposition is "inline"
		if name == "content-disposition" && strings.Contains(value, "inline") {
			return true
		}
	}
	return false
}
