package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type CarrierQuoteStatus string

const (
	RequestedCarrierQuoteStatus CarrierQuoteStatus = "requested"
	RespondedCarrierQuoteStatus CarrierQuoteStatus = "responded"
	SelectedCarrierQuoteStatus  CarrierQuoteStatus = "selected"
	RejectedCarrierQuoteStatus  CarrierQuoteStatus = "rejected"
)

// A carrier quote represents a generated email sent to a carrier asking for a quote.
// It contains information about the CarrierQuote request made by the user,
// and if the carrier responds, the carrier's quote is stored here.
// See ADR and Lucid<PERSON>hart https://www.notion.so/drumkitai/Quoting-1a72b16b087a8093b0fef7dab1bd09f8?pvs=4
type CarrierQuote struct {
	gorm.Model

	Status CarrierQuoteStatus `json:"status"`

	// Initially empty when first sending email to carrier; non-empty after reply
	EmailID  uint   `gorm:"default:NULL" json:"emailID"`
	Email    Email  `json:"-"`
	ThreadID string `json:"threadID"` // Always non-empty

	RecipientID uint    `gorm:"default:NULL" json:"recipientID"`
	Recipient   User    `gorm:"foreignKey:RecipientID" json:"-"`
	ServiceID   uint    `json:"serviceID"`
	Service     Service `gorm:"foreignKey:ServiceID" json:"-"`

	// Carrier Location; not TMSCarrier because a carrier can operate out of multiple locations
	// For Carrier Quote using location
	CarrierLocationID uint        `gorm:"default:NULL" json:"carrierLocationID"`
	CarrierLocation   TMSLocation `gorm:"foreignKey:CarrierLocationID" json:"carrierLocation"`

	// A carrier quote can belong to only 1 quote request hence the singular foreign key
	QuoteRequestID uint         `json:"quoteRequestID"`
	QuoteRequest   QuoteRequest `gorm:"foreignKey:QuoteRequestID" json:"-"`

	// Carrier may respond without providing a quote; this is 0 in that case
	SuggestedQuote *CarrierQuoteData `gorm:"type:jsonb" json:"suggestedQuote"`
	AppliedQuote   *CarrierQuoteData `gorm:"type:jsonb" json:"appliedQuote"`

	// Null for Carrier Quote using location
	SelectedCarrierGroupID *uint `gorm:"index" json:"selectedCarrierGroupId,omitempty"`

	// For internal recordkeeping & debugging
	SuggestedQuoteHistory CarrierQuoteHistory `gorm:"type:jsonb" json:"-"`
}

type CarrierQuoteHistory []MiniCarrierQuote

//nolint:lll
type CarrierQuoteData struct {
	TotalCost   float32 `json:"totalCost" jsonschema_description:"The total amount the carrier is charging for the shipment"`
	Currency    string  `json:"currency" jsonschema_description:"Always USD"`
	IsAvailable bool    `json:"isAvailable" jsonschema:"enum=true,enum=false" jsonschema_description:"If the carrier decides to be available for this job"`
	Notes       string  `json:"notes" jsonschema_description:"Any notable information that the carrier provides about the quote, like wait time pricing, fuel surcharge, or time window"`
}

type MiniCarrierQuote struct {
	EmailID   uint              `json:"emailID"`
	Timestamp time.Time         `json:"timestamp"`
	Quote     *CarrierQuoteData `json:"quote"`
}

// Implements sql.Scanner interface
func (cq *CarrierQuoteData) Scan(value any) error {
	if value == nil {
		*cq = CarrierQuoteData{}
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for CarrierQuote: %T", value)
	}
	var carrierQuote CarrierQuoteData
	if err := json.Unmarshal(val, &carrierQuote); err != nil {
		return err
	}
	*cq = carrierQuote

	return nil
}

// Implement driver.Valuer interface
func (cq CarrierQuoteData) Value() (driver.Value, error) {
	if cq == (CarrierQuoteData{}) {
		return nil, nil
	}

	return json.Marshal(cq)
}

// Implements sql.Scanner interface
func (qh *CarrierQuoteHistory) Scan(value any) error {
	if value == nil {
		*qh = CarrierQuoteHistory{}
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for CarrierQuoteHistory: %T", value)
	}
	var carrierQuoteHistory CarrierQuoteHistory
	if err := json.Unmarshal(val, &carrierQuoteHistory); err != nil {
		return err
	}
	*qh = carrierQuoteHistory

	return nil
}

// Implement driver.Valuer interface
func (qh CarrierQuoteHistory) Value() (driver.Value, error) {
	if qh == nil {
		return nil, nil
	}

	return json.Marshal(qh)
}

var (
	_ sql.Scanner   = &CarrierQuoteData{}
	_ driver.Valuer = &CarrierQuoteData{}
	_ sql.Scanner   = &CarrierQuoteHistory{}
	_ driver.Valuer = &CarrierQuoteHistory{}
)
