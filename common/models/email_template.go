package models

import (
	"bytes"
	"fmt"
	"text/template"
	"time"

	"github.com/lib/pq"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"gorm.io/gorm"
)

type (
	EmailTemplateType       string
	EmailTemplateAccessTier string
	StopType                string
	TemperatureType         string

	EmailTemplate struct {
		gorm.Model
		ServiceID   uint
		Service     Service `json:"-"`
		UserID      uint
		User        User `json:"-"`
		UserGroupID uint
		UserGroup   UserGroup `json:"-"`

		Name         string            `json:"name"`
		Subject      string            `json:"subject"`
		Body         string            `json:"body"`
		CC           pq.StringArray    `gorm:"type:text[]" json:"cc"`
		TemplateType EmailTemplateType `json:"templateType"`
	}

	BaseTemplateData struct {
		FreightTrackingID string
		PONumbers         string
		FromCity          string
		FromState         string
		ToCity            string
		ToState           string
		DriverName        string
	}

	PickupTemplateData struct {
		BaseTemplateData
		PickupAddress     string
		PickupAppointment string
	}

	DropoffTemplateData struct {
		BaseTemplateData
		DropoffAddress     string
		DropoffAppointment string
	}

	AppointmentRequestData struct {
		Company             string
		ContactName         string
		ContactPhone        string
		PreferredDate       string
		PreferredTime       string
		StopType            string
		StopTypeCapitalized string
		LoadExternalID      string
		PONumber            string
		Integration         string
		WarehouseName       string
		Notes               string
		TrailerType         string
		// NFI Appointment Request Fields
		CustomerRefNumber  string
		PickupName         string
		DateRequested      string
		TimeRequested      string
		PalletCount        string
		DryChilledOrFrozen string
		LTLorTL            string
	}

	CarrierQuoteTemplateData struct {
		PickupLocation    string `json:"pickupLocation"`
		PickupStartTime   string `json:"pickupStartTime"`
		PickupEndTime     string `json:"pickupEndTime"`
		DeliveryLocation  string `json:"deliveryLocation"`
		DeliveryStartTime string `json:"deliveryStartTime"`
		DeliveryEndTime   string `json:"deliveryEndTime"`
		TransportType     string `json:"transportType"`
		ItemDescription   string `json:"itemDescription"`
	}
)

const (
	// Email Template Access Tiers
	EmailTemplateUserTier    EmailTemplateAccessTier = "user"
	EmailTemplateGroupTier   EmailTemplateAccessTier = "group"
	EmailTemplateServiceTier EmailTemplateAccessTier = "service"
	EmailTemplateGenericTier EmailTemplateAccessTier = "generic"

	// Appointment Scheduling
	// TODO: Remove in favor of AppointmentSchedulingRequestHTML - this is kept in favor of backwards compatibility
	AppointmentSchedulingRequest     EmailTemplateType = "appointment_scheduling_request"
	AppointmentSchedulingRequestHTML EmailTemplateType = "appointment_scheduling_request_html"
	// Quick Quote
	QuickQuoteReply EmailTemplateType = "quick_quote_email_template"
	// Carrier Quote
	CarrierQuoteByGroup    EmailTemplateType = "carrier_quote_by_group"
	CarrierQuoteByLocation EmailTemplateType = "carrier_quote_by_location"
	// Track and Trace
	TrackAndTraceCarrier EmailTemplateType = "track_and_trace_carrier"
	// Subtypes for TrackAndTraceCarrier
	TrackAndTraceCarrierDispatch     EmailTemplateType = "track_and_trace_carrier_dispatch"
	TrackAndTraceCarrierPickup       EmailTemplateType = "track_and_trace_carrier_pickup"
	TrackAndTraceCarrierAfterPickup  EmailTemplateType = "track_and_trace_carrier_after_pickup"
	TrackAndTraceCarrierInTransit    EmailTemplateType = "track_and_trace_carrier_in_transit"
	TrackAndTraceCarrierDropoff      EmailTemplateType = "track_and_trace_carrier_dropoff"
	TrackAndTraceCarrierAfterDropoff EmailTemplateType = "track_and_trace_carrier_after_dropoff"

	// Stop Types
	PickupStop  StopType = "pickup"
	DropoffStop StopType = "dropoff"

	// Temperature Types
	DryTemperature     TemperatureType = "Dry"
	ChilledTemperature TemperatureType = "Chilled"
	FrozenTemperature  TemperatureType = "Frozen"
	EmptyTemperature   TemperatureType = ""
)

// NOTE: Templates that are rendered within WYSIWYG editors should be HTML-safe.
// If they're simple plain text, all we need to do is wrap them in <p> tags.
var GenericEmailTemplates = map[EmailTemplateType]EmailTemplate{
	AppointmentSchedulingRequest: {
		TemplateType: AppointmentSchedulingRequest,
		Subject:      `Appointment Request {{if .LoadExternalID}}- Load {{.LoadExternalID}}{{end}}`,
		Body: `Dear Scheduling Team,\n
I would like to request an appointment for:
\n
{{- if .Company}}- Company: {{.Company}}\n{{- end}}
{{- if .ContactName}}- Contact: {{.ContactName}}\n{{- end}}
{{- if .ContactPhone}}- Phone: {{.ContactPhone}}\n{{- end}}
{{- if .PreferredDate}}- Preferred Date: {{.PreferredDate}}\n{{- end}}
{{- if .PreferredTime}}- Preferred Time: {{.PreferredTime}}\n{{- end}}
{{- if .LoadExternalID}}- Load ID: {{.LoadExternalID}}\n{{- end}}
{{- if .PONumber}}- PO Number: {{.PONumber}}\n{{- end}}
{{- if .StopType}}- Stop Type: {{.StopType}}\n{{- end}}
{{- if .WarehouseName}}- Warehouse: {{.WarehouseName}}\n{{- end}}
{{- if .TrailerType}}- Trailer Type: {{.TrailerType}}\n{{- end}}
{{- if .Notes}}- Notes: {{.Notes}}\n{{- end}}
Please confirm availability.\n
Thank you,\n
{{- if .ContactName}}{{.ContactName}}{{- end}}`,
	},
	AppointmentSchedulingRequestHTML: {
		TemplateType: AppointmentSchedulingRequest,
		Subject:      `Appointment Request {{if .LoadExternalID}}- Load {{.LoadExternalID}}{{end}}`,
		Body: `<p>Dear Scheduling Team,</p>
<p>I would like to request an appointment for:</p>
<ul>
{{- if .Company}}<li>Company: {{.Company}}</li>{{- end -}}
{{- if .ContactName}}<li>Contact: {{.ContactName}}</li>{{- end -}}
{{- if .ContactPhone}}<li>Phone: {{.ContactPhone}}</li>{{- end -}}
{{- if .PreferredDate}}<li>Preferred Date: {{.PreferredDate}}</li>{{- end -}}
{{- if .PreferredTime}}<li>Preferred Time: {{.PreferredTime}}</li>{{- end -}}
{{- if .LoadExternalID}}<li>Load ID: {{.LoadExternalID}}</li>{{- end -}}
{{- if .PONumber}}<li>PO Number: {{.PONumber}}</li>{{- end -}}
{{- if .StopType}}<li>Stop Type: {{.StopType}}</li>{{- end -}}
{{- if .WarehouseName}}<li>Warehouse: {{.WarehouseName}}</li>{{- end -}}
{{- if .TrailerType}}<li>Trailer Type: {{.TrailerType}}</li>{{- end -}}
{{- if .Notes}}<li>Notes: {{.Notes}}</li>{{- end -}}
</ul>
<p>Please confirm availability.</p>
<p>Thank you,<br>
{{- if .ContactName}}{{.ContactName}}{{- end}}</p>`,
	},
	// Note unlike other templates which are populated by BE using text/template, this one is populated by FE using
	// mustache.js, hence the different syntax.
	QuickQuoteReply: {
		TemplateType: QuickQuoteReply,
		Subject:      ``, // Email reply does not have a subject
		//nolint:lll
		Body: `<p>{{#isMultiStop}}Thank you for your {{stops.length}}-stop {{transportType}} request from {{pickupLocation}} to {{deliveryLocation}}.{{/isMultiStop}}{{^isMultiStop}}Thank you for your {{transportType}} request from {{pickupLocation}} to {{deliveryLocation}}.{{/isMultiStop}} The rate would be {{rate}}.</p>`,
	},
	CarrierQuoteByLocation: {
		TemplateType: CarrierQuoteByLocation,
		Subject:      `Quote Pick-up {{.PickupLocation}} {{if .DeliveryLocation}}to {{.DeliveryLocation}}{{end}}`,
		//nolint:lll
		Body: `<p>Please see the info below and let me know your quote.</p>
<p>Pickup in {{.PickupLocation}} {{if .PickupStartTime}} on {{.PickupStartTime}}{{end}} {{if .PickupStartTime}} between {{.PickupStartTime}} and {{.PickupEndTime}}{{else}}<br>Please let me know your availability{{end}}</p>
<p>Job Description:</p><p>{{.ItemDescription}}</p>
<p>>>> We would need you to recover the loose material listed above to be palletized & securely shrink wrapped. Once the pallet/s are back at your warehouse we will send in a truck to recover the freight from your dock.</p>
<p>Thank you</p>`,
	},
	CarrierQuoteByGroup: {
		TemplateType: CarrierQuoteByGroup,
		Subject:      `Quote Pick-up {{.PickupLocation}} {{if .DeliveryLocation}}to {{.DeliveryLocation}}{{end}}`,
		//nolint:lll
		Body: `<p>Hello, I am scheduling a pickup in {{.PickupLocation}}{{if .DeliveryLocation}} to be dropped off at {{.DeliveryLocation}}{{end}}. Please see the info below and let me know if you can help and your quote.</p>
<p>Pickup Time: {{if .PickupStartTime}}{{.PickupStartTime}}{{else}}TBD{{end}}</p>
<p>Dropoff Time: {{if .DeliveryStartTime}}{{.DeliveryStartTime}}{{else}}TBD{{end}}</p>
<p>Equipment: {{if .TransportType}}{{.TransportType}}{{else}}TBD{{end}}</p>`,
	},
	TrackAndTraceCarrierDispatch: {
		TemplateType: TrackAndTraceCarrierDispatch,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Dispatch{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Hi, please share the {{if not .DriverName}}driver name, {{end}}phone number, truck number, trailer number, and ETA.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},
	TrackAndTraceCarrierPickup: {
		TemplateType: TrackAndTraceCarrierPickup,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm At-Pickup{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver is on site for the expected pickup{{if .PickupAppointment}} ({{.PickupAppointment}}){{end}}{{if .PickupAddress}} at {{.PickupAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierAfterPickup: {
		TemplateType: TrackAndTraceCarrierAfterPickup,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Loaded{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the truck is loaded for the expected pickup{{if .PickupAppointment}} ({{.PickupAppointment}}){{end}}{{if .PickupAddress}} at {{.PickupAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierInTransit: {
		TemplateType: TrackAndTraceCarrierInTransit,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Location Updates{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver is on the way to the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierDropoff: {
		TemplateType: TrackAndTraceCarrierDropoff,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm At-Dropoff{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver has arrived to the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},

	TrackAndTraceCarrierAfterDropoff: {
		TemplateType: TrackAndTraceCarrierAfterDropoff,
		//nolint:lll
		Subject: `{{if .FreightTrackingID}}Load {{.FreightTrackingID}} {{end}}Confirm Delivery{{if and .FromCity .FromState .ToCity .ToState}} - From {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}{{end}}`,
		//nolint:lll
		Body: `Please confirm if the driver has unloaded at the dropoff{{if .DropoffAppointment}} ({{.DropoffAppointment}}){{end}}{{if .DropoffAddress}} at {{.DropoffAddress}}{{end}}.\n{{if .FreightTrackingID}}\nLoad ID: {{.FreightTrackingID}}\n{{end}}{{if .PONumbers}}\nPO numbers: {{.PONumbers}}\n{{end}}{{if and .FromCity .FromState .ToCity .ToState}}\nLane: {{.FromCity}}, {{.FromState}} to {{.ToCity}}, {{.ToState}}\n{{end}}{{if .DriverName}}\nDriver: {{.DriverName}}\n{{end}}\n\nThank You`,
	},
}

// EmailTemplateOptions contains configuration for email templates.
type EmailTemplateOptions struct {
	Template         EmailTemplate
	StopType         StopType
	Load             Load
	CarrierQuoteData CarrierQuoteTemplateData
}

// EmailTemplateOption defines a function that modifies EmailTemplateOptions.
type EmailTemplateOption func(*EmailTemplateOptions)

func WithTemplate(tmpl EmailTemplate) EmailTemplateOption {
	return func(o *EmailTemplateOptions) {
		o.Template = tmpl
	}
}

func WithStopType(stopType StopType) EmailTemplateOption {
	return func(o *EmailTemplateOptions) {
		o.StopType = stopType
	}
}

func WithLoad(load Load) EmailTemplateOption {
	return func(o *EmailTemplateOptions) {
		o.Load = load
	}
}

func WithCarrierQuoteData(carrierQuoteData CarrierQuoteTemplateData) EmailTemplateOption {
	return func(o *EmailTemplateOptions) {
		o.CarrierQuoteData = carrierQuoteData
	}
}

func PrepareEmailTemplate(
	templateType EmailTemplateType,
	opts ...EmailTemplateOption,
) (string, string, error) {

	options := &EmailTemplateOptions{
		Template: GenericEmailTemplates[templateType],
	}

	for _, opt := range opts {
		opt(options)
	}

	var data any

	switch templateType {
	// TODO: incorporate stop type logic when we use Stops[] to switch between pickup and dropoff fields
	case AppointmentSchedulingRequest, AppointmentSchedulingRequestHTML:
		stopTime := options.Load.Pickup.ApptStartTime
		if options.StopType == DropoffStop {
			stopTime = options.Load.Consignee.ApptStartTime
		}

		warehouseName := options.Load.Pickup.Name
		if options.StopType == DropoffStop {
			warehouseName = options.Load.Consignee.Name
		}

		initialDate := stopTime.Time
		if !stopTime.Valid {
			initialDate = time.Now()
		}

		palletCount := ""
		if options.Load.Specifications.TotalOutPalletCount > 0 {
			palletCount = fmt.Sprintf("%d", options.Load.Specifications.TotalOutPalletCount)
		}

		stopTypeStr := string(PickupStop)
		if options.StopType == DropoffStop {
			stopTypeStr = string(DropoffStop)
		}

		data = AppointmentRequestData{
			LoadExternalID:      options.Load.FreightTrackingID,
			PONumber:            options.Load.PONums,
			Company:             options.Load.Customer.Name,
			ContactName:         options.Load.Customer.Contact,
			ContactPhone:        options.Load.Customer.Phone,
			PreferredDate:       options.Load.Pickup.ApptStartTime.Time.Format("2006-01-02"),
			PreferredTime:       options.Load.Pickup.ApptStartTime.Time.Format("15:04"),
			TrailerType:         options.Load.Specifications.TransportType,
			Notes:               options.Load.Pickup.ApptNote,
			WarehouseName:       warehouseName,
			StopType:            stopTypeStr,
			StopTypeCapitalized: titleCase(stopTypeStr),

			// NFI Appointment Request Fields
			CustomerRefNumber:  options.Load.Customer.RefNumber,
			PickupName:         options.Load.Pickup.Name,
			DateRequested:      initialDate.Format("01-02"),
			TimeRequested:      initialDate.Format("15:04"),
			PalletCount:        palletCount,
			DryChilledOrFrozen: string(getMaxTemp(options.Load.Specifications.MaxTempFahrenheit)),
			LTLorTL:            "TL",
		}

	case CarrierQuoteByLocation, CarrierQuoteByGroup:
		data = CarrierQuoteTemplateData{
			PickupLocation:    options.CarrierQuoteData.PickupLocation,
			PickupStartTime:   options.CarrierQuoteData.PickupStartTime,
			PickupEndTime:     options.CarrierQuoteData.PickupEndTime,
			DeliveryLocation:  options.CarrierQuoteData.DeliveryLocation,
			DeliveryStartTime: options.CarrierQuoteData.DeliveryStartTime,
			DeliveryEndTime:   options.CarrierQuoteData.DeliveryEndTime,
			TransportType:     options.CarrierQuoteData.TransportType,
			ItemDescription:   options.CarrierQuoteData.ItemDescription,
		}

	case TrackAndTraceCarrierDispatch:
		data = BaseTemplateData{
			FreightTrackingID: options.Load.FreightTrackingID,
			PONumbers:         options.Load.PONums,
			FromCity:          options.Load.Pickup.City,
			FromState:         options.Load.Pickup.State,
			ToCity:            options.Load.Consignee.City,
			ToState:           options.Load.Consignee.State,
			DriverName:        options.Load.Carrier.FirstDriverName,
		}

	case TrackAndTraceCarrierPickup, TrackAndTraceCarrierAfterPickup:
		data = PickupTemplateData{
			BaseTemplateData: BaseTemplateData{
				FreightTrackingID: options.Load.FreightTrackingID,
				PONumbers:         options.Load.PONums,
				FromCity:          options.Load.Pickup.City,
				FromState:         options.Load.Pickup.State,
				ToCity:            options.Load.Consignee.City,
				ToState:           options.Load.Consignee.State,
				DriverName:        options.Load.Carrier.FirstDriverName,
			},
			PickupAddress: FormatAddress(
				options.Load.Pickup.AddressLine1,
				options.Load.Pickup.AddressLine2,
				options.Load.Pickup.City,
				options.Load.Pickup.State,
				options.Load.Pickup.Zipcode,
			),
			PickupAppointment: FormatAppointment(options.Load.Pickup.ApptStartTime),
		}

	case TrackAndTraceCarrierInTransit, TrackAndTraceCarrierDropoff, TrackAndTraceCarrierAfterDropoff:
		data = DropoffTemplateData{
			BaseTemplateData: BaseTemplateData{
				FreightTrackingID: options.Load.FreightTrackingID,
				PONumbers:         options.Load.PONums,
				FromCity:          options.Load.Pickup.City,
				FromState:         options.Load.Pickup.State,
				ToCity:            options.Load.Consignee.City,
				ToState:           options.Load.Consignee.State,
				DriverName:        options.Load.Carrier.FirstDriverName,
			},
			DropoffAddress: FormatAddress(
				options.Load.Consignee.AddressLine1,
				options.Load.Consignee.AddressLine2,
				options.Load.Consignee.City,
				options.Load.Consignee.State,
				options.Load.Consignee.Zipcode,
			),
			DropoffAppointment: FormatAppointment(options.Load.Consignee.ApptStartTime),
		}
	}

	subjectTmpl, err := template.New("subject").Parse(options.Template.Subject)
	if err != nil {
		return "", "", err
	}

	bodyTmpl, err := template.New("body").Parse(options.Template.Body)
	if err != nil {
		return "", "", err
	}

	var subject, body bytes.Buffer
	err = subjectTmpl.Execute(&subject, data)
	if err != nil {
		return "", "", err
	}

	err = bodyTmpl.Execute(&body, data)
	if err != nil {
		return "", "", err
	}

	return subject.String(), body.String(), nil
}

func FormatAddress(addressLine1, addressLine2, city, state, zipCode string) string {
	address := titleCase(addressLine1)

	if addressLine2 != "" {
		address += fmt.Sprintf(" %s", titleCase(addressLine2))
	}

	address += fmt.Sprintf(", %s %s %s", titleCase(city), state, zipCode)

	return address
}

func FormatAppointment(apptTime NullTime) string {
	if !apptTime.Valid {
		return ""
	}

	return apptTime.Time.Format("01/02 15:04")
}

func titleCase(s string) string {
	return cases.Title(language.English).String(s)
}

func getMaxTemp(maxTemp float32) TemperatureType {
	if maxTemp < 32 {
		return FrozenTemperature
	}
	if maxTemp < 60 {
		return ChilledTemperature
	}
	return EmptyTemperature
}
