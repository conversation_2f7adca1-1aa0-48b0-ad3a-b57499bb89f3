package models

import (
	"gorm.io/gorm"
)

// A CarrierGroup represents a group of TMS Carriers that can be contacted via Carrier Quote for quotes.
type CarrierGroup struct {
	gorm.Model
	Name        string        `gorm:"uniqueIndex:uq_carrier_group_name_service_id" json:"name"`
	Email       string        `json:"email"` // for Outlook distribution list email
	ServiceID   uint          `gorm:"uniqueIndex:uq_carrier_group_name_service_id" json:"serviceID"`
	Service     Service       `gorm:"foreignKey:ServiceID" json:"-"`
	TMSCarriers []*TMSCarrier `gorm:"many2many:carrier_group_tms_carriers;" json:"carriers"`
}
