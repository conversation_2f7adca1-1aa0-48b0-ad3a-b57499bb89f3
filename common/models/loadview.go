package models

import (
	"gorm.io/gorm"
)

type LoadEventAction string

// SQL query for billing:
//
//	 SELECT service_id, COUNT(DISTINCT load_id) FROM load_view_events
//		WHERE created_at BETWEEN '2025-01-01T00:00:00Z' AND '2025-01-31T00:00:00Z' -- adjust dates
//		GROUP BY service_id;
type LoadViewEvent struct {
	gorm.Model // CreatedAt = approx. timestamp of when user viewed/edited load
	// Foreign keys
	LoadID    uint `json:"loadID"`
	Load      Load `json:"-"`
	UserID    uint
	User      User `json:"-"`
	ServiceID uint
	Service   Service `json:"-"`
	TMSID     uint
	TMS       Integration `json:"-"`

	UserEmail             string
	LoadExternalTMSID     string
	LoadFreightTrackingID string
}
