package models

import (
	"gorm.io/gorm"
)

type CheckCall struct {
	gorm.Model        `json:"-"`
	LoadID            uint     `json:"loadID"`
	FreightTrackingID string   `gorm:"uniqueIndex:idx_freight_id_date_time" json:"freightTrackingID"`
	City              string   `json:"city"`
	State             string   `json:"state"`
	Zip               string   `json:"zip"`
	Country           string   `json:"country"`
	Lat               float64  `json:"lat"`
	Lon               float64  `json:"lon"`
	Status            string   `json:"status"`
	Reason            string   `json:"reason"` // e.g. EDI reason
	Notes             string   `json:"notes"`
	Author            string   `json:"author"`
	IsOnTime          *bool    `json:"isOnTime"`
	IsException       *bool    `json:"isException"`
	DateTime          NullTime `gorm:"uniqueIndex:idx_freight_id_date_time" json:"dateTime,omitempty"`

	// Primarily for Aljex/Relay, which does not provide TZ info so all timestamps are defaulted to UTC
	// Required field. EndDatetime is optional
	//nolint:lll
	DateTimeWithoutTimezone NullTime `gorm:"uniqueIndex:idx_freight_id_date_time" json:"dateTimeWithoutTimezone,omitempty"`

	// Optional
	EndDateTimeWithoutTimezone NullTime `json:"endDateTimeWithoutTimezone,omitempty"`

	// Check call location's IANA timezone. For example, if truck is currently in Chicago, IL,
	// then timezone is America/Chicago.
	Timezone string `json:"timezone"`

	// The ETA for the next stop, timezone-agnostic and normalized to UTC.
	NextStopETAWithoutTimezone NullTime `json:"nextStopETAWithoutTimezone"`

	// Stop that the next stop ETA refers to. If multi-stop load (LTL), then FE should prompt user to select
	// the stop the ETA is referring to.
	NextStopID string `json:"nextStopID"`

	// When the check call was captured/inputted into TMS, parsed in the right timezone. For example, the check call
	// happened at 9 am but the user inputted it at 10:30 am.
	CapturedDatetime NullTime `json:"capturedDateTime"`

	// Source of the update e.g. FourKites, dispatcher, driver, etc. Different from Author as John Doe may
	// input the check call into TMS, but he received the information from the carrier's dispatcher.
	Source string `json:"source"`
}
