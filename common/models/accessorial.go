package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type CategoryTag string

const (
	CategoryPickup   CategoryTag = "pickup"
	CategoryDelivery CategoryTag = "delivery"
	CategoryShipment CategoryTag = "shipment"
)

type FormType string

// FormType defines the kind of input field the frontend should render for an accessorial when additional details are
// required. Selecting an accessorial may open further form fields, and the FormType tells the UI which component to
// display (for example, a text box or a date picker).
const (
	FormTypeText FormType = "text"
	FormTypeDate FormType = "date"
)

type (
	Accessorial struct {
		Name         string        `json:"name"`
		Code         string        `json:"code"`
		CategoryTags []CategoryTag `json:"categoryTags,omitempty"`
		// optional: further form fields required to get rate
		Actions map[string]FormType `json:"actions,omitempty"`
	}

	Accessorials []Accessorial
)

// Implements sql.Scanner interface
func (a *Accessorials) Scan(value any) error {
	if value == nil {
		*a = nil
		return nil
	}

	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Accessorials: %T", value)
	}

	var accessorials Accessorials
	if err := json.Unmarshal(val, &accessorials); err != nil {
		return err
	}
	*a = accessorials

	return nil
}

// Implement driver.Valuer interface
func (a Accessorials) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}

	return json.Marshal(a)
}

var (
	_ sql.Scanner   = &Accessorials{}
	_ driver.Valuer = &Accessorials{}
)
