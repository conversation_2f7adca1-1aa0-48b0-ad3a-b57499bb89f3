package models

import (
	"gorm.io/gorm"
)

// TMSCustomerSchedulingIntegration represents the association between TMS customers, warehouses, and scheduling
// integrations. This enables warehouse-specific scheduling configurations for different customers.
// Supports lookups by either TMSCustomerID or CustomerName (load info based fallback for fuzzy matching).
type TMSCustomerSchedulingIntegration struct {
	gorm.Model

	ServiceID uint    `gorm:"not null" json:"serviceId"`
	Service   Service `gorm:"foreignKey:ServiceID" json:"-"`

	//nolint:lll
	TMSIntegrationID uint        `gorm:"not null;uniqueIndex:idx_tms_id_warehouse_id_tms_customer_id,priority:1;uniqueIndex:idx_tms_id_warehouse_id_customer_name,priority:1" json:"tmsIntegrationId"`
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`

	// Warehouse association - required for scheduling integrations
	// NOTE: AssociatedWarehouseID references Warehouse.ID (uint primary key), NOT Warehouse.WarehouseID (string)
	//nolint:lll
	AssociatedWarehouseID uint      `gorm:"not null;uniqueIndex:idx_tms_id_warehouse_id_tms_customer_id,priority:2;uniqueIndex:idx_tms_id_warehouse_id_customer_name,priority:2" json:"warehouseId"`
	AssociatedWarehouse   Warehouse `gorm:"foreignKey:AssociatedWarehouseID" json:"-"`

	// Optional foreign key to TMS customer (nullable bc TMSCustomer for tms integration may not be implemented)
	//nolint:lll
	TMSCustomerID *uint        `gorm:"uniqueIndex:idx_tms_id_warehouse_id_tms_customer_id,priority:3" json:"tmsCustomerId"`
	TMSCustomer   *TMSCustomer `gorm:"foreignKey:TMSCustomerID" json:"-"`
	// Optional customer name for fuzzy matching fallback (when TMSCustomerID is NULL use load.Customer.Name)
	//nolint:lll
	CustomerName *string `gorm:"uniqueIndex:idx_tms_id_warehouse_id_customer_name,priority:3" json:"customerName,omitempty"`

	//nolint:lll
	SchedulingIntegrationID uint        `gorm:"not null;uniqueIndex:idx_tms_id_warehouse_id_tms_customer_id,priority:4;uniqueIndex:idx_tms_id_warehouse_id_customer_name,priority:4" json:"schedulingIntegrationId"`
	SchedulingIntegration   Integration `gorm:"foreignKey:SchedulingIntegrationID" json:"schedulingIntegration"`

	UsageCount int    `gorm:"default:1" json:"usageCount"`
	Notes      string `json:"notes,omitempty"`
	IsDisabled bool   `gorm:"default:false" json:"isDisabled"`
}

func (TMSCustomerSchedulingIntegration) TableName() string {
	return "tms_customer_scheduling_integrations"
}
