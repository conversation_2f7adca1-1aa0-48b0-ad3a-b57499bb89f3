package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"gorm.io/gorm"
)

type TruckType string

const (
	VanTruckType     TruckType = "VAN"
	FlatbedTruckType TruckType = "FLATBED"
	ReeferTruckType  TruckType = "REEFER"
)

var TruckTypesThatMapToFlatbed = []string{
	"53' step deck",
	"53' conestoga",
	"53' double drop",
	"48' step deck",
	"48' conestoga",
	"48' double drop",
	"RGN",
	"removable goose neck",
	"hotshot",
	"flatbed",
}

func ListTruckTypes() []TruckType {
	return []TruckType{VanTruckType, FlatbedTruckType, ReeferTruckType}
}

// NOTE: If updating this struct, also update truck.CopyProcessedDuplicate()
type (
	TruckList struct {
		gorm.Model
		User             User               `json:"-"`
		UserID           uint               `gorm:"index"`
		Service          Service            `json:"-"`
		ServiceID        uint               `gorm:"index"`
		Email            Email              `json:"-"`
		EmailID          uint               `gorm:"index"`
		ThreadID         string             `gorm:"index"`
		IsDraft          bool               `gorm:"default:true" json:"isDraft"`
		Carrier          CarrierInformation `gorm:"embedded;embeddedPrefix:carrier_" json:"carrier"`
		Trucks           []Truck            `gorm:"many2many:trucklist_trucks"`
		Errors           TruckListErrors    `json:"errors"`
		BraintrustSpanID string             `json:"spanID"`
	}

	CarrierInfoErrors map[string][]CarrierInformation
	TruckErrors       map[uint]TruckError

	TruckListErrors struct {
		Carrier        CarrierInfoErrors `json:"carrierErrors"`
		CarrierContact []string          `json:"carrierContactErrors"`
		Equipment      []string          `json:"equipmentErrors"`
		Truck          TruckErrors       `json:"truckErrors"`
		PostedBy       []string          `json:"postedByErrors"`
	}

	TruckError struct {
		PickupDate      []string `json:"pickupDateErrors"`
		PickupLocation  []string `json:"pickupLocationErrors"`
		DropoffLocation []string `json:"dropoffLocationErrors"`
		Equipment       []string `json:"equipmentErrors"`
		Posting         []string `json:"postingErrors"`
	}

	CarrierInformation struct {
		Name          string `json:"name"`
		MC            string `json:"mc"`
		DOT           string `json:"dot"`
		ContactEmail  string `json:"contactEmail"`
		ContactName   string `json:"contactName"`
		ExternalTMSID string `json:"externalTMSID"`
	}

	// NOTE: If updating this struct, also update truck.CopyProcessedDuplicate()
	Truck struct {
		gorm.Model
		User                     User                             `json:"-"`
		UserID                   uint                             `gorm:"index"`
		Service                  Service                          `json:"-"`
		ServiceID                uint                             `gorm:"index"`
		Email                    Email                            `json:"-"`
		EmailID                  uint                             `gorm:"index"`
		ThreadID                 string                           `gorm:"index"`
		PickupLocation           SuggestionAppliedPair[Address]   `gorm:"type:jsonb" json:"pickupLocation"`
		PickupDate               SuggestionAppliedPair[NullTime]  `gorm:"type:jsonb" json:"pickupDate"`
		DropoffLocation          SuggestionAppliedPair[Address]   `gorm:"type:jsonb" json:"dropoffLocation"`
		DropoffDate              SuggestionAppliedPair[NullTime]  `gorm:"type:jsonb" json:"dropoffDate"`
		Type                     SuggestionAppliedPair[TruckType] `gorm:"type:jsonb" json:"type"`
		Length                   SuggestionAppliedPair[float32]   `gorm:"type:jsonb" json:"length"`
		Notes                    SuggestionAppliedPair[string]    `gorm:"type:jsonb" json:"notes"`
		DropoffIsCarrierDomicile bool                             `gorm:"default:false" json:"dropoffIsCarrierDomicile"`
	}
)

// Implements sql.Scanner interface
func (t *TruckListErrors) Scan(value any) error {
	if value == nil {
		*t = TruckListErrors{}
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for TruckListErrors: %T", value)
	}
	var te TruckListErrors
	if err := json.Unmarshal(val, &te); err != nil {
		return err
	}
	*t = te
	return nil
}

// Implement driver.Valuer interface
func (t TruckListErrors) Value() (driver.Value, error) {
	if &t == (&TruckListErrors{}) {
		return nil, nil
	}

	return json.Marshal(t)
}

var (
	_ sql.Scanner   = &TruckListErrors{}
	_ driver.Valuer = &TruckListErrors{}
)

// HasErrors returns if the truck list has relevant errors (PostedBy is not relevant).
func (t *TruckListErrors) HasErrors() bool {
	return len(t.Carrier) > 0 || len(t.CarrierContact) > 0 || len(t.Truck) > 0
}

func (t *TruckList) CopyProcessedDuplicate(email *Email) *TruckList {
	newTruckList := &TruckList{}
	// Set user-specific fields
	newTruckList.UserID = email.UserID
	newTruckList.ServiceID = email.ServiceID
	newTruckList.ThreadID = email.ThreadID
	newTruckList.EmailID = email.ID

	// Copy only specific fields
	newTruckList.Carrier = t.Carrier
	newTruckList.Trucks = t.Trucks
	newTruckList.Errors = t.Errors
	newTruckList.BraintrustSpanID = t.BraintrustSpanID // TODO: Log IDs

	for i, truck := range t.Trucks {
		copiedTruck := truck.CopyProcessedDuplicate(email)
		newTruckList.Trucks[i] = *copiedTruck
	}

	return newTruckList
}

func (t *Truck) CopyProcessedDuplicate(email *Email) *Truck {
	newTruck := &Truck{}
	newTruck.UserID = email.UserID
	newTruck.ServiceID = email.ServiceID
	newTruck.EmailID = email.ID
	newTruck.ThreadID = email.ThreadID

	newTruck.DropoffIsCarrierDomicile = t.DropoffIsCarrierDomicile
	newTruck.PickupLocation.Suggestion = t.PickupLocation.Suggestion
	newTruck.PickupDate.Suggestion = t.PickupDate.Suggestion
	newTruck.DropoffLocation.Suggestion = t.DropoffLocation.Suggestion
	newTruck.DropoffDate.Suggestion = t.DropoffDate.Suggestion
	newTruck.Type.Suggestion = t.Type.Suggestion
	newTruck.Length.Suggestion = t.Length.Suggestion
	newTruck.Notes.Suggestion = t.Notes.Suggestion

	return newTruck
}

var (
	_ ProcessedCopier[*Truck]     = &Truck{}
	_ ProcessedCopier[*TruckList] = &TruckList{}
)
