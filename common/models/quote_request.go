package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

type MatchedBy string
type MarginType string
type CarrierCostType string
type SourceCategory string

const (
	MatchedBySuggestion      MatchedBy = "suggestion"
	MatchedByFieldSimilarity MatchedBy = "field_similarity"

	Percentage MarginType = "Percentage"
	Amount     MarginType = "Amount"

	Flat     CarrierCostType = "Flat"
	PerMile  CarrierCostType = "PerMile"
	Linehaul CarrierCostType = "Linehaul"

	EmailSourceCategory         SourceCategory = "email"
	QuotingPortalSourceCategory SourceCategory = "quoting-portal"
)

// ThirdPartyQuoteURLs is for when shipper asks broker to submit quote to a website/form
type ThirdPartyQuoteURLs struct {
	FormURL       string `json:"formURL"`
	SubmissionURL string `json:"submissionURL"`
}

// Model for when broker (Drumkit customer) receives a request for a quote from their shipper,
// which is different from when broker receives a `models.Quote` from an API like GreenScreens
// or a `models.CarrierQuote` from a carrier via email.
// See ADR and LucidChart https://www.notion.so/drumkitai/Quoting-1a72b16b087a8093b0fef7dab1bd09f8?pvs=4
//
// NOTE: If updating QuoteRequest struct, also update quoteRequest.CopyProcessedDuplicate()
type QuoteRequest struct {
	// gorm.Model
	ID        uint      `gorm:"primarykey"`
	CreatedAt time.Time `gorm:"index:idx_service_created,priority:2"`
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	UserID         uint
	User           User            `json:"-"`
	EmailID        uint            `gorm:"index"`
	Email          Email           `json:"-"`
	RFCMessageID   string          `gorm:"index"` // For deduping of QRs from the same email received by multiple users
	ServiceID      uint            `gorm:"index:idx_service_created,priority:1"`
	Service        Service         `json:"-"`
	ThreadID       string          `gorm:"index"` // Optional; user may initiate QR outside of email
	SourceCategory SourceCategory  // Required, e.g. "email", "quoting-portal"
	Source         IntegrationName // Required, e.g. "e2open-quoting", "freightview", "uberfreight", etc

	// When SourceCategory=email, this is the email's external ID. For bidding portals, it's the shipment's UID either
	// provided by the portal or generated by Drumkit (see create_suggestion.go/generateSourceExternalID)
	SourceExternalID string `gorm:"index"`
	// Optional; only provide if quoting/bidding portal has a unique URL for each quote request
	SourceURL string

	Attachment          Attachment `gorm:"type:JSONB;default:NULL"`
	Status              SuggestionStatus
	ThirdPartyQuoteURLs ThirdPartyQuoteURLs `gorm:"type:json;default:NULL" json:"third_party_quote_urls"`
	RawLLMOutput        QuoteLoadInfo       `gorm:"type:JSONB" json:"raw_llm_output"`

	SuggestedRequest QuoteLoadInfo `gorm:"type:JSONB"`
	AppliedRequest   QuoteLoadInfo `gorm:"type:JSONB"`

	// Span IDs were added in the deprecated log tracing approach - left here since we have plans to come back to it.
	BraintrustSpanID string        `json:"spanID"`
	BraintrustLogIDs LogRecordList `gorm:"type:JSONB"`

	// Quick Quote Associations
	QuickQuotes          []QuickQuote `gorm:"many2many:quote_request_quick_quotes_1tomany"`
	SelectedQuickQuoteID *uint        `gorm:"column:selected_quick_quote_id" json:"selected_quick_quote_id,omitempty"`
	SelectedQuickQuote   *QuickQuote  `gorm:"foreignKey:SelectedQuickQuoteID;references:ID;-"`

	// Carrier Quoting Associations
	CarrierEmails []GeneratedEmail `gorm:"many2many:quote_request_carrier_emails_1tomany"`
	CarrierQuotes []CarrierQuote   `gorm:"many2many:quote_request_carrier_quotes_1tomany"`

	// Final quote data, embedded
	FinalQuoteData

	// For carrier quoting within shared inboxes, User A may initiate CQ but User B finalizes quote because User A
	// is OOO, re-assigned email to User B, etc
	QuotedByUserID *uint
	QuotedByUser   *User `gorm:"foreignKey:QuotedByUserID" json:"-"`

	FinalQuoteHistory FinalQuoteHistory `gorm:"type:jsonb" json:"finalQuoteHistory"`

	// If service has LB and QQ enabled, then every load building suggestion is associated with a quote request
	// suggestion. This enables deduplication of suggestions on FE
	LoadSuggestionID *uint                `gorm:"index"`
	LoadSuggestion   *SuggestedLoadChange `json:"-"`

	// When a load is created, we need to associate it with a quote request
	WonLoadID *uint `gorm:"index"`
	WonLoad   *Load `json:"-"`
	// For loads matched by field similarity, the confidence score of the match (0-100) based on
	// common/rds/quote_request/query.go:scoreCandidates() function.
	// For example, if we want to create metrics for won loads we're 100% confident in,
	// we can filter for WonLoadScore = 100
	WonLoadScore int `gorm:"default:NULL"`

	// Whether the load was matched by suggestion or matched by field similarity
	MatchedToLoadBy MatchedBy

	// Many-to-many relationship with Vector
	Vectors []Vector `gorm:"many2many:vector_quote_requests"`

	// Batch Quote Association
	BatchQuoteID *uint       `gorm:"index" json:"batchQuoteId,omitempty"`
	BatchQuote   *BatchQuote `gorm:"foreignKey:BatchQuoteID;references:ID;-"`

	// --------------------------------- * Read-Only Columns * ---------------------------------
	// Indexes are created in common/rds/migrate.go because order of columns matters for composite indexes.

	ReadOnlySuggestedRequest ReadOnlyQueryFields `gorm:"embedded;embeddedPrefix:suggested_" json:"-"`
	ReadOnlyAppliedRequest   ReadOnlyQueryFields `gorm:"embedded;embeddedPrefix:applied_" json:"-"`
}

// NOTE: If updating QuoteRequest struct, also update quoteRequest.CopyProcessedDuplicate()

// These fields are used for querying quote requests by load fields and automatically populated by Gorm hooks.
// Application code should write only to the SuggestedRequest and AppliedRequest fields, NOT these.
type ReadOnlyQueryFields struct {
	LoadMode              LoadMode      `json:"-"`
	TransportType         TransportType `json:"-"`
	CustomerExternalTMSID string        `json:"-"`
	CustomerName          string        `json:"-"`
	PickupCity            string        `json:"-"`
	PickupState           string        `json:"-"`
	PickupZip             string        `json:"-"` // CAN zips are alphanumeric
	PickupDate            NullTime      `json:"-"`
	DropoffState          string        `json:"-"`
	DropoffCity           string        `json:"-"`
	DropoffZip            string        `json:"-"` // CAN zips are alphanumeric
	DropoffDate           NullTime      `json:"-"`
}

// NOTE: In order for BeforeSave to work on updates,
// we need to use .Updates(&qr) instead of .Updates(models.QuoteRequest{})
func (q *QuoteRequest) BeforeSave(*gorm.DB) error {
	copyReadOnlyQueryFields(q)
	return nil
}

func copyReadOnlyQueryFields(q *QuoteRequest) {
	suggestedTransportType := strings.TrimSpace(strings.ToLower(string(q.SuggestedRequest.TransportType)))

	q.ReadOnlySuggestedRequest = ReadOnlyQueryFields{
		LoadMode:              q.SuggestedRequest.LoadMode,
		TransportType:         TransportType(suggestedTransportType),
		CustomerExternalTMSID: q.SuggestedRequest.Customer.ExternalTMSID,
		CustomerName:          q.SuggestedRequest.Customer.Name,
		PickupCity:            strings.TrimSpace(strings.ToLower(q.SuggestedRequest.PickupLocation.City)),
		PickupState:           strings.TrimSpace(strings.ToLower(q.SuggestedRequest.PickupLocation.State)),
		PickupZip:             strings.TrimSpace(strings.ToLower(q.SuggestedRequest.PickupLocation.Zip)),
		PickupDate:            q.SuggestedRequest.PickupDate,
		DropoffState:          strings.TrimSpace(strings.ToLower(q.SuggestedRequest.DeliveryLocation.State)),
		DropoffCity:           strings.TrimSpace(strings.ToLower(q.SuggestedRequest.DeliveryLocation.City)),
		DropoffZip:            strings.TrimSpace(strings.ToLower(q.SuggestedRequest.DeliveryLocation.Zip)),
		DropoffDate:           q.SuggestedRequest.DeliveryDate,
	}

	appliedTransportType := strings.TrimSpace(strings.ToLower(string(q.AppliedRequest.TransportType)))

	q.ReadOnlyAppliedRequest = ReadOnlyQueryFields{
		LoadMode:              q.AppliedRequest.LoadMode,
		TransportType:         TransportType(appliedTransportType),
		CustomerExternalTMSID: q.AppliedRequest.Customer.ExternalTMSID,
		CustomerName:          q.AppliedRequest.Customer.Name,
		PickupCity:            strings.TrimSpace(strings.ToLower(q.AppliedRequest.PickupLocation.City)),
		PickupState:           strings.TrimSpace(strings.ToLower(q.AppliedRequest.PickupLocation.State)),
		PickupZip:             strings.TrimSpace(strings.ToLower(q.AppliedRequest.PickupLocation.Zip)),
		PickupDate:            q.AppliedRequest.PickupDate,
		DropoffState:          strings.TrimSpace(strings.ToLower(q.AppliedRequest.DeliveryLocation.State)),
		DropoffCity:           strings.TrimSpace(strings.ToLower(q.AppliedRequest.DeliveryLocation.City)),
		DropoffZip:            strings.TrimSpace(strings.ToLower(q.AppliedRequest.DeliveryLocation.Zip)),
		DropoffDate:           q.AppliedRequest.DeliveryDate,
	}

}

type QuoteRequestWithDetails struct {
	QuoteRequest
	RFCMessageID         string    `json:"rfcMessageId"`
	Subject              string    `json:"subject"`
	SentAt               time.Time `json:"sentAt"`
	Sender               string    `json:"sender"`
	Recipients           string    `json:"recipients"`
	ClassificationMethod string    `json:"classificationMethod"`
	UserEmail            string    `json:"userEmail"`
	ServiceName          string    `json:"serviceName"`
	Currency             string    `json:"currency"`
	TotalCost            float64   `json:"totalCost"`
	FinalQuotePrice      int       `json:"finalQuotePrice"`
	FinalMargin          int       `json:"finalMargin"`
	FinalCarrierCost     int       `json:"finalCarrierCost"`
	TargetBuyRate        float64   `json:"targetBuyRate"`
	MinMarkup            float64   `json:"minMarkup"`
	MaxMarkup            float64   `json:"maxMarkup"`
	QuoteID              uint      `json:"quoteId"`
	QuoteRequestStatus   string    `json:"quoteRequestStatus"`
	QuotePipeline        string    `json:"quotePipeline"`
}

type FinalQuoteData struct {
	// TODO: Capture distance since user can edit ENG-3281
	CustomerID       uint   `json:"customerID"`
	FinalQuotePrice  int    `json:"finalQuotePrice"`
	FinalMargin      int    `json:"finalMargin"`
	MarginType       string `json:"marginType"`
	CarrierCostType  string `json:"carrierCostType"`
	FinalCarrierCost int    `json:"finalCarrierCost"`
}

type FinalQuoteHistory []QuoteHistoryLog

type QuoteHistoryLog struct {
	UserID    uint           `json:"userID"`
	UserEmail string         `json:"userEmail"`
	Timestamp time.Time      `json:"timestamp"`
	Quote     FinalQuoteData `json:"quote"`
}

const (
	QuickQuoteSuggestion SuggestionCategory = "quick_quote"
	QuickQuotePipeline   SuggestionPipeline = "quick_quote_pipeline"
)

func (tp *ThirdPartyQuoteURLs) Scan(value any) error {
	if value == nil {
		*tp = ThirdPartyQuoteURLs{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for ThirdPartyQuoteURLs: %T", value)
	}

	return json.Unmarshal(bytes, tp)
}

func (tp *ThirdPartyQuoteURLs) Value() (driver.Value, error) {
	if tp == nil {
		return nil, nil
	}
	return json.Marshal(tp)
}

func (h *FinalQuoteHistory) Scan(value any) error {
	if value == nil {
		*h = FinalQuoteHistory{}
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for FinalQuoteHistory: %T", value)
	}
	var history FinalQuoteHistory
	if err := json.Unmarshal(val, &history); err != nil {
		return err
	}
	*h = history

	return nil
}

func (h FinalQuoteHistory) Value() (driver.Value, error) {
	if h == nil {
		return nil, nil
	}
	return json.Marshal(h)
}

func (q *QuoteRequest) CopyProcessedDuplicate(email *Email) *QuoteRequest {
	newQuote := &QuoteRequest{}
	// Set user-specific fields
	newQuote.UserID = email.UserID
	newQuote.ServiceID = email.ServiceID
	newQuote.ThreadID = email.ThreadID
	newQuote.EmailID = email.ID
	newQuote.RFCMessageID = email.RFCMessageID
	newQuote.SourceExternalID = email.ExternalID
	newQuote.SourceCategory = EmailSourceCategory

	// Copy only specific fields
	newQuote.Status = Pending
	newQuote.Source = q.Source
	newQuote.SourceURL = q.SourceURL
	newQuote.SuggestedRequest = q.SuggestedRequest
	newQuote.RawLLMOutput = q.RawLLMOutput
	newQuote.Vectors = q.Vectors
	newQuote.Attachment = q.Attachment
	newQuote.ThirdPartyQuoteURLs = q.ThirdPartyQuoteURLs
	newQuote.BraintrustSpanID = q.BraintrustSpanID
	newQuote.BraintrustLogIDs = q.BraintrustLogIDs
	newQuote.LoadSuggestionID = q.LoadSuggestionID
	newQuote.MatchedToLoadBy = q.MatchedToLoadBy
	newQuote.WonLoadID = q.WonLoadID
	newQuote.WonLoadScore = q.WonLoadScore
	newQuote.SelectedQuickQuoteID = q.SelectedQuickQuoteID
	newQuote.BatchQuoteID = q.BatchQuoteID

	// FinalQuoteData is not copied because it's specific to each user
	// though not the case for shared inboxes grrr another time

	return newQuote
}

var _ ProcessedCopier[*QuoteRequest] = &QuoteRequest{}

func (q QuoteRequest) GetVectors() []Vector {
	return q.Vectors
}

func (q QuoteRequest) GetSuggestionID() uint {
	return q.ID
}

func (q QuoteRequest) GetContentType() ContentType {
	if q.Attachment.ExternalID != "" {
		return AttachmentContent
	}
	return EmailBodyContent
}

func (q QuoteRequest) GetServiceID() uint {
	return q.ServiceID
}

func (q QuoteRequest) GetSuggestionType() SuggestionCategory {
	return QuickQuoteSuggestion
}

type (
	LogRecordList []LogRecord
	LogRecord     struct {
		ID              string
		ProjectStepName string
	}
)

// Implements sql.Scanner interface
func (lrl *LogRecordList) Scan(value any) error {
	if value == nil {
		*lrl = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Notes: %T", value)
	}
	var notes []LogRecord
	if err := json.Unmarshal(val, &notes); err != nil {
		return err
	}
	*lrl = notes
	return nil
}

// Implement driver.Valuer interface
func (lrl LogRecordList) Value() (driver.Value, error) {
	if len(lrl) == 0 {
		return nil, nil
	}

	return json.Marshal(lrl)
}

// Implements sql.Scanner interface
func (qli *QuoteLoadInfo) Scan(value any) error {
	if value == nil {
		*qli = QuoteLoadInfo{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for QuoteLoadInfo: %T", value)
	}

	return json.Unmarshal(bytes, qli)
}

// Implements driver.Valuer interface
func (qli QuoteLoadInfo) Value() (driver.Value, error) {
	hasLegacyFields := qli.PickupLocation.City != "" || qli.DeliveryLocation.City != ""

	// Check if the struct is effectively empty by checking individual fields
	if len(qli.Stops) == 0 && !hasLegacyFields {
		return nil, nil
	}

	return json.Marshal(qli)
}

var (
	_ sql.Scanner   = &ThirdPartyQuoteURLs{}
	_ driver.Valuer = &ThirdPartyQuoteURLs{}

	_ sql.Scanner   = &FinalQuoteHistory{}
	_ driver.Valuer = &FinalQuoteHistory{}

	_ sql.Scanner   = &LogRecordList{}
	_ driver.Valuer = &LogRecordList{}

	_ sql.Scanner   = &QuoteLoadInfo{}
	_ driver.Valuer = &QuoteLoadInfo{}
)
