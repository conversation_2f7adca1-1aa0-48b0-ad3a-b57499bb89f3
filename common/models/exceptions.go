package models

import (
	"gorm.io/gorm"
)

type Exception struct {
	gorm.Model `json:"-"`
	LoadID     uint   `gorm:"uniqueIndex:idx_date_time_load_id,unique" json:"loadID"`
	Load       Load   `json:"-"`
	EventCode  string `json:"eventCode"`
	WhoEntered string `json:"whoEntered"`
	// Source of the update e.g. <PERSON>K<PERSON>, dispatcher, driver, etc. Different from WhoEntered as <PERSON> may
	// input the check call into TMS, but he received the information from the carrier's dispatcher.
	Source                  string   `json:"source"`
	Carrier                 string   `json:"carrier"`
	Driver                  string   `json:"driver"`
	Fault                   string   `json:"fault"`
	Trailer                 string   `json:"trailer"`
	Note                    string   `json:"note"`
	Status                  string   `json:"status"`
	IsOnTime                *bool    `json:"isOnTime"`
	DateTime                string   `gorm:"uniqueIndex:idx_date_time_load_id,unique" json:"dateTime"`
	DateTimeWithoutTimezone NullTime `gorm:"uniqueIndex:idx_date_time_load_id,unique" json:"dateTimeWithoutTimezone"`
}
