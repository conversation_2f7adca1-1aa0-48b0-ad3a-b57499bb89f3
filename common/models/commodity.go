package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

type CommodityInfo struct {
	// Basic info
	Description     string `json:"description"`
	ReferenceNumber string `json:"referenceNumber"` // Product SKU, item number, or product code
	Quantity        int    `json:"quantity"`

	// Dimensions
	Length         float64 `json:"length"`
	Width          float64 `json:"width"`
	Height         float64 `json:"height"`
	DimensionsUnit string  `json:"dimensionsUnit"`
	WeightTotal    float64 `json:"weightTotal"`

	// Additional info
	HandlingQuantity  int       `json:"handlingQuantity"` // Idk diff b/w quantity and handling quantity
	TotalPieces       ValueUnit `json:"totalPieces"`      // for turvo
	GrossWeight       ValueUnit `json:"grossWeight"`      // for turvo
	NetWeight         ValueUnit `json:"netWeight"`        // for turvo
	PackagingType     string    `json:"packagingType"`
	HazardousMaterial bool      `json:"hazardousMaterial"`
	FreightClass      string    `json:"freightClass"`
	NMFC              string    `json:"nmfc"`
	NMFCSub           string    `json:"nmfcSub"`
	PackagingGroup    string    `json:"packagingGroup"`
}

type CommodityInfoList []CommodityInfo

// Implements sql.Scanner interface
func (cil *CommodityInfoList) Scan(value any) error {
	if value == nil {
		*cil = nil
		return nil
	}

	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for CommodityInfoList: %T", value)
	}

	var commoditiesList CommodityInfoList
	if err := json.Unmarshal(val, &commoditiesList); err != nil {
		return err
	}
	*cil = commoditiesList

	return nil
}

// Implement driver.Valuer interface
func (cil CommodityInfoList) Value() (driver.Value, error) {
	if len(cil) == 0 {
		return nil, nil
	}

	return json.Marshal(cil)
}

var (
	_ sql.Scanner   = &Accessorials{}
	_ driver.Valuer = &Accessorials{}
)

type HazardClass []string

type Commodity struct {
	gorm.Model
	LoadID uint `gorm:"index" json:"loadID" validate:"required"`

	// Basic info
	Description     string `json:"description"`
	ReferenceNumber string `json:"referenceNumber"` // Product SKU, item number, or product code
	Quantity        int    `json:"quantity"`

	// Dimensions
	Length         float64 `json:"length"`
	Width          float64 `json:"width"`
	Height         float64 `json:"height"`
	DimensionsUnit string  `json:"dimensionsUnit"`
	WeightTotal    float64 `json:"weightTotal"`

	// Additional info
	HandlingQuantity             int         `json:"handlingQuantity"` // Idk diff b/w quantity and handling quantity
	TotalPieces                  ValueUnit   `json:"totalPieces"`      // for turvo
	GrossWeight                  ValueUnit   `json:"grossWeight"`      // for turvo
	NetWeight                    ValueUnit   `json:"netWeight"`        // for turvo
	PackagingType                string      `json:"packagingType"`
	HazardousMaterial            bool        `json:"hazardousMaterial"`
	FreightClass                 string      `json:"freightClass"`
	NMFC                         string      `json:"nmfc"`
	AdditionalMarkings           string      `json:"additionalMarkings"`
	UNNumber                     string      `json:"unNumber"`
	PackagingGroup               string      `json:"packagingGroup"`
	HazmatCustomClassDescription string      `json:"hazmatCustomClassDescription"`
	HazmatPieceDescription       string      `json:"hazmatPieceDescription"`
	HarmonizedCode               string      `json:"harmonizedCode"`
	HazardClasses                HazardClass `gorm:"type:VARCHAR(255)" json:"hazardClasses"`
}

func (h *HazardClass) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("value cannot cast to []byte")
	}

	*h = strings.Split(string(bytes), ",")

	return nil
}

func (h HazardClass) Value() (driver.Value, error) {
	if len(h) == 0 {
		return nil, nil
	}

	return strings.Join(h, ","), nil
}
