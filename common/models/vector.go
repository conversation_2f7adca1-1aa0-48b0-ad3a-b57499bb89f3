package models

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

type ContentType string

// Suggestion represents any type of suggestion that can be enhanced with smart patterns
type Suggestion interface {
	GetVectors() []Vector
	GetServiceID() uint
	GetSuggestionType() SuggestionCategory
	GetSuggestionID() uint
	GetContentType() ContentType
}

const (
	EmailBodyContent  ContentType = "email_body"
	AttachmentContent ContentType = "attachment"
)

// Vector represents a vector embedding of email or attachment content
type Vector struct {
	gorm.Model

	// Relationships
	EmailID   uint    `gorm:"index"`
	Email     Email   `json:"-"`
	ServiceID uint    `gorm:"index"`
	Service   Service `json:"-"`
	UserID    uint    `gorm:"index"`
	User      User    `json:"-"`

	// For attachment embeddings
	AttachmentExternalID string `gorm:"index"`

	// The content type (email_body, attachment)
	ContentType ContentType `gorm:"type:varchar(50);index"`

	// The vector embedding - using pgvector's vector type
	// 1536 dimensions for OpenAI's text-embedding-3-small model
	Embedding VectorEmbedding `gorm:"type:vector(1536)"`

	// Many-to-many relationships
	SuggestedLoadChanges []SuggestedLoadChange `gorm:"many2many:vector_suggested_load_changes"`
	QuoteRequests        []QuoteRequest        `gorm:"many2many:vector_quote_requests"`
	Emails               []Email               `gorm:"many2many:vector_emails"`
}

// VectorSearchResult represents a result from a vector similarity search
type VectorSearchResult struct {
	Vector
	Distance float64 `json:"distance"`
}

// VectorEmbedding is a custom type that implements the sql.Scanner interface
// to properly handle PostgreSQL pgvector types
type VectorEmbedding []float64

func (v *VectorEmbedding) Scan(src any) error {
	var embedStr string
	switch s := src.(type) {
	case string:
		embedStr = s
	case []byte:
		embedStr = string(s)
	default:
		return fmt.Errorf("unsupported scan type for VectorEmbedding: %T", src)
	}

	// Parse PostgreSQL vector format: [val1,val2,val3,...]
	embedStr = strings.TrimPrefix(embedStr, "[")
	embedStr = strings.TrimSuffix(embedStr, "]")

	if embedStr == "" {
		*v = make(VectorEmbedding, 0)
		return nil
	}

	values := strings.Split(embedStr, ",")
	embedding := make(VectorEmbedding, len(values))

	for i, val := range values {
		f, err := strconv.ParseFloat(strings.TrimSpace(val), 64)
		if err != nil {
			return fmt.Errorf("failed to parse embedding value at index %d: %w", i, err)
		}
		embedding[i] = f
	}

	*v = embedding
	return nil
}

func (v VectorEmbedding) Value() (driver.Value, error) {
	if v == nil {
		return nil, nil
	}

	// Format as PostgreSQL vector string: [val1,val2,val3,...]
	var sb strings.Builder
	sb.WriteString("[")
	for i, val := range v {
		if i > 0 {
			sb.WriteString(",")
		}
		sb.WriteString(strconv.FormatFloat(val, 'f', -1, 64))
	}
	sb.WriteString("]")

	return sb.String(), nil
}
