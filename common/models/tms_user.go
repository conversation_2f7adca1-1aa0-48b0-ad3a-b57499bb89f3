package models

import "gorm.io/gorm"

type TMSUser struct {
	gorm.Model

	TMSID         uint        `gorm:"uniqueIndex:idx_tms_id_tms_user" json:"tmsID"`
	TMS           Integration `gorm:"foreignKey:TMSID" json:"-"`
	ExternalTMSID string      `gorm:"uniqueIndex:idx_tms_id_tms_user" json:"externalTMSID"`
	Username      string      `json:"username"` // e.g. Human-readable name, e.g. <PERSON>, <PERSON>
	EmailAddress  string      `gorm:"index" json:"emailAddress"`
	// aka office location or user code. For Mcleod Enterprise x Load Building
	RevenueCode string `json:"revenueCode"`
	// for Fetch Freight this is the brokers supervisor
	OperationsUser string `json:"operationsUser"`
}
