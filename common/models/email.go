package models

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type ClassificationApproach string
type EmailBodyType string
type ProcessingDuration time.Duration // Wrapped to implement custom DB scanning in seconds instead of nanoseconds

const (
	RegexApproach          ClassificationApproach = "regex"
	LLMApproach            ClassificationApproach = "llm"
	PlaintextEmailBodyType EmailBodyType          = "plaintext"
	MarkdownEmailBodyType  EmailBodyType          = "markdown"
)

type (
	// Used for emails received over the network
	IngestedEmail struct {
		// The userID of this msg according to the customer that self-hosts.
		ExternalUserID uint `json:"external_user_id"`
		// The ID of this msg according to the customer that self-hosts.
		ExternalEmailID uint `json:"external_email_id"`
		// bodyType is either "plaintext" or "markdown" - we tag the email for more accurate processing
		BodyType EmailBodyType `json:"body_type,omitempty"`
		// email body (up to 200 KB, limit of SQS max msg size)
		Body                 string `json:"body,omitempty"`
		BodyWithoutSignature string `json:"body_without_signature,omitempty"`
		Signature            string `json:"signature,omitempty"`
		// True if the Plaintext had to be truncated to fit in the SQS max msg size
		Truncated bool `json:"truncated,omitempty"`

		Label                string                 `json:"label"`
		ClassificationMethod ClassificationApproach `json:"classification_method,omitempty"`

		// ******* Common fields in Email and IngestedEmail ********
		// The email address and userID of the inbox account owner (will match the users table)
		Account   string `json:"account"`
		UserID    uint   `json:"user_id"`
		ServiceID uint   `json:"service_id"`

		// "Message-ID" RFC header value (will be the same for all recipients)
		RFCMessageID string `json:"rfc_message_id"`
		// The ID of this msg according to the email provider (e.g. Gmail/Outlook)
		ExternalID string    `json:"external_id"`
		ThreadID   string    `json:"thread_id"`
		SentAt     time.Time `json:"sent_at"`
		Sender     string    `json:"sender"`

		// These headers indicate that the message is a reply to a previous msg
		ThreadReferences string `json:"thread_references,omitempty"`
		InReplyTo        string `json:"in_reply_to,omitempty"`

		// Comma-delimited list of email addresses, e.g. "<EMAIL>, <EMAIL>"
		Recipients string `json:"recipients"`
		CC         string `json:"cc,omitempty"`

		Subject     string      `json:"subject,omitempty"`
		S3URL       string      `json:"s3_url,omitempty"`
		HasPDFs     bool        `json:"has_pdfs,omitempty"` // As of April 2025, only PDFs supported in LLM processing
		Attachments Attachments `gorm:"type:JSONB" json:"attachment_s3_urls,omitempty"`

		ProcessingMetadata `json:"-"` // Not marshalled to SQS JSON
	}

	// NOTE: If updating this struct, also update email.CopyProcessedDuplicate()
	Email struct {
		gorm.Model
		Service Service `json:"-"` // Foreign key object for ServiceID

		// *************** Common fields in Email and IngestedEmail ******************
		// The email address and userID of the inbox account owner (will match the users table)
		Account   string `json:"account"`
		UserID    uint   `json:"user_id" gorm:"index:idx_emails_user_sent,priority:1"`
		ServiceID uint   `json:"service_id"`
		// "Message-ID" RFC header value (will be the same for all recipients)
		RFCMessageID string `json:"rfc_message_id"`
		// The ID specific to the email provider (e.g. gmail) - unique for each recipient
		// For Gmail, this is a hex-encoded large int, e.g. "18ae1b45b4e58662"
		ExternalID string `gorm:"uniqueIndex"`

		ThreadID string `gorm:"index"`

		Subject    string
		SentAt     time.Time `gorm:"index;index:idx_emails_user_sent,priority:2,sort:desc"`
		Sender     string    `gorm:"index"`
		Recipients string
		CC         string
		// bodyType is either "plaintext" or "markdown" - we tag the email for more accurate processing
		BodyType             EmailBodyType
		Body                 string
		BodyWithoutSignature string `json:"body_without_signature,omitempty"`
		Signature            string `json:"signature,omitempty"`
		ThreadReferences     string
		InReplyTo            string
		WebLink              string // Direct link to email
		S3URL                string
		HasPDFs              bool        // As of April 2025, only PDFs supported in LLM processing
		Attachments          Attachments `gorm:"type:JSONB"`
		// ************************************************************************

		Labels               string // Drumkit-generated labels, not mailbox labels
		CategoryReasoning    string // Category classification reasoning - Classification Process
		LabelReasoning       string // Label classification reasoning - Classification Process
		ClassificationMethod ClassificationApproach

		Loads []Load `gorm:"many2many:email_loads;"`

		Quotes []QuickQuote `gorm:"foreignKey:LookedAtEmailID"`

		// Many-to-many relationship with Vector
		Vectors []Vector `gorm:"many2many:vector_emails"`
		// Processing metadata
		ProcessingMetadata        `gorm:"embedded"`
		ProcessingDurationSeconds ProcessingDuration `gorm:"type:float"` // Duration of email processing,
		CopiedFromEmailID         *uint              // ID of the original email that was copied to create this email

		// DEPRECATED; use GeneratedEmails. Indicates if email was generated by Drumkit and sent via Gmail/Outlook
		BeaconGenerated bool
		// DEPRECATED; use GeneratedEmails instead.
		Bounced bool
		// DEPRECATED; use GeneratedEmails instead.
		BouncedMessageID *uint
		// DEPRECATED; use GeneratedEmails instead
		BouncedMessage *Email
	}

	// An abridged version of the Email model that is used for on-premise email processing
	OnPremEmail struct {
		gorm.Model

		// The email address of the inbox account owner (will match the users table)
		// e.g. "<EMAIL>"
		Account string `gorm:"index"`

		// Drumkit UserID associated with the email account
		UserID uint `gorm:"index"`

		// Value of the "Message-ID" header - this will be the same for all recipients
		// e.g. "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>"
		RFCMessageID string

		// The ID specific to the email provider (e.g. gmail) - unique for each recipient
		// For Gmail, this is a hex-encoded large int, e.g. "18ae1b45b4e58662"
		ExternalID string `gorm:"uniqueIndex"`

		ThreadID string `gorm:"index"`

		SentAt           time.Time `gorm:"index"`
		ThreadReferences string
	}
)

type ProcessingMetadata struct {
	LambdaRequestID                     string // AWS Lambda request ID for tracing
	ProcessingStartTime                 time.Time
	SQSSentTimestamp                    time.Time // When ingestion lambda put message in SQS
	SQSEventID                          string
	SQSApproximateFirstReceiveTimestamp time.Time
	SQSApproximateReceiveCount          int // Approx bc multiple Lambdas may pick up message at the same time
}

func (e *IngestedEmail) Sanitize() map[string]any {
	return map[string]any{
		"account":           e.Account,
		"user_id":           e.UserID,
		"service_id":        e.ServiceID,
		"rfc_message_id":    e.RFCMessageID,
		"external_id":       e.ExternalID,
		"thread_id":         e.ThreadID,
		"sent_at":           e.SentAt,
		"thread_references": e.ThreadReferences,
		"in_reply_to":       e.InReplyTo,
		"recipients":        e.Recipients,
		"sender":            e.Sender,
		"cc":                e.CC,
		"subject":           e.Subject,
		"truncated":         e.Truncated,
		"s3_url":            e.S3URL,
	}
}

// Copy creates a new Email instance with the same content as the source email,
// but with new user-specific fields. dstEmailID is blank because it will be set
// after the email is created in the database.
// NOTE: src should have Loads preloaded!
func (src *Email) CopyProcessedDuplicate(ingestedCopy *Email) (dst *Email) {
	newMsg := *src
	// Zero-out Gorm system fields
	newMsg.ID = 0
	newMsg.CreatedAt = time.Time{}
	newMsg.UpdatedAt = time.Time{}

	// Re-assign user-specific fields
	newMsg.UserID = ingestedCopy.UserID
	newMsg.Account = ingestedCopy.Account
	newMsg.ServiceID = ingestedCopy.ServiceID
	newMsg.ExternalID = ingestedCopy.ExternalID
	newMsg.ThreadID = ingestedCopy.ThreadID
	newMsg.Attachments = ingestedCopy.Attachments
	newMsg.CopiedFromEmailID = &src.ID
	newMsg.LambdaRequestID = ingestedCopy.LambdaRequestID
	newMsg.ProcessingStartTime = ingestedCopy.ProcessingStartTime

	return &newMsg
}

var _ ProcessedCopier[*Email] = &Email{}

func (d *ProcessingDuration) Scan(value any) error {
	if value == nil {
		return nil
	}

	var seconds float64
	switch v := value.(type) {
	case float64:
		seconds = v
	case string:
		var err error
		seconds, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return fmt.Errorf("processing duration: failed to parse string %q as float: %w", v, err)
		}
	default:
		return fmt.Errorf("processing duration: expected float64 or string, got %T", value)
	}

	*d = ProcessingDuration(time.Duration(seconds) * time.Second)
	return nil
}

func (d ProcessingDuration) Value() (driver.Value, error) {
	return time.Duration(d).Seconds(), nil
}
