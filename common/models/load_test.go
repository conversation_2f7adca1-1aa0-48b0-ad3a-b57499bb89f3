package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestLoadDiff(t *testing.T) {
	fixedTime := time.Date(2023, time.January, 1, 0, 0, 0, 0, time.UTC)

	t.Run("should detect differences in Load if present", func(t *testing.T) {
		load1 := Load{
			Model:             gorm.Model{ID: 1},
			FreightTrackingID: "2884930",
			ServiceID:         1,
			LoadCoreInfo: LoadCoreInfo{
				Status:         "open",
				PONums:         "3333333,4444444,5555555",
				Specifications: Specifications{TotalOutPalletCount: 5},
				BillTo: BillTo{
					CompanyCoreInfo: CompanyCoreInfo{
						ExternalTMSID: "483",
						Name:          "ABC Corp",
						AddressLine1:  "123 Main St",
						AddressLine2:  "Apt 100",
						City:          "Los Angeles",
						State:         "CA",
						Zipcode:       "12345",
						Country:       "USA",
						Contact:       "<PERSON>",
						Phone:         "(*************",
						Email:         "<EMAIL>",
					},
				},
				Carrier: Carrier{
					MCNumber:                 "999",
					DOTNumber:                "555",
					Name:                     "XYZ Logistics",
					Phone:                    "(*************",
					Dispatcher:               "Jane Smith",
					SealNumber:               "12345",
					SCAC:                     "6789",
					FirstDriverName:          "Driver One",
					FirstDriverPhone:         "(*************",
					SecondDriverName:         "Driver Two",
					SecondDriverPhone:        "(*************",
					Email:                    "<EMAIL>",
					DispatchCity:             "Gotham",
					DispatchState:            "NY",
					ExternalTMSTruckID:       "123",
					ExternalTMSTrailerID:     "456",
					ConfirmationSentTime:     NullTime{Time: fixedTime, Valid: true},
					ConfirmationReceivedTime: NullTime{Time: fixedTime, Valid: true},
					DispatchedTime:           NullTime{Time: fixedTime, Valid: true},
					ExpectedPickupTime:       NullTime{Time: fixedTime, Valid: true},
					PickupStart:              NullTime{Time: fixedTime, Valid: true},
					PickupEnd:                NullTime{Time: fixedTime, Valid: true},
					ExpectedDeliveryTime:     NullTime{Time: fixedTime, Valid: true},
					DeliveryStart:            NullTime{Time: fixedTime, Valid: true},
					DeliveryEnd:              NullTime{Time: fixedTime, Valid: true},
					SignedBy:                 "Jane Smith",
				},
				Consignee: Consignee{
					CompanyCoreInfo: CompanyCoreInfo{
						ExternalTMSID: "333",
						Name:          "Alice Bob",
						AddressLine1:  "456 Main St",
						AddressLine2:  "Apt 700",
						City:          "Los Angeles",
						State:         "CA",
						Zipcode:       "12345",
						Country:       "USA",
						Contact:       "Alice Bob",
						Phone:         "(*************",
						Email:         "<EMAIL>",
					},
					BusinessHours: "9-5",
					RefNumber:     "1234",
					MustDeliver:   NullTime{Time: fixedTime, Valid: true},
					ApptStartTime: NullTime{Time: fixedTime, Valid: true},
					ApptNote:      "Sup",
				},
				Customer: Customer{
					CompanyCoreInfo: CompanyCoreInfo{
						ExternalTMSID: "345",
						Name:          "Bob Alice",
						AddressLine1:  "789 Main St",
						AddressLine2:  "Apt 500",
						City:          "Los Angeles",
						State:         "CA",
						Zipcode:       "12345",
						Country:       "USA",
						Contact:       "Bob Alice",
						Phone:         "(*************",
						Email:         "<EMAIL>",
					},
					RefNumber: "888",
				},
				Pickup: Pickup{
					CompanyCoreInfo: CompanyCoreInfo{
						ExternalTMSID: "555",
						Name:          "Charlie Charles",
						AddressLine1:  "999 Main St",
						AddressLine2:  "Apt 900",
						City:          "Los Angeles",
						State:         "CA",
						Zipcode:       "54321",
						Country:       "USA",
						Contact:       "Charlie Charles",
						Phone:         "(*************",
						Email:         "<EMAIL>",
					},
					BusinessHours: "9-5",
					RefNumber:     "324",
					ReadyTime:     NullTime{Time: fixedTime, Valid: true},
					ApptStartTime: NullTime{Time: fixedTime, Valid: true},
					ApptNote:      "",
				},
			},
		}

		load2 := load1

		expectedDiff := LoadDiff{
			Load:      nil,
			RateData:  nil,
			Customer:  nil,
			BillTo:    nil,
			Pickup:    nil,
			Consignee: nil,
			Carrier:   nil,
		}
		actualDiff := load1.Diff(load2)

		// there should be no differences here
		assert.Equal(t, expectedDiff, actualDiff, "Load.Diff should correctly identify differences if present")

		load2.Pickup.AddressLine1 = "666 Valley Rd"
		load2.Pickup.Email = "<EMAIL>"

		expectedDiff = LoadDiff{
			Load:      nil,
			RateData:  nil,
			Customer:  nil,
			BillTo:    nil,
			Pickup:    []string{"Address Line1", "Email"},
			Consignee: nil,
			Carrier:   nil,
		}
		actualDiff = load1.Diff(load2)

		// there should be differences here
		assert.Equal(t, expectedDiff, actualDiff, "Load.Diff should correctly identify differences if present")
	})
}
