package models

import (
	"time"

	"gorm.io/gorm"
)

// OrderStatus represents the current state of an order
type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "pending"     // Initial state when order is created
	OrderStatusAccepted   OrderStatus = "accepted"    // Order has been accepted and is being processed
	OrderStatusRejected   OrderStatus = "rejected"    // Order has been rejected
	OrderStatusCancelled  OrderStatus = "cancelled"   // Order has been cancelled
	OrderStatusInProgress OrderStatus = "in_progress" // Order is being fulfilled (has associated loads)
	OrderStatusCompleted  OrderStatus = "completed"   // Order has been fully fulfilled
)

// OrderStop represents a pickup or delivery location for an order
type OrderStop struct {
	gorm.Model
	OrderID           uint        `json:"orderId"`
	Type              string      `json:"type"` // "pickup" or "delivery"
	ExpectedAt        *time.Time  `json:"expectedAt"`
	Items             []OrderItem `gorm:"foreignKey:StopID" json:"items"`
	AppointmentNumber string      `json:"appointmentNumber"`
	AppointmentTime   *time.Time  `json:"appointmentTime"`
	ContactID         string      `json:"contactId"`
	DepartedAt        *time.Time  `json:"departedAt"`
}

// OrderItem represents an item in a pickup or delivery
type OrderItem struct {
	gorm.Model
	OrderID           uint    `json:"orderId"`
	StopID            uint    `json:"stopId"`
	Commodity         string  `json:"commodity"`
	Description       string  `json:"description"`
	TotalWeightLbs    float64 `json:"totalWeightLbs"`
	UnitWeightLbs     float64 `json:"unitWeightLbs"`
	ReceivedTotes     int     `json:"receivedTotes"`
	ProductNumber     string  `json:"productNumber"`
	ReceivedPallets   int     `json:"receivedPallets"`
	UnitPrice         float64 `json:"unitPrice"`
	PickupNumber      string  `json:"pickupNumber"`
	UnitCost          float64 `json:"unitCost"`
	ReceivedWeight    float64 `json:"receivedWeight"`
	EmptyPalletWeight float64 `json:"emptyPalletWeight"`
	UnitsPerPallet    int     `json:"unitsPerPallet"`
	TotalPackages     int     `json:"totalPackages"`
	PackagingType     string  `json:"packagingType"`
	UnitCostCurrency  string  `json:"unitCostCurrency"`
	UnitPriceCurrency string  `json:"unitPriceCurrency"`
	ReceivedPackages  int     `json:"receivedPackages"`
}

// BillingItem represents a billing or cost item
type BillingItem struct {
	gorm.Model
	OrderID         uint       `json:"orderId"`
	ChargeCodeID    string     `json:"chargeCodeId"`
	Description     string     `json:"description"`
	Quantity        float64    `json:"qty"`
	RateType        string     `json:"rateType"`
	UnitRate        float64    `json:"unitRate"`
	SourceOrderID   string     `json:"sourceOrderId"`
	IsAssessorial   bool       `json:"isAssessorial"`
	Amount          float64    `json:"amount"`
	ContactID       string     `json:"contactId"`
	Currency        string     `json:"currency"`
	ChargeCode      ChargeCode `gorm:"embedded" json:"chargeCode"`
	ReferenceNumber string     `json:"referenceNumber"`
}

// ChargeCode represents a charge code for billing items
type ChargeCode struct {
	Description  string `json:"description"`
	SumsToTotals bool   `json:"sumsToTotals"`
	Type         string `json:"type"`
	Name         string `json:"name"`
	ID           string `json:"id"`
}

// Branch represents a branch office or location
type Branch struct {
	TeamID   string `json:"teamId"`
	Name     string `json:"name"`
	ID       uint   `json:"id"`
	Slug     string `json:"slug"`
	IsActive bool   `json:"isActive"`
}

// OrderCoreInfo contains the core information about an order
type OrderCoreInfo struct {
	ExternalOrderID  string   `json:"externalOrderId"`
	OrderTrackingID  string   `json:"orderTrackingId" gorm:"uniqueIndex"`
	ExternalLoadID   string   `json:"externalLoadId"`
	PONums           string   `json:"poNums"`
	Status           string   `json:"status"`
	Mode             string   `json:"mode"`             // FTL (full truckload), LTL (Less-Than-Truckload), etc
	MoreThanTwoStops bool     `json:"moreThanTwoStops"` // LTL order does not necessarily have more than 2 stops
	Customer         Customer `gorm:"embedded;embeddedPrefix:customer_" json:"customer"`
	BillTo           BillTo   `gorm:"embedded;embeddedPrefix:billto_" json:"billTo"`
	RateData         RateData `gorm:"embedded;embeddedPrefix:ratedata_" json:"rateData"`

	// Stops relationship
	Pickups    []OrderStop `gorm:"foreignKey:OrderID" json:"pickups"`
	Deliveries []OrderStop `gorm:"foreignKey:OrderID" json:"deliveries"`

	// Pickup aka Origin, Shipper
	Pickup Pickup `gorm:"embedded;embeddedPrefix:pickup_" json:"pickup"`
	// Consignee aka Delivery, Dropoff, Receiver
	Consignee      Consignee      `gorm:"embedded;embeddedPrefix:consignee_" json:"consignee"`
	Specifications Specifications `gorm:"embedded;embeddedPrefix:specifications_" json:"specifications"`
	Notes          Notes          `gorm:"type:JSONB" json:"notes"`

	// Date-only fields for pickup and dropoff (day, month, year)
	PickupDate  time.Time `json:"pickupDate" gorm:"type:date"`
	DropoffDate time.Time `json:"dropoffDate" gorm:"type:date"`

	// Additional fields from XML
	FreightTerms      string      `json:"freightTerms"`
	IsPrePayment      bool        `json:"isPrePayment"`
	Currency          string      `json:"currency"`
	TotalWeight       float64     `json:"totalWeight"`
	TotalVolume       float64     `json:"totalVolume"`
	PieceCount        int         `json:"pieceCount"`
	HandlingUnitCount int         `json:"handlingUnitCount"`
	IsHazmat          bool        `json:"isHazmat"`
	OrderLines        []OrderLine `gorm:"foreignKey:OrderID" json:"orderLines"`

	// Additional fields from JSON
	TotalWeightLbs        float64 `json:"totalWeightLbs"`
	ReceivedTotalWeight   float64 `json:"receivedTotalWeight"`
	TotalTotes            int     `json:"totalTotes"`
	ReceivedTotalTotes    int     `json:"receivedTotalTotes"`
	TotalPackages         int     `json:"totalPackages"`
	ReceivedTotalPackages int     `json:"receivedTotalPackages"`
	TotalPallets          int     `json:"totalPallets"`
	ReceivedTotalPallets  int     `json:"receivedTotalPallets"`
	TotalAmount           float64 `json:"totalAmount"`
}

// Order represents a customer order that can be fulfilled by one or more loads
type Order struct {
	gorm.Model
	OrderCoreInfo

	// The load that this order belongs to
	LoadID         uint   `json:"loadId"`
	ExternalLoadID string `json:"externalLoadId"`
	Load           Load   `json:"-"`

	// Customer's requested pickup and delivery dates
	RequestedPickupDate   time.Time `json:"requestedPickupDate"`
	RequestedDeliveryDate time.Time `json:"requestedDeliveryDate"`

	// Order priority (e.g., "high", "medium", "low")
	Priority string `json:"priority"`

	// Whether the order has been fully fulfilled
	IsFulfilled bool `json:"isFulfilled"`

	// The service this order belongs to
	ServiceID uint    `json:"serviceID"`
	Service   Service `json:"-"`

	// Additional fields from XML
	OrganizationName  string `json:"organizationName"`
	OrderTMSStatus    string `json:"orderTMSStatus"`
	IsInPlanning      bool   `json:"isInPlanning"`
	IsHot             bool   `json:"isHot"`
	BillingStatus     string `json:"billingStatus"`
	IntegrationStatus string `json:"integrationStatus"`
	DoNotOverwrite    bool   `json:"doNotOverwrite"`
	NeedsReview       bool   `json:"needsReview"`

	// Additional fields from JSON
	IsBooked      bool          `json:"isBooked"`
	IsVoided      bool          `json:"isVoided"`
	Reference     string        `json:"reference"`
	SalesTeam     string        `json:"salesTeam"`
	BillingItems  []BillingItem `gorm:"type:JSONB" json:"billingItems"`
	CostItems     []BillingItem `gorm:"type:JSONB" json:"costItems"`
	TruckBrokerID string        `json:"truckBrokerId"`
	CarrierID     string        `json:"carrierId"`
	CreatorID     string        `json:"creatorId"`
	Type          string        `json:"type"`
	CrossdockType string        `json:"crossdockType"`
	CustomerID    string        `json:"customerId"`
}

// OrderLine represents a line item in an order
type OrderLine struct {
	gorm.Model
	OrderID              uint    `json:"orderId"`
	OrdLineNum           int     `json:"ordLineNum"`
	FreightClass         string  `json:"freightClass"`
	Description          string  `json:"description"`
	WeightGross          float64 `json:"weightGross"`
	WeightNet            float64 `json:"weightNet"`
	VolumeGross          float64 `json:"volumeGross"`
	VolumeNet            float64 `json:"volumeNet"`
	PieceCount           int     `json:"pieceCount"`
	PieceType            string  `json:"pieceType"`
	HandlingUnitCount    int     `json:"handlingUnitCount"`
	HandlingUnitTypeName string  `json:"handlingUnitTypeName"`
	IsHazmat             bool    `json:"isHazmat"`
	IsFlaggedForDelete   bool    `json:"isFlaggedForDelete"`
	IsFreightClassLocked bool    `json:"isFreightClassLocked"`
	IsNonStackable       bool    `json:"isNonStackable"`
}
