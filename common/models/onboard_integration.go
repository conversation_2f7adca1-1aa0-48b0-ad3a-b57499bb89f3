package models

import "time"

type (
	OnboardTMSRequest struct {
		Name              string
		APIKey            string
		AppID             string
		Username          string
		Password          string
		AccessToken       string
		Tenant            string
		TwoFactorSecret   string
		AuthorizationCode string
		UserGroups        []string
	}

	OnboardTMSResponse struct {
		APIKey                    string
		AppID                     string
		Username                  string
		Password                  string
		EncryptedPassword         string
		AccessToken               string
		RefreshToken              string
		Tenant                    string
		AccessTokenExpirationDate time.Time
		TwoFactorSecret           string
	}

	OnboardCarrierVerificationRequest struct {
		Name              string
		Username          string
		Password          string
		APIKey            string
		AuthorizationCode string
		UserGroups        string
	}

	OnboardCarrierVerificationResponse struct {
		Username                  string
		EncryptedPassword         string
		AccessToken               string
		RefreshToken              string
		APIKey                    string
		AccessTokenExpirationDate time.Time
	}

	OnboardPricingRequest struct {
		Name              string
		Username          string
		Password          string
		APIKey            string
		AuthorizationCode string
		UserEmailAddress  string
		UserGroups        string
		Tenant            string
		AppID             string
		Note              string
	}

	OnboardPricingResponse struct {
		Username                  string
		EncryptedPassword         string
		AccessToken               string
		RefreshToken              string
		APIKey                    string
		AccessTokenExpirationDate time.Time
	}

	OnboardSchedulerRequest struct {
		Name          string   `json:"name"`
		APIKey        string   `json:"apiKey"`
		AppID         string   `json:"appID"`
		Username      string   `json:"username"`
		Password      string   `json:"password"`
		AccessToken   string   `json:"accessToken"`
		Tenant        string   `json:"tenant"`
		Note          string   `json:"note"`
		UserGroups    []string `json:"userGroups"`
		IsServiceWide bool     `json:"is_service_wide"`
		UserGroup     string   `json:"user_group,omitempty"`
	}

	OnboardSchedulerResponse struct {
		APIKey                    string
		AppID                     string
		Username                  string
		EncryptedPassword         string
		AccessToken               string
		AccessTokenExpirationDate time.Time
		RefreshToken              string
		Tenant                    string
		Note                      string
	}
)
