package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseCustomDateTime(t *testing.T) {
	t.Run("Date with full year", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 0, 0, 0, 0, time.UTC), res)
	})

	t.Run("Date with abbrv year", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/23")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 0, 0, 0, 0, time.UTC), res)
	})

	t.Run("Date and time,", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023, 8:53:05")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Date and time, AM", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Date and time, PM", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023, 8:53:05 PM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 20, 53, 5, 0, time.UTC), res)
	})

	t.Run("Date and time, abbrv year", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/23, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Single-digit month", func(t *testing.T) {
		res, err := parseCustomDateTime("9/04/23, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 9, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Single-digit day", func(t *testing.T) {
		res, err := parseCustomDateTime("09/4/23, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 9, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Single-digit month & day, short year", func(t *testing.T) {
		res, err := parseCustomDateTime("9/4/23, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 9, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Single-digit month & day, full year", func(t *testing.T) {
		res, err := parseCustomDateTime("9/4/2023, 8:53:05 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 9, 4, 8, 53, 5, 0, time.UTC), res)
	})

	t.Run("Date and time, no seconds", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/23, 8:53 AM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 8, 53, 0, 0, time.UTC), res)
	})

	t.Run("No second space", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023, 8:00:00PM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 20, 0, 0, 0, time.UTC), res)
	})

	t.Run("24-hour format with PM converts to 12-hour", func(t *testing.T) {
		res, err := parseCustomDateTime("10/04/2023, 14:00:00 PM")

		assert.NoError(t, err)
		assert.Equal(t, time.Date(2023, 10, 4, 14, 0, 0, 0, time.UTC), res)
	})
}

func TestNullTime(t *testing.T) {
	t.Run("Empty string", func(*testing.T) {
		jsonStr := `""`

		var result NullTime
		err := json.Unmarshal([]byte(jsonStr), &result)

		require.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("Null JSON", func(t *testing.T) {
		jsonStr := `null`

		var result NullTime
		err := json.Unmarshal([]byte(jsonStr), &result)

		require.NoError(t, err)
		assert.Empty(t, result)
	})

	t.Run("OK", func(t *testing.T) {
		s := "10/06/2023, 8:00:00 PM"
		b, err := json.Marshal(s)
		require.NoError(t, err)

		expected := NullTime{
			Time:  time.Date(2023, 10, 06, 20, 0, 0, 0, time.UTC),
			Valid: true,
		}

		var result NullTime
		err = json.Unmarshal(b, &result)

		require.NoError(t, err)
		assert.Equal(t, expected, result)
	})

	t.Run("Invalid value", func(t *testing.T) {
		s := "10/06/023, 8:00:00"
		b, err := json.Marshal(s)
		require.NoError(t, err)

		var result NullTime
		err = json.Unmarshal(b, &result)

		// No error is expected since NullTime can handle invalid time.
		assert.NoError(t, err)
		// Should be zero time and valid set to false.
		assert.False(t, result.Valid)
		assert.Equal(t, time.Time{}, result.Time)
	})

}
