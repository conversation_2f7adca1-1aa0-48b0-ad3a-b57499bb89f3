package models

type TransportType string

const (
	FlatbedTransportType TransportType = "FLATBED"
	VanTransportType     TransportType = "VAN"
	ReeferTransportType  TransportType = "REEFER"

	// Additional transport types
	HotShotTransportType       TransportType = "HOTSHOT"        // aka FLATBED HOTSHOT
	BoxTruckTransportType      TransportType = "BOX TRUCK"      // aka VAN
	SprinterTransportType      TransportType = "SPRINTER"       // aka VAN
	DryVanTransportType        TransportType = "DRY VAN"        // Currently supported in LoadBuilding, not QQ
	StraightTruckTransportType TransportType = "STRAIGHT TRUCK" // Currently supported in LoadBuilding, not QQ
	CargoVanTransportType      TransportType = "CARGO VAN"      // Currently supported in LoadBuilding, not QQ

	// Generic catch all for excluded special equipment
	SpecialTransportType TransportType = "SPECIAL"

	// TODO: These are load modes, not transport types. Refactor references
	FTLTransportType TransportType = "FTL"
	LTLTransportType TransportType = "LTL"
)

func ListQuickQuoteTransportTypes() []TransportType {
	return []TransportType{
		FlatbedTransportType,
		VanTransportType,
		ReeferTransportType,
		HotShotTransportType,
		BoxTruckTransportType,
	}
}

func ListLoadBuildingTransportTypes() []TransportType {
	return []TransportType{
		FlatbedTransportType,
		VanTransportType,
		ReeferTransportType,
		HotShotTransportType,
		BoxTruckTransportType,
		DryVanTransportType,
		StraightTruckTransportType,
		CargoVanTransportType,
	}
}

func TransportTypesToStrings(types []TransportType) []string {
	result := make([]string, len(types))
	for i, t := range types {
		result[i] = string(t)
	}
	return result
}
