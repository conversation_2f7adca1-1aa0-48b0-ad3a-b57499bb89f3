package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type (
	// Appointment is a delivery appt which has been successfully submitted via Drumkit.
	Appointment struct {
		gorm.Model

		// The email address of the inbox account owner that scheduled the appt (will match the users table)
		// e.g. "<EMAIL>"
		Account       string      `gorm:"index" json:"-"`
		UserID        uint        `gorm:"index" json:"-"`
		User          User        `json:"-"`
		ServiceID     uint        `gorm:"index" json:"-"`
		Service       Service     `json:"-"`
		IntegrationID uint        `gorm:"index" json:"integrationId"`
		Integration   Integration `json:"-"`

		FreightTrackingID string `gorm:"index" json:"freightTrackingId"`
		LoadID            uint   `gorm:"index" json:"loadId"`
		Load              Load   `json:"-"`

		// Optional association with TMS customer
		TMSCustomerID *uint        `gorm:"index;default:NULL" json:"tmsCustomerId"`
		TMSCustomer   *TMSCustomer `gorm:"foreignKey:TMSCustomerID" json:"tmsCustomer,omitempty"`
		// Optional customer name (pulled from load.Customer.Name) used as fallback query method via fuzzy matching
		CustomerName *string `gorm:"index;default:NULL" json:"customerName,omitempty"`

		// Appointment ID in the external system (e.g. Opendock)
		ExternalID string `validate:"required"`
		// Confirmation number may be different from ID (e.g. Opendock)
		ConfirmationNo    string
		ExternalPrivateID int

		// Delivery details
		ExternalWarehouseID string
		WarehouseID         uint
		Warehouse           Warehouse `json:"-"`

		LoadTypeID     string
		DockID         string
		Date           string
		TimePreference string
		PONums         string
		RefNumber      string
		CcEmails       pq.StringArray `gorm:"type:text[]"`
		StartTime      time.Time
		Notes          string
		Source         IntegrationName

		Status string

		EmailTemplateID uint
		EmailBody       string
	}

	// A scheduled appointment.
	ScheduledAppointment struct {
		Request            MakeAppointmentRequest
		ID                 string `json:"id"`
		ConfirmationNumber string `json:"confirmationNumber"`

		CreateDateTime       time.Time                 `json:"createDateTime"`
		CreatedBy            string                    `json:"createdBy"`
		LastChangedDateTime  time.Time                 `json:"lastChangedDateTime"`
		LastChangedBy        string                    `json:"lastChangedBy"`
		IsActive             bool                      `json:"isActive"`
		Tags                 []string                  `json:"tags"`
		Type                 string                    `json:"type"`
		Status               string                    `json:"status"`
		StatusTimeline       AppointmentStatusTimeline `json:"statusTimeline"`
		OrgID                string                    `json:"orgId"`
		Eta                  any                       `json:"eta"`
		RecurringParentID    any                       `json:"recurringParentId"`
		RecurringPattern     any                       `json:"recurringPattern"`
		Reschedules          any                       `json:"reschedules"`
		MuteNotifications    bool                      `json:"muteNotifications"`
		IsCheckedInByCarrier bool                      `json:"isCheckedInByCarrier"`
		CheckInAcknowledged  bool                      `json:"checkInAcknowledged"`
		RecurringParent      any                       `json:"recurringParent"`
	}

	AppointmentStatusTimeline struct {
		NoShow     time.Time `json:"noShow"`
		Arrived    time.Time `json:"arrived"`
		Cancelled  time.Time `json:"cancelled"`
		Completed  time.Time `json:"completed"`
		Requested  time.Time `json:"requested"`
		Scheduled  time.Time `json:"scheduled"`
		InProgress time.Time `json:"inProgress"`
	}

	// AppointmentData represents a single appointment request for bulk scheduling
	AppointmentData struct {
		Start             string `json:"start" validate:"required"`
		FreightTrackingID string `json:"freightTrackingId" validate:"required"`
		ZipCode           string `json:"zipCode,omitempty"`
		City              string `json:"city,omitempty"`
		State             string `json:"state,omitempty"`
		Country           string `json:"country,omitempty"`
		AppointmentID     string `json:"appointmentId,omitempty"`   // For Manhattan
		FacilityID        string `json:"facilityId,omitempty"`      // For Manhattan
		FacilityText      string `json:"facilityText,omitempty"`    // For Manhattan
		AppointmentType   string `json:"appointmentType,omitempty"` // For Manhattan
		Scac              string `json:"scac,omitempty"`
		ProNumber         string `json:"proNumber"`       // For Costco
		DoorType          string `json:"doorType"`        // For Costco
		UnloadType        string `json:"unloadType"`      // For Costco
		Commodity         string `json:"commodity"`       // For Costco
		ContainerNumber   string `json:"containerNumber"` // For Costco
		LinkLoadID        string `json:"linkLoadId"`      // For Costco
		Notes             string `json:"notes"`           // For Costco
		DepotValue        string `json:"depotValue"`      // For Costco
		Uom               string `json:"uom"`             // For Costco
		QtyCount          int    `json:"qtyCount"`        // For Costco
	}

	// MakeAppointmentRequest encapsulates details for scheduling appointments.
	MakeAppointmentRequest struct {
		// User-provided
		StartTime         time.Time  `json:"start" validate:"required"`
		EndTime           *time.Time `json:"end,omitempty"`
		WarehouseID       string     `json:"warehouseID,omitempty"`
		WarehouseTimezone string     `json:"warehouseTimezone"`
		DockID            string     `json:"dockId"`
		LoadTypeID        string     `json:"loadTypeId" validate:"required"`
		Operation         string     `json:"operation,omitempty"`
		Company           string     `json:"company,omitempty"`
		// Automatically filled by MakeAppointment
		UserID         string         `json:"userId,omitempty"`
		RefNumber      string         `json:"refNumber,omitempty"`
		PONums         string         `json:"poNums"`
		Notes          string         `json:"notes,omitempty"`
		CcEmails       []string       `json:"ccEmails,omitempty"`
		ContactDetails ContactDetails `json:"contactDetails"`
		// Hard-coded defaults for 7/11; provided by user for other warehouses
		CustomFields CustomApptFieldsTemplate `json:"customFields,omitempty"`
		//nolint:lll // Specific to 7/11
		TrailerType string `json:"trailerType,omitempty" validate:"required,oneof='Cargo Van' 'LTL/FTL Trailer' 'Passenger Vehicle' 'Straight Truck'"`
		RequestType string `json:"requestType,omitempty"`

		ApptKey   string `json:"appointmentKey,omitempty"`
		Weight    int    `json:"weight,omitempty"`
		TrailerID string `json:"trailerID,omitempty"`
		// For bulk support
		AppointmentType string            `json:"appointmentType,omitempty"`
		StopType        string            `json:"stopType,omitempty"`
		Appointments    []AppointmentData `json:"appointments,omitempty"`
		// OneNetwork-specific
		SearchBy SearchBy `json:"searchBy,omitempty"`
		// For E2open
		AppointmentDate string `json:"appointmentDate,omitempty"`
		ProIDFieldName  string `json:"proIdFieldName,omitempty"`
	}

	// Dock represents a loading dock within a warehouse.
	Dock struct {
		ID          string   `json:"id"`
		Name        string   `json:"name"`
		WarehouseID string   `json:"warehouseId"`
		LoadTypeIDs []string `json:"loadTypeIds"`
	}

	// LoadType encapsulates details related to a loading type.
	LoadType struct {
		ID                     string           `json:"id"`
		Name                   string           `json:"name"`
		WarehouseID            string           `json:"warehouseId"`
		EquipmentType          string           `json:"equipmentType"`
		CreateDateTime         time.Time        `json:"createDateTime"`
		CreatedBy              string           `json:"createdBy"`
		LastChangedDateTime    time.Time        `json:"lastChangedDateTime"`
		LastChangedBy          string           `json:"lastChangedBy"`
		IsActive               bool             `json:"isActive"`
		Tags                   any              `json:"tags"`
		AllowCarrierScheduling bool             `json:"allowCarrierScheduling"`
		DurationMin            int              `json:"duration_min"`
		OrgID                  string           `json:"orgId"`
		Direction              string           `json:"direction"`
		Operation              string           `json:"operation"`
		TransportationMode     string           `json:"transportationMode"`
		Description            string           `json:"description"`
		Settings               LoadTypeSettings `json:"settings"`
		Schedule               LoadTypeSchedule `json:"schedule"`
	}

	// LoadTypeSettings configures load type-specific settings.
	LoadTypeSettings struct {
		AllowCarrierDockSelection bool `json:"allowCarrierDockSelection"`
	}

	// LoadTypeSchedule defines schedules for different days.
	LoadTypeSchedule struct {
		Friday          []ScheduleHours `json:"friday"`
		Monday          []ScheduleHours `json:"monday"`
		Sunday          []ScheduleHours `json:"sunday"`
		Tuesday         []ScheduleHours `json:"tuesday"`
		Saturday        []ScheduleHours `json:"saturday"`
		Thursday        []ScheduleHours `json:"thursday"`
		Wednesday       []ScheduleHours `json:"wednesday"`
		ClosedIntervals []any           `json:"closedIntervals"`
		Version         int             `json:"version"`
	}

	// ScheduleHours defines the working hours for a day.
	ScheduleHours struct {
		End   string `json:"end"`
		Start string `json:"start"`
	}

	// Slot represents an available time slot with associated dock.
	Slot struct {
		Dock       Dock        `json:"dock"`
		StartTimes []time.Time `json:"startTimes"`
		Capacity   int         `json:"capacity"`
	}

	SchedulingUser struct {
		ID                       string    `json:"id"`
		CreateDateTime           time.Time `json:"createDateTime"`
		LastChangedDateTime      time.Time `json:"lastChangedDateTime"`
		IsActive                 bool      `json:"isActive"`
		Tags                     any       `json:"tags"`
		Email                    string    `json:"email"`
		FirstName                string    `json:"firstName"`
		LastName                 string    `json:"lastName"`
		IsEmailVerified          bool      `json:"isEmailVerified"`
		Role                     string    `json:"role"`
		OrgID                    any       `json:"orgId"`
		CompanyID                string    `json:"companyId"`
		Phone                    string    `json:"phone"`
		CreatedBy                any       `json:"createdBy"`
		LastChangedBy            string    `json:"lastChangedBy"`
		InvalidLoginAttempts     int       `json:"invalidLoginAttempts"`
		WarehouseAccessList      any       `json:"warehouseAccessList"`
		Extension                any       `json:"extension"`
		TcConfirmedAt            any       `json:"tcConfirmedAt"`
		LastLoginAt              any       `json:"lastLoginAt"`
		PasswordResetRequired    bool      `json:"passwordResetRequired"`
		PasswordResetEmailSentAt any       `json:"passwordResetEmailSentAt"`
		OrgIsActive              any       `json:"orgIsActive"`
		OrgName                  any       `json:"orgName"`
		OrgCreateDateTime        any       `json:"orgCreateDateTime"`
		OrgType                  any       `json:"orgType"`
	}

	// GetDocksRequest encapsulates parameters for fetching docks.
	GetDocksRequest struct {
		Search string // required - never fetch all docks for all warehouses
		Page   int
		Limit  int
	}

	// GetLoadTypesRequest encapsulates parameters for fetching load types.
	GetLoadTypesRequest struct {
		// (Required)
		WarehouseID string
		Page        int
		Limit       int
		Search      string // EX: {"allowCarrierScheduling":true}
	}

	// GetOpenSlotsRequest encapsulates parameters for fetching open slots.
	GetOpenSlotsRequest struct {
		Start             time.Time   `json:"start" validate:"required"` // RFC3339
		End               time.Time   `json:"end" validate:"required"`   // RFC3339
		IncludeStartTimes bool        `json:"includeStartTimes"`
		WarehouseID       string      `json:"warehouseId"`
		Warehouse         *Warehouse  `json:"warehouse,omitempty"`
		PONumbers         []string    `json:"poNums"`
		RequestType       RequestType `json:"requestType,omitempty"`
		Operation         string      `json:"operation,omitempty"`
		Company           string      `json:"company,omitempty"`
		AppointmentType   string      `json:"appointmentType,omitempty"`
		// `Inbound`, `TargetAirFreightOnly`, `TaxAirFreight`, or `Elwood`
		FilterType string `json:"filterType,omitempty"`

		// Warehouse Details
		ZipCode string `json:"zipCode,omitempty"`
		City    string `json:"city,omitempty"`
		State   string `json:"state,omitempty"`
		Country string `json:"country,omitempty"`

		// OneNetwork
		SearchBy SearchBy `json:"searchBy,omitempty"`
		// For Manhattan
		AppointmentID string `json:"appointmentId,omitempty"`
		FacilityID    string `json:"facilityId,omitempty"`
		FacilityText  string `json:"facilityText,omitempty"`
		Tenant        string `json:"tenant,omitempty"`
		FlowType      string `json:"flowType,omitempty"`
		// For E2open
		AppointmentDate string `json:"appointmentDate,omitempty"`
		ProIDFieldName  string `json:"proIdFieldName,omitempty"`
	}

	GetAppointmentsRequest struct {
		StartDate   time.Time         `json:"startDate"`
		EndDate     time.Time         `json:"endDate"`
		WarehouseID string            `json:"warehouseId"`
		Warehouse   *Warehouse        `json:"warehouse,omitempty"`
		Status      AppointmentStatus `json:"status"`
		CustomerID  string            `json:"customerId"`

		// When true, will return error if no appointments are found (default should be true)
		//  - Used to fail open when no appointments are found (used in Yardview get open slots flow — we don't want to
		//    fail to get open slots because there are no appointments in the date range)
		ErrorOnEmptyAppointments bool `json:"errorOnEmptyAppointments"`
	}

	// WarehouseDetails contains warehouse data for responses.
	WarehouseDetails struct {
		// Neutron's ID for the warehouse, equal to SevenElevenNeutronWarehouseID
		ID                       string                   `json:"id"`
		Name                     string                   `json:"name"`
		Street                   string                   `json:"street"`
		City                     string                   `json:"city"`
		State                    string                   `json:"state"`
		Zip                      string                   `json:"zip"`
		Email                    string                   `json:"email"`
		Phone                    string                   `json:"phone"`
		Timezone                 string                   `json:"timezone"`
		CustomApptFieldsTemplate CustomApptFieldsTemplate `json:"customApptFieldsTemplate"`
		Settings                 WarehouseSettings        `json:"settings"`
		DefaultSubscribedEmail   string                   `json:"defaultSubscribedEmail"`
	}

	ValidatedPONumber struct {
		PONumber   string            `json:"poNumber"`
		IsValid    bool              `json:"isValid"`
		Facilities map[string]string `json:"facilities"`
		DoorType   map[string]string `json:"doorType"`
		Error      string            `json:"error"`
	}

	ContactDetails struct {
		Phone string `json:"phone"`
		Email string `json:"email"`
	}
)
