package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type (
	SendStatus      string
	MilestoneStatus string
)

const (
	SentStatus     SendStatus = "sent"
	PendingStatus  SendStatus = "pending"
	CanceledStatus SendStatus = "canceled"
	FailedStatus   SendStatus = "failed"

	DispatchMilestone  MilestoneStatus = "dispatch"
	PickupMilestone    MilestoneStatus = "pickup"
	LoadedMilestone    MilestoneStatus = "loaded"
	InTransitMilestone MilestoneStatus = "inTransit"
	DropoffMilestone   MilestoneStatus = "dropoff"
	UnloadedMilestone  MilestoneStatus = "unloaded"
)

type GeneratedEmail struct {
	gorm.Model

	ServiceID uint    `gorm:"index" json:""`
	Service   Service `json:"-"`
	// Drumkit UserID that actually sent the email. Settle in for a story kids:
	//
	// If UserID = TriggeredByUserID, then the email was sent by the user who triggered the email
	//
	// If UserID == TriggeredByUserID but "sender" != User<PERSON>'s email, then the email was sent from an alias
	// (e.g. <EMAIL> sent from one of her aliases, <EMAIL>)
	//
	// If UserID != TriggeredByUserID, then the email was sent directly from a delegated inbox rather than an alias
	// And that kids, is how I met your mother.
	UserID uint `gorm:"index"`
	User   User `json:"-"`

	// Original Drumkit user who triggered the email, e.g. <EMAIL> was the user who was
	// made the request, but chose to send the <NAME_EMAIL> (UserID)
	TriggeredByUserID uint
	TriggeredByUser   User `gorm:"foreignKey:TriggeredByUserID" json:"-"`

	TriggeredByRuleID *uint                // EmailForwardingRule that triggered this email, optional
	TriggeredByRule   *EmailForwardingRule `json:"-"`

	// The ID specific to the email provider (e.g. gmail) - unique for each recipient
	// For Gmail, this is a hex-encoded large int, e.g. "18ae1b45b4e58662"
	ExternalID string
	ThreadID   string `gorm:"index"`

	// If isForward is true, then sent email will contain:
	//  - "Fwd:"" subject prefix ('FW:' for Outlook)
	//  - "----Forwarded Message----" body prefix
	IsForward                  bool
	ForwardedMessageID         *uint
	ForwardedMessage           *Email `json:"-"`
	ForwardedMessageExternalID string `gorm:"default:null"`
	ForwardedMessageThreadID   string `gorm:"default:null"`

	// Value of the "Message-ID" header - this will be the same for all recipients
	// e.g. "<CAMdwnNi+AWoNogE+Pn+XqOrDOXDy5wW=<EMAIL>>"
	RFCMessageID      string `gorm:"index"`
	FreightTrackingID string `gorm:"index"`

	ScheduleSend NullTime `gorm:"index"`
	SentAt       NullTime `gorm:"index"`

	Sender           string         `gorm:"index"`
	Recipients       pq.StringArray `gorm:"type:text[]"`
	CC               pq.StringArray `gorm:"type:text[]"`
	ThreadReferences string
	InReplyTo        pq.StringArray `gorm:"type:text[]"`

	Milestone      MilestoneStatus `gorm:"index"`
	Status         SendStatus      `gorm:"index"`
	CanceledReason string
	Subject        string

	// If IsForward=true, then before sending, this is the content that will be above the inline forwarded content.
	// After sending, this will contain both the added content and the original, forwarded content.
	Body string

	// S3URL may be empty if we directly passed through attachments to mail client instead of hosting ourselves
	Attachments Attachments `gorm:"type:JSONB"`
	WebLink     string      // Email's direct URL

	Bounced          bool   // Indicates if generated email bounce
	BouncedMessageID *uint  // Email that tells us why this generated email bounced
	BouncedMessage   *Email // Email that tells us why this generated email bounced

	Loads         []Load         `gorm:"many2many:generated_email_loads"`
	QuoteRequests []QuoteRequest `gorm:"many2many:quote_request_carrier_emails_1tomany"`
}

type PendingEmail struct {
	ID        uint      `json:"id"`
	Recipient string    `json:"recipient"`
	Subject   string    `json:"subject"`
	Body      string    `json:"body"`
	Timestamp time.Time `json:"timestamp"`
}

type PendingCarrierEmails struct {
	Pickup    []PendingEmail `json:"pickup"`
	Loaded    []PendingEmail `json:"loaded"`
	InTransit []PendingEmail `json:"inTransit"`
	Dropoff   []PendingEmail `json:"dropoff"`
	Unloaded  []PendingEmail `json:"unloaded"`
}

type PendingOutboxEmails struct {
	CarrierEmails PendingCarrierEmails `json:"carrierEmails"`
}
