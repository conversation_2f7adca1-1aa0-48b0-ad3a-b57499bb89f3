package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"gorm.io/gorm"
)

type BatchQuoteStatus string

const (
	BatchQuoteStatusProcessed BatchQuoteStatus = "processed"
	BatchQuoteStatusSubmitted BatchQuoteStatus = "submitted"
	BatchQuoteStatusError     BatchQuoteStatus = "error"
	BatchQuoteStatusCompleted BatchQuoteStatus = "completed"
)

// BatchQuote represents a collection of quote requests and associated quick quotes
// that were extracted/generated together, typically from a single email
type BatchQuote struct {
	gorm.Model

	User      User    `json:"-"`
	UserID    uint    `gorm:"index"`
	Service   Service `json:"-"`
	ServiceID uint    `gorm:"index"`
	Email     Email   `json:"-"`
	EmailID   uint    `gorm:"index"`
	ThreadID  string  `gorm:"index"`

	Status           BatchQuoteStatus `json:"status"` // e.g., "pending", "processed", "submitted"
	BraintrustLogIDs LogRecordList    `gorm:"type:JSONB" json:"braintrustLogIDs"`
	BraintrustSpanID string           `json:"braintrustSpanID"`

	// Many-to-many relationships
	QuoteRequests []QuoteRequest `gorm:"many2many:batch_quote_quote_requests;constraint:OnDelete:CASCADE;"`

	// Processing metadata
	SourceCategory   SourceCategory   `json:"sourceCategory"`                // e.g., "email", "quoting-portal"
	SourceExternalID string           `gorm:"index" json:"sourceExternalID"` // Email's external ID or other
	ProcessingErrors BatchQuoteErrors `gorm:"type:JSONB" json:"processingErrors"`

	// Batch summary information
	TotalQuoteRequests int     `json:"totalQuoteRequests"`
	AverageConfidence  float64 `json:"averageConfidence"` // Average confidence score across quotes
}

// BatchQuoteQuoteRequest is the join table for BatchQuote and QuoteRequest with ON DELETE CASCADE
// Ensures that when a BatchQuote or QuoteRequest is deleted, the join table rows are also deleted.
type BatchQuoteQuoteRequest struct {
	BatchQuoteID   uint `gorm:"primaryKey;constraint:OnDelete:CASCADE;"`
	QuoteRequestID uint `gorm:"primaryKey;constraint:OnDelete:CASCADE;"`
}

// BatchQuoteErrors holds any errors that occurred during batch processing
type BatchQuoteErrors struct {
	LLMExtractionErrors []string          `json:"llmExtractionErrors"`
	QuoteRequestErrors  map[uint][]string `json:"quoteRequestErrors"` // QuoteRequest ID -> errors
	QuickQuoteErrors    map[uint][]string `json:"quickQuoteErrors"`   // QuickQuote ID -> errors
	GeneralErrors       []string          `json:"generalErrors"`
}

// Implements sql.Scanner interface
func (bqe *BatchQuoteErrors) Scan(value any) error {
	if value == nil {
		*bqe = BatchQuoteErrors{}
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for BatchQuoteErrors: %T", value)
	}
	var errors BatchQuoteErrors
	if err := json.Unmarshal(val, &errors); err != nil {
		return err
	}
	*bqe = errors
	return nil
}

// Implement driver.Valuer interface
func (bqe BatchQuoteErrors) Value() (driver.Value, error) {
	if len(bqe.LLMExtractionErrors) == 0 &&
		len(bqe.QuoteRequestErrors) == 0 &&
		len(bqe.QuickQuoteErrors) == 0 &&
		len(bqe.GeneralErrors) == 0 {
		return nil, nil
	}
	return json.Marshal(bqe)
}

// HasErrors returns if the batch quote has any processing errors
func (bqe *BatchQuoteErrors) HasErrors() bool {
	return len(bqe.LLMExtractionErrors) > 0 ||
		len(bqe.QuoteRequestErrors) > 0 ||
		len(bqe.QuickQuoteErrors) > 0 ||
		len(bqe.GeneralErrors) > 0
}

// CopyProcessedDuplicate creates a copy of the batch quote for a different email/user
func (bq *BatchQuote) CopyProcessedDuplicate(email *Email) *BatchQuote {
	newBatchQuote := &BatchQuote{}

	// Set user-specific fields
	newBatchQuote.UserID = email.UserID
	newBatchQuote.ServiceID = email.ServiceID
	newBatchQuote.ThreadID = email.ThreadID
	newBatchQuote.EmailID = email.ID
	newBatchQuote.SourceExternalID = email.ExternalID

	// Copy specific fields
	newBatchQuote.SourceCategory = bq.SourceCategory
	newBatchQuote.ProcessingErrors = bq.ProcessingErrors
	newBatchQuote.Status = BatchQuoteStatusProcessed

	// Copy quote requests
	newBatchQuote.QuoteRequests = make([]QuoteRequest, len(bq.QuoteRequests))
	for i, qr := range bq.QuoteRequests {
		copiedQR := qr.CopyProcessedDuplicate(email)
		newBatchQuote.QuoteRequests[i] = *copiedQR
	}

	// Update summary counts
	newBatchQuote.TotalQuoteRequests = len(newBatchQuote.QuoteRequests)
	newBatchQuote.AverageConfidence = bq.AverageConfidence

	return newBatchQuote
}

// UpdateSummaryInfo updates the batch summary information based on associated quotes
func (bq *BatchQuote) UpdateSummaryInfo() {
	bq.TotalQuoteRequests = len(bq.QuoteRequests)

	// Calculate average confidence from all quick quotes associated with quote requests
	var totalConfidence float64
	var quickQuoteCount int
	for _, qr := range bq.QuoteRequests {
		for _, qq := range qr.QuickQuotes {
			totalConfidence += qq.ConfidenceLevel
			quickQuoteCount++
		}
	}
	if quickQuoteCount > 0 {
		bq.AverageConfidence = totalConfidence / float64(quickQuoteCount)
	} else {
		bq.AverageConfidence = 0
	}
}

var (
	_ sql.Scanner                  = &BatchQuoteErrors{}
	_ driver.Valuer                = &BatchQuoteErrors{}
	_ ProcessedCopier[*BatchQuote] = &BatchQuote{}
)
