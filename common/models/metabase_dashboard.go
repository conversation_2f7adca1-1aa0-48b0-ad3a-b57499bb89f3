package models

import (
	"gorm.io/gorm"
)

// MetabaseDashboard represents a public Metabase dashboard link associated with a service
type MetabaseDashboard struct {
	gorm.Model
	// URL of the public dashboard
	URL string `json:"url"`
	// UUID extracted from the URL
	UUID string `json:"uuid"`
	// Question ID of the dashboard
	QuestionID int `json:"questionId"`
	// Dashboard ID of the dashboard
	DashboardID int `json:"dashboardId"`
	// Name/title of the dashboard
	Name string `json:"name"`
	// Description of the dashboard (optional)
	Description string `json:"description"`
	// Service association
	ServiceID uint    `json:"serviceId"`
	Service   Service `json:"-"`
	// User association (optional)
	UserID *uint `json:"userId"`
	User   *User `json:"-"`
}
