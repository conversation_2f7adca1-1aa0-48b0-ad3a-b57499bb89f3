package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
)

type Attachment struct {
	// MsgId and AttachmentId required by Gmail API to download and upload the attachment to a new email
	MessageExternalID string `json:"messageExternalId"`
	// S3 does not support special chars so we transform it,
	// but we need to store the original name for user to map suggestion to corresponding file
	ExternalID string `json:"externalId"`
	// e.g. application/pdf, image/png, etc. https://www.ietf.org/rfc/rfc1521.txt
	// NOTE: MimeType may also be `application/octet-stream` which is a generic type for all binary data
	// so you should use utility function IsPDF to check if the attachment is a PDF, which is more reliable
	// than directly checking the MimeType
	MimeType            string `json:"mimeType"`
	IsInline            bool   `json:"isInline"`
	IsInSenderSignature bool   `json:"isInSenderSignature"` // Processor guesses using regex
	OriginalFileName    string `json:"originalFileName"`
	TransformedFileName string `json:"transformedFileName"`
	S3URL               string `json:"s3URL"`
}

type Attachments []Attachment

// Implement scanner and valuer interfaces for Attachments
func (a *Attachments) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for Attachments: %T", value)
	}
	var attachments []Attachment
	if err := json.Unmarshal(val, &attachments); err != nil {
		return err
	}
	*a = attachments
	return nil
}

func (a Attachments) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}

	return json.Marshal(a)
}

// Implement scanner and valuer interfaces for Attachment
func (a *Attachment) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return errors.New("value cannot cast to []byte")
	}
	var attachment Attachment
	if err := json.Unmarshal(val, &attachment); err != nil {
		return err
	}
	*a = attachment
	return nil
}

func (a Attachment) Value() (driver.Value, error) {
	return json.Marshal(a)
}
