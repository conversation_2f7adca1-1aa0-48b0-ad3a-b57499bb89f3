package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
)

type (
	Groups []Group
	Group  struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	}
)

// Implement scanner and valuer interfaces for TMSGroups
func (g *Groups) Scan(value any) error {
	if value == nil {
		return nil
	}

	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for TMSGroups: %T", value)
	}

	var groups []Group
	if err := json.Unmarshal(val, &groups); err != nil {
		return err
	}
	*g = groups
	return nil
}

func (g Groups) Value() (driver.Value, error) {
	if len(g) == 0 {
		return nil, nil
	}
	return json.Marshal(g)
}

// Implement scanner and valuer interfaces for Group
func (g *Group) Scan(value any) error {
	if value == nil {
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return errors.New("value cannot cast to []byte")
	}
	var group Group
	if err := json.Unmarshal(val, &group); err != nil {
		return err
	}
	*g = group
	return nil
}

func (g Group) Value() (driver.Value, error) {
	return json.Marshal(g)
}
