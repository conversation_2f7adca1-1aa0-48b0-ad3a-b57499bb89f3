package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCustomApptField_MarshalJSON(t *testing.T) {
	tests := []struct {
		name         string
		caf          CustomApptField
		expectedJSON string
	}{
		{
			name: "TypeIntString",
			caf: CustomApptField{
				Name:        "test",
				Type:        "int",
				Label:       "Test Label",
				Value:       "123",
				Description: "Test Description",
			},
			//nolint:lll
			expectedJSON: `{"name":"test","type":"int","label":"Test Label","value":123,"description":"Test Description","dropDownValues":null,"hiddenFromCarrier":false,"requiredForCarrier":false,"requiredForCheckIn":false,"requiredForWarehouse":false}`,
		},
		{
			name: "TypeIntEmptyString",
			caf: CustomApptField{
				Name:        "test",
				Type:        "int",
				Label:       "Test Label",
				Value:       "",
				Description: "Test Description",
				Placeholder: "",
			},
			//nolint:lll
			expectedJSON: `{"name":"test","type":"int","label":"Test Label","value":0,"description":"Test Description","dropDownValues":null,"hiddenFromCarrier":false,"requiredForCarrier":false,"requiredForCheckIn":false,"requiredForWarehouse":false}`,
		},
		{
			name: "TypeNotInt",
			caf: CustomApptField{
				Name:        "test",
				Type:        "str",
				Label:       "Test Label",
				Value:       "abc",
				Description: "Test Description",
			},
			//nolint:lll
			expectedJSON: `{"name":"test","type":"str","label":"Test Label","value":"abc","description":"Test Description","dropDownValues":null,"hiddenFromCarrier":false,"requiredForCarrier":false,"requiredForCheckIn":false,"requiredForWarehouse":false}`,
		},
		{
			name: "TypeIntIsInt",
			caf: CustomApptField{
				Name:        "test",
				Type:        "int",
				Label:       "Test Label",
				Value:       22,
				Description: "Test Description",
				Placeholder: "",
			},
			//nolint:lll
			expectedJSON: `{"name":"test","type":"int","label":"Test Label","value":22,"description":"Test Description","dropDownValues":null,"hiddenFromCarrier":false,"requiredForCarrier":false,"requiredForCheckIn":false,"requiredForWarehouse":false}`,
		},
		{
			name: "TypeIntIsFloat",
			caf: CustomApptField{
				Name:        "test",
				Type:        "int",
				Label:       "Test Label",
				Value:       float32(22.00),
				Description: "Test Description",
				Placeholder: "",
			},
			//nolint:lll
			expectedJSON: `{"name":"test","type":"int","label":"Test Label","value":22,"description":"Test Description","dropDownValues":null,"hiddenFromCarrier":false,"requiredForCarrier":false,"requiredForCheckIn":false,"requiredForWarehouse":false}`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actualJSON, err := tt.caf.MarshalJSON()
			require.NoError(t, err)
			assert.Equal(t, tt.expectedJSON, string(actualJSON))
		})
	}
}
