package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

const (
	LoadBuilding         SuggestionCategory = "load_building"
	LoadBuildingPipeline SuggestionPipeline = "load_building_pipeline"

	AppointmentConfirmation SuggestionPipeline = "appointment_confirmation"
	CarrierInfoPipeline     SuggestionPipeline = "carrier_info_pipeline"
	CheckCallPipeline       SuggestionPipeline = "check_call_pipeline"
	TruckListPipeline       SuggestionPipeline = "truck_list"
	AppointmentSlot         SuggestionCategory = "appointment_slot"
	PickupInfo              SuggestionCategory = "pickup_info"
	DropOffInfo             SuggestionCategory = "dropoff_info"
	CustomerInfo            SuggestionCategory = "customer_info"
	BillingInfo             SuggestionCategory = "billing_info"
	CarrierInfo             SuggestionCategory = "carrier_info"
	CheckCallResponse       SuggestionCategory = "check_call"
)

// Suggestions stores both the LLM output in the `Suggested` field and what
// the user actually applied/edited in `Applied`.
// This is used primarily for observability and future training of our model.
//
// NOTE: If updating this struct, also update CopyProcessedDuplicate() method below
type (
	SuggestedLoadChange struct {
		gorm.Model

		Account   string  `gorm:"index" json:"account"`
		ServiceID uint    `gorm:"index" json:""`
		Service   Service `json:"-"`

		FreightTrackingID string             `gorm:"index" json:"freightTrackingID"`
		Suggested         SuggestedChanges   `gorm:"type:json" json:"suggested"`
		Applied           SuggestedChanges   `gorm:"type:json" json:"applied"`
		Status            SuggestionStatus   `json:"status"`
		Pipeline          SuggestionPipeline `json:"pipeline"`
		Category          SuggestionCategory `json:"category"`
		LoadID            *uint              `gorm:"index" json:"loadID"`
		Load              Load               `json:"-"`
		EmailID           uint               `gorm:"index" json:"emailID"`
		Email             Email              `json:"-"`
		ThreadID          string             `gorm:"index" json:"threadID"`
		S3Attachment      Attachment         `json:"s3Attachment,omitempty"`
		BraintrustSpanID  string             `json:"spanID"`
		BraintrustLogIDs  LogRecordList      `gorm:"type:JSONB"`

		// If service has LB and QQ enabled, then every quote suggestion is associated with a load building one
		// This enables deduplication of suggestions on FE
		QuoteRequestSuggestionID *uint         `gorm:"index"`
		QuoteRequestSuggestion   *QuoteRequest `json:"-"`

		// Many-to-many relationship with Vector
		Vectors []Vector `gorm:"many2many:vector_suggested_load_changes"`
	}

	SuggestedChanges struct {
		CheckCallChanges *CheckCallChanges  `json:"checkCallChanges,omitempty"`
		Changes          *Changes           `json:"changes,omitempty"`
		LoadChanges      *LoadChanges       `json:"loadChanges,omitempty"`
		Pipeline         SuggestionPipeline `json:"-"`
		// Raw load building LLM output, for analysis
		RawLLMOutput *LoadChanges `json:"rawLLMOutput,omitempty"`
	}
)

func (sc *SuggestedChanges) Scan(value any) error {
	if value == nil {
		return nil
	}

	jsonData, ok := value.([]byte)
	if !ok {
		return errors.New("invalid type for SuggestedChanges")
	}

	var temp struct {
		CheckCallChanges *CheckCallChanges `json:"checkCallChanges"`
		Changes          *Changes          `json:"changes"`
		LoadChanges      *LoadChanges      `json:"loadChanges"`
	}

	err := json.Unmarshal(jsonData, &temp)
	if err != nil {
		return err
	}

	switch {
	case temp.CheckCallChanges != nil:
		sc.CheckCallChanges = temp.CheckCallChanges
	case temp.Changes != nil:
		sc.Changes = temp.Changes
	case temp.LoadChanges != nil:
		sc.LoadChanges = temp.LoadChanges
	}

	return nil
}

func (sc *SuggestedChanges) Value() (driver.Value, error) {
	switch sc.Pipeline {
	case CheckCallPipeline:
		return json.Marshal(sc.CheckCallChanges)
	case LoadBuildingPipeline:
		return json.Marshal(sc.LoadChanges)
	default:
		return json.Marshal(sc.Changes)
	}
}

type CheckCallChanges struct {
	Status    string   `json:"status"`
	Timestamp NullTime `json:"timestamp"`
	Timezone  string   `json:"timezone"` // IANA timezone, e.g. America/New_York
	City      string   `json:"city"`
	State     string   `json:"state"`
	Notes     string   `json:"notes"`
}

type Changes struct {
	PickupApptTime      NullTime `json:"pickupApptTime"`
	PickupApptTimezone  string   `json:"pickupApptTimezone"`
	DropoffApptTime     NullTime `json:"dropoffApptTime"`
	DropoffApptTimezone string   `json:"dropoffApptTimezone"`

	FirstDriverName    string   `json:"firstDriverName"`
	FirstDriverPhone   string   `json:"firstDriverPhone"`
	SecondDriverName   string   `json:"secondDriverName"`
	SecondDriverPhone  string   `json:"secondDriverPhone"`
	TruckNumber        string   `json:"truckNumber"`
	TrailerNumber      string   `json:"trailerNumber"`
	DispatchCity       string   `json:"dispatchCity"`
	DispatchState      string   `json:"dispatchState"`
	ExpectedPickupTime NullTime `json:"expectedPickupTime"`
	DispatchedTime     NullTime `json:"dispatchedTime"`
}

type LoadChanges struct {
	// IDs will always be empty in `suggested` but filled in `applied`; this is so we can capture the applied load
	ExternalTMSID     string `json:"externalTMSID"`
	FreightTrackingID string `json:"freightTrackingID"`

	Mode                 LoadMode                `json:"mode"`
	MoreThanTwoStops     bool                    `json:"moreThanTwoStops"`
	PONums               string                  `json:"poNums"`
	Customer             Customer                `json:"customer"`
	RateData             RateData                `json:"rateData"`
	Pickup               Pickup                  `json:"pickup"`
	Consignee            Consignee               `json:"consignee"`
	Specifications       SuggestedSpecifications `json:"specifications"`
	AdditionalReferences AdditionalReferences    `json:"additionalReferences"`
	Commodities          []Commodity             `json:"commodities"`
}

// Subset of models.Load.Specifications
type SuggestedSpecifications struct {
	ServiceType         string      `json:"serviceType"`
	TransportType       string      `json:"transportType"`
	TransportSize       string      `json:"transportSize"`
	TotalInPalletCount  json.Number `json:"totalInPalletCount"`
	TotalOutPalletCount json.Number `json:"totalOutPalletCount"`
	TotalPieces         ValueUnit   `json:"totalPieces"`
	Commodities         string      `json:"commodities"`
	NumCommodities      int         `json:"numCommodities"`
	TotalWeight         ValueUnit   `json:"totalWeight"`
	NetWeight           ValueUnit   `json:"netWeight"`
	BillableWeight      ValueUnit   `json:"billableWeight"`
	TotalDistance       ValueUnit   `json:"totalDistance"`
	MinTempFahrenheit   float64     `json:"minTempFahrenheit"`
	MaxTempFahrenheit   float64     `json:"maxTempFahrenheit"`
	PlanningComment     string      `json:"planningComment"`
}

// s should preload QuoteRequestSuggestion object
func (s *SuggestedLoadChange) CopyProcessedDuplicate(email *Email) *SuggestedLoadChange {
	newSuggestion := &SuggestedLoadChange{}
	// Set user-specific fields
	newSuggestion.Account = email.Account
	newSuggestion.ServiceID = email.ServiceID
	newSuggestion.ThreadID = email.ThreadID
	newSuggestion.EmailID = email.ID

	// Copy only specific fields
	newSuggestion.Status = Pending
	newSuggestion.Pipeline = s.Pipeline
	newSuggestion.S3Attachment = s.S3Attachment
	newSuggestion.Suggested = s.Suggested
	newSuggestion.BraintrustSpanID = s.BraintrustSpanID
	newSuggestion.BraintrustLogIDs = s.BraintrustLogIDs

	if s.QuoteRequestSuggestionID != nil && s.QuoteRequestSuggestion != nil {
		newSuggestion.QuoteRequestSuggestionID = nil // Reset foreign key
		newSuggestion.QuoteRequestSuggestion = s.QuoteRequestSuggestion.CopyProcessedDuplicate(email)
	}

	return newSuggestion
}

var _ ProcessedCopier[*SuggestedLoadChange] = &SuggestedLoadChange{}

func (s SuggestedLoadChange) GetVectors() []Vector {
	return s.Vectors
}

func (s SuggestedLoadChange) GetServiceID() uint {
	return s.ServiceID
}

func (s SuggestedLoadChange) GetSuggestionID() uint {
	return s.ID
}

func (s SuggestedLoadChange) GetContentType() ContentType {
	if s.S3Attachment.ExternalID != "" {
		return AttachmentContent
	}
	return EmailBodyContent
}

func (s SuggestedLoadChange) GetSuggestionType() SuggestionCategory {
	return LoadBuilding
}
