package models

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type Service struct {
	gorm.Model
	Name string `json:"name"`
	SCAC string `json:"scac"`

	EmailDomains pq.StringArray `json:"emailDomains" gorm:"type:text[]"` // e.g. [@drumkit.ai, @drumkit.com]
	TenantID     string         `json:"tenantID"`
	Integrations []Integration  `json:"-"`
	// Used in dynamic paths to provide same feature to different customers (e.g. quote/wickerpark, quote/atsa, etc)
	// Check constraint ensures nickname must exist, be lowercase and alphanumeric
	// https://www.postgresql.org/docs/current/ddl-constraints.html#DDL-CONSTRAINTS-CHECK-CONSTRAINTS
	//nolint:lll
	Nickname           string            `gorm:"default:NULL;uniqueIndex;check:(nickname IS NULL OR (NOT nickname = '' AND nickname ~ '^[a-z0-9_-]+$'))"`
	QuickQuoteConfigID *uint             `json:"-" gorm:"default:null"`
	QuickQuoteConfig   *QuickQuoteConfig `json:"-"`

	// carrier verification
	HighwayClientID     string
	HighwayClientSecret string

	// Integrations
	// Scheduling
	TurvoSchedulingClientID     string
	TurvoSchedulingClientSecret string
	VelosticsClientID           string
	VelosticsClientSecret       string
	// TMS
	McLeodAPIKey       string `gorm:"column:mcleod_api_key"`
	McLeodClientID     string `gorm:"column:mcleod_client_id"`
	McLeodClientSecret string `gorm:"column:mcleod_client_secret"`

	ThreePLSystemsClientID     string
	ThreePLSystemsClientSecret string

	// OnPrem Auth
	OnPremAuthToken string `gorm:"default:NULL;"`

	RedirectURI           string
	TruckstopClientID     string
	TruckstopClientSecret string

	// Front Auth
	FrontTenantSubdomain string `gorm:"default:NULL;"`
	FrontAuthToken       string `gorm:"default:NULL;"`

	FeatureFlags
}

type FeatureFlags struct {
	// NOTE: These take precedence over the respective tabs in the Load and Quote views
	// For example, if IsQuickQuoteEnabled=true, but IsQuoteViewEnabled = false,
	// the quote view will not show in the Drumkit sidebar.
	// GET service/features is forgiving and tries to correct these cases, but
	// shouldEnableLoadView and shouldEnableQuoteView functions must be updated as tabs are added to views
	IsLoadViewEnabled  bool `gorm:"default:false;" json:"isLoadViewEnabled"`
	IsQuoteViewEnabled bool `gorm:"default:false;" json:"isQuoteViewEnabled"`

	// Load View Tabs (Load Info Tab is on by default)
	IsAppointmentSchedulingEnabled bool `gorm:"default:false;" json:"isAppointmentSchedulingEnabled"`
	IsTrackAndTraceEnabled         bool `gorm:"default:false;" json:"isTrackAndTraceEnabled"`
	IsCarrierVerificationEnabled   bool `gorm:"default:false;" json:"isCarrierVerificationEnabled"`

	// Quote View Tabs
	IsQuickQuoteEnabled            bool `gorm:"default:false;" json:"isQuickQuoteEnabled"`
	IsCarrierNetworkQuotingEnabled bool `gorm:"default:false;" json:"isCarrierNetworkQuotingEnabled"`
	IsLoadBuildingEnabled          bool `gorm:"default:false;" json:"isLoadBuildingEnabled"`
	IsTruckListEnabled             bool `gorm:"default:false;" json:"isTruckListEnabled"`

	// Load View - Load Info Tab sub-sections
	// Show number of stops in the Drumkit sidebar -- a WIP feature
	IsMultiStopLoadViewEnabled bool `gorm:"default:false;" json:"isMultiStopLoadViewEnabled"`
	// Allows user to edit TMS object on Turvo instead of via Drumkit
	IsTurvoSectionLinksEnabled bool `gorm:"default:false;" json:"isTurvoSectionLinksEnabled"`
	// Allows user to assign operator to load on Drumkit; TMS integration must implement this
	IsOperatorEnabled bool `gorm:"default:false;" json:"isOperatorEnabled"`
	// Allows user to view orders associated with a load in Drumkit
	IsOrderEnabled bool `gorm:"default:false;" json:"isOrderEnabled"`

	// Track & Trace sub-sections / sub-features
	IsCheckCallNotesEnabled      bool `gorm:"default:false;" json:"isCheckCallNotesEnabled"`
	IsExceptionsEnabled          bool `gorm:"default:false;" json:"isExceptionsEnabled"`
	IsCheckCallCarrierSOPEnabled bool `gorm:"default:false;" json:"isCheckCallCarrierSOPEnabled"`
	IsCheckCallShipperSOPEnabled bool `gorm:"default:false;" json:"isCheckCallShipperSOPEnabled"`
	IsCarrierEmailOutboxEnabled  bool `gorm:"default:false;" json:"isCarrierEmailOutboxEnabled"`

	IsCheckCallSuggestionsEnabled   bool `gorm:"default:false;" json:"isCheckCallSuggestionsEnabled"`
	IsCarrierInfoSuggestionsEnabled bool `gorm:"default:false;" json:"isCarrierInfoSuggestionsEnabled"`

	// Appointment Scheduling sub-sections / sub-features
	// Allows Drumkit to update TMS with appointment details
	IsAppointmentTMSUpdateEnabled   bool `gorm:"default:false;" json:"isAppointmentTMSUpdateEnabled"`
	IsAppointmentEmailingEnabled    bool `gorm:"default:false;" json:"isAppointmentEmailingEnabled"`
	IsAppointmentSuggestionsEnabled bool `gorm:"default:false;" json:"isAppointmentSuggestionsEnabled"`

	// Quick Quote sub-sections / sub-features
	// Show multi-stop Quick Quote tab -- a WIP feature
	IsMultiStopQuickQuoteEnabled bool `gorm:"default:false;" json:"isMultiStopQuickQuoteEnabled"`
	// Allow users to submit quotes to third-party URLs, e.g. bidding websites
	IsQuoteSubmissionViaURLEnabled bool `gorm:"default:false;" json:"isQuoteSubmissionViaURLEnabled"`
	// Allows submitting quote to service's internal system (not the same as TMS)
	IsQuoteSubmissionToServiceEnabled bool `gorm:"default:false;" json:"isQuoteSubmissionToServiceEnabled"`
	IsGetLaneRateFromServiceEnabled   bool `gorm:"default:false;" json:"isGetLaneRateFromServiceEnabled"`
	// Allows access to quoting tools lane history (e.g. Greenscreens)
	IsQuoteLaneHistoryEnabled bool `gorm:"default:false;" json:"isQuoteLaneHistoryEnabled"`
	// Allows users to pull DAT Lane History
	IsOnDemandDATLaneHistoryEnabled bool `gorm:"default:false;" json:"isOnDemandDATLaneHistoryEnabled"`
	// Allows users to update DAT RateView using custom parameters
	IsUpdateRateViewEnabled bool `gorm:"default:false;" json:"isUpdateRateViewEnabled"`
	// Allows access to TMS lane history (e.g. McLeod, Turvo)
	IsTMSLaneHistoryEnabled bool `gorm:"default:false;" json:"isTMSLaneHistoryEnabled"`
	// Allow user to submit quote to TMS (TMS integration must implement CreateQuote)
	IsTMSQuoteSubmissionEnabled bool `gorm:"default:false;" json:"isTMSQuoteSubmissionEnabled"`
	// Allow user to toggle fuel type on QQ Calculator (e.g. DOE vs DAT rates)
	// TODO: Deprecate after Vulcan 0.43.0
	// Deprecated
	IsFuelTypeToggleEnabled bool `gorm:"default:false;" json:"isFuelTypeToggleEnabled"`
	// Allow user to input profit margin rather than mark-up on QQ Calculator
	IsQuoteCalculatorMarginEnabled bool `gorm:"default:false;" json:"isQuoteCalculatorMarginEnabled"`
	// Allow user to fetch Market Conditions from a DAT integration
	IsDATMarketConditionsEnabled bool `gorm:"default:false;" json:"isDATMarketConditionsEnabled"`
	// Allow user to fetch Market Conditions Forecast from a DAT integration
	IsDATMarketConditionsForecastEnabled bool `gorm:"default:false;" json:"isDATMarketConditionsForecastEnabled"`
	// Allow user to fetch Load to Truck Ratio from a DAT integration
	IsDATLoadToTruckRatioEnabled bool `gorm:"default:false;" json:"isDATLoadToTruckRatioEnabled"`
	// Show batch quote in Quick Quote tab
	IsBatchQuoteEnabled bool `gorm:"default:false;" json:"isBatchQuoteEnabled"`

	// Misc FE
	IsAdvancedSearchEnabled bool `gorm:"default:false;" json:"isAdvancedSearchEnabled"`

	// Email configurations
	// Allows users to send emails from delegated inboxes
	IsDelegatedInboxEnabled bool `gorm:"default:false;" json:"isDelegatedInboxEnabled"`
	// Allow users to forward emails to other email addresses based on EmailForwardingRules in DB
	IsEmailForwardingEnabled bool `gorm:"default:false;" json:"isEmailForwardingEnabled"`

	// Lean Solutions customer
	IsLeanSolutionsCustomer bool `gorm:"default:false;" json:"isLeanSolutionsCustomer"`

	// Default quote view tab to open to
	DefaultQuoteViewTab QuoteViewTab `gorm:"default:'quickQuote';" json:"defaultQuoteViewTab"`
}

// QuoteViewTab represents the available tabs for Quote view
type QuoteViewTab string

const (
	QuickQuoteTab     QuoteViewTab = "quickQuote"
	TrucksListTab     QuoteViewTab = "trucksList"
	LoadBuildingTab   QuoteViewTab = "loadBuilding"
	CarrierQuotingTab QuoteViewTab = "carrierQuoting"
)
