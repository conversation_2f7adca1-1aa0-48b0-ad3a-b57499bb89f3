package models

import (
	"encoding/json"
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type (
	EmailProvider          string
	UserType               string
	Role                   string
	DefaultPriceMarginType string
)

const (
	GmailEmailProvider   EmailProvider = "gmail"
	OutlookEmailProvider EmailProvider = "outlook"
	FrontEmailProvider   EmailProvider = "front"

	InternalUserType UserType = "user"
	OnPremUserType   UserType = "onpremuser"

	Admin  Role = "admin"
	Member Role = "member"

	DefaultPriceMarginTypePercentage DefaultPriceMarginType = "percentage"
	DefaultPriceMarginTypeAmount     DefaultPriceMarginType = "amount"
)

// UserAccessor is the interface for the `User` and `OnPremUser` models.
type UserAccessor interface {
	// Getter methods
	GetID() uint
	GetEncryptedAccessToken() string
	GetEncryptedRefreshToken() string
	GetEmailAddress() string
	GetHashedSessionToken() string
	GetHashedSessionTokens() pq.StringArray
	GetName() string
	GetOutlookClientState() string
	GetOutlookSubscriptionID() string
	GetTenantID() string
	GetTokenExpiry() time.Time
	GetWebhookExpiration() time.Time
	GetTMSGroups() Groups
	GetEmailSignature() string

	// Setter methods
	SetEmailAddress(email string)
	SetEncryptedAccessToken(token string)
	SetEncryptedRefreshToken(token string)
	SetHashedSessionToken(token string)
	SetHashedSessionTokens(tokens pq.StringArray)
	SetName(name string)
	SetOutlookClientState(state string)
	SetOutlookSubscriptionID(id string)
	SetTenantID(id string)
	SetTokenExpiry(expiry time.Time)
	SetWebhookExpiration(expiry time.Time)
	SetEmailSignature(signature string)
}

type ServiceIDAccessor interface {
	// Use GetServiceID by type checking against the UserAccessor interface first:
	//
	// if s, ok := user.(models.ServiceIDAccessor); ok {
	// 	ctx = log.With(
	// 		ctx,
	// 		zap.Uint("serviceID", s.GetServiceID()),
	// 	)
	// }
	GetServiceID() uint
}

type User struct {
	gorm.Model

	IsTestUser bool        `gorm:"default:false"`
	ServiceID  uint        `gorm:"index" json:"serviceID"`
	Service    Service     `json:"-"`
	UserGroups []UserGroup `gorm:"many2many:user_group_users;"`

	Name                  string // From mail client
	Role                  Role
	EmailAddress          string        `gorm:"index"`
	SecondaryEmailAddress string        `gorm:"index"`
	EmailProvider         EmailProvider `gorm:"index"`

	// Email config fields

	// Raw HTML appended to Drumkit-generated emails
	EmailSignature string
	// Rendered on Drumkit Portal settings page; Outlook API does not expose signature so Outlook users must
	// upload manually to WYSIWYG editor on Drumkit Portal.
	// NOTE: Quill Editor defines custom classes that conflict with classes in Email Signature,
	// so we use a raw JSON message to store the Quill representation for consistent rendering in Portal.
	EmailSignatureQuillDelta      json.RawMessage `gorm:"type:jsonb"`
	UseSignatureOnNewEmails       bool            `gorm:"default:false"`
	UseSignatureOnRepliesForwards bool            `gorm:"default:false"`
	// Carrier email CC; TODO: use track & trace config
	CC pq.StringArray `gorm:"type:text[]"`

	// The email client's ID for the user (e.g. Outlook AccountID)
	MailClientID string
	// The email client's ID for the organization/domain (e.g. Outlook's Tenant ID)
	TenantID string

	// After /login, the hashed Drumkit access token is stored here
	// Valid API requests must include a token that has not been invalidated (by /logout or a new /login)
	HashedSessionToken  string
	HashedSessionTokens pq.StringArray `gorm:"type:text[]"`

	// Mail access/refresh tokens are encrypted with AES-GCM using a 32-byte key stored in AWS Secrets Manager
	EncryptedAccessToken  string
	EncryptedRefreshToken string
	TokenExpiry           time.Time
	NeedsReauthorization  bool // Mail tokens can become invalid for various reasons, such as user updated mail pwd

	// Email webhook related fields
	GmailLastHistoryID    uint64 // ID of the user's last inbox history snapshot
	WebhookExpiration     time.Time
	OutlookClientState    string // Randomly generated per user, validates Outlook webhooks
	OutlookSubscriptionID string
	BackfillStartTime     NullTime

	// On-premise related fields
	IsOnPrem bool
	// This is the custom portal domain (e.g. redwood.drumkit.ai); only applicable to users that have `IsOnPrem=true``
	OnPremPortalDomain string
	// The customer's internal ID of the user
	OnPremID uint `gorm:"index"`

	// Internal TMS user groups if necessary
	TMSGroups Groups `gorm:"type:JSONB" json:"tmsGroups"`

	// DAT Integration
	DATEmailAddress          string         `json:"datEmailAddress"`
	HasGrantedDATPermissions bool           `json:"hasGrantedDATPermissions"`
	StarredLoads             pq.StringArray `gorm:"type:text[]"`
	ViewedLoads              pq.StringArray `gorm:"type:text[]"`

	// Front Integration - Email Provider
	FrontID string `json:"frontId"`

	IntermediateStopFeeUSD *int `gorm:"default:NULL" json:"intermediateStopFeeUSD"`

	// TODO: Refactor config-as-columns pattern to either a JSONB column or an EAV table
	// Miscellaneous Configuration
	DefaultPriceMargin     *float64                `json:"defaultPriceMargin" gorm:"default:NULL"`
	DefaultPriceMarginType *DefaultPriceMarginType `json:"defaultPriceMarginType" gorm:"default:NULL"`
}

func (u *User) GetID() uint {
	return u.ID
}

func (u *User) GetServiceID() uint {
	return u.ServiceID
}

func (u *User) GetEmailAddress() string {
	return u.EmailAddress
}

func (u *User) GetEncryptedAccessToken() string {
	return u.EncryptedAccessToken
}

func (u *User) GetEncryptedRefreshToken() string {
	return u.EncryptedRefreshToken
}

func (u *User) GetHashedSessionToken() string {
	return u.HashedSessionToken
}

func (u *User) GetHashedSessionTokens() pq.StringArray {
	return u.HashedSessionTokens
}

func (u *User) GetName() string {
	return u.Name
}

func (u *User) GetOutlookClientState() string {
	return u.OutlookClientState
}

func (u *User) GetOutlookSubscriptionID() string {
	return u.OutlookSubscriptionID
}

func (u *User) GetTenantID() string {
	return u.TenantID
}

func (u *User) GetTokenExpiry() time.Time {
	return u.TokenExpiry
}

func (u *User) GetWebhookExpiration() time.Time {
	return u.WebhookExpiration
}

func (u *User) GetTMSGroups() Groups {
	return u.TMSGroups
}

func (u *User) SetEmailAddress(email string) {
	u.EmailAddress = email
}

func (u *User) SetEncryptedAccessToken(token string) {
	u.EncryptedAccessToken = token
}

func (u *User) SetEncryptedRefreshToken(token string) {
	u.EncryptedRefreshToken = token
}

func (u *User) SetHashedSessionToken(token string) {
	u.HashedSessionToken = token
}

func (u *User) SetHashedSessionTokens(tokens pq.StringArray) {
	u.HashedSessionTokens = tokens
}

func (u *User) SetName(name string) {
	u.Name = name
}

func (u *User) SetOutlookClientState(state string) {
	u.OutlookClientState = state
}

func (u *User) SetOutlookSubscriptionID(id string) {
	u.OutlookSubscriptionID = id
}

func (u *User) SetTenantID(id string) {
	u.TenantID = id
}

func (u *User) SetTokenExpiry(expiry time.Time) {
	u.TokenExpiry = expiry
}

func (u *User) SetWebhookExpiration(expiry time.Time) {
	u.WebhookExpiration = expiry
}

func (u *User) GetEmailSignature() string {
	return u.EmailSignature
}

func (u *User) SetEmailSignature(signature string) {
	u.EmailSignature = signature
}

type OnPremUser struct {
	gorm.Model

	Name string

	EmailAddress   string `gorm:"index"`
	EmailProvider  EmailProvider
	EmailSignature string
	// The email client's ID for the user (e.g. Outlook AccountID)
	MailClientID string
	// The email client's ID for the organization/domain (e.g. Outlook's Tenant ID)
	TenantID string

	PortalDomain string
	// After /login, the hashed On-Prem API access token is stored here
	// Valid API requests must include a token that has not been invalidated (by /logout or a new /login)
	HashedSessionToken  string
	HashedSessionTokens pq.StringArray `gorm:"type:text[]"`

	// TODO:
	// Gmail access/refresh tokens are encrypted with AES-GCM using a 32-byte key stored in AWS Secrets Manager
	EncryptedAccessToken  string
	EncryptedRefreshToken string

	TokenExpiry time.Time
	// ID of the user's last inbox history snapshot
	GmailLastHistoryID uint64
	// TODO: get org/service info from Gmail
	WebhookExpiration time.Time

	// Randomly generated per user, so we can validate webhooks are really coming from MS
	OutlookClientState string

	OutlookSubscriptionID string

	BackfillStartTime NullTime
}

func (o *OnPremUser) GetID() uint {
	return o.ID
}

func (o *OnPremUser) GetEmailAddress() string {
	return o.EmailAddress
}

func (o *OnPremUser) GetEncryptedAccessToken() string {
	return o.EncryptedAccessToken
}

func (o *OnPremUser) GetEncryptedRefreshToken() string {
	return o.EncryptedRefreshToken
}

func (o *OnPremUser) GetHashedSessionToken() string {
	return o.HashedSessionToken
}

func (o *OnPremUser) GetHashedSessionTokens() pq.StringArray {
	return o.HashedSessionTokens
}

func (o *OnPremUser) GetName() string {
	return o.Name
}

func (o *OnPremUser) GetOutlookClientState() string {
	return o.OutlookClientState
}

func (o *OnPremUser) GetOutlookSubscriptionID() string {
	return o.OutlookSubscriptionID
}

func (o *OnPremUser) GetTenantID() string {
	return o.TenantID
}

func (o *OnPremUser) GetTokenExpiry() time.Time {
	return o.TokenExpiry
}

func (o *OnPremUser) GetWebhookExpiration() time.Time {
	return o.WebhookExpiration
}

// OnPrem users don't have TMS groups, so we return an empty array
func (o *OnPremUser) GetTMSGroups() Groups {
	return Groups{}
}

func (o *OnPremUser) SetEmailAddress(email string) {
	o.EmailAddress = email
}

func (o *OnPremUser) SetEncryptedAccessToken(token string) {
	o.EncryptedAccessToken = token
}

func (o *OnPremUser) SetEncryptedRefreshToken(token string) {
	o.EncryptedRefreshToken = token
}

func (o *OnPremUser) SetHashedSessionToken(token string) {
	o.HashedSessionToken = token
}

func (o *OnPremUser) SetHashedSessionTokens(tokens pq.StringArray) {
	o.HashedSessionTokens = tokens
}

func (o *OnPremUser) SetName(name string) {
	o.Name = name
}

func (o *OnPremUser) SetOutlookClientState(state string) {
	o.OutlookClientState = state
}

func (o *OnPremUser) SetOutlookSubscriptionID(id string) {
	o.OutlookSubscriptionID = id
}

func (o *OnPremUser) SetTenantID(id string) {
	o.TenantID = id
}

func (o *OnPremUser) SetTokenExpiry(expiry time.Time) {
	o.TokenExpiry = expiry
}

func (o *OnPremUser) SetWebhookExpiration(expiry time.Time) {
	o.WebhookExpiration = expiry
}

func (o *OnPremUser) GetEmailSignature() string {
	return o.EmailSignature
}

func (o *OnPremUser) SetEmailSignature(signature string) {
	o.EmailSignature = signature
}
