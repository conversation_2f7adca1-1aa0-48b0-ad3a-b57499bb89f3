package models

// ProcessedCopier defines an interface for models that can be copied during email processing.
// CopyProcessedDuplicate creates a new instance with preserved fields from the original while resetting user-specific
// fields based on the newly-ingested email. This enables deduplication of email processing by 60-80%.
// Long term, we could deduplicate all DB data (e.g. 1 suggestion per RFC ID instead of per Gmail/Outlook/Front ID)
// but that's a much larger effort.
type ProcessedCopier[T any] interface {
	CopyProcessedDuplicate(ingestedCopy *Email) T
}
