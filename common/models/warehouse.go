package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

const (
	// Suffixing with "Source" to prevent conflicts with IntegrationName
	DataDocksSource  WarehouseSource = "datadocks"
	DaySmartSource   WarehouseSource = "daysmart"
	E2openSource     WarehouseSource = "e2open"
	OneNetworkSource WarehouseSource = "onenetwork"
	ManhattanSource  WarehouseSource = "manhattan"
	CostcoSource     WarehouseSource = "costco"
	LineageSource    WarehouseSource = "lineage"
	OpendockSource   WarehouseSource = "opendock"
	RetalixSource    WarehouseSource = "retalix"
	TurvoSource      WarehouseSource = "turvo"
	VelosticsSource  WarehouseSource = "velostics"
	YardViewSource   WarehouseSource = "yardview"
)

type WarehouseSupportType string

const (
	NativeWarehouseSupportType WarehouseSupportType = "native"
	CustomWarehouseSupportType WarehouseSupportType = "custom"
)

// NativeWarehouseSupportList contains warehouse sources with native warehouse integration support
var NativeWarehouseSupportList = []WarehouseSource{
	OpendockSource,
	RetalixSource,
}

// CustomWarehouseSupportList contains warehouse sources that allow a custom warehouse to be built by Drumkit.
// These scheduling integrations don't have native warehouses/don't require them as a part of their appointment
// creation process.
//
// Used to determine if a custom warehouse can be safely created before we attempt to record customer x warehouse
// specific scheduler integration usage.
var CustomWarehouseSupportList = []WarehouseSource{
	CostcoSource,
	E2openSource,
	ManhattanSource,
	OneNetworkSource,
	YardViewSource, // YardView requires custom warehouse to be built by Drumkit BEFORE scheduling is done
}

type (
	WarehouseSource string

	Warehouse struct {
		gorm.Model

		// TODO: Refactor WarehouseID to be ExternalWarehouseID
		// WarehouseID is the external warehouse id
		//nolint:lll
		WarehouseID              string                   `gorm:"index:idx_wh_id_source,unique" json:"warehouseID" validate:"required"`
		WarehouseName            string                   `json:"warehouseName" validate:"required"`
		WarehouseAddressLine1    string                   `json:"warehouseAddressLine1"`
		WarehouseAddressLine2    string                   `json:"warehouseAddressLine2"`
		WarehouseFullAddress     string                   `json:"warehouseFullAddress"`
		WarehouseFullIdentifier  string                   `json:"warehouseFullIdentifier"`
		WarehouseTimezone        string                   `json:"warehouseTimezone"`
		DefaultSubscribedEmail   string                   `json:"defaultSubscribedEmail"`
		CustomApptFieldsTemplate CustomApptFieldsTemplate `gorm:"type:JSONB" json:"customApptFieldsTemplate"`
		Settings                 WarehouseSettings        `gorm:"type:JSONB" json:"settings"`
		Source                   WarehouseSource          `gorm:"index:idx_wh_id_source,unique" json:"source"`
		// Address associations
		Addresses []WarehouseAddress `gorm:"many2many:warehouse_address_associations"`
	}

	WarehouseCore struct {
		ID uint `json:"id"`
		// TODO: Refactor to be ExternalWarehouseID
		WarehouseID           string          `json:"warehouseID"` // This is actually external warehouse ID
		WarehouseName         string          `json:"warehouseName"`
		WarehouseAddressLine1 string          `json:"warehouseAddressLine1"`
		WarehouseAddressLine2 string          `json:"warehouseAddressLine2"`
		WarehouseTimezone     string          `json:"warehouseTimezone"`
		WarehouseSource       WarehouseSource `json:"warehouseSource"`
	}

	WarehouseAddress struct {
		gorm.Model
		Address
	}

	CustomApptFieldsTemplate []CustomApptField

	CustomApptField struct {
		Name                 string   `json:"name"`
		Type                 string   `json:"type"`
		Label                string   `json:"label"`
		Value                any      `json:"value"` // Provided by Drumkit user
		Description          string   `json:"description"`
		Placeholder          string   `json:"placeholder,omitempty"`
		DropDownValues       []string `json:"dropDownValues"`
		HiddenFromCarrier    bool     `json:"hiddenFromCarrier"`
		RequiredForCarrier   bool     `json:"requiredForCarrier"`
		RequiredForCheckIn   bool     `json:"requiredForCheckIn"`
		RequiredForWarehouse bool     `json:"requiredForWarehouse"`
		// any-typed for preventing type inconsistency on empty results e.g. 0 and " "
		MaxLengthOrValue any `json:"maxLengthOrValue,omitempty"`
		MinLengthOrValue any `json:"minLengthOrValue,omitempty"`
	}

	WarehouseSettings struct {
		Domain                     string `json:"domain"`
		ReferenceNumberIsVisible   bool   `json:"referenceNumberIsVisible"`
		ReferenceNumberIsRequired  bool   `json:"referenceNumberIsRequired"`
		ReferenceNumberDisplayName string `json:"referenceNumberDisplayName"`
		ReferenceNumberHelperText  string `json:"referenceNumberHelperText"`
		ReferenceNumberIsUnique    bool   `json:"referenceNumberIsUnique"`

		// Shifts defines warehouse shift windows in local warehouse time.
		// These are consumed by scheduling logic (e.g., YardView) to determine
		// valid booking windows by shift.
		Shifts ShiftSettings `json:"shifts"`
	}

	// ShiftSettings groups a list of daily shift windows.
	// The order of Schedule is significant and represents shift indices (0-based).
	ShiftSettings struct {
		Schedule []ShiftWindow `json:"schedule"`
	}

	// ShiftWindow represents a single daily time window.
	// Times are 24-hour strings in the warehouse's local timezone (HH:MM).
	// If End is logically before Start (e.g., start 22:00, end 04:00), the window crosses midnight.
	ShiftWindow struct {
		Start string `json:"start"`          // e.g., "14:00"
		End   string `json:"end"`            // e.g., "20:00" or "04:00" when crossing midnight
		Name  string `json:"name,omitempty"` // optional human-readable label
	}
)

// PersistDrumkitConfig copies subscribed email from src to dst warehouse after refreshing the warehouse
// object from its API. We don't want to override Drumkit settings in the DB after updating the warehouse object.
func (dst *Warehouse) PersistDrumkitConfig(src Warehouse) {
	dst.DefaultSubscribedEmail = src.DefaultSubscribedEmail
}

// Implements sql.Scanner interface for CustomApptFields
func (t *CustomApptFieldsTemplate) Scan(value any) error {
	if value == nil {
		*t = nil
		return nil
	}
	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for CustomApptFieldsTemplate: %T", value)
	}
	var fields []CustomApptField
	if err := json.Unmarshal(val, &fields); err != nil {
		return err
	}
	*t = fields

	return nil
}

// Implement driver.Valuer interface for CustomApptFields
func (t CustomApptFieldsTemplate) Value() (driver.Value, error) {
	if len(t) == 0 {
		return nil, nil
	}

	return json.Marshal(t)
}

// Custom marshaller for CustomApptFields to handle "int" cases
func (caf CustomApptField) MarshalJSON() ([]byte, error) {
	// Check if the type is "int"
	if caf.Type == "int" && caf.Value != nil {
		var value int

		switch t := caf.Value.(type) {
		case string:
			switch v := strings.TrimSpace(t); v {
			case "":
				value = 0

			default:
				var err error
				value, err = strconv.Atoi(v)
				if err != nil {
					return nil, fmt.Errorf("error converting to integer: %w", err)
				}
			}

		case int:
			value = caf.Value.(int)

			// If not string or int, parse as string
		default:
			switch v := fmt.Sprint(caf.Value); v {
			case "":
				value = 0

			default:
				var err error
				value, err = strconv.Atoi(v)
				if err != nil {
					return nil, fmt.Errorf("error converting to integer: %w", err)
				}
			}
		}

		// Marshal the integer value; use anonymous struct to avoid recursion overflow
		return json.Marshal(struct {
			Name                 string   `json:"name"`
			Type                 string   `json:"type"`
			Label                string   `json:"label"`
			Value                int      `json:"value"`
			Description          string   `json:"description"`
			Placeholder          string   `json:"placeholder,omitempty"`
			DropDownValues       []string `json:"dropDownValues"`
			HiddenFromCarrier    bool     `json:"hiddenFromCarrier"`
			RequiredForCarrier   bool     `json:"requiredForCarrier"`
			RequiredForCheckIn   bool     `json:"requiredForCheckIn"`
			RequiredForWarehouse bool     `json:"requiredForWarehouse"`
			// any-typed for preventing type inconsistency on empty results e.g. 0 and " "
			MaxLengthOrValue any `json:"maxLengthOrValue,omitempty"`
			MinLengthOrValue any `json:"minLengthOrValue,omitempty"`
		}{
			Name:                 caf.Name,
			Type:                 caf.Type,
			Label:                caf.Label,
			Value:                value,
			Description:          caf.Description,
			Placeholder:          caf.Placeholder,
			DropDownValues:       caf.DropDownValues,
			HiddenFromCarrier:    caf.HiddenFromCarrier,
			RequiredForCarrier:   caf.RequiredForCarrier,
			RequiredForCheckIn:   caf.RequiredForCheckIn,
			RequiredForWarehouse: caf.RequiredForWarehouse,
			MaxLengthOrValue:     caf.MaxLengthOrValue,
			MinLengthOrValue:     caf.MinLengthOrValue,
		})
	}

	// If the type is not "int", marshal normally
	return json.Marshal(struct {
		Name                 string   `json:"name"`
		Type                 string   `json:"type"`
		Label                string   `json:"label"`
		Value                any      `json:"value"`
		Description          string   `json:"description"`
		Placeholder          string   `json:"placeholder,omitempty"`
		DropDownValues       []string `json:"dropDownValues"`
		HiddenFromCarrier    bool     `json:"hiddenFromCarrier"`
		RequiredForCarrier   bool     `json:"requiredForCarrier"`
		RequiredForCheckIn   bool     `json:"requiredForCheckIn"`
		RequiredForWarehouse bool     `json:"requiredForWarehouse"`
		// any-typed for preventing type inconsistency on empty results e.g. 0 and " "
		MaxLengthOrValue any `json:"maxLengthOrValue,omitempty"`
		MinLengthOrValue any `json:"minLengthOrValue,omitempty"`
	}{
		Name:                 caf.Name,
		Type:                 caf.Type,
		Label:                caf.Label,
		Value:                caf.Value,
		Description:          caf.Description,
		Placeholder:          caf.Placeholder,
		DropDownValues:       caf.DropDownValues,
		HiddenFromCarrier:    caf.HiddenFromCarrier,
		RequiredForCarrier:   caf.RequiredForCarrier,
		RequiredForCheckIn:   caf.RequiredForCheckIn,
		RequiredForWarehouse: caf.RequiredForWarehouse,
		MaxLengthOrValue:     caf.MaxLengthOrValue,
		MinLengthOrValue:     caf.MinLengthOrValue,
	})
}

// Implements sql.Scanner interface for Settings
func (t *WarehouseSettings) Scan(value any) error {
	if value == nil {
		return nil
	}

	val, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("invalid type for WarehouseSettings: %T", value)
	}

	return json.Unmarshal(val, t)
}

// GetWarehouseSupportType determines the warehouse integration support type for a given source.
// Returns the support type (native or custom) and a boolean indicating if the source was found.
func GetWarehouseSupportType(source WarehouseSource) (WarehouseSupportType, error) {
	if source == YardViewSource {
		return CustomWarehouseSupportType, errors.New(
			"YardView requires custom warehouse to be built by Drumkit BEFORE appointment scheduling is done",
		)
	}

	for _, nativeSource := range NativeWarehouseSupportList {
		if source == nativeSource {
			return NativeWarehouseSupportType, nil
		}
	}

	for _, customSource := range CustomWarehouseSupportList {
		if source == customSource {
			return CustomWarehouseSupportType, nil
		}
	}

	return "", fmt.Errorf("unsupported warehouse support type for source: %s", source)
}

var (
	_ sql.Scanner    = &CustomApptFieldsTemplate{}
	_ driver.Valuer  = &CustomApptFieldsTemplate{}
	_ json.Marshaler = &CustomApptField{}
)
