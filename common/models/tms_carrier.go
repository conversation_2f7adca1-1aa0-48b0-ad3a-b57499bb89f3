package models

import (
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type TMSCarrier struct {
	gorm.Model       `json:"-"`
	TMSIntegrationID uint        `json:"tmsIntegrationId,omitempty"`
	TMSIntegration   Integration `json:"-"`
	ServiceID        uint        `gorm:"index" json:"serviceId,omitempty"`
	CompanyCoreInfo
	// Email list bc there can be multiple emails for a single carrier in a TMS
	// (singular email column still present/populated from CompanyCoreInfo)
	Emails     pq.StringArray `gorm:"type:text[]" json:"emails"`
	DOTNumber  string         `json:"dotNumber"`
	Dispatcher string         `json:"dispatcher"`
}
