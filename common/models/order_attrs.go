package models

// OrderCoreInfoAttributes represents the attributes for the Order model
type (
	OrderCoreInfoAttributes struct {
		Status           FieldAttributes    `json:"status"`
		Mode             FieldAttributes    `json:"mode"`
		MoreThanTwoStops FieldAttributes    `json:"moreThanTwoStops"`
		Customer         CustomerAttributes `json:"customer"`
		BillTo           BillToAttributes   `json:"billTo"`
		RateData         RateDataAttributes `json:"rateData"`

		// Stops relationship
		Pickups    []OrderStopAttributes `json:"pickups"`
		Deliveries []OrderStopAttributes `json:"deliveries"`

		// Pickup and Consignee
		Pickup         PickupAttributes         `json:"pickup"`
		Consignee      ConsigneeAttributes      `json:"consignee"`
		Specifications SpecificationsAttributes `json:"specifications"`
		Notes          NoteAttributes           `json:"notes"`

		// Date fields
		PickupDate  FieldAttributes `json:"pickupDate"`
		DropoffDate FieldAttributes `json:"dropoffDate"`

		// Additional fields from XML
		FreightTerms      FieldAttributes `json:"freightTerms"`
		IsPrePayment      FieldAttributes `json:"isPrePayment"`
		Currency          FieldAttributes `json:"currency"`
		TotalWeight       FieldAttributes `json:"totalWeight"`
		TotalVolume       FieldAttributes `json:"totalVolume"`
		PieceCount        FieldAttributes `json:"pieceCount"`
		HandlingUnitCount FieldAttributes `json:"handlingUnitCount"`
		IsHazmat          FieldAttributes `json:"isHazmat"`
		OrderLines        FieldAttributes `json:"orderLines"`

		// Additional fields from JSON
		TotalWeightLbs        FieldAttributes `json:"totalWeightLbs"`
		ReceivedTotalWeight   FieldAttributes `json:"receivedTotalWeight"`
		TotalTotes            FieldAttributes `json:"totalTotes"`
		ReceivedTotalTotes    FieldAttributes `json:"receivedTotalTotes"`
		TotalPackages         FieldAttributes `json:"totalPackages"`
		ReceivedTotalPackages FieldAttributes `json:"receivedTotalPackages"`
		TotalPallets          FieldAttributes `json:"totalPallets"`
		ReceivedTotalPallets  FieldAttributes `json:"receivedTotalPallets"`
		TotalAmount           FieldAttributes `json:"totalAmount"`
	}

	// OrderAttributes represents the attributes for the Order model
	OrderAttributes struct {
		// Internals
		Model FieldAttributes `json:"-"` // matching gorm.Model{}; not relevant

		OrderCoreInfoAttributes

		// The load that this order belongs to
		LoadID FieldAttributes `json:"loadId"`
		Load   FieldAttributes `json:"-"`

		// Customer's requested pickup and delivery dates
		RequestedPickupDate   FieldAttributes `json:"requestedPickupDate"`
		RequestedDeliveryDate FieldAttributes `json:"requestedDeliveryDate"`

		// Order priority
		Priority FieldAttributes `json:"priority"`

		// Whether the order has been fully fulfilled
		IsFulfilled FieldAttributes `json:"isFulfilled"`

		// The service this order belongs to
		ServiceID FieldAttributes `json:"serviceID"`
		Service   FieldAttributes `json:"-"`

		// Additional fields from XML
		OrganizationName  FieldAttributes `json:"organizationName"`
		OrderTMSStatus    FieldAttributes `json:"orderTMSStatus"`
		IsInPlanning      FieldAttributes `json:"isInPlanning"`
		IsHot             FieldAttributes `json:"isHot"`
		BillingStatus     FieldAttributes `json:"billingStatus"`
		IntegrationStatus FieldAttributes `json:"integrationStatus"`
		DoNotOverwrite    FieldAttributes `json:"doNotOverwrite"`
		NeedsReview       FieldAttributes `json:"needsReview"`

		// Reference numbers
		ExternalReferences FieldAttributes `json:"externalReferences"`

		// Additional fields from JSON
		IsBooked      FieldAttributes `json:"isBooked"`
		IsVoided      FieldAttributes `json:"isVoided"`
		Reference     FieldAttributes `json:"reference"`
		SalesTeam     FieldAttributes `json:"salesTeam"`
		BillingItems  FieldAttributes `json:"billingItems"`
		CostItems     FieldAttributes `json:"costItems"`
		Branches      FieldAttributes `json:"branches"`
		TruckBrokerID FieldAttributes `json:"truckBrokerId"`
		CarrierID     FieldAttributes `json:"carrierId"`
		CreatorID     FieldAttributes `json:"creatorId"`
		Type          FieldAttributes `json:"type"`
		CrossdockType FieldAttributes `json:"crossdockType"`
		CustomerID    FieldAttributes `json:"customerId"`
	}

	// OrderStopAttributes represents the attributes for the OrderStop model
	OrderStopAttributes struct {
		// Internals
		Model FieldAttributes `json:"-"` // matching gorm.Model{}; not relevant

		// Regular fields
		OrderID           FieldAttributes `json:"orderId"`
		ExpectedAt        FieldAttributes `json:"expectedAt"`
		Items             FieldAttributes `json:"items"`
		AppointmentNumber FieldAttributes `json:"appointmentNumber"`
		AppointmentTime   FieldAttributes `json:"appointmentTime"`
		ContactID         FieldAttributes `json:"contactId"`
		DepartedAt        FieldAttributes `json:"departedAt"`
		Type              FieldAttributes `json:"type"` // "pickup" or "delivery"
	}
)
