package services

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"go.uber.org/zap"

	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/fn/api/env"
)

// AssignOrCreateService looks up a service by the user's email domain.
// If a service is found, it assigns the user to that service.
// If not, it creates a new service for that domain.
func AssignOrCreateService(ctx context.Context, user *models.User) (err error) {
	ctx, span := otel.StartSpan(ctx, "AssignOrCreateService", nil)
	defer span.End(err)

	domain := ExtractEmailDomain(user.EmailAddress)

	service, err := rds.GetServiceByDomain(ctx, domain)
	if err == nil {
		if strings.Contains(user.EmailAddress, "78n517.onmicrosoft.com") {
			if user.ServiceID != 0 {
				log.Info(ctx, "our test user already has a service ID, skipping assigning new service")
				return nil
			}
		}
		user.ServiceID = service.ID

		if err := PostInternalOnboardingCallback(ctx, user, &service, false); err != nil {
			log.Warn(ctx, "error sending onboarding callback", zap.Error(err))
		}

		return nil
	}

	// Fail-open, create a service instead
	log.WarnNoSentry(ctx, "serviceDB query error", zap.Error(err))

	service = models.Service{
		EmailDomains: []string{domain},
		TenantID:     user.TenantID,
	}

	if err := rds.CreateService(ctx, &service); err != nil {
		log.Error(ctx, "user service creation failed", zap.Error(err))
		return fmt.Errorf("failed to create service for user %s", user.EmailAddress)
	}
	user.ServiceID = service.ID

	if err := PostInternalOnboardingCallback(ctx, user, &service, true); err != nil {
		log.Warn(ctx, "error sending onboarding callback", zap.Error(err))
	}

	return nil
}

// ExtractEmailDomain returns the domain part of an email address, including the "@".
func ExtractEmailDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return ""
	}

	return "@" + parts[1]
}

func PostInternalOnboardingCallback(
	ctx context.Context,
	user *models.User,
	service *models.Service,
	isNewService bool,
) error {

	// Optional in dev & staging
	callbackURI := env.Vars.OnboardingPipedreamURL
	if callbackURI == "" {
		if env.Vars.AppEnv == "prod" {
			log.Warn(ctx, "no onboarding callback configured")
		} else {
			log.WarnNoSentry(ctx, "no onboarding callback configured")
		}

		return nil
	}

	body := map[string]any{
		"environment":         env.Vars.AppEnv,
		"id":                  user.ID,
		"email":               user.EmailAddress,
		"name":                user.Name,
		"role":                user.Role,
		"emailProvider":       user.EmailProvider,
		"serviceName":         service.Name,
		"serviceEmailDomains": service.EmailDomains,
		"isNewService":        isNewService,
		"isOnPrem":            user.IsOnPrem,
	}

	itg := models.Integration{Name: "pipedream"}
	addr, err := url.Parse(env.Vars.OnboardingPipedreamURL)
	if err != nil {
		return fmt.Errorf("error parsing callback url: %w", err)
	}

	log.Info(ctx, "sending onboarding callback to Pipedream", zap.Any("body", body))
	_, _, err = httputil.PostBytesWithToken(ctx, itg, *addr, body, nil, nil, "")

	return err
}
