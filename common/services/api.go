package services

import (
	"fmt"
	"strings"
)

func GetOnPremWatchInboxURL(serviceName string) (string, error) {
	customer := strings.ToLower(serviceName)

	switch customer {
	case "redwood":
		return "https://rw-drumkit-process-fn-prod.azurewebsites.net/api/watchInbox", nil
	default:
		return "", fmt.Errorf("unknown customer: %s", customer)
	}
}

func GetOnPremIngestEmailURL(serviceName string) (string, error) {
	customer := strings.ToLower(serviceName)

	switch customer {
	case "redwood":
		return "https://rw-drumkit-process-fn-prod.azurewebsites.net/api/emailIngest", nil
	default:
		return "", fmt.Errorf("unknown customer: %s", customer)
	}
}
