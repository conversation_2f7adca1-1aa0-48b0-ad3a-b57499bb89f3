package errtypes

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/drumkitai/drumkit/common/helpers/scrub"
	"github.com/drumkitai/drumkit/common/models"
)

// HTTPResponseError is returned for a non-2xx status response from an HTTP call.
// Harmonizes bespoke, non-REST API conformant integrations.
type HTTPResponseError struct {
	IntegrationName models.IntegrationName
	IntegrationType models.IntegrationType
	AxleTSPID       uint
	ServiceID       uint

	HTTPMethod string
	URL        string

	StatusCode      int
	ResponseHeaders http.Header
	ResponseBody    []byte
}

func (e HTTPResponseError) Error() string {
	return fmt.Sprintf("%s %s %s returned %d: %s",
		e.IntegrationName, e.HTTPMethod, e.URL, e.StatusCode, e.ResponseBody)
}

// UserFacingErrorString returns HTTPResponseError string representation.
// NOTE: This function returns the integration's response body, so when building integrations it's important
// to verify and revise the body to be user-friendly before returning it to the user
// For example, if a TMS always returns an error in a JSON object, then build a package utility function to extract the
// plaintext error and re-assign it to httpErr.ResponseBody, then wrap it with NewUserFacingError(httpErr).
// Example:
func (e HTTPResponseError) UserFacingErrorString() string {
	name := models.FormatIntegrationName(e.IntegrationName)
	body := string(e.ResponseBody)

	if strings.TrimSpace(body) != "" {
		return fmt.Sprintf("%s returned an error: %s", name, body)
	}

	statusText := http.StatusText(e.StatusCode)
	// If statusText is empty, then the status code is invalid or custom like 599 for context timeouts
	if statusText == "" {
		return name + " returned an error"
	}

	return name + " returned an error: " + statusText

}

func (e HTTPResponseError) Wrap(msg string) error {
	// We want to preserve the original error message and add the outer context to it
	if e.ResponseBody != nil {
		e.ResponseBody = []byte(msg + ": " + string(e.ResponseBody))
	} else {
		e.ResponseBody = []byte(msg)
	}

	return e
}

// Verify that HTTPResponseError implements UserFacingErrorStringer
var _ UserFacingErrorStringer = HTTPResponseError{}

// NewHTTPResponseError constructs an error based on a single HTTP request + response.
func NewHTTPResponseError(integ models.Integration, req *http.Request, resp *http.Response, responseBody []byte) error {
	// Note: the HTTP response body can only be read once, so we force the caller to pass the body in

	return HTTPResponseError{
		IntegrationName: integ.Name,
		AxleTSPID:       integ.ID,
		ServiceID:       integ.ServiceID,
		HTTPMethod:      req.Method,
		URL:             scrub.URL(req.URL).String(),
		StatusCode:      resp.StatusCode,
		ResponseHeaders: resp.Header,
		ResponseBody:    responseBody,
	}
}

// EntityNotFoundError returns a HTTPResponseError for 404 not found.
// idName is optional but can be helpful to provide additional context.
func EntityNotFoundError(integ models.Integration, externalID string, idName string) error {
	url := externalID
	if idName != "" {
		url = fmt.Sprintf("%s=%s", idName, externalID)
	}

	return HTTPResponseError{
		IntegrationName: integ.Name,
		AxleTSPID:       integ.ID,
		ServiceID:       integ.ServiceID,
		HTTPMethod:      http.MethodGet,
		URL:             url,
		StatusCode:      http.StatusNotFound,
		ResponseBody:    []byte(fmt.Sprintf("%s %s not found", idName, externalID)),
	}
}

// DisabledIntegrationError returns a HTTPResponseError for 406 Not Acceptable.
// This likely represents an integration that had it's credentials updated, so turned the integration off
// in order prevent locking the account.
func DisabledIntegrationError(integ models.Integration) error {
	return HTTPResponseError{
		IntegrationName: integ.Name,
		IntegrationType: integ.Type,
		ServiceID:       integ.ServiceID,
		StatusCode:      http.StatusNotAcceptable,
	}
}

// NotFoundError represents a custom "Not Found" error type.
// For example, when a load does not exist, Aljex returns 200 instead of 404 but with "invalid PRO" msg in the body.
// This custom type is for such cases.
func IsEntityNotFoundError(err error) bool {
	var httpErr HTTPResponseError
	return errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusNotFound
}

// To use when integrations don't implement certain methods of an interface.
// Helpful for filtering out from Sentry
type NotImplementedError struct {
	IntegrationName models.IntegrationName
	Function        string
}

func (e NotImplementedError) Error() string {
	return e.Function + " is not available for " + string(e.IntegrationName)
}

func NotImplemented(intName models.IntegrationName, function string) error {
	return NotImplementedError{IntegrationName: intName, Function: function}
}

func IsNotImplementedError(err error) bool {
	var notImplErr NotImplementedError
	return errors.As(err, &notImplErr)
}

// UserFacingError is an error that is returned to the user,
// e.g. TMS input validation errors.
// Drumkit API returns this in 'Message' field of the response body JSON.
type UserFacingError struct {
	OriginalError error    // The original error
	UserMessage   string   // The cleaned, user-friendly message
	ContextChain  []string // Chain of wrapping contexts (outermost first)
}

// If an error implements UserFacingErrorStringer, then the error's UserFacingErrorString() is returned.
// Otherwise, the error's Error() is returned.
// Helpful for separating internal error strings from user-facing error strings,
// e.g. HTTPResponseError.Error() returns:
//
//	fmt.Sprintf("%s %s %s returned %d: %s",
//	e.IntegrationName, e.HTTPMethod, e.URL, e.StatusCode, e.ResponseBody)
//
// whereas its UserFacingErrorString() returns the more simple:
//
//	fmt.Sprintf("%s returned an error: %s", e.IntegrationName, e.ResponseBody)
type UserFacingErrorStringer interface {
	UserFacingErrorString() string
	Wrap(msg string) error
}

// NewUserFacingError creates an error object that can be surfaced to users.
// If cleanFunc is provided, it will be used to generate the UserMessage.
// Otherwise, the UserMessage will be generated using the default logic for the error type.
//
// NOTE: When building integrations, it's important to verify and revise TMS error responses to be user-friendly
// before returning them to the user.
// For example, if a TMS always returns an error in a JSON struct or HTML, then build a utility function to extract the
// error plaintext and return that to the user, not the whole error JSON/HTML.
// Example: aljex/errors.go:cleanErrorMessage
func NewUserFacingError(err error, cleanFunc ...func(error) string) error {
	ufErr := UserFacingError{
		OriginalError: err,
		ContextChain:  []string{}, // Initialize empty context chain
	}

	// If a clean function is provided, use it to generate the user message
	if len(cleanFunc) > 0 && cleanFunc[0] != nil {
		cleanedMsg := cleanFunc[0](err)

		if strings.TrimSpace(cleanedMsg) != "" {
			var httpErr HTTPResponseError

			if errors.As(err, &httpErr) {
				name := models.FormatIntegrationName(httpErr.IntegrationName)
				ufErr.UserMessage = fmt.Sprintf("%s returned an error: %s", name, cleanedMsg)
			} else {
				ufErr.UserMessage = cleanedMsg
			}
		}
	}
	// If no UserMessage is set, it will be generated in Error() method

	return ufErr
}

// Error returns the user-friendly error message.
// If UserMessage is set, it returns that with context chain.
// Otherwise, if the original error implements UserFacingErrorStringer, then that's used.
// Otherwise, the error message is cleaned and returned.
func (e UserFacingError) Error() string {
	var baseMessage string

	// If we have a pre-computed UserMessage, use it
	if e.UserMessage != "" {
		baseMessage = e.UserMessage
	} else {
		// Try to get the base message from UserFacingErrorStringer
		var baseErr UserFacingErrorStringer
		if errors.As(e.OriginalError, &baseErr) {
			baseMessage = baseErr.UserFacingErrorString()
		} else {
			// If no UserMessage set and UserFacingErrorStringer is not implemented, use the original error message
			str := strings.TrimSpace(e.OriginalError.Error())
			if len(str) < 2 {
				baseMessage = str
			} else {
				baseMessage = strings.ToUpper(string(str[0])) + strings.ToLower(str[1:])
			}
		}
	}

	// If no context chain, return the base message
	if len(e.ContextChain) == 0 {
		return baseMessage
	}

	// Build the message with context chain
	return e.buildMessageWithContext(baseMessage)
}

func (e UserFacingError) OriginalErrorString() string {
	return e.buildMessageWithContext(e.OriginalError.Error())
}

// buildMessageWithContext constructs the final error message by combining the context chain with the base message
func (e UserFacingError) buildMessageWithContext(baseMessage string) string {
	// For HTTP errors, we need to preserve the integration prefix and insert context after it
	var httpErr HTTPResponseError
	if errors.As(e.OriginalError, &httpErr) {
		name := models.FormatIntegrationName(httpErr.IntegrationName)
		prefix := fmt.Sprintf("%s returned an error", name)

		// Check if baseMessage starts with this prefix and remove duplicate prefix if present
		if strings.HasPrefix(baseMessage, prefix) {
			remainder := strings.TrimPrefix(baseMessage, prefix)
			remainder = strings.TrimPrefix(remainder, ": ")

			// Build: "[Integration] returned an error: [context chain]: [remainder]"
			contextStr := strings.Join(e.ContextChain, ": ")
			if remainder != "" {
				return fmt.Sprintf("%s: %s: %s", prefix, contextStr, remainder)
			}
			return fmt.Sprintf("%s: %s", prefix, contextStr)
		}
	}

	// For non-HTTP errors, just prepend the context chain
	contextStr := strings.Join(e.ContextChain, ": ")

	// Capitalize the first letter of the context string
	if len(contextStr) > 0 {
		contextStr = strings.ToUpper(string(contextStr[0])) + contextStr[1:]
	}
	if len(baseMessage) > 0 {
		baseMessage = strings.ToLower(string(baseMessage[0])) + baseMessage[1:]
	}
	return fmt.Sprintf("%s: %s", contextStr, baseMessage)
}

func (e UserFacingError) Unwrap() error {
	return e.OriginalError
}

// WrapNewUserFacingError wraps an error with an outer message, similar to fmt.Errorf().
// This is useful when an error is returned from a nested operation and should be surfaced to the user with context
// on the sub-operation that failed.
// Example: Mcleod CreateLoad() has multiple, nested steps that can fail.
// Correct usage:
//
//	 func (m *McleodEnterprise) CreateLoad(ctx context.Context, reqLoad models.Load) (resp Load, err error) {
//
//		err := createPickupLocation(ctx, reqLoad)
//		if err != nil {
//			return errtypes.WrapNewUserFacingError("error creating pickup location", err)
//		}
//
// Assuming the error returned by Mcleod API was "duplicate location name", then
// user sees: "Mcleod returned an error: error creating pickup location: duplicate location name"
// Incorrect:
//
//	 func (m *McleodEnterprise) CreateLoad(ctx context.Context, reqLoad models.Load) (resp Load, err error) {
//
//		err := createPickupLocation()
//		if err != nil {
//			return errtypes.NewUserFacingError(fmt.Errorf("error creating pickup location: %w", err))
//		}
//
// User sees: "`Error creating pickup location: mcleodenterprise post <long_url> returned 400: duplicate location name`"
// Issues:
// 1) The outer "Mcleod returned an error" is dropped, 2) user isn't given info on which sub-operation failed,
// and 3) the error message is not user friendly as it contains the full URL and status code.
func WrapNewUserFacingError(msg string, err error, cleanFunc ...func(error) string) error {
	// Check if we're wrapping an existing UserFacingError
	var existingUFErr UserFacingError
	if errors.As(err, &existingUFErr) {
		// Create a new UserFacingError that extends the existing one
		newUFErr := UserFacingError{
			OriginalError: existingUFErr.OriginalError,
			UserMessage:   existingUFErr.UserMessage,
			ContextChain:  make([]string, len(existingUFErr.ContextChain)+1),
		}

		// Prepend the new context to the chain (outermost first)
		newUFErr.ContextChain[0] = msg
		copy(newUFErr.ContextChain[1:], existingUFErr.ContextChain)

		// If a clean function is provided, apply it to the original error and update UserMessage
		if len(cleanFunc) > 0 && cleanFunc[0] != nil {
			cleanedMsg := cleanFunc[0](existingUFErr.OriginalError)
			if strings.TrimSpace(cleanedMsg) != "" {
				var httpErr HTTPResponseError
				if errors.As(existingUFErr.OriginalError, &httpErr) {
					name := models.FormatIntegrationName(httpErr.IntegrationName)
					newUFErr.UserMessage = fmt.Sprintf("%s returned an error: %s", name, cleanedMsg)
				} else {
					newUFErr.UserMessage = cleanedMsg
				}
			}
		}

		return newUFErr
	}

	if strings.TrimSpace(msg) == "" {
		return NewUserFacingError(err, cleanFunc...)
	}

	// If not wrapping a UserFacingError, create a new one with the context
	newUFErr := UserFacingError{
		OriginalError: err,
		ContextChain:  []string{msg},
	}

	// If a clean function is provided, apply it
	if len(cleanFunc) > 0 && cleanFunc[0] != nil {
		cleanedMsg := cleanFunc[0](err)

		if strings.TrimSpace(cleanedMsg) != "" {
			var httpErr HTTPResponseError
			if errors.As(err, &httpErr) {
				name := models.FormatIntegrationName(httpErr.IntegrationName)
				newUFErr.UserMessage = fmt.Sprintf("%s returned an error: %s", name, cleanedMsg)
			} else {
				newUFErr.UserMessage = cleanedMsg
			}
		}
	}

	return newUFErr
}
