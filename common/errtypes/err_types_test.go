package errtypes

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestNewUserFacingError(t *testing.T) {
	t.Run("simple error", func(t *testing.T) {
		baseErr := errors.New("something went wrong")
		ufErr := NewUserFacingError(baseErr)

		assert.Equal(t, "Something went wrong", ufErr.Error())
		assert.ErrorIs(t, ufErr, baseErr)
	})

	t.Run("with custom clean function", func(t *testing.T) {
		baseErr := errors.New("ugly internal error message")
		cleanFunc := func(error) string {
			return "Nice user-friendly message"
		}
		ufErr := NewUserFacingError(baseErr, cleanFunc)

		assert.Equal(t, "Nice user-friendly message", ufErr.Error())
		assert.ErrorIs(t, ufErr, baseErr)

		// Test that we can access the fields
		var ufTyped UserFacingError
		if errors.As(ufErr, &ufTyped) {
			assert.Equal(t, baseErr, ufTyped.OriginalError)
			assert.Equal(t, "Nice user-friendly message", ufTyped.UserMessage)
		} else {
			t.Fatal("Expected UserFacingError type")
		}
	})

	t.Run("HTTPResponseError with response body", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      http.StatusBadRequest,
			ResponseBody:    []byte("invalid data"),
		}

		ufErr := NewUserFacingError(httpErr)
		expected := "Mcleod returned an error: invalid data"
		assert.Equal(t, expected, ufErr.Error())
	})

	t.Run("HTTPResponseError with clean function", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      http.StatusBadRequest,
			ResponseBody:    []byte("invalid data"),
		}

		ufErr := NewUserFacingError(httpErr, func(error) string {
			return "nice user-friendly message"
		})
		expected := "Mcleod returned an error: nice user-friendly message"
		assert.Equal(t, expected, ufErr.Error())
	})

	t.Run("HTTPResponseError with empty body", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      404,
			ResponseBody:    []byte{},
		}

		ufErr := NewUserFacingError(httpErr)
		expected := "Mcleod returned an error: Not Found"
		assert.Equal(t, expected, ufErr.Error())
	})

	t.Run("HTTPResponseError with invalid status code", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      599,
		}

		ufErr := NewUserFacingError(httpErr)
		expected := "Mcleod returned an error"
		assert.Equal(t, expected, ufErr.Error())
	})

}

func TestWrapNewUserFacingError(t *testing.T) {

	t.Run("wrap plain error", func(t *testing.T) {
		baseErr := errors.New("invalid data")
		wrappedErr := WrapNewUserFacingError("failed to process", baseErr)
		assert.Equal(t, "Failed to process: invalid data", wrappedErr.Error())

		var ufTyped UserFacingError
		if errors.As(wrappedErr, &ufTyped) {
			assert.Equal(t, "Failed to process: invalid data", ufTyped.OriginalErrorString())
		} else {
			t.Fatal("Expected UserFacingError type")
		}
	})

	t.Run("wrap HTTPResponseError", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      400,
			ResponseBody:    []byte("duplicate location name"),
		}

		wrappedErr := WrapNewUserFacingError("error creating pickup location", httpErr)
		expected := "Mcleod returned an error: error creating pickup location: duplicate location name"
		assert.Equal(t, expected, wrappedErr.Error())
	})

	t.Run("wrap with empty context", func(t *testing.T) { // Why you'd do this is beyond me, but we test it
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			HTTPMethod:      "POST",
			URL:             "https://api.example.com/loads",
			StatusCode:      400,
			ResponseBody:    []byte("duplicate location name"),
		}

		wrappedErr := WrapNewUserFacingError("", httpErr)
		assert.Equal(t, "Mcleod returned an error: duplicate location name", wrappedErr.Error())
	})

	t.Run("multiple wrapping levels with HTTPResponseError", func(t *testing.T) {
		baseErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			ResponseBody:    []byte("invalid credentials"),
		}

		wrappedOnce := WrapNewUserFacingError("auth failed", baseErr)
		wrappedTwice := WrapNewUserFacingError("operation failed", wrappedOnce)

		expected := "Mcleod returned an error: operation failed: auth failed: invalid credentials"
		assert.Equal(t, expected, wrappedTwice.Error())
	})

	t.Run("wrap HTTPResponseError with clean function", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			ResponseBody:    []byte("ugly internal error message"),
		}

		cleanFunc := func(error) string {
			return "nice user-friendly message"
		}

		wrappedErr := WrapNewUserFacingError("error creating pickup location", httpErr, cleanFunc)
		expected := "Mcleod returned an error: error creating pickup location: nice user-friendly message"
		assert.Equal(t, expected, wrappedErr.Error())
	})

	t.Run("multiple wrapping levels with clean function", func(t *testing.T) {
		baseErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			ResponseBody:    []byte("invalid credentials"),
		}

		cleanFunc := func(error) string {
			return "nice user-friendly message"
		}

		wrappedOnce := WrapNewUserFacingError("auth failed", baseErr, cleanFunc)
		wrappedTwice := WrapNewUserFacingError("operation failed", wrappedOnce, cleanFunc)

		expected := "Mcleod returned an error: operation failed: auth failed: nice user-friendly message"
		assert.Equal(t, expected, wrappedTwice.Error())
	})

	t.Run("wrap user facing error", func(t *testing.T) {
		httpErr := HTTPResponseError{
			IntegrationName: models.McleodEnterprise,
			ResponseBody:    []byte("invalid credentials"),
		}

		baseErr := UserFacingError{
			OriginalError: httpErr,
			UserMessage:   "Mcleod returned an error: invalid credentials",
		}

		wrappedErr := WrapNewUserFacingError("auth failed", baseErr)
		expected := "Mcleod returned an error: auth failed: invalid credentials"
		assert.Equal(t, expected, wrappedErr.Error())
	})
}
