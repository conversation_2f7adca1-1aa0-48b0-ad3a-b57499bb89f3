# Prompt Engineering Guidelines

Read before updating prompts.

## Guidelines for Updating Prompts

When modifying existing prompts, especially critical ones like the "Quote Request" prompt, please adhere to the following process:

1.  **Testing with Braintrust is Mandatory:**
    - Any changes to prompts like the Quote Request prompt _must_ be tested using Braintrust.
2.  **Add Updated Prompt to Braintrust:**
    - Go to [Braintrust](https://www.braintrust.dev/app/Drumkit) and navigate to the project/prompt page for the prompt you are editing.
    - Add the modified prompt as a _new_ prompt. Making sure to change the output from Text to Structured Output, you can copy paste the structured output from other prompts. If you also made edits to any schema that is sent to create the expected Structured Output update those as well.
    - **Prompt Naming Convention:** `[Improvement Goal/Focus]` or `Pre-Release #[PR num]`  or `Release #[PR num] Prod`
      - Ex: `TruckType Taxonomy Date Parsing Updates`
      - Ex: `Pre-Release #1826` or `Release #1826 Prod`
      Rename prod prompt after new releases that update prompt.
3.  **Backtest for Accuracy and Regression:**
    - Test the new prompt using Braintrust experiments using JSONDiff and Factuality scorers (Drumkit and standard).
    - Compare its performance with the current/previous prompt to ensure there are no regressions.
4.  **Commit Changes:**
    - If the updated prompt performs satisfactorily (i.e., meets or exceeds the performance of the previous version and passes all tests), then you can commit the changes.

By following these guidelines, we can ensure the quality, consistency, and continuous improvement of our LLM prompts.

## Braintrust Specific Instructions

## General Best Practices for Prompt Engineering

- **Be Specific and Clear:** Provide detailed instructions. The more specific you are, the better the LLM can understand the desired output. Avoid ambiguity.
- **Provide Examples (Few-shot Learning):** Show the LLM what a good output looks like. One or more examples can significantly improve performance.
- **Define the Persona/Role:** Instruct the LLM to act as a specific persona (e.g., "You are a helpful assistant specializing in logistics"). This helps set the tone and context.
- **Use Delimiters:** Clearly separate different parts of your prompt (e.g., instructions, examples, input data) using markers like `###`, `---`, or XML tags. This helps the LLM parse the prompt.
- **Iterate and Test:** Prompt engineering is an iterative process. Start with a simple prompt and gradually refine it based on the LLM's responses. Test with various inputs to ensure robustness.
- **Control Output Format:** If you need the output in a specific format (e.g., JSON, XML, a numbered list), explicitly state this in the prompt. You can also provide an example of the desired format.
- **Break Down Complex Tasks:** For complex tasks, consider breaking them down into smaller, simpler sub-tasks. You might even chain multiple prompts together.
- **Handle Edge Cases:** Think about potential edge cases or unexpected inputs and provide instructions on how the LLM should handle them (e.g., "If the information is not found, respond with 'N/A'").
- **Temperature and Other Parameters:** Experiment with LLM parameters like temperature (for randomness/creativity), top_p, and max_tokens to fine-tune the output. Lower temperatures are better for factual/precise tasks.
- **Keep it Concise (but not too concise):** While clarity is key, overly long prompts can sometimes confuse the LLM or hit token limits. Find a balance.
- **Review and Refine:** Regularly review and refine your prompts as you learn more about how the LLM responds and as the requirements of your application evolves.
