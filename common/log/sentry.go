package log

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strings"

	sentrySDK "github.com/getsentry/sentry-go" //nolint:gomodguard // associate errors with traces https://rb.gy/drm188
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/sentry"
)

var zapToSentryLvl = map[zapcore.Level]sentry.Level{
	zapcore.DebugLevel:  sentry.LevelDebug,
	zapcore.InfoLevel:   sentry.LevelInfo,
	zapcore.WarnLevel:   sentry.LevelWarning,
	zapcore.ErrorLevel:  sentry.LevelError,
	zapcore.DPanicLevel: sentry.LevelError,
	zapcore.FatalLevel:  sentry.LevelFatal,
	zapcore.PanicLevel:  sentry.LevelFatal,
}

func sentryCapture(ctx context.Context, level zapcore.Level, msg string, fields ...zap.Field) {
	tagsStr, tags, errVal := buildSentryMsgAndTags(ctx, fields...)

	hub := sentry.GetHubFromContext(ctx)

	hub.WithScope(func(scope *sentry.Scope) {
		scope.SetLevel(zapToSentryLvl[level])

		if len(tags) > 0 {
			scope.SetTags(tags)
		}

		if errVal != nil {
			if filterFromSentry(tags["caller"], errVal) {
				return
			}

			// If error, use CaptureException so beforeSend() function can add the stack of error types to the
			// default fingerprint to merge less aggressively.
			captureExceptionWithTrace(ctx, hub, scope, fmt.Errorf("%s: %w %s", msg, errVal, tagsStr))
		} else {
			captureMessageWithTrace(ctx, hub, scope, msg+" "+tagsStr)
		}

	})

	// If this is a panic/fatal log, we need to flush now before the program terminates
	if level > zapcore.ErrorLevel {
		hub.Flush(sentry.FlushTimeout)
	}
}

// Given a log msg + zap fields, construct the Sentry msg and tags. If error, returns the
// error so caller can use sentry.CaptureException()
func buildSentryMsgAndTags(ctx context.Context, fields ...zap.Field) (string, map[string]string, error) {
	if len(fields) == 0 {
		return "", nil, nil
	}

	// Serialize all zap fields for the Sentry issue message
	encoder := zapcore.NewMapObjectEncoder()

	// Auto-add Sentry tags for zap.Bool() and sufficiently small zap.String() fields
	tags := make(map[string]string, len(fields)+1)
	tags["otelTraceID"] = trace.SpanContextFromContext(ctx).TraceID().String()

	var errVal error
	for _, f := range fields {
		if f.Type == zapcore.ErrorType {
			// Optimization: pull out error value here instead of a separate O(n) function to find it
			errVal = f.Interface.(error)
			continue
		}

		f.AddTo(encoder)

		switch f.Type {
		case zapcore.BoolType:
			if f.Integer > 0 {
				tags[f.Key] = "true"
			} else {
				tags[f.Key] = "false"
			}

		default:
			if f.Key != "caller" {
				if 0 < len(f.String) && len(f.String) < 256 {
					tags[f.Key] = f.String
				} else if f.Integer > 0 {
					tags[f.Key] = fmt.Sprint(f.Integer)
				}
			}

		}
	}

	fieldJSON, err := json.Marshal(encoder.Fields)
	if err != nil {
		// this should be impossible, encoder.Fields is a simple map designed for JSON serialization
		defaultLogger.zlog.Error("json marshal of encoded zap fields failed", zap.Error(err))
	}

	return string(fieldJSON), tags, errVal
}

// Function to drop transient and/or unactionable errors from Sentry
func filterFromSentry(caller string, err error) bool {
	// Intentionally didn't use `if strings.Contains(caller, "api") || strings.Contains(caller, "rds")`
	// so that this condition is updated as we add microservices
	//nolint:staticcheck
	if !(strings.Contains(caller, "processor") || strings.Contains(caller, "outlook") ||
		strings.Contains(caller, "gmail")) {
		return false
	}

	// Ignore transient HTTP errors; ingestion webhooks/processor lambda will retry +
	// AWS CloudWatch alarms for major spikes
	var httpErr errtypes.HTTPResponseError
	if errors.As(err, &httpErr) && httpErr.StatusCode >= 500 {
		return true
	}

	// Ignore network blips; logged as 599s + AWS Cloudwatch alarms
	var urlErr *url.Error
	lowerErrMsg := strings.ToLower(err.Error())
	if errors.As(err, &urlErr) && (strings.HasSuffix(lowerErrMsg, "eof") ||
		strings.HasSuffix(lowerErrMsg, "read: connection reset by peer") ||
		strings.HasSuffix(lowerErrMsg, "read: connection timed out") ||
		strings.HasSuffix(lowerErrMsg, "i/o timeout")) {

		return true
	}

	return false
}

// Attaches event hint to Sentry exception to associate it with Otel trace + Axiom trace URL.
func captureExceptionWithTrace(
	ctx context.Context,
	hub *sentrySDK.Hub,
	scope *sentry.Scope,
	err error,
) *sentrySDK.EventID {

	sentryClient := hub.Client()

	if sentryClient != nil {
		return sentryClient.CaptureException(err, &sentrySDK.EventHint{Context: ctx}, scope)
	}

	Info(ctx, "no Sentry client found, capturing without event hint")
	return hub.CaptureException(err)

}

// Attaches event hint to Sentry message to associate it with Otel trace + Axiom trace URL.
func captureMessageWithTrace(
	ctx context.Context,
	hub *sentrySDK.Hub,
	scope *sentry.Scope,
	msg string,
) *sentrySDK.EventID {

	sentryClient := hub.Client()

	if sentryClient != nil {
		return sentryClient.CaptureMessage(msg, &sentrySDK.EventHint{Context: ctx}, scope)
	}

	Info(ctx, "no Sentry client found, capturing without event hint")
	return hub.CaptureMessage(msg)

}
