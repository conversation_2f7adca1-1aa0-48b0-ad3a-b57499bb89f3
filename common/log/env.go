package log

import (
	"context"

	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type Config struct {
	AppEnv          string `envconfig:"APP_ENV"`
	AxiomLogDataset string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomToken      string `envconfig:"AXIOM_TOKEN"`
	AxiomOrgID      string `envconfig:"AXIOM_ORG_ID"`
	Debug           bool   `envconfig:"DEBUG"`
}

func NewFromEnv(ctx context.Context, baseFields ...zap.Field) context.Context {
	var env Config
	envconfig.MustProcess("", &env)

	var level zapcore.Level
	if env.Debug {
		level = zapcore.DebugLevel
	} else {
		level = zapcore.InfoLevel
	}

	return New(ctx, env, level, baseFields...)
}
