package log

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

func TestBuildSentryMsgAndTags(t *testing.T) {
	t.<PERSON>()

	t.Run("No fields", func(t *testing.T) {
		t.Parallel()

		tagsStr, tags, errVal := buildSentryMsgAndTags(context.Background())
		assert.Equal(t, "", tagsStr)
		assert.Nil(t, tags)
		assert.Nil(t, errVal)
	})

	t.Run("Fields", func(t *testing.T) {
		t.<PERSON>()

		tagsStr, tags, errVal := buildSentryMsgAndTags(
			context.Background(),
			zap.String("axle", "api"),
			zap.Bool("debug", true),
			zap.Int("lifeAnswer", 42),
			zap.Any("object", map[string]any{"alpha": "abc", "bravo": 123}),
		)

		assert.Equal(t, `{"axle":"api","debug":true,"lifeAnswer":42,"object":{"alpha":"abc","bravo":123}}`, tagsStr)
		assert.Equal(t, map[string]string{"axle": "api", "debug": "true", "lifeAnswer": "42",
			"otelTraceID": "00000000000000000000000000000000"}, tags)
		assert.Nil(t, errVal)
	})

	t.Run("Error field", func(t *testing.T) {
		t.Parallel()

		tagsStr, tags, errVal := buildSentryMsgAndTags(
			context.Background(),
			zap.Error(errors.New("some error")),
			zap.String("axle", "api"),
			zap.Bool("debug", true),
			zap.Int("lifeAnswer", 42),
			zap.Any("object", map[string]any{"alpha": "abc", "bravo": 123}),
		)

		assert.Equal(t, `{"axle":"api","debug":true,"lifeAnswer":42,"object":{"alpha":"abc","bravo":123}}`, tagsStr)
		assert.Equal(t, map[string]string{"axle": "api", "debug": "true", "lifeAnswer": "42",
			"otelTraceID": "00000000000000000000000000000000"}, tags)
		assert.NotNil(t, errVal)
	})

}

func TestFilterFromSentry(t *testing.T) {
	t.Run("API TMS 5XX", func(t *testing.T) {
		tmsErr := errtypes.NewHTTPResponseError(models.Integration{},
			&http.Request{URL: &url.URL{Host: "example.com"}},
			&http.Response{StatusCode: http.StatusBadGateway},
			[]byte("502 - Gateway Timeout"))

		wrappedErr := fmt.Errorf("request failed: %w", tmsErr)
		assert.False(t, filterFromSentry("api/main.go:140", wrappedErr))
	})

	t.Run("Processor TMS 5XX", func(t *testing.T) {
		tmsErr := errtypes.NewHTTPResponseError(models.Integration{},
			&http.Request{URL: &url.URL{Host: "example.com"}},
			&http.Response{StatusCode: http.StatusBadGateway},
			[]byte("502 - Gateway Timeout"))

		wrappedErr := fmt.Errorf("request failed: %w", tmsErr)
		assert.True(t, filterFromSentry("processor/main.go:140", wrappedErr))
	})

	t.Run("Processor TMS 4XX", func(t *testing.T) {
		tmsErr := errtypes.NewHTTPResponseError(models.Integration{},
			&http.Request{URL: &url.URL{Host: "example.com"}},
			&http.Response{StatusCode: http.StatusBadRequest},
			[]byte("400 - Bad Request"))

		wrappedErr := fmt.Errorf("request failed: %w", tmsErr)
		assert.False(t, filterFromSentry("processor/main.go:140", wrappedErr))
	})

	t.Run("Ingestion Network EOF", func(t *testing.T) {
		err := &url.Error{Err: errors.New("failed to send GET request: https://example.com: EOF")}
		wrappedErr := fmt.Errorf("request failed: %w", err)

		assert.True(t, filterFromSentry("ingestion/outlook/main.go:140", wrappedErr))
	})

	t.Run("Ingestion Connection Reset", func(t *testing.T) {
		err := &url.Error{Err: errors.New(
			"failed to send GET request: https://example.com: read: connection reset by peer")}
		assert.True(t, filterFromSentry("ingestion/outlook/main.go:140", err))
	})

	t.Run("New Service TMS 5XX", func(t *testing.T) {
		tmsErr := errtypes.NewHTTPResponseError(models.Integration{},
			&http.Request{URL: &url.URL{Host: "example.com"}},
			&http.Response{StatusCode: http.StatusBadGateway},
			[]byte("502 - Gateway Timeout"))

		wrappedErr := fmt.Errorf("request failed: %w", tmsErr)
		assert.False(t, filterFromSentry("newservice/main.go:140", wrappedErr))
	})
}
