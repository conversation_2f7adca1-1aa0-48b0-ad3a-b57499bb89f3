// Package log wraps zap.Logger with support for context and Sentry.
package log

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"syscall"

	"github.com/aws/aws-lambda-go/lambdacontext"
	oteltrace "go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// New returns context with a new zap logger.
func New(ctx context.Context, logCfg Config, level zapcore.Level, baseFields ...zap.Field) context.Context {
	// If we're running in a Lambda function, add the request ID to the base fields
	if lc, ok := lambdacontext.FromContext(ctx); ok {
		lambdaField := zap.String("lambdaRequestId", lc.AwsRequestID)
		baseFields = append(baseFields, lambdaField)

		span := oteltrace.SpanFromContext(ctx)
		if span != nil && span.IsRecording() {
			addFieldAsAttribute(span, lambdaField)

		}
	}

	return addToContext(ctx, newCoreLogger(logCfg, level, baseFields...))
}

// BaseFields returns the set of base fields currently in the logger context.
func BaseFields(ctx context.Context) []zap.Field {
	return fromContext(ctx).baseFields
}

// With creates a new logger with additional base fields, and adds the fields as attributes to the current span.
func With(ctx context.Context, fields ...zap.Field) context.Context {
	span := oteltrace.SpanFromContext(ctx)
	if span != nil && span.IsRecording() {
		for _, field := range fields {
			addFieldAsAttribute(span, field)
		}
	}

	return addToContext(ctx, fromContext(ctx).With(fields...))
}

func Debug(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.DebugLevel, false, msg, fields...)
}

// Debugf logs a formatted informational message with optional additional fields.
func Debugf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.DebugLevel, false, formattedMsg, fields...)
}

func Info(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.InfoLevel, false, msg, fields...)
}

// Infof logs a formatted informational message with optional additional fields.
func Infof(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.InfoLevel, false, formattedMsg, fields...)
}

// InfoNoTrace logs without adding a span event to the trace.
// Use this for high-volume audit logs that would max out trace memory limits.
func InfoNoTrace(ctx context.Context, msg string, fields ...zap.Field) {
	logNoTrace(ctx, zapcore.InfoLevel, false, msg, fields...)
}

// InfofNoTrace logs a formatted informational message without adding a span event to the trace.
// Use this for high-volume audit logs that would max out trace memory limits.
func InfofNoTrace(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	logNoTrace(ctx, zapcore.InfoLevel, false, formattedMsg, fields...)
}

func Warn(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.WarnLevel, true, msg, fields...)
}

// Warnf logs a formatted warn message with optional additional fields.
func Warnf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.WarnLevel, false, formattedMsg, fields...)
}

func WarnNoSentry(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.WarnLevel, false, msg, fields...)
}

func Error(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.ErrorLevel, true, msg, fields...)
}

// Errorf logs a formatted warn message with optional additional fields.
func Errorf(ctx context.Context, format string, argsAndFields ...any) {
	var fields []zap.Field
	var args []any

	for _, arg := range argsAndFields {
		switch argTyped := arg.(type) {
		case zap.Field:
			fields = append(fields, argTyped)
		default:
			args = append(args, arg)
		}
	}

	formattedMsg := fmt.Sprintf(format, args...)
	log(ctx, zapcore.WarnLevel, true, formattedMsg, fields...)
}

func ErrorNoSentry(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.ErrorLevel, false, msg, fields...)
}

// DPanic panics if the logger is in development mode.
// This is useful for catching errors that are recoverable, but shouldn't ever happen.
func DPanic(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.DPanicLevel, true, msg, fields...)
}

func Panic(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.PanicLevel, true, msg, fields...)
}

func Fatal(ctx context.Context, msg string, fields ...zap.Field) {
	log(ctx, zapcore.FatalLevel, true, msg, fields...)
}

func Flush(ctx context.Context) {
	core := fromContext(ctx)
	defer func() {
		// Wrapper around core.zlog.Sync() that ignores EINVAL errors.
		// See: https://github.com/uber-go/zap/issues/1093#issuecomment-1120667285
		if syncErr := core.zlog.Sync(); syncErr != nil {
			if !errors.Is(syncErr, syscall.EINVAL) && !errors.Is(syncErr, syscall.ENOTTY) {
				Warn(ctx, "Error flushing buffered log entries from traced logger", zap.Error(syncErr))
			}
		}

		if syncErr := core.untracedZlog.Sync(); syncErr != nil {
			if !errors.Is(syncErr, syscall.EINVAL) && !errors.Is(syncErr, syscall.ENOTTY) {
				Warn(ctx, "Error flushing buffered log entries from untraced logger", zap.Error(syncErr))
			}
		}
	}()
}

func log(ctx context.Context, level zapcore.Level, withSentry bool, msg string, fields ...zap.Field) {
	core := fromContext(ctx)
	logWithLogger(ctx, core, level, withSentry, msg, true, fields...)
}

// logNoTrace logs without adding telemetry span events to traces (uses raw zap logger).
// This avoids maxing out trace memory limits for high-volume logs.
func logNoTrace(ctx context.Context, level zapcore.Level, withSentry bool, msg string, fields ...zap.Field) {
	core := fromContext(ctx)
	logWithLogger(ctx, core, level, withSentry, msg, false, fields...)
}

// logWithLogger is the shared implementation for both log() and logNoTrace().
func logWithLogger(
	ctx context.Context,
	core *logger,
	level zapcore.Level,
	withSentry bool,
	msg string,
	withTrace bool,
	fields ...zap.Field,
) {
	if withTrace {
		if level < core.zlog.Level() {
			return
		}
	} else {
		if level < core.untracedZlog.Level() {
			return
		}
	}

	caller := zapcore.NewEntryCaller(runtime.Caller(3))

	allFields := append(
		[]zap.Field{zap.String("caller", caller.TrimmedPath())},
		core.Merge(fields...)...,
	)

	if withSentry {
		sentryCapture(ctx, level, msg, allFields...)
	}

	if withTrace {
		switch level {
		case zapcore.DebugLevel:
			core.zlog.DebugContext(ctx, msg, allFields...)
		case zapcore.InfoLevel:
			core.zlog.InfoContext(ctx, msg, allFields...)
		case zapcore.WarnLevel:
			core.zlog.WarnContext(ctx, msg, allFields...)
		case zapcore.ErrorLevel:
			core.zlog.ErrorContext(ctx, msg, allFields...)
		case zapcore.DPanicLevel:
			core.zlog.DPanicContext(ctx, msg, allFields...)
		case zapcore.PanicLevel:
			core.zlog.PanicContext(ctx, msg, allFields...)
		case zapcore.FatalLevel:
			core.zlog.FatalContext(ctx, msg, allFields...)
		default:
			panic("invalid zap level: " + level.String())
		}
	} else {
		switch level {
		case zapcore.DebugLevel:
			core.untracedZlog.Debug(msg, allFields...)
		case zapcore.InfoLevel:
			core.untracedZlog.Info(msg, allFields...)
		case zapcore.WarnLevel:
			core.untracedZlog.Warn(msg, allFields...)
		case zapcore.ErrorLevel:
			core.untracedZlog.Error(msg, allFields...)
		case zapcore.DPanicLevel:
			core.untracedZlog.DPanic(msg, allFields...)
		case zapcore.PanicLevel:
			core.untracedZlog.Panic(msg, allFields...)
		case zapcore.FatalLevel:
			core.untracedZlog.Fatal(msg, allFields...)
		default:
			panic("invalid zap level: " + level.String())
		}
	}
}
