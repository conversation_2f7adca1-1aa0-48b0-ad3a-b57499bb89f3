# Zap Logger

We use [zap](https://pkg.go.dev/go.uber.org/zap) for fast, customizable, structured, leveled logging.

This package wraps the raw `*zap.Logger` with context and Sentry integration: messages at WARN level or higher
are sent to Sentry automatically.

Complete example:

```go
package main

import (
	"context"
	"errors"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
)

func main() {
	os.Setenv("APP_ENV", "dev")
	os.Setenv("DEBUG", "false")
	os.Setenv("SENTRY_DSN", "YOUR_DSN_HERE") // replace with your own value

	sentry.Initialize()
	ctx := context.Background()
	defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)

	ctx = log.NewFromEnv(ctx)

	// debug logs only show up if DEBUG=1 or DEBUG=true
	log.Debug(ctx, "debug msg")

	// instead of % formatters, add strongly typed log fields
	log.Info(ctx, "info msg", zap.Time("t", time.Now()), zap.Int("answerToLife", 42))

	// warnings are logged and sent to Sentry
	log.Warn(ctx, "warn msg", zap.Strings("users", []string{"austin", "boogs", "kayky", "sophie"}))

	// errors are logged and sent to Sentry
	log.Error(ctx, "err msg", zap.Error(errors.New("something went wrong")))

	// warnings and errors can also be logged without the Sentry integration
	log.WarnNoSentry(ctx, "warn without sentry")
	log.ErrorNoSentry(ctx, "error without sentry")

	// add base fields to be included in every subsequent log message
	ctx = log.With(ctx, zap.String("base", "field"))

	// use zap.Any for objects like structs and maps
	log.Warn(ctx, "warn 2", zap.Any("obj", map[string]int{"a": 1, "b": 2, "c": 3}))
}
```

Dev output:

```
16:54:36	INFO	info msg	{"caller": "logtest/main.go:30", "t": "16:54:36", "answerToLife": 42}
16:54:36	WARN	warn msg	{"caller": "logtest/main.go:33", "users": ["austin", "boogs", "kayky", "sophie"]}
16:54:36	ERROR	err msg	{"caller": "logtest/main.go:36", "error": "something went wrong"}
16:54:36	WARN	warn without sentry	{"caller": "logtest/main.go:39"}
16:54:36	ERROR	error without sentry	{"caller": "logtest/main.go:40"}
16:54:36	WARN	warn 2	{"caller": "logtest/main.go:46", "base": "field", "obj": {"a":1,"b":2,"c":3}}
```

Prod output:

```
{"level":"info","ts":1701986056.9189389,"msg":"info msg","caller":"logtest/main.go:30","t":1701986056.918924,"answerToLife":42}
{"level":"warn","ts":1701986056.919347,"msg":"warn msg","caller":"logtest/main.go:33","users":["austin","boogs","kayky","sophie"]}
{"level":"error","ts":1701986056.919382,"msg":"err msg","caller":"logtest/main.go:36","error":"something went wrong"}
{"level":"warn","ts":1701986056.919389,"msg":"warn without sentry","caller":"logtest/main.go:39"}
{"level":"error","ts":1701986056.919395,"msg":"error without sentry","caller":"logtest/main.go:40"}
{"level":"warn","ts":1701986056.919429,"msg":"warn 2","caller":"logtest/main.go:46","base":"field","obj":{"a":1,"b":2,"c":3}}
```

## Integrations

### OpenTelemetry Tracing

The logger automatically integrates with OpenTelemetry traces using the [otelzap](https://github.com/uptrace/opentelemetry-go-extra/tree/main/otelzap) library:

**Automatic Span Events**: Log statements at `Info` level or higher are automatically added as span events to the active OpenTelemetry trace. This allows you to see logs directly in your trace visualization (e.g., Axiom, Jaeger).

**Trace ID in Logs**: All logs automatically include the trace ID from the current context, making it easy to correlate logs with traces.

**Base Fields as Span Attributes**: When you add base fields using `log.With()`, those fields are also added as attributes to the current span:

```go
// These fields become both log fields AND span attributes
ctx = log.With(ctx,
	zap.Uint("userID", user.ID),
	zap.String("email", user.Email),
)
```

**High-Volume Logs (NoTrace Functions)**: For high-volume audit logs or verbose logging that would exceed trace memory limits, use the `*NoTrace` variants:

```go
// Regular logging - adds span event to trace
log.Info(ctx, "Processing order", zap.String("orderID", orderID))

// High-volume logging - skips span event, still logs normally
log.InfoNoTrace(ctx, "Relay WS message", zap.String("payload", payload))
```

Available NoTrace functions:

- `InfoNoTrace()`
- `InfofNoTrace()`

**When to use NoTrace**:

- Audit logs that fire on every API request
- Verbose debugging logs in tight loops
- Large payloads that would bloat trace size
- Logs that don't add value to trace visualization

**What NoTrace does NOT skip**:

- ✅ Logs still appear in CloudWatch/Axiom
- ✅ Logs still include trace IDs for correlation
- ✅ Logs still go to Sentry (if using Warn/Error)
- ❌ Just skips adding the log as a span event

### Sentry Integration

The logger automatically integrates with Sentry for error monitoring:

**Automatic Capture**: Log statements at `Warn` level or higher are automatically sent to Sentry:

```go
log.Warn(ctx, "Unexpected condition")  // → Sent to Sentry
log.Error(ctx, "Failed to process")    // → Sent to Sentry
log.Info(ctx, "Normal operation")      // → Not sent to Sentry
```

**Opt-out Functions**: Use `*NoSentry` variants to log warnings/errors without sending to Sentry:

```go
log.WarnNoSentry(ctx, "Transient network blip")   // Logs only, no Sentry
log.ErrorNoSentry(ctx, "Expected retry scenario") // Logs only, no Sentry
```

**Error Association**: Sentry events are automatically associated with:

- OpenTelemetry trace ID (for correlation)
- Axiom trace URL (added via event hints)
- All log fields as Sentry tags
- Error stack traces (if `zap.Error()` is used)

**Automatic Filtering**: The logger automatically filters transient/unactionable errors from Sentry:

- HTTP 5xx errors from external services
- Network timeouts and connection resets
- EOF errors from network I/O

**Error Fingerprinting**: When logging errors with `zap.Error()`, the Sentry integration uses the error type chain to create more specific fingerprints, reducing noise from aggressive grouping.

### How It Works

The logger uses a dual-logger approach:

1. **Traced Logger** (`otelzap.Logger`): Wraps standard zap logger with OpenTelemetry integration

   - Used by: `Info()`, `Warn()`, `Error()`, etc.
   - Adds logs as span events
   - Includes trace context

2. **Untraced Logger** (`*zap.Logger`): Standard zap logger without telemetry overhead
   - Used by: `InfoNoTrace()`, etc.
   - Same log output, no span events
   - Still includes trace ID in log fields

Both loggers write to the same destinations (stdout, Axiom), ensuring logs are never lost regardless of which variant you use.
