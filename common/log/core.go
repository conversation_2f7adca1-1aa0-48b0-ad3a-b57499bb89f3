package log

import (
	adapter "github.com/axiomhq/axiom-go/adapters/zap"
	"github.com/axiomhq/axiom-go/axiom"
	"github.com/uptrace/opentelemetry-go-extra/otelzap"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// logger is a wrapper around zap.Logger that adds telemetry span events to traces.
type logger struct {
	zlog *otelzap.Logger

	// Unwrapped zap logger for logging without adding span events to traces.
	// Used by InfoNoTrace, WarnNoTrace, etc. to avoid maxing out trace memory limits.
	untracedZlog *zap.Logger

	// While zap.Logger supports base/initial fields, they are not exposed.
	// Therefore, we track the base fields ourselves so that we can easily access them when serializing to Sentry.
	baseFields []zap.Field
}

func newCoreLogger(logCfg Config, level zapcore.Level, baseFields ...zap.Field) *logger {
	var cfg zap.Config

	if logCfg.AppEnv == "dev" || logCfg.AppEnv == "" {
		cfg = zap.NewDevelopmentConfig()
		cfg.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		cfg.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("15:04:05")
	} else {
		cfg = zap.NewProductionConfig()
	}

	cfg.DisableCaller = true     // because we wrap zap, we have to calculate this ourselves
	cfg.DisableStacktrace = true // we already have stack traces in Sentry
	cfg.Level = zap.NewAtomicLevelAt(level)

	zlog, err := cfg.Build()
	if err != nil {
		panic(err)
	}

	var prodLogger *zap.Logger

	// Check if Axiom (https://axiom.co) logging is configured.
	// We use Axiom in addition to AWS for log management and observability.
	// The following conditions check if the necessary Axiom configuration is present.
	if logCfg.AxiomLogDataset != "" && logCfg.AxiomToken != "" && logCfg.AxiomOrgID != "" {
		axiomZapCore, err := adapter.New(
			adapter.SetDataset(logCfg.AxiomLogDataset),
			adapter.SetClientOptions(axiom.SetPersonalTokenConfig(logCfg.AxiomToken, logCfg.AxiomOrgID)))
		if err != nil {
			panic(err)
		}

		core := zapcore.NewTee(zlog.Core(), axiomZapCore)
		prodLogger = zap.New(core)
	} else {
		prodLogger = zlog
	}

	otelLogger := otelzap.New(prodLogger,
		otelzap.WithTraceIDField(true),
		otelzap.WithExtraFields(baseFields...),
		otelzap.WithMinLevel(zapcore.InfoLevel))

	untracedZlog := prodLogger.With(baseFields...)

	return &logger{
		zlog:         otelLogger,
		untracedZlog: untracedZlog,
		baseFields:   baseFields,
	}
}

// With creates a new core logger with additional base fields.
func (l *logger) With(fields ...zap.Field) *logger {
	return &logger{
		zlog:         l.zlog,
		untracedZlog: l.untracedZlog.With(fields...),
		baseFields:   l.Merge(fields...),
	}
}

func (l *logger) Merge(fields ...zap.Field) []zap.Field {
	return append(l.baseFields, fields...)
}
