package log

import (
	"context"
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type axleLogCtxKey string

const ctxKey = axleLogCtxKey("axle-zap")

// default logger if there isn't a logger attached to the given context
var defaultLogger = newCoreLogger(
	Config{
		AppEnv:          os.Getenv("APP_ENV"),
		AxiomLogDataset: os.Getenv("AXIOM_LOG_DATASET"),
		AxiomToken:      os.Getenv("AXIOM_TOKEN"),
		AxiomOrgID:      os.Getenv("AXIOM_ORG_ID"),
	},
	zapcore.InfoLevel, zap.String("loggerType", "default"))

func addToContext(ctx context.Context, log *logger) context.Context {
	return context.WithValue(ctx, ctxKey, log)
}

func fromContext(ctx context.Context) *logger {
	if result, _ := ctx.Value(ctxKey).(*logger); result != nil {
		return result
	}

	return defaultLogger
}

func InheritContext(parent, child context.Context) context.Context {
	// hub := sentry.CurrentHub().Clone()

	child = context.WithValue(child, ctxKey, fromContext(parent))
	// child = sentry.SetHubOnContext(child, hub)

	return child

}
