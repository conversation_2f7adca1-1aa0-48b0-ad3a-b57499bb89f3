---
description: "Go coding conventions and best practices for the Drumkit project"
globs: ["**/*.go"]
alwaysApply: true
---

# Go Coding Conventions

## Import Organization

Organize imports in this order:
1. Standard library
2. Third-party packages
3. Internal packages (`github.com/drumkitai/drumkit/...`)

Example:
```go
import (
    "context"
    "fmt"

    "github.com/gofiber/fiber/v2"
    "go.uber.org/zap"

    "github.com/drumkitai/drumkit/common/models"
    "github.com/drumkitai/drumkit/common/redis"
)
```

## Error Handling

- Always handle errors, don't use `_` unless there's a specific reason
- Wrap errors with context: `fmt.Errorf("failed to get user %s: %w", userID, err)`
- Use appropriate log levels (Debug, Info, Warn, Error)
- Log variables should be written in camelCase not snake_case
- For Redis errors, the wrapper functions already provide appropriate logging

## Naming Conventions

- Use meaningful concise variable names
- Keep functions focused and single-purpose
- Use camelCase for unexported functions and variables
- Use PascalCase for exported functions and types

## Functions

- Functions should never use named returns
  - Wrong example: `func foo() (test string, err error)`
  - Correct example: `func foo() (string, error)`

## Performance Considerations

- Use connection pooling (already configured in Redis wrappers)
- Set appropriate expiration times for cached data
- Consider using batch operations for multiple Redis operations
- Profile code that makes many Redis calls

## Security

- Never log sensitive data (passwords, tokens, etc.)
- Use the existing redaction helpers for logging
- Validate input data before storing in Redis
- Use appropriate key naming conventions to avoid collisions
