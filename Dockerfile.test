# Start from golang base image
FROM golang:1.24-alpine

# Set the current working directory inside the container
WORKDIR /test

# Copy go.mod, go.sum files and download deps
COPY go.mod go.sum ./
RUN go mod download

# Copy sources to the working directory
COPY . .

# Run the test suite
ARG project
RUN CGO_ENABLED=0 go test -v -cover $project \
    | sed ''/PASS/s//$(printf "\033[32mPASS\033[0m")/'' \
    | sed ''/FAIL/s//$(printf "\033[31mFAIL\033[0m")/'' \