name: go test

on:
  pull_request:
    branches: ["*"]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: drumkitai/postgis-pgvector:17-3.4
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: drumkit_test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23
      - name: Parallel unit tests
        run: DISABLE_RATE_LIMIT=true FORCE_GEN_AES_KEY=true go test ./...
      - name: Sequential live tests
        run: DISABLE_RATE_LIMIT=true FORCE_GEN_AES_KEY=true TEST_LIVE_DB=true go test -p=1 ./...
