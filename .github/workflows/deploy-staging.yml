name: Deploy all lambda functions to staging

on:
  push:
    branches:
      - main
  # allow manual trigger from the actions tab
  workflow_dispatch:

jobs:
  deploy-rds-migrate:
    name: beacon-rds-migrate-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-rds-migrate-staging
      working-directory: fn/migrate
    secrets: inherit

  run-rds-migrate:
    needs: deploy-rds-migrate
    name: beacon-run-rds-migrate-staging
    uses: ./.github/workflows/invoke-fn.yml
    with:
      function-name: beacon-rds-migrate-staging
    secrets: inherit

  deploy-ingestion-gmail:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion-gmail-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-gmail-staging
      working-directory: fn/ingestion/gmail
    secrets: inherit

  deploy-ingestion-outlook:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion-outlook-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-outlook-staging
      working-directory: fn/ingestion/outlook
    secrets: inherit

  deploy-ingestion-front:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-ingestion-front-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-front-staging
      working-directory: fn/ingestion/front
    secrets: inherit

  deploy-processor:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-processor-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-processor-staging
      working-directory: fn/processor
    secrets: inherit

  deploy-api:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-api-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-api-staging
      working-directory: fn/api
    secrets: inherit

  deploy-send-email:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-send-email-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-send-email-staging
      working-directory: fn/sendemail
    secrets: inherit

  deploy-poller:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: drumkit-poller-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: drumkit-poller-staging
      working-directory: fn/poller
    secrets: inherit

  deploy-watch-inbox:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-watch-inbox-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-watch-inbox-staging
      working-directory: fn/watchinbox
    secrets: inherit

  deploy-backfill-worker:
    needs: [deploy-rds-migrate, run-rds-migrate]
    name: beacon-backfill-worker-staging
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-backfill-worker-staging
      working-directory: fn/backfill
    secrets: inherit