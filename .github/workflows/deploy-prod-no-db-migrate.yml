name: Deploy prod WITHOUT DB migration

on:
  workflow_dispatch:

jobs:
  deploy-ingestion-gmail:
    name: beacon-ingestion
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion
      working-directory: fn/ingestion/gmail
    secrets: inherit

  deploy-ingestion-outlook:
    name: beacon-ingestion-outlook
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-outlook
      working-directory: fn/ingestion/outlook
    secrets: inherit

  deploy-ingestion-front:
    name: beacon-ingestion-front
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-ingestion-front
      working-directory: fn/ingestion/front
    secrets: inherit

  deploy-processor:
    name: beacon-processor
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-processor
      working-directory: fn/processor
    secrets: inherit

  deploy-api:
    name: beacon-api
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-api
      working-directory: fn/api
    secrets: inherit

  deploy-send-email:
    name: beacon-send-email
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-send-email
      working-directory: fn/sendemail
    secrets: inherit

  deploy-poller:
    name: drumkit-poller
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: drumkit-poller
      working-directory: fn/poller
    secrets: inherit

  deploy-watch-inbox:
    name: beacon-watch-inbox
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/release'
    uses: ./.github/workflows/update-fn.yml
    with:
      function-name: beacon-watch-inbox
      working-directory: fn/watchinbox
    secrets: inherit
