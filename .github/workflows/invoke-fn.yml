name: Invoke lambda function

on:
  workflow_call:
    inputs:
      function-name:
        required: true
        type: string
      payload:
        required: false
        type: string
        default: "{}"

jobs:
  invoke-function:
    name: aws lambda invoke
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          aws-region: us-east-1
          aws-secret-access-key: ${{ secrets.SECRET_ACCESS_KEY }}
      - run: aws lambda invoke --cli-binary-format raw-in-base64-out --function-name ${{ inputs.function-name }} --payload ${{ inputs.payload }} response.json
      - uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
