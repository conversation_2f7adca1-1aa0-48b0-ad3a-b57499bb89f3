name: Update lambda function code

on:
  workflow_call:
    inputs:
      function-name:
        required: true
        type: string
      working-directory:
        required: true
        type: string

jobs:
  update-fn:
    name: aws lambda update-fn
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          aws-region: us-east-1
          aws-secret-access-key: ${{ secrets.SECRET_ACCESS_KEY }}
      - run: go build -o bootstrap -ldflags "-s -w -extldflags '-static'" # 11/26/24: build statically because of github.com/gen2brain/go-fitz
        working-directory: ${{ inputs.working-directory }}
        env:
          CGO_ENABLED: 1 # 08/2023: set to 0 because of https://github.com/golang/go/issues/58550 - 11/26/24: set to 1 because of github.com/gen2brain/go-fitz
          GOARCH: amd64
          GOOS: linux
      - run: zip -r function.zip bootstrap . ../../common/
        working-directory: ${{ inputs.working-directory }}
      - run: aws lambda update-function-code --function-name ${{ inputs.function-name }} --zip-file fileb://function.zip
        working-directory: ${{ inputs.working-directory }}
      - uses: ravsamhq/notify-slack-action@v2
        if: always()
        with:
          status: ${{ job.status }}
          notify_when: "failure"
          notification_title: "{workflow} is failing"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.GH_ACTION_STATUS_SLACK_WH_URL }}
