name: Deploy `OnPrem` lambda function to Azure staging

on:
  push:
    branches: ["main"]
  # allow manual trigger from the actions tab
  workflow_dispatch:

env:
  # DRUMKIT_AZURE_FUNCTIONAPP_NAME: redwooddrumkittesting
  AZURE_FUNCTIONAPP_NAME: rw-drumkit-process-fn-stage
  AZURE_FUNCTIONAPP_PACKAGE_PATH: "./fn/onprem"

jobs:
  # deploy-staging-onprem-drumkit:
  #   runs-on: ubuntu-latest
  #   defaults:
  #     run:
  #       working-directory: fn/onprem
  #   steps:
  #     - uses: azure/login@v1
  #       with:
  #         creds: ${{ secrets.DRUMKIT_AZURE_RBAC_CREDENTIALS }}

  #     - uses: actions/checkout@v3
  #     - uses: actions/setup-go@v4
  #       with:
  #         go-version: 1.21.5
  #     - run: go build -o bootstrap -ldflags "-s -w"
  #       working-directory: fn/onprem
  #       env:
  #         CGO_ENABLED: 0  # https://github.com/golang/go/issues/58550
  #         GOARCH: amd64
  #         GOOS: linux

  #     - uses: Azure/functions-action@v1
  #       with:
  #         app-name: ${{ env.DRUMKIT_AZURE_FUNCTIONAPP_NAME }}
  #         package:  ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}

  deploy-staging-onprem-redwood:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: fn/onprem
    steps:
      - uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_RBAC_CREDENTIALS }}

      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.22
      - run: go build -o bootstrap -ldflags "-s -w -extldflags '-static'" # 11/26/24: build statically because of github.com/gen2brain/go-fitz
        working-directory: fn/onprem
        env:
          CGO_ENABLED: 1 # 08/2023: set to 0 because of https://github.com/golang/go/issues/58550 - 11/26/24: set to 1 because of github.com/gen2brain/go-fitz
          GOARCH: amd64
          GOOS: linux

      - uses: Azure/functions-action@v1
        with:
          app-name: ${{ env.AZURE_FUNCTIONAPP_NAME }}
          package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
