name: gofmt

on:
  pull_request:
    branches: [ "main" ]

permissions:
  contents: write

jobs:
  fmt:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          token: ${{ secrets.BOT_RW_PAT }}
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23
      - name: Install goimports-reviser
        run: go install github.com/incu6us/goimports-reviser/v3@latest
      - name: goimports
        run: goimports-reviser -rm-unused -company-prefixes github.com/drumkitai ./...
      - name: gofmt
        run: gofmt -l -s -w .
      - name: go mod tidy
        run: go mod tidy
      - name: check for modified files
        id: git-check
        run: echo "modified=$(if git diff-index --quiet HEAD --; then echo "false"; else echo "true"; fi)" >> $GITHUB_OUTPUT
      - name: push changes
        if: steps.git-check.outputs.modified == 'true'
        run: |
          git config --global user.name 'AxleGithubBot'
          git config --global user.email '<EMAIL>'
          git commit -am "automated go fmt"
          git push
