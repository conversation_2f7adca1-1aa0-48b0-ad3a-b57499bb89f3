# Local S3 Development Environment Utilities

This directory contains scripts to set up and manage a local S3-compatible development environment using LocalStack.

## Scripts

- `setup-localstack-s3.sh`: Initializes the LocalStack container and creates the necessary S3 bucket for local development.
- `localstack-s3-utils.sh`: A collection of utility functions to interact with the LocalStack S3 service (e.g., listing buckets, deleting objects).

---

## `setup-localstack-s3.sh`

This script automates the setup process for the local S3 environment.

### Usage

You need to create a `.env` file following `.env.example` in this directory.

To start the environment, simply run the script from the project root:

```bash
./drumkit/scripts/setup-localstack-s3.sh
```

If env file isn't being pulled properly try running prefixed with aws env var creds:

```bash
AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test ./setup-localstack-s3.sh
```

### What it Does

1.  **Checks Docker**: Verifies if the `localstack` service is already running via `docker-compose`. If not, it starts the service in detached mode.
2.  **Health Check**: Waits for the LocalStack container to become healthy and ready to accept connections.
3.  **Creates S3 Bucket**: Creates the `drumkit-local` S3 bucket, which is used by the services for local development.
4.  **Verification**: Lists the available S3 buckets to confirm that the setup was successful.
5.  **Provides Instructions**: Prints out helpful environment variables and commands for testing your services with the local S3 instance.

---

## `localstack-s3-utils.sh`

This script provides a convenient CLI for interacting with the local S3 buckets and objects.

### Usage

There are two ways to use this script:

#### 1. Direct Execution (Recommended for simple commands)

You can execute the script directly from the project root and pass a command as an argument.

```bash
# List all S3 buckets
./drumkit/scripts/localstack-s3-utils.sh list

# List all objects in the default 'drumkit-local' bucket
./drumkit/scripts/localstack-s3-utils.sh objects

# Show statistics for a specific bucket
./drumkit/scripts/localstack-s3-utils.sh stats my-other-bucket
```

#### 2. Sourcing the Script

You can `source` the script to make its functions available in your current shell session. This is useful if you need to perform multiple operations.

```bash
source ./drumkit/scripts/localstack-s3-utils.sh

# Now you can call the functions directly
list_buckets
list_objects drumkit-local
```

### Commands and Functions

| Command    | Function          | Description                                                                |
| :--------- | :---------------- | :------------------------------------------------------------------------- |
| `list`     | `list_buckets`    | Lists all S3 buckets in the LocalStack instance.                           |
| `objects`  | `list_objects`    | Lists all objects within a specified bucket (defaults to `drumkit-local`). |
| `download` | `download_object` | Downloads an object from a bucket to a local file.                         |
| `delete`   | `delete_object`   | Deletes a specific object from a bucket.                                   |
| `clear`    | `clear_bucket`    | Deletes all objects within a specified bucket.                             |
| `stats`    | `bucket_stats`    | Displays a summary of objects and total size for a bucket.                 |
| `help`     | N/A               | Shows the help menu with all available commands.                           |
