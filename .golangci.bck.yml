# Configuration file for golangci-lint: https://golangci-lint.run/usage/configuration

run:
  concurrency: 20
  timeout: 10m

linters:
  # For reproducible CI builds, enable every linter explicitly (see below)
  disable-all: true

  enable:
    # Linters enabled by default: https://golangci-lint.run/usage/linters/#enabled-by-default
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused

    # Preset: bugs
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - contextcheck
    - durationcheck
    - errchkjson
    - errorlint
    - copyloopvar
    - gosec
    - loggercheck
    - makezero
    - nilerr
    - noctx
    - reassign

    # Preset: metalinter
    - gocritic
    - revive

    # Preset: style
    - containedctx
    - lll
    - forbidigo
    - misspell
    - nolintlint
    - unconvert
    - usestdlibvars

    # Preset: unused
    - unparam

    # Dependency control
    - gomodguard

linters-settings:
  errcheck:
    # do not allow errors to be ignored with an underscore
    check-blank: true

  forbidigo:
    # Forbid the following identifiers: ["^(fmt\\.Print(|f|ln)|print|println)$"]
    forbid:
      - p: ^print.*$
        msg: use common/log pkg instead
      - p: ^fmt\.Print.*$
        msg: use common/log pkg instead
      - p: models\.LoadAttributes\{\}
        msg: use TMS.GetDefaultLoadAttributes() instead
      - p: ".*http\\.Client\\{\\}.*"
        msg: use otel.TracingHTTPClient() instead
      - p: http\.DefaultClient
        msg: use otel.TracingHTTPClient() instead

  gomodguard:
    blocked:
      modules:
        - github.com/aws/aws-sdk-go/aws:
            recommendations:
              - github.com/aws/aws-sdk-go-v2
        - github.com/brianvoe/gofakeit:
            recommendations:
              - github.com/brianvoe/gofakeit/v6
        - github.com/getsentry/sentry-go:
            recommendations:
              - github.com/drumkitai/drumkit/common/sentry
        - github.com/go-ozzo/ozzo-validation:
            recommendations:
              - github.com/go-playground/validator/v10
        - github.com/go-playground/validator:
            recommendations:
              - github.com/go-playground/validator/v10
        - github.com/go-redis/redis/v9:
            recommendations:
              - github.com/redis/go-redis/v9
        - github.com/json-iterator/go:
            reason: "use 'encoding/json' from the stdlib"
        - github.com/pkg/errors:
            reason: "use 'errors' from the stdlib"

      versions:
        # Blocked module with version constraint.
        - gorm.io/gorm:
            # Version constraint, see https://github.com/Masterminds/semver#basic-comparisons.
            version: "> 1.25.5"
            # Reason why the version constraint exists. (Optional)
            reason: "newer versions are backwards-incompatible. Check Gorm Github periodically for a fix"

        - gorm.io/driver/postgres:
            version: "> v1.5.4"
            reason: "newer versions are backwards-incompatible. Check Gorm Github periodically for a fix"

        - gorm.io/driver/mysql:
            version: "> v1.5.2"
            reason: "newer versions are backwards-incompatible. Check Gorm Github periodically for a fix"

  lll:
    line-length: 120
    tab-width: 4 # '\t' character should count as this many spaces

  revive:
    enable-all-rules: true
    rules:
      # TODO: consider enabling these
      - name: confusing-results
        disabled: true
      - name: early-return
        disabled: true
      - name: empty-lines
        disabled: true
      - name: import-alias-naming
        disabled: true
      - name: import-shadowing
        disabled: true
      - name: nested-structs
        disabled: true
      - name: unchecked-type-assertion
        disabled: true
      - name: unexported-naming
        disabled: true
      - name: unused-receiver
        disabled: true

      - name: add-constant
        disabled: true
      - name: argument-limit
        disabled: true
      - name: banned-characters
        disabled: true
      - name: bare-return
        disabled: true
      - name: cognitive-complexity
        disabled: true
      - name: comment-spacings
        disabled: true
      - name: confusing-naming
        disabled: true
      - name: cyclomatic
        disabled: true
      - name: file-header
        disabled: true
      - name: flag-parameter
        disabled: true
      - name: function-length
        disabled: true
      - name: function-result-limit
        disabled: true
      - name: line-length-limit
        disabled: true # see lll linter
      - name: max-public-structs
        disabled: true
      - name: unhandled-error
        disabled: true # see errcheck linter
