<p align="center"><a href="https://www.drumkit.ai"><img src="./docs/drumkit-logo.png" width="400px" alt="Drumkit - An AI copilot for broker communication" /></a></p>
<p align="center"><b>An AI copilot for broker communication</b></p>

# Drumkit Lambda Functions

This repo manages the source code for all of our Drumkit Lambda functions.
[Parthenon](https://github.com/drumkitai/parthenon) manages the _configuration_ (permissions, triggers, env vars, etc.) for each function.

## Architecture

![Drumkit Backend Architecture](./docs/arch.png)

_You can modify the Google Drawing [here](https://docs.google.com/drawings/d/15LWap76oLGmMAUxwNB8ktenhnMBDMQpm0y2X3dWG2xg/edit)_

1. The Gmail app sends a notification (with a messageID) to a GCP pub/sub topic for every new msg in the inbox
2. The topic invokes `beacon-ingestion` via a Function URL
3. The ingestion Lambda uses credentials from the `users` table in RDS to pull email contents via the Gmail SDK
4. The full email is dumped to S3 as a JSON file
5. The plaintext body and important email metadata is sent to the `beacon-processor` via SQS
6. The processor strips signatures, classifies the email, and writes a summary row to the `emails` table in RDS
7. When the user opens the Drumkit sidebar next to a msg in their inbox, they ping the `beacon-api` via a Function URL
8. The `beacon-api` returns the msg summary from RDS
9. When the user confirms an action to take in the TMS, this is submitted via the `beacon-api`

## Testing Locally

### Running each service individually

Each Go Lambda function (`fn/api`, `fn/ingestion/gmail`, `fn/migrate`, and `fn/processor`) can be run locally with `go run .`
Starting any service locally will also run the DB AutoMigrate for you automatically.

To test the Drumkit backend end-to-end (no external network access required):

Firstly, at Drumkit we run our services locally using air so they hot-reload. You can run `go install github.com/air-verse/air@latest` or use another method by checking out the air repo:
https://github.com/air-verse/air?tab=readme-ov-file#installation

0. Create a `drumkit_dev` DB in Postgres

```sql
$ ~ psql
postgres=# CREATE DATABASE drumkit_dev;
postgres=# \c drumkit_dev
```

- Enable the `pg_tgrm` extension in your PostgreSQL database in order to support fuzzy matching used in some queries. You can do this by running the following command in the PostgreSQL shell:

```sql
CREATE EXTENSION pg_trgm;
```

Install PostGIS (PostgreSQL extension) by running `brew install postgis` as it's not installed when you run `brew install postgresql`

Install and run Redis locally: [Local Redis Setup Mac](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-mac-os/)

Once installed you can run:

```bash
# start Redis
redis-server
# get REDIS_URL for env file
redis-cli
```

**There are also folder specific README files such as `fn/poller/README.md` and `fn/ingestion/outlook/README.md`**

It may be helpful to read these to get the functions running.

1. Create `.env` files: `fn/api/.env`, `fn/ingestion/outlook/.env`, `fn/ingestion/gmail/.env`, `fn/migrate/.env` and `fn/processor/.env`:

```shell
APP_ENV=dev
DB_USER=postgres
DB_HOST=localhost
DB_NAME=drumkit_dev
DB_PASSWORD=postgres

GOOGLE_CLIENT_ID=local
GOOGLE_CLIENT_SECRET="" # see 1Password
MICROSOFT_CLIENT_ID=7ef2023f-908c-4cd7-99ba-866a75fa15d0
MICROSOFT_WEBHOOK_URL="https://jiczbgfk3v4a4lvppaxgiovxte0weejt.lambda-url.us-east-1.on.aws"
MICROSOFT_CLIENT_SECRET="" # see 1Password Azure app registration password (NOT "Drumkit Portal")

REDIS_URL="redis://127.0.0.1:6379" # Local Redis URL see step 0
USPS_USER_ID="" # value is sensitive, ask a team member

CYCLOPS_URL=http://localhost:8000 # add this to fn/processor .env

S3_BUCKET_NAME=local
SQS_QUEUE_URL=local

BACKFILL_HOURS=0 # for outlook and gmail email ingestion

# Logging vars - see common/log/README.md for more info
DEBUG=false # set to true for Log.debug logs

# For protected routes intended only for internal use like POST to /carrier-groups
# See 1Password for the API key when attempting to use routes on prod
INTERNAL_DRUMKIT_KEY=''
```

#### Note: for `fn/ingestion/gmail/.env` also include `BACKFILL_HOURS=0`

2. From the `fn/api` directory, run `air` to start the API on port 5000. This will also AutoMigrate the DB
   - Note: Port 5000 is used for airplay, switch off AirPlay Receiver in apple settings/general/airdrop&handle in order for fn/api to run
   - Note: Some functionality within `fn` functions require AWS, to set that up check out the instructions [here](https://www.notion.so/axleapi/aws-vault-c144881ae88f4563982d43a857eb5b0e#c68e24c88e6148938c55fd2eb47c733f). You will likely be running them using an alias e.g. `dev -- air` instead of `air`
3. Create a new user via `POST http://localhost:5000/user/signup` with a body like `{"DevEmail": "<EMAIL>"}`
   - In order to actually log in to the Drumkit sidebar when you start working/testing, you need to use the Drumkit-portal which requires the google client id and secret for the beacon email account. Ask a team member for these and include them in the .env file in `fn/api`.
4. In a separate terminal, from the `fn/ingestion/gmail` directory, `air` to start the service on port 5001.
5. In a separate terminal, from `fn/processor`, `air` to start that service on port 5005.
   - This is a good time to set up your aws-vault profile. Check out the instructions [here](https://www.notion.so/axleapi/aws-vault-c144881ae88f4563982d43a857eb5b0e#c68e24c88e6148938c55fd2eb47c733f)
   - In order for this to work in local dev, you need to start `fn/processor` using `AWS_SESSION_TOKEN_TTL=12h aws-vault exec <your-profile-name> -- air` (or dev -- air if you configured your aws vault) instead of just `air`.
6. Manually add a service to the `services` table in the DB - include id, createdAt (just use the createdAt entry from the user you just made), name, domain, and nickname.
   - For work involving quick quotes and extraction, make sure the isCarrierNetworkQuotingEnabled and isQuoteRequestExtractionEnabled flags are set to true for your service.
7. Manually add the serviceId to the user. Also change gmail_last_history_id to something other than 0 (1 or 4837 or whatever) on the user.
8. Now you can POST to `http://localhost:5001/inboxWebhook` to ingest emails:

```json
{
  "emailAddress": "<EMAIL>",
  "addPayloadEncoding": true,
  "msg": {
    "id": "abc123",
    "threadId": "abc123",
    "internalDate": "1696606918000",
    "payload": {
      "headers": [
        {
          "name": "Message-ID",
          "value": "<<EMAIL>>"
        },
        {
          "name": "Subject",
          "value": "[EXTERNAL] Re: PRO 1234567"
        },
        {
          "name": "From",
          "value": "Axle <<EMAIL>>"
        },
        {
          "name": "To",
          "value": "NFI <<EMAIL>>"
        }
      ],
      "mimeType": "text/plain",
      "body": {
        "data": "Hello, Drumkit!\nWhat's the delivery ETA?"
      }
    }
  }
}
```

- `emailAddress` must be a valid user in the DB
- The `msg` format is `*gmail.Message`, the same format stored in our S3 archive.
  - Set `"addPayloadEncoding": true` if the `body.data` sections of the payload are not already base64-URL encoded
  - If the `msg` is copied from an example in S3, the body will already be encoded

9. Verify the new addition to the `emails` DB table and note the ingestion/processor logs
10. Invoke the `beacon-api` to fetch email metadata
11. POST `http://localhost:5000/user/login` with a JSON body like `{"DevEmail": "<EMAIL>"}` to get an API access token
12. GET `http://localhost:5000/email/thread/abc123` with an `Authorization: Bearer {token}` header, using the access token from the login step above

### Running with Docker

NOTE: As of Feb 2025 running with docker does not work smoothly with fresh setup and requires some changes.
If it's your first time setting up we recommend running each lambda function `api`, `processor`, etc individually as described above.

Docker helps by letting you run each service all at once instead of running each service individually.

0. Create a `beacon_dev` DB in Postgres
1. Run Docker locally. On Mac, you can use [Docker Desktop](https://www.docker.com/products/docker-desktop/).
2. Include a `.env` file in the root directory like the example below

```shell
GOOGLE_CLIENT_ID=968651685013-i120oufqf06lonr2lj3ahh92il7j67qo.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=<See 1Password>
APP_ENV=dev
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=host.docker.internal
DB_NAME=drumkit_dev
MICROSOFT_CLIENT_ID=<ID>
MICROSOFT_CLIENT_SECRET=<SECRET>
MICROSOFT_WEBHOOK_URL=""
OPENAI_API_KEY=<API KEY>
NFI_APP_ID=<APP ID>
NFI_PASS=<PASSWORD>
DISABLE_RATE_LIMIT=true
BACKFILL_HOURS=0
S3_BUCKET_NAME=local
SQS_QUEUE_URL=local
```

#### Note that the `DB_HOST` has been set to `host.docker.internal`. This directs the internal Docker server to `localhost` on your computer as we are running the database separate to the service itself.

#### Note that MICROSOFT_WEBHOOK_URL is set to `""`, keep it that way - at least for local development

3. Navigate to the `drumkit` (project root) directory and run `docker compose up` or `docker compose up -d`. The `-d` allows you to run it in the background, but hides the logs. If you just want to run one service, you can run `docker compose up <service>` e.g. `docker compose up api`.
4. Then, you can follow any of the above steps.

If you want to make changes to the Dockerfile itself, be sure to run `docker compose build` before running the services.

### Replay from S3

Rather than specifying the email contents directly, you can replay a real example from the `axle-beacon-ingestion` [S3 archive](https://s3.console.aws.amazon.com/s3/buckets/axle-beacon-ingestion?region=us-east-1&tab=objects):

1. Restart the ingestion service with your AWS credentials: `(fn/ingestion/gmail) aws-vault exec your-profile -- go run .`
2. POST to `http://localhost:5001/inboxWebhook`, specifying an object key:

```json
{
  "emailAddress": "<EMAIL>",
  "s3Key": "gmail/<EMAIL>/18b05dea69236b99.json"
}
```

### End-to-End Test

[fn/ingestion/gmail/replay/emails.yml](fn/ingestion/gmail/replay/emails.yml) defines test cases with real emails from the archive.
This makes it easy to quickly test ingestion and the processor end-to-end with multiple emails.

1. Make sure `beacon-ingestion` and `beacon-processor` are running in separate terminals (the former with AWS access)
2. Each user in the test set must exist in your local DB with a positive `gmail_last_history_id`
   1. `<EMAIL>` and `<EMAIL>`
3. In a third terminal, `cd fn/ingestion/gmail` and `go run ./replay`:

```
19:11:10	INFO	replay/main.go:85	replaying 7 emails from S3
19:11:11	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b1a8e5230560ba.json
19:11:11	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b1b9046aed69fc.json
19:11:11	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18af79c3de43501f.json
19:11:11	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b05b087b1127c3.json
19:11:11	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b1b25887cce003.json
19:11:12	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b05dea69236b99.json
19:11:12	INFO	replay/main.go:105	PASS: gmail/<EMAIL>/18b070e4d28805c0.json
19:11:12	INFO	replay/main.go:109	Done: 7/7 passed
```

## Deployment

Commits to the `main` branch will update the code for every Lambda function.

To deploy manually, you can trigger the `deploy` GitHub action directly. Or, using the AWS CLI:

1. `cd` to the folder for your function, e.g. `cd fn/api`
2. Compile: `GOARCH=amd64 GOOS=linux go build -o main -ldflags "-s -w"`
3. Create the deployment package: `zip fn.zip main`
4. `aws-vault exec {profile} -- aws lambda update-function-code --function-name {FUNCTION_NAME} --zip-file fileb://fn.zip`

## Adding a Lambda function

To add a new Lambda function:

1. Start with a Parthenon PR ([example](https://github.com/drumkitai/parthenon/pull/54)) to create a new function with a default echo program
2. Add a new `fn/{name}` folder to this repo with the source code
3. Add a new job to [deploy.yml](.github/workflows/deploy.yml)

&nbsp;
&nbsp;
---
&nbsp;
&nbsp;

# Dashboard API

This module provides APIs to manage Metabase dashboards embedded within the Drumkit platform. It enables creating, updating, retrieving, and deleting embedded dashboard records.

## 🛠 Technologies Used

- [Metabase](https://www.metabase.com/)

## 📦 Endpoints

### 1. Create Dashboard
**POST `/metabase/dashboard`**

Create a new dashboard record.

#### Request Body

```json
{
  "dashboardId": 456,
  "name": "Sales Dashboard",
  "description": "Overview of sales KPIs",
  "userId": 1,
  "serviceId": 2
}
```

#### Success Response

```json
{
  "id": 1,
  "dashboardId": 456,
  "name": "Sales Dashboard",
  "description": "Overview of sales KPIs",
  "serviceId": 2,
  "userId": 1,
  "createdAt": "2025-05-22T10:00:00Z",
  "updatedAt": "2025-05-22T10:00:00Z"
}
```

> ⚠️ Only users with `@drumkit.ai` domain are allowed to perform this action.

### 2. Update Dashboard
**PUT `/metabase/dashboard/:dashboardId`**

Update an existing dashboard by ID.

#### Path Param

- `dashboardId`: ID of the dashboard to update

#### Request Body

```json
{
  "dashboardId": 321,
  "name": "Updated Name",
  "description": "Updated Description",
  "userId": 2
}
```

#### Success Response

```json
{
  "id": 1,
  "name": "Updated Name",
  "description": "Updated Description",
  "serviceId": 2,
  "userId": 2,
  "dashboardId": 321,
  "createdAt": "2025-05-22T10:00:00Z",
  "updatedAt": "2025-05-22T11:00:00Z"
}
```

> ⚠️ Only users with `@drumkit.ai` domain are allowed to perform this action.

### 3. Get Dashboards for Service
**GET `/metabase/dashboard/:serviceId`**

Retrieve all dashboards associated with a given service ID.

#### Path Param

- `serviceId`: ID of the service

#### Success Response

```json
{
  "dashboards": [
    {
      "id": 1,
      "dashboardId": 456,
      "name": "Sales Dashboard",
      "description": "Overview of sales KPIs",
      "serviceId": 2,
      "userId": 1,
      "createdAt": "2025-05-22T10:00:00Z",
      "updatedAt": "2025-05-22T10:00:00Z",
      "iframeUrl": "https://metabase.example.com/embed/dashboard/token#bordered=true&titled=true"
    }
  ]
}
```

> ⚠️ JWT `serviceID` must match requested `serviceId`.

### 4. Delete Dashboard
**DELETE `/metabase/dashboard/:dashboardId`**

Delete a dashboard entry by ID.

#### Path Param

- `dashboardId`: ID of the dashboard to delete

#### Success Response

- `204 No Content`

#### Error

- `404 Not Found`: If dashboard does not exist

## 🔐 Metabase iFrame Embed Token Generator

The API internally uses JWT to generate secure iframe URLs for embedding dashboards or questions.

```go
generateIFrameURL(dashboardID int, questionID int, dateRange DateRange) string
```

#### DateRange Struct

```go
type DateRange struct {
  From string `json:"from"` // e.g. "2024-01-01"
  To   string `json:"to"`   // e.g. "2024-01-31"
}
```

## 🌍 Environment Variables

Make sure these are configured in your `.env` or deployment config:

- `METABASE_SITE_URL` – e.g. `https://metabase.example.com`
- `METABASE_SECRET_KEY` – your Metabase JWT secret

---

## Instructions to Create a Dashboard in Metabase

Follow the steps below to create a dashboard in Metabase that can be embedded and managed via APIs:

1. **Create a Base Model**: 
    - Go to Browse → Models → + New Model.
    - Choose the SQL option to define a custom dataset.
    - Write your SQL query to retrieve the required data.
    - Save the model with a recognizable name. This model will serve as the data source for charts and dashboards.
2. **Create a Chart (Question)**:
    - Go to **New Question**.
    - Click + New → Question.
    - Select **Custom Question** and choose the model created in Step 1 as the base table.
    - Add filters (like date, user) to make it interactive.
    - Use the Summarize or Group by features to aggregate data.
    - Choose the appropriate visualization type (bar, line, table, pie, etc.) from the top right.
    - Once satisfied, click Save and provide a meaningful name.
3. **Add the Chart to a Dashboard**
    - Navigate to the created chart.
    - Click on three dots on top left corner
    - Click on Add to Dashboard.
    - Select the Dashboard in which we need to add.
    - Adjust the position, height and width of the chart
    - Click on Save.
    - Repeat for other charts if needed.
4. **Add Filters to the Dashboard**:
    - Click Edit on the dashboard.
    - Click + Add a Filter (e.g., Date, Dropdown, etc.).
    - Configure the filter settings and connect it to the relevant fields in your charts.
    - Example: A Date Range filter can be connected to a created_at field in multiple charts.
5. **Set Full Width for Charts**
    - On the dashboard editor, click the three dots (...) on the card.
    - Choose "Change width" → Select "Full" to utilize the full dashboard width.
6. **Save the Dashboard**
    - Once layout and filters are finalized, click Done and Save the dashboard.
7. **Enable Sharing and Embedding**
    - Click on the Share icon at the top right of the dashboard.
    - Choose "Embed this dashboard in an application".
8. **Choose Static Embedding**
    - In the Embed popup, select "Static Embed" (useful for fixed dashboards with controlled inputs).
9. **Configure Parameters**:
    - Navigate to the Parameters tab.
    - For each filter:
        - Choose "Locked" if the filter value must be provided via API or application.
        - Choose "Disabled" if the filter is optional.
10. **Publish the Dashboard**
    - Click "Publish" to generate the final Embed URL and make it publicly accessible (within the configured security constraints).
11. **Store Dashboard ID in Our Application**
    - Once the dashboard is published, note down the Dashboard ID (available in the URL or API response).
    - Save this Dashboard ID using your API (shown below) to your backend/database so that it can be accessed later through your portal.

